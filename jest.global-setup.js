/**
 * Jest Global Setup
 * Runs once before all tests
 */

module.exports = async () => {
  console.log('🧪 Setting up test environment...');
  
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  
  // Mock Redis for tests
  process.env.UPSTASH_REDIS_REST_URL = 'https://test-redis.upstash.io';
  process.env.UPSTASH_REDIS_REST_TOKEN = 'test-token';
  
  // Mock AI provider keys
  process.env.OPENAI_API_KEY = 'test-openai-key';
  process.env.ANTHROPIC_API_KEY = 'test-anthropic-key';
  
  console.log('✅ Test environment setup complete');
};
