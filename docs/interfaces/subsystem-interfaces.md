# Subsystem Interfaces

This document defines the interfaces between the various subsystems in the Goal-Based Content Generation System. These interfaces ensure clean separation of concerns and enable subsystems to interact with each other in a standardized way.

## 1. Interface Overview

The system uses the following types of interfaces:

1. **Event-Based Interfaces**: Subsystems communicate by emitting and consuming events through the central Event Bus.
2. **API Interfaces**: Subsystems expose methods that can be called by other subsystems.
3. **State-Based Interfaces**: Subsystems interact through shared state managed by the central State Store.

## 2. Event-Based Interfaces

### 2.1 Event Structure

All events in the system follow a standard structure:

```typescript
interface SystemEvent {
  id: string;
  type: string;
  payload: any;
  timestamp: string;
  source: string;
  correlationId?: string;
  causationId?: string;
}
```

### 2.2 Core Event Types

#### Workflow Events

| Event Type | Description | Payload | Produced By | Consumed By |
|------------|-------------|---------|------------|-------------|
| `workflow.goal.created` | A new goal has been created | `{ goalId: string, goalType: string, description: string }` | WorkflowManager | SystemController, AgentCollaborationSystem |
| `workflow.goal.activated` | A goal has been activated | `{ goalId: string, activatedAt: string }` | WorkflowManager | SystemController, ArtifactManager |
| `workflow.goal.completed` | A goal has been completed | `{ goalId: string, completedAt: string }` | WorkflowManager | SystemController |
| `workflow.phase.changed` | The workflow phase has changed | `{ oldPhase: string, newPhase: string }` | WorkflowManager | SystemController |

#### Artifact Events

| Event Type | Description | Payload | Produced By | Consumed By |
|------------|-------------|---------|------------|-------------|
| `artifact.created` | A new artifact has been created | `{ artifactId: string, goalId: string, type: string }` | ArtifactManager | QualityAssuranceSystem, FeedbackSystem |
| `artifact.updated` | An artifact has been updated | `{ artifactId: string, version: number }` | ArtifactManager | QualityAssuranceSystem |
| `artifact.submitted` | An artifact has been submitted for approval | `{ artifactId: string, submittedBy: string }` | ArtifactManager | HumanInteractionSystem |
| `artifact.approved` | An artifact has been approved | `{ artifactId: string, approvedBy: string }` | ArtifactManager | WorkflowManager |
| `artifact.rejected` | An artifact has been rejected | `{ artifactId: string, rejectedBy: string, reason: string }` | ArtifactManager | WorkflowManager |

#### Feedback Events

| Event Type | Description | Payload | Produced By | Consumed By |
|------------|-------------|---------|------------|-------------|
| `feedback.requested` | Feedback has been requested | `{ requestId: string, artifactId: string, fromAgent: string, toAgent: string }` | FeedbackSystem | AgentCollaborationSystem |
| `feedback.provided` | Feedback has been provided | `{ responseId: string, requestId: string, fromAgent: string, toAgent: string }` | FeedbackSystem | ArtifactManager |
| `feedback.incorporated` | Feedback has been incorporated | `{ incorporationId: string, originalArtifactId: string, updatedArtifactId: string }` | FeedbackSystem | QualityAssuranceSystem |

#### Human Interaction Events

| Event Type | Description | Payload | Produced By | Consumed By |
|------------|-------------|---------|------------|-------------|
| `human.approval.requested` | Human approval has been requested | `{ requestId: string, artifactId: string, urgency: string }` | HumanInteractionSystem | SystemController |
| `human.approval.provided` | Human has provided approval | `{ requestId: string, approved: boolean, feedback?: string }` | HumanInteractionSystem | ArtifactManager |
| `human.input.requested` | Human input has been requested | `{ requestId: string, prompt: string, context: any }` | HumanInteractionSystem | SystemController |
| `human.input.provided` | Human has provided input | `{ requestId: string, input: any }` | HumanInteractionSystem | SystemController |

#### Quality Assurance Events

| Event Type | Description | Payload | Produced By | Consumed By |
|------------|-------------|---------|------------|-------------|
| `artifact.evaluation.completed` | Artifact evaluation has been completed | `{ artifactId: string, evaluation: ArtifactEvaluation }` | QualityAssuranceSystem | SystemController |
| `artifact.quality.issue` | A quality issue has been identified | `{ artifactId: string, issueType: string, details: any }` | QualityAssuranceSystem | FeedbackSystem |

#### System Events

| Event Type | Description | Payload | Produced By | Consumed By |
|------------|-------------|---------|------------|-------------|
| `system.error` | An error occurred in the system | `{ subsystem: string, errorType: string, details: any }` | Any | SystemController |
| `system.autonomous.mode.changed` | Autonomous mode has been changed | `{ enabled: boolean }` | SystemController | All |

## 3. API Interfaces

### 3.1 WorkflowManager Interface

```typescript
interface IWorkflowManager {
  /**
   * Define goals for the session
   * @param goals Goals to define
   * @returns Promise<string[]> Goal IDs
   */
  defineGoals(goals: Partial<Goal>[]): Promise<string[]>;

  /**
   * Activate a goal
   * @param goalId Goal ID
   * @returns Promise<boolean> Success indicator
   */
  activateGoal(goalId: string): Promise<boolean>;

  /**
   * Complete a goal
   * @param goalId Goal ID
   * @returns Promise<boolean> Success indicator
   */
  completeGoal(goalId: string): Promise<boolean>;

  /**
   * Check if a goal's dependencies are satisfied
   * @param goalId Goal ID
   * @returns Promise<boolean> Whether dependencies are satisfied
   */
  checkGoalDependencies(goalId: string): Promise<boolean>;

  /**
   * Get the current workflow phase
   * @returns Promise<WorkflowPhase> Current phase
   */
  getCurrentPhase(): Promise<WorkflowPhase>;

  /**
   * Update workflow progress
   * @returns Promise<void>
   */
  updateWorkflowProgress(): Promise<void>;
}
```

### 3.2 ArtifactManager Interface

```typescript
interface IArtifactManager {
  /**
   * Create a new artifact
   * @param params Artifact creation parameters
   * @returns Promise<string> Artifact ID
   */
  createArtifact(params: ArtifactCreationParams): Promise<string>;

  /**
   * Update an artifact
   * @param artifactId Artifact ID
   * @param content New content
   * @returns Promise<string> Updated artifact ID
   */
  updateArtifact(artifactId: string, content: any): Promise<string>;

  /**
   * Submit an artifact for approval
   * @param artifactId Artifact ID
   * @returns Promise<void>
   */
  submitArtifactForApproval(artifactId: string): Promise<void>;

  /**
   * Approve an artifact
   * @param artifactId Artifact ID
   * @param approverId Approver ID
   * @param comments Optional comments
   * @returns Promise<void>
   */
  approveArtifact(artifactId: string, approverId: string, comments?: string): Promise<void>;

  /**
   * Reject an artifact
   * @param artifactId Artifact ID
   * @param rejectorId Rejector ID
   * @param reason Rejection reason
   * @returns Promise<void>
   */
  rejectArtifact(artifactId: string, rejectorId: string, reason: string): Promise<void>;

  /**
   * Get an artifact by ID
   * @param artifactId Artifact ID
   * @returns Promise<Artifact | null> The artifact or null if not found
   */
  getArtifact(artifactId: string): Promise<Artifact | null>;
}
```

### 3.3 FeedbackSystem Interface

```typescript
interface IFeedbackSystem {
  /**
   * Request feedback on an artifact
   * @param fromAgent Agent requesting feedback
   * @param toAgent Agent to provide feedback
   * @param artifactId Artifact ID
   * @param specificAreas Specific areas to focus on
   * @returns Promise<string> Request ID
   */
  requestFeedback(
    fromAgent: string,
    toAgent: string,
    artifactId: string,
    specificAreas?: string[]
  ): Promise<string>;

  /**
   * Provide feedback on an artifact
   * @param fromAgent Agent providing feedback
   * @param toAgent Agent who requested feedback
   * @param requestId Request ID
   * @param feedback Feedback data
   * @returns Promise<string> Response ID
   */
  provideFeedback(
    fromAgent: string,
    toAgent: string,
    requestId: string,
    feedback: FeedbackData
  ): Promise<string>;

  /**
   * Incorporate feedback into an artifact
   * @param artifactId Artifact ID
   * @param responseIds Feedback response IDs
   * @param agentId Agent incorporating feedback
   * @returns Promise<string> Updated artifact ID
   */
  incorporateFeedback(
    artifactId: string,
    responseIds: string[],
    agentId: string
  ): Promise<string>;

  /**
   * Generate feedback on an artifact
   * @param artifactId Artifact ID
   * @param fromAgent Agent providing feedback
   * @param specificAreas Specific areas to focus on
   * @returns Promise<FeedbackData> Generated feedback
   */
  generateFeedback(
    artifactId: string,
    fromAgent: string,
    specificAreas?: string[]
  ): Promise<FeedbackData>;
}
```

### 3.4 HumanInteractionSystem Interface

```typescript
interface IHumanInteractionSystem {
  /**
   * Request approval from a human
   * @param artifactId Artifact ID
   * @param context Context information
   * @param urgency Request urgency
   * @returns Promise<string> Request ID
   */
  requestApproval(
    artifactId: string,
    context: any,
    urgency?: InterventionUrgency
  ): Promise<string>;

  /**
   * Process approval response
   * @param requestId Request ID
   * @param approved Whether the artifact was approved
   * @param feedback Optional feedback
   * @returns Promise<void>
   */
  processApprovalResponse(
    requestId: string,
    approved: boolean,
    feedback?: string
  ): Promise<void>;

  /**
   * Request input from a human
   * @param prompt Input prompt
   * @param context Context information
   * @param urgency Request urgency
   * @returns Promise<string> Request ID
   */
  requestHumanInput(
    prompt: string,
    context: any,
    urgency?: InterventionUrgency
  ): Promise<string>;

  /**
   * Process human input
   * @param requestId Request ID
   * @param input Input data
   * @returns Promise<void>
   */
  processHumanInput(
    requestId: string,
    input: any
  ): Promise<void>;

  /**
   * Notify for optional review
   * @param artifactId Artifact ID
   * @param context Context information
   * @returns Promise<string> Request ID
   */
  notifyForOptionalReview(
    artifactId: string,
    context: any
  ): Promise<string>;
}
```

### 3.5 QualityAssuranceSystem Interface

```typescript
interface IQualityAssuranceSystem {
  /**
   * Evaluate artifact quality
   * @param artifactId Artifact ID
   * @returns Promise<ArtifactEvaluation> Evaluation results
   */
  evaluateArtifactQuality(
    artifactId: string
  ): Promise<ArtifactEvaluation>;

  /**
   * Get evaluation criteria for an artifact type
   * @param artifactType Artifact type
   * @returns Promise<EvaluationCriteriaSet> Criteria set
   */
  getEvaluationCriteria(
    artifactType: string
  ): Promise<EvaluationCriteriaSet>;

  /**
   * Generate improvement suggestions
   * @param artifactId Artifact ID
   * @param evaluationId Evaluation ID
   * @returns Promise<ImprovementSuggestion[]> Improvement suggestions
   */
  generateImprovementSuggestions(
    artifactId: string,
    evaluationId: string
  ): Promise<ImprovementSuggestion[]>;

  /**
   * Track quality metrics
   * @param artifactType Artifact type
   * @param metrics Quality metrics
   * @returns Promise<void>
   */
  trackQualityMetrics(
    artifactType: string,
    metrics: Record<string, number>
  ): Promise<void>;
}
```

### 3.6 AgentCollaborationSystem Interface

```typescript
interface IAgentCollaborationSystem {
  /**
   * Initiate a collaboration
   * @param params Collaboration parameters
   * @returns Promise<string> Collaboration ID
   */
  initiateCollaboration(
    params: CollaborationParams
  ): Promise<string>;

  /**
   * Send a message in a collaboration
   * @param message Collaboration message
   * @returns Promise<string> Message ID
   */
  sendMessage(
    message: CollaborationMessage
  ): Promise<string>;

  /**
   * Complete a collaboration
   * @param collaborationId Collaboration ID
   * @param outcome Collaboration outcome
   * @returns Promise<void>
   */
  completeCollaboration(
    collaborationId: string,
    outcome: any
  ): Promise<void>;

  /**
   * Evaluate if collaboration is needed
   * @param task Task description
   * @param context Task context
   * @returns Promise<CollaborationEvaluation> Evaluation result
   */
  evaluateCollaborationNeed(
    task: string,
    context: any
  ): Promise<CollaborationEvaluation>;
}
```

## 4. State-Based Interfaces

### 4.1 State Store Interface

```typescript
interface IStateStore<T> {
  /**
   * Get the current state
   * @returns Promise<T | null> Current state or null if not found
   */
  getState(): Promise<T | null>;

  /**
   * Set the state
   * @param state New state
   * @returns Promise<void>
   */
  setState(state: T): Promise<void>;

  /**
   * Update the state using a function
   * @param updateFn Function that takes current state and returns new state
   * @returns Promise<void>
   */
  updateState(
    updateFn: (state: T | null) => T | null
  ): Promise<void>;

  /**
   * Update state with optimistic concurrency control
   * @param updateFn Function that takes current state and returns new state
   * @returns Promise<T> Updated state
   */
  transactionalUpdate(
    updateFn: (state: T) => T
  ): Promise<T>;
}
```

### 4.2 Event Bus Interface

```typescript
interface IEventBus {
  /**
   * Register an event handler
   * @param eventType Event type
   * @param handler Event handler function
   * @returns void
   */
  on(
    eventType: string,
    handler: (event: SystemEvent) => Promise<void> | void
  ): void;

  /**
   * Emit an event
   * @param eventType Event type
   * @param payload Event payload
   * @param source Event source
   * @param options Additional options
   * @returns Promise<void>
   */
  emit(
    eventType: string,
    payload: any,
    source: string,
    options?: {
      correlationId?: string;
      causationId?: string;
    }
  ): Promise<void>;

  /**
   * Remove an event handler
   * @param eventType Event type
   * @param handler Event handler function
   * @returns void
   */
  off(
    eventType: string,
    handler: (event: SystemEvent) => Promise<void> | void
  ): void;
}
```

## 5. Sequence Diagrams for Key Interactions

### 5.1 Artifact Creation and Approval Sequence

```
┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐
│          │     │          │     │          │     │          │     │          │
│ Workflow │     │ Artifact │     │ Quality  │     │ Human    │     │ System   │
│ Manager  │     │ Manager  │     │ Assurance│     │ Interaction│   │ Controller│
│          │     │          │     │          │     │          │     │          │
└────┬─────┘     └────┬─────┘     └────┬─────┘     └────┬─────┘     └────┬─────┘
     │                │                │                │                │
     │ activateGoal() │                │                │                │
     │───────────────▶│                │                │                │
     │                │                │                │                │
     │                │ createArtifact()                │                │
     │                │───────────────▶│                │                │
     │                │                │                │                │
     │                │                │ evaluateArtifact()              │
     │                │                │───────────────▶│                │
     │                │                │                │                │
     │                │                │ requestApproval()               │
     │                │                │───────────────▶│                │
     │                │                │                │                │
     │                │                │                │ emit(human.approval.requested)
     │                │                │                │───────────────▶│
     │                │                │                │                │
     │                │                │                │◀ ─ ─ ─ ─ ─ ─ ─ │
     │                │                │                │ processApprovalResponse()
     │                │                │                │                │
     │                │◀ ─ ─ ─ ─ ─ ─ ─ ┼ ─ ─ ─ ─ ─ ─ ─ │                │
     │                │ approveArtifact()               │                │
     │                │                │                │                │
     │                │ emit(artifact.approved)         │                │
     │                │───────────────────────────────────────────────▶│
     │                │                │                │                │
     │◀ ─ ─ ─ ─ ─ ─ ─ ┼ ─ ─ ─ ─ ─ ─ ─ ┼ ─ ─ ─ ─ ─ ─ ─ ┼ ─ ─ ─ ─ ─ ─ ─ │
     │ completeGoal() │                │                │                │
     │                │                │                │                │
```

### 5.2 Feedback Request and Incorporation Sequence

```
┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐
│          │     │          │     │          │     │          │     │          │
│ Agent A  │     │ Feedback │     │ Agent    │     │ Agent B  │     │ Artifact │
│          │     │ System   │     │ Collab   │     │          │     │ Manager  │
│          │     │          │     │ System   │     │          │     │          │
└────┬─────┘     └────┬─────┘     └────┬─────┘     └────┬─────┘     └────┬─────┘
     │                │                │                │                │
     │ requestFeedback()               │                │                │
     │───────────────▶│                │                │                │
     │                │                │                │                │
     │                │ emit(feedback.requested)        │                │
     │                │───────────────▶│                │                │
     │                │                │                │                │
     │                │                │ sendMessage()  │                │
     │                │                │───────────────▶│                │
     │                │                │                │                │
     │                │                │◀ ─ ─ ─ ─ ─ ─ ─ │                │
     │                │                │ sendMessage()  │                │
     │                │                │                │                │
     │                │◀ ─ ─ ─ ─ ─ ─ ─ │                │                │
     │                │ provideFeedback()               │                │
     │                │                │                │                │
     │◀ ─ ─ ─ ─ ─ ─ ─ │                │                │                │
     │ incorporateFeedback()           │                │                │
     │───────────────▶│                │                │                │
     │                │                │                │                │
     │                │ updateArtifact()                │                │
     │                │───────────────────────────────────────────────▶│
     │                │                │                │                │
     │                │◀ ─ ─ ─ ─ ─ ─ ─ ┼ ─ ─ ─ ─ ─ ─ ─ ┼ ─ ─ ─ ─ ─ ─ ─ │
     │                │ emit(feedback.incorporated)     │                │
     │                │                │                │                │
     │◀ ─ ─ ─ ─ ─ ─ ─ │                │                │                │
     │ Feedback       │                │                │                │
     │ Incorporated   │                │                │                │
     │                │                │                │                │
```

## 6. Protocol Adapters

### 6.1 Protocol Adapter Interface

The system is designed to support multiple communication protocols through adapters. This allows for future implementation of the A2A protocol as a wrapper around our native communication system.

```typescript
interface IProtocolAdapter {
  /**
   * Convert an internal message to the protocol-specific format
   * @param message Internal message
   * @returns Protocol-specific message
   */
  adaptOutgoing(message: InternalMessage): ProtocolMessage;

  /**
   * Convert a protocol-specific message to the internal format
   * @param message Protocol-specific message
   * @returns Internal message
   */
  adaptIncoming(message: ProtocolMessage): InternalMessage;

  /**
   * Check if this adapter can handle a specific message
   * @param message Message to check
   * @returns Whether this adapter can handle the message
   */
  canHandle(message: any): boolean;

  /**
   * Get the protocol identifier
   * @returns Protocol identifier
   */
  getProtocolId(): string;
}
```

### 6.2 A2A Protocol Adapter (Future Implementation)

The A2A Protocol Adapter will be implemented in the future to provide compatibility with the existing A2A protocol.

```typescript
interface IA2AProtocolAdapter extends IProtocolAdapter {
  /**
   * Convert an internal message to the A2A format
   * @param message Internal message
   * @returns A2A message
   */
  adaptOutgoing(message: InternalMessage): A2AMessage;

  /**
   * Convert an A2A message to the internal format
   * @param message A2A message
   * @returns Internal message
   */
  adaptIncoming(message: A2AMessage): InternalMessage;

  /**
   * Handle A2A-specific message routing
   * @param message A2A message
   * @returns Routing information
   */
  getRoutingInfo(message: A2AMessage): A2ARoutingInfo;
}

interface A2AMessage {
  jsonrpc: string;
  method: string;
  params: any;
  id?: string | number;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}

interface A2ARoutingInfo {
  source: string;
  target: string;
  conversationId: string;
  messageType: string;
}
```

## 7. Contract Testing Recommendations

### 7.1 Event Contract Testing

1. **Event Schema Validation**: Ensure all events conform to the defined schema
2. **Event Payload Validation**: Validate that event payloads contain required fields
3. **Event Sequence Testing**: Test that events are emitted in the expected sequence
4. **Event Handler Testing**: Test that event handlers process events correctly

### 7.2 API Contract Testing

1. **Input Validation**: Test that API methods validate their inputs
2. **Output Validation**: Ensure API methods return the expected outputs
3. **Error Handling**: Test that API methods handle errors appropriately
4. **Concurrency Testing**: Test API behavior under concurrent access

### 7.3 State Contract Testing

1. **State Schema Validation**: Ensure state conforms to the defined schema
2. **State Transition Testing**: Test that state transitions work as expected
3. **Optimistic Concurrency Testing**: Test behavior under concurrent updates
4. **State Recovery Testing**: Test recovery from invalid states

### 7.4 Protocol Adapter Testing

1. **Adapter Conversion Testing**: Test that adapters correctly convert between internal and protocol-specific formats
2. **Protocol Compliance Testing**: Test that protocol-specific messages comply with the protocol specification
3. **Adapter Selection Testing**: Test that the correct adapter is selected for a given message
4. **Error Handling Testing**: Test that adapters handle protocol-specific errors correctly
