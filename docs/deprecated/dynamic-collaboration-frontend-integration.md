# Dynamic Collaboration Frontend Integration

This document describes how to integrate the Dynamic Collaboration V2 API with frontend components.

## Overview

The Dynamic Collaboration V2 API provides a more efficient and type-safe way to interact with the dynamic collaboration system. This document explains how to use the new client and update existing components to work with the new API.

## Client Usage

### Importing the Client

```typescript
import { dynamicCollaborationClientV2 } from '../../lib/dynamic-collaboration-client-v2';
```

### Initializing a Session

```typescript
const result = await dynamicCollaborationClientV2.initiate({
  topic: 'CRM Systems',
  contentType: 'blog-article',
  targetAudience: 'business professionals',
  tone: 'informative',
  keywords: ['CRM', 'customer relationship management', 'sales']
});

const sessionId = result.sessionId;
```

### Getting Session State

```typescript
const result = await dynamicCollaborationClientV2.getState(sessionId);
const state = result.state;
```

### Transitioning to a New Phase

```typescript
import { DynamicWorkflowPhase } from '../../app/(payload)/api/agents/dynamic-collaboration-v2/state';

const result = await dynamicCollaborationClientV2.transitionPhase(
  sessionId, 
  DynamicWorkflowPhase.CREATION
);
```

### Adding a User Message

```typescript
const result = await dynamicCollaborationClientV2.addUserMessage(
  sessionId, 
  'Can you add more information about CRM benefits?'
);

const messageId = result.messageId;
const conversationId = result.conversationId;
```

## Component Integration

### Updating Imports

Replace imports from the old system with imports from the new system:

```typescript
// Old
import { DynamicCollaborationState } from '../../app/(payload)/api/agents/dynamic-collaboration-v2/types';
import { dynamicCollaborationClient } from '../../lib/dynamic-collaboration-client';

// New
import { DynamicCollaborationState } from '../../app/(payload)/api/agents/dynamic-collaboration-v2/state';
import { dynamicCollaborationClientV2 } from '../../lib/dynamic-collaboration-client-v2';
```

### Updating API Calls

#### Old API Calls

```typescript
// Create session
const result = await dynamicCollaborationClient.createSession(formData);
const sessionId = result.sessionId;

// Get session
const state = await dynamicCollaborationClient.getSession(sessionId);

// Send message
const result = await dynamicCollaborationClient.sendMessage(
  sessionId, 
  'Hello, world!', 
  'user_message', 
  'system'
);
```

#### New API Calls

```typescript
// Create session
const result = await dynamicCollaborationClientV2.initiate(formData);
const sessionId = result.sessionId;

// Get session
const result = await dynamicCollaborationClientV2.getState(sessionId);
const state = result.state;

// Send message
const result = await dynamicCollaborationClientV2.addUserMessage(
  sessionId, 
  'Hello, world!'
);
```

## Compatibility Layer

The new client includes compatibility methods for the old API to ease migration:

```typescript
// Old API
const result = await dynamicCollaborationClient.createSession(formData);

// New API with compatibility
const result = await dynamicCollaborationClientV2.createSession(formData);
```

## Type Differences

The new API uses types from the new state management system:

```typescript
// Old
import { DynamicCollaborationState } from '../../app/(payload)/api/agents/dynamic-collaboration-v2/types';

// New
import { 
  DynamicCollaborationState,
  DynamicWorkflowPhase,
  DynamicMessageType
} from '../../app/(payload)/api/agents/dynamic-collaboration-v2/state';
```

## Error Handling

The new API provides more detailed error information:

```typescript
try {
  const result = await dynamicCollaborationClientV2.initiate(formData);
  // Success
} catch (err) {
  if (err instanceof Error) {
    // Handle error with message
    console.error(err.message);
  } else {
    // Handle unknown error
    console.error('An unknown error occurred');
  }
}
```

## Migration Checklist

- [ ] Update imports to use the new client and types
- [ ] Update API calls to use the new methods
- [ ] Update error handling to handle the new error format
- [ ] Test the integration to ensure it works as expected
- [ ] Remove any unused code from the old implementation

## Example Components

- `DynamicCollaborationDashboard.tsx`: Main dashboard component
- `ArticleInitiationForm.tsx`: Form for creating a new session
- `AgentCollaborationGraph.tsx`: Graph visualization of agent collaboration
- `GoalProgressTracker.tsx`: Tracker for goal progress
- `AgentStatusPanel.tsx`: Panel for agent status
- `ArtifactGallery.tsx`: Gallery for artifacts
- `AgentCommunicationPanel.tsx`: Panel for agent communication
- `ReasoningVisualizer.tsx`: Visualizer for reasoning
- `ArticlePreview.tsx`: Preview for the generated article
