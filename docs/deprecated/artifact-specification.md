# Artifact Specification: Enhanced Collaborative Agent Workflow System

## Overview

This document defines the standardized artifact structure for the collaborative agent workflow system. All artifacts generated by agents must conform to this specification to ensure consistency, proper storage, and effective display.

## Core Artifact Structure

```typescript
interface IterativeArtifact {
  // Core identification
  id: string;                                  // Unique identifier
  name: string;                                // Human-readable name
  type: string;                                // Artifact type (e.g., 'market-research', 'seo-keywords')
  
  // Creation metadata
  createdBy: string;                           // Agent ID that created the artifact
  createdAt: string;                           // ISO timestamp of creation
  updatedAt: string;                           // ISO timestamp of last update
  
  // Versioning
  currentVersion: number;                      // Current version number
  iterations: Iteration[];                     // History of versions
  
  // Status
  status: ArtifactStatus;                      // Current status (e.g., 'draft', 'completed')
  qualityScore: number;                        // Quality assessment score (0-1)
  
  // Content (flexible structure based on type)
  content?: string | Record<string, any>;      // Main content of the artifact
  
  // Additional metadata
  metadata?: Record<string, any>;              // Additional metadata
  data?: any;                                  // Legacy support for older implementations
}

interface Iteration {
  version: number;                             // Version number
  timestamp: string;                           // ISO timestamp of this version
  agent: string;                               // Agent that created this version
  content: string | Record<string, any>;       // Content at this version
  feedback?: Array<{                           // Feedback that led to this version
    from: string;                              // Agent providing feedback
    content: string;                           // Feedback content
    timestamp: string;                         // Feedback timestamp
  }>;
  incorporatedConsultations?: string[];        // IDs of consultations incorporated
  changes?: string;                            // Description of changes from previous version
  reasoning?: Reasoning | EnhancedReasoning;   // Reasoning behind the changes
}
```

## Storage in Session State

Artifacts must be stored in the session state using a standardized pattern:

```typescript
// In the session state
artifacts: Record<string, IterativeArtifact>;  // Map of artifact ID to artifact
generatedArtifacts: string[];                  // Array of artifact IDs for tracking
```

## Type-Specific Content Structures

### Market Research Artifact

```typescript
interface MarketResearchContent {
  // Required fields
  audience: {
    demographics: Record<string, any>;         // Demographic information
    psychographics?: Record<string, any>;      // Psychographic information
    needs: string[];                           // Audience needs
    painPoints: string[];                      // Audience pain points
    preferences?: string[];                    // Content preferences
  };
  
  trends: {
    current: string[];                         // Current market trends
    emerging: string[];                        // Emerging trends
    relevance: Record<string, number>;         // Relevance scores for trends
  };
  
  competitors: Array<{
    name: string;                              // Competitor name
    strengths: string[];                       // Competitor strengths
    weaknesses: string[];                      // Competitor weaknesses
    contentStrategy?: string;                  // Competitor content strategy
    marketShare?: number;                      // Estimated market share
  }>;
  
  // Optional fields
  opportunities: string[];                     // Market opportunities
  threats: string[];                           // Market threats
  contentGaps: string[];                       // Content gaps in the market
  recommendations: string[];                   // Strategic recommendations
}
```

### SEO Keyword Artifact

```typescript
interface SeoKeywordContent {
  // Required fields
  primaryKeywords: Array<{
    keyword: string;                           // Primary keyword
    volume: number;                            // Search volume
    difficulty: number;                        // Keyword difficulty (0-100)
    relevance: number;                         // Relevance to topic (0-1)
  }>;
  
  longTailKeywords: Array<{
    keyword: string;                           // Long-tail keyword
    volume?: number;                           // Search volume if available
    difficulty?: number;                       // Keyword difficulty if available
    parentKeyword?: string;                    // Related primary keyword
  }>;
  
  searchIntent: Record<string, {
    intent: 'informational' | 'navigational' | 'transactional' | 'commercial';
    confidence: number;                        // Confidence in intent classification
  }>;
  
  // Optional fields
  semanticKeywords?: string[];                 // Related semantic keywords
  questions?: string[];                        // Related questions
  competitorKeywords?: Record<string, string[]>; // Competitor keyword usage
  keywordGroups?: Record<string, string[]>;    // Grouped keywords by theme
  recommendations?: string[];                  // Keyword usage recommendations
}
```

### Content Strategy Artifact

```typescript
interface ContentStrategyContent {
  // Required fields
  structure: {
    format: string;                            // Content format
    estimatedLength: number;                   // Estimated word count
    components: string[];                      // Required components
  };
  
  sections: Array<{
    title: string;                             // Section title
    purpose: string;                           // Purpose of this section
    keypoints: string[];                       // Key points to cover
    estimatedLength?: number;                  // Estimated section length
    keywords?: string[];                       // Keywords for this section
  }>;
  
  keypoints: string[];                         // Overall key points to cover
  
  // Optional fields
  tone: string;                                // Content tone
  style: string;                               // Writing style
  targetAudience: string;                      // Target audience description
  keywordStrategy?: Record<string, string[]>;  // Keyword placement strategy
  callToAction?: string;                       // Recommended call to action
  visualRecommendations?: string[];            // Visual content recommendations
}
```

### Content Generation Artifact

```typescript
interface ContentGenerationContent {
  // Required fields
  title: string;                               // Content title
  introduction: string;                        // Introduction paragraph(s)
  bodySections: Array<{
    heading: string;                           // Section heading
    content: string;                           // Section content
    subheadings?: Array<{                      // Optional subheadings
      heading: string;                         // Subheading text
      content: string;                         // Subheading content
    }>;
  }>;
  conclusion: string;                          // Conclusion paragraph(s)
  
  // Optional fields
  metaDescription?: string;                    // Meta description for SEO
  tags?: string[];                             // Content tags
  callToAction?: string;                       // Call to action
  citations?: string[];                        // Citations or references
  keywordsUsed?: Record<string, number>;       // Keywords used and count
}
```

### SEO Optimization Artifact

```typescript
interface SeoOptimizationContent {
  // Required fields
  analysis: {
    score: number;                             // Overall SEO score (0-100)
    keywordUsage: Record<string, {
      count: number;                           // Keyword count
      density: number;                         // Keyword density
      recommendation: string;                  // Usage recommendation
    }>;
    readabilityScore: number;                  // Readability score (0-100)
    structureAnalysis: {                       // Structure analysis
      headingStructure: string;                // Heading structure assessment
      paragraphLength: string;                 // Paragraph length assessment
      contentFlow: string;                     // Content flow assessment
    };
  };
  
  recommendations: Array<{
    type: string;                              // Recommendation type
    description: string;                       // Detailed description
    priority: 'high' | 'medium' | 'low';       // Implementation priority
    implementationDetails?: string;            // How to implement
  }>;
  
  metaTags: {
    title: string;                             // Optimized title tag
    description: string;                       // Optimized meta description
    keywords?: string;                         // Meta keywords (if used)
  };
  
  // Optional fields
  optimizedContent?: string;                   // Fully optimized content
  schemaMarkup?: string;                       // Recommended schema markup
  internalLinkingStrategy?: string[];          // Internal linking recommendations
  externalLinkingStrategy?: string[];          // External linking recommendations
}
```

## Artifact Status Values

The `status` field must use one of the following values:

```typescript
type ArtifactStatus =
  | 'draft'        // Initial creation, not yet complete
  | 'in-progress'  // Being actively worked on
  | 'review'       // Ready for review
  | 'in-review'    // Currently being reviewed
  | 'completed'    // Finished and validated
  | 'rejected'     // Failed validation
  | 'approved'     // Explicitly approved
  | 'revised'      // Updated after completion
  | 'final';       // Final version, no more changes
```

## Quality Thresholds

Each artifact type has a defined quality threshold that must be met for the artifact to be considered valid:

```typescript
const QUALITY_THRESHOLDS = {
  MARKET_RESEARCH: 0.75,
  KEYWORD_RESEARCH: 0.75,
  CONTENT_STRATEGY: 0.80,
  CONTENT_GENERATION: 0.75,
  SEO_OPTIMIZATION: 0.80,
  FEEDBACK: 0.70
};
```

## Implementation Guidelines

1. **Creation**: Use a factory function to create new artifacts with proper defaults
2. **Updates**: Always create a new iteration when updating artifact content
3. **Validation**: Validate required fields before changing status to 'completed'
4. **Storage**: Store in session state using the artifact ID as the key
5. **Retrieval**: Use helper functions to retrieve artifacts by ID or type
6. **Display**: Use the `EnhancedArtifactGalleryV2` component for consistent display

## Example Implementation

```typescript
// Create a new market research artifact
function createMarketResearchArtifact(
  topic: string,
  audience: any,
  trends: any,
  competitors: any,
  createdBy: string
): IterativeArtifact {
  const id = uuidv4();
  const timestamp = new Date().toISOString();
  
  const artifact: IterativeArtifact = {
    id,
    name: `Market Research: ${topic}`,
    type: 'market-research',
    createdBy,
    createdAt: timestamp,
    updatedAt: timestamp,
    currentVersion: 1,
    iterations: [
      {
        version: 1,
        timestamp,
        agent: createdBy,
        content: {
          audience,
          trends,
          competitors
        }
      }
    ],
    status: 'completed',
    qualityScore: 0.8,
    content: {
      audience,
      trends,
      competitors
    }
  };
  
  return artifact;
}
```
