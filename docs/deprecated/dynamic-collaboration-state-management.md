# Dynamic Collaboration State Management

This document describes the state management system for the Dynamic Collaboration feature, which provides a dedicated state model and specialized state store for dynamic agent collaboration.

## Overview

The Dynamic Collaboration State Management system consists of three main components:

1. **State Models**: Dedicated data models for dynamic collaboration
2. **State Store**: Specialized storage system for these models
3. **State Manager**: Higher-level operations for state manipulation

This architecture provides several benefits:

- **Type Safety**: Full TypeScript type checking without casting
- **Performance**: No runtime type conversions or adapters
- **Maintainability**: Clear separation of concerns and domain-specific logic
- **Scalability**: Easier to extend with new features
- **Consistency**: Single source of truth for state

## Architecture

### State Models

The state models define the structure of the data used in the dynamic collaboration system. These models are designed to be used directly with the specialized state store without requiring adaptation to fit other state models.

Key models include:

- `DynamicCollaborationState`: The main state model for a collaboration session
- `ContentGoal`: Represents a goal in the content creation process
- `DynamicAgentMessage`: Represents a message between agents
- `DynamicArtifact`: Represents an artifact created during collaboration

### State Store

The state store provides methods for storing, retrieving, and updating state. It uses both in-memory caching and Redis persistence for efficient state management.

Key features:

- **In-memory caching**: Fast access to frequently used state
- **Redis persistence**: Durable storage for state across server restarts
- **Singleton pattern**: Ensures a single instance of the store is used

### State Manager

The state manager provides higher-level operations for state manipulation, such as:

- **Session initialization**: Creating a new collaboration session
- **Phase transitions**: Moving between workflow phases
- **Goal management**: Defining and assigning goals
- **Message handling**: Adding and processing messages
- **Artifact management**: Creating and updating artifacts

## Usage

### Initializing a Session

```typescript
import { DynamicWorkflowOrchestratorV2 } from './dynamic-workflow-orchestrator-v2';

// Initialize a new session
const sessionId = 'session-123';
const success = await DynamicWorkflowOrchestratorV2.initiate(sessionId, {
  topic: 'CRM Systems',
  contentType: 'blog-article',
  targetAudience: 'business professionals',
  tone: 'informative',
  keywords: ['CRM', 'customer relationship management', 'sales']
});
```

### Transitioning Between Phases

```typescript
import { DynamicWorkflowOrchestratorV2, DynamicWorkflowPhase } from './dynamic-workflow-orchestrator-v2';

// Create orchestrator
const orchestrator = new DynamicWorkflowOrchestratorV2(sessionId);

// Transition to a new phase
await orchestrator.transitionToPhase(DynamicWorkflowPhase.CREATION);
```

### Working with State Directly

```typescript
import { createStateManager } from './state';

// Create state manager
const stateManager = createStateManager(sessionId);

// Get current state
const state = await stateManager.getState();

// Add a message
await stateManager.addMessage({
  id: 'msg-123',
  timestamp: new Date().toISOString(),
  from: 'user',
  to: 'system',
  type: 'user_message',
  content: 'Can you add more information about CRM benefits?',
  conversationId: 'conv-123'
});
```

## API Reference

### DynamicWorkflowOrchestratorV2

The main orchestrator class for dynamic collaboration.

#### Static Methods

- `initiate(sessionId: string, params: ContentGenerationParams): Promise<boolean>`
  - Initializes a new collaboration session
  - Returns a boolean indicating success or failure

#### Instance Methods

- `getState(): Promise<DynamicCollaborationState | null>`
  - Gets the current state of the session
  - Returns the state or null if not found

- `transitionToPhase(phase: DynamicWorkflowPhase): Promise<boolean>`
  - Transitions to a new phase
  - Returns a boolean indicating success or failure

- `processUserMessage(messageId: string): Promise<boolean>`
  - Processes a user message
  - Returns a boolean indicating success or failure

### DynamicStateManager

Provides higher-level operations for state manipulation.

#### Methods

- `initializeSession(params: ContentGenerationParams): Promise<boolean>`
  - Initializes a new session
  - Returns a boolean indicating success

- `getState(): Promise<DynamicCollaborationState | null>`
  - Gets the current state
  - Returns the state or null if not found

- `updatePhase(phase: DynamicWorkflowPhase): Promise<boolean>`
  - Updates the workflow phase
  - Returns a boolean indicating success

- `defineGoals(goalTemplates: Array<...>): Promise<string[]>`
  - Defines goals for the session
  - Returns an array of created goal IDs

- `assignGoal(goalId: string, agentId: string): Promise<boolean>`
  - Assigns a goal to an agent
  - Returns a boolean indicating success

- `addMessage(message: DynamicAgentMessage): Promise<string>`
  - Adds a message to the state
  - Returns the message ID

- `addSystemMessage(message: DynamicAgentMessage): Promise<string>`
  - Adds a system message to the state
  - Returns the message ID

- `addArtifact(artifact: DynamicArtifact): Promise<string>`
  - Adds an artifact to the state
  - Returns the artifact ID

### DynamicCollaborationStateStore

Provides methods for storing, retrieving, and updating state.

#### Methods

- `getState(sessionId: string): Promise<DynamicCollaborationState | null>`
  - Gets state for a session
  - Returns the session state or null if not found

- `setState(sessionId: string, state: DynamicCollaborationState): Promise<void>`
  - Sets state for a session

- `updateState(sessionId: string, updateFn: (state: DynamicCollaborationState | null) => DynamicCollaborationState | null): Promise<void>`
  - Updates state for a session using an update function

- `deleteState(sessionId: string): Promise<void>`
  - Deletes state for a session

- `listSessions(): Promise<string[]>`
  - Lists all session IDs
  - Returns an array of session IDs

## JSON-RPC API

The Dynamic Collaboration system exposes a JSON-RPC API for client interaction.

### Methods

- `initiate`
  - Initializes a new collaboration session
  - Parameters: `topic`, `contentType`, `targetAudience`, `tone`, `keywords`, `additionalInstructions`, `referenceUrls`
  - Returns: `{ sessionId, success }`

- `getState`
  - Gets the current state of a session
  - Parameters: `sessionId`
  - Returns: `{ state }`

- `transitionPhase`
  - Transitions to a new phase
  - Parameters: `sessionId`, `phase`
  - Returns: `{ success }`

- `processUserMessage`
  - Processes a user message
  - Parameters: `sessionId`, `messageId`
  - Returns: `{ success }`

- `addUserMessage`
  - Adds a user message and processes it
  - Parameters: `sessionId`, `content`
  - Returns: `{ messageId, conversationId }`

## Implementation Details

### State Persistence

The state is persisted in both memory and Redis:

1. **In-memory cache**: Provides fast access to frequently used state
2. **Redis**: Provides durable storage with expiration (7 days by default)

### Error Handling

All methods include comprehensive error handling with detailed logging. Errors are caught, logged, and appropriate error responses are returned.

### Logging

The system uses a structured logging approach with context information for better debugging and monitoring.

## Migration Guide

To migrate from the old state management system to the new one:

1. Replace imports from the old system with imports from the new system:

```typescript
// Old
import { DynamicWorkflowOrchestrator } from './dynamic-workflow-orchestrator';

// New
import { DynamicWorkflowOrchestratorV2 } from './dynamic-workflow-orchestrator-v2';
```

2. Update API endpoints to use the new JSON-RPC API:

```typescript
// Old
fetch('/api/agents/dynamic-collaboration-v2/jsonrpc', {...});

// New
fetch('/api/agents/dynamic-collaboration-v2/jsonrpc-v2', {...});
```

3. Update client code to use the new API methods and parameters.

## Future Enhancements

Planned enhancements for the state management system:

1. **Database-backed state store**: Move from Redis to a more structured database
2. **Optimistic concurrency control**: Handle concurrent updates to the same state
3. **State versioning**: Track changes to state over time
4. **State snapshots**: Create point-in-time snapshots for rollback
5. **State validation**: Validate state against schema before saving
