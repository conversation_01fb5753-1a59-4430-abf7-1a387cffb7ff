# Goal-Based Orchestration System Documentation

## System Overview

The Goal-Based Orchestration System is a flexible, agent-driven content generation platform that uses a goal-oriented approach to create high-quality content. Unlike fixed-phase systems, this architecture allows for dynamic collaboration between specialized agents working toward specific goals.

### Key Components

1. **State Management**: Centralized state store with a unified schema for goals, artifacts, and messages
2. **Workflow Engine**: Goal orchestration, phase transitions, and artifact creation
3. **Agent System**: Specialized agents for different content creation tasks
4. **API Layer**: Endpoints for session management and workflow control
5. **Error Recovery System**: Robust error handling with retry mechanisms
6. **Performance Optimizer**: Optimizes system performance and resource usage
7. **Artifact Decision Framework**: Determines which artifacts to create for each goal
8. **Artifact Evaluation Service**: Evaluates artifact quality against goal criteria

## Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        API Layer                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  Session API    │  │  Progress API   │  │  Artifact API   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└───────────────────────────────┬─────────────────────────────────┘
                                │
┌───────────────────────────────▼─────────────────────────────────┐
│                      Workflow Engine                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │Goal Orchestrator│  │ Goal Processor  │  │Phase Transitions│  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└───────────────────────────────┬─────────────────────────────────┘
                                │
┌───────────────────────────────▼─────────────────────────────────┐
│                       Agent System                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  Agent Factory  │  │Specialized Agents│  │ Message Handling│  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└───────────────────────────────┬─────────────────────────────────┘
                                │
┌───────────────────────────────▼─────────────────────────────────┐
│                     State Management                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  State Store    │  │  State Manager  │  │  Unified Schema │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## Message Flow and State Transitions

### Message Types

The system uses a unified message type system defined in `unified-schema.ts`:

- `SYSTEM`: System-level messages
- `SYSTEM_MESSAGE`: Informational system messages
- `SYSTEM_ERROR`: Error messages
- `USER`: User input messages
- `AGENT`: Agent-to-agent communication
- `GOAL_UPDATE`: Goal status updates
- `ARTIFACT_UPDATE`: Artifact creation/modification notifications
- `WORKFLOW_TRANSITION`: Phase transition notifications
- `FEEDBACK_REQUEST`: Requests for feedback on artifacts
- `FEEDBACK_RESPONSE`: Responses to feedback requests
- `PROGRESS_UPDATE`: Workflow progress updates

### Message Flow

1. **Session Initialization**:
   - User creates a session with topic and parameters
   - System initializes state and defines high-level goals
   - Initial RESEARCH goal is activated

2. **Goal Assignment**:
   - Orchestrator sends `GOAL_UPDATE` messages to appropriate agents
   - Agents acknowledge receipt with `SYSTEM_MESSAGE` responses

3. **Artifact Creation**:
   - Agents create artifacts and send `ARTIFACT_UPDATE` messages
   - System evaluates artifacts and updates their status

4. **Artifact Evaluation**:
   - System automatically evaluates artifacts against goal criteria
   - Evaluation includes quality scoring, criteria assessment, and feedback
   - Artifacts are approved or rejected based on evaluation results
   - Approved artifacts mark their associated goals as completed

5. **Feedback Loop**:
   - Agents can request feedback via `FEEDBACK_REQUEST` messages
   - Other agents respond with `FEEDBACK_RESPONSE` messages
   - Original agent may improve artifacts based on feedback

6. **Goal Completion**:
   - When artifacts are approved, goals are marked as completed
   - System activates the next goal in the workflow
   - `WORKFLOW_TRANSITION` messages indicate phase changes

## Frontend Implementation

The frontend implementation of the goal-based collaboration system consists of several key components:

1. **GoalBasedDashboard**: Main dashboard component that manages the overall UI and session state
2. **GoalVisualization**: Visualizes the goal hierarchy and status
3. **WorkflowProgressVisualization**: Shows the progress of the workflow through different phases
4. **ArtifactEvaluationDisplay**: Displays artifacts and their evaluations
5. **ArticlePreview**: Shows a preview of the generated article
6. **AgentCollaborationGraphForGoals**: Visualizes the collaboration between agents, goals, and artifacts

### Client Implementation

The `goal-orchestrator-client.ts` provides a client-side API for interacting with the backend:

```typescript
// Example: Using the client to initialize a session
const result = await goalOrchestratorClient.initiate({
  topic: 'CRM Systems',
  contentType: 'blog-article',
  targetAudience: 'business professionals',
  tone: 'informative',
  keywords: ['CRM', 'customer relationship management', 'sales']
});

const sessionId = result.sessionId;

// Example: Getting the current state
const state = await goalOrchestratorClient.getState(sessionId);

// Example: Processing goals
await goalOrchestratorClient.progressSession(sessionId);

// Example: Forcing progress (for debugging)
await goalOrchestratorClient.forceProgress(sessionId);
```

### Dashboard Implementation

The dashboard provides a comprehensive view of the goal-based collaboration process:

1. **Overview Tab**: Shows the goal hierarchy and workflow progress
2. **Artifacts Tab**: Displays artifacts and their evaluations
3. **Article Preview Tab**: Shows a preview of the generated article
4. **Collaboration Graph Tab**: Visualizes the collaboration between agents, goals, and artifacts

The dashboard also provides controls for:
- Starting a new session
- Progressing the session
- Forcing progress (for debugging)
- Viewing detailed information about goals and artifacts

### Collaboration Graph Visualization

The `AgentCollaborationGraphForGoals` component provides a visual representation of the collaboration between agents, goals, and artifacts:

```typescript
// Example: AgentCollaborationGraphForGoals component
const AgentCollaborationGraphForGoals: React.FC<AgentCollaborationGraphForGoalsProps> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  // Build graph data from state
  useEffect(() => {
    if (!state) return;

    const nodes: GraphNode[] = [];
    const links: GraphLink[] = [];

    // Add agent nodes
    // Add goal nodes
    // Add artifact nodes
    // Add links between nodes

    setGraphData({ nodes, links });
  }, [state, theme]);

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2, height: '100%', minHeight: '500px' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Agent Collaboration Network</Typography>
        <Box>
          {/* Zoom controls */}
        </Box>
      </Box>

      <Box sx={{ display: 'flex', height: 'calc(100% - 50px)', minHeight: '450px' }}>
        <Box sx={{ flexGrow: 1, position: 'relative', border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
          {loading ? (
            <CircularProgress />
          ) : (
            <ForceGraph2D
              graphData={graphData}
              nodeLabel={(node: GraphNode) => `${node.name} (${node.type})`}
              nodeColor={(node: GraphNode) => node.color}
              nodeVal={(node: GraphNode) => node.val}
              linkWidth={(link: GraphLink) => Math.sqrt(link.value) * 0.5}
              linkColor={(link: GraphLink) => link.color || theme.palette.grey[400]}
              linkDirectionalParticles={4}
              onNodeClick={handleNodeClick}
              onLinkClick={handleLinkClick}
              onNodeHover={setHoveredNode}
              cooldownTicks={100}
              linkDirectionalParticleSpeed={0.01}
            />
          )}
        </Box>
      </Box>
    </Paper>
  );
};
```

The graph visualization shows:
- Agents as nodes with their respective colors
- Goals as nodes with colors indicating their status (completed, active, pending)
- Artifacts as nodes connected to the agents that created them and the goals they're associated with
- Connections between agents and goals to show which agent is assigned to which goal
- Connections between goals and artifacts to show which artifacts were produced for which goals

This visualization helps users understand the complex relationships in the goal-based collaboration system at a glance.

### Artifact Evaluation Display

The `ArtifactEvaluationDisplay` component provides a detailed view of artifact evaluations:

```typescript
// Example: ArtifactEvaluationDisplay component
const ArtifactEvaluationDisplay: React.FC<ArtifactEvaluationDisplayProps> = ({
  sessionId,
  artifacts,
  loading = false,
  onRefresh
}) => {
  // Load artifact evaluation
  const loadArtifactEvaluation = async (artifactId: string) => {
    try {
      setLoadingEvaluations(true);
      console.log('Loading evaluation for artifact:', artifactId);

      // Try to get evaluation from the API
      let evaluation;
      try {
        evaluation = await goalOrchestratorClient.getArtifactEvaluation(sessionId, artifactId);
        console.log('Evaluation from API:', evaluation);
      } catch (apiError) {
        console.warn('Error fetching evaluation from API:', apiError);
        evaluation = null;
      }

      if (evaluation) {
        console.log('Setting evaluation from API');
        setEvaluations([evaluation]);
      } else {
        // Fallback to metadata if API doesn't return evaluation
        const artifact = artifacts[artifactId];
        console.log('Artifact from state:', artifact);

        if (artifact?.metadata?.evaluation) {
          console.log('Using evaluation from artifact metadata:', artifact.metadata.evaluation);
          setEvaluations([artifact.metadata.evaluation]);
        } else {
          // Check if the artifact has any evaluation data in other formats
          const possibleEvaluationData = extractEvaluationData(artifact);

          if (possibleEvaluationData) {
            console.log('Found evaluation data in alternative format:', possibleEvaluationData);
            setEvaluations([possibleEvaluationData]);
          } else {
            console.log('No evaluation data found at all');
            setEvaluations([]);
          }
        }
      }
    } catch (error) {
      console.error('Error loading artifact evaluation:', error);
      // Fallback handling...
    } finally {
      setLoadingEvaluations(false);
    }
  };

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2, height: '100%', minHeight: '500px' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Artifact Evaluations</Typography>
        <Box>
          {/* Controls */}
        </Box>
      </Box>

      {/* Artifact selection and evaluation display */}
      {/* Evaluation details with criteria, scores, and feedback */}
    </Paper>
  );
};
```

The evaluation display shows:
- Overall evaluation status (approved/rejected)
- Quality score
- Criteria-based assessment
- Detailed feedback
- Improvement suggestions

This component helps users understand why artifacts were approved or rejected and what improvements might be needed.

### State Transitions

```
┌──────────────┐
│  Initialize  │
│   Session    │
└──────┬───────┘
       │
       ▼
┌──────────────┐
│  Define      │
│  High-Level  │
│  Goals       │
└──────┬───────┘
       │
       ▼
┌──────────────┐
│  Activate    │
│  Research    │◄────────────┐
│  Goal        │             │
└──────┬───────┘             │
       │                     │
       ▼                     │
┌──────────────┐             │
│  Create      │             │
│  Research    │             │
│  Artifacts   │             │
└──────┬───────┘             │
       │                     │
       ▼                     │
┌──────────────┐             │
│  Evaluate    │    No       │
│  Artifacts   ├─────────────┘
└──────┬───────┘
       │ Yes
       ▼
┌──────────────┐
│  Complete    │
│  Research    │
│  Goal        │
└──────┬───────┘
       │
       ▼
┌──────────────┐
│  Activate    │◄────────────┐
│  Content     │             │
│  Goal        │             │
└──────┬───────┘             │
       │                     │
       ▼                     │
┌──────────────┐             │
│  Create      │             │
│  Content     │             │
│  Artifacts   │             │
└──────┬───────┘             │
       │                     │
       ▼                     │
┌──────────────┐             │
│  Evaluate    │    No       │
│  Artifacts   ├─────────────┘
└──────┬───────┘
       │ Yes
       ▼
┌──────────────┐
│  Complete    │
│  Content     │
│  Goal        │
└──────┬───────┘
       │
       ▼
┌──────────────┐
│  Activate    │◄────────────┐
│  Quality     │             │
│  Goal        │             │
└──────┬───────┘             │
       │                     │
       ▼                     │
┌──────────────┐             │
│  Create      │             │
│  Quality     │             │
│  Artifacts   │             │
└──────┬───────┘             │
       │                     │
       ▼                     │
┌──────────────┐             │
│  Evaluate    │    No       │
│  Artifacts   ├─────────────┘
└──────┬───────┘
       │ Yes
       ▼
┌──────────────┐
│  Complete    │
│  Session     │
└──────────────┘
```

## Workflow Phases

The system progresses through five main phases:

1. **Planning**: Initial goal definition and session setup
2. **Research**: Market research and keyword analysis
3. **Creation**: Content strategy and content creation
4. **Review**: SEO optimization and quality assessment
5. **Finalization**: Final review and completion

## Specialized Agents

The system includes the following specialized agents:

1. **Market Research Agent**: Analyzes market trends and target audience
2. **Keyword Analysis Agent**: Identifies relevant keywords and search terms
3. **Content Strategy Agent**: Develops content structure and approach
4. **Content Creation Agent**: Creates the actual content
5. **SEO Optimization Agent**: Optimizes content for search engines
6. **Quality Assessment Agent**: Evaluates content quality and suggests improvements

## Goal Types

Goals are categorized into high-level and specific types:

### High-Level Goals
- `RESEARCH`: Overall research goal
- `CONTENT`: Overall content creation goal
- `QUALITY`: Overall quality assessment goal

### Specific Goals
- `MARKET_RESEARCH`: Market research and analysis
- `KEYWORD_ANALYSIS`: Keyword identification and analysis
- `CONTENT_STRATEGY`: Content structure and approach
- `CONTENT_CREATION`: Actual content writing
- `SEO_OPTIMIZATION`: Search engine optimization
- `QUALITY_ASSESSMENT`: Quality evaluation and improvement

## API Endpoints

1. **POST /api/agents/dynamic-collaboration-v3**
   - Creates a new collaboration session
   - Parameters: topic, contentType, targetAudience, tone, keywords

2. **GET /api/agents/dynamic-collaboration-v3?sessionId=xxx**
   - Gets the current state of a session
   - Parameters: sessionId

3. **POST /api/agents/dynamic-collaboration-v3/progress**
   - Progresses a session by processing goals
   - Parameters: sessionId, steps (optional)
