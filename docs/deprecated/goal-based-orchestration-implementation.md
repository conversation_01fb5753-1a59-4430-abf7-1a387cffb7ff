# Goal-Based Orchestration Implementation Details

## Core Components

### 1. State Management

#### Unified Schema (`unified-schema.ts`)

The unified schema defines all data structures used in the system:

- **Enums**: WorkflowPhase, MilestoneStatus, GoalType, GoalStatus, MessageType, SessionStatus, ArtifactStatus
- **Schemas**: Goal, Message, Artifact, FeedbackData, WorkflowProgress, CollaborationState
- **Types**: Derived TypeScript types from the schemas

#### State Manager (`manager.ts`)

The StateManager class provides methods for:

- Initializing sessions
- Defining and managing goals
- Creating and updating artifacts
- Adding messages
- Updating workflow progress
- Transactional state updates with optimistic concurrency control

#### State Store (`typed-store.ts`)

The TypedStateStore provides:

- Type-safe state storage and retrieval
- Transactional updates with optimistic concurrency control
- Schema validation using Zod

### 2. Workflow Engine

#### Goal Orchestrator (`goal-orchestrator.ts`)

The GoalOrchestrator is responsible for:

- Initializing sessions with high-level goals
- Breaking down high-level goals into specific goals
- Triggering specialized agents for goal processing
- Tracking workflow progress

```typescript
// Example: Initializing a session
public static async initiate(sessionId: string, params: ContentGenerationParams): Promise<boolean> {
  const orchestrator = new GoalOrchestrator(sessionId);

  // Initialize the session with error recovery
  const success = await orchestrator.errorRecovery.executeWithRetry(
    async () => {
      // Initialize the session
      const initSuccess = await orchestrator.stateManager.initializeSession(params);
      if (!initSuccess) {
        throw new Error('Failed to initialize session');
      }
      return true;
    },
    'GoalOrchestrator',
    'initializeSession',
    { details: { topic: params.topic, contentType: params.contentType } }
  );

  // Define initial high-level goals with error recovery
  await orchestrator.errorRecovery.executeWithRetry(
    async () => orchestrator.defineInitialGoals(),
    'GoalOrchestrator',
    'defineInitialGoals',
    { details: { topic: params.topic } }
  );

  // Update workflow progress
  await orchestrator.progressTracker.updatePhaseProgress(WorkflowPhase.PLANNING, 25);

  // Trigger initial goal processing with error recovery
  await orchestrator.errorRecovery.executeWithRetry(
    async () => orchestrator.processGoals(),
    'GoalOrchestrator',
    'processGoals',
    { details: { initialProcessing: true } }
  );

  return true;
}
```

#### Goal Processor (`goal-processor-fixed.ts`)

The GoalProcessorFixed handles:

- Processing active goals
- Creating artifacts for goals using specialized agents
- Evaluating artifacts against goal criteria
- Updating workflow progress
- Activating the next goal when current goals are completed
- Error handling and recovery

```typescript
// Example: Processing goals
public async processGoals(): Promise<boolean> {
  try {
    // Get current state
    const state = await this.stateManager.getState();
    if (!state) {
      throw new Error('Session state not found');
    }

    // Get active goals
    const activeGoals = state.goals.activeIds.map(id => state.goals.byId[id]).filter(Boolean);

    // If no active goals, check if we need to create more specific goals
    if (activeGoals.length === 0) {
      logger.info(`No active goals found, checking if we need to create more specific goals`, {
        sessionId: this.sessionId
      });
      return await this.createMoreSpecificGoals();
    }

    logger.info(`Processing ${activeGoals.length} active goals`, {
      sessionId: this.sessionId,
      activeGoalIds: activeGoals.map(g => g.id),
      activeGoalTypes: activeGoals.map(g => g.type)
    });

    // Process each active goal
    for (const goal of activeGoals) {
      await this.processGoal(goal);
    }

    // Update workflow progress
    await this.updateWorkflowProgress();

    return true;
  } catch (error) {
    logger.error(`Error processing goals`, {
      sessionId: this.sessionId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });

    return false;
  }
}
```

```typescript
// Example: Evaluating an artifact
async evaluateArtifact(goalId: string, artifactId: string): Promise<boolean> {
  try {
    // Get the current state
    const state = await this.stateManager.getState();
    if (!state) {
      throw new Error('Session state not found');
    }

    // Get the goal and artifact
    const goal = state.goals.byId[goalId];
    const artifact = state.artifacts[artifactId];

    if (!goal) {
      throw new Error(`Goal ${goalId} not found`);
    }

    if (!artifact) {
      throw new Error(`Artifact ${artifactId} not found`);
    }

    // Get the appropriate agent for evaluation
    const agent = AgentFactory.createAgentForGoalType(goal.type, this.sessionId);

    // Generate evaluation
    const evaluation = await agent.evaluateArtifact(artifact, goal, state);
    const now = new Date().toISOString();

    // Update artifact with evaluation
    await this.stateManager.updateState(currentState => {
      if (!currentState) return currentState;

      const updatedArtifact = {
        ...currentState.artifacts[artifactId],
        status: evaluation.meetsRequirements ? ArtifactStatus.APPROVED : ArtifactStatus.REJECTED,
        updatedAt: now,
        metadata: {
          ...currentState.artifacts[artifactId].metadata,
          evaluation
        }
      };

      return {
        ...currentState,
        artifacts: {
          ...currentState.artifacts,
          [artifactId]: updatedArtifact
        },
        lastUpdated: now
      };
    });

    // If approved, mark the goal as completed
    if (evaluation.meetsRequirements) {
      await this.completeGoal(goalId);
    }

    return true;
  } catch (error) {
    logger.error(`Error evaluating artifact`, {
      sessionId: this.sessionId,
      goalId,
      artifactId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });

    return false;
  }
}
```

### 3. Additional Components

#### Error Recovery System (`error-recovery-system.ts`)

The ErrorRecoverySystem provides:

- Retry mechanisms with exponential backoff
- Error logging and tracking
- Circuit breaker pattern to prevent cascading failures

```typescript
// Example: Executing with retry
async executeWithRetry<T>(
  operation: () => Promise<T>,
  component: string,
  method: string,
  context?: any
): Promise<T> {
  let attempt = 0;
  let delay = this.retryConfig.initialDelayMs;

  while (attempt < this.retryConfig.maxRetries) {
    try {
      return await operation();
    } catch (error) {
      attempt++;

      logger.warn(`Retry attempt ${attempt}/${this.retryConfig.maxRetries} for ${component}.${method}`, {
        sessionId: this.sessionId,
        error: error instanceof Error ? error.message : String(error),
        component,
        method,
        context
      });

      if (attempt >= this.retryConfig.maxRetries) {
        throw error;
      }

      // Exponential backoff with jitter
      await new Promise(resolve => setTimeout(resolve, delay * (0.75 + Math.random() * 0.5)));
      delay = Math.min(delay * this.retryConfig.backoffFactor, this.retryConfig.maxDelayMs);
    }
  }

  throw new Error(`Max retries exceeded for ${component}.${method}`);
}
```

#### Performance Optimizer (`performance-optimizer.ts`)

The PerformanceOptimizer provides:

- Resource usage optimization
- Caching of expensive operations
- Prioritization of critical tasks
- Performance monitoring and metrics

#### Artifact Decision Framework (`artifact-decision-framework.ts`)

The ArtifactDecisionFramework provides:

- Logic to determine which artifacts to create for each goal
- Prioritization of artifact creation based on content needs
- Decision-making for artifact revisions

#### Artifact Evaluation Service (`artifact-evaluation-service.ts`)

The ArtifactEvaluationService provides:

- Evaluation of artifacts against goal criteria
- Quality scoring and feedback generation
- Approval/rejection decisions

### 4. API Layer

#### Route Handlers (`route.ts`, `progress/route.ts`, `debug/route.ts`)

The API endpoints handle:

- Session creation and initialization
- State retrieval
- Goal processing and workflow progression
- Debug operations for development and troubleshooting

```typescript
// Example: Progress endpoint
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Parse request body
    const { sessionId, steps = 1 } = await req.json();

    // Validate input
    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    // Create goal processor
    const goalProcessor = new GoalProcessorFixed(sessionId);

    // Process goals
    let success = false;
    for (let i = 0; i < steps; i++) {
      success = await goalProcessor.processGoals();
      if (!success) {
        break;
      }
    }

    // Get updated state
    const stateManager = new StateManager(sessionId);
    const updatedState = await stateManager.getState();

    // Return updated state
    return NextResponse.json({
      success,
      state: updatedState
    });
  } catch (error) {
    logger.error('Error in progress endpoint', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
```

### 5. Agent System

#### Agent Base (`agent-base.ts`)

The AgentBase class provides:

- Message handling infrastructure
- Goal processing interface
- Message creation and sending utilities

```typescript
// Example: Processing a message
public async processMessage(message: any): Promise<any> {
  // Get current state
  const state = await this.stateManager.getState();

  // Normalize message
  const normalizedMessage: Message = {
    id: message.id,
    timestamp: message.timestamp,
    from: message.from,
    to: message.to,
    type: message.type as MessageType,
    content: message.content,
    conversationId: message.conversationId || message.id,
    replyTo: message.replyTo,
    metadata: message.metadata
  };

  // Get handler for message type
  const handler = this.handlers.get(normalizedMessage.type);

  // Process message with handler
  const response = await handler(normalizedMessage, state, this.stateManager);

  // Send response
  if (response) {
    await this.messageBus.sendMessage(response);
  }

  return response;
}
```

#### Agent Factory (`agent-factory.ts`)

The AgentFactory creates and manages specialized agents:

- Creates agent instances with the correct session ID
- Maps goal types to appropriate agents
- Provides evaluation agents for artifact assessment

```typescript
// Example: Getting an agent for a goal type
getAgentForGoalType(goalType: GoalType): Agent | null {
  switch (goalType) {
    case GoalType.RESEARCH:
    case GoalType.MARKET_RESEARCH:
      return this.marketResearchAgent;

    case GoalType.KEYWORD_ANALYSIS:
      return this.keywordAnalysisAgent;

    case GoalType.CONTENT:
    case GoalType.CONTENT_CREATION:
      return this.contentCreationAgent;

    // Other cases...

    default:
      logger.warn(`No agent found for goal type: ${goalType}`);
      return null;
  }
}
```

#### Specialized Agents

Each specialized agent extends the AgentBase class and implements:

- Message handlers for specific message types
- Goal processing logic for their specific domain
- Artifact generation capabilities

```typescript
// Example: Content Creation Agent
export class ContentCreationAgent extends AgentBase {
  protected registerHandlers(): void {
    this.registerHandler(MessageType.GOAL_UPDATE, this.handleGoalAssignment.bind(this));
    this.registerHandler(MessageType.ARTIFACT_UPDATE, this.handleArtifactRequest.bind(this));
    this.registerHandler(MessageType.FEEDBACK_RESPONSE, this.handleFeedback.bind(this));
    this.registerHandler(MessageType.FEEDBACK_REQUEST, this.handleConsultationRequest.bind(this));
  }

  public async processGoal(goalId: string): Promise<boolean> {
    // Implementation for processing a content creation goal
  }

  private async handleGoalAssignment(message: Message, state: CollaborationState, stateManager: StateManager): Promise<Message | null> {
    // Implementation for handling goal assignment
  }

  // Other handlers...
}
```

### 4. API Layer

#### Route Handlers (`route.ts`, `progress/route.ts`)

The API endpoints handle:

- Session creation and initialization
- State retrieval
- Goal processing and workflow progression

```typescript
// Example: Progress endpoint
export async function POST(req: NextRequest): Promise<NextResponse> {
  // Parse request body
  const { sessionId, steps = 1 } = await req.json();

  // Create goal processor
  const goalProcessor = new GoalProcessorFixed(sessionId);

  // Process goals
  for (let i = 0; i < steps; i++) {
    success = await goalProcessor.processGoals();
    if (!success) {
      break;
    }
  }

  // Get updated state
  const updatedState = await orchestrator.getState();

  // Return updated state
  return NextResponse.json({
    success: true,
    state: updatedState
  });
}
```

## Message Flow Details

### 1. Session Initialization

```
User → POST /api/agents/dynamic-collaboration-v3 → GoalOrchestrator.initiate()
  ↓
StateManager.initializeSession() → Creates initial state
  ↓
GoalOrchestrator.defineInitialGoals() → Creates high-level goals
  ↓
GoalOrchestrator.processGoals() → Activates initial research goal
```

### 2. Goal Processing

```
User → POST /api/agents/dynamic-collaboration-v3/progress → GoalProcessorFixed.processGoals()
  ↓
For each active goal:
  ↓
GoalProcessorFixed.createArtifactsForGoal() → Creates artifact using appropriate agent
  ↓
Agent.generateArtifact() → Creates artifact content
  ↓
GoalProcessorFixed.evaluateArtifact() → Evaluates artifact quality
  ↓
If all artifacts approved:
  ↓
GoalProcessorFixed.activateNextGoal() → Activates next goal in workflow
```

### 3. Agent Communication

```
GoalOrchestrator → MessageType.GOAL_UPDATE → Agent
  ↓
Agent processes goal and creates artifact
  ↓
Agent → MessageType.ARTIFACT_UPDATE → System
  ↓
System evaluates artifact
  ↓
If improvements needed:
  ↓
Agent A → MessageType.FEEDBACK_REQUEST → Agent B
  ↓
Agent B → MessageType.FEEDBACK_RESPONSE → Agent A
  ↓
Agent A improves artifact based on feedback
```

## State Transitions Details

### Goal Status Transitions

```
PENDING → ACTIVE → IN_PROGRESS → COMPLETED
    ↓         ↓          ↓
    ↓         ↓          → FAILED
    ↓         → BLOCKED
    → PAUSED
```

### Artifact Status Transitions

```
DRAFT → REVIEW → APPROVED → PUBLISHED
           ↓
           → REJECTED → DRAFT
```

### Workflow Phase Transitions

```
PLANNING → RESEARCH → CREATION → REVIEW → FINALIZATION
```

## Error Handling and Recovery

The system includes robust error handling:

- **ErrorRecoverySystem**: Records errors and provides retry capabilities
- **Transactional Updates**: Ensures state consistency even during failures
- **Logging**: Comprehensive logging for debugging and monitoring

## Performance Optimization

The system includes performance optimizations:

- **PerformanceOptimizer**: Optimizes resource usage during processing
- **Pagination**: Support for paginated collections in the state
- **Selective Updates**: Only updates changed parts of the state
