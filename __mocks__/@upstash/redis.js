/**
 * Mock for @upstash/redis
 * Provides in-memory Redis simulation for testing
 */

class MockRedis {
  constructor(config) {
    this.config = config;
    this.data = new Map();
    this.expirations = new Map();
  }

  async ping() {
    return 'PONG';
  }

  async get(key) {
    this._cleanupExpired();
    return this.data.get(key) || null;
  }

  async set(key, value, options = {}) {
    this.data.set(key, value);
    
    if (options.px) {
      // Set expiration in milliseconds
      this.expirations.set(key, Date.now() + options.px);
    } else if (options.ex) {
      // Set expiration in seconds
      this.expirations.set(key, Date.now() + (options.ex * 1000));
    }

    if (options.nx && this.data.has(key)) {
      return null; // Key already exists
    }

    return 'OK';
  }

  async del(key) {
    const existed = this.data.has(key);
    this.data.delete(key);
    this.expirations.delete(key);
    return existed ? 1 : 0;
  }

  async exists(key) {
    this._cleanupExpired();
    return this.data.has(key) ? 1 : 0;
  }

  async eval(script, keys, args) {
    // Simple implementation for lock release script
    if (script.includes('redis.call("get", KEYS[1]) == ARGV[1]')) {
      const key = keys[0];
      const value = args[0];
      const currentValue = this.data.get(key);
      
      if (currentValue === value) {
        this.data.delete(key);
        this.expirations.delete(key);
        return 1;
      }
      return 0;
    }
    
    return null;
  }

  async expire(key, seconds) {
    if (this.data.has(key)) {
      this.expirations.set(key, Date.now() + (seconds * 1000));
      return 1;
    }
    return 0;
  }

  async ttl(key) {
    const expiration = this.expirations.get(key);
    if (!expiration) {
      return -1; // No expiration
    }
    
    const remaining = Math.ceil((expiration - Date.now()) / 1000);
    return remaining > 0 ? remaining : -2; // -2 means expired
  }

  async keys(pattern) {
    this._cleanupExpired();
    const allKeys = Array.from(this.data.keys());
    
    if (pattern === '*') {
      return allKeys;
    }
    
    // Simple pattern matching (only supports * wildcard)
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    return allKeys.filter(key => regex.test(key));
  }

  async flushall() {
    this.data.clear();
    this.expirations.clear();
    return 'OK';
  }

  async incr(key) {
    const current = parseInt(this.data.get(key) || '0', 10);
    const newValue = current + 1;
    this.data.set(key, newValue.toString());
    return newValue;
  }

  async decr(key) {
    const current = parseInt(this.data.get(key) || '0', 10);
    const newValue = current - 1;
    this.data.set(key, newValue.toString());
    return newValue;
  }

  async hget(key, field) {
    const hash = this.data.get(key);
    if (hash && typeof hash === 'object') {
      return hash[field] || null;
    }
    return null;
  }

  async hset(key, field, value) {
    let hash = this.data.get(key);
    if (!hash || typeof hash !== 'object') {
      hash = {};
    }
    hash[field] = value;
    this.data.set(key, hash);
    return 1;
  }

  async hgetall(key) {
    const hash = this.data.get(key);
    return (hash && typeof hash === 'object') ? hash : {};
  }

  async hdel(key, field) {
    const hash = this.data.get(key);
    if (hash && typeof hash === 'object' && field in hash) {
      delete hash[field];
      return 1;
    }
    return 0;
  }

  async lpush(key, ...values) {
    let list = this.data.get(key);
    if (!Array.isArray(list)) {
      list = [];
    }
    list.unshift(...values);
    this.data.set(key, list);
    return list.length;
  }

  async rpush(key, ...values) {
    let list = this.data.get(key);
    if (!Array.isArray(list)) {
      list = [];
    }
    list.push(...values);
    this.data.set(key, list);
    return list.length;
  }

  async lpop(key) {
    const list = this.data.get(key);
    if (Array.isArray(list) && list.length > 0) {
      return list.shift();
    }
    return null;
  }

  async rpop(key) {
    const list = this.data.get(key);
    if (Array.isArray(list) && list.length > 0) {
      return list.pop();
    }
    return null;
  }

  async llen(key) {
    const list = this.data.get(key);
    return Array.isArray(list) ? list.length : 0;
  }

  async lrange(key, start, stop) {
    const list = this.data.get(key);
    if (!Array.isArray(list)) {
      return [];
    }
    
    if (stop === -1) {
      return list.slice(start);
    }
    return list.slice(start, stop + 1);
  }

  // Helper method to clean up expired keys
  _cleanupExpired() {
    const now = Date.now();
    for (const [key, expiration] of this.expirations.entries()) {
      if (now >= expiration) {
        this.data.delete(key);
        this.expirations.delete(key);
      }
    }
  }

  // Method to get current state (for testing)
  _getState() {
    this._cleanupExpired();
    return {
      data: Object.fromEntries(this.data),
      expirations: Object.fromEntries(this.expirations)
    };
  }

  // Method to reset state (for testing)
  _reset() {
    this.data.clear();
    this.expirations.clear();
  }
}

// Export the mock Redis class
module.exports = {
  Redis: MockRedis
};
