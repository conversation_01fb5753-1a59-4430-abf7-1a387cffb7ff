// scripts/test-research-workflow.js

import { execSync } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('Starting Research Workflow Test...');

try {
  // Run the TypeScript file using ts-node
  execSync(
    'npx ts-node-esm --project tsconfig.json src/app/\\(payload\\)/api/agents/collaborative-iteration/tests/test-research-workflow.ts',
    { stdio: 'inherit' }
  );

  console.log('Test completed successfully!');
} catch (error) {
  console.error('Test failed with error:', error.message);
  process.exit(1);
}
