import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { 
  AgentId, 
  IterativeMessageType, 
  IterativeCollaborationState,
  IterativeMessage 
} from '../../../(payload)/api/agents/collaborative-iteration/types';
import { stateStore } from '../../../(payload)/api/agents/collaborative-iteration/utils/stateStore';
import { messageBus } from '../../../(payload)/api/agents/collaborative-iteration/utils/messageBus';

/**
 * API route for sending messages to the collaborative agent system
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { sessionId, message } = body;

    // Validate required fields
    if (!sessionId || !message) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: sessionId or message' },
        { status: 400 }
      );
    }

    // Get current session state
    const state = await stateStore.get(sessionId);
    if (!state) {
      return NextResponse.json({ success: false, error: 'Session not found' }, { status: 404 });
    }

    // Format message for the collaborative agent system
    const formattedMessage: IterativeMessage = {
      id: message.id || uuidv4(),
      timestamp: message.timestamp || new Date().toISOString(),
      from: message.from || 'user',
      to: message.to || 'system',
      type: message.type || IterativeMessageType.QUESTION,
      content: message.content || { text: message.text || message.parts?.[0]?.text || '' },
      conversationId: sessionId,
      metadata: { 
        sessionId,
        ...message.metadata
      }
    };

    // Send message to collaborative agent system via API
    const response = await fetch('http://localhost:3000/api/agents/collaborative-iteration/agents/api', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: 'sendMessage',
        sessionId,
        message: formattedMessage
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to send message');
    }

    // Process the data returned from the API
    const data = await response.json();
    
    // Get the latest state
    const updatedState = await stateStore.get(sessionId);
    
    // Process state for frontend display
    const processedState = processStateForFrontend(updatedState);

    return NextResponse.json({
      success: true,
      sessionId,
      messageId: formattedMessage.id,
      state: processedState,
      finalOutput: extractFinalOutput(updatedState)
    });
  } catch (error: any) {
    console.error('Error sending message to collaborative agent system:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * Helper function to process state for frontend
 * Transforms the state to be more suitable for display
 */
function processStateForFrontend(state: IterativeCollaborationState): any {
  if (!state) return null;

  // Calculate progress based on completed goals
  const totalGoals = state.goals?.length || 0;
  const completedGoals = state.goals?.filter(goal => goal.status === 'completed').length || 0;
  const progress = totalGoals > 0 ? Math.round((completedGoals / totalGoals) * 100) : 0;

  // Get current goal
  const currentGoal = state.goals?.find(goal => goal.status === 'active')?.title || 
                     'Collaboration in progress';

  // Calculate duration
  const startTime = new Date(state.startTime).getTime();
  const endTime = state.endTime ? new Date(state.endTime).getTime() : Date.now();
  const durationMs = endTime - startTime;
  const durationMinutes = Math.floor(durationMs / 60000);
  const durationSeconds = Math.floor((durationMs % 60000) / 1000);
  const duration = `${durationMinutes}m ${durationSeconds}s`;

  return {
    ...state,
    progress,
    currentGoal,
    duration,
    // Convert artifacts object to array for easier frontend processing
    artifacts: Object.values(state.artifacts || {}),
    // Convert consultations object to array
    consultations: Object.values(state.consultations || {})
  };
}

/**
 * Helper function to extract final output from state
 * Looks for the final content artifact and formats it for the frontend
 */
function extractFinalOutput(state: IterativeCollaborationState): any {
  if (!state || !state.artifacts) return null;

  // Look for the final content artifact
  const finalContentArtifact = Object.values(state.artifacts).find(
    artifact => artifact.type === 'final-content' || artifact.type === 'content'
  );

  // If no final content is found, return null
  if (!finalContentArtifact) return null;

  // Get the latest iteration
  const latestIteration = finalContentArtifact.iterations[finalContentArtifact.currentVersion - 1];
  
  return {
    title: finalContentArtifact.name || state.topic,
    content: latestIteration.content.text || latestIteration.content,
    seoScore: finalContentArtifact.qualityScore || 75,
    generatedAt: latestIteration.timestamp,
    contributors: Array.from(new Set(
      finalContentArtifact.iterations.map(iteration => iteration.agent)
    )),
    metadata: {
      wordCount: countWords(latestIteration.content.text || latestIteration.content),
      readingTime: calculateReadingTime(latestIteration.content.text || latestIteration.content),
      keywords: state.keywords || [],
      generatedAt: latestIteration.timestamp,
      targetAudience: state.targetAudience
    }
  };
}

/**
 * Helper function to count words in a string
 */
function countWords(text: string): number {
  return text.split(/\s+/).filter(Boolean).length;
}

/**
 * Helper function to calculate reading time in minutes
 * Assumes average reading speed of 200 words per minute
 */
function calculateReadingTime(text: string): number {
  const words = countWords(text);
  return Math.max(1, Math.ceil(words / 200));
}
