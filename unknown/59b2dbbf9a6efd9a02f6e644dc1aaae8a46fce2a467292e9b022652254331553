'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Chip,
  Autocomplete,
  CircularProgress,
  Alert,
  Divider,
  Card,
  CardContent,
  IconButton,
  <PERSON>ltip,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>L<PERSON>l,
  StepContent
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

// Define content types
type ContentType = 'blog-article' | 'product-page' | 'buying-guide';

// Define tone options
const toneOptions = [
  { value: 'professional', label: 'Professional' },
  { value: 'conversational', label: 'Conversational' },
  { value: 'academic', label: 'Academic' },
  { value: 'enthusiastic', label: 'Enthusiastic' },
  { value: 'technical', label: 'Technical' },
  { value: 'informative', label: 'Informative' }
];

// Define audience options
const audienceOptions = [
  { value: 'general', label: 'General Audience' },
  { value: 'technical', label: 'Technical Professionals' },
  { value: 'executives', label: 'Business Executives' },
  { value: 'marketers', label: 'Marketing Professionals' },
  { value: 'developers', label: 'Software Developers' },
  { value: 'beginners', label: 'Beginners/Novices' }
];

// Form data interface
interface ArticleFormData {
  topic: string;
  contentType: ContentType;
  targetAudience: string;
  tone: string;
  keywords: string[];
  additionalInstructions?: string;
}

interface ArticleInitiationFormProps {
  onSubmit: (formData: ArticleFormData) => Promise<void>;
  loading?: boolean;
}

const ArticleInitiationForm: React.FC<ArticleInitiationFormProps> = ({
  onSubmit,
  loading = false
}) => {
  // Form state
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState<ArticleFormData>({
    topic: '',
    contentType: 'blog-article',
    targetAudience: 'general',
    tone: 'professional',
    keywords: [],
    additionalInstructions: ''
  });
  
  // Form validation state
  const [formErrors, setFormErrors] = useState<Partial<Record<keyof ArticleFormData, string>>>({});
  
  // Keyword input state
  const [keywordInput, setKeywordInput] = useState<string>('');

  // Handle form field changes
  const handleChange = (field: keyof ArticleFormData, value: any) => {
    setFormData({
      ...formData,
      [field]: value
    });
    
    // Clear error for this field if it exists
    if (formErrors[field]) {
      setFormErrors({
        ...formErrors,
        [field]: undefined
      });
    }
  };

  // Add a keyword
  const handleAddKeyword = () => {
    if (keywordInput.trim() && !formData.keywords.includes(keywordInput.trim())) {
      handleChange('keywords', [...formData.keywords, keywordInput.trim()]);
      setKeywordInput('');
    }
  };

  // Remove a keyword
  const handleRemoveKeyword = (keyword: string) => {
    handleChange('keywords', formData.keywords.filter(k => k !== keyword));
  };

  // Validate form data
  const validateForm = (): boolean => {
    const errors: Partial<Record<keyof ArticleFormData, string>> = {};
    
    if (!formData.topic.trim()) {
      errors.topic = 'Topic is required';
    }
    
    if (!formData.targetAudience) {
      errors.targetAudience = 'Target audience is required';
    }
    
    if (formData.keywords.length === 0) {
      errors.keywords = 'At least one keyword is required';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      await onSubmit(formData);
    }
  };

  // Steps for the form
  const steps = [
    {
      label: 'Basic Information',
      description: 'Define the core details of your article',
      content: (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Article Topic"
              value={formData.topic}
              onChange={(e) => handleChange('topic', e.target.value)}
              error={!!formErrors.topic}
              helperText={formErrors.topic || 'Enter the main topic for your article'}
              placeholder="e.g., Benefits of AI in Content Creation"
              required
              disabled={loading}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Content Type</InputLabel>
              <Select
                value={formData.contentType}
                onChange={(e) => handleChange('contentType', e.target.value)}
                label="Content Type"
                disabled={loading}
              >
                <MenuItem value="blog-article">Blog Article</MenuItem>
                <MenuItem value="product-page">Product Page</MenuItem>
                <MenuItem value="buying-guide">Buying Guide</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      )
    },
    {
      label: 'Audience & Tone',
      description: 'Define who you\'re writing for and how it should sound',
      content: (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <FormControl fullWidth error={!!formErrors.targetAudience}>
              <InputLabel>Target Audience</InputLabel>
              <Select
                value={formData.targetAudience}
                onChange={(e) => handleChange('targetAudience', e.target.value)}
                label="Target Audience"
                disabled={loading}
              >
                {audienceOptions.map(option => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
              {formErrors.targetAudience && (
                <Typography variant="caption" color="error">
                  {formErrors.targetAudience}
                </Typography>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Content Tone</InputLabel>
              <Select
                value={formData.tone}
                onChange={(e) => handleChange('tone', e.target.value)}
                label="Content Tone"
                disabled={loading}
              >
                {toneOptions.map(option => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      )
    },
    {
      label: 'Keywords & Instructions',
      description: 'Add keywords and any additional instructions',
      content: (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Box sx={{ mb: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Keywords
                <Tooltip title="Add keywords that should be included in your article">
                  <IconButton size="small">
                    <HelpOutlineIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <TextField
                  fullWidth
                  value={keywordInput}
                  onChange={(e) => setKeywordInput(e.target.value)}
                  placeholder="Enter a keyword"
                  onKeyPress={(e) => e.key === 'Enter' && handleAddKeyword()}
                  disabled={loading}
                  error={!!formErrors.keywords}
                  sx={{ mr: 1 }}
                />
                <Button
                  variant="contained"
                  onClick={handleAddKeyword}
                  disabled={!keywordInput.trim() || loading}
                >
                  Add
                </Button>
              </Box>
              {formErrors.keywords && (
                <Typography variant="caption" color="error">
                  {formErrors.keywords}
                </Typography>
              )}
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                {formData.keywords.map((keyword) => (
                  <Chip
                    key={keyword}
                    label={keyword}
                    onDelete={() => handleRemoveKeyword(keyword)}
                    disabled={loading}
                  />
                ))}
              </Box>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Additional Instructions (Optional)"
              value={formData.additionalInstructions}
              onChange={(e) => handleChange('additionalInstructions', e.target.value)}
              multiline
              rows={4}
              placeholder="Any specific requirements or instructions for the agents"
              disabled={loading}
            />
          </Grid>
        </Grid>
      )
    }
  ];

  // Handle next step
  const handleNext = () => {
    if (activeStep === 0 && !formData.topic.trim()) {
      setFormErrors({
        ...formErrors,
        topic: 'Topic is required'
      });
      return;
    }
    
    if (activeStep === 1 && !formData.targetAudience) {
      setFormErrors({
        ...formErrors,
        targetAudience: 'Target audience is required'
      });
      return;
    }
    
    if (activeStep === steps.length - 1) {
      // Submit the form on the last step
      validateForm() && handleSubmit(new Event('submit') as unknown as React.FormEvent);
      return;
    }
    
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  // Handle back step
  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  return (
    <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
      <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
        Create New Article
      </Typography>
      
      <Stepper activeStep={activeStep} orientation="vertical">
        {steps.map((step, index) => (
          <Step key={step.label}>
            <StepLabel>
              <Typography variant="subtitle1">{step.label}</Typography>
            </StepLabel>
            <StepContent>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {step.description}
              </Typography>
              
              {step.content}
              
              <Box sx={{ mb: 2, mt: 3 }}>
                <div>
                  <Button
                    disabled={activeStep === 0 || loading}
                    onClick={handleBack}
                    sx={{ mr: 1 }}
                  >
                    Back
                  </Button>
                  <Button
                    variant="contained"
                    onClick={handleNext}
                    disabled={loading}
                    endIcon={activeStep === steps.length - 1 ? <PlayArrowIcon /> : undefined}
                  >
                    {activeStep === steps.length - 1 ? 'Start Collaboration' : 'Continue'}
                  </Button>
                  {loading && (
                    <CircularProgress size={24} sx={{ ml: 2 }} />
                  )}
                </div>
              </Box>
            </StepContent>
          </Step>
        ))}
      </Stepper>
      
      {activeStep === steps.length && (
        <Paper square elevation={0} sx={{ p: 3 }}>
          <Typography>All steps completed - starting collaboration...</Typography>
          <CircularProgress size={24} sx={{ mt: 2 }} />
        </Paper>
      )}
    </Paper>
  );
};

export default ArticleInitiationForm;
