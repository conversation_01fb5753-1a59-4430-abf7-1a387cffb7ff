'use client';

import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Chip, 
  Accordion, 
  AccordionSummary, 
  AccordionDetails,
  <PERSON>vider,
  <PERSON>rid,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>b,
  Button
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import JsonView from 'react18-json-view';
import 'react18-json-view/src/style.css';
import HistoryIcon from '@mui/icons-material/History';

interface ArtifactViewerProps {
  artifacts: Record<string, any>;
}

export const ArtifactViewer: React.FC<ArtifactViewerProps> = ({ artifacts }) => {
  const [expandedArtifact, setExpandedArtifact] = useState<string | false>(false);
  const [selectedIterationIndex, setSelectedIterationIndex] = useState<Record<string, number>>({});

  const handleAccordionChange = (artifactId: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedArtifact(isExpanded ? artifactId : false);
  };

  const handleIterationChange = (artifactId: string, index: number) => {
    setSelectedIterationIndex({
      ...selectedIterationIndex,
      [artifactId]: index
    });
  };

  if (!artifacts || Object.keys(artifacts).length === 0) {
    return (
      <Typography variant="body2" color="textSecondary">
        No artifacts generated yet
      </Typography>
    );
  }

  return (
    <Box>
      {Object.entries(artifacts).map(([artifactId, artifact]) => {
        const currentIterationIndex = selectedIterationIndex[artifactId] || artifact.iterations.length - 1;
        const currentIteration = artifact.iterations[currentIterationIndex];
        
        return (
          <Accordion 
            key={`artifact-viewer-${artifactId}-${Math.random().toString(36).substring(2, 9)}`}
            expanded={expandedArtifact === artifactId}
            onChange={handleAccordionChange(artifactId)}
            sx={{ mb: 2 }}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="subtitle1">
                    {artifact.name}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    Created by: {artifact.createdBy} | Type: {artifact.type}
                  </Typography>
                </Box>
                <Chip 
                  label={artifact.status} 
                  size="small"
                  color={artifact.status === 'draft' ? 'default' : 'success'}
                />
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
                <Stack direction="row" spacing={1} alignItems="center">
                  <HistoryIcon fontSize="small" color="action" />
                  <Typography variant="body2">
                    Version History: {artifact.currentVersion} version{artifact.currentVersion > 1 ? 's' : ''}
                  </Typography>
                  
                  {artifact.iterations.length > 1 && (
                    <Box sx={{ ml: 'auto' }}>
                      {artifact.iterations.map((iteration: any, index: number) => (
                        <Button
                          key={index}
                          size="small"
                          variant={currentIterationIndex === index ? "contained" : "outlined"}
                          onClick={() => handleIterationChange(artifactId, index)}
                          sx={{ 
                            minWidth: 'auto', 
                            p: '3px 8px',
                            fontSize: '0.7rem',
                            mr: 0.5
                          }}
                        >
                          v{index + 1}
                        </Button>
                      ))}
                    </Box>
                  )}
                </Stack>
              </Box>
              
              {currentIteration.reasoning && (
                <Paper 
                  variant="outlined" 
                  sx={{ p: 1.5, mb: 2, backgroundColor: 'rgba(0, 0, 0, 0.02)' }}
                >
                  <Typography variant="body2" fontWeight="medium" gutterBottom>
                    Chain of Thought Reasoning:
                  </Typography>
                  <Typography variant="body2">
                    {currentIteration.reasoning.process}
                  </Typography>
                  {currentIteration.reasoning.steps && (
                    <Box component="ul" sx={{ pl: 2, mt: 1, mb: 0 }}>
                      {currentIteration.reasoning.steps.map((step: string, index: number) => (
                        <Typography component="li" variant="body2" key={index}>
                          {step}
                        </Typography>
                      ))}
                    </Box>
                  )}
                </Paper>
              )}
              
              <Paper 
                variant="outlined" 
                sx={{ p: 1.5, borderRadius: 1, overflow: 'auto', maxHeight: '400px' }}
              >
                <Typography variant="body2" fontWeight="medium" gutterBottom>
                  Content:
                </Typography>
                <Box sx={{ mt: 1 }}>
                  <JsonView src={currentIteration.content} />
                </Box>
              </Paper>
              
              {currentIteration.feedback && currentIteration.feedback.length > 0 && (
                <Paper variant="outlined" sx={{ p: 1.5, mt: 2 }}>
                  <Typography variant="body2" fontWeight="medium" gutterBottom>
                    Feedback:
                  </Typography>
                  <Box component="ul" sx={{ pl: 2, mt: 1, mb: 0 }}>
                    {currentIteration.feedback.map((feedback: string, index: number) => (
                      <Typography component="li" variant="body2" key={index}>
                        {feedback}
                      </Typography>
                    ))}
                  </Box>
                </Paper>
              )}
            </AccordionDetails>
          </Accordion>
        );
      })}
    </Box>
  );
};
