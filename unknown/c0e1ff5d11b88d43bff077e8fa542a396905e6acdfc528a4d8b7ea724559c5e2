# Agent-to-Agent (A2A) Protocol Implementation

## 1. Introduction

The Agent-to-Agent (A2A) protocol is a standardized communication framework that enables interoperability between different AI agents. This document provides a comprehensive overview of the A2A protocol implementation in the AuthenCIO collaborative AI agents system.

## 2. Protocol Overview

The A2A protocol defines a standardized way for agents to communicate, share information, and collaborate on tasks. It is based on JSON-RPC 2.0 and includes:

- **Agent Cards**: Descriptive metadata about each agent's capabilities
- **Tasks**: Units of work that can be assigned to agents
- **Messages**: Structured communication between agents
- **Artifacts**: Content or data produced by agents
- **Decisions**: Reasoning and conclusions made by agents

## 3. Core Components

### 3.1 Agent Cards

Agent cards provide metadata about each agent's capabilities, endpoints, and authentication requirements:

```typescript
interface AgentCard {
  name: string;
  description: string;
  url: string;
  provider: {
    organization: string;
    url: string;
  };
  version: string;
  capabilities: {
    streaming: boolean;
    pushNotifications: boolean;
    stateTransitionHistory: boolean;
  };
  authentication: {
    schemes: Array<{
      type: string;
      [key: string]: any;
    }>;
  };
  skills?: AgentSkill[];
}
```

Example agent card implementation:

```typescript
contentStrategy: {
  name: "Content Strategy Agent",
  description: "Develops comprehensive content strategies based on audience, SEO, and business goals",
  url: "/api/agents/content-strategy",
  provider: {
    organization: "AuthenCIO",
    url: "https://authencio.com"
  },
  version: "1.0.0",
  capabilities: {
    streaming: true,
    pushNotifications: false,
    stateTransitionHistory: true
  },
  authentication: {
    schemes: ["none"]
  }
}
```

### 3.2 Tasks

Tasks are the core units of work in the A2A protocol:

```typescript
interface A2ATask {
  id: string;
  sessionId: string;
  status: TaskStatus;
  history?: A2AMessage[];
  artifacts?: EnhancedArtifact[];
  decisions?: Decision[];
  metadata?: Record<string, any>;
  collaborationState?: CollaborationState;
}

interface TaskStatus {
  state: TaskState;
  message?: A2AMessage;
  timestamp?: string;
  progress?: number; // 0-100 percentage
  currentStage?: string;
  expectedCompletion?: string; // ISO timestamp
}

type TaskState = 
  | "submitted"
  | "planning"
  | "discussing"
  | "working"
  | "input-required"
  | "reviewing"
  | "refining"
  | "completed"
  | "canceled"
  | "failed"
  | "unknown";
```

### 3.3 Messages

Messages are the primary means of communication between agents:

```typescript
interface EnhancedA2AMessage extends A2AMessage {
  id: string;
  timestamp: string;
  from: string;
  to: string | string[];
  replyTo?: string;
  conversationId: string;
  reasoning?: Reasoning;
  intentions?: MessageIntention[];
  requestedActions?: RequestedAction[];
  artifactReferences?: string[];
  decisionReferences?: string[];
}

interface A2AMessage {
  role: "user" | "agent" | "system";
  parts: Part[];
  metadata?: Record<string, any>;
}

interface Part {
  type: "text" | "data" | "agent_message" | "artifact" | "decision";
  text?: string;
  data?: any;
  message?: EnhancedA2AMessage;
  artifact?: EnhancedArtifact;
  decision?: Decision;
}
```

### 3.4 Reasoning

The A2A protocol includes structured reasoning to make agent decision-making transparent:

```typescript
interface Reasoning {
  thoughts: string[];
  considerations: string[];
  alternatives?: string[];
  decision: string;
  confidence: number; // 0-1 scale
  sources?: string[];
  constraints?: string[];
}
```

## 4. Server Implementation

The A2A server implementation provides JSON-RPC endpoints for agent communication:

```typescript
export class A2AServer {
  private agentCard: AgentCard;
  private taskHandler: (task: A2ATask) => Promise<A2ATask>;
  private streamingEnabled: boolean;
  private pushNotificationsEnabled: boolean;
  
  async handleJsonRpcRequest(req: NextRequest): Promise<NextResponse> {
    try {
      const jsonRpcRequest: JsonRpcRequest = await req.json();
      
      // Route to appropriate method handler
      switch (jsonRpcRequest.method) {
        case 'tasks/send':
          return await this.handleTaskSend(jsonRpcRequest);
        case 'tasks/sendSubscribe':
          return await this.handleTaskSendSubscribe(jsonRpcRequest);
        case 'tasks/get':
          return await this.handleTaskGet(jsonRpcRequest);
        case 'tasks/cancel':
          return await this.handleTaskCancel(jsonRpcRequest);
      }
    } catch (error) {
      // Error handling
    }
  }
}
```

### 4.1 JSON-RPC Methods

The A2A protocol defines several JSON-RPC methods:

1. **tasks/send**: Send a task to an agent
2. **tasks/sendSubscribe**: Send a task and subscribe to streaming updates
3. **tasks/get**: Get the current state of a task
4. **tasks/cancel**: Cancel a running task

Example JSON-RPC request:

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tasks/send",
  "params": {
    "id": "task-123",
    "message": {
      "role": "user",
      "parts": [
        {
          "type": "text",
          "text": "Generate a content strategy for AI in healthcare"
        }
      ]
    },
    "metadata": {
      "streamingEnabled": true
    }
  }
}
```

## 5. Client Implementation

The A2A client implementation enables agents to communicate with other agents:

```typescript
export class A2AClient {
  private baseUrl: string;
  private apiKey?: string;
  private timeout: number;
  
  async sendMessage(targetAgentUrl: string, message: Omit<A2AMessage, 'id' | 'timestamp'>): Promise<A2AMessage> {
    const fullMessage: A2AMessage = {
      ...message,
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      timestamp: new Date().toISOString(),
    };
    
    const task: Partial<A2ATask> = {
      id: `task_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      history: [
        {
          role: 'user',
          parts: [
            {
              type: 'agent_message',
              message: fullMessage,
            }
          ]
        }
      ],
      metadata: {
        messageId: fullMessage.id,
        messageType: fullMessage.type,
      }
    };
    
    // Send to target agent
    // ...
  }
  
  async requestCollaboration(
    fromAgentId: string, 
    toAgentId: string, 
    targetAgentUrl: string, 
    task: any, 
    replyToId?: string
  ): Promise<any> {
    const message: Omit<A2AMessage, 'id' | 'timestamp'> = {
      from: fromAgentId,
      to: toAgentId,
      type: 'request_collaboration',
      content: { task },
      replyTo: replyToId
    };
    
    const response = await this.publish(message, targetAgentUrl);
    
    if (response.type === 'collaboration_response') {
      return response.content;
    }
    
    throw new Error(`Unexpected response type: ${response.type}`);
  }
}
```

## 6. Utility Functions

The A2A implementation includes utility functions for creating messages and artifacts:

```typescript
// Create a text message
export function createTextMessage(role: "user" | "agent", text: string): A2AMessage {
  return {
    role,
    parts: [
      {
        type: "text",
        text
      }
    ]
  };
}

// Create a data message
export function createDataMessage(role: "user" | "agent", data: Record<string, any>): A2AMessage {
  return {
    role,
    parts: [
      {
        type: "data",
        data
      }
    ]
  };
}

// Create a text artifact
export function createTextArtifact(text: string, name?: string): Artifact {
  return {
    name,
    parts: [
      {
        type: "text",
        text
      }
    ],
    index: 0
  };
}
```

## 7. Integration with Collaborative Agents

The A2A protocol is integrated with the collaborative agents system through:

1. **Message Bus**: Translates between internal message formats and A2A messages
2. **Agent Base Class**: Provides methods for A2A communication
3. **Workflow Orchestrator**: Uses A2A for coordinating agent activities

## 8. Testing Interface

The system includes a testing interface for the A2A protocol:

```typescript
// Create the JSON-RPC request
const jsonRpcRequest = {
  jsonrpc: '2.0',
  id: 1,
  method: 'tasks/send',
  params: {
    id: newTaskId,
    message: {
      role: 'user',
      parts: [
        {
          type: 'text',
          text: formState.userMessage
        }
      ]
    },
    metadata: {
      streamingEnabled: formState.streamingEnabled
    }
  }
};

// Send the request to the agent
const response = await fetch(formState.agentEndpoint, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(jsonRpcRequest)
});
```

## 9. Conclusion

The A2A protocol implementation provides a standardized way for agents to communicate and collaborate, enabling:

- **Interoperability**: Agents can communicate regardless of their internal implementation
- **Structured Reasoning**: Transparent decision-making processes
- **Task Management**: Standardized task lifecycle management
- **Artifact Sharing**: Consistent format for sharing content and data
- **Extensibility**: The protocol can be extended with new message types and capabilities

This implementation forms the foundation of the collaborative AI agents system, enabling sophisticated agent interactions and workflows.
