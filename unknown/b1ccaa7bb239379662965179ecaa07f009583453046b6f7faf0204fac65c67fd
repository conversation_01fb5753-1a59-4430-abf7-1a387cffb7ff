/**
 * A2A JSONRPC API endpoint for goal-based dynamic collaboration
 * This endpoint provides a unified interface for agent-to-agent communication
 */

import { NextRequest, NextResponse } from 'next/server';
import logger from '../../../utils/logger';
import { GoalOrchestrator } from '../workflow/goal-orchestrator';
import { StateManager } from '../state/manager';
import { ArtifactStatus } from '../state/unified-schema';
import { v4 as uuidv4 } from 'uuid';

/**
 * Process a JSON-RPC request
 * @param request The JSON-RPC request
 * @returns The JSON-RPC response
 */
async function processJsonRpcRequest(request: any): Promise<any> {
  // Validate JSON-RPC request
  if (request.jsonrpc !== '2.0') {
    return {
      jsonrpc: '2.0',
      error: { code: -32600, message: 'Invalid Request: Not a valid JSON-RPC 2.0 request' },
      id: request.id || null
    };
  }

  // Extract method and params
  const { method, params, id } = request;

  try {
    // Handle different methods
    switch (method) {
      case 'goal.list':
        return await handleGoalList(params, id);
      case 'artifact.list':
        return await handleArtifactList(params, id);
      case 'artifact.update':
        return await handleArtifactUpdate(params, id);
      case 'artifact.requestEvaluation':
        return await handleRequestArtifactEvaluation(params, id);
      case 'artifact.getEvaluation':
        return await handleGetArtifactEvaluation(params, id);
      case 'workflow.getProgress':
        return await handleWorkflowProgress(params, id);
      case 'feedback.provide':
        return await handleProvideFeedback(params, id);
      default:
        return {
          jsonrpc: '2.0',
          error: { code: -32601, message: `Method not found: ${method}` },
          id
        };
    }
  } catch (error) {
    const err = error as Error;
    logger.error(`Error processing JSON-RPC request`, {
      method,
      error: err.message || String(error),
      stack: err.stack
    });

    return {
      jsonrpc: '2.0',
      error: { code: -32603, message: 'Internal error', data: err.message },
      id
    };
  }
}

/**
 * Process a batch of JSON-RPC requests
 * @param requests The JSON-RPC requests
 * @returns The JSON-RPC responses
 */
async function processJsonRpcBatch(requests: any[]): Promise<any[]> {
  const responses = [];

  for (const request of requests) {
    responses.push(await processJsonRpcRequest(request));
  }

  return responses;
}

/**
 * Handle goal.list method
 * @param params Method parameters
 * @param id Request ID
 * @returns JSON-RPC response
 */
async function handleGoalList(params: any, id: string): Promise<any> {
  const { sessionId } = params;

  if (!sessionId) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: sessionId is required' },
      id
    };
  }

  const stateManager = new StateManager(sessionId);
  const state = await stateManager.getState();

  if (!state) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: session not found' },
      id
    };
  }

  return {
    jsonrpc: '2.0',
    result: {
      goals: state.goals,
      activeGoalIds: state.activeGoalIds,
      completedGoalIds: state.completedGoalIds
    },
    id
  };
}

/**
 * Handle artifact.list method
 * @param params Method parameters
 * @param id Request ID
 * @returns JSON-RPC response
 */
async function handleArtifactList(params: any, id: string): Promise<any> {
  const { sessionId, type } = params;

  if (!sessionId) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: sessionId is required' },
      id
    };
  }

  const stateManager = new StateManager(sessionId);
  const state = await stateManager.getState();

  if (!state) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: session not found' },
      id
    };
  }

  let artifacts = Object.values(state.artifacts);

  // Filter by type if provided
  if (type) {
    artifacts = artifacts.filter((artifact: any) => artifact.type === type);
  }

  return {
    jsonrpc: '2.0',
    result: {
      artifacts
    },
    id
  };
}

/**
 * Handle artifact.update method
 * @param params Method parameters
 * @param id Request ID
 * @returns JSON-RPC response
 */
async function handleArtifactUpdate(params: any, id: string): Promise<any> {
  const { sessionId, artifactId, content, status } = params;

  if (!sessionId) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: sessionId is required' },
      id
    };
  }

  if (!artifactId && !params.type) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: either artifactId or type is required' },
      id
    };
  }

  const stateManager = new StateManager(sessionId);
  const state = await stateManager.getState();

  if (!state) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: session not found' },
      id
    };
  }

  try {
    let updatedArtifact;

    if (artifactId) {
      // Update existing artifact
      await stateManager.updateState(currentState => {
        if (!currentState) return currentState;

        const artifacts = { ...currentState.artifacts };
        const artifact = artifacts[artifactId];

        if (!artifact) {
          throw new Error(`Artifact ${artifactId} not found`);
        }

        artifacts[artifactId] = {
          ...artifact,
          content: content !== undefined ? content : artifact.content,
          status: status !== undefined ? status : artifact.status,
          updatedAt: new Date().toISOString(),
          version: artifact.version + 1
        };

        updatedArtifact = artifacts[artifactId];

        return {
          ...currentState,
          artifacts
        };
      });
    } else {
      // Create new artifact
      const newArtifactId = uuidv4();
      const now = new Date().toISOString();

      await stateManager.updateState(currentState => {
        if (!currentState) return currentState;

        const artifacts = { ...currentState.artifacts };
        const generatedArtifactIds = [...currentState.generatedArtifactIds, newArtifactId];

        artifacts[newArtifactId] = {
          id: newArtifactId,
          type: params.type,
          title: params.title || `${params.type} Artifact`,
          content: content,
          createdBy: params.createdBy || 'user',
          status: status || ArtifactStatus.DRAFT,
          version: 1,
          createdAt: now,
          updatedAt: now
        };

        updatedArtifact = artifacts[newArtifactId];

        return {
          ...currentState,
          artifacts,
          generatedArtifactIds
        };
      });
    }

    return {
      jsonrpc: '2.0',
      result: {
        success: true,
        artifact: updatedArtifact
      },
      id
    };
  } catch (error) {
    const err = error as Error;
    return {
      jsonrpc: '2.0',
      error: { code: -32603, message: 'Internal error', data: err.message },
      id
    };
  }
}

/**
 * Handle workflow.getProgress method
 * @param params Method parameters
 * @param id Request ID
 * @returns JSON-RPC response
 */
async function handleWorkflowProgress(params: any, id: string): Promise<any> {
  const { sessionId } = params;

  if (!sessionId) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: sessionId is required' },
      id
    };
  }

  const stateManager = new StateManager(sessionId);
  const state = await stateManager.getState();

  if (!state) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: session not found' },
      id
    };
  }

  return {
    jsonrpc: '2.0',
    result: {
      workflowProgress: state.workflowProgress
    },
    id
  };
}

/**
 * Handle feedback.provide method
 * @param params Method parameters
 * @param id Request ID
 * @returns JSON-RPC response
 */
async function handleProvideFeedback(params: any, id: string): Promise<any> {
  const { sessionId, artifactId, fromAgent, toAgent, feedback } = params;

  if (!sessionId || !artifactId || !fromAgent || !feedback) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: sessionId, artifactId, fromAgent, and feedback are required' },
      id
    };
  }

  const stateManager = new StateManager(sessionId);
  const state = await stateManager.getState();

  if (!state) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: session not found' },
      id
    };
  }

  const artifact = state.artifacts[artifactId];
  if (!artifact) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: artifact not found' },
      id
    };
  }

  try {
    // Create feedback request
    const feedbackRequestId = uuidv4();
    const now = new Date().toISOString();

    await stateManager.updateState(currentState => {
      if (!currentState) return currentState;

      const feedbackRequests = { ...currentState.feedbackRequests };
      feedbackRequests[feedbackRequestId] = {
        id: feedbackRequestId,
        artifactId,
        fromAgent,
        toAgent: toAgent || artifact.createdBy,
        requestedAt: now,
        status: 'completed',
        feedback: {
          overallRating: feedback.overallRating || 0,
          strengths: feedback.strengths || [],
          areasForImprovement: feedback.areasForImprovement || [],
          specificFeedback: feedback.specificFeedback || [],
          summary: feedback.summary || ''
        }
      };

      return {
        ...currentState,
        feedbackRequests
      };
    });

    return {
      jsonrpc: '2.0',
      result: {
        success: true,
        feedbackRequestId
      },
      id
    };
  } catch (error) {
    const err = error as Error;
    return {
      jsonrpc: '2.0',
      error: { code: -32603, message: 'Internal error', data: err.message },
      id
    };
  }
}

/**
 * Handle artifact.requestEvaluation method
 * @param params Method parameters
 * @param id Request ID
 * @returns JSON-RPC response
 */
async function handleRequestArtifactEvaluation(params: any, id: string): Promise<any> {
  const { sessionId, artifactId, goalId } = params;

  if (!sessionId || !artifactId || !goalId) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: sessionId, artifactId, and goalId are required' },
      id
    };
  }

  const stateManager = new StateManager(sessionId);
  const state = await stateManager.getState();

  if (!state) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: session not found' },
      id
    };
  }

  const artifact = state.artifacts[artifactId];
  if (!artifact) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: artifact not found' },
      id
    };
  }

  const goal = state.goals.byId[goalId];
  if (!goal) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: goal not found' },
      id
    };
  }

  try {
    // Import the artifact evaluation service
    const { ArtifactEvaluationService } = await import('../services/artifact-evaluation-service');

    // Evaluate the artifact
    const evaluation = await ArtifactEvaluationService.evaluateArtifact(artifact, goal);

    // Update the artifact with the evaluation
    await stateManager.updateState(currentState => {
      if (!currentState) return currentState;

      const artifacts = { ...currentState.artifacts };
      const updatedArtifact = { ...artifacts[artifactId] };

      // Add evaluation to metadata
      updatedArtifact.metadata = {
        ...updatedArtifact.metadata,
        evaluation
      };

      artifacts[artifactId] = updatedArtifact;

      return {
        ...currentState,
        artifacts
      };
    });

    return {
      jsonrpc: '2.0',
      result: {
        success: true,
        evaluation
      },
      id
    };
  } catch (error) {
    const err = error as Error;
    return {
      jsonrpc: '2.0',
      error: { code: -32603, message: 'Internal error', data: err.message },
      id
    };
  }
}

/**
 * Handle artifact.getEvaluation method
 * @param params Method parameters
 * @param id Request ID
 * @returns JSON-RPC response
 */
async function handleGetArtifactEvaluation(params: any, id: string): Promise<any> {
  const { sessionId, artifactId } = params;

  if (!sessionId || !artifactId) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: sessionId and artifactId are required' },
      id
    };
  }

  const stateManager = new StateManager(sessionId);
  const state = await stateManager.getState();

  if (!state) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: session not found' },
      id
    };
  }

  const artifact = state.artifacts[artifactId];
  if (!artifact) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Invalid params: artifact not found' },
      id
    };
  }

  // Get evaluation from artifact metadata
  const evaluation = artifact.metadata?.evaluation;

  if (!evaluation) {
    return {
      jsonrpc: '2.0',
      error: { code: -32602, message: 'Evaluation not found for this artifact' },
      id
    };
  }

  return {
    jsonrpc: '2.0',
    result: {
      evaluation
    },
    id
  };
}

/**
 * POST handler for JSON-RPC requests
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const jsonrpcRequest = await req.json();

    // Handle both single requests and batches
    const isBatch = Array.isArray(jsonrpcRequest);

    if (isBatch) {
      const responses = await processJsonRpcBatch(jsonrpcRequest);
      return NextResponse.json(responses);
    } else {
      const response = await processJsonRpcRequest(jsonrpcRequest);
      return NextResponse.json(response);
    }
  } catch (error) {
    const err = error as Error;
    logger.error(`Error processing JSON-RPC request`, {
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json({
      jsonrpc: '2.0',
      error: { code: -32700, message: 'Parse error' },
      id: null
    }, { status: 400 });
  }
}
