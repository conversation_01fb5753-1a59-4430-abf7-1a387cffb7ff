/**
 * Artifact Decision Framework
 *
 * This framework helps agents decide whether to create new artifacts,
 * improve existing ones, or use artifacts created by other agents.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import { StateManager } from '../state/manager';
import { Artifact, Goal, GoalStatus, GoalType, CollaborationState } from '../state/unified-schema';
import { ArtifactEvaluationService, ArtifactEvaluation } from './artifact-evaluation-service';
import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * Artifact decision types
 */
export enum ArtifactDecisionType {
  CREATE_NEW = 'CREATE_NEW',
  IMPROVE_EXISTING = 'IMPROVE_EXISTING',
  USE_EXISTING = 'USE_EXISTING'
}

/**
 * Artifact decision result
 */
export interface ArtifactDecision {
  type: ArtifactDecisionType;
  existingArtifactId?: string;
  reasoning: string;
  requiredArtifactIds: string[];
}

/**
 * Artifact Decision Framework
 */
export class ArtifactDecisionFramework {
  private sessionId: string;
  private stateManager: StateManager;

  /**
   * Constructor
   * @param sessionId The session ID
   */
  constructor(sessionId: string) {
    this.sessionId = sessionId;
    this.stateManager = new StateManager(sessionId);
  }

  /**
   * Decide whether to create a new artifact, improve an existing one, or use an existing one
   * @param agentId The agent ID
   * @param goalId The goal ID
   * @returns The decision
   */
  public async decideArtifactCreation(agentId: string, goalId: string): Promise<ArtifactDecision> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      // Get the goal
      const goal = state.goals.byId[goalId];
      if (!goal) {
        throw new Error(`Goal ${goalId} not found`);
      }

      // Find existing artifacts of the same type
      const existingArtifacts = Object.values(state.artifacts).filter(
        artifact => artifact.type === this.mapGoalTypeToArtifactType(goal.type)
      );

      // If no existing artifacts, create a new one
      if (existingArtifacts.length === 0) {
        return this.decideCreateNew(goal, state);
      }

      // Find the most recent artifact
      const mostRecentArtifact = existingArtifacts.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      // Evaluate the most recent artifact
      const evaluation = await ArtifactEvaluationService.evaluateArtifact(mostRecentArtifact, goal);

      // If the artifact meets requirements, use it
      if (evaluation.meetsRequirements && evaluation.score >= 85) {
        return {
          type: ArtifactDecisionType.USE_EXISTING,
          existingArtifactId: mostRecentArtifact.id,
          reasoning: `Existing artifact meets requirements with a score of ${evaluation.score}. ${evaluation.overallFeedback}`,
          requiredArtifactIds: this.getRequiredArtifactIds(goal, state)
        };
      }

      // If the artifact is close to meeting requirements, improve it
      if (evaluation.score >= 60) {
        return {
          type: ArtifactDecisionType.IMPROVE_EXISTING,
          existingArtifactId: mostRecentArtifact.id,
          reasoning: `Existing artifact partially meets requirements with a score of ${evaluation.score}. Improvement needed: ${evaluation.overallFeedback}`,
          requiredArtifactIds: this.getRequiredArtifactIds(goal, state)
        };
      }

      // Otherwise, create a new artifact
      return this.decideCreateNew(goal, state);
    } catch (error) {
      const err = error as Error;
      logger.error(`Error deciding artifact creation`, {
        sessionId: this.sessionId,
        agentId,
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });

      // Default to creating a new artifact in case of error
      return {
        type: ArtifactDecisionType.CREATE_NEW,
        reasoning: `Defaulting to creating a new artifact due to an error: ${err.message}`,
        requiredArtifactIds: []
      };
    }
  }

  /**
   * Decide to create a new artifact
   * @param goal The goal
   * @param state The current state
   * @returns The decision
   */
  private decideCreateNew(goal: Goal, state: CollaborationState): ArtifactDecision {
    return {
      type: ArtifactDecisionType.CREATE_NEW,
      reasoning: `No suitable existing artifact found. Creating a new ${this.mapGoalTypeToArtifactType(goal.type)} artifact.`,
      requiredArtifactIds: this.getRequiredArtifactIds(goal, state)
    };
  }

  /**
   * Get the IDs of artifacts required to complete the goal
   * @param goal The goal
   * @param state The current state
   * @returns Array of artifact IDs
   */
  private getRequiredArtifactIds(goal: Goal, state: CollaborationState): string[] {
    const requiredArtifactIds: string[] = [];

    // Get completed goals that this goal depends on
    const dependencyGoals = goal.dependencies
      .map(id => state.goals.byId[id])
      .filter(g => g && g.status === GoalStatus.COMPLETED);

    // Get the artifacts from those goals
    for (const dependencyGoal of dependencyGoals) {
      if (dependencyGoal.artifactIds && dependencyGoal.artifactIds.length > 0) {
        requiredArtifactIds.push(dependencyGoal.artifactIds[0]);
      }
    }

    // Add specific artifact dependencies based on goal type
    switch (goal.type) {
      case GoalType.CONTENT_STRATEGY:
        // Content strategy needs market research and keyword analysis
        this.addArtifactsByType(requiredArtifactIds, state, 'market-research');
        this.addArtifactsByType(requiredArtifactIds, state, 'keyword-analysis');
        break;

      case GoalType.CONTENT_CREATION:
        // Content creation needs content strategy
        this.addArtifactsByType(requiredArtifactIds, state, 'content-strategy');
        break;

      case GoalType.SEO_OPTIMIZATION:
        // SEO optimization needs content creation and keyword analysis
        this.addArtifactsByType(requiredArtifactIds, state, 'content-creation');
        this.addArtifactsByType(requiredArtifactIds, state, 'keyword-analysis');
        break;

      case GoalType.QUALITY_ASSESSMENT:
        // Quality assessment needs SEO optimization
        this.addArtifactsByType(requiredArtifactIds, state, 'seo-optimization');
        break;
    }

    return [...new Set(requiredArtifactIds)]; // Remove duplicates
  }

  /**
   * Add artifacts of a specific type to the required artifacts array
   * @param requiredArtifactIds Array of required artifact IDs
   * @param state The current state
   * @param artifactType The artifact type to add
   */
  private addArtifactsByType(requiredArtifactIds: string[], state: CollaborationState, artifactType: string): void {
    const artifacts = Object.values(state.artifacts).filter(
      artifact => artifact.type === artifactType
    );

    if (artifacts.length > 0) {
      // Get the most recent artifact of this type
      const mostRecent = artifacts.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      requiredArtifactIds.push(mostRecent.id);
    }
  }

  /**
   * Map goal type to artifact type
   * @param goalType The goal type
   * @returns The corresponding artifact type
   */
  private mapGoalTypeToArtifactType(goalType: GoalType): string {
    switch (goalType) {
      case GoalType.MARKET_RESEARCH:
        return 'market-research';
      case GoalType.KEYWORD_ANALYSIS:
        return 'keyword-analysis';
      case GoalType.CONTENT_STRATEGY:
        return 'content-strategy';
      case GoalType.CONTENT_CREATION:
        return 'content-creation';
      case GoalType.SEO_OPTIMIZATION:
        return 'seo-optimization';
      case GoalType.QUALITY_ASSESSMENT:
        return 'quality-assessment';
      default:
        return goalType.toLowerCase();
    }
  }
}
