import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import {
  IterativeCollaborationState,
  IterativeMessage,
  IterativeMessageType
} from '../agents/collaborative-iteration/types';
import { stateStore } from '../agents/collaborative-iteration/utils/stateStore';
import {
  initiateCollaborativeWorkflow,
  transitionToContentGeneration,
  transitionToReviewPhase,
  completeWorkflow
} from '../agents/collaborative-iteration/workflows/workflow-orchestrator';

/**
 * GET handler - Get session state
 */
export async function GET(request: NextRequest) {
  // Get session ID from the URL
  const searchParams = request.nextUrl.searchParams;
  const sessionId = searchParams.get('sessionId');

  if (!sessionId) {
    return NextResponse.json(
      { error: 'Session ID is required' },
      { status: 400 }
    );
  }

  // Get the session state
  const state = await stateStore.getState(sessionId);

  if (!state) {
    return NextResponse.json(
      { error: 'Session not found' },
      { status: 404 }
    );
  }

  // Return the session state
  return NextResponse.json({ state });
}

/**
 * POST handler - Create a new session or perform workflow actions
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate body contents
    if (!body) {
      return NextResponse.json({ error: 'Request body is required' }, { status: 400 });
    }

    // Create a new session and start the workflow
    if (body.action === 'createArticle') {
      const sessionId = uuidv4();
      const { topic, contentType, targetAudience, tone, keywords, additionalInstructions, clientName } = body;

      // Create initial state
      const initialState: IterativeCollaborationState = {
        id: sessionId,
        topic,
        contentType: contentType || 'blog-article',
        targetAudience: targetAudience || 'general audience',
        tone: tone || 'informative',
        keywords: keywords || [],
        additionalInstructions: additionalInstructions || '',
        clientName: clientName || '',
        status: 'active',
        startTime: new Date().toISOString(),
        artifacts: {},
        generatedArtifacts: [],
        consultations: {},
        agentStates: {},
        messages: [],
        goals: [],
        currentPhase: 'initialization',
        workflowProgress: {
          marketResearchComplete: false,
          keywordResearchComplete: false,
          contentStrategyComplete: false,
          contentGenerationComplete: false,
          seoOptimizationComplete: false,
          currentPhase: 'initialization'
        }
      };

      // Save the initial state
      await stateStore.setState(sessionId, initialState);

      // Start the collaborative workflow
      console.log(`Starting collaborative workflow for session ${sessionId}`);
      initiateCollaborativeWorkflow(sessionId, topic, {
        contentType: contentType || 'blog-article',
        targetAudience: targetAudience || 'general audience',
        tone: tone || 'informative',
        keywords: keywords || [],
        additionalInstructions: additionalInstructions || '',
        clientName: clientName || ''
      }).catch(error => {
        console.error(`Error initializing collaborative workflow:`, error);
      });

      // Get the updated state
      const state = await stateStore.getState(sessionId);

      return NextResponse.json({
        success: true,
        sessionId,
        state,
        message: `Created new content generation session with collaborative workflow. ID: ${sessionId}`
      });
    }

    // Manually transition to the next phase (for testing or recovery)
    if (body.action === 'transitionPhase' && body.sessionId && body.phase) {
      const { sessionId, phase, topic, contentType, targetAudience, tone } = body;
      let success = false;

      // Get the current state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        return NextResponse.json({ error: 'Session not found' }, { status: 404 });
      }

      // Transition to the specified phase
      switch (phase) {
        case 'research':
          // Manually set the phase to research
          await stateStore.updateState(sessionId, (currentState) => {
            currentState.currentPhase = 'research';
            if (currentState.workflowProgress) {
              currentState.workflowProgress.currentPhase = 'research';
            }
            return currentState;
          });
          success = true;
          break;
        case 'content-generation':
          success = await transitionToContentGeneration(
            sessionId,
            topic || state.topic,
            contentType || state.contentType,
            targetAudience || state.targetAudience,
            tone || state.tone,
            {}
          );
          break;
        case 'review':
          success = await transitionToReviewPhase(
            sessionId,
            topic || state.topic,
            contentType || state.contentType,
            targetAudience || state.targetAudience,
            tone || state.tone,
            {}
          );
          break;
        case 'complete':
          success = await completeWorkflow(
            sessionId,
            topic || state.topic
          );
          break;
        case 'pause':
          // Pause the workflow
          state.status = 'paused';
          await stateStore.setState(sessionId, state);
          success = true;
          break;
        case 'resume':
          // Resume the workflow
          state.status = 'active';
          await stateStore.setState(sessionId, state);
          success = true;
          break;
        default:
          return NextResponse.json({ error: 'Invalid phase' }, { status: 400 });
      }

      // Get the updated state
      const updatedState = await stateStore.getState(sessionId);

      return NextResponse.json({
        success,
        state: updatedState,
        message: success ? `Transitioned to ${phase} phase` : `Failed to transition to ${phase} phase`
      });
    }

    // Handle sending a message to the agents
    if (body.action === 'sendMessage' && body.sessionId && body.message) {
      const { sessionId, message, from } = body;

      // Get the current state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        return NextResponse.json({ error: 'Session not found' }, { status: 404 });
      }

      // Add the message to the state
      const newMessage: IterativeMessage = {
        id: uuidv4(),
        content: message,
        from: from || 'user',
        to: 'all',
        timestamp: new Date().toISOString(),
        type: IterativeMessageType.USER_MESSAGE
      };

      state.messages = [...(state.messages || []), newMessage];
      await stateStore.setState(sessionId, state);

      // Get the updated state
      const updatedState = await stateStore.getState(sessionId);

      return NextResponse.json({
        success: true,
        state: updatedState,
        message: 'Message sent successfully'
      });
    }

    // Handle sending feedback to the agents
    if (body.action === 'sendFeedback' && body.sessionId && body.feedback) {
      const { sessionId, feedback, from, artifactId } = body;

      // Get the current state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        return NextResponse.json({ error: 'Session not found' }, { status: 404 });
      }

      // Add the feedback to the state
      const newMessage: IterativeMessage = {
        id: uuidv4(),
        content: artifactId ? { feedback, artifactId } : feedback,
        from: from || 'user',
        to: 'all',
        timestamp: new Date().toISOString(),
        type: IterativeMessageType.USER_FEEDBACK,
        artifactId
      };

      state.messages = [...(state.messages || []), newMessage];

      // Add an event to the state if it supports events
      if (state.events) {
        state.events.push({
          type: 'SYSTEM',
          timestamp: new Date().toISOString(),
          data: {
            eventType: 'USER_FEEDBACK',
            feedback,
            artifactId: artifactId || undefined
          }
        });
      }

      await stateStore.setState(sessionId, state);

      // Get the updated state
      const updatedState = await stateStore.getState(sessionId);

      return NextResponse.json({
        success: true,
        state: updatedState,
        message: 'Feedback sent successfully'
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Error in POST handler:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
