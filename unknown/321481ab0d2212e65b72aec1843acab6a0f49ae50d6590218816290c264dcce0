/**
 * Agent Communication Hub
 * 
 * This class facilitates bidirectional communication between agents,
 * implementing the A2A protocol for standardized message passing.
 */

import { v4 as uuidv4 } from 'uuid';
import { stateStore } from '../collaborative-iteration/utils/stateStore';
import logger from '../collaborative-iteration/utils/logger';
import { 
  DynamicAgentMessage, 
  DynamicMessageType 
} from './types';
import { IterativeMessage, IterativeMessageType } from '../collaborative-iteration/types';

export class AgentCommunicationHub {
  private sessionId: string;
  private messageRateLimits: Map<string, number> = new Map();
  private messageChains: Map<string, string[]> = new Map();
  private MAX_CHAIN_LENGTH = 10; // Maximum length of a message chain to prevent loops
  private MAX_MESSAGES_PER_MINUTE = 20; // Rate limit per agent
  
  constructor(sessionId: string) {
    this.sessionId = sessionId;
  }
  
  /**
   * Send a message to one or more agents
   * @param message The message to send
   * @returns The message ID
   */
  public async sendMessage(message: DynamicAgentMessage): Promise<string> {
    try {
      // Validate message
      this.validateMessage(message);
      
      // Check for rate limits
      if (!this.checkRateLimit(message.from)) {
        logger.warn(`Rate limit exceeded for agent ${message.from}`, {
          sessionId: this.sessionId,
          agent: message.from,
          messageId: message.id
        });
        
        // Store rate limit warning message
        const warningMessage: DynamicAgentMessage = {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: 'system',
          to: message.from,
          type: DynamicMessageType.SYSTEM_MESSAGE,
          content: {
            warning: 'Rate limit exceeded. Please slow down your message rate.'
          },
          conversationId: message.conversationId,
          reasoning: {
            thoughts: [
              'Agent is sending messages too quickly',
              'Rate limiting prevents message flooding'
            ],
            considerations: [
              'Agent may be in a loop',
              'System stability requires rate limiting'
            ],
            decision: 'Enforce rate limit and notify agent',
            confidence: 0.9
          }
        };
        
        // Store the warning message
        await this.storeMessage(warningMessage);
        
        return message.id;
      }
      
      // Check for message chains/loops
      if (message.replyTo && !this.checkMessageChain(message)) {
        logger.warn(`Message chain limit exceeded for conversation ${message.conversationId}`, {
          sessionId: this.sessionId,
          conversationId: message.conversationId,
          messageId: message.id
        });
        
        // Store chain limit warning message
        const warningMessage: DynamicAgentMessage = {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: 'system',
          to: message.from,
          type: DynamicMessageType.SYSTEM_MESSAGE,
          content: {
            warning: 'Message chain limit exceeded. This conversation may be in a loop.'
          },
          conversationId: message.conversationId,
          reasoning: {
            thoughts: [
              'Message chain is too long',
              'Agents may be in a conversation loop'
            ],
            considerations: [
              'Preventing infinite loops is essential',
              'Agents should reach conclusions efficiently'
            ],
            decision: 'Break potential loop and notify agent',
            confidence: 0.95
          }
        };
        
        // Store the warning message
        await this.storeMessage(warningMessage);
        
        return message.id;
      }
      
      // Store the message
      await this.storeMessage(message);
      
      // Convert to standard IterativeMessage format for compatibility
      const iterativeMessage = this.convertToIterativeMessage(message);
      
      // Store in the standard messages array
      await stateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;
        
        return {
          ...currentState,
          messages: [...(currentState.messages || []), iterativeMessage]
        };
      });
      
      // Update message chain tracking
      if (message.replyTo) {
        this.updateMessageChain(message);
      } else {
        // Start a new chain
        this.messageChains.set(message.id, [message.id]);
      }
      
      // Update rate limit tracking
      this.updateRateLimit(message.from);
      
      logger.info(`Message sent from ${message.from} to ${Array.isArray(message.to) ? message.to.join(', ') : message.to}`, {
        sessionId: this.sessionId,
        messageId: message.id,
        messageType: message.type,
        from: message.from,
        to: message.to
      });
      
      return message.id;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error sending message`, {
        sessionId: this.sessionId,
        messageId: message.id,
        error: err.message || String(error),
        stack: err.stack
      });
      throw error;
    }
  }
  
  /**
   * Request information from another agent
   * @param fromAgent The requesting agent
   * @param toAgent The agent to request from
   * @param query The information query
   * @param context Additional context for the request
   * @returns The message ID
   */
  public async requestInformation(
    fromAgent: string,
    toAgent: string,
    query: string,
    context?: any
  ): Promise<string> {
    const message: DynamicAgentMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: fromAgent,
      to: toAgent,
      type: DynamicMessageType.REQUEST_INFORMATION,
      content: {
        query,
        context
      },
      conversationId: uuidv4(),
      reasoning: {
        thoughts: [
          `Need information from ${toAgent} to proceed`,
          `${toAgent} has expertise in this area`
        ],
        considerations: [
          'Information is required for current task',
          'Clear query will help get precise information'
        ],
        decision: `Request information from ${toAgent}`,
        confidence: 0.9
      }
    };
    
    return this.sendMessage(message);
  }
  
  /**
   * Provide information to another agent
   * @param fromAgent The providing agent
   * @param toAgent The agent to provide to
   * @param information The information to provide
   * @param replyToId The message ID being replied to
   * @returns The message ID
   */
  public async provideInformation(
    fromAgent: string,
    toAgent: string,
    information: any,
    replyToId: string
  ): Promise<string> {
    const message: DynamicAgentMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: fromAgent,
      to: toAgent,
      type: DynamicMessageType.PROVIDE_INFORMATION,
      content: information,
      replyTo: replyToId,
      conversationId: await this.getConversationId(replyToId),
      reasoning: {
        thoughts: [
          `Responding to information request from ${toAgent}`,
          'Providing comprehensive information'
        ],
        considerations: [
          'Accuracy and relevance of information',
          'Format for easy understanding'
        ],
        decision: `Provide requested information to ${toAgent}`,
        confidence: 0.95
      }
    };
    
    return this.sendMessage(message);
  }
  
  /**
   * Store a message in the state
   * @param message The message to store
   */
  private async storeMessage(message: DynamicAgentMessage): Promise<void> {
    await stateStore.updateState(this.sessionId, (currentState) => {
      if (!currentState) return currentState;
      
      // Add to dynamic messages
      const dynamicMessages = { ...currentState.dynamicMessages };
      dynamicMessages[message.id] = message;
      
      // Add to conversations
      const conversations = { ...currentState.conversations };
      if (!conversations[message.conversationId]) {
        conversations[message.conversationId] = [];
      }
      conversations[message.conversationId].push(message.id);
      
      return {
        ...currentState,
        dynamicMessages,
        conversations
      };
    });
  }
  
  /**
   * Convert a dynamic message to the standard IterativeMessage format
   * @param message The dynamic message to convert
   * @returns The converted IterativeMessage
   */
  private convertToIterativeMessage(message: DynamicAgentMessage): IterativeMessage {
    // Determine the appropriate message type
    let messageType: IterativeMessageType;
    switch (message.type) {
      case DynamicMessageType.REQUEST_INFORMATION:
      case DynamicMessageType.REQUEST_COLLABORATION:
      case DynamicMessageType.REQUEST_FEEDBACK:
        messageType = IterativeMessageType.AGENT_REQUEST;
        break;
      case DynamicMessageType.PROVIDE_INFORMATION:
      case DynamicMessageType.PROVIDE_COLLABORATION:
      case DynamicMessageType.PROVIDE_FEEDBACK:
        messageType = IterativeMessageType.AGENT_RESPONSE;
        break;
      case DynamicMessageType.SYSTEM_MESSAGE:
        messageType = IterativeMessageType.SYSTEM_MESSAGE;
        break;
      case DynamicMessageType.GOAL_UPDATE:
        messageType = IterativeMessageType.SYSTEM_MESSAGE;
        break;
      case DynamicMessageType.ARTIFACT_CREATED:
        messageType = IterativeMessageType.ARTIFACT_CREATED;
        break;
      case DynamicMessageType.ARTIFACT_UPDATED:
        messageType = IterativeMessageType.ARTIFACT_UPDATED;
        break;
      case DynamicMessageType.WORKFLOW_TRANSITION:
        messageType = IterativeMessageType.SYSTEM_MESSAGE;
        break;
      default:
        messageType = IterativeMessageType.AGENT_MESSAGE;
    }
    
    // Convert to standard format
    return {
      id: message.id,
      timestamp: message.timestamp,
      from: message.from,
      to: Array.isArray(message.to) ? message.to[0] : message.to,
      type: messageType,
      content: message.content,
      replyTo: message.replyTo,
      reasoning: message.reasoning,
      metadata: {
        ...message.metadata,
        conversationId: message.conversationId,
        dynamicMessageType: message.type
      }
    };
  }
  
  /**
   * Validate a message
   * @param message The message to validate
   * @throws Error if the message is invalid
   */
  private validateMessage(message: DynamicAgentMessage): void {
    if (!message.id) {
      message.id = uuidv4();
    }
    
    if (!message.timestamp) {
      message.timestamp = new Date().toISOString();
    }
    
    if (!message.from) {
      throw new Error('Message must have a sender (from)');
    }
    
    if (!message.to) {
      throw new Error('Message must have a recipient (to)');
    }
    
    if (!message.type) {
      throw new Error('Message must have a type');
    }
    
    if (message.content === undefined || message.content === null) {
      throw new Error('Message must have content');
    }
    
    if (!message.conversationId) {
      message.conversationId = uuidv4();
    }
  }
  
  /**
   * Check if an agent has exceeded its rate limit
   * @param agentId The agent ID to check
   * @returns True if the agent is within rate limits
   */
  private checkRateLimit(agentId: string): boolean {
    const currentCount = this.messageRateLimits.get(agentId) || 0;
    return currentCount < this.MAX_MESSAGES_PER_MINUTE;
  }
  
  /**
   * Update the rate limit counter for an agent
   * @param agentId The agent ID to update
   */
  private updateRateLimit(agentId: string): void {
    const currentCount = this.messageRateLimits.get(agentId) || 0;
    this.messageRateLimits.set(agentId, currentCount + 1);
    
    // Reset the counter after 1 minute
    setTimeout(() => {
      const newCount = (this.messageRateLimits.get(agentId) || 0) - 1;
      if (newCount <= 0) {
        this.messageRateLimits.delete(agentId);
      } else {
        this.messageRateLimits.set(agentId, newCount);
      }
    }, 60000);
  }
  
  /**
   * Check if a message chain has exceeded the maximum length
   * @param message The message to check
   * @returns True if the chain is within limits
   */
  private checkMessageChain(message: DynamicAgentMessage): boolean {
    if (!message.replyTo) {
      return true;
    }
    
    const chain = this.messageChains.get(message.replyTo) || [];
    return chain.length < this.MAX_CHAIN_LENGTH;
  }
  
  /**
   * Update the message chain tracking
   * @param message The message to update the chain for
   */
  private updateMessageChain(message: DynamicAgentMessage): void {
    if (!message.replyTo) {
      return;
    }
    
    const chain = this.messageChains.get(message.replyTo) || [];
    const newChain = [...chain, message.id];
    this.messageChains.set(message.id, newChain);
  }
  
  /**
   * Get the conversation ID for a message
   * @param messageId The message ID to look up
   * @returns The conversation ID or a new UUID if not found
   */
  private async getConversationId(messageId: string): Promise<string> {
    const state = await stateStore.getState(this.sessionId);
    if (!state) {
      return uuidv4();
    }
    
    const dynamicMessages = state.dynamicMessages || {};
    const message = dynamicMessages[messageId];
    
    return message?.conversationId || uuidv4();
  }
}
