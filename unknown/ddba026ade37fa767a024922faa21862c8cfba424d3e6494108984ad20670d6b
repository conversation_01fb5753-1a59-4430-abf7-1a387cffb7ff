// src/app/(payload)/admin/iterative-collaboration/page.tsx

import React from 'react';
import { Metadata } from 'next';
import { Box } from '@mui/material';
import IterativeCollaborationDashboard from '../../../../components/IterativeCollaboration/IterativeCollaborationDashboard';

export const metadata: Metadata = {
  title: 'Iterative Agent Collaboration | AuthenCIO',
  description: 'Collaborative content generation using iterative agent interactions',
};

export default function IterativeCollaborationPage() {
  return (
    <Box sx={{ maxWidth: '1200px', mx: 'auto' }}>
      <IterativeCollaborationDashboard />
    </Box>
  );
}
