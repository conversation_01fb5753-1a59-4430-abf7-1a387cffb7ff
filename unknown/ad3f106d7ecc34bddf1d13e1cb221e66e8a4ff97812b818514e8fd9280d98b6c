/**
 * Dynamic Collaboration State Models
 * 
 * This file defines the dedicated state models for the dynamic collaboration system.
 * These models are designed to be used directly with the specialized state store
 * without requiring adaptation to fit other state models.
 */

import { v4 as uuidv4 } from 'uuid';

/**
 * Dynamic workflow phases
 */
export enum DynamicWorkflowPhase {
  PLANNING = 'planning',
  RESEARCH = 'research',
  CREATION = 'creation',
  REVIEW = 'review',
  FINALIZATION = 'finalization'
}

/**
 * Goal types for content generation
 */
export enum GoalType {
  MARKET_RESEARCH = 'market_research',
  KEYWORD_ANALYSIS = 'keyword_analysis',
  CONTENT_STRATEGY = 'content_strategy',
  CONTENT_CREATION = 'content_creation',
  SEO_OPTIMIZATION = 'seo_optimization',
  QUALITY_ASSESSMENT = 'quality_assessment'
}

/**
 * Goal status
 */
export enum GoalStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  BLOCKED = 'blocked',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

/**
 * Dynamic message types for agent communication
 */
export enum DynamicMessageType {
  REQUEST_INFORMATION = 'request_information',
  PROVIDE_INFORMATION = 'provide_information',
  REQUEST_COLLABORATION = 'request_collaboration',
  PROVIDE_COLLABORATION = 'provide_collaboration',
  REQUEST_FEEDBACK = 'request_feedback',
  PROVIDE_FEEDBACK = 'provide_feedback',
  SYSTEM_MESSAGE = 'system_message',
  SYSTEM_NOTIFICATION = 'system_notification',
  GOAL_UPDATE = 'goal_update',
  ARTIFACT_UPDATE = 'artifact_update',
  ARTIFACT_CREATED = 'artifact_created',
  ARTIFACT_UPDATED = 'artifact_updated',
  WORKFLOW_TRANSITION = 'workflow_transition',
  USER_MESSAGE = 'user_message',
  AGENT_MESSAGE = 'agent_message',
  FEEDBACK_REQUEST = 'feedback_request',
  FEEDBACK_RESPONSE = 'feedback_response'
}

/**
 * Artifact status
 */
export enum ArtifactStatus {
  DRAFT = 'draft',
  REVIEW = 'review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PUBLISHED = 'published'
}

/**
 * Reasoning structure for agent decisions
 */
export interface Reasoning {
  thoughts: string[];
  considerations?: string[];
  decision: string;
  confidence: number;
}

/**
 * Content goal
 */
export interface ContentGoal {
  id: string;
  type: GoalType;
  description: string;
  criteria: string[];
  status: GoalStatus;
  progress: number;
  dependencies: string[];
  assignedTo?: string | string[];
  createdAt: string;
  startTime?: string;
  completedAt?: string;
  artifactId?: string;
  artifacts?: string[];
  reasoning?: Reasoning;
}

/**
 * Dynamic agent message
 */
export interface DynamicAgentMessage {
  id: string;
  timestamp: string;
  from: string;
  to: string | string[];
  type: DynamicMessageType;
  content: any;
  replyTo?: string;
  conversationId: string;
  reasoning?: Reasoning;
  metadata?: Record<string, any>;
}

/**
 * Artifact interface
 */
export interface DynamicArtifact {
  id: string;
  type: string;
  title: string;
  content: any;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  status: ArtifactStatus;
  version: number;
  metadata?: Record<string, any>;
  reasoning?: Reasoning;
}

/**
 * Feedback cycle
 */
export interface FeedbackCycle {
  id: string;
  artifactId: string;
  requestedBy: string;
  requestedAt: string;
  status: 'pending' | 'completed';
  feedback?: {
    providedBy: string;
    providedAt: string;
    content: any;
    rating?: number;
    reasoning?: Reasoning;
  };
}

/**
 * Content generation parameters
 */
export interface ContentGenerationParams {
  topic: string;
  contentType: 'blog-article' | 'product-page' | 'buying-guide';
  targetAudience: string;
  tone: string;
  keywords: string[];
  additionalInstructions?: string;
  referenceUrls?: string[];
  comparisonSessionId?: string;
}

/**
 * Dynamic collaboration state
 */
export interface DynamicCollaborationState {
  id: string;
  topic: string;
  contentType: 'blog-article' | 'product-page' | 'buying-guide';
  targetAudience: string;
  tone: string;
  keywords: string[];
  status: 'active' | 'paused' | 'completed' | 'failed';
  startTime: string;
  endTime?: string;
  currentPhase: DynamicWorkflowPhase;
  goals: Record<string, ContentGoal>;
  activeGoals: string[];
  completedGoals: string[];
  artifacts: Record<string, DynamicArtifact>;
  generatedArtifacts: string[];
  messages: DynamicAgentMessage[];
  dynamicMessages: Record<string, DynamicAgentMessage>;
  conversations: Record<string, string[]>; // conversationId -> messageIds
  feedbackCycles: Record<string, FeedbackCycle>;
  metadata?: Record<string, any>;
  comparisonSessionId?: string;
}

/**
 * Create a new dynamic collaboration state
 */
export function createDynamicCollaborationState(
  sessionId: string,
  params: ContentGenerationParams
): DynamicCollaborationState {
  return {
    id: sessionId,
    topic: params.topic,
    contentType: params.contentType,
    targetAudience: params.targetAudience,
    tone: params.tone,
    keywords: params.keywords || [],
    status: 'active',
    startTime: new Date().toISOString(),
    currentPhase: DynamicWorkflowPhase.PLANNING,
    goals: {},
    activeGoals: [],
    completedGoals: [],
    artifacts: {},
    generatedArtifacts: [],
    messages: [],
    dynamicMessages: {},
    conversations: {},
    feedbackCycles: {},
    metadata: {
      additionalInstructions: params.additionalInstructions,
      referenceUrls: params.referenceUrls
    },
    comparisonSessionId: params.comparisonSessionId
  };
}
