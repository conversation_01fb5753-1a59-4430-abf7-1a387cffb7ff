/**
 * Dynamic Content Structure
 * 
 * This file contains types and functions for dynamic content structure generation and negotiation
 * between agents. It enables flexible content structures based on content type, topic, and agent inputs.
 */

import { A2AMessage, A2ATask, Artifact } from './a2atypes';
import { ContentGenerationState } from './contentGeneration';
import { ChatOpenAI } from "@langchain/openai";
import { SystemMessage } from "@langchain/core/messages";

// Base content section interface
export interface ContentSection {
  id: string;
  type: ContentSectionType;
  title: string;
  purpose: string;
  keyPoints?: string[];
  keywordTargeting?: string[];
  estimatedLength?: string;
  children?: ContentSection[];
}

// Content section types
export type ContentSectionType = 
  | 'heading'
  | 'text'
  | 'list'
  | 'callout'
  | 'faq'
  | 'image'
  | 'table'
  | 'comparison'
  | 'testimonial'
  | 'cta'
  | 'overview'
  | 'details'
  | 'benefits'
  | 'features'
  | 'specifications'
  | 'related-products'
  | 'custom';

// Content structure interface
export interface ContentStructure {
  contentType: 'product-page' | 'blog-article' | 'buying-guide';
  contentObjective: string;
  topicFocus: string;
  targetAudience: {
    primary: string;
    secondary?: string;
    needs: string[];
  };
  sections: ContentSection[];
  keywordStrategy: {
    primary: string;
    secondary: string;
    distribution: string;
  };
  toneAndStyle: string;
  contentDifferentiators: string[];
  reasoning: string;
}

// Content structure proposal from an agent
export interface ContentStructureProposal {
  agentId: string;
  agentName: string;
  structure: ContentStructure;
  reasoning: string;
  strengths: string[];
  considerations: string[];
}

// Content structure negotiation state
export interface ContentStructureNegotiation {
  proposals: ContentStructureProposal[];
  feedback: ContentStructureFeedback[];
  finalStructure?: ContentStructure;
  negotiationStatus: 'in-progress' | 'completed';
}

// Feedback on a content structure proposal
export interface ContentStructureFeedback {
  fromAgentId: string;
  toAgentId: string;
  proposalId: string;
  feedback: string;
  suggestions: {
    section: string;
    suggestion: string;
    reasoning: string;
  }[];
  agreement: {
    section: string;
    reasoning: string;
  }[];
}

/**
 * Generate a content structure based on content type, topic, and audience
 */
export async function generateContentStructure(
  contentType: 'product-page' | 'blog-article' | 'buying-guide',
  topicFocus: string,
  targetAudience: string,
  primaryKeywords: string[],
  tonePreference: string,
  marketResearchInsights?: any,
  seoKeywordInsights?: any
): Promise<ContentStructure> {
  const model = new ChatOpenAI({
    temperature: 0.7,
    modelName: "gpt-3.5-turbo-1106",
  });

  const prompt = `
    You are a Content Structure Generator that creates optimal content structures based on content type, topic, and audience.
    
    Content Type: ${contentType}
    Topic Focus: ${topicFocus}
    Target Audience: ${targetAudience}
    Primary Keywords: ${primaryKeywords.join(', ')}
    Tone Preference: ${tonePreference}
    
    ${marketResearchInsights ? `Market Research Insights: ${JSON.stringify(marketResearchInsights)}` : ''}
    ${seoKeywordInsights ? `SEO Keyword Insights: ${JSON.stringify(seoKeywordInsights)}` : ''}
    
    Create a dynamic content structure that is optimized for this specific content type and topic.
    Do not use a fixed template, but instead design a structure that will be most effective for this particular content.
    
    IMPORTANT: Show your reasoning process for why this structure is optimal for this content type and topic.
    
    Your response MUST be valid JSON with the following structure:
    {
      "contentType": "${contentType}",
      "contentObjective": "Clear statement of the content's primary objective",
      "topicFocus": "${topicFocus}",
      "targetAudience": {
        "primary": "${targetAudience}",
        "secondary": "Description of secondary audience if applicable",
        "needs": ["Specific need 1", "Specific need 2"]
      },
      "sections": [
        {
          "id": "section-1",
          "type": "heading|text|list|callout|faq|image|table|comparison|testimonial|cta|overview|details|benefits|features|specifications|related-products|custom",
          "title": "Section title",
          "purpose": "Purpose of this section",
          "keyPoints": ["Key point 1", "Key point 2"],
          "keywordTargeting": ["Primary keyword", "Secondary keyword"],
          "estimatedLength": "Word count estimate",
          "children": [
            {
              "id": "subsection-1",
              "type": "heading|text|list|callout|faq|image|table|comparison|testimonial|cta",
              "title": "Subsection title",
              "purpose": "Purpose of this subsection",
              "keyPoints": ["Key point 1", "Key point 2"],
              "keywordTargeting": ["Primary keyword", "Secondary keyword"],
              "estimatedLength": "Word count estimate"
            }
          ]
        }
      ],
      "keywordStrategy": {
        "primary": "Primary keyword strategy",
        "secondary": "Secondary keyword strategy",
        "distribution": "How keywords should be distributed"
      },
      "toneAndStyle": "Detailed description of tone and style approach",
      "contentDifferentiators": ["What will make this content stand out 1", "What will make this content stand out 2"],
      "reasoning": "Detailed explanation of why this structure is optimal for this content type and topic"
    }
  `;

  try {
    const response = await model.invoke([
      new SystemMessage(
        "You are a Content Structure Generator that creates optimal content structures based on content type, topic, and audience. Always respond with valid JSON."
      ),
      new SystemMessage(prompt)
    ]);

    // Parse the response - handle potential markdown formatting
    let jsonContent = response.content as string;
    
    // Clean the response if it contains markdown code blocks
    if (jsonContent.includes("```")) {
      // Extract content between json code blocks
      const jsonMatch = jsonContent.match(/```(?:json)?\s*([\s\S]*?)```/);
      if (jsonMatch && jsonMatch[1]) {
        jsonContent = jsonMatch[1].trim();
      } else {
        // Try to remove any backticks at the beginning and end
        jsonContent = jsonContent.replace(/^```[\s\S]*?\n/, '').replace(/\n```$/, '');
      }
    }
    
    const structureResponse = JSON.parse(jsonContent);
    
    return structureResponse as ContentStructure;
  } catch (error) {
    console.error("Error generating content structure:", error);
    
    // Return a fallback structure
    return {
      contentType,
      contentObjective: `Create a comprehensive ${contentType} about ${topicFocus}`,
      topicFocus,
      targetAudience: {
        primary: targetAudience,
        needs: ["Information", "Solutions", "Guidance"]
      },
      sections: [
        {
          id: "section-1",
          type: "heading",
          title: "Introduction",
          purpose: "Introduce the topic and engage the reader",
          keyPoints: ["Establish relevance", "Preview key points"],
          keywordTargeting: [primaryKeywords[0] || ""],
          estimatedLength: "200-300 words"
        },
        {
          id: "section-2",
          type: "heading",
          title: "Main Content",
          purpose: "Provide detailed information on the topic",
          keyPoints: ["Key information", "Supporting evidence"],
          keywordTargeting: [primaryKeywords[0] || "", "Secondary keywords"],
          estimatedLength: "800-1200 words",
          children: [
            {
              id: "subsection-1",
              type: "text",
              title: "Key Point 1",
              purpose: "Elaborate on first key point",
              keyPoints: ["Detail 1", "Detail 2"],
              keywordTargeting: ["Related keywords"],
              estimatedLength: "250-300 words"
            },
            {
              id: "subsection-2",
              type: "text",
              title: "Key Point 2",
              purpose: "Elaborate on second key point",
              keyPoints: ["Detail 1", "Detail 2"],
              keywordTargeting: ["Related keywords"],
              estimatedLength: "250-300 words"
            }
          ]
        },
        {
          id: "section-3",
          type: "heading",
          title: "Conclusion",
          purpose: "Summarize key points and provide next steps",
          keyPoints: ["Summary", "Call to action"],
          keywordTargeting: [primaryKeywords[0] || ""],
          estimatedLength: "150-200 words"
        }
      ],
      keywordStrategy: {
        primary: `Focus on ${primaryKeywords[0] || "primary keyword"} in title, headers, and key positions`,
        secondary: "Distribute secondary keywords naturally throughout content",
        distribution: "Natural integration prioritizing readability"
      },
      toneAndStyle: tonePreference,
      contentDifferentiators: ["Comprehensive coverage", "Actionable insights", "Clear structure"],
      reasoning: "This structure provides a balanced approach for most content types with introduction, detailed body, and conclusion."
    };
  }
}

/**
 * Generate a content structure proposal from an agent
 */
export async function generateContentStructureProposal(
  agentId: string,
  agentName: string,
  contentType: 'product-page' | 'blog-article' | 'buying-guide',
  topicFocus: string,
  targetAudience: string,
  primaryKeywords: string[],
  tonePreference: string,
  agentSpecialty: string,
  marketResearchInsights?: any,
  seoKeywordInsights?: any
): Promise<ContentStructureProposal> {
  // Generate a content structure
  const structure = await generateContentStructure(
    contentType,
    topicFocus,
    targetAudience,
    primaryKeywords,
    tonePreference,
    marketResearchInsights,
    seoKeywordInsights
  );
  
  // Create a proposal
  return {
    agentId,
    agentName,
    structure,
    reasoning: `As a ${agentName}, I've designed this structure to optimize for ${agentSpecialty} while ensuring it meets the needs of ${targetAudience} for a ${contentType} about ${topicFocus}.`,
    strengths: [
      `Strong focus on ${agentSpecialty}`,
      `Optimized for ${contentType} format`,
      `Addresses key audience needs`
    ],
    considerations: [
      `May need input from other agents on ${agentId === 'marketResearch' ? 'SEO optimization' : agentId === 'seoKeyword' ? 'audience targeting' : 'keyword strategy'}`,
      `Structure flexibility may vary based on actual content development`
    ]
  };
}

/**
 * Generate feedback on a content structure proposal
 */
export async function generateContentStructureFeedback(
  fromAgentId: string,
  fromAgentName: string,
  toAgentId: string,
  proposalId: string,
  proposal: ContentStructureProposal,
  agentSpecialty: string
): Promise<ContentStructureFeedback> {
  const model = new ChatOpenAI({
    temperature: 0.7,
    modelName: "gpt-3.5-turbo-1106",
  });

  const prompt = `
    You are a ${fromAgentName} reviewing a content structure proposal from the ${proposal.agentName}.
    
    Content Type: ${proposal.structure.contentType}
    Topic Focus: ${proposal.structure.topicFocus}
    Target Audience: ${proposal.structure.targetAudience.primary}
    
    Proposal: ${JSON.stringify(proposal.structure)}
    
    As a ${fromAgentName} specializing in ${agentSpecialty}, provide feedback on this content structure proposal.
    
    Focus on:
    1. Areas where you agree with the proposal
    2. Suggestions for improvement based on your expertise
    3. Specific feedback on sections that could be enhanced
    
    Your response MUST be valid JSON with the following structure:
    {
      "feedback": "Overall feedback on the proposal",
      "suggestions": [
        {
          "section": "Section ID or title",
          "suggestion": "Specific suggestion for improvement",
          "reasoning": "Why you're making this suggestion"
        }
      ],
      "agreement": [
        {
          "section": "Section ID or title",
          "reasoning": "Why you agree with this section"
        }
      ]
    }
  `;

  try {
    const response = await model.invoke([
      new SystemMessage(
        `You are a ${fromAgentName} specializing in ${agentSpecialty}. Always respond with valid JSON.`
      ),
      new SystemMessage(prompt)
    ]);

    // Parse the response - handle potential markdown formatting
    let jsonContent = response.content as string;
    
    // Clean the response if it contains markdown code blocks
    if (jsonContent.includes("```")) {
      // Extract content between json code blocks
      const jsonMatch = jsonContent.match(/```(?:json)?\s*([\s\S]*?)```/);
      if (jsonMatch && jsonMatch[1]) {
        jsonContent = jsonMatch[1].trim();
      } else {
        // Try to remove any backticks at the beginning and end
        jsonContent = jsonContent.replace(/^```[\s\S]*?\n/, '').replace(/\n```$/, '');
      }
    }
    
    const feedbackResponse = JSON.parse(jsonContent);
    
    return {
      fromAgentId,
      toAgentId,
      proposalId,
      feedback: feedbackResponse.feedback,
      suggestions: feedbackResponse.suggestions,
      agreement: feedbackResponse.agreement
    };
  } catch (error) {
    console.error("Error generating content structure feedback:", error);
    
    // Return a fallback feedback
    return {
      fromAgentId,
      toAgentId,
      proposalId,
      feedback: `As a ${fromAgentName}, I find this proposal generally solid but have some suggestions for improvement.`,
      suggestions: [
        {
          section: "Overall structure",
          suggestion: `Consider adding more emphasis on ${agentSpecialty}`,
          reasoning: `This would better align with the needs of ${proposal.structure.targetAudience.primary}`
        }
      ],
      agreement: [
        {
          section: "Content objective",
          reasoning: "The objective is clear and well-aligned with the topic"
        }
      ]
    };
  }
}

/**
 * Negotiate a final content structure based on proposals and feedback
 */
export async function negotiateFinalContentStructure(
  negotiation: ContentStructureNegotiation
): Promise<ContentStructure> {
  const model = new ChatOpenAI({
    temperature: 0.5,
    modelName: "gpt-3.5-turbo-1106",
  });

  const prompt = `
    You are a Content Lead responsible for finalizing a content structure based on proposals and feedback from multiple agents.
    
    Proposals: ${JSON.stringify(negotiation.proposals)}
    
    Feedback: ${JSON.stringify(negotiation.feedback)}
    
    Your task is to create a final content structure that:
    1. Incorporates the best elements from each proposal
    2. Addresses the feedback provided by each agent
    3. Creates a cohesive and effective structure for the content
    
    IMPORTANT: Show your reasoning process for why this final structure is optimal.
    
    Your response MUST be valid JSON with the same structure as the proposals, but with an additional "integrationReasoning" field explaining how you integrated the different proposals and feedback.
  `;

  try {
    const response = await model.invoke([
      new SystemMessage(
        "You are a Content Lead responsible for finalizing content structures. Always respond with valid JSON."
      ),
      new SystemMessage(prompt)
    ]);

    // Parse the response - handle potential markdown formatting
    let jsonContent = response.content as string;
    
    // Clean the response if it contains markdown code blocks
    if (jsonContent.includes("```")) {
      // Extract content between json code blocks
      const jsonMatch = jsonContent.match(/```(?:json)?\s*([\s\S]*?)```/);
      if (jsonMatch && jsonMatch[1]) {
        jsonContent = jsonMatch[1].trim();
      } else {
        // Try to remove any backticks at the beginning and end
        jsonContent = jsonContent.replace(/^```[\s\S]*?\n/, '').replace(/\n```$/, '');
      }
    }
    
    const finalStructureResponse = JSON.parse(jsonContent);
    
    return finalStructureResponse as ContentStructure;
  } catch (error) {
    console.error("Error negotiating final content structure:", error);
    
    // If there are proposals, return the first one as a fallback
    if (negotiation.proposals.length > 0) {
      return negotiation.proposals[0].structure;
    }
    
    // Otherwise, return a basic structure
    return {
      contentType: 'blog-article',
      contentObjective: "Provide information on the topic",
      topicFocus: "General topic",
      targetAudience: {
        primary: "General audience",
        needs: ["Information"]
      },
      sections: [
        {
          id: "section-1",
          type: "heading",
          title: "Introduction",
          purpose: "Introduce the topic",
          estimatedLength: "200 words"
        },
        {
          id: "section-2",
          type: "heading",
          title: "Main Content",
          purpose: "Provide details",
          estimatedLength: "800 words"
        },
        {
          id: "section-3",
          type: "heading",
          title: "Conclusion",
          purpose: "Summarize",
          estimatedLength: "200 words"
        }
      ],
      keywordStrategy: {
        primary: "Focus on primary keyword",
        secondary: "Include secondary keywords",
        distribution: "Natural distribution"
      },
      toneAndStyle: "Informative",
      contentDifferentiators: ["Comprehensive"],
      reasoning: "Basic structure covering essential elements"
    };
  }
}

/**
 * Initialize a content structure negotiation
 */
export function initializeContentStructureNegotiation(): ContentStructureNegotiation {
  return {
    proposals: [],
    feedback: [],
    negotiationStatus: 'in-progress'
  };
}

/**
 * Add a proposal to the negotiation
 */
export function addProposalToNegotiation(
  negotiation: ContentStructureNegotiation,
  proposal: ContentStructureProposal
): ContentStructureNegotiation {
  return {
    ...negotiation,
    proposals: [...negotiation.proposals, proposal]
  };
}

/**
 * Add feedback to the negotiation
 */
export function addFeedbackToNegotiation(
  negotiation: ContentStructureNegotiation,
  feedback: ContentStructureFeedback
): ContentStructureNegotiation {
  return {
    ...negotiation,
    feedback: [...negotiation.feedback, feedback]
  };
}

/**
 * Complete the negotiation with a final structure
 */
export function completeNegotiation(
  negotiation: ContentStructureNegotiation,
  finalStructure: ContentStructure
): ContentStructureNegotiation {
  return {
    ...negotiation,
    finalStructure,
    negotiationStatus: 'completed'
  };
}

/**
 * Convert a content structure to a task artifact
 */
export function contentStructureToArtifact(
  structure: ContentStructure,
  name: string = "Content Structure"
): Artifact {
  return {
    name,
    parts: [
      {
        type: "data",
        data: structure
      }
    ],
    index: 0
  };
}

/**
 * Extract a content structure from a task artifact
 */
export function extractContentStructureFromArtifact(
  artifact: Artifact
): ContentStructure | null {
  const dataPart = artifact.parts.find(part => part.type === "data");
  
  if (dataPart && dataPart.type === "data") {
    return dataPart.data as ContentStructure;
  }
  
  return null;
}