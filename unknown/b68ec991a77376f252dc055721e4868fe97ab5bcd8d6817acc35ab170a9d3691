// src/app/(payload)/api/agents/collaborative-iteration/controller.ts

import { v4 as uuidv4 } from 'uuid';
import { 
  IterativeCollaborationState, 
  IterativeMessage, 
  IterativeArtifact,
  Consultation,
  AgentState,
  ConsultationRequest,
  ConsultationResponse,
  IterationRequest,
  IterationResponse,
  ITERATIVE_CONFIG,
  IterativeMessageType,
  AgentId
} from './types';
import { Reasoning } from '../a2atypes';
import { stateStore } from './server-based';
import { messageBus, getSessionMessageBus } from './server-based';
import { AgentStateManager } from './core/AgentStateManager';
import { AgentMessaging } from './core/AgentMessaging';
import { getAgentRoleType } from './utils/agentRoleMapper';
import logger from './utils/logger';

/**
 * Standard return type for message handler methods
 */
interface StandardizedHandlerResult {
  response: IterativeMessage | null;
  stateUpdates: Partial<IterativeCollaborationState>;
}

/**
 * Controller for managing iterative collaboration between agents
 * 
 * This controller handles:
 * 1. State management for iterative collaboration
 * 2. Message routing between agents
 * 3. Tracking consultations and iterations
 * 4. Convergence detection for final output
 */
export class IterativeCollaborationController {
  private sessions: Map<string, IterativeCollaborationState> = new Map();
  private baseUrl: string;
  private messageService: AgentMessaging;
  private stateManager: AgentStateManager;
  
  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl || process.env.NEXT_PUBLIC_API_URL || 
      `http://${process.env.VERCEL_URL || 'localhost:3000'}`;
    
    // Initialize dependencies
    this.messageService = new AgentMessaging(messageBus);
    this.stateManager = new AgentStateManager(stateStore);
  }
  
  /**
   * Start a new iterative collaboration session
   */
  async startCollaboration(
    topic: string,
    contentType: 'blog-article' | 'product-page' | 'buying-guide',
    targetAudience: string,
    tone: string = 'professional',
    keywords: string[] = [],
    additionalInstructions: string = '',
    clientName: string = ''
  ): Promise<IterativeCollaborationState> {
    const sessionId = uuidv4();
    
    // Create initial state
    const state: IterativeCollaborationState = {
      id: sessionId,
      topic,
      contentType,
      targetAudience,
      tone,
      keywords,
      status: 'active',
      startTime: new Date().toISOString(),
      artifacts: {},
      consultations: {},
      agentStates: {},
      currentPhase: 'planning',
      collaborationState: 'planning',
      messages: [],
      iterations: 0,
      maxIterations: ITERATIVE_CONFIG.maxIterations,
      events: [],
      sessionId,
      clientName: clientName || undefined,
      additionalInstructions: additionalInstructions || undefined
    };
    
    // Initialize agent states
    const agents = Object.values(AgentId);
    
    state.agents = {};
    
    agents.forEach(agentId => {
      // Create agent state record
      state.agentStates[agentId] = {
        id: agentId,
        processedRequests: [],
        generatedArtifacts: [],
        consultationsProvided: [],
        consultationsReceived: [],
        lastUpdated: new Date().toISOString()
      };
      
      // Track active agents
      if (state.agents) {
        state.agents[agentId] = {
          active: true,
          role: this.getAgentRoleType(agentId as AgentId),
          status: 'ready'
        };
      }
    });
    
    // Store in the stateStore
    try {
      await stateStore.setState(sessionId, state);
    } catch (err) {
      console.error(`Error storing new session in stateStore`, {
        sessionId,
        error: err instanceof Error ? err.message : String(err)
      });
    }
    
    // Store the session in the controller's sessions Map
    this.sessions.set(sessionId, state);
    
    return state;
  }
  
  /**
   * Get a session by ID (synchronous version)
   */
  getSession(sessionId: string): IterativeCollaborationState | null {
    return this.sessions.get(sessionId) || null;
  }
  
  /**
   * Get a session by ID (async version)
   * Checks both the controller's sessions Map and the stateStore
   */
  async getSessionAsync(sessionId: string): Promise<IterativeCollaborationState | null> {
    // Check the controller's sessions Map first for performance
    const fromMap = this.sessions.get(sessionId);
    if (fromMap) return fromMap;
    
    // If not found in Map, try the stateStore
    try {
      const fromStore = await stateStore.getState(sessionId);
      
      // If found in stateStore, sync back to the controller's Map
      if (fromStore) {
        this.sessions.set(sessionId, fromStore);
        return fromStore;
      }
    } catch (error) {
      console.error(`Error retrieving session from stateStore: ${error}`);
    }
    
    return null;
  }
  
  /**
   * Update a session state
   */
  async updateSession(sessionId: string, updater: (state: IterativeCollaborationState) => IterativeCollaborationState): Promise<IterativeCollaborationState | null> {
    // Check both storage mechanisms using getSessionAsync
    const session = await this.getSessionAsync(sessionId);
    if (!session) {
      console.error(`Failed to update session ${sessionId}: Session not found`);
      return null;
    }
    
    const updatedSession = updater(session);
    // Update both storage locations
    this.sessions.set(sessionId, updatedSession);
    await stateStore.setState(sessionId, updatedSession);
    
    return updatedSession;
  }
  
  /**
   * Maps a generic REQUEST type message to a more specific message type based on context
   * This is needed because agents only register handlers for specific message types
   */
  private mapRequestToSpecificType(message: IterativeMessage, session: IterativeCollaborationState): IterativeMessage {
    // If it's not a REQUEST type, return the original message
    if (message.type !== IterativeMessageType.REQUEST) {
      return message;
    }
    
    console.log(`Mapping REQUEST message from ${message.from} to ${message.to} to specific type`);
    
    const toAgent = typeof message.to === 'string' ? message.to : (Array.isArray(message.to) ? message.to[0] : '');
    const fromAgent = message.from;
    const phase = session.currentPhase || 'planning';
    const agentState = session.agentStates?.[toAgent as AgentId] || {};
    
    // Check if this is the first message to this agent (no processed requests)
    const isFirstMessageToAgent = !agentState.processedRequests || agentState.processedRequests.length === 0;
    
    // Check if we have artifacts already
    const artifacts = session.artifacts || {};
    const hasArtifacts = Object.keys(artifacts).length > 0;
    
    // Check for specific artifact types
    const hasContentArtifacts = Object.values(artifacts).some(a => a.type === 'content');
    const hasKeywordArtifacts = Object.values(artifacts).some(a => a.type === 'keywords');
    const hasMarketResearchArtifacts = Object.values(artifacts).some(a => a.type === 'market_research');
    const hasStrategyArtifacts = Object.values(artifacts).some(a => a.type === 'content_strategy');
    
    // Check message content for specific indicators
    const messageContent = message.content || {};
    const contentType = messageContent.type;
    const artifactType = messageContent.artifactType;
    const hasArtifactReference = Boolean(
      messageContent.artifactType || 
      messageContent.artifactId || 
      (messageContent.artifacts && messageContent.artifacts.length > 0) || 
      (messageContent.artifactIds && messageContent.artifactIds.length > 0)
    );
    
    // Check for discussions
    const isDiscussionContribution = contentType === 'discussion_contribution' || 
                                   messageContent.discussionId;
    const isDiscussionStart = contentType === 'discussion_start';
    const isInDiscussionPhase = phase === 'discussion';
    
    // Check for consultation
    const isConsultation = contentType === 'consultation' || 
                          phase === 'research' || 
                          (fromAgent !== 'system' && fromAgent !== toAgent && !isDiscussionContribution);
    
    // Check if we're in feedback phase
    const isFeedbackPhase = phase === 'review' || phase === 'refinement';
    const isFeedbackMessage = contentType === 'feedback' || messageContent.feedback;
    
    // Agent-specific context
    // For SEO Keyword Agent
    const isSeoKeywordAgent = toAgent === AgentId.SEO_KEYWORD;
    const requestingKeywords = artifactType === 'keywords' || messageContent.keywordType;
    
    // For Market Research Agent
    const isMarketResearchAgent = toAgent === AgentId.MARKET_RESEARCH;
    const requestingMarketResearch = artifactType === 'market_research' || messageContent.researchType;
    
    // For Content Strategy Agent
    const isContentStrategyAgent = toAgent === AgentId.CONTENT_STRATEGY;
    const requestingContentStrategy = artifactType === 'content_strategy' || messageContent.strategyType;
    
    // For Content Generation Agent
    const isContentGenAgent = toAgent === AgentId.CONTENT_GENERATION;
    const requestingContent = artifactType === 'content' || messageContent.contentType;
    const isIterationRequest = contentType === 'iteration_request' || messageContent.iteration;
    
    // For SEO Optimization Agent
    const isSeoOptAgent = toAgent === AgentId.SEO_OPTIMIZATION;
    const requestingSeoAnalysis = artifactType === 'seo_analysis' || messageContent.seoType;
    
    // Log the analysis factors
    console.log(`Message analysis: 
      Agent: ${toAgent} 
      Phase: ${phase} 
      isFirstMessage: ${isFirstMessageToAgent} 
      hasArtifacts: ${hasArtifacts} 
      hasArtifactReference: ${hasArtifactReference} 
      isConsultation: ${isConsultation} 
      isFeedback: ${isFeedbackMessage || isFeedbackPhase}
      isDiscussion: ${isDiscussionContribution || isDiscussionStart || isInDiscussionPhase}`
    );
    
    // Map to specific type based on context
    let specificType: IterativeMessageType;
    
    // Special handling for UPDATE message type
    if (message.type === 'UPDATE') {
      if (isInDiscussionPhase || isDiscussionContribution) {
        // In discussion phase, map UPDATE to DISCUSSION_CONTRIBUTION
        specificType = IterativeMessageType.DISCUSSION_CONTRIBUTION;
        console.log(`Mapped UPDATE to DISCUSSION_CONTRIBUTION since we're in discussion phase`);
      } else if (hasArtifacts || hasArtifactReference) {
        // If there are artifacts involved, map to ARTIFACT_REQUEST
        specificType = IterativeMessageType.ARTIFACT_REQUEST;
        console.log(`Mapped UPDATE to ARTIFACT_REQUEST since it involves artifacts`);
      } else if (phase === 'review' || phase === 'refinement') {
        // In review phase, map to FEEDBACK
        specificType = IterativeMessageType.FEEDBACK;
        console.log(`Mapped UPDATE to FEEDBACK since we're in review/refinement phase`);
      } else {
        // Default fallback for UPDATE messages
        specificType = IterativeMessageType.ARTIFACT_REQUEST;
        console.log(`Default mapped UPDATE to ARTIFACT_REQUEST as fallback`);
      }
    } else if (isFirstMessageToAgent) {
      // First message should be mapped to INITIAL_REQUEST
      specificType = IterativeMessageType.INITIAL_REQUEST;
      console.log(`Mapped to INITIAL_REQUEST because this is the first message to ${toAgent}`);
    } else if (isDiscussionStart || (isInDiscussionPhase && !isDiscussionContribution)) {
      // If starting a discussion
      specificType = IterativeMessageType.DISCUSSION_START;
      console.log(`Mapped to DISCUSSION_START because ${isDiscussionStart ? 'content type indicates discussion start' : 'we are in discussion phase'}`);
    } else if (isDiscussionContribution) {
      // If contributing to a discussion
      specificType = IterativeMessageType.DISCUSSION_CONTRIBUTION;
      console.log(`Mapped to DISCUSSION_CONTRIBUTION because content indicates discussion contribution`);
    } else if (isFeedbackMessage || isFeedbackPhase) {
      // If explicitly providing feedback or in feedback phase
      specificType = IterativeMessageType.FEEDBACK;
      console.log(`Mapped to FEEDBACK because ${isFeedbackMessage ? 'content type is feedback' : 'we are in feedback/review phase'}`);
    } else if (isConsultation) {
      // If it's a consultation between agents
      specificType = IterativeMessageType.CONSULTATION_REQUEST;
      console.log(`Mapped to CONSULTATION_REQUEST because message is from ${fromAgent} to ${toAgent} in ${phase} phase`);
    } else if (isIterationRequest && isContentGenAgent) {
      // If explicitly requesting content iteration
      specificType = IterativeMessageType.ITERATION_REQUEST;
      console.log(`Mapped to ITERATION_REQUEST for content generation agent`);
    } else if (hasArtifactReference || requestingKeywords || requestingMarketResearch || 
              requestingContentStrategy || requestingContent || requestingSeoAnalysis) {
      // If requesting any kind of artifact
      specificType = IterativeMessageType.ARTIFACT_REQUEST;
      console.log(`Mapped to ARTIFACT_REQUEST because message references or requests artifacts`);
    } else {
      // Determine most appropriate default based on agent and phase
      if (isContentGenAgent && hasStrategyArtifacts) {
        specificType = IterativeMessageType.ITERATION_REQUEST;
        console.log(`Default mapped to ITERATION_REQUEST for content generation agent with strategy artifacts available`);
      } else if (isSeoOptAgent && hasContentArtifacts) {
        specificType = IterativeMessageType.FEEDBACK;
        console.log(`Default mapped to FEEDBACK for SEO optimization agent with content artifacts available`);
      } else {
        // Default to ARTIFACT_REQUEST as fallback for subsequent requests
        specificType = IterativeMessageType.ARTIFACT_REQUEST;
        console.log(`Default mapped to ARTIFACT_REQUEST as fallback`);
      }
    }
    
    console.log(`Mapped REQUEST to ${specificType} based on context`);
    
    // Create a new message with the specific type
    return {
      ...message,
      type: specificType
    };
  }

  /**
   * Process a message for a session
   */
  async processMessage(sessionId: string, message: IterativeMessage): Promise<{ response: IterativeMessage | null, stateUpdates: Partial<IterativeCollaborationState> }> {
    console.log(`Processing message from ${message.from} to ${message.to} of type ${message.type}`);
    
    // Validate the message
    if (!message) {
      console.error('[CONTROLLER] No message provided to process');
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    try {
      // Get the session using the async method that checks both sources
      const session = await this.getSessionAsync(sessionId);
      if (!session) {
        console.error(`[CONTROLLER] Session with ID ${sessionId} not found`);
        return {
          response: {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: 'system',
            to: message.from,
            type: IterativeMessageType.RESPONSE,
            content: {
              error: `Session with ID ${sessionId} not found`,
              originalMessage: message
            },
            conversationId: message.conversationId || uuidv4()
          },
          stateUpdates: {}
        };
      }

      // If this is a REQUEST type, map it to a more specific type based on context
      const processedMessage = this.mapRequestToSpecificType(message, session);
      
      // Log the mapping if it occurred
      if (processedMessage.type !== message.type) {
        console.log(`Mapped message type from ${message.type} to ${processedMessage.type}`);
      }

      // Process the message based on its type
      switch (processedMessage.type) {
        case IterativeMessageType.INITIAL_REQUEST:
          return this.handleInitialRequest(sessionId, processedMessage);
        case IterativeMessageType.CONSULTATION_REQUEST:
          return this.handleConsultationRequest(sessionId, processedMessage as ConsultationRequest);
        case IterativeMessageType.CONSULTATION_RESPONSE:
          return this.handleConsultationResponse(sessionId, processedMessage as ConsultationResponse);
        case IterativeMessageType.ITERATION_REQUEST:
          return this.handleIterationRequest(sessionId, processedMessage as IterationRequest);
        case IterativeMessageType.ITERATION_RESPONSE:
          return this.handleIterationResponse(sessionId, processedMessage as IterationResponse); 
        case IterativeMessageType.ARTIFACT_DELIVERY:
          return this.handleArtifactDelivery(sessionId, processedMessage);
        case IterativeMessageType.ARTIFACT_REQUEST:
          return this.handleArtifactRequest(sessionId, processedMessage);
        case IterativeMessageType.FINAL_OUTPUT:
          console.log(`Final output received from ${processedMessage.from}:`, processedMessage.content);
          return {
            response: null,
            stateUpdates: {
              status: 'completed',
              endTime: new Date().toISOString()
            }
          };
        default:
          console.warn(`Unhandled message type: ${message.type}`);
          return {
            response: null,
            stateUpdates: {}
          };
      }
    } catch (error) {
      console.error(`Error processing message of type ${message.type}:`, error instanceof Error ? error.message : String(error));
      return {
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: 'system',
          to: message.from,
          type: IterativeMessageType.RESPONSE,
          content: {
            error: `Error processing message: ${error instanceof Error ? error.message : String(error)}`,
            originalMessage: message
          },
          conversationId: message.conversationId || uuidv4()
        },
        stateUpdates: {
          status: 'active' // Keep the session active despite the error
        }
      };
    }
  }
  
  /**
   * Handle initial content generation request
   */
  private async handleInitialRequest(sessionId: string, message: IterativeMessage): Promise<StandardizedHandlerResult> {
    console.log('Handling initial request for session:', sessionId);
    
    const session = this.getSession(sessionId);
    if (!session) {
      throw new Error(`Session with ID ${sessionId} not found`);
    }
    
    try {
      // Import the market research agent handler
      console.log('Importing market research agent handler...');
      const { handleMarketResearchInitialRequest } = await import('./agents/market-research');
      
      // Create initial message for market research
      const initialMessage: IterativeMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: 'system',
        to: AgentId.MARKET_RESEARCH,
        type: IterativeMessageType.INITIAL_REQUEST,
        content: {
          topic: session.topic,
          contentType: session.contentType,
          targetAudience: session.targetAudience,
          tone: session.tone,
          keywords: session.keywords,
          additionalInstructions: session.additionalInstructions
        },
        conversationId: uuidv4()
      };
      
      // Get the market research agent state
      const marketResearchState = session.agentStates[AgentId.MARKET_RESEARCH] || {
        id: AgentId.MARKET_RESEARCH,
        processedRequests: [],
        generatedArtifacts: [],
        consultationsProvided: [],
        consultationsReceived: [],
        lastUpdated: new Date().toISOString()
      };
      
      // Process the message with the market research agent
      console.log('Processing message with market research agent...');
      const result = await handleMarketResearchInitialRequest(
        initialMessage,
        marketResearchState
      );
      
      // Update the session with the result
      const updatedArtifacts = { ...session.artifacts };
      
      if (result.artifactUpdates?.new) {
        Object.assign(updatedArtifacts, result.artifactUpdates.new);
      }
      
      if (result.artifactUpdates?.updated) {
        Object.assign(updatedArtifacts, result.artifactUpdates.updated);
      }
      
      // Update agent states
      const updatedAgentStates = { ...session.agentStates };
      
      if (result.updatedState) {
        updatedAgentStates[AgentId.MARKET_RESEARCH] = result.updatedState;
      }
      
      // Update state
      const stateUpdates: Partial<IterativeCollaborationState> = {
        currentPhase: 'research',
        artifacts: updatedArtifacts,
        agentStates: updatedAgentStates,
        messages: [...session.messages, initialMessage],
        iterations: session.iterations + 1
      };
      
      // Return the response
      return {
        response: result.response || null,
        stateUpdates
      };
    } catch (error) {
      console.error('Error processing initial request:', error);
      
      return {
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: 'system',
          to: 'system',
          type: IterativeMessageType.RESPONSE,
          content: {
            error: error instanceof Error ? error.message : 'Unknown error'
          },
          conversationId: message.conversationId || uuidv4()
        },
        stateUpdates: {
          currentPhase: 'error',
          status: 'failed'
        }
      };
    }
  }
  
  /**
   * Handle consultation request
   */
  private async handleConsultationRequest(sessionId: string, message: ConsultationRequest): Promise<StandardizedHandlerResult> {
    console.log(`Handling consultation request from ${message.from} to ${Array.isArray(message.to) ? message.to.join(', ') : message.to}:`, message.content);
    
    const session = this.getSession(sessionId);
    if (!session) {
      console.error(`Session with ID ${sessionId} not found`);
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    // Determine target agent (the agent being consulted)
    const targetAgent = Array.isArray(message.to) ? message.to[0] : message.to;
    if (!targetAgent) {
      console.error('No target agent specified in consultation request');
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    // Get the target agent state or create a new one if it doesn't exist
    const targetAgentState = session.agentStates[targetAgent] || {
      id: targetAgent,
      processedRequests: [],
      generatedArtifacts: [],
      consultationsProvided: [],
      consultationsReceived: [],
      lastUpdated: new Date().toISOString()
    };
    
    try {
      // Import the target agent handler
      console.log(`Importing ${targetAgent} agent handler...`);
      let agentHandler;
      
      // Dynamically import the appropriate handler based on targetAgent
      switch (targetAgent) {
        case AgentId.MARKET_RESEARCH:
          const { handleMarketResearchConsultation } = await import('./agents/market-research');
          agentHandler = handleMarketResearchConsultation;
          break;
        case AgentId.CONTENT_GENERATION:
          const { handleContentGenerationConsultation } = await import('./agents/content-generation');
          agentHandler = handleContentGenerationConsultation;
          break;
        case AgentId.SEO_OPTIMIZATION:
          const { handleSeoOptimizationConsultation } = await import('./agents/seo-optimization');
          agentHandler = handleSeoOptimizationConsultation;
          break;
        default:
          throw new Error(`Unknown agent: ${targetAgent} or no handler for message type ${message.type}`);
      }
      
      // Create a unique ID for this consultation
      const consultationId = uuidv4();
      
      // Update the target agent state to include the received consultation
      targetAgentState.consultationsReceived = targetAgentState.consultationsReceived || [];
      targetAgentState.consultationsReceived.push(consultationId);
      
      // Create a consultation record
      const consultation = {
        id: consultationId,
        requestId: message.id,
        requestingAgent: message.from,
        targetAgent,
        timestamp: new Date().toISOString(),
        status: 'pending'
      };
      
      // Create a combined state with both agent state and session state for the handler
      const combinedState = {
        ...targetAgentState,
        topic: session.topic,
        contentType: session.contentType,
        targetAudience: session.targetAudience,
        tone: session.tone,
        keywords: session.keywords,
        status: session.status,
        startTime: session.startTime,
        endTime: session.endTime,
        artifacts: session.artifacts,
        consultations: {
          ...session.consultations,
          [consultationId]: consultation
        },
        agentStates: session.agentStates,
        currentPhase: session.currentPhase,
        messages: session.messages,
        iterations: session.iterations,
        maxIterations: session.maxIterations
      };
      
      // Process the message with the target agent
      const result = await agentHandler(
        message,
        combinedState,
        this.messageService,
        this.stateManager
      );
      
      // Prepare state updates
      const stateUpdates: Partial<IterativeCollaborationState> = {};
      
      // Update agent state to reflect completion
      stateUpdates.agentStates = {
        ...session.agentStates,
        [targetAgent]: {
          ...session.agentStates[targetAgent],
          lastUpdated: new Date().toISOString(),
          status: 'completed'
        }
      };
      
      // Add the consultation to the state
      stateUpdates.consultations = {
        ...session.consultations,
        [consultationId]: {
          ...consultation,
          status: 'completed'
        }
      };
      
      // Return the response
      return {
        response: result && result.response ? result.response : null,
        stateUpdates
      };
    } catch (error) {
      console.error('Error processing consultation request:', error);
      
      return {
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: 'system',
          to: message.from,
          type: IterativeMessageType.ERROR,
          content: `Error processing your consultation request: ${error instanceof Error ? error.message : String(error)}`,
          conversationId: message.conversationId || uuidv4()
        },
        stateUpdates: {}
      };
    }
  }
  
  /**
   * Handle consultation response
   */
  private async handleConsultationResponse(sessionId: string, message: ConsultationResponse): Promise<StandardizedHandlerResult> {
    console.log(`Handling consultation response from ${message.from}:`, message.content);
    
    try {
      const session = this.getSession(sessionId);
      if (!session) {
        console.error(`Session ${sessionId} not found`);
        return {
          response: null,
          stateUpdates: {}
        };
      }
      
      // Extract consultation details
      const consultationId = message.consultationId;
      if (!consultationId) {
        console.error('No consultation ID in response', message);
        return {
          response: null,
          stateUpdates: {}
        };
      }
      
      // Find the consultation
      const consultation = session.consultations[consultationId];
      if (!consultation) {
        console.error(`Consultation ${consultationId} not found`);
        return {
          response: null,
          stateUpdates: {}
        };
      }
      
      // Update the consultation
      const updatedConsultation = {
        ...consultation,
        status: 'completed',
        feedback: message.feedback,
        suggestions: message.suggestions || []
      };
      
      // Return response to original requester
      return {
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: message.from,
          to: consultation.requestingAgent,
          type: IterativeMessageType.RESPONSE,
          content: {
            consultationId,
            feedback: message.feedback,
            suggestions: message.suggestions
          },
          conversationId: message.conversationId || uuidv4()
        },
        stateUpdates: {
          consultations: {
            ...session.consultations,
            [consultationId]: updatedConsultation
          }
        }
      };
    } catch (error) {
      console.error('Error processing consultation response:', error);
      return {
        response: null,
        stateUpdates: {}
      };
    }
  }
  
  /**
   * Handle iteration request
   */
  private async handleIterationRequest(sessionId: string, message: IterationRequest): Promise<StandardizedHandlerResult> {
    console.log(`Handling iteration request for artifact ${message.artifactId}`);
    
    const session = this.getSession(sessionId);
    if (!session) {
      console.error(`Session ${sessionId} not found`);
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    // Find the artifact
    const artifact = session.artifacts[message.artifactId];
    if (!artifact) {
      console.error(`Artifact ${message.artifactId} not found`);
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    // Create a unique ID for this iteration
    const iterationId = uuidv4();
    
    // Update state with the iteration request
    return {
      response: null,
      stateUpdates: {
        iterations: session.iterations + 1,
        currentPhase: 'refinement'
      }
    };
  }
  
  /**
   * Handle iteration response
   */
  private async handleIterationResponse(sessionId: string, message: IterationResponse): Promise<StandardizedHandlerResult> {
    console.log(`Handling iteration response for artifact ${message.artifactId}`);
    
    const session = this.getSession(sessionId);
    if (!session) {
      console.error(`Session ${sessionId} not found`);
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    // Find the artifact
    const artifact = session.artifacts[message.artifactId];
    if (!artifact) {
      console.error(`Artifact ${message.artifactId} not found`);
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    // Create a new iteration
    const newIteration = {
      version: message.newVersion,
      timestamp: new Date().toISOString(),
      agent: message.from,
      content: message.content,
      feedback: [],
      incorporatedConsultations: [],
      changes: message.changes
    };
    
    // Update the artifact
    const updatedArtifact = {
      ...artifact,
      currentVersion: message.newVersion,
      iterations: [...(artifact.iterations || []), newIteration],
      qualityScore: message.qualityScore,
      updatedAt: new Date().toISOString()
    };
    
    // Update state
    return {
      response: null,
      stateUpdates: {
        artifacts: {
          ...session.artifacts,
          [message.artifactId]: updatedArtifact
        },
        iterations: session.iterations + 1,
        currentPhase: message.convergenceStatus === 'converged' ? 'finalization' : 'refinement'
      }
    };
  }
  
  /**
   * Handle artifact delivery
   */
  private async handleArtifactDelivery(sessionId: string, message: IterativeMessage): Promise<StandardizedHandlerResult> {
    console.log(`Handling artifact delivery from ${message.from}`);
    
    const session = this.getSession(sessionId);
    if (!session) {
      console.error(`Session ${sessionId} not found`);
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    // Extract artifact data
    const { artifactId, artifact } = message.content || {};
    
    if (!artifactId || !artifact) {
      console.error('Missing artifact information in delivery');
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    // Ensure the artifact has required fields
    const fullArtifact = {
      ...artifact,
      id: artifactId,
      createdBy: message.from,
      createdAt: artifact.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Update agent state
    const agentState = session.agentStates[message.from] || {
      id: message.from,
      processedRequests: [],
      generatedArtifacts: [],
      consultationsProvided: [],
      consultationsReceived: [],
      lastUpdated: new Date().toISOString()
    };
    
    if (!agentState.generatedArtifacts) {
      agentState.generatedArtifacts = [];
    }
    
    if (!agentState.generatedArtifacts.includes(artifactId)) {
      agentState.generatedArtifacts.push(artifactId);
    }
    
    // Update state
    return {
      response: null,
      stateUpdates: {
        artifacts: {
          ...session.artifacts,
          [artifactId]: fullArtifact
        },
        agentStates: {
          ...session.agentStates,
          [message.from]: agentState
        }
      }
    };
  }
  
  /**
   * Handle artifact request
   */
  private async handleArtifactRequest(sessionId: string, message: IterativeMessage): Promise<StandardizedHandlerResult> {
    console.log(`Handling artifact request from ${message.from}`);
    
    const session = this.getSession(sessionId);
    if (!session) {
      console.error(`Session ${sessionId} not found`);
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    // Extract artifact ID
    const { artifactId } = message.content || {};
    
    if (!artifactId) {
      console.error('Missing artifact ID in request');
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    // Find the artifact
    const artifact = session.artifacts[artifactId];
    
    if (!artifact) {
      console.error(`Artifact ${artifactId} not found`);
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    // Return the artifact
    return {
      response: {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: 'system',
        to: message.from,
        type: IterativeMessageType.RESPONSE,
        content: {
          artifactId,
          artifact
        },
        conversationId: message.conversationId || uuidv4()
      },
      stateUpdates: {}
    };
  }
  
  /**
   * Check if content has converged to a final state
   */
  private hasContentConverged(artifactId: string, session: IterativeCollaborationState): boolean {
    // Check if we have a valid artifact
    const artifact = session.artifacts[artifactId];
    if (!artifact) return false;
    
    // If we've reached maximum iterations
    if (session.iterations >= session.maxIterations) {
      return true;
    }
    
    // If quality score exceeds threshold
    if (artifact.qualityScore >= ITERATIVE_CONFIG.qualityThreshold) {
      return true;
    }
    
    // Default: continue iterations
    return false;
  }
}
