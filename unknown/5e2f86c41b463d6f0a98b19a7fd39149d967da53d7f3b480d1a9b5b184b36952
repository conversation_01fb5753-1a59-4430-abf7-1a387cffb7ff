/**
 * Content Creation Agent
 *
 * This file implements the specialized content creation agent for the goal-based orchestration system.
 * It handles content creation goals and produces content artifacts.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import {
  MessageType,
  Message,
  GoalStatus,
  GoalType,
  CollaborationState,
  Artifact
} from '../state/unified-schema';
import { StateManager } from '../state/manager';
import { ArtifactDecisionFramework, ArtifactDecisionType } from '../services/artifact-decision-framework';
import { FeedbackLoopSystem } from '../utils/feedback-loop-system-new';
import { AgentBase } from './agent-base';

/**
 * Content Creation Agent
 */
export class ContentCreationAgent extends AgentBase {
  /**
   * Constructor
   * @param sessionId Session ID
   */
  constructor(sessionId: string) {
    super('content-creation', sessionId);
    logger.info(`Content Creation Agent initialized for session ${sessionId}`);
  }

  /**
   * Register message handlers
   */
  protected registerHandlers(): void {
    this.registerHandler(MessageType.GOAL_UPDATE, this.handleGoalAssignment.bind(this));
    this.registerHandler(MessageType.ARTIFACT_UPDATE, this.handleArtifactRequest.bind(this));
    this.registerHandler(MessageType.FEEDBACK_RESPONSE, this.handleFeedback.bind(this));
    this.registerHandler(MessageType.FEEDBACK_REQUEST, this.handleConsultationRequest.bind(this));
    this.registerHandler(MessageType.SYSTEM, this.handleGoalAssignment.bind(this));
    this.registerHandler(MessageType.USER, this.handleGoalAssignment.bind(this));
  }

  /**
   * Process a goal
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  public async processGoal(goalId: string): Promise<boolean> {
    try {
      logger.info(`Content Creation Agent processing goal ${goalId}`, {
        sessionId: this.getSessionId(),
        goalId
      });

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Get the goal
      const goal = state.goals.byId[goalId];
      if (!goal) {
        throw new Error(`Goal ${goalId} not found`);
      }

      // Assign goal to agent
      await this.stateManager.assignGoal(goalId, this.agentId);

      // Make a decision about artifact creation
      const decision = await this.decisionFramework.decideArtifactCreation('content-creation', goalId);

      // If we should use an existing artifact
      if (decision.type === ArtifactDecisionType.USE_EXISTING && decision.existingArtifactId) {
        // Complete the goal with the existing artifact
        await this.stateManager.completeGoal(goalId, decision.existingArtifactId);
        return true;
      }

      // Find content strategy and keyword analysis artifacts
      const contentStrategyArtifacts = Object.values(state.artifacts).filter(
        artifact => artifact.type === 'content-strategy'
      );

      const keywordAnalysisArtifacts = Object.values(state.artifacts).filter(
        artifact => artifact.type === 'keyword-analysis'
      );

      if (contentStrategyArtifacts.length === 0) {
        throw new Error('Content strategy artifact not found');
      }

      if (keywordAnalysisArtifacts.length === 0) {
        throw new Error('Keyword analysis artifact not found');
      }

      // Get the most recent artifacts
      const contentStrategyArtifact = contentStrategyArtifacts.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      const keywordAnalysisArtifact = keywordAnalysisArtifacts.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      // If we should improve an existing artifact
      if (decision.type === ArtifactDecisionType.IMPROVE_EXISTING && decision.existingArtifactId) {
        // Get the existing artifact
        const existingArtifact = state.artifacts[decision.existingArtifactId];
        if (!existingArtifact) {
          throw new Error(`Artifact ${decision.existingArtifactId} not found`);
        }

        // Import the OpenAI integration
        const { generateArticleContent } = await import('../utils/content-generation');

        // Generate improved article content using OpenAI
        const { title, content } = await generateArticleContent(
          state.topic,
          state.contentType,
          state.targetAudience,
          state.tone,
          contentStrategyArtifact,
          keywordAnalysisArtifact
        );

        // Create the improved artifact
        const artifactId = await this.stateManager.createArtifact(
          'content-creation',
          title,
          content,
          'content-creation',
          goalId,
          decision.existingArtifactId
        );

        // Initialize feedback loop system
        const feedbackSystem = new FeedbackLoopSystem(this.getSessionId());

        // Generate feedback on the content
        const feedback = await feedbackSystem.generateFeedback(
          artifactId,
          'seo-optimization',
          ['content quality', 'keyword usage', 'readability']
        );

        // Create a feedback request
        const requestId = await feedbackSystem.requestFeedback(
          'content-creation',
          'seo-optimization',
          artifactId,
          ['content quality', 'keyword usage', 'readability']
        );

        // Provide feedback
        await feedbackSystem.provideFeedback(
          'seo-optimization',
          'content-creation',
          requestId,
          feedback
        );

        // Complete the goal
        await this.stateManager.completeGoal(goalId, artifactId);
        return true;
      }

      // Otherwise, create a new artifact
      // Import the OpenAI integration
      const { generateArticleContent } = await import('../utils/content-generation');

      // Generate article content using OpenAI
      const { title, content } = await generateArticleContent(
        state.topic,
        state.contentType,
        state.targetAudience,
        state.tone,
        contentStrategyArtifact,
        keywordAnalysisArtifact
      );

      // Create the artifact with the generated content
      const artifactId = await this.stateManager.createArtifact(
        'content-creation',
        title,
        content,
        'content-creation',
        goalId
      );

      // Initialize feedback loop system
      const feedbackSystem = new FeedbackLoopSystem(this.getSessionId());

      // The artifact will be automatically evaluated by the StateManager
      // We'll wait a short time to allow the evaluation to complete
      logger.info(`Content creation artifact created, waiting for evaluation: ${artifactId}`, {
        sessionId: this.getSessionId(),
        goalId,
        artifactId
      });

      // Wait for the evaluation to complete (give it a moment)
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Get the current state to check the evaluation results
      const updatedState = await this.stateManager.getState();
      if (!updatedState) {
        throw new Error('Failed to get updated state');
      }

      const updatedArtifact = updatedState.artifacts[artifactId];
      if (!updatedArtifact) {
        throw new Error(`Artifact ${artifactId} not found in updated state`);
      }

      // Check if the artifact has been evaluated
      const evaluation = updatedArtifact.metadata?.evaluation;

      if (evaluation) {
        logger.info(`Content creation artifact evaluation found`, {
          sessionId: this.getSessionId(),
          goalId,
          artifactId,
          meetsRequirements: evaluation.meetsRequirements,
          score: evaluation.score
        });

        // If the artifact doesn't meet requirements, improve it based on feedback
        if (!evaluation.meetsRequirements) {
          logger.info(`Content creation artifact needs improvement`, {
            sessionId: this.getSessionId(),
            goalId,
            artifactId,
            feedback: evaluation.overallFeedback
          });

          // Generate feedback based on evaluation
          const feedback = {
            overallRating: evaluation.score,
            strengths: Object.entries(evaluation.criteriaEvaluation)
              .filter(([_, evalItem]: [string, any]) => evalItem.met)
              .map(([criterion, _]: [string, any]) => criterion),
            areasForImprovement: Object.entries(evaluation.criteriaEvaluation)
              .filter(([_, evalItem]: [string, any]) => !evalItem.met)
              .map(([criterion, _]: [string, any]) => criterion),
            specificFeedback: Object.entries(evaluation.criteriaEvaluation).map(([criterion, evalItem]: [string, any]) => ({
              section: criterion,
              feedback: evalItem.feedback,
              suggestions: `Improve this aspect to score higher than ${evalItem.score}/100`
            })),
            summary: evaluation.overallFeedback
          };

          // Create a feedback request
          const requestId = await feedbackSystem.requestFeedback(
            'content-creation',
            'seo-optimization',
            artifactId,
            feedback.areasForImprovement
          );

          // Provide feedback
          await feedbackSystem.provideFeedback(
            'seo-optimization',
            'content-creation',
            requestId,
            feedback
          );

          // Incorporate feedback to improve the artifact
          const improvedArtifactId = await feedbackSystem.incorporateFeedback(
            artifactId,
            [requestId],
            'content-creation'
          );

          logger.info(`Improved content creation artifact created: ${improvedArtifactId}`, {
            sessionId: this.getSessionId(),
            goalId,
            originalArtifactId: artifactId,
            improvedArtifactId
          });

          // The improved artifact will be automatically evaluated
          // No need to manually call evaluateArtifact
        }
      } else {
        // If no evaluation found, manually trigger one
        logger.info(`No evaluation found, manually evaluating artifact: ${artifactId}`, {
          sessionId: this.getSessionId(),
          goalId,
          artifactId
        });

        try {
          await this.stateManager.evaluateArtifact(artifactId, goalId);
        } catch (err) {
          const error = err as Error;
          logger.error(`Error manually evaluating content creation artifact`, {
            sessionId: this.getSessionId(),
            goalId,
            artifactId,
            error: error.message || String(err),
            stack: error.stack
          });
        }
      }

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error processing content creation goal`, {
        sessionId: this.getSessionId(),
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Handle goal assignment message
   * @param message Goal assignment message
   * @param state Current state
   * @param stateManager State manager
   * @returns Promise<Message | null> Response message or null
   */
  private async handleGoalAssignment(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    try {
      // Extract goal ID from message content
      let goalId = message.content?.goalId;

      // If no goalId in content, check if there's a goal in the state that needs processing
      if (!goalId && (message.type === MessageType.USER || message.type === MessageType.SYSTEM)) {
        // Find an active goal assigned to this agent
        const activeGoals = Object.values(state.goals.byId).filter(
          goal => goal.status === GoalStatus.ACTIVE && goal.assignedTo === this.agentId
        );

        if (activeGoals.length > 0) {
          goalId = activeGoals[0].id;
        } else {
          // Create a new goal if none exists
          const newGoal = {
            description: `Create content for ${state.topic}`,
            type: GoalType.CONTENT_CREATION,
            dependencies: [],
            criteria: [
              'Write engaging introduction',
              'Develop comprehensive body content',
              'Create compelling conclusion',
              'Include relevant examples and data'
            ]
          };

          const goals = await stateManager.defineGoals([newGoal]);
          goalId = goals[0];
        }
      }

      if (!goalId) {
        throw new Error('Goal ID not provided and no active goals found');
      }

      // Process the goal
      await this.processGoal(goalId);

      // Send acknowledgment
      return this.createMessage(
        message.from,
        MessageType.SYSTEM_MESSAGE,
        {
          goalId,
          status: 'processing',
          message: `Content Creation Agent is processing goal ${goalId}`
        },
        message.conversationId,
        message.id
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling goal assignment`, {
        sessionId: this.getSessionId(),
        error: err.message || String(error),
        stack: err.stack
      });

      // Send error message
      return this.createMessage(
        message.from,
        MessageType.SYSTEM_ERROR,
        {
          error: err.message || String(error),
          originalMessage: message.id
        },
        message.conversationId,
        message.id
      );
    }
  }

  /**
   * Handle artifact request message
   * @param message Artifact request message
   * @param state Current state
   * @param stateManager State manager
   * @returns Promise<Message | null> Response message or null
   */
  private async handleArtifactRequest(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    try {
      // Find the most recent content creation artifact
      const contentArtifacts = Object.values(state.artifacts).filter(
        artifact => artifact.type === 'content-creation'
      );

      if (contentArtifacts.length === 0) {
        // No artifact found, create one
        const contentStrategyArtifacts = Object.values(state.artifacts).filter(
          artifact => artifact.type === 'content-strategy'
        );

        if (contentStrategyArtifacts.length === 0) {
          throw new Error('Content strategy artifact required but not found');
        }

        const newGoal = {
          description: `Create content for ${state.topic}`,
          type: GoalType.CONTENT_CREATION,
          dependencies: [],
          criteria: [
            'Write engaging introduction',
            'Develop comprehensive body content',
            'Create compelling conclusion',
            'Include relevant examples and data'
          ]
        };

        const goals = await stateManager.defineGoals([newGoal]);
        await this.processGoal(goals[0]);

        // Get the artifact we just created
        const updatedState = await stateManager.getState();
        if (!updatedState) {
          throw new Error('Failed to get updated state');
        }

        const newArtifacts = Object.values(updatedState.artifacts).filter(
          artifact => artifact.type === 'content-creation'
        );

        if (newArtifacts.length === 0) {
          throw new Error('Failed to create content artifact');
        }

        // Return the artifact
        return this.createMessage(
          message.from,
          MessageType.ARTIFACT_UPDATE,
          {
            artifactId: newArtifacts[0].id,
            artifactType: 'content-creation',
            content: newArtifacts[0].content,
            title: newArtifacts[0].title
          },
          message.conversationId,
          message.id
        );
      }

      // Get the most recent artifact
      const artifact = contentArtifacts.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      // Return the artifact
      return this.createMessage(
        message.from,
        MessageType.ARTIFACT_UPDATE,
        {
          artifactId: artifact.id,
          artifactType: 'content-creation',
          content: artifact.content,
          title: artifact.title
        },
        message.conversationId,
        message.id
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling artifact request`, {
        sessionId: this.getSessionId(),
        error: err.message || String(error),
        stack: err.stack
      });

      return this.createMessage(
        message.from,
        MessageType.SYSTEM_ERROR,
        {
          error: err.message || String(error),
          originalMessage: message.id
        },
        message.conversationId,
        message.id
      );
    }
  }

  /**
   * Handle feedback message
   * @param message Feedback message
   * @param state Current state
   * @param stateManager State manager
   * @returns Promise<Message | null> Response message or null
   */
  private async handleFeedback(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    try {
      const { feedback, artifactId } = message.content;

      if (!feedback || !artifactId) {
        throw new Error('Missing required fields: feedback, artifactId');
      }

      // Get the artifact
      const artifact = state.artifacts[artifactId];
      if (!artifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }

      // Initialize feedback loop system
      const feedbackSystem = new FeedbackLoopSystem(this.getSessionId());

      // Process the feedback
      await feedbackSystem.provideFeedback(
        'quality-assessment',
        'content-creation',
        'feedback-request-id', // This should be a real request ID
        feedback
      );

      // Check if we need to improve the artifact
      if (feedback.improvementNeeded) {
        // Create a new goal to improve the artifact
        const newGoal = {
          description: `Improve content for ${state.topic} based on feedback`,
          type: GoalType.CONTENT_CREATION,
          dependencies: [],
          criteria: [
            'Address feedback points',
            'Improve content quality',
            'Enhance readability and engagement'
          ]
        };

        const goals = await stateManager.defineGoals([newGoal]);
        await this.processGoal(goals[0]);
      }

      // Send acknowledgment
      return this.createMessage(
        message.from,
        MessageType.SYSTEM_MESSAGE,
        {
          message: `Feedback received and processed for artifact ${artifactId}`,
          status: 'success'
        },
        message.conversationId,
        message.id
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling feedback`, {
        sessionId: this.getSessionId(),
        error: err.message || String(error),
        stack: err.stack
      });

      return this.createMessage(
        message.from,
        MessageType.SYSTEM_ERROR,
        {
          error: err.message || String(error),
          originalMessage: message.id
        },
        message.conversationId,
        message.id
      );
    }
  }

  /**
   * Handle consultation request message
   * @param message Consultation request message
   * @param state Current state
   * @param stateManager State manager
   * @returns Promise<Message | null> Response message or null
   */
  private async handleConsultationRequest(
    message: Message,
    state: CollaborationState,
    _stateManager: StateManager
  ): Promise<Message | null> {
    try {
      const { question } = message.content;

      if (!question) {
        throw new Error('Missing required field: question');
      }

      // Generate a response based on the question and context
      const response = `Content Creation Agent consultation response to: "${question}"\n\n` +
        `Based on our content creation process for ${state.topic}, we can provide the following insights:\n\n` +
        `1. We're creating a ${state.contentType} targeted at ${state.targetAudience}\n` +
        `2. The content follows a structure outlined in the content strategy\n` +
        `3. We're incorporating keywords from the keyword analysis\n` +
        `4. The tone of the content is ${state.tone}`;

      // Send consultation response
      return this.createMessage(
        message.from,
        MessageType.FEEDBACK_RESPONSE,
        {
          response,
          originalQuestion: question
        },
        message.conversationId,
        message.id
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling consultation request`, {
        sessionId: this.getSessionId(),
        error: err.message || String(error),
        stack: err.stack
      });

      return this.createMessage(
        message.from,
        MessageType.SYSTEM_ERROR,
        {
          error: err.message || String(error),
          originalMessage: message.id
        },
        message.conversationId,
        message.id
      );
    }
  }
}
