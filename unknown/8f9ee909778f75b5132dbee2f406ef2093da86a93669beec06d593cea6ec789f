/**
 * Performance Optimizer
 *
 * This utility provides performance optimizations for the goal-based orchestration system,
 * including pagination, caching, and batch processing for large collections.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import { StateManager } from '../state/manager';
import { Message, Artifact } from '../state/unified-schema';

/**
 * Page size for collections
 */
const PAGE_SIZE = 50;

/**
 * Cache TTL in milliseconds
 */
const CACHE_TTL = 60 * 1000; // 1 minute

/**
 * Cache entry
 */
interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

/**
 * Performance Optimizer
 */
export class PerformanceOptimizer {
  private sessionId: string;
  private stateManager: StateManager;
  private cache: Map<string, CacheEntry<any>> = new Map();

  /**
   * Constructor
   * @param sessionId Session ID
   */
  constructor(sessionId: string) {
    this.sessionId = sessionId;
    this.stateManager = new StateManager(sessionId);
  }

  /**
   * Add a message with pagination
   * @param message Message to add
   * @returns Message ID
   */
  public async addMessage(message: Omit<Message, 'id' | 'timestamp'>): Promise<string> {
    try {
      const messageId = uuidv4();
      const now = new Date().toISOString();

      // Create the message
      const newMessage: Message = {
        ...message,
        id: messageId,
        timestamp: now
      };

      // Update state with the message
      await this.stateManager.updateState(currentState => {
        if (!currentState) return currentState;

        // Add message to messages collection
        const messages = { ...currentState.messages };
        messages[messageId] = newMessage;

        // Add to conversation mapping
        const conversations = { ...currentState.conversations };
        if (message.conversationId) {
          if (conversations[message.conversationId]) {
            conversations[message.conversationId] = [...conversations[message.conversationId], messageId];
          } else {
            conversations[message.conversationId] = [messageId];
          }
        }

        // Add to paginated collection
        const messagePages = { ...currentState.messagePages } || {};

        // Get the latest page
        const pageKeys = Object.keys(messagePages).sort((a, b) => parseInt(b) - parseInt(a));
        const latestPageKey = pageKeys.length > 0 ? pageKeys[0] : '1';
        const latestPage = messagePages[latestPageKey] || [];

        // Check if the latest page is full
        if (latestPage.length >= PAGE_SIZE) {
          // Create a new page
          const newPageKey = (parseInt(latestPageKey) + 1).toString();
          messagePages[newPageKey] = [messageId];
        } else {
          // Add to the latest page
          messagePages[latestPageKey] = [...latestPage, messageId];
        }

        return {
          ...currentState,
          messages,
          conversations,
          messagePages
        };
      });

      // Invalidate cache
      this.invalidateCache(`messages`);
      this.invalidateCache(`conversation:${message.conversationId}`);

      return messageId;
    } catch (error) {
      logger.error(`Error adding message with pagination`, {
        sessionId: this.sessionId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Add an artifact with pagination
   * @param artifact Artifact to add
   * @returns Artifact ID
   */
  public async addArtifact(artifact: Omit<Artifact, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const artifactId = uuidv4();
      const now = new Date().toISOString();

      // Create the artifact
      const newArtifact: Artifact = {
        ...artifact,
        id: artifactId,
        createdAt: now,
        updatedAt: now
      };

      // Update state with the artifact
      await this.stateManager.updateState(currentState => {
        if (!currentState) return currentState;

        // Add artifact to artifacts collection
        const artifacts = { ...currentState.artifacts };
        artifacts[artifactId] = newArtifact;

        // Add to generated artifacts list
        const generatedArtifactIds = [...currentState.generatedArtifactIds, artifactId];

        // Add to paginated collection
        const artifactPages = { ...currentState.artifactPages } || {};

        // Get the latest page
        const pageKeys = Object.keys(artifactPages).sort((a, b) => parseInt(b) - parseInt(a));
        const latestPageKey = pageKeys.length > 0 ? pageKeys[0] : '1';
        const latestPage = artifactPages[latestPageKey] || [];

        // Check if the latest page is full
        if (latestPage.length >= PAGE_SIZE) {
          // Create a new page
          const newPageKey = (parseInt(latestPageKey) + 1).toString();
          artifactPages[newPageKey] = [artifactId];
        } else {
          // Add to the latest page
          artifactPages[latestPageKey] = [...latestPage, artifactId];
        }

        return {
          ...currentState,
          artifacts,
          generatedArtifactIds,
          artifactPages
        };
      });

      // Invalidate cache
      this.invalidateCache(`artifacts`);
      this.invalidateCache(`artifactType:${artifact.type}`);

      return artifactId;
    } catch (error) {
      logger.error(`Error adding artifact with pagination`, {
        sessionId: this.sessionId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Get messages by page
   * @param page Page number (1-based)
   * @returns Messages for the page
   */
  public async getMessagesByPage(page: number): Promise<Message[]> {
    try {
      const cacheKey = `messages:page:${page}`;

      // Check cache
      const cachedData = this.getFromCache<Message[]>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      // Get page data
      const pageKey = page.toString();
      const messageIds = state.messagePages?.[pageKey] || [];

      // Get messages
      const messages = messageIds.map(id => state.messages[id]).filter(Boolean);

      // Cache the result
      this.setCache(cacheKey, messages);

      return messages;
    } catch (error) {
      logger.error(`Error getting messages by page`, {
        sessionId: this.sessionId,
        page,
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  /**
   * Get artifacts by page
   * @param page Page number (1-based)
   * @returns Artifacts for the page
   */
  public async getArtifactsByPage(page: number): Promise<Artifact[]> {
    try {
      const cacheKey = `artifacts:page:${page}`;

      // Check cache
      const cachedData = this.getFromCache<Artifact[]>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      // Get page data
      const pageKey = page.toString();
      const artifactIds = state.artifactPages?.[pageKey] || [];

      // Get artifacts
      const artifacts = artifactIds.map(id => state.artifacts[id]).filter(Boolean);

      // Cache the result
      this.setCache(cacheKey, artifacts);

      return artifacts;
    } catch (error) {
      logger.error(`Error getting artifacts by page`, {
        sessionId: this.sessionId,
        page,
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  /**
   * Get messages by conversation
   * @param conversationId Conversation ID
   * @returns Messages for the conversation
   */
  public async getMessagesByConversation(conversationId: string): Promise<Message[]> {
    try {
      const cacheKey = `conversation:${conversationId}`;

      // Check cache
      const cachedData = this.getFromCache<Message[]>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      // Get conversation messages
      const messageIds = state.conversations[conversationId] || [];

      // Get messages
      const messages = messageIds.map(id => state.messages[id]).filter(Boolean);

      // Cache the result
      this.setCache(cacheKey, messages);

      return messages;
    } catch (error) {
      logger.error(`Error getting messages by conversation`, {
        sessionId: this.sessionId,
        conversationId,
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  /**
   * Get artifacts by type
   * @param type Artifact type
   * @returns Artifacts of the specified type
   */
  public async getArtifactsByType(type: string): Promise<Artifact[]> {
    try {
      const cacheKey = `artifactType:${type}`;

      // Check cache
      const cachedData = this.getFromCache<Artifact[]>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      // Get artifacts by type
      const artifacts = Object.values(state.artifacts)
        .filter(artifact => artifact.type === type);

      // Cache the result
      this.setCache(cacheKey, artifacts);

      return artifacts;
    } catch (error) {
      logger.error(`Error getting artifacts by type`, {
        sessionId: this.sessionId,
        type,
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  /**
   * Perform batch updates
   * @param updateFn Function to update the state
   * @returns Success indicator
   */
  public async batchUpdate(updateFn: (state: any) => any): Promise<boolean> {
    try {
      // Update state in a single transaction
      await this.stateManager.updateState(updateFn);

      // Clear all cache
      this.clearCache();

      return true;
    } catch (error) {
      logger.error(`Error performing batch update`, {
        sessionId: this.sessionId,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Get data from cache
   * @param key Cache key
   * @returns Cached data or null if not found or expired
   */
  private getFromCache<T>(key: string): T | null {
    const entry = this.cache.get(key);

    if (!entry) {
      return null;
    }

    // Check if expired
    if (Date.now() - entry.timestamp > CACHE_TTL) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  /**
   * Set data in cache
   * @param key Cache key
   * @param data Data to cache
   */
  private setCache<T>(key: string, data: T): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Invalidate cache entry
   * @param key Cache key
   */
  private invalidateCache(key: string): void {
    // Delete exact match
    this.cache.delete(key);

    // Delete entries that start with the key (for prefix matches)
    for (const cacheKey of this.cache.keys()) {
      if (cacheKey.startsWith(`${key}:`)) {
        this.cache.delete(cacheKey);
      }
    }
  }

  /**
   * Clear all cache
   */
  private clearCache(): void {
    this.cache.clear();
  }
}
