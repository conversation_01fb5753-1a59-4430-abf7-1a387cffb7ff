'use client';

import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Chip,
  LinearProgress,
  Button,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import PauseIcon from '@mui/icons-material/Pause';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

interface WorkflowMonitorProps {
  sessionId: string;
  status: string;
  currentPhase?: string;
  progress?: number;
  estimatedTimeRemaining?: string;
  error?: string | null;
  onRefresh?: () => void;
  onPause?: () => void;
  onResume?: () => void;
  loading?: boolean;
  compact?: boolean;
}

/**
 * Component to monitor workflow status and provide controls
 * Can be used in compact mode for embedding in other components
 */
const WorkflowMonitor: React.FC<WorkflowMonitorProps> = ({
  sessionId,
  status,
  currentPhase,
  progress = 0,
  estimatedTimeRemaining,
  error,
  onRefresh,
  onPause,
  onResume,
  loading = false,
  compact = false
}) => {
  // Format phase name for display
  const formatPhaseName = (phase?: string): string => {
    if (!phase) return 'Initializing';
    
    return phase.charAt(0).toUpperCase() + phase.slice(1);
  };
  
  // Get color for status
  const getStatusColor = (status: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (status) {
      case 'active':
        return 'primary';
      case 'completed':
        return 'success';
      case 'paused':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  // Compact view for embedding in other components
  if (compact) {
    return (
      <Box sx={{ mb: 2 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 1 }}>
            {error}
          </Alert>
        )}
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Chip 
              size="small" 
              label={formatPhaseName(currentPhase)} 
              color="primary" 
              sx={{ mr: 1 }}
            />
            <Chip 
              size="small" 
              label={status} 
              color={getStatusColor(status)}
            />
          </Box>
          
          <Box>
            {status === 'active' && onPause && (
              <Tooltip title="Pause Workflow">
                <IconButton 
                  size="small" 
                  onClick={onPause}
                  disabled={loading}
                  sx={{ mr: 0.5 }}
                >
                  <PauseIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
            
            {status === 'paused' && onResume && (
              <Tooltip title="Resume Workflow">
                <IconButton 
                  size="small" 
                  onClick={onResume}
                  disabled={loading}
                  sx={{ mr: 0.5 }}
                >
                  <PlayArrowIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
            
            {onRefresh && (
              <Tooltip title="Refresh Status">
                <IconButton 
                  size="small" 
                  onClick={onRefresh}
                  disabled={loading}
                >
                  <RefreshIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
          <Box sx={{ flexGrow: 1, mr: 1 }}>
            <LinearProgress 
              variant="determinate" 
              value={progress} 
              sx={{ height: 6, borderRadius: 3 }}
            />
          </Box>
          <Typography variant="caption" fontWeight="medium">
            {progress}%
          </Typography>
        </Box>
        
        {estimatedTimeRemaining && (
          <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
            <AccessTimeIcon fontSize="inherit" sx={{ mr: 0.5 }} />
            Est. time remaining: {estimatedTimeRemaining}
          </Typography>
        )}
      </Box>
    );
  }

  // Full view
  return (
    <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Workflow Status
        </Typography>
        
        <Box>
          {status === 'active' && onPause && (
            <Button
              variant="outlined"
              startIcon={<PauseIcon />}
              onClick={onPause}
              disabled={loading}
              size="small"
              sx={{ mr: 1 }}
            >
              Pause
            </Button>
          )}
          
          {status === 'paused' && onResume && (
            <Button
              variant="contained"
              startIcon={<PlayArrowIcon />}
              onClick={onResume}
              disabled={loading}
              size="small"
              sx={{ mr: 1 }}
            >
              Resume
            </Button>
          )}
          
          {onRefresh && (
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={onRefresh}
              disabled={loading}
              size="small"
            >
              Refresh
            </Button>
          )}
        </Box>
      </Box>
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Session ID
        </Typography>
        <Typography variant="body1" fontWeight="medium">
          {sessionId}
        </Typography>
      </Box>
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Box>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Status
          </Typography>
          <Chip 
            label={status} 
            color={getStatusColor(status)}
          />
        </Box>
        
        <Box>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Current Phase
          </Typography>
          <Chip 
            label={formatPhaseName(currentPhase)} 
            color="primary"
          />
        </Box>
      </Box>
      
      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Progress
          </Typography>
          <Typography variant="body2" fontWeight="medium">
            {progress}%
          </Typography>
        </Box>
        <LinearProgress 
          variant="determinate" 
          value={progress} 
          sx={{ height: 8, borderRadius: 4 }}
        />
      </Box>
      
      {estimatedTimeRemaining && (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <AccessTimeIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
          <Typography variant="body2" color="text.secondary">
            Estimated time remaining: {estimatedTimeRemaining}
          </Typography>
        </Box>
      )}
      
      {loading && (
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 2 }}>
          <CircularProgress size={20} sx={{ mr: 1 }} />
          <Typography variant="body2" color="text.secondary">
            Updating...
          </Typography>
        </Box>
      )}
    </Paper>
  );
};

export default WorkflowMonitor;
