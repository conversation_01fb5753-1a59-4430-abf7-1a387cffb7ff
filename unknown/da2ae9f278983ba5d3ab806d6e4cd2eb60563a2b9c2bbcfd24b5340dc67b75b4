// src/components/EnhancedCollaboration/EnhancedArtifactGallery.tsx

import React, { useState, useEffect, useMemo } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Card, 
  CardContent, 
  CardActions,
  Button, 
  Divider, 
  Chip,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tabs,
  Tab,
  CircularProgress,
  Alert
} from '@mui/material';
import StarIcon from '@mui/icons-material/Star';
import HistoryIcon from '@mui/icons-material/History';
import DownloadIcon from '@mui/icons-material/Download';
import FeedbackIcon from '@mui/icons-material/Feedback';
import CloseIcon from '@mui/icons-material/Close';
import RefreshIcon from '@mui/icons-material/Refresh';

// Define the iteration structure based on the Redis data
interface Iteration {
  version: number;
  timestamp: string;
  agent: string;
  content: string | Record<string, any>;
  feedback?: Array<{
    id?: string;
    from: string;
    timestamp: string;
    content: string;
    rating?: number;
  }>;
  incorporatedConsultations?: string[];
  reasoning?: {
    process?: string;
    steps?: string[];
    thoughts?: string[];
    considerations?: string[];
    decision?: string;
    confidence?: number;
    agentId?: string;
    timestamp?: string;
    conclusion?: string;
    supportingEvidence?: string[];
    insights?: string[];
  };
  changes?: string;
}

// Define the artifact structure based on the Redis data
interface Artifact {
  id: string;
  name: string;
  type: string;
  createdBy: string;
  createdAt: string;
  updatedAt?: string;
  currentVersion: number;
  iterations: Iteration[];
  status: string;
  qualityScore: number;
  content?: string | Record<string, any>;
  metadata?: Record<string, any>;
  data?: any;
  agent?: string;
  title?: string;
  creator?: string;
  timestamp?: string;
  created?: string;
  date?: string;
  text?: string;
  reasoningSteps?: any[];
}

interface EnhancedArtifactGalleryProps {
  artifacts: any; // Accept any type as input to handle different structures
  onSendFeedback?: (artifactId: string, feedback: string) => void;
  refreshContent?: () => void;
  loading?: boolean;
}

const EnhancedArtifactGallery = ({ 
  artifacts,
  onSendFeedback,
  refreshContent,
  loading = false
}: EnhancedArtifactGalleryProps): React.ReactElement => {
  const [selectedArtifact, setSelectedArtifact] = useState<Artifact | null>(null);
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);
  const [feedbackContent, setFeedbackContent] = useState<string>('');
  const [historyDialogOpen, setHistoryDialogOpen] = useState(false);
  const [activeHistoryTab, setActiveHistoryTab] = useState<number>(0);
  const [normalizedArtifacts, setNormalizedArtifacts] = useState<Artifact[]>([]);
  
  // Define normalization function using useMemo to avoid recreation on each render
  const normalizeArtifacts = useMemo(() => {  
    return (artifactsInput: any): Artifact[] => {
    if (!artifactsInput) {
      return [];
    }
    
    console.log('Normalizing artifacts:', typeof artifactsInput);
    let extractedArtifacts: Artifact[] = [];
    
    try {
      // PRIORITY CASE: Handle the Redis structure (object with artifact IDs as keys)
      // This is the primary data structure as shown in the Redis session data
      if (artifactsInput && typeof artifactsInput === 'object' && !Array.isArray(artifactsInput)) {
        // Check if it's the direct artifacts object structure from Redis
        const hasValidArtifacts = Object.values(artifactsInput).some(v => 
          typeof v === 'object' && v !== null && 
          ('type' in (v as any) || 'createdBy' in (v as any) || 'iterations' in (v as any))
        );
        
        if (hasValidArtifacts) {
          console.log('Processing Redis-style artifacts object with ID keys');
          const artifacts = Object.entries(artifactsInput).map(([id, data]: [string, any]) => {
            if (!data) return null;
            
            // Create artifact from the Redis data structure
            return {
              id: data.id || id,
              name: data.name || data.title || 'Unnamed Artifact',
              type: data.type || 'unknown',
              createdBy: data.createdBy || data.creator || 'Unknown',
              createdAt: data.createdAt || data.timestamp || new Date().toISOString(),
              status: data.status || 'draft',
              content: data.content || '',
              currentVersion: data.currentVersion || (data.iterations?.length || 1),
              qualityScore: typeof data.qualityScore === 'number' ? data.qualityScore : 
                           (typeof data.qualityScore === 'string' ? parseFloat(data.qualityScore) : 0),
              iterations: Array.isArray(data.iterations) ? data.iterations : [],
              metadata: data.metadata || {}
            } as Artifact;
          }).filter(Boolean) as Artifact[];
          
          if (artifacts.length > 0) {
            return artifacts.sort((a, b) => {
              const dateA = new Date(a.createdAt).getTime();
              const dateB = new Date(b.createdAt).getTime();
              return dateB - dateA; // newest first
            });
          }
        }
      }
      
      // Check if it's already an array of artifacts
      if (Array.isArray(artifactsInput)) {
        console.log('Processing array of artifacts');
        extractedArtifacts = artifactsInput.map(item => {
          if (typeof item !== 'object') return null;
          
          // Ensure each item has the required fields
          return {
            id: item.id || Math.random().toString(36).substring(7),
            name: item.name || item.title || 'Unnamed Artifact',
            type: item.type || 'unknown',
            createdBy: item.createdBy || item.creator || 'Unknown',
            createdAt: item.createdAt || item.timestamp || new Date().toISOString(),
            status: item.status || 'draft',
            content: item.content || '',
            currentVersion: item.currentVersion || (item.iterations?.length > 0 ? item.iterations.length : 1) || 1,
            qualityScore: typeof item.qualityScore === 'number' ? item.qualityScore : 0,
            iterations: Array.isArray(item.iterations) ? item.iterations : [],
            metadata: item.metadata || {}
          } as Artifact;
        }).filter(Boolean) as Artifact[];
      }
      // If it's an object with potentially nested artifacts (common in state objects)
      else if (artifactsInput && typeof artifactsInput === 'object') {
        console.log('Processing object containing artifacts');
        
        // NEXT: Look for explicit artifacts field, common in the Redis structure
        if ('artifacts' in artifactsInput && artifactsInput.artifacts) {
          console.log('Found artifacts property, processing it directly');
          return normalizeArtifacts(artifactsInput.artifacts);
        }
        
        // Case 1: Direct mapping where keys are artifact IDs (already handled above)
        // This is just a fallback in case the first check missed something
        if (Object.values(artifactsInput).every(v => typeof v === 'object' && v !== null)) {
          const artifacts = Object.entries(artifactsInput).map(([id, data]: [string, any]) => {
            if (!data) return null;
            
            return {
              id: id,
              name: data.name || data.title || 'Unnamed Artifact',
              type: data.type || 'unknown',
              createdBy: data.createdBy || data.creator || 'Unknown',
              createdAt: data.createdAt || data.timestamp || new Date().toISOString(),
              status: data.status || 'draft',
              content: data.content || '',
              currentVersion: data.currentVersion || (data.iterations?.length || 1),
              qualityScore: typeof data.qualityScore === 'number' ? data.qualityScore : 0,
              iterations: Array.isArray(data.iterations) ? data.iterations : [],
              metadata: data.metadata || {}
            } as Artifact;
          }).filter(Boolean) as Artifact[];
          
          if (artifacts.length > 0) {
            extractedArtifacts = artifacts;
          }
        }
        
        // Case 2: Check for agent states as in the Redis structure
        if (extractedArtifacts.length === 0 && 'agentStates' in artifactsInput) {
          console.log('Looking for artifacts in agentStates');
          const agentStates = artifactsInput.agentStates;
          
          if (agentStates && typeof agentStates === 'object') {
            // Check each agent's generatedArtifacts list
            for (const agentId in agentStates) {
              const agent = agentStates[agentId];
              if (agent && typeof agent === 'object' && Array.isArray(agent.generatedArtifacts)) {
                console.log(`Agent ${agentId} has ${agent.generatedArtifacts.length} generated artifacts`);                
                // These are usually IDs, so we need to find the actual artifacts
                if (artifactsInput.artifacts) {
                  for (const artifactId of agent.generatedArtifacts) {
                    if (artifactsInput.artifacts[artifactId]) {
                      const artifactData = artifactsInput.artifacts[artifactId];
                      extractedArtifacts.push({
                        id: artifactId,
                        name: artifactData.name || artifactData.title || 'Unnamed Artifact',
                        type: artifactData.type || 'unknown',
                        createdBy: artifactData.createdBy || artifactData.creator || agentId || 'Unknown',
                        createdAt: artifactData.createdAt || artifactData.timestamp || new Date().toISOString(),
                        status: artifactData.status || 'draft',
                        content: artifactData.content || '',
                        currentVersion: artifactData.currentVersion || (artifactData.iterations?.length || 1),
                        qualityScore: typeof artifactData.qualityScore === 'number' ? 
                                     artifactData.qualityScore : 
                                     (typeof artifactData.qualityScore === 'string' ? 
                                      parseFloat(artifactData.qualityScore) : 0),
                        iterations: Array.isArray(artifactData.iterations) ? artifactData.iterations : [],
                        metadata: artifactData.metadata || {}
                      } as Artifact);
                    }
                  }
                }
              }
            }
          }
        }
        
        // Case 3: Check for messages that might contain artifacts
        if (extractedArtifacts.length === 0 && 'messages' in artifactsInput) {
          console.log('Looking for artifacts in messages');
          const messages = artifactsInput.messages;
          
          if (Array.isArray(messages)) {
            // Look for ARTIFACT_DELIVERY messages which contain artifact data
            const artifactMessages = messages.filter(m => 
              m && m.type === 'ARTIFACT_DELIVERY' && m.content && m.content.artifact
            );
            
            console.log(`Found ${artifactMessages.length} artifact delivery messages`);
            
            for (const message of artifactMessages) {
              if (message.content && message.content.artifact) {
                const artifactData = message.content.artifact;
                extractedArtifacts.push({
                  id: artifactData.id || message.content.artifactId || Math.random().toString(36).substring(7),
                  name: artifactData.name || artifactData.title || 'Unnamed Artifact',
                  type: artifactData.type || message.content.artifactType || 'unknown',
                  createdBy: artifactData.createdBy || artifactData.creator || message.from || 'Unknown',
                  createdAt: artifactData.createdAt || artifactData.timestamp || message.timestamp || new Date().toISOString(),
                  status: artifactData.status || 'draft',
                  content: artifactData.content || '',
                  currentVersion: artifactData.currentVersion || (artifactData.iterations?.length || 1),
                  qualityScore: typeof artifactData.qualityScore === 'number' ? artifactData.qualityScore : 0,
                  iterations: Array.isArray(artifactData.iterations) ? artifactData.iterations : [],
                  metadata: artifactData.metadata || {}
                } as Artifact);
              }
            }
          }
        }
        
        // Case 4: As a last resort, check all fields for anything that might be an artifact
        if (extractedArtifacts.length === 0) {
          // Look for fields that might contain artifacts
          const artifactFields = [
            'output', 'result', 'results', 'generatedContent', 'items', 'documents'
          ];
          
          for (const field of artifactFields) {
            if (artifactsInput[field]) {
              const value = artifactsInput[field];
              // If it's an array, try to process it recursively
              if (Array.isArray(value) && value.length > 0) {
                // Create a recursive call using a separate normalization function to avoid circular reference
                const processArrayRecursively = (items: any[]): Artifact[] => {
                  return items.filter(item => item && typeof item === 'object')
                    .map(item => ({
                      id: item.id || Math.random().toString(36).substring(7),
                      name: item.name || item.title || 'Unnamed Artifact',
                      type: item.type || 'unknown',
                      createdBy: item.createdBy || item.creator || 'Unknown',
                      createdAt: item.createdAt || item.timestamp || new Date().toISOString(),
                      status: item.status || 'draft',
                      content: item.content || '',
                      currentVersion: item.currentVersion || 1,
                      qualityScore: typeof item.qualityScore === 'number' ? item.qualityScore : 0,
                      iterations: Array.isArray(item.iterations) ? item.iterations : [],
                      metadata: item.metadata || {}
                    } as Artifact));
                };
                
                const potentialArtifacts = processArrayRecursively(value);
                if (potentialArtifacts.length > 0) {
                  extractedArtifacts = [...extractedArtifacts, ...potentialArtifacts];
                  break;
                }
              }
              // If it's an object, try to process it recursively
              else if (typeof value === 'object' && value !== null) {
                // Process the object as a potential artifact container
                if ('id' in value || 'name' in value || 'type' in value) {
                  // This looks like a single artifact
                  extractedArtifacts.push({
                    id: value.id || Math.random().toString(36).substring(7),
                    name: value.name || value.title || 'Unnamed Artifact',
                    type: value.type || 'unknown',
                    createdBy: value.createdBy || value.creator || 'Unknown',
                    createdAt: value.createdAt || value.timestamp || new Date().toISOString(),
                    status: value.status || 'draft',
                    content: value.content || '',
                    currentVersion: value.currentVersion || 1,
                    qualityScore: typeof value.qualityScore === 'number' ? value.qualityScore : 0,
                    iterations: Array.isArray(value.iterations) ? value.iterations : [],
                    metadata: value.metadata || {}
                  } as Artifact);
                } else {
                  // Check if it's a container of artifacts
                  const containerCheck = Object.values(value).find(v => v && typeof v === 'object');
                  if (containerCheck) {
                    // Process each potential artifact in the container
                    const artifactsFromContainer = Object.entries(value)
                      .filter(([_, v]) => v && typeof v === 'object')
                      .map(([id, item]: [string, any]) => ({
                        id: item.id || id,
                        name: item.name || item.title || 'Unnamed Artifact',
                        type: item.type || 'unknown',
                        createdBy: item.createdBy || item.creator || 'Unknown',
                        createdAt: item.createdAt || item.timestamp || new Date().toISOString(),
                        status: item.status || 'draft',
                        content: item.content || '',
                        currentVersion: item.currentVersion || 1,
                        qualityScore: typeof item.qualityScore === 'number' ? item.qualityScore : 0,
                        iterations: Array.isArray(item.iterations) ? item.iterations : [],
                        metadata: item.metadata || {}
                      }));
                    
                    if (artifactsFromContainer.length > 0) {
                      extractedArtifacts = [...extractedArtifacts, ...artifactsFromContainer as Artifact[]];
                    }
                  }
                }
                break;
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Error normalizing artifacts:', error);
      return [];
    }
    
    // Sort artifacts by creation date, newest first
    return extractedArtifacts
      .filter(art => art && art.name && art.type) // Ensure only valid artifacts
      .sort((a, b) => {
        const dateA = new Date(a.createdAt).getTime();
        const dateB = new Date(b.createdAt).getTime();
        return dateB - dateA; // newest first
      });
    };
  }, []);
  
  // Normalize artifacts to our internal format
  const normalizedArtifactsMemo = useMemo(() => {
    return normalizeArtifacts(artifacts) || [];
  }, [artifacts, normalizeArtifacts]);

  useEffect(() => {
    setNormalizedArtifacts(normalizeArtifacts(artifacts) || []);
  }, [artifacts, normalizeArtifacts]);

  // Helper functions for the component
  const handleOpenArtifact = (artifact: Artifact): void => {
    setSelectedArtifact(artifact);
  };
  
  const handleCloseArtifact = (): void => {
    setSelectedArtifact(null);
  };
  
  const handleOpenFeedbackDialog = (): void => {
    setFeedbackDialogOpen(true);
  };
  
  const handleCloseFeedbackDialog = (): void => {
    setFeedbackDialogOpen(false);
    setFeedbackContent('');
  };
  
  const handleSubmitFeedback = (): void => {
    if (selectedArtifact && feedbackContent.trim() && onSendFeedback) {
      onSendFeedback(selectedArtifact.id, feedbackContent);
      handleCloseFeedbackDialog();
    }
  };
  
  const handleOpenHistoryDialog = (): void => {
    setHistoryDialogOpen(true);
  };
  
  const handleCloseHistoryDialog = (): void => {
    setHistoryDialogOpen(false);
    setActiveHistoryTab(0);
  };
  
  const handleHistoryTabChange = (event: React.SyntheticEvent, newValue: number): void => {
    setActiveHistoryTab(newValue);
  };
  
  const formatTimestamp = (timestamp: string): string => {
    try {
      return new Date(timestamp).toLocaleString();
    } catch (e) {
      return timestamp;
    }
  };
  
  // Download artifact as JSON
  const downloadArtifact = (artifact: Artifact): void => {
    const data = JSON.stringify(artifact, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${artifact.name.replace(/\s+/g, '-').toLowerCase()}-v${artifact.currentVersion}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  
  // Helper function to capitalize first letter
  const capitalizeFirstLetter = (string: string): string => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  // Render the history dialog
  const renderHistoryDialog = (): React.ReactNode => {
    if (!selectedArtifact) return null;
    
    return (
      <Dialog 
        open={historyDialogOpen} 
        onClose={handleCloseHistoryDialog} 
        maxWidth="md" 
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Version History: {selectedArtifact.name}</Typography>
            <IconButton onClick={handleCloseHistoryDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        
        <DialogContent>
          {selectedArtifact.iterations.length === 0 ? (
            <Typography variant="body2" color="text.secondary">
              No history available for this artifact.
            </Typography>
          ) : (
            <>
              <Tabs 
                value={activeHistoryTab} 
                onChange={handleHistoryTabChange} 
                variant="scrollable" 
                scrollButtons="auto"
              >
                {selectedArtifact.iterations.map((iteration, index) => (
                  <Tab 
                    key={index} 
                    label={`v${iteration.version}`} 
                  />
                ))}
              </Tabs>
              
              <Box sx={{ mt: 2 }}>
                {selectedArtifact.iterations.map((iteration, index) => (
                  <Box 
                    key={index} 
                    sx={{ 
                      display: activeHistoryTab === index ? 'block' : 'none',
                      mt: 2
                    }}
                  >
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2">
                        Version {iteration.version}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" display="block">
                        {formatTimestamp(iteration.timestamp)} by {iteration.agent}
                      </Typography>
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        {iteration.changes || 'Content updated'}
                      </Typography>
                    </Box>
                    
                    <Divider sx={{ my: 2 }} />
                    
                    <Box sx={{ mt: 3 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Content
                      </Typography>
                      <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                        <Box component="pre" sx={{ margin: 0, whiteSpace: 'pre-wrap', fontFamily: 'monospace', fontSize: '0.875rem' }}>
                          {(() => {
                            const content = iteration.content;
                            if (typeof content === 'string') {
                              return content;
                            } else if (typeof content === 'object' && content !== null) {
                              return JSON.stringify(content, null, 2);
                            }
                            return 'No content available';
                          })()}
                        </Box>
                      </Paper>
                    </Box>
                    
                    {iteration.reasoning && (
                      <Box sx={{ mt: 3 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Reasoning
                        </Typography>
                        <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                          <Box component="pre" sx={{ margin: 0, whiteSpace: 'pre-wrap', fontFamily: 'monospace', fontSize: '0.875rem' }}>
                            {(() => {
                              const reasoning = iteration.reasoning;
                              if (typeof reasoning === 'string') {
                                return reasoning;
                              } else if (typeof reasoning === 'object' && reasoning !== null) {
                                return JSON.stringify(reasoning, null, 2);
                              }
                              return 'No reasoning available';
                            })()}
                          </Box>
                        </Paper>
                      </Box>
                    )}
                  </Box>
                ))}
              </Box>
            </>
          )}
        </DialogContent>
            </Box>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

  // Render the feedback dialog
  const renderFeedbackDialog = (): React.ReactNode => {
    if (!selectedArtifact) return null;
    
    return (
      <Dialog
        open={feedbackDialogOpen}
        onClose={handleCloseFeedbackDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Provide Feedback</Typography>
            <IconButton onClick={handleCloseFeedbackDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Your Feedback"
            fullWidth
            multiline
            rows={4}
            value={feedbackContent}
            onChange={(e) => setFeedbackContent(e.target.value)}
            variant="outlined"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseFeedbackDialog}>Cancel</Button>
          <Button 
            onClick={handleSubmitFeedback} 
            variant="contained" 
            color="primary"
            disabled={!feedbackContent.trim()}
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Render the artifact detail dialog
  const renderArtifactDetail = (): React.ReactNode => {
    if (!selectedArtifact) return null;
    
    return (
      <Dialog 
        open={selectedArtifact !== null} 
        onClose={handleCloseArtifact}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">{selectedArtifact.name}</Typography>
            <IconButton onClick={handleCloseArtifact}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(12, 1fr)', gap: 2 }}>
            <Box sx={{ gridColumn: 'span 12' }}>
              <Typography variant="subtitle1" gutterBottom>Description</Typography>
              <Typography variant="body1" paragraph>
                {typeof selectedArtifact.content === 'string' 
                  ? selectedArtifact.content 
                  : selectedArtifact.metadata?.description || `${selectedArtifact.type} artifact`}
              </Typography>
            </Box>
            
            <Box sx={{ gridColumn: { xs: 'span 12', sm: 'span 6' } }}>
              <Typography variant="subtitle1" gutterBottom>Metadata</Typography>
              <Typography variant="body2">
                <strong>Type:</strong> {selectedArtifact.type}
              </Typography>
              <Typography variant="body2">
                <strong>Status:</strong> {selectedArtifact.status}
              </Typography>
              <Typography variant="body2">
                <strong>Version:</strong> {selectedArtifact.currentVersion}
              </Typography>
              <Typography variant="body2">
                <strong>Quality Score:</strong> {Math.round(selectedArtifact.qualityScore * 100)}%
              </Typography>
              <Typography variant="body2">
                <strong>Created by:</strong> {selectedArtifact.createdBy}
              </Typography>
              <Typography variant="body2">
                <strong>Created at:</strong> {new Date(selectedArtifact.createdAt).toLocaleString()}
              </Typography>
              {selectedArtifact.updatedAt && (
                <Typography variant="body2">
                  <strong>Updated at:</strong> {new Date(selectedArtifact.updatedAt).toLocaleString()}
                </Typography>
              )}
            </Box>
            
            <Box sx={{ gridColumn: { xs: 'span 12', sm: 'span 6' } }}>
              <Typography variant="subtitle1" gutterBottom>Contributors</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                <Chip 
                  label={selectedArtifact.createdBy} 
                  size="small" 
                  color="info" 
                />
                {selectedArtifact.iterations
                  .map(iteration => iteration.agent)
                  .filter((agent, index, self) => self.indexOf(agent) === index && agent !== selectedArtifact.createdBy)
                  .map((contributor, index) => (
                    <Chip 
                      key={index}
                      label={contributor} 
                      size="small" 
                      color="info" 
                    />
                  ))}
              </Box>
            </Box>
          </Box>
        </DialogContent>
        
        <DialogActions>
          <Button 
            startIcon={<HistoryIcon />} 
            onClick={handleOpenHistoryDialog}
          >
            History
          </Button>
          {onSendFeedback && (
            <Button 
              startIcon={<FeedbackIcon />} 
              onClick={handleOpenFeedbackDialog}
            >
              Add Feedback
            </Button>
          )}
          <Button 
            startIcon={<DownloadIcon />} 
            onClick={() => downloadArtifact(selectedArtifact)}
          >
            Download
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Group artifacts by type
  const groupedArtifacts: Record<string, Artifact[]> = {};
  normalizedArtifacts.forEach((artifact: Artifact) => {
  if (!artifact.type) return; // Skip artifacts without a type
  
  if (!groupedArtifacts[artifact.type]) {
    groupedArtifacts[artifact.type] = [];
  }
  groupedArtifacts[artifact.type].push(artifact);
});

// Add console log for debugging
console.log('EnhancedArtifactGallery: Displaying artifacts:', normalizedArtifacts);

return (
  <Box sx={{ p: 2 }}>
    {loading ? (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
        <CircularProgress />
        <Typography variant="body1" color="text.secondary" sx={{ ml: 2 }}>
          Loading artifacts...
        </Typography>
      </Box>
    ) : normalizedArtifacts.length === 0 ? (
      <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
        <Typography variant="body1" color="text.secondary">
          No artifacts available yet. They will appear here as agents create them.
        </Typography>
        {refreshContent && (
          <Button 
            startIcon={<RefreshIcon />} 
            onClick={refreshContent} 
            sx={{ mt: 2 }}
            variant="outlined"
          >
            Refresh
          </Button>
        )}
      </Box>
    ) : (
      <>
        {Object.entries(groupedArtifacts).map(([artifactType, typeArtifacts]) => (
          <Box key={artifactType} sx={{ mb: 4 }}>
            <Typography variant="h5" sx={{ mb: 2 }}>
              {capitalizeFirstLetter(artifactType.replace(/-/g, ' '))}
            </Typography>
            
            <Box sx={{ display: 'grid', gridTemplateColumns: { xs: 'repeat(1, 1fr)', sm: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)' }, gap: 2 }}>
              {typeArtifacts.map((artifact: Artifact) => (
                <Card key={artifact.id || Math.random().toString(36).substring(7)} variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" noWrap>
                      {artifact.name || artifact.title || `Unnamed ${artifactType}`}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1, color: 'text.secondary' }}>
                      {Array.isArray(artifact.iterations) && artifact.iterations.length > 0 ? 
                       `Version ${artifact.currentVersion || 1}/${artifact.iterations.length}` : 
                       `Version ${artifact.currentVersion || 1}`}
                    </Typography>
                    
                    {Array.isArray(artifact.iterations) && artifact.iterations.length > 0 ? (
                      <Box sx={{ mt: 1 }}>
                        {(() => {
                          try {
                            const latestIteration = artifact.iterations[artifact.currentVersion - 1] || artifact.iterations[artifact.iterations.length - 1];
                            let content = '';
                            
                            if (latestIteration && typeof latestIteration.content === 'string') {
                              content = latestIteration.content.substring(0, 150);
                              if (latestIteration.content.length > 150) content += '...';
                            } else if (latestIteration && latestIteration.content) {
                              try {
                                const stringified = JSON.stringify(latestIteration.content);
                                content = stringified.substring(0, 150);
                                if (stringified.length > 150) content += '...';
                              } catch (e) {
                                content = 'Content cannot be displayed';
                              }
                            } else {
                              content = 'No content available';
                            }
                            
                            return <Typography sx={{ mb: 1 }}>{content}</Typography>;
                          } catch (error) {
                            console.error('Error displaying artifact content:', error);
                            return <Typography sx={{ mb: 1 }}>Error displaying content</Typography>;
                          }
                        })()}
                      </Box>
                    ) : typeof artifact.content === 'string' ? (
                      <Typography>
                        {artifact.content.substring(0, 150)}
                        {artifact.content.length > 150 && '...'}
                      </Typography>
                    ) : (
                      <Typography>No content available</Typography>
                    )}

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
                      <Chip 
                        size="small" 
                        label={artifact.status || 'Draft'} 
                        color={
                          artifact.status === 'completed' ? 'success' : 
                          artifact.status === 'in-progress' ? 'info' : 
                          artifact.status === 'draft' ? 'default' : 'warning'
                        }
                      />
                      <Typography variant="body2" color="text.secondary" noWrap>
                        {artifact.createdAt ? formatTimestamp(artifact.createdAt) : 
                         artifact.timestamp ? formatTimestamp(artifact.timestamp) : 
                         artifact.created ? formatTimestamp(artifact.created) : 'Unknown date'}
                      </Typography>
                    </Box>
                    
                    {artifact.qualityScore !== undefined && artifact.qualityScore !== null && (
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                        <StarIcon sx={{ color: 'gold', mr: 0.5, fontSize: '1rem' }} />
                        <Typography variant="body2">
                          {typeof artifact.qualityScore === 'number' 
                            ? `${(artifact.qualityScore * 100).toFixed(0)}%` 
                            : String(artifact.qualityScore)}
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                  <CardActions>
                    <Button 
                      size="small" 
                      onClick={() => {
                        handleOpenArtifact(artifact);
                      }}
                    >
                      View
                    </Button>
                    <Button
                      size="small"
                      startIcon={<DownloadIcon />}
                      onClick={() => {
                        downloadArtifact(artifact);
                      }}
                    >
                      Download
                    </Button>
                  </CardActions>
                </Card>
              ))}
                ))}
              </Box>
            </Box>
          ))}
          
          {/* Render dialogs */}
          {renderArtifactDetail()}
          {renderHistoryDialog()}
          {renderFeedbackDialog()}
        </>
      )}
    </Box>
  );
};

// Export the component
export default EnhancedArtifactGallery;
