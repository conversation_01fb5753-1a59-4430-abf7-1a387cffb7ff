// src/app/(payload)/api/agents/content-generation/iterative/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { handleIterativeMessage } from '../../collaborative-iteration/content-generation-agent';
import { IterativeMessage, IterativeCollaborationState } from '../../collaborative-iteration/types';

/**
 * API route handler for iterative agent collaboration
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("Content Generation Agent received iterative POST request:", JSON.stringify(body));
    
    // Extract the message and state from request body
    const { message, state } = body;
    
    if (!message || !state) {
      return NextResponse.json(
        { error: 'Message and state are required' },
        { status: 400 }
      );
    }
    
    // Handle the iterative message
    const { response, stateUpdates } = await handleIterativeMessage(message, state);
    
    return NextResponse.json({
      response,
      stateUpdates
    });
  } catch (error) {
    console.error("Error in Content Generation Agent iterative handler:", error);
    
    // Create error response
    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'content-generation',
      to: 'system',
      type: 'SYSTEM_MESSAGE',
      content: `Error processing message: ${(error as Error).message}`,
      conversationId: uuidv4(),
      reasoning: {
        thoughts: ['An error occurred while processing the message'],
        considerations: ['The error might be due to invalid input or internal processing'],
        decision: 'Return error message with details',
        confidence: 1.0
      }
    };
    
    return NextResponse.json(
      { response: errorResponse, error: (error as Error).message },
      { status: 500 }
    );
  }
}

/**
 * API route handler for agent capabilities
 */
export async function GET(request: NextRequest) {
  return NextResponse.json({ 
    agent: "Content Generation Agent",
    status: "active",
    capabilities: [
      "content-drafting", 
      "content-revision", 
      "tone-adaptation"
    ],
    iterativeProtocol: true,
    reasoningEnabled: true,
    artifactCreation: true,
    version: "1.0.0"
  });
}
