'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Tabs,
  Tab,
  Button,
  Alert,
  <PERSON>nackbar,
  CircularProgress,
  Divider,
  IconButton,
  Tooltip,
  Grid
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import VisibilityIcon from '@mui/icons-material/Visibility';
import InfoIcon from '@mui/icons-material/Info';
import dynamic from 'next/dynamic';
import WorkflowInitiator from '@/components/EnhancedCollaboration/WorkflowInitiator';
import EnhancedWorkflowVisualizer from '@/components/EnhancedCollaboration/EnhancedWorkflowVisualizer';
import DetailedWorkflowVisualizer from '@/components/EnhancedCollaboration/DetailedWorkflowVisualizer';
import AgentCollaborationNetwork from '@/components/EnhancedCollaboration/AgentCollaborationNetwork';
import EnhancedReasoningVisualizer from '@/components/EnhancedCollaboration/EnhancedReasoningVisualizer';
import { AgentDiscussionPanel } from '@/components/EnhancedCollaboration/AgentDiscussionPanel';
import { WorkflowInitData } from '@/components/EnhancedCollaboration/WorkflowInitiator';
import WorkflowControlPanel from '@/components/EnhancedCollaboration/WorkflowControlPanel';
import WorkflowPhaseExplainer from '@/components/EnhancedCollaboration/WorkflowPhaseExplainer';
import ArtifactReasoningPanel from '@/components/EnhancedCollaboration/ArtifactReasoningPanel';
import WorkflowMonitoringDashboard from '@/components/EnhancedCollaboration/WorkflowMonitoringDashboard';

// Import SimpleArtifactGallery and FinalArticlePreview
import SimpleArtifactGallery from '@/components/EnhancedCollaboration/SimpleArtifactGallery';
import FinalArticlePreview from '@/components/EnhancedCollaboration/FinalArticlePreview';

/**
 * Main dashboard for the collaborative agent system
 * Integrates all components with a clean, intuitive interface
 */
const CollaborativeDashboardPage: React.FC = () => {
  // State
  const [activeTab, setActiveTab] = useState(0);
  const [sessionId, setSessionId] = useState<string>('');
  const [sessionState, setSessionState] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Fetch session state
  const fetchSessionState = async (id: string) => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/workflow-orchestrator?sessionId=${id}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch session state: ${response.statusText}`);
      }

      const data = await response.json();

      // Check if we have state in the response
      if (data.state) {
        setSessionState(data.state);
      } else {
        setSessionState(data);
      }

      // Auto-advance to workflow tab if we're on the initiator tab
      if (activeTab === 0) {
        setActiveTab(1);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch session state');
      showSnackbar('Error fetching session data', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Start a new workflow
  const startWorkflow = async (workflowData: WorkflowInitData) => {
    setLoading(true);
    setError(null);

    try {
      // Format the request body according to the API expectations
      const requestBody = {
        action: 'createArticle', // Add the required action field
        topic: workflowData.topic,
        contentType: workflowData.contentType,
        targetAudience: workflowData.targetAudience,
        tone: workflowData.tone,
        keywords: workflowData.keywords,
        additionalInstructions: workflowData.additionalInstructions,
        clientName: workflowData.clientName
      };

      const response = await fetch('/api/workflow-orchestrator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`Failed to start workflow: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.sessionId) {
        setSessionId(data.sessionId);

        if (data.state) {
          setSessionState(data.state);
        } else {
          setSessionState(data);
        }

        showSnackbar('Workflow started successfully', 'success');
        setActiveTab(1); // Switch to workflow tab
      } else {
        throw new Error('No session ID returned from the server');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to start workflow');
      showSnackbar('Error starting workflow', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Pause workflow
  const pauseWorkflow = async () => {
    if (!sessionId) return;

    setLoading(true);

    try {
      const response = await fetch(`/api/workflow-orchestrator`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'transitionPhase',
          sessionId,
          phase: 'pause'
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to pause workflow: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.state) {
        setSessionState(data.state);
      } else {
        await fetchSessionState(sessionId);
      }

      showSnackbar('Workflow paused', 'info');
    } catch (err: any) {
      setError(err.message || 'Failed to pause workflow');
      showSnackbar('Error pausing workflow', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Resume workflow
  const resumeWorkflow = async () => {
    if (!sessionId) return;

    setLoading(true);

    try {
      const response = await fetch(`/api/workflow-orchestrator`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'transitionPhase',
          sessionId,
          phase: 'resume'
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to resume workflow: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.state) {
        setSessionState(data.state);
      } else {
        await fetchSessionState(sessionId);
      }

      showSnackbar('Workflow resumed', 'success');
    } catch (err: any) {
      setError(err.message || 'Failed to resume workflow');
      showSnackbar('Error resuming workflow', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Manually transition to a specific phase (for testing)
  const transitionToPhase = async (phase: string) => {
    if (!sessionId) return;

    setLoading(true);

    try {
      const response = await fetch(`/api/workflow-orchestrator`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'transitionPhase',
          sessionId,
          phase
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to transition to ${phase} phase: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.state) {
        setSessionState(data.state);
      } else {
        await fetchSessionState(sessionId);
      }

      showSnackbar(`Transitioned to ${phase} phase`, 'success');
    } catch (err: any) {
      setError(err.message || `Failed to transition to ${phase} phase`);
      showSnackbar(`Error transitioning to ${phase} phase`, 'error');
    } finally {
      setLoading(false);
    }
  };

  // Send message to agents
  const sendMessageToAgents = async (message: string) => {
    if (!sessionId) return;

    try {
      const response = await fetch(`/api/workflow-orchestrator`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'sendMessage',
          sessionId,
          message,
          from: 'user'
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to send message: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.state) {
        setSessionState(data.state);
      } else {
        await fetchSessionState(sessionId);
      }

      showSnackbar('Message sent', 'success');
    } catch (err: any) {
      setError(err.message || 'Failed to send message');
      showSnackbar('Error sending message', 'error');
    }
  };

  // Show snackbar
  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Refresh data on interval if session is active
  useEffect(() => {
    if (!sessionId || !sessionState || sessionState.status !== 'active') return;

    const intervalId = setInterval(() => {
      fetchSessionState(sessionId);
    }, 10000); // Refresh every 10 seconds

    return () => clearInterval(intervalId);
  }, [sessionId, sessionState]);

  // Determine if we have a final article
  const hasFinalArticle = Array.isArray(sessionState?.artifacts)
    ? sessionState.artifacts.some(
        (artifact: any) =>
          artifact.type === 'final-content' ||
          artifact.type === 'final-article' ||
          artifact.type === 'blog-content' ||
          artifact.type === 'generated-content' ||
          artifact.type === 'blog-post' ||
          artifact.type === 'content-draft'
      )
    : Object.values(sessionState?.artifacts || {}).some(
        (artifact: any) =>
          artifact.type === 'final-content' ||
          artifact.type === 'final-article' ||
          artifact.type === 'blog-content' ||
          artifact.type === 'generated-content' ||
          artifact.type === 'blog-post' ||
          artifact.type === 'content-draft'
      );

  // Log available artifacts for debugging
  console.log('Available artifacts for article preview check:',
    Array.isArray(sessionState?.artifacts)
      ? sessionState?.artifacts?.map((artifact: any) => artifact.type)
      : Object.values(sessionState?.artifacts || {}).map((artifact: any) => artifact.type)
  );

  // Calculate overall progress percentage
  const calculateOverallProgress = (state: any): number => {
    if (!state) return 0;

    // If we have a direct progress indicator, use it
    if (state.progress !== undefined) {
      return state.progress;
    }

    // Calculate based on phases from the new workflow orchestrator
    const phases = ['initialization', 'research', 'creation', 'review', 'finalization'];
    const currentPhase = state.currentPhase || state.collaborationState;
    const currentPhaseIndex = phases.findIndex(phase => phase === currentPhase);

    if (currentPhaseIndex < 0) return 0;

    // Calculate progress based on completed phases and current phase progress
    const totalPhases = phases.length;
    const completedPhases = currentPhaseIndex;

    // Estimate current phase progress
    let currentPhaseProgress = 50; // Default to 50%

    if (state.workflowProgress) {
      const progress = state.workflowProgress;

      switch (currentPhase) {
        case 'initialization':
          currentPhaseProgress = 100; // Initialization is quick
          break;
        case 'research':
          // Calculate research phase progress based on completed research tasks
          if (progress.marketResearchComplete && progress.keywordResearchComplete && progress.contentStrategyComplete) {
            currentPhaseProgress = 100;
          } else if (progress.marketResearchComplete && progress.keywordResearchComplete) {
            currentPhaseProgress = 75;
          } else if (progress.marketResearchComplete) {
            currentPhaseProgress = 50;
          } else {
            currentPhaseProgress = 25;
          }
          break;
        case 'creation':
          // Content generation phase progress
          if (progress.contentGenerationComplete) {
            currentPhaseProgress = 100;
          } else {
            // Check if we have any content generation artifacts
            const hasContentArtifacts = state.generatedArtifacts?.some(id =>
              state.artifacts[id]?.type === 'blog-content' ||
              state.artifacts[id]?.type === 'generated-content' ||
              state.artifacts[id]?.type === 'blog-post' ||
              state.artifacts[id]?.type === 'content-draft'
            );
            currentPhaseProgress = hasContentArtifacts ? 75 : 50;
          }
          break;
        case 'review':
          // Review phase progress
          currentPhaseProgress = progress.seoOptimizationComplete ? 100 : 50;
          break;
        case 'finalization':
          currentPhaseProgress = 100;
          break;
        default:
          currentPhaseProgress = 50;
      }
    }

    // Calculate overall progress
    const overallProgress = ((completedPhases + (currentPhaseProgress / 100)) / totalPhases) * 100;
    return Math.round(overallProgress);
  };

  // Estimate remaining time
  const estimateTimeRemaining = (state: any): string => {
    if (!state) return '';

    const phases = ['initialization', 'research', 'creation', 'review', 'finalization'];
    const currentPhase = state.currentPhase || state.collaborationState;
    const currentPhaseIndex = phases.findIndex(phase => phase === currentPhase);

    if (currentPhaseIndex < 0) return '';

    // Phase time estimates in minutes for the new workflow orchestrator
    const phaseTimeEstimates: Record<string, number> = {
      'initialization': 1,
      'research': 4, // Research phase includes market research, keyword research, and content strategy
      'creation': 5, // Content generation phase
      'review': 3,   // Review phase includes SEO optimization
      'finalization': 1
    };

    // Calculate remaining time
    let remainingMinutes = 0;

    // Add time for current phase (proportional to remaining progress)
    const totalPhases = phases.length;
    const currentPhaseProgress = calculateOverallProgress(state) * totalPhases / 100;
    const currentPhaseRemaining = 1 - (currentPhaseProgress - currentPhaseIndex);
    remainingMinutes += phaseTimeEstimates[currentPhase] * currentPhaseRemaining;

    // Add time for remaining phases
    for (let i = currentPhaseIndex + 1; i < phases.length; i++) {
      remainingMinutes += phaseTimeEstimates[phases[i]];
    }

    // Format the time
    if (remainingMinutes < 1) {
      return 'Less than a minute';
    } else if (remainingMinutes < 60) {
      return `~${Math.ceil(remainingMinutes)} minutes`;
    } else {
      const hours = Math.floor(remainingMinutes / 60);
      const minutes = Math.ceil(remainingMinutes % 60);
      return `~${hours}h ${minutes}m`;
    }
  };

  // Send feedback to agents
  const sendFeedbackToAgents = async (feedback: string) => {
    if (!sessionId || !feedback.trim()) return;

    try {
      const response = await fetch(`/api/workflow-orchestrator`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'sendFeedback',
          sessionId,
          feedback,
          from: 'user'
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to send feedback: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.state) {
        setSessionState(data.state);
      } else {
        await fetchSessionState(sessionId);
      }

      showSnackbar('Feedback sent to agents', 'success');
    } catch (err: any) {
      setError(err.message || 'Failed to send feedback');
      showSnackbar('Error sending feedback', 'error');
    }
  };

  // Import the new CohesiveDashboard component
  const CohesiveDashboard = dynamic(() => import('@/components/EnhancedCollaboration/CohesiveDashboard'), {
    loading: () => <CircularProgress />,
    ssr: false
  });

  // Render the new cohesive dashboard if we have a session, otherwise show the workflow initiator
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Error display */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Show workflow initiator if no session, otherwise show cohesive dashboard */}
      {!sessionId ? (
        <>
          <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
            <Typography variant="h4" gutterBottom>
              Collaborative AI Content Creation
            </Typography>
            <Typography variant="body1" paragraph>
              Create high-quality content with our collaborative AI agent system. Start by filling out the form below.
            </Typography>
          </Paper>

          <WorkflowInitiator
            onStartWorkflow={startWorkflow}
            loading={loading}
          />
        </>
      ) : (
        <>
          {/* Debug Controls */}
          <Paper elevation={3} sx={{ p: 2, mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                <Typography variant="subtitle2">
                  Session ID: <strong>{sessionId}</strong>
                  {sessionState?.status && (
                    <> • Status: <strong>{sessionState.status}</strong></>
                  )}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => setActiveTab(0)}
                  sx={{ mr: 2 }}
                >
                  New Session
                </Button>

                <Tooltip title="Refresh Status">
                  <IconButton
                    color="primary"
                    onClick={() => fetchSessionState(sessionId)}
                    disabled={loading}
                  >
                    <RefreshIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>

            {/* Phase Transition Buttons (for testing) */}
            <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              <Typography variant="caption" sx={{ width: '100%', mb: 1 }}>
                Debug: Manually Transition Phases
              </Typography>
              <Button
                size="small"
                variant="outlined"
                onClick={() => transitionToPhase('research')}
                disabled={loading}
              >
                Research
              </Button>
              <Button
                size="small"
                variant="outlined"
                onClick={() => transitionToPhase('content-generation')}
                disabled={loading}
              >
                Content Generation
              </Button>
              <Button
                size="small"
                variant="outlined"
                onClick={() => transitionToPhase('review')}
                disabled={loading}
              >
                Review
              </Button>
              <Button
                size="small"
                variant="outlined"
                onClick={() => transitionToPhase('complete')}
                disabled={loading}
              >
                Complete
              </Button>
            </Box>
          </Paper>

          {/* Cohesive Dashboard */}
          <CohesiveDashboard
            sessionId={sessionId}
            state={sessionState}
            onRefresh={() => fetchSessionState(sessionId)}
            onSendFeedback={sendFeedbackToAgents}
            loading={loading}
          />
        </>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default CollaborativeDashboardPage;
