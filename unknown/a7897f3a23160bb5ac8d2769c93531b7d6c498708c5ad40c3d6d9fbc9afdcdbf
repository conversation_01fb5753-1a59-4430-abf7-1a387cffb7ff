import { CollectionConfig } from 'payload'

export const Sellers: CollectionConfig = {
  slug: 'sellers',
  admin: {
    useAsTitle: 'name',
    group: 'Product Content',
  },
  fields: [
    {
      name: 'name',
      required: true,
      type: 'text',
    },
    {
      name: 'logo',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'description',
      type: 'textarea',
      admin: {
        components: {
          Field: {
            path: '@/components/AI/AIHelperField',
            exportName: 'AIHelperField'
          }
        }
      }
    },
    {
      name: 'website',
      type: 'group',
      fields:[
        {
          name: 'linktext',
          type: 'text',
        }, 
        {
          name: 'linkurl',
          type: 'text'
        }
      ]
    },
    {
      name: 'foundedYear',
      type: 'number'
    },
    {
      name: 'headquarters',
      type: 'text'
    },
    {
      name: 'socialMedia',
      type: 'group',
      fields: [
        {
          name: 'twitter',
          type: 'group',
          fields:[
            {
              name: 'linktext',
              type: 'text',
            }, 
            {
              name: 'linkurl',
              type: 'text'
            }
          ]
        },
        {
          name: 'linkedin',
          type: 'group',
          fields:[
            {
              name: 'linktext',
              type: 'text',
            }, 
            {
              name: 'linkurl',
              type: 'text'
            }
          ]
        }
      ]
    },
    {
      name: 'companySize',
      type: 'select',
      options: [
        { label: 'Startup (<50)', value: 'startup' },
        { label: 'Small (50-200)', value: 'small' },
        { label: 'Medium (201-1000)', value: 'medium' },
        { label: 'Large (1001-5000)', value: 'large' },
        { label: 'Enterprise (5000+)', value: 'enterprise' },
      ]
    },
    {
      name: 'employees',
      type: 'number'
    },
    {
      name: 'fundingStatus',
      type: 'select',
      options: [
        { label: 'Bootstrapped', value: 'bootstrapped' },
        { label: 'Seed', value: 'seed' },
        { label: 'Series A/B', value: 'series-a-b' },
        { label: 'Series C+', value: 'series-c-plus' },
        { label: 'Public', value: 'public' },
        { label: 'Acquired', value: 'acquired' },
      ]
    },
    {
      name: 'owner',
      type: 'text'
    },
    {
      name: 'contactInfo',
      type: 'group',
      fields: [
        {
          name: 'phone',
          type: 'text'
        },
        {
          name: 'email',
          type: 'email'
        },
        {
          name: 'supportUrl',
          type: 'text'
        }
      ]
    },
    {
      name: 'keyFacts',
      type: 'array',
      fields: [
        {
          name: 'fact',
          type: 'text',
        }
      ],
      admin: {
        description: 'Important facts about the company to highlight',
      }
    },
    {
      name: 'competitors',
      type: 'relationship',
      relationTo: 'sellers',
      hasMany: true,
      admin: {
        description: 'Other companies that compete with this seller',
      }
    },
    {
      name: 'industries',
      type: 'array',
      fields: [
        {
          name: 'industry',
          type: 'text',
        }
      ],
      admin: {
        description: 'Industries this seller primarily serves',
      }
    }
  ]
}