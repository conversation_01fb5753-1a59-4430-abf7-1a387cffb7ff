/**
 * Common Feedback Handler
 *
 * This module provides shared functionality for agents to process feedback
 * from the evaluation system and improve artifacts based on that feedback.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from './logger';
import { stateStore } from './stateStore';
import {
  IterativeArtifact,
  IterativeMessage,
  IterativeMessageType,
  AgentId,
  IterativeCollaborationState
} from '../types';
import { AgentStateManager } from './agentStateManager';
import { AgentMessaging } from './agentMessaging';
import OpenAI from 'openai';

// Initialize OpenAI client
const openai = process.env.OPENAI_API_KEY ? new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
}) : null;

/**
 * Process feedback from the evaluation system and improve an artifact
 */
export async function processFeedback(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging,
  agentId: AgentId
): Promise<{
  response: IterativeMessage;
  updatedArtifact?: IterativeArtifact;
}> {
  logger.info(`Processing feedback for ${agentId}`, {
    messageId: message.id,
    from: message.from
  });

  // Extract feedback details
  const { artifactId, goalId, evaluation } = message.content || {};

  if (!artifactId || !evaluation) {
    const errorResponse = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ERROR,
      {
        error: 'Missing required fields',
        message: 'Artifact ID and evaluation are required for processing feedback'
      },
      message.conversationId
    );

    return { response: errorResponse };
  }

  // Get the artifact from state
  const artifact = state.artifacts?.[artifactId];

  if (!artifact) {
    const errorResponse = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ERROR,
      {
        error: 'Artifact not found',
        message: `Could not find artifact with ID ${artifactId}`
      },
      message.conversationId
    );

    return { response: errorResponse };
  }

  // Generate improvement plan based on evaluation
  const improvementPlan = generateImprovementPlan(artifact, evaluation);

  // Improve the artifact based on the evaluation and improvement plan
  const improvedArtifact = await improveArtifact(
    artifact,
    evaluation,
    improvementPlan,
    agentId
  );

  // Create a new iteration for the improved artifact
  const updatedArtifact = await createNewArtifactIteration(
    state.id,
    improvedArtifact,
    agentId,
    improvementPlan
  );

  // Send response acknowledging the feedback and describing improvements
  const response = await messaging.send(
    state.id,
    message.from as AgentId,
    IterativeMessageType.ACKNOWLEDGMENT,
    {
      originalMessageId: message.id,
      message: `Thank you for your feedback. I've improved the ${artifact.type} artifact based on your evaluation.`,
      improvementPlan,
      artifactId: updatedArtifact.id,
      changes: summarizeChanges(artifact, updatedArtifact)
    },
    message.conversationId
  );

  return {
    response,
    updatedArtifact
  };
}

/**
 * Generate an improvement plan based on evaluation
 */
function generateImprovementPlan(artifact: IterativeArtifact, evaluation: any): string {
  // Extract the weakest criteria from the evaluation
  const weakCriteria = evaluation.criteriaResults
    .filter((result: any) => result.score < 0.7)
    .sort((a: any, b: any) => a.score - b.score);

  // Generate improvement plan
  let plan = `# Improvement Plan for ${artifact.name}\n\n`;

  plan += `## Overall Assessment\n`;
  plan += `${evaluation.feedback}\n\n`;

  plan += `## Areas Needing Improvement\n`;

  if (weakCriteria.length > 0) {
    weakCriteria.forEach((criterion: any, index: number) => {
      plan += `### ${index + 1}. ${criterion.criterion}\n`;
      plan += `- Score: ${Math.round(criterion.score * 100)}%\n`;
      plan += `- Feedback: ${criterion.feedback}\n`;

      if (criterion.suggestions && criterion.suggestions.length > 0) {
        plan += `- Suggestions:\n`;
        criterion.suggestions.forEach((suggestion: string) => {
          plan += `  - ${suggestion}\n`;
        });
      }

      plan += `\n`;
    });
  } else {
    plan += `No specific areas were identified as needing significant improvement, but the overall quality could still be enhanced.\n\n`;
  }

  plan += `## Improvement Strategy\n`;
  plan += `1. Address the weakest areas first (listed above)\n`;
  plan += `2. Ensure all criteria are fully met\n`;
  plan += `3. Enhance the overall quality and coherence\n`;

  return plan;
}

/**
 * Improve an artifact based on evaluation and improvement plan
 */
async function improveArtifact(
  artifact: IterativeArtifact,
  evaluation: any,
  improvementPlan: string,
  agentId: AgentId
): Promise<IterativeArtifact> {
  // Create a deep copy of the artifact to avoid modifying the original
  const improvedArtifact = JSON.parse(JSON.stringify(artifact));

  // Extract content from the artifact
  const content = extractContent(artifact);

  // If OpenAI is available, use it to improve the content
  if (openai) {
    try {
      // Create a prompt for improving the content
      const prompt = `
You are an expert content creator tasked with improving content based on feedback.

ORIGINAL CONTENT:
${content}

EVALUATION FEEDBACK:
${evaluation.feedback}

IMPROVEMENT PLAN:
${improvementPlan}

Please provide an improved version of the content that addresses all the feedback points.
Focus especially on the areas with the lowest scores.
Maintain the same general structure and purpose, but enhance the quality based on the feedback.
`;

      // Call OpenAI to improve the content
      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {role: "system", content: "You are an expert content creator specializing in improving content based on feedback."},
          {role: "user", content: prompt}
        ],
        temperature: 0.7,
        max_tokens: 2000
      });

      // Extract the improved content
      const improvedContent = completion.choices[0]?.message?.content || content;

      // Update the artifact with the improved content
      if (typeof improvedArtifact.content === 'string') {
        improvedArtifact.content = improvedContent;
      } else if (improvedArtifact.iterations && improvedArtifact.iterations.length > 0) {
        // Create a new iteration with the improved content
        const latestIteration = improvedArtifact.iterations[improvedArtifact.iterations.length - 1];
        if (typeof latestIteration.content === 'string') {
          latestIteration.content = improvedContent;
        } else if (typeof latestIteration.content === 'object') {
          // If content is an object, we need to be more careful
          // For now, just update the 'text' field if it exists
          if (latestIteration.content.text) {
            latestIteration.content.text = improvedContent;
          }
        }
      }

      // Update quality score based on evaluation
      improvedArtifact.qualityScore = Math.round(evaluation.overallScore * 100);

      return improvedArtifact;
    } catch (error) {
      logger.error(`Error improving artifact with OpenAI`, {
        artifactId: artifact.id,
        error: error instanceof Error ? error.message : String(error)
      });

      // Fall back to basic improvements
      return improveArtifactBasic(improvedArtifact, evaluation, improvementPlan);
    }
  } else {
    // If OpenAI is not available, use basic improvement logic
    return improveArtifactBasic(improvedArtifact, evaluation, improvementPlan);
  }
}

/**
 * Basic artifact improvement without using OpenAI
 */
function improveArtifactBasic(
  artifact: IterativeArtifact,
  evaluation: any,
  improvementPlan: string
): IterativeArtifact {
  // Extract content from the artifact
  let content = extractContent(artifact);

  // Add improvement notes to the content
  content = `${content}\n\n---\n\n## Improvements Based on Feedback\n\n${improvementPlan}`;

  // Update the artifact with the improved content
  if (typeof artifact.content === 'string') {
    artifact.content = content;
  } else if (artifact.iterations && artifact.iterations.length > 0) {
    // Create a new iteration with the improved content
    const latestIteration = artifact.iterations[artifact.iterations.length - 1];
    if (typeof latestIteration.content === 'string') {
      latestIteration.content = content;
    } else if (typeof latestIteration.content === 'object') {
      // If content is an object, we need to be more careful
      // For now, just update the 'text' field if it exists
      if (latestIteration.content.text) {
        latestIteration.content.text = content;
      }
    }
  }

  // Update quality score based on evaluation
  artifact.qualityScore = Math.round(evaluation.overallScore * 100);

  return artifact;
}

/**
 * Extract content from an artifact
 */
function extractContent(artifact: IterativeArtifact): string {
  if (typeof artifact.content === 'string') {
    return artifact.content;
  }

  if (artifact.iterations && artifact.iterations.length > 0) {
    const latestIteration = artifact.iterations[artifact.iterations.length - 1];
    if (typeof latestIteration.content === 'string') {
      return latestIteration.content;
    }

    if (typeof latestIteration.content === 'object') {
      if (latestIteration.content.text) {
        return latestIteration.content.text;
      }
      return JSON.stringify(latestIteration.content);
    }
  }

  if (artifact.data) {
    if (typeof artifact.data === 'string') {
      return artifact.data;
    }

    if (typeof artifact.data === 'object') {
      return JSON.stringify(artifact.data);
    }
  }

  return '';
}

/**
 * Create a new iteration for an artifact
 */
async function createNewArtifactIteration(
  sessionId: string,
  artifact: IterativeArtifact,
  agentId: AgentId,
  improvementPlan: string
): Promise<IterativeArtifact> {
  // Create a new version number
  const newVersion = (artifact.currentVersion || 1) + 1;

  // Create a new iteration
  const newIteration = {
    version: newVersion,
    timestamp: new Date().toISOString(),
    agent: agentId,
    content: typeof artifact.content === 'string' ? artifact.content :
             artifact.iterations && artifact.iterations.length > 0 ?
             artifact.iterations[artifact.iterations.length - 1].content : '',
    feedback: [],
    incorporatedConsultations: [],
    improvementPlan
  };

  // Update the artifact in the state store
  await stateStore.updateState(sessionId, (currentState) => {
    if (!currentState || !currentState.artifacts || !currentState.artifacts[artifact.id]) {
      return currentState;
    }

    const updatedArtifacts = { ...currentState.artifacts };
    updatedArtifacts[artifact.id] = {
      ...updatedArtifacts[artifact.id],
      currentVersion: newVersion,
      iterations: [...(updatedArtifacts[artifact.id].iterations || []), newIteration],
      qualityScore: artifact.qualityScore
    };

    return {
      ...currentState,
      artifacts: updatedArtifacts
    };
  });

  // Get the updated artifact
  const state = await stateStore.getState(sessionId);
  return state?.artifacts?.[artifact.id] || artifact;
}

/**
 * Summarize changes made to an artifact
 */
function summarizeChanges(originalArtifact: IterativeArtifact, updatedArtifact: IterativeArtifact): string {
  const originalContent = extractContent(originalArtifact);
  const updatedContent = extractContent(updatedArtifact);

  // Simple summary based on content length
  const originalWords = originalContent.split(/\s+/).length;
  const updatedWords = updatedContent.split(/\s+/).length;
  const wordDiff = updatedWords - originalWords;

  let summary = `Updated from version ${originalArtifact.currentVersion} to ${updatedArtifact.currentVersion}. `;

  if (wordDiff > 0) {
    summary += `Added ${wordDiff} words. `;
  } else if (wordDiff < 0) {
    summary += `Removed ${Math.abs(wordDiff)} words. `;
  }

  summary += `Quality score: ${originalArtifact.qualityScore || 0} → ${updatedArtifact.qualityScore || 0}`;

  return summary;
}
