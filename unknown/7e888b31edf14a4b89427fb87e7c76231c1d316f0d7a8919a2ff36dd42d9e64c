/**
 * Reasoning Utilities
 *
 * This file contains utility functions for handling agent reasoning data,
 * including storing reasoning in the collaboration state and transforming
 * reasoning data for UI display.
 *
 * The enhanced reasoning system provides more transparent chain-of-thought reasoning
 * with explicit considerations, evaluation steps, confidence assessment, and
 * multi-dimensional validation capabilities.
 *
 * Features:
 * - Structured reasoning with explicit steps
 * - Cross-validation between reasoning components
 * - Confidence scoring with explicit justification
 * - Reasoning traceability for debugging and transparency
 * - Multi-dimensional validation for quality assessment
 */

import { v4 as uuidv4 } from 'uuid';
import { IterativeCollaborationState, Decision } from '../types';
import { stateStore } from './stateStore';

/**
 * Interface for reasoning validation results
 */
export interface ReasoningValidationResult {
  /** Whether the reasoning is valid overall */
  isValid: boolean;
  /** Validation score (0-1) */
  score: number;
  /** Detailed validation results for different dimensions */
  dimensions: {
    /** Logical consistency score (0-1) */
    logicalConsistency: number;
    /** Evidence support score (0-1) */
    evidenceSupport: number;
    /** Completeness score (0-1) */
    completeness: number;
    /** Relevance score (0-1) */
    relevance: number;
    /** Clarity score (0-1) */
    clarity: number;
  };
  /** Validation issues found */
  issues: Array<{
    /** Issue type */
    type: 'logical_error' | 'evidence_gap' | 'incompleteness' | 'irrelevance' | 'ambiguity';
    /** Issue description */
    description: string;
    /** Issue severity (low, medium, high) */
    severity: string;
    /** Suggested resolution */
    resolution?: string;
  }>;
  /** Validation strengths found */
  strengths: string[];
  /** Improvement suggestions */
  suggestions: string[];
}

/**
 * Interface for cross-validation results between multiple reasoning components
 */
export interface CrossValidationResult {
  /** Whether the cross-validation passed */
  isPassed: boolean;
  /** Overall consistency score (0-1) */
  consistencyScore: number;
  /** Identified conflicts between reasoning components */
  conflicts: Array<{
    /** Description of the conflict */
    description: string;
    /** Components involved in the conflict */
    components: string[];
    /** Severity of the conflict (low, medium, high) */
    severity: string;
    /** Suggested resolution */
    resolution?: string;
  }>;
  /** Areas of strong agreement between components */
  agreements: string[];
  /** Suggested improvements based on cross-validation */
  suggestions: string[];
}

/**
 * Represents a single thought in the agent's reasoning process
 */
export interface Thought {
  /** The content of the thought */
  content: string;
  /** Optional alternative content field for backward compatibility */
  thought?: string;
  /** Optional confidence score for this thought (0-1) */
  confidence?: number;
  /** Timestamp when the thought occurred */
  timestamp?: string;
  /** Optional array of considerations related to this thought */
  considerations?: Consideration[];
  /** Optional evidence supporting this thought */
  evidence?: string;
}

/**
 * Represents a factor the agent considered in its reasoning
 */
export interface Consideration {
  /** The specific factor being considered */
  factor: string;
  /** The content explaining this consideration */
  content?: string;
  /** The impact level of this consideration (high, medium, low) */
  impact: string;
  /** Probability assessment (0-1) if applicable */
  probability?: number;
  /** Evidence supporting this consideration */
  evidence?: string;
  /** Detailed explanation of the reasoning */
  explanation?: string;
}

/**
 * Enhanced Reasoning type with more explicit chain-of-thought structure
 * Updated to support LangGraph-inspired reasoning patterns
 */
export interface EnhancedReasoning {
  /** Unique identifier for this reasoning instance */
  id?: string;
  /** The specific reasoning process or context */
  context?: Record<string, any>;
  /** The actual reasoning text, showing chain of thought */
  reasoning?: string;
  /** The final decision or conclusion reached */
  decision: string;
  /** Timestamp when the reasoning occurred */
  timestamp: string;
  /** ID of the agent that generated this reasoning */
  agentId: string;
  /** The original question (can be string or object) */
  question?: string | Record<string, any>;
  /** String representation of the question */
  questionStr?: string;
  /** Detailed thought process as an array of thoughts */
  thoughts?: string[];
  /** Factors considered in making the decision */
  considerations?: string[];
  /** Confidence in the decision (0-1) */
  confidence?: number;
  /** Steps taken in the reasoning process */
  steps?: string[];
  /** Process name or type */
  process?: string;
  /** The question being analyzed */
  question?: string;

  // New LangGraph-inspired fields
  /** Analysis of the available context */
  contextAnalysis?: {
    /** Completeness score for available context (0-1) */
    completeness?: number;
    /** Available knowledge domains relevant to the reasoning */
    availableDomains?: string[];
    /** Missing information that would improve reasoning */
    missingInformation?: string[];
  };
  /** Analysis of the problem or question */
  questionAnalysis?: {
    /** Type or category of question */
    type?: string;
    /** Subcategory of question */
    category?: string;
    /** Complexity assessment (low, medium, high) */
    complexity?: string;
    /** Intent of the question (informational, implementation, etc.) */
    intent?: string;
  };
  /** Evidence supporting the reasoning */
  supportingEvidence?: string[];
  /** Specific insights gained during reasoning */
  insights?: string[];
  /** Alternative perspectives considered */
  alternativePerspectives?: Array<{view: string, strength: string}>;
  /** Detailed calculation of confidence score */
  confidenceCalculation?: Record<string, any>;
  /** Type of question being analyzed (for content strategy) */
  questionType?: string;
  /** Category of question (implementation, strategy, etc.) */
  questionCategory?: string;
  /** Complexity of the question (low, medium, high) */
  questionComplexity?: string;
  /** Information about context completeness */
  contextCompleteness?: number;
  /** Available domains of knowledge */
  availableDomains?: string[];
  /** Missing information */
  missingInformation?: string[];
}

/**
 * Legacy reasoning type for backward compatibility
 */
export interface Reasoning {
  process: string;
  thoughts: Thought[];
  considerations: Consideration[];
  decision: string;
  timestamp: string;
  agentId: string;
  confidence?: number;
}

/**
 * Create structured reasoning using LangGraph-inspired approach with explicit steps
 * This function generates a comprehensive reasoning structure with step-by-step evaluation
 *
 * @param question - The question or task to reason about
 * @param context - The context information for reasoning
 * @param steps - Array of reasoning steps to follow
 * @param openaiClient - OpenAI client instance
 * @returns Promise<EnhancedReasoning> - Structured reasoning result
 */
export async function createStructuredReasoning(
  question: string,
  context: any,
  steps: string[],
  openaiClient: any
): Promise<EnhancedReasoning> {
  console.log(`Creating structured reasoning for question: ${question}`);

  try {
    // Format the context for the reasoning prompt
    const contextStr = typeof context === 'string' ? context : JSON.stringify(context, null, 2);

    // Generate steps string for prompt
    const stepsStr = steps.map((step, index) => `${index + 1}. ${step}`).join('\n');

    const response = await openaiClient.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: `You are a reasoning engine that performs structured step-by-step analysis similar to LangGraph's chain-of-thought approach.

          Given a question and context, you will follow the specified reasoning steps exactly, providing your analysis for each step.
          For each step, provide your detailed reasoning with supporting evidence.

          After completing all steps, provide a final decision with an explicit confidence level (0-1).

          Your output must be a valid JSON object with the following structure:
          {
            "steps": [
              { "step": "Step description", "reasoning": "Your reasoning for this step", "evidence": "Supporting evidence" },
              ...
            ],
            "thoughts": ["Key thought 1", "Key thought 2", ...],
            "considerations": ["Important consideration 1", "Important consideration 2", ...],
            "contextAnalysis": {
              "completeness": 0.8, // 0-1 score for how complete the provided context is
              "missingInformation": ["Description of missing info 1", ...]
            },
            "questionAnalysis": {
              "complexity": "medium", // low, medium, high
              "type": "classification", // implementation, classification, etc.
              "intent": "Determine the appropriate action"
            },
            "decision": "Your final decision or answer",
            "confidence": 0.85 // 0-1 score
          }`
        },
        {
          role: 'user',
          content: `Question: ${question}\n\nContext: ${contextStr}\n\nFollow these reasoning steps:\n${stepsStr}`
        }
      ],
      response_format: { type: 'json_object' },
      temperature: 0.2,
    });

    // Parse and validate the response
    const responseContent = response.choices[0].message.content;
    if (!responseContent) {
      throw new Error('Empty response from reasoning engine');
    }

    const result = JSON.parse(responseContent);

    // Transform into EnhancedReasoning format
    const enhancedReasoning: EnhancedReasoning = {
      id: uuidv4(),
      process: `Structured reasoning for: ${question}`,
      question,
      decision: result.decision,
      timestamp: new Date().toISOString(),
      agentId: 'reasoning-engine', // This will be overridden by the calling agent
      steps: result.steps?.map((s: any) => s.reasoning) || [],
      thoughts: result.thoughts || [],
      considerations: result.considerations || [],
      confidence: result.confidence || 0.5,
      contextAnalysis: result.contextAnalysis,
      questionAnalysis: result.questionAnalysis,
      context
    };

    return enhancedReasoning;
  } catch (error: any) {
    console.error('Error creating structured reasoning:', error);

    // Return a basic reasoning structure in case of error
    return {
      id: uuidv4(),
      process: `Error in structured reasoning for: ${question}`,
      question,
      decision: 'Unable to complete reasoning due to an error',
      timestamp: new Date().toISOString(),
      agentId: 'reasoning-engine',
      steps: [`Error: ${error.message}`],
      thoughts: ['Error occurred during reasoning process'],
      considerations: ['Proceeding with caution due to reasoning failure'],
      confidence: 0.1,
      context
    };
  }
}

/**
 * Store agent reasoning in the collaboration state
 *
 * @param sessionId - The collaboration session ID
 * @param agentId - The ID of the agent generating the reasoning
 * @param artifact - Optional artifact associated with the reasoning
 * @param messageId - Optional message ID associated with the reasoning
 * @param conversationId - Optional conversation ID associated with the reasoning
 * @param reasoning - The reasoning data (either EnhancedReasoning or legacy Reasoning)
 * @returns Promise<boolean> - Success status
 */
export async function storeAgentReasoning(
  sessionId: string,
  agentId: string,
  artifact: any | null,
  messageId: string,
  conversationId: string,
  reasoning: EnhancedReasoning | Reasoning
): Promise<boolean> {
  try {
    // Get current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      console.error(`No state found for session ${sessionId}`);
      return false;
    }

    // Determine if we're dealing with enhanced or legacy reasoning
    const isEnhancedReasoning = (
      'context' in reasoning ||
      ('considerations' in reasoning &&
       Array.isArray(reasoning.considerations) &&
       reasoning.considerations.length > 0 &&
       typeof reasoning.considerations[0] === 'string')
    );

    // Create reasoning content for the decision
    const reasoningContent = {
      process: '',
      thoughts: [] as string[],
      considerations: [] as string[],
      decision: reasoning.decision
    };

    // Set process
    if ('process' in reasoning && typeof reasoning.process === 'string') {
      reasoningContent.process = reasoning.process;
    } else {
      reasoningContent.process = 'Enhanced Agent Reasoning';
    }

    // Handle thoughts based on the type of reasoning
    if (isEnhancedReasoning) {
      // Enhanced reasoning format
      const enhancedReasoning = reasoning as EnhancedReasoning;

      if (enhancedReasoning.thoughts && Array.isArray(enhancedReasoning.thoughts)) {
        reasoningContent.thoughts = enhancedReasoning.thoughts;
      } else if (enhancedReasoning.reasoning) {
        reasoningContent.thoughts = [enhancedReasoning.reasoning];
      } else {
        reasoningContent.thoughts = [enhancedReasoning.decision];
      }

      if (enhancedReasoning.considerations && Array.isArray(enhancedReasoning.considerations)) {
        reasoningContent.considerations = enhancedReasoning.considerations;
      }
    } else {
      // Legacy reasoning format
      const legacyReasoning = reasoning as Reasoning;

      if (legacyReasoning.thoughts && Array.isArray(legacyReasoning.thoughts)) {
        reasoningContent.thoughts = legacyReasoning.thoughts.map(t => {
          if (typeof t === 'string') return t;
          return t.content || t.thought || '';
        });
      }

      if (legacyReasoning.considerations && Array.isArray(legacyReasoning.considerations)) {
        reasoningContent.considerations = legacyReasoning.considerations.map(c => {
          if (typeof c === 'string') return c;
          const factor = c.factor || '';
          const impact = c.impact || '';
          const explanation = c.explanation || (typeof c.content === 'string' ? c.content : '');
          return `${factor}: ${impact} - ${explanation}`;
        });
      }
    }

    // Create the decision object
    const decision: Decision = {
      id: uuidv4(),
      agent: agentId, // Use agent instead of agentId based on Decision interface
      timestamp: new Date().toISOString(),
      context: `Analysis related to ${artifact?.type || 'content'}`,
      reasoning: JSON.stringify(reasoningContent),
      outcome: reasoning.decision,
      confidence: 'confidence' in reasoning ? reasoning.confidence : 0.85,
      originalReasoning: reasoning // Store the original reasoning for reference
    };

    // Add to decisions array
    if (!state.decisions) {
      state.decisions = [];
    }
    state.decisions.push(decision);

    // Update state
    await stateStore.setState(sessionId, state);
    return true;
  } catch (error) {
    console.error('Error storing agent reasoning:', error);
    return false;
  }
}

/**
 * Create enhanced reasoning structure with detailed chain of thought
 *
 * @param context - The context or situation being reasoned about
 * @param considerations - Array of factors considered in the decision
 * @param decision - The final decision or conclusion reached
 * @param agentId - The ID of the agent generating this reasoning
 * @param confidence - Optional confidence score (0-1)
 * @returns EnhancedReasoning object
 */
/**
 * Create enhanced reasoning structure with detailed chain of thought
 * Updated to support our standardized reasoning format
 *
 * @param context - The context information for the reasoning
 * @param thoughts - Array of thought steps in the reasoning process
 * @param conclusion - The conclusion reached
 * @param agentId - The agent creating the reasoning
 * @param metadata - Additional metadata for the reasoning
 * @returns An enhanced reasoning object
 */
export function createEnhancedReasoning(
  context: Record<string, any>,
  thoughts: string[],
  conclusion: string,
  agentId: string,
  metadata: Record<string, any> = {}
): EnhancedReasoning {
  return {
    id: uuidv4(),
    context,
    thoughts,
    considerations: thoughts.filter(t =>
      t.includes('consider') ||
      t.includes('factor') ||
      t.includes('take into account') ||
      t.includes('important to note') ||
      t.includes('should be aware')
    ),
    decision: conclusion,
    timestamp: new Date().toISOString(),
    agentId,
    confidence: metadata.confidence || 0.8,
    steps: metadata.steps || thoughts.map(t => t.substring(0, 50)),
    process: `${agentId}-reasoning`,
    metadata: {
      confidence: metadata.confidence || 0.8,
      steps: metadata.steps || thoughts.map(t => t.substring(0, 50)),
      alternatives: metadata.alternatives || [],
      agent: agentId,
      timestamp: new Date().toISOString(),
      ...metadata
    }
  };
}

/**
 * Transform reasoning data to a format compatible with AgentReasoningPanel
 *
 * @param reasoning - The reasoning data from the agent (enhanced or legacy)
 * @returns The transformed reasoning data for UI display
 */
/**
 * Transform reasoning data to a format compatible with AgentReasoningPanel
 * Updated to support LangGraph-inspired reasoning patterns
 *
 * @param reasoning - The reasoning data from the agent (enhanced or legacy)
 * @returns The transformed reasoning data for UI display
 */
/**
 * Create a LangGraph-inspired chain-of-thought reasoning structure
 * This function helps standardize the reasoning pattern across all agents
 *
 * @param question - The question or prompt being answered
 * @param context - The context information available for reasoning
 * @param steps - Array of reasoning steps taken
 * @param decision - The final decision or conclusion reached
 * @param agentId - ID of the agent performing the reasoning
 * @param options - Additional options for enhanced reasoning
 * @returns An EnhancedReasoning object with LangGraph-inspired structure
 */
export function createChainOfThoughtReasoning(
  question: string | Record<string, any>,
  context: Record<string, any>,
  steps: string[],
  decision: string,
  agentId: string,
  options?: {
    confidence?: number;
    supportingEvidence?: string[];
    insights?: string[];
    questionType?: string;
    questionCategory?: string;
    questionComplexity?: string;
    contextCompleteness?: number;
    availableDomains?: string[];
    missingInformation?: string[];
    confidenceCalculation?: Record<string, any>;
  }
): EnhancedReasoning {
  // Convert question to string if it's an object
  const questionStr = typeof question === 'string'
    ? question
    : JSON.stringify(question);

  // Log the question type for debugging
  console.log(`createChainOfThoughtReasoning received question of type: ${typeof question}`);

  // Analyze the question if not provided
  const questionType = options?.questionType || inferQuestionType(questionStr);
  const questionCategory = options?.questionCategory || inferQuestionCategory(questionStr);
  const questionComplexity = options?.questionComplexity || inferQuestionComplexity(questionStr);

  // Analyze context completeness if not provided
  const contextCompleteness = options?.contextCompleteness || calculateContextCompleteness(context);

  // Create enhanced reasoning structure
  return {
    id: uuidv4(),
    context,
    decision,
    timestamp: new Date().toISOString(),
    agentId,
    steps,
    question, // Include the original question (object or string)
    questionStr, // Include the string version of the question
    questionType, // Explicitly include the question type
    questionCategory, // Explicitly include the question category
    questionComplexity, // Explicitly include the question complexity
    contextCompleteness, // Explicitly include the context completeness
    availableDomains: options?.availableDomains || [], // Explicitly include available domains
    missingInformation: options?.missingInformation || [], // Explicitly include missing information
    confidence: options?.confidence || calculateDefaultConfidence(contextCompleteness, questionComplexity),
    supportingEvidence: options?.supportingEvidence || [],
    insights: options?.insights || [],
    questionAnalysis: {
      type: questionType,
      category: questionCategory,
      complexity: questionComplexity,
      intent: questionType // For backward compatibility
    },
    contextAnalysis: {
      completeness: contextCompleteness,
      availableDomains: options?.availableDomains || [],
      missingInformation: options?.missingInformation || []
    },
    confidenceCalculation: options?.confidenceCalculation || {
      baseConfidence: 0.7,
      contextFactor: contextCompleteness * 0.2,
      complexityFactor: questionComplexity === 'low' ? 0.1 : questionComplexity === 'medium' ? 0 : -0.1
    }
  };
}

// Helper functions for reasoning creation
function inferQuestionType(question: string | any): string {
  // Ensure question is a string before calling toLowerCase()
  if (!question || typeof question !== 'string') {
    console.log('Warning: inferQuestionType received non-string question:', question);
    return 'general';
  }

  const questionLower = question.toLowerCase();

  if (questionLower.includes('audience') || questionLower.includes('targeting')) {
    return 'audience';
  } else if (questionLower.includes('format') || questionLower.includes('type')) {
    return 'format';
  } else if (questionLower.includes('distribution') || questionLower.includes('channel')) {
    return 'distribution';
  } else if (questionLower.includes('measure') || questionLower.includes('metric')) {
    return 'measurement';
  }
  return 'general';
}

function inferQuestionCategory(question: string | any): string {
  // Ensure question is a string before calling toLowerCase()
  if (!question || typeof question !== 'string') {
    console.log('Warning: inferQuestionCategory received non-string question:', question);
    return 'strategic';
  }

  const questionLower = question.toLowerCase();

  if (questionLower.includes('how') || questionLower.includes('implement')) {
    return 'implementation';
  } else if (questionLower.includes('why') || questionLower.includes('benefit')) {
    return 'justification';
  } else if (questionLower.includes('when') || questionLower.includes('timeline')) {
    return 'timing';
  }
  return 'strategic';
}

function inferQuestionComplexity(question: string | any): string {
  // Ensure question is a string before processing
  if (!question || typeof question !== 'string') {
    console.log('Warning: inferQuestionComplexity received non-string question:', question);
    return 'medium'; // Default to medium complexity for non-string inputs
  }

  const complexityFactors = [
    question.split(' ').length > 15, // Long question
    question.includes(' and ') || question.includes(' or '), // Multiple concepts
    question.includes('?') && question.split('?').length > 2, // Multiple questions
    /compare|difference|versus|vs\.?/i.test(question), // Comparative analysis
    /specific|detailed|comprehensive/i.test(question) // Requests detailed response
  ];

  const complexityScore = complexityFactors.filter(Boolean).length;
  return complexityScore <= 1 ? 'low' : complexityScore <= 3 ? 'medium' : 'high';
}

function calculateContextCompleteness(context: Record<string, any>): number {
  if (!context) return 0;

  // Basic empty check
  if (Object.keys(context).length === 0) return 0;

  // Calculate based on presence of key fields (very basic implementation)
  const nonEmptyFields = Object.values(context).filter(val =>
    val !== undefined &&
    val !== null &&
    val !== '' &&
    !(Array.isArray(val) && val.length === 0) &&
    !(typeof val === 'object' && Object.keys(val).length === 0)
  ).length;

  return Math.min(nonEmptyFields / Math.max(Object.keys(context).length, 1), 1);
}

function calculateDefaultConfidence(contextCompleteness: number, complexity: string): number {
  // Base confidence level
  let baseConfidence = 0.7;

  // Adjust for context completeness (0-1 scale)
  const contextFactor = contextCompleteness * 0.2; // Max +0.2 for complete context

  // Adjust for question complexity
  const complexityFactor =
    complexity === 'low' ? 0.1 :
    complexity === 'medium' ? 0 :
    -0.1; // Penalty for high complexity

  // Calculate final confidence (capped between 0.4 and 0.95)
  return Math.min(Math.max(baseConfidence + contextFactor + complexityFactor, 0.4), 0.95);
}

/**
 * Validate reasoning for logical consistency, evidence support, completeness, relevance, and clarity
 *
 * @param reasoning - The reasoning to validate
 * @returns Validation result with detailed analysis
 */
export function validateReasoning(reasoning: EnhancedReasoning): ReasoningValidationResult {
  // Initialize validation result
  const result: ReasoningValidationResult = {
    isValid: false,
    score: 0,
    dimensions: {
      logicalConsistency: 0,
      evidenceSupport: 0,
      completeness: 0,
      relevance: 0,
      clarity: 0
    },
    issues: [],
    strengths: [],
    suggestions: []
  };

  try {
    // Validate logical consistency
    const logicalConsistencyScore = validateLogicalConsistency(reasoning);
    result.dimensions.logicalConsistency = logicalConsistencyScore.score;

    if (logicalConsistencyScore.issues.length > 0) {
      result.issues.push(...logicalConsistencyScore.issues);
    }

    if (logicalConsistencyScore.strengths.length > 0) {
      result.strengths.push(...logicalConsistencyScore.strengths);
    }

    // Validate evidence support
    const evidenceSupportScore = validateEvidenceSupport(reasoning);
    result.dimensions.evidenceSupport = evidenceSupportScore.score;

    if (evidenceSupportScore.issues.length > 0) {
      result.issues.push(...evidenceSupportScore.issues);
    }

    if (evidenceSupportScore.strengths.length > 0) {
      result.strengths.push(...evidenceSupportScore.strengths);
    }

    // Validate completeness
    const completenessScore = validateCompleteness(reasoning);
    result.dimensions.completeness = completenessScore.score;

    if (completenessScore.issues.length > 0) {
      result.issues.push(...completenessScore.issues);
    }

    if (completenessScore.strengths.length > 0) {
      result.strengths.push(...completenessScore.strengths);
    }

    // Validate relevance
    const relevanceScore = validateRelevance(reasoning);
    result.dimensions.relevance = relevanceScore.score;

    if (relevanceScore.issues.length > 0) {
      result.issues.push(...relevanceScore.issues);
    }

    if (relevanceScore.strengths.length > 0) {
      result.strengths.push(...relevanceScore.strengths);
    }

    // Validate clarity
    const clarityScore = validateClarity(reasoning);
    result.dimensions.clarity = clarityScore.score;

    if (clarityScore.issues.length > 0) {
      result.issues.push(...clarityScore.issues);
    }

    if (clarityScore.strengths.length > 0) {
      result.strengths.push(...clarityScore.strengths);
    }

    // Calculate overall score (weighted average)
    const weights = {
      logicalConsistency: 0.3,
      evidenceSupport: 0.25,
      completeness: 0.2,
      relevance: 0.15,
      clarity: 0.1
    };

    result.score =
      result.dimensions.logicalConsistency * weights.logicalConsistency +
      result.dimensions.evidenceSupport * weights.evidenceSupport +
      result.dimensions.completeness * weights.completeness +
      result.dimensions.relevance * weights.relevance +
      result.dimensions.clarity * weights.clarity;

    // Determine overall validity
    result.isValid = result.score >= 0.75;

    // Generate suggestions based on issues
    if (result.issues.length > 0) {
      // Group issues by type
      const issuesByType: Record<string, Array<{description: string, severity: string}>> = {};

      result.issues.forEach(issue => {
        if (!issuesByType[issue.type]) {
          issuesByType[issue.type] = [];
        }
        issuesByType[issue.type].push({
          description: issue.description,
          severity: issue.severity
        });
      });

      // Generate suggestions for each issue type
      if (issuesByType.logical_error) {
        result.suggestions.push('Improve logical consistency by addressing contradictions and ensuring conclusions follow from premises');
      }

      if (issuesByType.evidence_gap) {
        result.suggestions.push('Strengthen evidence support by providing more concrete examples or data points');
      }

      if (issuesByType.incompleteness) {
        result.suggestions.push('Enhance completeness by addressing all aspects of the question or problem');
      }

      if (issuesByType.irrelevance) {
        result.suggestions.push('Improve relevance by focusing more directly on the core question or problem');
      }

      if (issuesByType.ambiguity) {
        result.suggestions.push('Enhance clarity by using more precise language and defining key terms');
      }
    }

    return result;
  } catch (error) {
    console.error('Error validating reasoning:', error);

    // Return a basic validation result in case of error
    return {
      isValid: false,
      score: 0.3,
      dimensions: {
        logicalConsistency: 0.3,
        evidenceSupport: 0.3,
        completeness: 0.3,
        relevance: 0.3,
        clarity: 0.3
      },
      issues: [{
        type: 'logical_error',
        description: 'Error occurred during validation',
        severity: 'high'
      }],
      strengths: [],
      suggestions: ['Retry reasoning with more structured approach']
    };
  }
}

/**
 * Cross-validate multiple reasoning components for consistency
 *
 * @param reasonings - Array of reasoning components to cross-validate
 * @returns Cross-validation result with detailed analysis
 */
export function crossValidateReasoning(reasonings: EnhancedReasoning[]): CrossValidationResult {
  // Initialize cross-validation result
  const result: CrossValidationResult = {
    isPassed: false,
    consistencyScore: 0,
    conflicts: [],
    agreements: [],
    suggestions: []
  };

  try {
    // Need at least 2 reasoning components to cross-validate
    if (!reasonings || reasonings.length < 2) {
      return {
        isPassed: true,
        consistencyScore: 1.0,
        conflicts: [],
        agreements: ['Only one reasoning component available, no cross-validation needed'],
        suggestions: []
      };
    }

    // Extract decisions for comparison
    const decisions = reasonings.map(r => ({
      agentId: r.agentId,
      decision: r.decision,
      confidence: r.confidence || 0.8
    }));

    // Check for direct conflicts in decisions
    const conflictPairs: Array<[number, number]> = [];

    for (let i = 0; i < decisions.length; i++) {
      for (let j = i + 1; j < decisions.length; j++) {
        // Simple string similarity check (can be enhanced with more sophisticated NLP)
        const similarity = calculateStringSimilarity(
          decisions[i].decision.toLowerCase(),
          decisions[j].decision.toLowerCase()
        );

        // If similarity is low, consider it a potential conflict
        if (similarity < 0.3) {
          conflictPairs.push([i, j]);

          result.conflicts.push({
            description: `Potential conflict between ${decisions[i].agentId} and ${decisions[j].agentId}`,
            components: [decisions[i].agentId, decisions[j].agentId],
            severity: 'medium',
            resolution: 'Review both reasoning chains and reconcile the differences'
          });
        } else if (similarity > 0.7) {
          // High similarity indicates agreement
          result.agreements.push(
            `Strong agreement between ${decisions[i].agentId} and ${decisions[j].agentId}`
          );
        }
      }
    }

    // Calculate consistency score based on conflicts
    const maxPossibleConflicts = (decisions.length * (decisions.length - 1)) / 2;
    const consistencyScore = 1 - (conflictPairs.length / maxPossibleConflicts);

    result.consistencyScore = consistencyScore;
    result.isPassed = consistencyScore >= 0.7;

    // Generate suggestions based on conflicts
    if (result.conflicts.length > 0) {
      result.suggestions.push('Review conflicting reasoning components and reconcile differences');
      result.suggestions.push('Consider additional evidence to resolve conflicts');
    } else {
      result.suggestions.push('Reasoning components are consistent, proceed with confidence');
    }

    return result;
  } catch (error) {
    console.error('Error cross-validating reasoning:', error);

    // Return a basic cross-validation result in case of error
    return {
      isPassed: false,
      consistencyScore: 0.5,
      conflicts: [{
        description: 'Error occurred during cross-validation',
        components: ['unknown'],
        severity: 'high'
      }],
      agreements: [],
      suggestions: ['Retry cross-validation with more structured approach']
    };
  }
}

// Helper functions for validation

/**
 * Calculate string similarity using Levenshtein distance
 */
function calculateStringSimilarity(str1: string, str2: string): number {
  if (str1 === str2) return 1.0;
  if (str1.length === 0 || str2.length === 0) return 0.0;

  const len1 = str1.length;
  const len2 = str2.length;

  // Initialize matrix with dimensions (len1+1) x (len2+1)
  const matrix: number[][] = Array(len1 + 1).fill(null).map(() => Array(len2 + 1).fill(0));

  // Fill the first row and column
  for (let i = 0; i <= len1; i++) matrix[i][0] = i;
  for (let j = 0; j <= len2; j++) matrix[0][j] = j;

  // Fill the rest of the matrix
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,      // deletion
        matrix[i][j - 1] + 1,      // insertion
        matrix[i - 1][j - 1] + cost // substitution
      );
    }
  }

  // Calculate similarity as 1 - normalized distance
  const maxLen = Math.max(len1, len2);
  return 1 - (matrix[len1][len2] / maxLen);
}

/**
 * Validate logical consistency of reasoning
 */
function validateLogicalConsistency(reasoning: EnhancedReasoning): {
  score: number;
  issues: Array<{type: 'logical_error', description: string, severity: string}>;
  strengths: string[];
} {
  const result = {
    score: 0.8, // Default score
    issues: [] as Array<{type: 'logical_error', description: string, severity: string}>,
    strengths: [] as string[]
  };

  // Check if reasoning has steps
  if (!reasoning.steps || reasoning.steps.length === 0) {
    result.score = 0.5;
    result.issues.push({
      type: 'logical_error',
      description: 'Reasoning lacks explicit steps',
      severity: 'medium'
    });
    return result;
  }

  // Check for contradictions between steps
  let contradictionFound = false;
  for (let i = 0; i < reasoning.steps.length; i++) {
    for (let j = i + 1; j < reasoning.steps.length; j++) {
      // Simple contradiction check (can be enhanced with more sophisticated NLP)
      if (reasoning.steps[i].includes('not') && reasoning.steps[j].includes(reasoning.steps[i].replace('not', '').trim())) {
        contradictionFound = true;
        result.issues.push({
          type: 'logical_error',
          description: `Potential contradiction between step ${i+1} and step ${j+1}`,
          severity: 'high'
        });
      }
    }
  }

  // Check if conclusion follows from steps
  const conclusionFollows = reasoning.steps.some(step =>
    calculateStringSimilarity(step, reasoning.decision) > 0.5
  );

  if (!conclusionFollows) {
    result.issues.push({
      type: 'logical_error',
      description: 'Conclusion may not directly follow from reasoning steps',
      severity: 'medium'
    });
  }

  // Adjust score based on issues
  if (contradictionFound) {
    result.score = 0.5;
  } else if (!conclusionFollows) {
    result.score = 0.7;
  } else {
    result.score = 0.9;
    result.strengths.push('Reasoning demonstrates strong logical consistency');
  }

  return result;
}

/**
 * Validate evidence support in reasoning
 */
function validateEvidenceSupport(reasoning: EnhancedReasoning): {
  score: number;
  issues: Array<{type: 'evidence_gap', description: string, severity: string}>;
  strengths: string[];
} {
  const result = {
    score: 0.7, // Default score
    issues: [] as Array<{type: 'evidence_gap', description: string, severity: string}>,
    strengths: [] as string[]
  };

  // Check for supporting evidence
  const hasExplicitEvidence = reasoning.supportingEvidence && reasoning.supportingEvidence.length > 0;

  // Check for evidence-like statements in steps
  const evidencePatterns = [
    /research shows/i, /studies indicate/i, /according to/i, /evidence suggests/i,
    /data shows/i, /statistics reveal/i, /example:/i, /instance:/i, /case study:/i
  ];

  let evidenceCount = 0;
  if (reasoning.steps) {
    for (const step of reasoning.steps) {
      for (const pattern of evidencePatterns) {
        if (pattern.test(step)) {
          evidenceCount++;
          break;
        }
      }
    }
  }

  // Evaluate evidence support
  if (hasExplicitEvidence) {
    result.score = 0.9;
    result.strengths.push('Reasoning includes explicit supporting evidence');
  } else if (evidenceCount > 2) {
    result.score = 0.8;
    result.strengths.push('Reasoning contains multiple evidence-based statements');
  } else if (evidenceCount > 0) {
    result.score = 0.7;
    result.strengths.push('Reasoning includes some evidence-based statements');
  } else {
    result.score = 0.5;
    result.issues.push({
      type: 'evidence_gap',
      description: 'Reasoning lacks supporting evidence',
      severity: 'medium'
    });
  }

  return result;
}

/**
 * Validate completeness of reasoning
 */
function validateCompleteness(reasoning: EnhancedReasoning): {
  score: number;
  issues: Array<{type: 'incompleteness', description: string, severity: string}>;
  strengths: string[];
} {
  const result = {
    score: 0.7, // Default score
    issues: [] as Array<{type: 'incompleteness', description: string, severity: string}>,
    strengths: [] as string[]
  };

  // Check if reasoning addresses all aspects of the question
  if (!reasoning.question) {
    result.score = 0.6;
    result.issues.push({
      type: 'incompleteness',
      description: 'Original question not included in reasoning',
      severity: 'low'
    });
    return result;
  }

  // Extract key terms from question
  const questionStr = typeof reasoning.question === 'string'
    ? reasoning.question
    : JSON.stringify(reasoning.question);

  const keyTerms = extractKeyTerms(questionStr);

  // Check if steps address key terms
  const addressedTerms = new Set<string>();

  if (reasoning.steps) {
    for (const step of reasoning.steps) {
      for (const term of keyTerms) {
        if (step.toLowerCase().includes(term.toLowerCase())) {
          addressedTerms.add(term);
        }
      }
    }
  }

  // Calculate completeness ratio
  const completenessRatio = keyTerms.length > 0
    ? addressedTerms.size / keyTerms.length
    : 0.5;

  // Evaluate completeness
  if (completenessRatio > 0.9) {
    result.score = 0.9;
    result.strengths.push('Reasoning comprehensively addresses all aspects of the question');
  } else if (completenessRatio > 0.7) {
    result.score = 0.8;
    result.strengths.push('Reasoning addresses most aspects of the question');
  } else if (completenessRatio > 0.5) {
    result.score = 0.7;
    result.strengths.push('Reasoning addresses key aspects of the question');
  } else {
    result.score = 0.5;
    result.issues.push({
      type: 'incompleteness',
      description: 'Reasoning fails to address several key aspects of the question',
      severity: 'medium'
    });
  }

  return result;
}

/**
 * Validate relevance of reasoning
 */
function validateRelevance(reasoning: EnhancedReasoning): {
  score: number;
  issues: Array<{type: 'irrelevance', description: string, severity: string}>;
  strengths: string[];
} {
  const result = {
    score: 0.8, // Default score
    issues: [] as Array<{type: 'irrelevance', description: string, severity: string}>,
    strengths: [] as string[]
  };

  // Check if reasoning is relevant to the question
  if (!reasoning.question || !reasoning.steps || reasoning.steps.length === 0) {
    result.score = 0.6;
    result.issues.push({
      type: 'irrelevance',
      description: 'Cannot assess relevance due to missing question or steps',
      severity: 'medium'
    });
    return result;
  }

  // Extract key terms from question
  const questionStr = typeof reasoning.question === 'string'
    ? reasoning.question
    : JSON.stringify(reasoning.question);

  const keyTerms = extractKeyTerms(questionStr);

  // Calculate relevance score based on term frequency in steps
  let totalRelevanceScore = 0;

  for (const step of reasoning.steps) {
    let stepRelevanceScore = 0;

    for (const term of keyTerms) {
      if (step.toLowerCase().includes(term.toLowerCase())) {
        stepRelevanceScore += 1;
      }
    }

    totalRelevanceScore += stepRelevanceScore / Math.max(1, keyTerms.length);
  }

  // Normalize relevance score
  const normalizedRelevanceScore = reasoning.steps.length > 0
    ? totalRelevanceScore / reasoning.steps.length
    : 0;

  // Evaluate relevance
  if (normalizedRelevanceScore > 0.7) {
    result.score = 0.9;
    result.strengths.push('Reasoning is highly relevant to the question');
  } else if (normalizedRelevanceScore > 0.4) {
    result.score = 0.7;
    result.strengths.push('Reasoning is mostly relevant to the question');
  } else {
    result.score = 0.5;
    result.issues.push({
      type: 'irrelevance',
      description: 'Reasoning contains significant irrelevant content',
      severity: 'medium'
    });
  }

  return result;
}

/**
 * Validate clarity of reasoning
 */
function validateClarity(reasoning: EnhancedReasoning): {
  score: number;
  issues: Array<{type: 'ambiguity', description: string, severity: string}>;
  strengths: string[];
} {
  const result = {
    score: 0.8, // Default score
    issues: [] as Array<{type: 'ambiguity', description: string, severity: string}>,
    strengths: [] as string[]
  };

  // Check if reasoning steps exist
  if (!reasoning.steps || reasoning.steps.length === 0) {
    result.score = 0.5;
    result.issues.push({
      type: 'ambiguity',
      description: 'Reasoning lacks explicit steps',
      severity: 'medium'
    });
    return result;
  }

  // Check for ambiguous language
  const ambiguousPatterns = [
    /maybe/i, /perhaps/i, /possibly/i, /might/i, /could be/i,
    /unclear/i, /ambiguous/i, /not sure/i, /uncertain/i
  ];

  let ambiguityCount = 0;

  for (const step of reasoning.steps) {
    for (const pattern of ambiguousPatterns) {
      if (pattern.test(step)) {
        ambiguityCount++;
        break;
      }
    }
  }

  // Check for clear structure
  const hasStructure = reasoning.steps.some(step =>
    /first|second|third|finally|in conclusion|therefore/i.test(step)
  );

  // Evaluate clarity
  if (ambiguityCount > reasoning.steps.length / 3) {
    result.score = 0.6;
    result.issues.push({
      type: 'ambiguity',
      description: 'Reasoning contains significant ambiguous language',
      severity: 'medium'
    });
  } else if (!hasStructure) {
    result.score = 0.7;
    result.issues.push({
      type: 'ambiguity',
      description: 'Reasoning lacks clear structural markers',
      severity: 'low'
    });
  } else {
    result.score = 0.9;
    result.strengths.push('Reasoning is clear and well-structured');
  }

  return result;
}

/**
 * Extract key terms from a question string
 */
function extractKeyTerms(question: string): string[] {
  // Remove common stop words
  const stopWords = [
    'a', 'an', 'the', 'and', 'or', 'but', 'if', 'then', 'else', 'when',
    'at', 'from', 'by', 'for', 'with', 'about', 'against', 'between',
    'into', 'through', 'during', 'before', 'after', 'above', 'below',
    'to', 'of', 'in', 'on', 'is', 'are', 'was', 'were', 'be', 'been',
    'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would',
    'shall', 'should', 'can', 'could', 'may', 'might', 'must', 'this',
    'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they'
  ];

  // Tokenize and filter
  const tokens = question.toLowerCase()
    .replace(/[^\w\s]/g, '') // Remove punctuation
    .split(/\s+/) // Split by whitespace
    .filter(token =>
      token.length > 2 && // Filter out short tokens
      !stopWords.includes(token) // Filter out stop words
    );

  // Return unique tokens
  return [...new Set(tokens)];
}

/**
 * Transform reasoning data to a format compatible with AgentReasoningPanel
 * Updated to support LangGraph-inspired reasoning patterns
 *
 * @param reasoning - The reasoning data from the agent (enhanced or legacy)
 * @returns The transformed reasoning data for UI display
 */
export function transformReasoningForUI(reasoning: EnhancedReasoning | Reasoning): any {
  // Handle enhanced reasoning format (including LangGraph-inspired patterns)
  if ('context' in reasoning || ('considerations' in reasoning &&
      Array.isArray(reasoning.considerations) &&
      reasoning.considerations.length > 0 &&
      typeof reasoning.considerations[0] === 'string')) {

    const enhancedReasoning = reasoning as EnhancedReasoning;

    // Extract context information for display
    let contextDisplay = '';
    if (typeof enhancedReasoning.context === 'string') {
      contextDisplay = enhancedReasoning.context;
    } else if (enhancedReasoning.context && typeof enhancedReasoning.context === 'object') {
      // Handle both formats for context
      if ('description' in enhancedReasoning.context) {
        contextDisplay = enhancedReasoning.context.description as string;
      } else {
        try {
          contextDisplay = JSON.stringify(enhancedReasoning.context, null, 2);
        } catch (e) {
          contextDisplay = 'Complex context object';
        }
      }
    }

    // Handle reasoning steps and chain-of-thought
    const reasoningSteps = [];

    // Add question analysis if available
    if (enhancedReasoning.questionAnalysis) {
      const qa = enhancedReasoning.questionAnalysis;
      reasoningSteps.push(
        `Question analyzed as ${qa.type || 'general'} type, ${qa.category || 'standard'} category with ${qa.complexity || 'medium'} complexity`
      );
    }

    // Add context analysis if available
    if (enhancedReasoning.contextAnalysis) {
      const ca = enhancedReasoning.contextAnalysis;
      if (typeof ca.completeness === 'number') {
        reasoningSteps.push(`Context completeness: ${(ca.completeness * 100).toFixed(0)}%`);
      }
      if (ca.availableDomains && ca.availableDomains.length > 0) {
        reasoningSteps.push(`Available knowledge domains: ${ca.availableDomains.join(', ')}`);
      }
      if (ca.missingInformation && ca.missingInformation.length > 0) {
        reasoningSteps.push(`Missing information: ${ca.missingInformation.join(', ')}`);
      }
    }

    // Add reasoning steps if available
    if (Array.isArray(enhancedReasoning.steps) && enhancedReasoning.steps.length > 0) {
      reasoningSteps.push(...enhancedReasoning.steps);
    }

    // Add supporting evidence if available
    if (Array.isArray(enhancedReasoning.supportingEvidence) && enhancedReasoning.supportingEvidence.length > 0) {
      reasoningSteps.push('Supporting evidence:');
      reasoningSteps.push(...enhancedReasoning.supportingEvidence.map(e => `- ${e}`));
    }

    // Add insights if available
    if (Array.isArray(enhancedReasoning.insights) && enhancedReasoning.insights.length > 0) {
      reasoningSteps.push('Key insights:');
      reasoningSteps.push(...enhancedReasoning.insights.map(i => `- ${i}`));
    }

    return {
      process: enhancedReasoning.process || 'Enhanced Reasoning',
      context: contextDisplay,
      thoughts: Array.isArray(enhancedReasoning.thoughts)
        ? enhancedReasoning.thoughts.map(t => ({ content: typeof t === 'string' ? t : '' }))
        : [],
      considerations: Array.isArray(enhancedReasoning.considerations)
        ? enhancedReasoning.considerations.map(c => {
            if (typeof c === 'string') {
              return { factor: c, impact: 'medium', explanation: '' };
            } else {
              // This should never happen in enhanced reasoning, but just in case
              return {
                factor: c.factor || '',
                impact: c.impact || 'medium',
                explanation: c.explanation || ''
              };
            }
          })
        : [],
      steps: reasoningSteps,
      decision: enhancedReasoning.decision,
      confidence: enhancedReasoning.confidence || 0.85,
      timestamp: enhancedReasoning.timestamp,
      // Add LangGraph-inspired metadata for debugging and transparency
      metadata: {
        questionAnalysis: enhancedReasoning.questionAnalysis || {},
        contextAnalysis: enhancedReasoning.contextAnalysis || {},
        confidenceCalculation: enhancedReasoning.confidenceCalculation || {}
      }
    };
  }

  // Handle legacy reasoning format
  const legacyReasoning = reasoning as Reasoning;
  return {
    process: legacyReasoning.process || 'Agent Reasoning',
    thoughts: Array.isArray(legacyReasoning.thoughts)
      ? legacyReasoning.thoughts.map(t => {
          // Handle string or object thoughts
          if (typeof t === 'string') {
            return {
              content: t,
              confidence: 0.8
            };
          }
          return {
            content: t.content || (t as any).thought || '',
            confidence: t.confidence || 0.8
          };
        })
      : [],
    considerations: Array.isArray(legacyReasoning.considerations)
      ? legacyReasoning.considerations.map(c => {
          // Handle string or object considerations
          if (typeof c === 'string') {
            return {
              factor: c,
              impact: 'medium',
              explanation: ''
            };
          }
          return {
            factor: c.factor || '',
            impact: c.impact || 'medium',
            explanation: c.explanation || (c.content ? String(c.content) : '')
          };
        })
      : [],
    decision: legacyReasoning.decision,
    timestamp: legacyReasoning.timestamp,
    confidence: legacyReasoning.confidence || 0.85
  };
}

/**
 * Get all reasoning data for a specific agent from the collaboration state
 *
 * @param state - The collaboration state
 * @param agentId - The agent ID to filter by
 * @returns Array of reasoning data for the specified agent
 */
export function getAgentReasoningHistory(
  state: IterativeCollaborationState,
  agentId: string
): any[] {
  if (!state.decisions) {
    return [];
  }

  return state.decisions
    .filter(d => d.agent === agentId) // Use agent instead of agentId based on Decision interface
    .map(decision => {
      // If we have the original reasoning, use that
      if (decision.originalReasoning) {
        // Check if it's an enhanced reasoning
        if ('context' in decision.originalReasoning) {
          const enhancedReasoning = decision.originalReasoning as EnhancedReasoning;
          return {
            id: decision.id,
            timestamp: decision.timestamp,
            process: enhancedReasoning.process || 'Enhanced Reasoning',
            context: enhancedReasoning.context || '',
            thoughts: enhancedReasoning.thoughts?.map(t => ({ content: typeof t === 'string' ? t : '' })) || [],
            considerations: enhancedReasoning.considerations?.map(c => {
              if (typeof c === 'string') {
                return { factor: c, impact: 'medium', explanation: '' };
              } else {
                return {
                  factor: c.factor || '',
                  impact: c.impact || 'medium',
                  explanation: c.explanation || c.content || ''
                };
              }
            }) || [],
            steps: enhancedReasoning.steps || [],
            decision: enhancedReasoning.decision,
            confidence: enhancedReasoning.confidence || 0.85
          };
        } else {
          // Legacy reasoning
          const legacyReasoning = decision.originalReasoning as Reasoning;
          return {
            id: decision.id,
            timestamp: decision.timestamp,
            process: legacyReasoning.process,
            thoughts: legacyReasoning.thoughts.map(t => ({
              content: 'content' in t ? t.content : (t as any).thought || ''
            })),
            considerations: legacyReasoning.considerations.map(c => ({
              factor: c.factor || '',
              impact: c.impact || 'medium',
              explanation: c.explanation || c.content || ''
            })),
            decision: legacyReasoning.decision,
            confidence: legacyReasoning.confidence || 0.85
          };
        }
      }

      // If we don't have original reasoning, try to reconstruct from what we have
      let reasoningData: any = {};

      // Try to parse the reasoning string if it's JSON
      if (typeof decision.reasoning === 'string') {
        try {
          reasoningData = JSON.parse(decision.reasoning);
        } catch (e) {
          // If parsing fails, use the string as is for thoughts
          reasoningData = {
            process: 'Reasoning Process',
            thoughts: [decision.reasoning],
            considerations: [],
            decision: decision.outcome || ''
          };
        }
      } else {
        // If reasoning is not a string, use it directly
        reasoningData = decision.reasoning || {};
      }

      return {
        id: decision.id,
        timestamp: decision.timestamp,
        process: reasoningData.process || 'Agent Reasoning',
        thoughts: Array.isArray(reasoningData.thoughts)
          ? reasoningData.thoughts.map((t: string) => ({ content: t }))
          : [],
        considerations: Array.isArray(reasoningData.considerations)
          ? reasoningData.considerations.map((c: string) => {
              if (typeof c === 'string' && c.includes(': ') && c.includes(' - ')) {
                const parts = c.split(' - ');
                const factorParts = parts[0].split(': ');
                return {
                  factor: factorParts[0] || '',
                  impact: factorParts[1] || 'medium',
                  explanation: parts.length > 1 ? parts[1] : ''
                };
              } else {
                return { factor: c || '', impact: 'medium', explanation: '' };
              }
            })
          : [],
        decision: reasoningData.decision || decision.outcome || '',
        confidence: decision.confidence || 0.85
      };
    });
}
