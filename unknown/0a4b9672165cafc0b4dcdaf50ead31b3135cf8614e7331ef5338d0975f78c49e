// src/app/(payload)/api/content-publication/route.ts
import { NextResponse } from 'next/server';
import configPromise from '@payload-config';
import { getPayload } from 'payload';

// Main API handler
export async function POST(request: Request) {
  try {
    // Parse request
    const { content, status } = await request.json();
    
    // Validate request
    if (!content) {
      return NextResponse.json({ success: false, error: 'Content is required' }, { status: 400 });
    }

    // Get Payload CMS instance
    const payload = await getPayload({
      config: configPromise,
    });

    // Determine collection and prepare data based on content type
    let collectionSlug;
    let data;
    
    switch (content.contentType) {
      case 'blog-article':
        collectionSlug = 'posts'; // Assuming you have a posts collection
        data = {
          title: content.title,
          content: content.sections,
          metaDescription: content.metaDescription,
          status,
          // Other fields
        };
        break;
        
      case 'product-page':
        collectionSlug = 'products'; // Assuming you have a products collection
        data = {
          title: content.title,
          overview: content.sections.find(s => s.type === 'text' && s.title.toLowerCase().includes('overview'))?.content || '',
          features: content.sections.filter(s => s.type === 'list' && s.title.toLowerCase().includes('feature')),
          pricing: content.sections.find(s => s.type === 'text' && s.title.toLowerCase().includes('pricing'))?.content || '',
          metaDescription: content.metaDescription,
          status,
          // Other fields
        };
        break;
        
      case 'buying-guide':
        collectionSlug = 'guides'; // Assuming you have a guides collection
        data = {
          title: content.title,
          introduction: content.sections.find(s => s.type === 'text' && s.title.toLowerCase().includes('introduction'))?.content || '',
          considerations: content.sections.filter(s => s.type === 'list' && s.title.toLowerCase().includes('consideration')),
          recommendations: content.sections.filter(s => s.type === 'text' && s.title.toLowerCase().includes('recommendation')),
          metaDescription: content.metaDescription,
          status,
          // Other fields
        };
        break;
        
      default:
        return NextResponse.json({ success: false, error: 'Invalid content type' }, { status: 400 });
    }

    // Create the document
    const result = await payload.create({
      collection: collectionSlug,
      data
    });

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Content published successfully',
      documentId: result.id
    });

  } catch (error) {
    console.error('Error in content publication:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      }, 
      { status: 500 }
    );
  }
}