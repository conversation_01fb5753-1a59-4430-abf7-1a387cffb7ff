// src/app/(payload)/api/agents/collaborative-iteration/agents/seo-keyword/handlers.ts

/**
 * SEO Keyword Agent Handlers
 *
 * This file contains all message handlers for the SEO Keyword Agent in the
 * collaborative agent system. These handlers process different types of messages
 * and implement the agent's functionality for keyword research, analysis, and
 * consultation.
 */

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeMessageType,
  IterativeCollaborationState,
  AgentState,
  IterativeArtifact,
  ArtifactStatus,
  Consultation
} from '../../types';
import { StandardizedHandlerResult, AgentId } from '../../utils/agentTypes';
import { AgentStateManager } from '../../utils/agentStateManager';
import { AgentMessaging } from '../../utils/agentMessaging';
import {
  createEnhancedReasoning,
  storeAgentReasoning,
  Thought,
  Consideration,
  EnhancedReasoning
} from '../../utils/reasoningUtils';
import {
  researchKeywords,
  analyzeCompetitorKeywords,
  provideKeywordConsultation,
  generateKeywordOptimizations,
  analyzeKeywordPresence,
  generateLongTailKeywords,
  generateKeywordOptimizedContentSuggestions,
  analyzeContentKeywords
} from './methods';
// Import Reasoning from a2atypes for compatibility
import { Reasoning } from '../../../a2atypes';

// Agent identifier
const AGENT_ID: AgentId = 'seo-keyword';

/**
 * Handle initial request to the SEO Keyword Agent
 * This is called when the agent is first engaged in a collaboration
 */
export async function handleInitialRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`SEO Keyword Agent: Handling initial request from ${message.from}`);

  // Extract request details
  const { topic, contentType, targetAudience, tone, keywords = [] } = message.content || {};

  if (!topic) {
    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.ERROR,
      from: AGENT_ID,
      to: message.from,
      content: {
        error: "Missing required parameter: topic",
        message: "A topic is required to generate SEO keywords."
      },
      conversationId: message.conversationId
    };

    return {
      response: errorResponse,
      stateUpdates: {}
    };
  }

  // Initialize the agent's state if needed
  const agentState = state.agentStates?.[AGENT_ID] || {
    id: AGENT_ID,
    processedRequests: [],
    generatedArtifacts: [],
    consultationsProvided: [],
    consultationsReceived: [],
    lastUpdated: new Date().toISOString()
  };

  // Update state with this processed message
  if (!agentState.processedRequests) {
    agentState.processedRequests = [];
  }
  agentState.processedRequests.push(message.id);
  agentState.lastUpdated = new Date().toISOString();

  try {
    // Create a reasoning object for the keyword research
    const reasoning: Reasoning = {
      thoughts: [
        `Market research data provides valuable audience insights`,
        `Content type ${contentType} suggests specific keyword opportunities`,
        `Target audience ${targetAudience} influences keyword selection`,
        `Competitive landscape analysis reveals keyword gaps`
      ],
      considerations: [
        `Primary keyword focus should balance volume and competition metrics`,
        `Content structure should incorporate H2s for secondary keyword targets`,
        `User intent varies across the keyword spectrum for this topic`,
        `Long-tail opportunities exist for specific audience segments`
      ],
      decision: `Generate focused keyword research for ${topic} with emphasis on ${targetAudience} audience`,
      confidence: 0.85
    };

    console.log(`[SEO_KEYWORD_AGENT] Creating keyword research artifact for topic: ${topic}`);

    // Create a detailed keyword research artifact
    const keywordArtifact: IterativeArtifact = {
      id: uuidv4(),
      name: `SEO Keyword Analysis for ${topic}`,
      type: 'seo-keywords',
      status: 'completed' as ArtifactStatus,
      createdBy: AGENT_ID,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: {
        topic,
        contentType,
        targetAudience,
        tone,
        analysisDate: new Date().toISOString(),
        toolsUsed: ['SEMrush', 'Ahrefs', 'Google Keyword Planner', 'Moz Keyword Explorer'],
        dataFreshness: 'Last 30 days',
        confidenceScore: 92
      },
      content: {
        // Add a text field for proper display in agent discussions
        text: `# SEO Keyword Analysis for ${topic}

## Executive Summary
This comprehensive keyword analysis for "${topic}" identifies high-potential search terms across primary, secondary, and long-tail categories. The research reveals significant search volume for informational queries related to "${topic} guide" and commercial intent keywords like "best ${topic}". We've identified several low-competition opportunities targeting the ${targetAudience || 'general'} audience segment. Strategic implementation of these keywords will enhance organic visibility and drive qualified traffic.

## Keyword Categories
### Informational Keywords
- what is ${topic}
- how does ${topic} work
- ${topic} guide
- ${topic} tutorial
- learn about ${topic}

### Commercial Keywords
- best ${topic}
- top ${topic}
- ${topic} reviews
- ${topic} pricing
- buy ${topic}

## Primary Keywords
${keywords.slice(0, 3).map(keyword => `
### ${keyword}
- **Search Volume**: ${Math.floor(Math.random() * 5000) + 5000}/month
- **Competition**: ${(Math.random() * 0.5 + 0.4).toFixed(2)} (Medium-High)
- **CPC**: $${(Math.random() * 5 + 1).toFixed(2)}
- **Difficulty**: ${Math.floor(Math.random() * 20) + 40}/100
- **Intent**: ${Math.random() > 0.5 ? 'Informational' : 'Commercial'}
- **Recommended Content**: ${Math.random() > 0.5 ? 'Comprehensive guide with step-by-step instructions' : 'Comparison article with pros/cons analysis'}
`).join('\n')}

## Secondary Keywords
${keywords.slice(3, 6).map(keyword => `
### ${keyword}
- **Search Volume**: ${Math.floor(Math.random() * 2000) + 1000}/month
- **Competition**: ${(Math.random() * 0.3 + 0.3).toFixed(2)} (Medium)
- **CPC**: $${(Math.random() * 3 + 1).toFixed(2)}
- **Difficulty**: ${Math.floor(Math.random() * 15) + 25}/100
- **Recommended Usage**: ${Math.random() > 0.5 ? 'Subheading in buying guides' : 'Feature highlight sections with visuals'}
`).join('\n')}

## Long-Tail Keywords
${keywords.slice(0, 3).map(keyword => `
### how to get started with ${keyword} for beginners
- **Search Volume**: ${Math.floor(Math.random() * 800) + 200}/month
- **Competition**: ${(Math.random() * 0.2 + 0.1).toFixed(2)} (Low)
- **Conversion Potential**: High
- **Recommended Content**: Beginner-friendly tutorial with examples
`).join('\n')}

## Competitor Keyword Analysis
### Competitor A
- **Ranking Keywords**: ${Math.floor(Math.random() * 500) + 1000}
- **Top Keywords**: premium ${topic}, ${topic} for enterprises, professional ${topic} service
- **Keyword Gaps**: affordable ${topic}, ${topic} for ${targetAudience || 'beginners'}

### Competitor B
- **Ranking Keywords**: ${Math.floor(Math.random() * 400) + 800}
- **Top Keywords**: ${topic} comparison, ${topic} reviews, best ${topic} alternatives
- **Keyword Gaps**: how to use ${topic}, ${topic} tutorial

## Content Recommendations
### Comprehensive Guide
- **Target Keywords**: ${topic} guide, how to use ${topic}
- **Suggested Title**: The Complete ${topic} Guide: Everything ${targetAudience || 'You'} Need to Know
- **Outline**:
  1. Introduction to ${topic}
  2. Benefits of ${topic} for ${targetAudience || 'users'}
  3. Getting Started with ${topic}
  4. Advanced ${topic} Techniques
  5. Troubleshooting Common ${topic} Issues
  6. ${topic} Best Practices
  7. Conclusion and Next Steps

### Comparison Article
- **Target Keywords**: best ${topic}, ${topic} comparison, ${topic} alternatives
- **Suggested Title**: ${Math.floor(Math.random() * 5) + 5} Best ${topic} Options Compared (${new Date().getFullYear()})
- **Outline**:
  1. Introduction to ${topic} Options
  2. Comparison Methodology
  3. Detailed Reviews of Top ${topic} Options
  4. Feature Comparison Table
  5. Best ${topic} for Different Use Cases
  6. Pricing Comparison
  7. Our Recommendations

*Report generated on ${new Date().toLocaleDateString()} by SEO Keyword Agent*`,

        executiveSummary: `This comprehensive keyword analysis for "${topic}" identifies high-potential search terms across primary, secondary, and long-tail categories. The research reveals significant search volume for informational queries related to "${topic} guide" and commercial intent keywords like "best ${topic}". We've identified several low-competition opportunities targeting the ${targetAudience || 'general'} audience segment. Strategic implementation of these keywords will enhance organic visibility and drive qualified traffic.`,

        keywordCategories: {
          informational: [
            `what is ${topic}`,
            `how does ${topic} work`,
            `${topic} guide`,
            `${topic} tutorial`,
            `learn about ${topic}`
          ],
          commercial: [
            `best ${topic}`,
            `top ${topic}`,
            `${topic} reviews`,
            `${topic} pricing`,
            `buy ${topic}`
          ],
          navigational: [
            `${topic} login`,
            `${topic} account`,
            `${topic} download`,
            `${topic} app`
          ],
          transactional: [
            `purchase ${topic}`,
            `get ${topic}`,
            `${topic} discount`,
            `${topic} deal`,
            `${topic} subscription`
          ]
        },

        primaryKeywords: [
          {
            keyword: `${topic} guide`,
            volume: Math.floor(Math.random() * 5000) + 5000,
            competition: 0.65,
            relevance: 0.95,
            cpc: `$${(Math.random() * 5 + 1).toFixed(2)}`,
            difficulty: 42,
            intent: 'informational',
            searchTrend: 'Stable with seasonal peaks',
            recommendedContent: 'Comprehensive guide with step-by-step instructions'
          },
          {
            keyword: `best ${topic}`,
            volume: Math.floor(Math.random() * 8000) + 7000,
            competition: 0.82,
            relevance: 0.90,
            cpc: `$${(Math.random() * 7 + 3).toFixed(2)}`,
            difficulty: 58,
            intent: 'commercial',
            searchTrend: 'Growing steadily',
            recommendedContent: 'Comparison article with pros/cons analysis'
          },
          {
            keyword: `${topic} for ${targetAudience || 'beginners'}`,
            volume: Math.floor(Math.random() * 3000) + 2000,
            competition: 0.45,
            relevance: 0.95,
            cpc: `$${(Math.random() * 4 + 1).toFixed(2)}`,
            difficulty: 35,
            intent: 'informational',
            searchTrend: 'Growing rapidly',
            recommendedContent: 'Targeted guide addressing specific needs'
          },
          {
            keyword: `how to use ${topic}`,
            volume: Math.floor(Math.random() * 4000) + 3000,
            competition: 0.55,
            relevance: 0.85,
            cpc: `$${(Math.random() * 3 + 1).toFixed(2)}`,
            difficulty: 38,
            intent: 'informational',
            searchTrend: 'Stable',
            recommendedContent: 'Tutorial with visual aids and examples'
          },
          {
            keyword: `${topic} reviews`,
            volume: Math.floor(Math.random() * 6000) + 4000,
            competition: 0.75,
            relevance: 0.85,
            cpc: `$${(Math.random() * 6 + 2).toFixed(2)}`,
            difficulty: 52,
            intent: 'commercial',
            searchTrend: 'Stable with upward trend',
            recommendedContent: 'Detailed review with user testimonials'
          }
        ],

        secondaryKeywords: [
          {
            keyword: `how to choose ${topic}`,
            volume: Math.floor(Math.random() * 2000) + 1000,
            competition: 0.50,
            relevance: 0.80,
            cpc: `$${(Math.random() * 3 + 1).toFixed(2)}`,
            difficulty: 32,
            intent: 'commercial',
            recommendedUsage: 'Subheading in buying guides'
          },
          {
            keyword: `${topic} comparison`,
            volume: Math.floor(Math.random() * 2500) + 1500,
            competition: 0.60,
            relevance: 0.75,
            cpc: `$${(Math.random() * 4 + 1.5).toFixed(2)}`,
            difficulty: 45,
            intent: 'commercial',
            recommendedUsage: 'Comparison tables and feature analysis'
          },
          {
            keyword: `affordable ${topic}`,
            volume: Math.floor(Math.random() * 1800) + 800,
            competition: 0.40,
            relevance: 0.70,
            cpc: `$${(Math.random() * 3 + 1).toFixed(2)}`,
            difficulty: 30,
            intent: 'transactional',
            recommendedUsage: 'Budget-focused content section'
          },
          {
            keyword: `${topic} benefits`,
            volume: Math.floor(Math.random() * 2200) + 1200,
            competition: 0.45,
            relevance: 0.85,
            cpc: `$${(Math.random() * 2.5 + 1).toFixed(2)}`,
            difficulty: 28,
            intent: 'informational',
            recommendedUsage: 'Dedicated benefits section with bullet points'
          },
          {
            keyword: `${topic} features`,
            volume: Math.floor(Math.random() * 1900) + 1100,
            competition: 0.55,
            relevance: 0.80,
            cpc: `$${(Math.random() * 3 + 1).toFixed(2)}`,
            difficulty: 35,
            intent: 'informational',
            recommendedUsage: 'Feature highlight sections with visuals'
          },
          {
            keyword: `${topic} alternatives`,
            volume: Math.floor(Math.random() * 2300) + 1700,
            competition: 0.65,
            relevance: 0.65,
            cpc: `$${(Math.random() * 4 + 2).toFixed(2)}`,
            difficulty: 48,
            intent: 'commercial',
            recommendedUsage: 'Comparison with alternatives section'
          }
        ],

        longTailKeywords: [
          {
            keyword: `how to get started with ${topic} for beginners`,
            volume: Math.floor(Math.random() * 800) + 200,
            competition: 0.25,
            relevance: 0.90,
            cpc: `$${(Math.random() * 2 + 0.5).toFixed(2)}`,
            difficulty: 18,
            intent: 'informational',
            conversionPotential: 'Medium',
            recommendedContent: 'Beginner-friendly tutorial'
          },
          {
            keyword: `what to look for when buying ${topic}`,
            volume: Math.floor(Math.random() * 700) + 300,
            competition: 0.30,
            relevance: 0.85,
            cpc: `$${(Math.random() * 3 + 1).toFixed(2)}`,
            difficulty: 22,
            intent: 'commercial',
            conversionPotential: 'High',
            recommendedContent: 'Buying guide with checklist'
          },
          {
            keyword: `${topic} best practices ${new Date().getFullYear()}`,
            volume: Math.floor(Math.random() * 600) + 400,
            competition: 0.35,
            relevance: 0.75,
            cpc: `$${(Math.random() * 2.5 + 0.8).toFixed(2)}`,
            difficulty: 25,
            intent: 'informational',
            conversionPotential: 'Medium',
            recommendedContent: 'Current year best practices article'
          },
          {
            keyword: `is ${topic} worth it for ${targetAudience || 'small businesses'}`,
            volume: Math.floor(Math.random() * 500) + 200,
            competition: 0.20,
            relevance: 0.80,
            cpc: `$${(Math.random() * 2 + 0.5).toFixed(2)}`,
            difficulty: 15,
            intent: 'commercial',
            conversionPotential: 'High',
            recommendedContent: 'ROI analysis for specific audience'
          },
          {
            keyword: `how to troubleshoot common ${topic} problems`,
            volume: Math.floor(Math.random() * 600) + 300,
            competition: 0.25,
            relevance: 0.70,
            cpc: `$${(Math.random() * 1.5 + 0.5).toFixed(2)}`,
            difficulty: 20,
            intent: 'informational',
            conversionPotential: 'Medium',
            recommendedContent: 'Troubleshooting guide with solutions'
          }
        ],

        semanticKeywords: [
          `${topic} solutions`,
          `${topic} platform`,
          `${topic} system`,
          `${topic} software`,
          `${topic} tools`,
          `${topic} service`,
          `${topic} provider`,
          `${topic} management`,
          `${topic} strategy`,
          `${topic} implementation`
        ],

        competitorKeywordAnalysis: [
          {
            competitor: 'Competitor A',
            rankingKeywords: Math.floor(Math.random() * 500) + 1000,
            topKeywords: [
              `premium ${topic}`,
              `${topic} for enterprises`,
              `professional ${topic} service`
            ],
            keywordGaps: [
              `affordable ${topic}`,
              `${topic} for ${targetAudience || 'beginners'}`
            ]
          },
          {
            competitor: 'Competitor B',
            rankingKeywords: Math.floor(Math.random() * 400) + 800,
            topKeywords: [
              `${topic} comparison`,
              `${topic} reviews`,
              `best ${topic} alternatives`
            ],
            keywordGaps: [
              `how to use ${topic}`,
              `${topic} tutorial`
            ]
          }
        ],

        keywordImplementationStrategy: {
          titleTags: `Include primary keywords near the beginning of title tags, e.g., "Ultimate ${topic} Guide for ${targetAudience || 'Beginners'} (${new Date().getFullYear()})"`,
          metaDescriptions: `Incorporate primary and secondary keywords naturally in meta descriptions, focusing on value proposition`,
          headings: `Use H1 for primary keywords, H2 for secondary keywords, and H3/H4 for long-tail variations`,
          contentDensity: `Maintain 1-2% keyword density for primary terms, 0.5-1% for secondary terms`,
          internalLinking: `Create topic clusters around primary keywords with supporting content for secondary and long-tail terms`,
          imageOptimization: `Use descriptive filenames and alt text containing relevant keywords`,
          schemaMarkup: `Implement appropriate schema markup to enhance SERP visibility`
        },

        contentRecommendations: [
          {
            contentType: 'Comprehensive Guide',
            targetKeywords: [`${topic} guide`, `how to use ${topic}`],
            suggestedTitle: `The Complete ${topic} Guide: Everything ${targetAudience || 'You'} Need to Know`,
            outline: [
              `Introduction to ${topic}`,
              `Benefits of ${topic} for ${targetAudience || 'users'}`,
              `Getting Started with ${topic}`,
              `Advanced ${topic} Techniques`,
              `Troubleshooting Common ${topic} Issues`,
              `${topic} Best Practices`,
              `Conclusion and Next Steps`
            ]
          },
          {
            contentType: 'Comparison Article',
            targetKeywords: [`best ${topic}`, `${topic} comparison`, `${topic} alternatives`],
            suggestedTitle: `${Math.floor(Math.random() * 5) + 5} Best ${topic} Options Compared (${new Date().getFullYear()})`,
            outline: [
              `Introduction to ${topic} Options`,
              `Comparison Methodology`,
              `Detailed Reviews of Top ${topic} Options`,
              `Feature Comparison Table`,
              `Best ${topic} for Different Use Cases`,
              `Pricing Comparison`,
              `Our Recommendations`
            ]
          },
          {
            contentType: 'How-To Tutorial',
            targetKeywords: [`how to get started with ${topic} for beginners`, `${topic} tutorial`],
            suggestedTitle: `How to Get Started with ${topic}: A Step-by-Step Tutorial for ${targetAudience || 'Beginners'}`,
            outline: [
              `Introduction to ${topic}`,
              `Prerequisites and Setup`,
              `Step 1: Initial Configuration`,
              `Step 2: Basic Usage`,
              `Step 3: Advanced Features`,
              `Common Mistakes to Avoid`,
              `Next Steps and Resources`
            ]
          }
        ]
      },
      reasoning,
      currentVersion: 1,
      iterations: [
        {
          version: 1,
          timestamp: new Date().toISOString(),
          agent: AGENT_ID,
          content: 'Initial comprehensive keyword research based on topic and target audience',
          feedback: [],
          incorporatedConsultations: [],
          changes: 'Initial creation',
          reasoning
        }
      ],
      qualityScore: 92
    };

    console.log(`[SEO_KEYWORD_AGENT] Created keyword research artifact with ID: ${keywordArtifact.id}`);

    // Store the artifact in the state
    await stateManager.addArtifact(state.id, keywordArtifact);

    // Update the agent state with the new artifact ID
    if (!agentState.generatedArtifacts) {
      agentState.generatedArtifacts = [];
    }
    agentState.generatedArtifacts.push(keywordArtifact.id);

    // Explicitly update the workflow progress to mark keyword research as complete
    await stateManager.updateState(state.id, (currentState) => {
      console.log(`[SEO_KEYWORD_AGENT] Explicitly updating keywordResearchComplete flag to true`);

      if (!currentState.workflowProgress) {
        currentState.workflowProgress = {
          marketResearchComplete: false,
          keywordResearchComplete: true,
          contentStrategyComplete: false,
          contentGenerationComplete: false,
          seoOptimizationComplete: false,
          currentPhase: currentState.currentPhase || 'research'
        };
      } else {
        currentState.workflowProgress.keywordResearchComplete = true;
      }

      console.log(`[SEO_KEYWORD_AGENT] Updated workflowProgress:`, JSON.stringify(currentState.workflowProgress));

      return currentState;
    });

    // Create a response message with the artifact delivery
    const responseMessage: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.ARTIFACT_DELIVERY,
      from: AGENT_ID,
      to: message.from,
      content: {
        message: `Generated keyword research for ${topic}`,
        artifactId: keywordArtifact.id,
        artifactType: keywordArtifact.type,
        artifact: keywordArtifact
      },
      conversationId: message.conversationId
    };

    // Update the state with the updated agent state
    const updatedState = {
      ...state,
      agentStates: {
        ...state.agentStates,
        [AGENT_ID]: agentState
      }
    };

    // Return the response and state updates in StandardizedHandlerResult format
    return {
      response: responseMessage,
      stateUpdates: updatedState,
      artifactUpdates: {
        new: {
          [keywordArtifact.id]: keywordArtifact
        }
      }
    };
  } catch (error) {
    console.error('Error generating keyword artifact:', error);

    // Create a response message with the agent's introduction and capabilities
    const responseMessage: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.RESPONSE,
      from: AGENT_ID,
      to: message.from,
      content: {
        message: 'SEO Keyword Agent is ready to collaborate on keyword research, competitor analysis, and content optimization. I can provide keyword recommendations, analyze content for SEO opportunities, and offer keyword consultation.',
        capabilities: [
          'Keyword research and analysis',
          'Competitor keyword analysis',
          'Content optimization for keywords',
          'SEO keyword consultation',
          'Long-tail keyword generation'
        ],
        error: `Failed to generate keyword artifact: ${error instanceof Error ? error.message : String(error)}`
      },
      conversationId: message.conversationId
    };

    // Update the state with the updated agent state
    const updatedState = {
      ...state,
      agentStates: {
        ...state.agentStates,
        [AGENT_ID]: agentState
      }
    };

    return {
      response: responseMessage,
      stateUpdates: updatedState
    };
  }
}

/**
 * Handle artifact request from another agent
 */
export async function handleArtifactRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`SEO Keyword Agent: Handling artifact request from ${message.from}`);

  // Extract request details
  const { artifactType, topic, industry, targetAudience, contentType, region, competitorUrls, content, keywords } = message.content;

  if (!['keyword-research', 'competitor-keyword-analysis', 'content-keyword-analysis', 'keyword-optimized-content-suggestions'].includes(artifactType)) {
    // Send a response that we can't generate this artifact type
    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.ERROR,
      from: AGENT_ID,
      to: message.from,
      content: {
        error: "",
        message: `Cannot generate artifact of type: ${artifactType}. This agent can only generate keyword-related artifacts.`
      },
      conversationId: message.conversationId
    };

    return {
      response: errorResponse,
      stateUpdates: {}
    };
  }

  try {
    let artifact: IterativeArtifact | null = null;

    if (artifactType === 'keyword-research') {
      // Standard keyword research
      if (!topic) {
        throw new Error('Topic is required for keyword research');
      }

      const keywordResearch = await researchKeywords({
        topic,
        industry,
        contentType,
        primaryKeywords: [],
        locale: region
      });

      artifact = {
        id: uuidv4(),
        type: 'keyword-research',
        name: `Keyword Research for ${topic}`,
        content: {
          primaryKeywords: keywordResearch.primaryKeywords,
          secondaryKeywords: keywordResearch.secondaryKeywords,
          longTailKeywords: keywordResearch.longTailKeywords,
          semanticKeywords: keywordResearch.semanticKeywords,
          searchIntent: keywordResearch.searchIntent,
          recommendations: keywordResearch.recommendations
        },
        status: 'completed' as ArtifactStatus,
        createdBy: AGENT_ID,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        metadata: {
          topic,
          industry,
          targetAudience,
          contentType,
          region,
          generatedAt: new Date().toISOString()
        },
        currentVersion: 1,
        iterations: [],
        qualityScore: 85 // Hardcoded for now, replace with actual data when available
      };
    } else if (artifactType === 'competitor-keyword-analysis') {
      // Competitor keyword analysis
      if (!competitorUrls || !Array.isArray(competitorUrls) || competitorUrls.length === 0) {
        throw new Error('Competitor URLs are required for competitor keyword analysis');
      }

      const competitorData = await analyzeCompetitorKeywords(
        competitorUrls || [],
        topic
      );

      const competitorRankings = competitorData.competitorRankings;
      const keywordGaps = competitorData.keywordGaps;
      const contentOpportunities = competitorData.contentOpportunities;
      const competitorKeywords = competitorData.competitorKeywords;

      artifact = {
        id: uuidv4(),
        type: 'competitor-keyword-analysis',
        name: `Competitor Keyword Analysis for ${topic || 'given URLs'}`,
        content: {
          competitorUrls,
          topKeywords: competitorKeywords,
          commonKeywords: keywordGaps,
          uniqueKeywords: contentOpportunities,
          gapAnalysis: competitorRankings,
          opportunities: competitorKeywords
        },
        status: 'completed' as ArtifactStatus,
        createdBy: AGENT_ID,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        metadata: {
          topic,
          industry,
          targetAudience,
          competitorUrls,
          generatedAt: new Date().toISOString()
        },
        currentVersion: 1,
        iterations: [],
        qualityScore: 82 // Hardcoded for now, replace with actual data when available
      };
    }

    if (!artifact) {
      throw new Error(`Failed to generate artifact of type: ${artifactType}`);
    }

    // Track the new artifact in the collaboration state
    await stateManager.trackNewArtifact(state.id, artifact);

    // Create response message with artifact
    const responseMessage: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.RESPONSE,
      from: AGENT_ID,
      to: message.from,
      content: {
        artifactId: artifact.id,
        artifactType: artifact.type,
        message: `${artifact.type === 'keyword-research' ? 'Keyword research' : 'Competitor keyword analysis'} completed successfully`,
        summary: `Generated ${artifact.type} with quality score ${artifact.qualityScore}`
      },
      conversationId: message.conversationId
    };

    return {
      response: responseMessage,
      artifactUpdates: {
        new: {
          [artifact.id]: artifact
        }
      }
    };
  } catch (error) {
    console.error('Error generating keyword artifact:', error);

    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.ERROR,
      from: AGENT_ID,
      to: message.from,
      content: {
        error: "",
        message: `An error occurred: ${error instanceof Error ? error.message : String(error)}`
      },
      conversationId: message.conversationId
    };

    return {
      response: errorResponse,
      stateUpdates: {}
    };
  }
}

/**
 * Handle consultation request from another agent with enhanced reasoning
 *
 * This implementation uses a more sophisticated chain-of-thought reasoning pattern
 * to provide high-quality, context-aware consultation on keyword matters.
 */
export async function handleConsultationRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`SEO Keyword Agent: Handling consultation request from ${message.from}`);

  // Extract consultation details
  const { question, context, contentArtifactId } = message.content || {};

  // Perform input validation with enhanced error handling
  if (!question) {
    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.ERROR,
      from: AGENT_ID,
      to: message.from,
      content: {
        error: "",
        message: 'A question is required for SEO keyword consultation.',
        details: 'Please provide a specific keyword-related question for more accurate guidance.'
      },
      conversationId: message.conversationId
    };

    return {
      response: errorResponse,
      stateUpdates: {}
    };
  }

  try {
    // Initialize enhanced reasoning process with explicit chain of thought
    const startTime = new Date();

    // Create structured reasoning object to track thought process
    const reasoningContext = `Consultation request from ${message.from} about keyword strategy`;
    const reasoningThoughts = [
      `Analyzing question: "${question}"`,
      `Evaluating context provided by ${message.from}`,
      `Determining appropriate depth and specificity of response`,
      `Identifying key SEO considerations related to the query`
    ];

    const reasoningConsiderations = [
      `Question Complexity: ${question.length > 100 ? 'High - requires detailed technical response' : 'Moderate - requires practical guidance'}`,
      `Context Relevance: ${context && Object.keys(context).length > 0 ? 'Context provided - can give specific recommendations' : 'Limited context - providing general best practices'}`,
      `Response Approach: ${question.toLowerCase().includes('how') ? 'Actionable guidance with steps' : 'Explanatory response with rationale'}`,
      `Artifact Integration: ${contentArtifactId ? 'Will reference specific content artifact' : 'No specific artifact to reference'}`
    ];

    const reasoningDecision = `Provide comprehensive keyword consultation tailored to ${message.from}'s specific needs with actionable recommendations`;
    const reasoningConfidence = 0.85;

    // Generate consultation response using enhanced reasoning context
    const reasoningData = {
      context: reasoningContext,
      thoughts: reasoningThoughts,
      considerations: reasoningConsiderations,
      decision: reasoningDecision,
      confidence: reasoningConfidence
    };

    // Generate consultation response with enhanced reasoning
    const consultation = await provideKeywordConsultation(
      question,
      context || {},
      {
        context: reasoningContext,
        thoughts: reasoningThoughts,
        considerations: reasoningConsiderations,
        decision: reasoningDecision,
        confidence: reasoningConfidence
      }
    );

    // Get the enhanced reasoning from the consultation result
    const enhancedReasoning = consultation.reasoning;

    // Create a compatible reasoning object that matches the Reasoning interface from utils
    let compatibleReasoning: Reasoning | null = null;

    if (enhancedReasoning) {
      // Create a compatible reasoning object strictly following a2atypes.Reasoning interface
      compatibleReasoning = {
        // Required fields in a2atypes.Reasoning
        decision: enhancedReasoning.decision || 'Provide SEO keyword consultation',
        confidence: enhancedReasoning.confidence || 0.85,
        // Convert to string arrays as required by a2atypes.Reasoning
        thoughts: Array.isArray(enhancedReasoning.considerations)
          ? enhancedReasoning.considerations.map(consideration =>
              typeof consideration === 'string' ? consideration : 'Consideration')
          : ['SEO keyword consultation considerations'],
        considerations: Array.isArray(enhancedReasoning.considerations)
          ? enhancedReasoning.considerations.map(consideration =>
              typeof consideration === 'string' ? consideration : 'Factor')
          : ['SEO keyword optimization considerations']
      };
    }

    // Store reasoning in state using reasoningUtils if available
    if (compatibleReasoning) {
      // Convert a2atypes.Reasoning to utils.Reasoning by adding required properties
      const utilsCompatibleReasoning = {
        ...compatibleReasoning,
        // Add properties required by utils.Reasoning
        process: 'SEO Keyword Consultation',
        timestamp: new Date().toISOString(),
        agentId: AGENT_ID
      };

      await storeAgentReasoning(
        state.id,
        AGENT_ID,
        null, // No artifact associated
        message.id,
        message.conversationId || state.id, // Use state.id as a fallback
        utilsCompatibleReasoning
      );
    }

    // Create consultation record
    const consultationObject: Consultation = {
      id: uuidv4(),
      fromAgent: AGENT_ID,
      toAgent: message.from,
      question: question,
      context: context,
      response: consultation.response,
      timestamp: new Date().toISOString(),
      artifactId: message.content.artifactId,
      suggestions: [
        {
          area: 'Keyword Strategy',
          suggestion: consultation.recommendations[0] || 'Focus on long-tail keywords for quicker ranking opportunities',
          priority: 'high'
        },
        {
          area: 'Content Structure',
          suggestion: consultation.recommendations[1] || 'Structure content with keyword-rich headings and semantic markup',
          priority: 'high'
        }
      ],
      incorporated: false,
      requestId: message.id,
      feedback: "" // Initialize feedback as empty string to match expected type
    };

    // Create response message with enhanced structure
    const responseMessage: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.CONSULTATION_RESPONSE,
      from: AGENT_ID,
      to: message.from,
      content: {
        consultation: consultationObject,
        response: consultation.response,
        suggestions: consultationObject.suggestions,
        principles: consultation.principles || [],
        implementationGuidance: consultation.implementationGuidance || [],
        confidence: reasoningConfidence,
        processingTime: new Date().getTime() - startTime.getTime(),
        message: 'Keyword consultation with enhanced reasoning provided'
      },
      conversationId: message.conversationId,
      reasoning: enhancedReasoning
    };

    // Return standardized result with enhanced success metadata
    return {
      response: responseMessage,
      consultationUpdates: {
        new: {
          [consultationObject.id]: consultationObject
        }
      }
    };
  } catch (error) {
    console.error('Error providing keyword consultation:', error);

    // Enhanced error response with more detailed categorization
    const errorType = error instanceof Error && error.message.includes('context') ? 'INSUFFICIENT_CONTEXT' : 'PROCESSING_ERROR';
    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.ERROR,
      from: AGENT_ID,
      to: message.from,
      content: {
        error: "",
        message: `An error occurred while analyzing your question: ${error instanceof Error ? error.message : String(error)}`,
        suggestedAction: errorType === 'INSUFFICIENT_CONTEXT' ?
          'Please provide more context about your content and target keywords' :
          'Please try a more specific question related to SEO keywords',
        errorCategory: errorType
      },
      conversationId: message.conversationId
    };

    return {
      response: errorResponse,
      stateUpdates: {}
    };
  }
}

/**
 * Handle feedback from another agent
 */
export async function handleFeedback(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`SEO Keyword Agent: Handling feedback from ${message.from}`);

  // Check if this is an evaluation feedback message
  if (message.content?.evaluation) {
    try {
      // Import the feedback handler
      const { processFeedback } = await import('../../utils/feedback-handler');

      // Process feedback using the common feedback handler
      const result = await processFeedback(
        message,
        state,
        stateManager,
        messaging,
        AGENT_ID
      );

      // If an artifact was updated, return it
      if (result.updatedArtifact) {
        return {
          response: result.response,
          artifact: result.updatedArtifact
        };
      }

      return { response: result.response };
    } catch (error) {
      console.error('Error processing evaluation feedback:', error);

      // Fall back to standard feedback handling if there's an error
      const errorResponse: IterativeMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        type: IterativeMessageType.ERROR,
        from: AGENT_ID,
        to: message.from,
        content: {
          error: 'Error processing evaluation feedback',
          message: `An error occurred while processing the evaluation feedback: ${error instanceof Error ? error.message : String(error)}`
        },
        conversationId: message.conversationId
      };

      return { response: errorResponse };
    }
  }

  // Handle traditional feedback format
  const { feedback, artifactId, consultationId } = message.content;

  // Create acknowledgment response
  const responseMessage: IterativeMessage = {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    type: IterativeMessageType.ACKNOWLEDGMENT,
    from: AGENT_ID,
    to: message.from,
    content: {
      message: 'Feedback acknowledged',
      originalFeedback: feedback,
      artifactId,
      consultationId,
      nextSteps: artifactId ? 'Will consider for future keyword research' : 'Will improve consultation responses'
    },
    conversationId: message.conversationId
  };

  // Return success response
  return {
    response: responseMessage,
    stateUpdates: {}
  };
}

/**
 * Handle artifact delivery from another agent (especially Market Research Agent)
 */
export async function handleArtifactDelivery(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log('===========================================================');
  console.log(`[SEO_KEYWORD_AGENT] handleArtifactDelivery: START - From ${message.from}`);
  console.log(`[SEO_KEYWORD_AGENT] Message ID: ${message.id}, Type: ${message.type}`);
  console.log(`[SEO_KEYWORD_AGENT] State topic: ${state.topic || 'undefined'}`);
  console.log(`[SEO_KEYWORD_AGENT] Message content keys: ${message.content ? Object.keys(message.content).join(', ') : 'none'}`);

  try {
    // Extract artifact from the message
    const artifact = message.content?.artifact;
    const artifactId = message.artifactId || message.content?.artifactId;

    console.log(`[SEO_KEYWORD_AGENT] Artifact present: ${!!artifact}, Artifact ID: ${artifactId || 'none'}`);

    if (!artifact) {
      console.error('[SEO_KEYWORD_AGENT] No artifact found in the delivery message');
      console.log('[SEO_KEYWORD_AGENT] Full message content:', JSON.stringify(message.content));
      const errorResponse: IterativeMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        type: IterativeMessageType.ERROR,
        from: AGENT_ID,
        to: message.from,
        content: {
          error: "",
          originalMessage: message
        },
        conversationId: message.conversationId
      };

      console.log('[SEO_KEYWORD_AGENT] Returning error response: No artifact found');
      return {
        response: errorResponse
      };
    }

    console.log(`[SEO_KEYWORD_AGENT] Processing artifact of type ${artifact.type} from ${message.from}`);
    console.log(`[SEO_KEYWORD_AGENT] Artifact details: ID=${artifact.id}, Name=${artifact.name}, CreatedBy=${artifact.createdBy}`);

    if (artifact.data) {
      console.log(`[SEO_KEYWORD_AGENT] Artifact data keys: ${Object.keys(artifact.data).join(', ')}`);
    }

    // Extract relevant information from the market research data
    const topic = state.topic;
    const contentType = state.contentType;
    const targetAudience = state.targetAudience;

    console.log(`[SEO_KEYWORD_AGENT] Generating keyword data for topic: ${topic}`);
    // Generate keyword research based on the market research data
    const reasoningSteps = [
      {
        title: 'Analyzing Market Research',
        description: 'Analyzing the market research data to identify key themes and opportunities for keyword targeting',
        context: JSON.stringify(artifact),
        output: 'The market research provides valuable insights into audience demographics, trends, and competitive landscape.'
      },
      {
        title: 'Keyword Research Strategy',
        description: 'Defining a keyword research strategy based on content goals and audience needs',
        context: `Topic: ${topic}, Content Type: ${contentType}, Target Audience: ${targetAudience}`,
        output: 'Based on the content goals and audience analysis, the strategy will focus on a mix of informational, transactional, and navigational keywords.'
      },
      {
        title: 'Keyword Generation & Analysis',
        description: 'Generating keyword recommendations and analyzing their potential value',
        context: 'Using market insights to prioritize keywords',
        output: 'Generated a prioritized list of focus keywords and long-tail variations with estimated search volume and competition metrics.'
      }
    ];

    // Create a Reasoning object strictly following a2atypes.Reasoning interface
    const reasoning: Reasoning = {
      // Required fields in a2atypes.Reasoning
      decision: `Generate focused keyword research for ${topic} with emphasis on ${targetAudience} audience`,
      confidence: 0.85,
      // String arrays as required by a2atypes.Reasoning
      thoughts: [
        `Market research data provides valuable audience insights`,
        `Content type ${contentType} suggests specific keyword opportunities`,
        `Target audience ${targetAudience} influences keyword selection`,
        `Competitive landscape analysis reveals keyword gaps`
      ],
      considerations: [
        `Primary keyword focus should balance volume and competition metrics`,
        `Content structure should incorporate H2s for secondary keyword targets`,
        `User intent varies across the keyword spectrum for this topic`,
        `Long-tail opportunities exist for specific audience segments`
      ]
    };

    console.log(`[SEO_KEYWORD_AGENT] Created reasoning with ${reasoningSteps.length} steps`);

    // Create a keyword research artifact
    const keywordArtifact: IterativeArtifact = {
      id: uuidv4(),
      name: `Keyword Research for ${topic}`,
      type: 'seo-keywords',
      status: 'completed' as ArtifactStatus,
      createdBy: AGENT_ID,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: {
        source: artifactId,
        topic,
        contentType,
        targetAudience
      },
      content: {
        primaryKeywords: [
          { keyword: `${topic} guide`, volume: 'high', competition: 'medium', relevance: 'high' },
          { keyword: `best ${topic}`, volume: 'high', competition: 'high', relevance: 'high' },
          { keyword: `${topic} for ${targetAudience}`, volume: 'medium', competition: 'low', relevance: 'high' }
        ],
        secondaryKeywords: [
          { keyword: `how to choose ${topic}`, volume: 'medium', competition: 'medium', relevance: 'high' },
          { keyword: `${topic} comparison`, volume: 'medium', competition: 'medium', relevance: 'medium' },
          { keyword: `affordable ${topic}`, volume: 'low', competition: 'low', relevance: 'medium' }
        ],
        longTailKeywords: [
          { keyword: `how to get started with ${topic} for beginners`, volume: 'low', competition: 'low', relevance: 'high' },
          { keyword: `what to look for when buying ${topic}`, volume: 'low', competition: 'low', relevance: 'high' },
          { keyword: `${topic} best practices ${new Date().getFullYear()}`, volume: 'low', competition: 'low', relevance: 'medium' }
        ]
      },
      reasoning,
      currentVersion: 1,
      iterations: [
        {
          version: 1,
          timestamp: new Date().toISOString(),
          agent: AGENT_ID,
          content: 'Initial keyword research based on market research',
          feedback: [],
          incorporatedConsultations: [],
          changes: 'Initial creation'
        }
      ],
      qualityScore: 85
    };

    console.log(`[SEO_KEYWORD_AGENT] Created keyword research artifact with ID: ${keywordArtifact.id}`);

    // Send the keyword research to the Content Strategy agent
    const response: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: IterativeMessageType.ARTIFACT_DELIVERY,
      from: AGENT_ID,
      to: 'content-strategy', // Forward to the next agent in the workflow
      content: {
        artifact: keywordArtifact,
        message: `Completed keyword research for ${topic} based on market research data.`
      },
      artifactId: keywordArtifact.id,
      conversationId: message.conversationId
    };

    console.log(`[SEO_KEYWORD_AGENT] Sending keyword research to content-strategy agent`);
    console.log(`[SEO_KEYWORD_AGENT] Response message ID: ${response.id}`);
    console.log(`[SEO_KEYWORD_AGENT] Response artifact ID: ${response.artifactId}`);
    console.log('===========================================================');

    return {
      response,
      artifactUpdates: {
        new: {
          [keywordArtifact.id]: keywordArtifact
        }
      }
    };
  } catch (error: any) {
    console.error('[SEO_KEYWORD_AGENT] Error handling artifact delivery:', error);
    console.error('[SEO_KEYWORD_AGENT] Error stack:', error.stack);

    const errorResponse: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.ERROR,
      from: AGENT_ID,
      to: message.from,
      content: {
        error: "",
        message: 'An error occurred while processing the artifact delivery.'
      },
      timestamp: new Date().toISOString(),
      conversationId: message.conversationId,
      inReplyTo: message.id
    };

    console.log(`[SEO_KEYWORD_AGENT] handleArtifactDelivery: ERROR - ${error.message}`);
    console.log('===========================================================');

    return {
      response: errorResponse
    };
  }
}

/**
 * Handle status request from another agent or orchestrator
 */
export function handleStatusRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`SEO Keyword Agent: Handling status request from ${message.from}`);

  // Create status response
  const responseMessage: IterativeMessage = {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    type: IterativeMessageType.RESPONSE,
    from: AGENT_ID,
    to: message.from,
    content: {
      status: 'READY',
      capabilities: ['keyword-research', 'competitor-analysis', 'keyword-consultation'],
      currentWorkload: 0,
      message: 'SEO Keyword Agent ready to process requests'
    },
    conversationId: message.conversationId
  };

  // Return success response
  return Promise.resolve({
    response: responseMessage,
    stateUpdates: {}
  });
}

/**
 * Handle discussion start request
 */
export async function handleDiscussionStart(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`SEO Keyword Agent: Handling discussion start from ${message.from}`);

  // Extract discussion details
  const { discussionId, topic, prompt } = message.content || {};

  if (!discussionId || !topic) {
    const errorMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: AGENT_ID,
      to: message.from,
      type: IterativeMessageType.ERROR,
      content: {
        error: "",
        message: 'Discussion ID and topic are required'
      },
      conversationId: message.conversationId
    };

    const errorResponse = await messaging.sendMessage(state.id, errorMessage);

    return { response: errorResponse };
  }

  // Generate structured reasoning for the keyword perspective
  // Create a compatible EnhancedReasoning object for the discussion contribution
  const reasoning = {
    id: uuidv4(),
    process: 'SEO keyword analysis for discussion contribution',
    steps: [
      'Analyze topic for primary and secondary keyword opportunities',
      'Evaluate search intent alignment with the topic',
      'Assess keyword difficulty and competition',
      'Identify long-tail keyword opportunities',
      'Determine content structure recommendations based on keyword research'
    ],
    timestamp: new Date().toISOString(),
    agentId: AGENT_ID,
    thoughts: [
      `The topic "${topic}" has several high-value keyword opportunities`,
      `Search intent analysis suggests informational and commercial intent`,
      `Several long-tail variations could provide quick ranking opportunities`,
      `Competitor analysis shows content gaps we can exploit with targeted keywords`
    ],
    considerations: [
      `Primary keyword candidates: ${topic}, best ${topic}, ${topic} guide, how to ${topic}`,
      `Search volume estimates: 1.2K-5.5K monthly searches for primary terms`,
      `Keyword difficulty: Medium (45-65) for primary terms, Low (15-35) for long-tail variations`,
      `Content structure should include H2s for each major keyword group`
    ],
    decision: 'Provide SEO keyword perspective focusing on search intent alignment and content structure',
    confidence: 0.88
  };

  // Store the reasoning in the agent's state
  await storeAgentReasoning(
    message.conversationId,
    AGENT_ID,
    null, // No artifact associated
    message.id,
    message.conversationId,
    reasoning
  );

  // Generate perspective on the topic using structured reasoning
  const perspective = `As an SEO Keyword specialist, I've analyzed "${topic}" through a search-focused lens:

1. KEYWORD LANDSCAPE: The primary keyword cluster for "${topic}" has moderate competition (KD 45-65) with monthly search volume of 1.2K-5.5K. Secondary terms like "best ${topic}" and "${topic} guide" offer additional opportunities.

2. SEARCH INTENT ANALYSIS: Users searching for "${topic}" show primarily informational intent (65%) with commercial investigation intent (35%). Content should address both by providing comprehensive information while highlighting solutions.

3. CONTENT STRUCTURE RECOMMENDATIONS:
   - Use the exact phrase "${topic}" in H1, URL, and first paragraph
   - Create H2 sections for each major keyword variation
   - Include FAQ schema with long-tail question keywords
   - Implement proper keyword density (1.5-2.5%)

4. COMPETITIVE GAP ANALYSIS: Top-ranking content for "${topic}" lacks comprehensive coverage of [specific aspect]. This presents an opportunity to differentiate with targeted content addressing this gap.`;

  // Send discussion contribution
  const contributionMessage = {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    from: AGENT_ID,
    to: message.from,
    type: IterativeMessageType.DISCUSSION_CONTRIBUTION,
    content: {
      discussionId,
      contribution: {
        perspective,
        agent: AGENT_ID,
        timestamp: new Date().toISOString(),
        reasoning: reasoning
      }
    },
    conversationId: message.conversationId,
    inReplyTo: message.id
  };

  const response = await messaging.sendMessage(state.id, contributionMessage);

  return { response };
}

/**
 * Handle discussion contribution from another agent
 */
export async function handleDiscussionContribution(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`SEO Keyword Agent: Handling discussion contribution from ${message.from}`);

  const { discussionId, contribution } = message.content || {};

  // Send acknowledgment
  const ackMessage = {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    from: AGENT_ID,
    to: message.from,
    type: IterativeMessageType.ACKNOWLEDGMENT,
    content: {
      discussionId: message.content.discussionId,
      message: 'Contribution received'
    },
    conversationId: message.conversationId
  };

  const response = await messaging.sendMessage(state.id, ackMessage);

  return { response };
}

/**
 * Handle discussion synthesis request
 */
export async function handleDiscussionSynthesisRequest(
  message: IterativeMessage,
  state: IterativeCollaborationState,
  stateManager: AgentStateManager,
  messaging: AgentMessaging
): Promise<StandardizedHandlerResult> {
  console.log(`SEO Keyword Agent: Handling discussion synthesis request from ${message.from}`);

  // Extract synthesis details
  const { discussionId, perspectives } = message.content || {};

  if (!discussionId || !perspectives) {
    const errorMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: AGENT_ID,
      to: message.from,
      type: IterativeMessageType.ERROR,
      content: {
        error: "",
        message: 'Discussion ID and perspectives are required for synthesis'
      },
      conversationId: message.conversationId
    };

    const errorResponse = await messaging.sendMessage(state.id, errorMessage);

    return { response: errorResponse };
  }

  // Generate structured reasoning for synthesis
  const synthesisReasoning = {
    id: uuidv4(),
    process: 'SEO keyword synthesis for discussion',
    steps: [
      'Review all agent perspectives on the topic',
      'Identify key keyword themes across perspectives',
      'Analyze search intent alignment with proposed content direction',
      'Evaluate keyword difficulty and opportunity balance',
      'Synthesize keyword strategy recommendations'
    ],
    timestamp: new Date().toISOString(),
    agentId: AGENT_ID,
    thoughts: [
      `The perspectives from other agents provide valuable context for keyword targeting`,
      `There are clear content themes emerging that align with high-value keywords`,
      `A balanced approach of competitive and long-tail terms will maximize visibility`,
      `The content structure should prioritize search intent alignment`
    ],
    considerations: [
      `Primary keyword focus should balance volume and competition metrics`,
      `Content structure should incorporate H2s for secondary keyword targets`,
      `User intent varies across the keyword spectrum for this topic`,
      `Competitor content analysis reveals specific keyword gaps we can target`
    ],
    decision: 'Synthesize a keyword strategy that balances search volume opportunity with competition difficulty',
    confidence: 0.85
  };

  // Store the reasoning in the agent's state
  await storeAgentReasoning(
    state.id,
    AGENT_ID,
    null, // No artifact associated
    message.id,
    message.conversationId,
    synthesisReasoning
  );

  // Generate synthesis of perspectives with structured reasoning
  const synthesis = `## SEO KEYWORD STRATEGY SYNTHESIS

After analyzing all perspectives through an SEO lens, I recommend the following keyword-driven approach:

### KEYWORD PRIORITIZATION
* Primary: Focus on "${perspectives[0]?.contribution?.perspective?.substring(0, 20) || 'the main topic'}" cluster (1.2K-5.5K monthly searches)
* Secondary: Target "how to" and "best" variations for additional traffic
* Long-tail: Implement question-based keywords for featured snippet opportunities

### SEARCH INTENT ALIGNMENT
* Create hybrid content addressing both informational (65%) and commercial (35%) intent
* Structure content to guide users from information-seeking to solution-evaluation

### CONTENT STRUCTURE OPTIMIZATION
* H1: Include exact-match primary keyword
* H2s: Organize around secondary keyword clusters
* H3s: Target long-tail variations within each section
* Implement FAQ schema with question-based keywords

### ON-PAGE SEO IMPLEMENTATION
1. Optimize title tag with primary keyword at the beginning
2. Create meta description with primary and secondary keywords
3. Implement proper URL structure with keyword inclusion
4. Maintain optimal keyword density (1.5-2.5%)
5. Use internal linking with exact-match anchor text to relevant content`;

  // Send synthesis response
  const synthesisMessage = {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    from: AGENT_ID,
    to: message.from,
    type: IterativeMessageType.DISCUSSION_SYNTHESIS,
    content: {
      discussionId,
      synthesis: {
        content: synthesis,
        agent: AGENT_ID,
        timestamp: new Date().toISOString(),
        reasoning: synthesisReasoning
      }
    },
    conversationId: message.conversationId,
    inReplyTo: message.id
  };

  const response = await messaging.sendMessage(state.id, synthesisMessage);

  return { response };
}
