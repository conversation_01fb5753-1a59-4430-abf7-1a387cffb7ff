/**
 * Debug State API Route
 *
 * This endpoint provides direct access to the raw session state for debugging purposes.
 */

import { NextRequest, NextResponse } from 'next/server';
import logger from '../../../utils/logger';
import { StateManager } from '../state/manager';

/**
 * GET /api/agents/dynamic-collaboration-v3/debug-state
 *
 * Get the raw state of a session for debugging
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Get session ID from query params
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing sessionId parameter' },
        { status: 400 }
      );
    }

    // Get raw session state
    const stateManager = new StateManager(sessionId);
    const state = await stateManager.getState();

    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Add debug information
    const debugInfo = {
      sessionId,
      timestamp: new Date().toISOString(),
      stateSize: JSON.stringify(state).length,
      goalCount: state.goals ? Object.keys(state.goals).length : 0,
      artifactCount: state.artifacts ? Object.keys(state.artifacts).length : 0,
      activeGoals: state.goals ? Object.values(state.goals).filter((goal: any) => goal.status === 'ACTIVE').length : 0,
      completedGoals: state.goals ? Object.values(state.goals).filter((goal: any) => goal.status === 'COMPLETED').length : 0,
      currentPhase: state.workflowProgress?.currentPhase || 'UNKNOWN',
      overallProgress: state.workflowProgress?.overallProgress || 0
    };

    // Return raw session state with debug info
    return NextResponse.json({
      debugInfo,
      rawState: state
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error getting debug state`, {
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
