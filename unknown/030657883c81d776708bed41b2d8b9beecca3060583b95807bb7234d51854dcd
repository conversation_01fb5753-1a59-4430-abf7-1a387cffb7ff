// src/app/(payload)/api/agents/generate-content/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';

// Define the content generation request interface
interface ContentGenerationRequest {
  topic: string;
  contentType: 'blog-article' | 'product-page' | 'buying-guide';
  targetAudience: string;
  keywords: string[];
  tone: string;
}

// Define the collaboration session interface
interface CollaborationSession {
  id: string;
  topic: string;
  startTime: string;
  endTime?: string;
  status: 'active' | 'completed' | 'failed';
  messages: any[];
  finalOutput?: any;
}

// Define the A2A message interface (internal format)
interface A2AMessage {
  id: string;
  timestamp: string;
  source: string;
  target: string;
  task: {
    type: string;
    [key: string]: any;
  };
}

// Define the UI message interface (format expected by the UI)
interface UIMessage {
  id: string;
  timestamp: string;
  from: string;
  to: string;
  type: string;
  content: any;
  replyTo?: string;
}

// Convert A2A message to UI message format
function convertToUIMessage(message: A2AMessage): UIMessage {
  // Extract content based on task type
  let content: any;
  switch (message.task.type) {
    case 'PROVIDE_INFORMATION':
      content = message.task.information;
      break;
    case 'PROVIDE_FEEDBACK':
      content = message.task.feedback;
      break;
    case 'COLLABORATION_RESULT':
      content = message.task.result;
      break;
    case 'ERROR':
      content = {
        error: message.task.error,
        details: message.task.details
      };
      break;
    default:
      content = message.task;
  }

  return {
    id: message.id,
    timestamp: message.timestamp,
    from: message.source,
    to: message.target,
    type: message.task.type.toLowerCase(),
    content: content
  };
}

/**
 * Generate content through agent collaboration
 * 
 * This endpoint initiates a content generation process by coordinating
 * multiple specialized agents (market research, SEO, content strategy, etc.)
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Parse the request body
    const requestData: ContentGenerationRequest = await req.json();
    
    // Validate the request data
    if (!requestData.topic) {
      return NextResponse.json(
        { error: 'Topic is required' },
        { status: 400 }
      );
    }
    
    // Create a unique session ID
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    // Initialize the message history
    const messageHistory: A2AMessage[] = [];
    
    // Create initial planning message
    const planningMessage: A2AMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      source: 'contentGeneration',
      target: 'internal',
      task: {
        type: 'PLANNING',
        plan: `Planning content generation for topic: ${requestData.topic}`,
        contentType: requestData.contentType,
        targetAudience: requestData.targetAudience,
        tone: requestData.tone,
        keywords: requestData.keywords,
        requiredResearch: [
          'Market research to understand audience needs and competitor landscape',
          'SEO keyword research to identify target keywords',
          'Content strategy to determine optimal structure and approach'
        ]
      }
    };
    
    messageHistory.push(planningMessage);
    
    // Define the base URL for API calls
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || `http://${process.env.VERCEL_URL || 'localhost:3000'}`;
    
    // Step 1: Request market research information
    console.log('Requesting market research information...');
    try {
      const marketResearchRequest: A2AMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        source: 'contentGeneration',
        target: 'market-research-agent',
        task: {
          type: 'REQUEST_INFORMATION',
          query: `Please provide market research for "${requestData.topic}" targeting ${requestData.targetAudience}`
        }
      };
      
      const marketResearchResponse = await fetch(`${baseUrl}/api/agents/market-research`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(marketResearchRequest),
      });
      
      if (!marketResearchResponse.ok) {
        throw new Error(`Market research request failed: ${marketResearchResponse.statusText}`);
      }
      
      const marketResearchData = await marketResearchResponse.json();
      messageHistory.push(marketResearchData);
      
      // Add a message showing the content generation agent received the information
      const acknowledgmentMessage: A2AMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        source: 'contentGeneration',
        target: 'market-research-agent',
        task: {
          type: 'ACKNOWLEDGE',
          status: 'received',
          message: 'Market research information received and processed'
        }
      };
      
      messageHistory.push(acknowledgmentMessage);
    } catch (error) {
      console.error('Error requesting market research:', error);
      
      // Add error message to history
      const errorMessage: A2AMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        source: 'system',
        target: 'contentGeneration',
        task: {
          type: 'ERROR',
          error: 'Failed to get market research information',
          details: error instanceof Error ? error.message : 'Unknown error'
        }
      };
      
      messageHistory.push(errorMessage);
    }
    
    // Step 2: Request SEO keyword information
    console.log('Requesting SEO keyword information...');
    try {
      const keywordQuery = `Please provide SEO keywords for "${requestData.topic}" with user-provided keywords: ${requestData.keywords.join(', ')}`;
      
      const seoKeywordRequest: A2AMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        source: 'contentGeneration',
        target: 'seo-keyword-agent',
        task: {
          type: 'REQUEST_INFORMATION',
          query: keywordQuery
        }
      };
      
      const seoKeywordResponse = await fetch(`${baseUrl}/api/agents/seo-keyword`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(seoKeywordRequest),
      });
      
      if (!seoKeywordResponse.ok) {
        throw new Error(`SEO keyword request failed: ${seoKeywordResponse.statusText}`);
      }
      
      const seoKeywordData = await seoKeywordResponse.json();
      messageHistory.push(seoKeywordData);
      
      // Add acknowledgment message
      const acknowledgmentMessage: A2AMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        source: 'contentGeneration',
        target: 'seo-keyword-agent',
        task: {
          type: 'ACKNOWLEDGE',
          status: 'received',
          message: 'SEO keyword information received and processed'
        }
      };
      
      messageHistory.push(acknowledgmentMessage);
    } catch (error) {
      console.error('Error requesting SEO keywords:', error);
      
      // Add error message to history
      const errorMessage: A2AMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        source: 'system',
        target: 'contentGeneration',
        task: {
          type: 'ERROR',
          error: 'Failed to get SEO keyword information',
          details: error instanceof Error ? error.message : 'Unknown error'
        }
      };
      
      messageHistory.push(errorMessage);
    }
    
    // Step 3: Request content strategy
    console.log('Requesting content strategy...');
    try {
      // Get market research and SEO keyword information from message history
      const marketResearchInfo = messageHistory.find(msg => 
        msg.source === 'market-research-agent' && 
        msg.task?.type === 'PROVIDE_INFORMATION'
      )?.task?.information;
      
      const seoKeywordInfo = messageHistory.find(msg => 
        msg.source === 'seo-keyword-agent' && 
        msg.task?.type === 'PROVIDE_INFORMATION'
      )?.task?.information;
      
      const contentStrategyRequest: A2AMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        source: 'contentGeneration',
        target: 'content-strategy-agent',
        task: {
          type: 'COLLABORATE',
          context: requestData.topic,
          content: {
            topic: requestData.topic,
            contentType: requestData.contentType,
            targetAudience: requestData.targetAudience,
            tone: requestData.tone,
            keywords: requestData.keywords,
            marketResearch: marketResearchInfo,
            seoKeywords: seoKeywordInfo
          }
        }
      };
      
      const contentStrategyResponse = await fetch(`${baseUrl}/api/agents/content-strategy`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contentStrategyRequest),
      });
      
      if (!contentStrategyResponse.ok) {
        throw new Error(`Content strategy request failed: ${contentStrategyResponse.statusText}`);
      }
      
      const contentStrategyData = await contentStrategyResponse.json();
      messageHistory.push(contentStrategyData);
      
      // Add acknowledgment message
      const acknowledgmentMessage: A2AMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        source: 'contentGeneration',
        target: 'content-strategy-agent',
        task: {
          type: 'ACKNOWLEDGE',
          status: 'received',
          message: 'Content strategy received and processed'
        }
      };
      
      messageHistory.push(acknowledgmentMessage);
    } catch (error) {
      console.error('Error requesting content strategy:', error);
      
      // Add error message to history
      const errorMessage: A2AMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        source: 'system',
        target: 'contentGeneration',
        task: {
          type: 'ERROR',
          error: 'Failed to get content strategy',
          details: error instanceof Error ? error.message : 'Unknown error'
        }
      };
      
      messageHistory.push(errorMessage);
    }
    
    // Step 4: Request SEO optimization (optional)
    console.log('Requesting SEO optimization...');
    try {
      // Get content strategy from message history
      const contentStrategy = messageHistory.find(msg => 
        msg.source === 'content-strategy-agent' && 
        (msg.task?.type === 'COLLABORATION_RESULT' || msg.task?.type === 'PROVIDE_INFORMATION')
      )?.task?.result || messageHistory.find(msg => 
        msg.source === 'content-strategy-agent'
      )?.task?.information;
      
      // Get SEO keywords from message history
      const seoKeywords = messageHistory.find(msg => 
        msg.source === 'seo-keyword-agent' && 
        msg.task?.type === 'PROVIDE_INFORMATION'
      )?.task?.information?.primaryKeywords || [];
      
      const seoOptimizationRequest: A2AMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        source: 'contentGeneration',
        target: 'seo-optimization-agent',
        task: {
          type: 'REQUEST_FEEDBACK',
          content: contentStrategy,
          keywords: seoKeywords
        }
      };
      
      const seoOptimizationResponse = await fetch(`${baseUrl}/api/agents/seo-optimization`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(seoOptimizationRequest),
      });
      
      if (!seoOptimizationResponse.ok) {
        throw new Error(`SEO optimization request failed: ${seoOptimizationResponse.statusText}`);
      }
      
      const seoOptimizationData = await seoOptimizationResponse.json();
      messageHistory.push(seoOptimizationData);
      
      // Add acknowledgment message
      const acknowledgmentMessage: A2AMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        source: 'contentGeneration',
        target: 'seo-optimization-agent',
        task: {
          type: 'ACKNOWLEDGE',
          status: 'received',
          message: 'SEO optimization feedback received and processed'
        }
      };
      
      messageHistory.push(acknowledgmentMessage);
    } catch (error) {
      console.error('Error requesting SEO optimization:', error);
      
      // Add error message to history
      const errorMessage: A2AMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        source: 'system',
        target: 'contentGeneration',
        task: {
          type: 'ERROR',
          error: 'Failed to get SEO optimization',
          details: error instanceof Error ? error.message : 'Unknown error'
        }
      };
      
      messageHistory.push(errorMessage);
    }
    
    // Step 5: Request content generation for each section
    console.log('Requesting content generation for each section...');
    try {
      const sections = [
        {
          id: 'section-intro',
          type: 'heading',
          title: `Introduction to ${requestData.topic}`,
          prompt: `Write the section "${requestData.topic}" for the topic "${requestData.topic}".\n- Include at least one real-world example, case study, or data point.\n- Make the content actionable and specific to "${requestData.topic}".\n- Avoid generic statements.\n- Use a "${requestData.tone}" tone.`,
          content: ''
        },
        {
          id: 'section-components',
          type: 'heading',
          title: `Key Components of ${requestData.topic}`,
          prompt: `Write the section "${requestData.topic}" for the topic "${requestData.topic}".\n- Include at least one real-world example, case study, or data point.\n- Make the content actionable and specific to "${requestData.topic}".\n- Avoid generic statements.\n- Use a "${requestData.tone}" tone.`,
          content: ''
        },
        {
          id: 'section-practices',
          type: 'heading',
          title: `Best Practices for ${requestData.topic}`,
          prompt: `Write the section "${requestData.topic}" for the topic "${requestData.topic}".\n- Include at least one real-world example, case study, or data point.\n- Make the content actionable and specific to "${requestData.topic}".\n- Avoid generic statements.\n- Use a "${requestData.tone}" tone.`,
          content: ''
        },
        {
          id: 'section-examples',
          type: 'heading',
          title: `${requestData.topic} Case Studies`,
          prompt: `Write the section "${requestData.topic}" for the topic "${requestData.topic}".\n- Include at least one real-world example, case study, or data point.\n- Make the content actionable and specific to "${requestData.topic}".\n- Avoid generic statements.\n- Use a "${requestData.tone}" tone.`,
          content: ''
        },
        {
          id: 'section-trends',
          type: 'heading',
          title: `Future Trends in ${requestData.topic}`,
          prompt: `Write the section "${requestData.topic}" for the topic "${requestData.topic}".\n- Include at least one real-world example, case study, or data point.\n- Make the content actionable and specific to "${requestData.topic}".\n- Avoid generic statements.\n- Use a "${requestData.tone}" tone.`,
          content: ''
        },
        {
          id: 'section-conclusion',
          type: 'heading',
          title: `Conclusion`,
          prompt: `Write the section "${requestData.topic}" for the topic "${requestData.topic}".\n- Include at least one real-world example, case study, or data point.\n- Make the content actionable and specific to "${requestData.topic}".\n- Avoid generic statements.\n- Use a "${requestData.tone}" tone.`,
          content: ''
        }
      ];
      
      for (const section of sections) {
        const contentGenerationRequest: A2AMessage = {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          source: 'contentGeneration',
          target: 'generate-content-agent',
          task: {
            type: 'GENERATE_CONTENT',
            prompt: section.prompt,
            topic: requestData.topic,
            contentType: requestData.contentType,
            targetAudience: requestData.targetAudience,
            tone: requestData.tone,
            keywords: requestData.keywords
          }
        };
        
        const contentGenerationResponse = await fetch(`${baseUrl}/api/agents/generate-content`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(contentGenerationRequest),
        });
        
        if (!contentGenerationResponse.ok) {
          throw new Error(`Content generation request failed: ${contentGenerationResponse.statusText}`);
        }
        
        const contentGenerationData = await contentGenerationResponse.json();
        messageHistory.push(contentGenerationData);
        
        section.content = contentGenerationData.task.result;
      }
    } catch (error) {
      console.error('Error requesting content generation:', error);
      
      // Add error message to history
      const errorMessage: A2AMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        source: 'system',
        target: 'contentGeneration',
        task: {
          type: 'ERROR',
          error: 'Failed to generate content',
          details: error instanceof Error ? error.message : 'Unknown error'
        }
      };
      
      messageHistory.push(errorMessage);
    }
    
    // Convert all messages to UI format
    const uiMessages = messageHistory.map(convertToUIMessage);
    
    // Create final session output
    const session: CollaborationSession = {
      id: sessionId,
      topic: requestData.topic,
      startTime: planningMessage.timestamp,
      endTime: new Date().toISOString(),
      status: 'completed',
      messages: uiMessages,
      finalOutput: {
        title: `${requestData.topic}: A Comprehensive Guide`,
        metaDescription: `Learn everything you need to know about ${requestData.topic} in this comprehensive guide covering best practices, implementation strategies, and real-world examples.`,
        topic: requestData.topic,
        contentType: requestData.contentType,
        targetAudience: requestData.targetAudience,
        tone: requestData.tone,
        marketResearch: messageHistory.find(msg => 
          msg.source === 'market-research-agent' && 
          msg.task?.type === 'PROVIDE_INFORMATION'
        )?.task?.information,
        seoKeywords: messageHistory.find(msg => 
          msg.source === 'seo-keyword-agent' && 
          msg.task?.type === 'PROVIDE_INFORMATION'
        )?.task?.information,
        contentStrategy: messageHistory.find(msg => 
          msg.source === 'content-strategy-agent' && 
          (msg.task?.type === 'COLLABORATION_RESULT' || msg.task?.type === 'PROVIDE_INFORMATION')
        )?.task?.result || messageHistory.find(msg => 
          msg.source === 'content-strategy-agent'
        )?.task?.information,
        seoOptimization: messageHistory.find(msg => 
          msg.source === 'seo-optimization-agent' && 
          msg.task?.type === 'PROVIDE_FEEDBACK'
        )?.task?.feedback,
        // Generate final content sections
        sections: [
          {
            id: 'section-intro',
            type: 'heading',
            title: `Introduction to ${requestData.topic}`,
            content: messageHistory.find(msg => 
              msg.source === 'content-generator-agent' && 
              msg.task?.type === 'PROVIDE_CONTENT' && 
              msg.task?.content?.id === 'section-intro'
            )?.task?.content?.content || ''
          },
          {
            id: 'section-components',
            type: 'heading',
            title: `Key Components of ${requestData.topic}`,
            content: messageHistory.find(msg => 
              msg.source === 'content-generator-agent' && 
              msg.task?.type === 'PROVIDE_CONTENT' && 
              msg.task?.content?.id === 'section-components'
            )?.task?.content?.content || ''
          },
          {
            id: 'section-practices',
            type: 'heading',
            title: `Best Practices for ${requestData.topic}`,
            content: messageHistory.find(msg => 
              msg.source === 'content-generator-agent' && 
              msg.task?.type === 'PROVIDE_CONTENT' && 
              msg.task?.content?.id === 'section-practices'
            )?.task?.content?.content || ''
          },
          {
            id: 'section-examples',
            type: 'heading',
            title: `${requestData.topic} Case Studies`,
            content: messageHistory.find(msg => 
              msg.source === 'content-generator-agent' && 
              msg.task?.type === 'PROVIDE_CONTENT' && 
              msg.task?.content?.id === 'section-examples'
            )?.task?.content?.content || ''
          },
          {
            id: 'section-trends',
            type: 'heading',
            title: `Future Trends in ${requestData.topic}`,
            content: messageHistory.find(msg => 
              msg.source === 'content-generator-agent' && 
              msg.task?.type === 'PROVIDE_CONTENT' && 
              msg.task?.content?.id === 'section-trends'
            )?.task?.content?.content || ''

1. **AI-Powered Personalization**: Artificial intelligence and machine learning algorithms will enable hyper-personalized member experiences by analyzing behavior patterns, preferences, and engagement history. This will allow sporting centers to automatically tailor recommendations, communications, and offers to individual members at scale.

2. **Predictive Analytics for Retention**: Advanced analytics will move beyond reporting what happened to predicting what will happen. CRM systems will identify members at risk of cancellation before they show obvious signs of disengagement, allowing for proactive retention strategies.

3. **Integrated Wearable Technology**: CRM systems will increasingly integrate with fitness wearables and tracking devices, automatically importing workout data, progress metrics, and health information (with appropriate permissions). This creates a more complete picture of member activity both inside and outside the facility.

4. **Voice-Activated Interfaces**: Voice assistants will become more prevalent in CRM interactions, allowing members to book classes, check schedules, or access information through conversational interfaces, enhancing accessibility and convenience.

5. **Augmented Reality Experiences**: AR technology will be incorporated into CRM systems to enhance member engagement through virtual facility tours, equipment usage demonstrations, or visualizing personal fitness journeys.

6. **Blockchain for Secure Records**: Blockchain technology will provide enhanced security for sensitive member data while potentially enabling new features like transferable memberships or seamless access to partner facilities.

7. **Automated Coaching and Support**: AI-powered chatbots and virtual assistants will provide 24/7 support for common member queries, basic coaching advice, and motivation, complementing human staff rather than replacing them.

8. **Community Building Features**: CRM systems will expand beyond transactional relationships to facilitate community building through integrated social features, member matchmaking (for finding workout partners), and gamification elements.

9. **Contactless and Biometric Access**: In response to health concerns and convenience demands, CRM systems will increasingly support contactless check-ins and biometric access methods integrated with member profiles.

10. **Sustainability Tracking**: As environmental concerns grow, CRM systems may incorporate features to track and reward sustainable behaviors, such as walking or cycling to the facility, reducing paper usage, or participating in eco-friendly initiatives.

Sporting centers that stay ahead of these trends will be well-positioned to meet evolving member expectations while improving operational efficiency and creating competitive advantages in an increasingly digital marketplace.`
          },
          {
            id: 'section-conclusion',
            type: 'heading',
            title: `Conclusion`,
            content: `Implementing ${requestData.topic} effectively represents a significant opportunity for sporting centers to enhance member experiences, streamline operations, and drive business growth. Throughout this guide, we've explored the essential components, best practices, real-world applications, and future trends that can inform your CRM strategy.

The most successful implementations share several common elements: they begin with clear objectives aligned with business goals, focus on solving specific pain points for both members and staff, prioritize user experience and adoption, and evolve continuously based on feedback and performance data.

Remember that CRM is not merely a technology solution but a business strategy centered around building and maintaining valuable relationships with your members. The technology should enable and enhance this strategy, not define it.

As you move forward with implementing or optimizing your ${requestData.topic} solution, consider these key takeaways:

1. Start with a clear understanding of your specific needs and objectives before selecting or customizing a CRM system.

2. Prioritize data quality and integration capabilities to create a unified view of your members across all touchpoints.

3. Balance automation with personalization to increase efficiency without sacrificing the human connection that is vital in sporting environments.

4. Invest in proper training and change management to ensure high adoption rates among your staff.

5. Measure success against your defined objectives and be prepared to make adjustments as needed.

6. Stay informed about emerging trends and technologies that could enhance your CRM capabilities in the future.

By taking a strategic, member-centric approach to CRM implementation, sporting centers of all sizes can create more engaging experiences, build stronger relationships, and achieve sustainable growth in an increasingly competitive market.

The journey to CRM excellence is ongoing, but with the right approach, it can transform your sporting center from a facility into a community, and your members from customers into advocates.`
          }
        ],
        // Add SEO score for content preview
        seoScore: {
          overall: 85,
          keywordUsage: 90,
          readability: 88,
          structure: 87,
          metaData: 80
        },
        // Add internal links for content preview
        internalLinks: [
          {
            anchorText: `${requestData.topic} implementation guide`,
            targetTitle: `How to Implement ${requestData.topic} Successfully`,
            targetURL: `/blog/${requestData.topic.toLowerCase().replace(/\s+/g, '-')}-implementation`,
            context: 'within implementation section',
            relevance: 95
          },
          {
            anchorText: `${requestData.topic} best practices`,
            targetTitle: `${requestData.topic} Best Practices for 2025`,
            targetURL: `/blog/${requestData.topic.toLowerCase().replace(/\s+/g, '-')}-best-practices`,
            context: 'within best practices section',
            relevance: 90
          }
        ],
        // Add agent discussion for the key decisions tab
        agentDiscussion: {
          planning: [
            `Topic: ${requestData.topic}`,
            `Content Type: ${requestData.contentType}`,
            `Target Audience: ${requestData.targetAudience}`,
            `Tone: ${requestData.tone}`,
            `Keywords: ${requestData.keywords.join(', ') || 'No specific keywords provided'}`
          ],
          discussion: [
            "Gathered market research information to understand audience needs and competitor landscape",
            "Identified SEO keywords for optimizing content visibility",
            "Developed content strategy based on market research and SEO keywords",
            "Received SEO optimization feedback to improve content structure"
          ],
          execution: [
            "Generated comprehensive content structure with sections and key points",
            "Optimized content with SEO recommendations",
            "Finalized content strategy with integrated market insights"
          ],
          review: [
            "Evaluated content against SEO best practices",
            "Verified alignment with target audience needs",
            "Confirmed comprehensive coverage of the topic",
            "Validated proper keyword usage and distribution"
          ]
        }
      }
    };
    
    // Return the session data
    return NextResponse.json(session);
  } catch (error) {
    console.error('Error in content generation:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
