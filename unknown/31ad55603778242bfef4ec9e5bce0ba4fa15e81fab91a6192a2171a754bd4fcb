'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  Button,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Divider,
  useTheme,
  Tab,
  Tabs
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import VisibilityIcon from '@mui/icons-material/Visibility';
import CloseIcon from '@mui/icons-material/Close';
import AssignmentIcon from '@mui/icons-material/Assignment';
import DescriptionIcon from '@mui/icons-material/Description';
import BarChartIcon from '@mui/icons-material/BarChart';
import TuneIcon from '@mui/icons-material/Tune';
import FeedbackIcon from '@mui/icons-material/Feedback';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import dynamic from 'next/dynamic';
import remarkGfm from 'remark-gfm';

// Dynamically import ReactMarkdown to avoid SSR issues
const ReactMarkdown = dynamic(() => import('react-markdown'), { ssr: false });
import { DynamicCollaborationState, DynamicWorkflowPhase } from '../../app/(payload)/api/agents/dynamic-collaboration-v2/state';
import { dynamicCollaborationClientV2 } from '../../lib/dynamic-collaboration-client-v2';

interface ArtifactGalleryProps {
  sessionId: string;
  state: DynamicCollaborationState | null;
  loading?: boolean;
  onRefresh?: () => void;
}

const ArtifactGallery: React.FC<ArtifactGalleryProps> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const theme = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterPhase, setFilterPhase] = useState('all');
  const [selectedArtifact, setSelectedArtifact] = useState<any | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [artifacts, setArtifacts] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState(0);
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);
  const [feedbackContent, setFeedbackContent] = useState('');

  // Agent colors and names
  const agentNames: Record<string, string> = {
    'market-research': 'Market Research',
    'seo-keyword': 'SEO Keyword',
    'content-strategy': 'Content Strategy',
    'content-generation': 'Content Writer',
    'seo-optimization': 'SEO Optimizer',
    'system': 'System',
    'user': 'User'
  };

  // Phase names
  const phaseNames: Record<string, string> = {
    [DynamicWorkflowPhase.PLANNING]: 'Planning',
    [DynamicWorkflowPhase.RESEARCH]: 'Research',
    [DynamicWorkflowPhase.CREATION]: 'Content Creation',
    [DynamicWorkflowPhase.REVIEW]: 'Review',
    [DynamicWorkflowPhase.FINALIZATION]: 'Finalization'
  };

  // Map agents to phases
  const agentPhaseMap: Record<string, DynamicWorkflowPhase> = {
    'market-research': DynamicWorkflowPhase.RESEARCH,
    'seo-keyword': DynamicWorkflowPhase.RESEARCH,
    'content-strategy': DynamicWorkflowPhase.CREATION,
    'content-generation': DynamicWorkflowPhase.REVIEW,
    'seo-optimization': DynamicWorkflowPhase.FINALIZATION
  };

  // Process artifacts from state
  useEffect(() => {
    if (!state || !state.artifacts || !state.generatedArtifacts) {
      setArtifacts([]);
      return;
    }

    const processedArtifacts = state.generatedArtifacts
      .map(id => {
        const artifact = state.artifacts[id];
        if (!artifact) return null;

        // Determine the phase this artifact belongs to
        let phase = DynamicWorkflowPhase.PLANNING;
        if (artifact.creator && agentPhaseMap[artifact.creator]) {
          phase = agentPhaseMap[artifact.creator];
        }

        // Extract feedback
        const feedback = extractFeedback(artifact);

        // Extract related goals
        const relatedGoals = extractRelatedGoals(id);

        // Determine if this artifact contributed to any completed goals
        const contributedToCompletedGoals = relatedGoals.some(goal => goal.status === 'completed');

        return {
          id,
          name: artifact.name || `Artifact ${id.substring(0, 6)}`,
          type: artifact.type || 'unknown',
          creator: artifact.creator,
          creatorName: agentNames[artifact.creator] || artifact.creator,
          timestamp: artifact.timestamp || new Date().toISOString(),
          content: extractContent(artifact),
          status: artifact.status || 'completed',
          qualityScore: artifact.qualityScore || 0,
          phase: phase,
          phaseName: phaseNames[phase] || 'Unknown Phase',
          feedback: feedback,
          hasFeedback: feedback.length > 0,
          relatedGoals: relatedGoals,
          contributedToCompletedGoals: contributedToCompletedGoals,
          iterations: artifact.iterations || [],
          rawArtifact: artifact // Store the raw artifact for detailed view
        };
      })
      .filter(Boolean);

    // Sort by timestamp (newest first)
    processedArtifacts.sort((a, b) =>
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );

    setArtifacts(processedArtifacts);
  }, [state]);

  // Extract content from artifact
  const extractContent = (artifact: any): string => {
    if (!artifact) return '';

    // Handle different content formats
    if (typeof artifact.content === 'string') {
      return artifact.content;
    }

    if (typeof artifact.text === 'string') {
      return artifact.text;
    }

    if (artifact.data) {
      if (typeof artifact.data === 'string') {
        return artifact.data;
      }

      if (typeof artifact.data === 'object') {
        // Try to extract content from common fields
        const possibleContentFields = ['content', 'text', 'body', 'description', 'summary'];
        for (const field of possibleContentFields) {
          if (artifact.data[field] && typeof artifact.data[field] === 'string') {
            return artifact.data[field];
          }
        }

        // If no specific field found, stringify the object
        return JSON.stringify(artifact.data, null, 2);
      }
    }

    return 'No content available';
  };

  // Extract feedback from artifact
  const extractFeedback = (artifact: any): any[] => {
    if (!artifact) return [];

    // Check for feedback in different possible locations
    if (Array.isArray(artifact.feedback)) {
      return artifact.feedback;
    }

    if (artifact.iterations && Array.isArray(artifact.iterations)) {
      // Collect feedback from all iterations
      return artifact.iterations.flatMap((iteration: any) =>
        Array.isArray(iteration.feedback) ? iteration.feedback : []
      );
    }

    return [];
  };

  // Extract goals that this artifact contributed to
  const extractRelatedGoals = (artifactId: string): any[] => {
    if (!state || !state.goals) return [];

    return Object.entries(state.goals)
      .filter(([_, goal]: [string, any]) =>
        goal.artifacts && Array.isArray(goal.artifacts) && goal.artifacts.includes(artifactId)
      )
      .map(([id, goal]: [string, any]) => ({
        id,
        description: goal.description,
        status: goal.status,
        progress: goal.progress || 0,
        type: goal.type
      }));
  };

  // Filter artifacts
  const filteredArtifacts = artifacts.filter(artifact => {
    // Apply search filter
    const matchesSearch =
      artifact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      artifact.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      artifact.creatorName.toLowerCase().includes(searchTerm.toLowerCase());

    // Apply type filter
    const matchesType = filterType === 'all' || artifact.type === filterType;

    // Apply phase filter
    const matchesPhase = filterPhase === 'all' || artifact.phase === filterPhase;

    return matchesSearch && matchesType && matchesPhase;
  });

  // Get unique artifact types for filter
  const artifactTypes = ['all', ...new Set(artifacts.map(a => a.type))];

  // Get phases for filter
  const phases = [
    { value: 'all', label: 'All Phases' },
    { value: DynamicWorkflowPhase.PLANNING, label: phaseNames[DynamicWorkflowPhase.PLANNING] },
    { value: DynamicWorkflowPhase.RESEARCH, label: phaseNames[DynamicWorkflowPhase.RESEARCH] },
    { value: DynamicWorkflowPhase.CREATION, label: phaseNames[DynamicWorkflowPhase.CREATION] },
    { value: DynamicWorkflowPhase.REVIEW, label: phaseNames[DynamicWorkflowPhase.REVIEW] },
    { value: DynamicWorkflowPhase.FINALIZATION, label: phaseNames[DynamicWorkflowPhase.FINALIZATION] }
  ];

  // Format time
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString([], {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Handle artifact click
  const handleArtifactClick = (artifact: any) => {
    setSelectedArtifact(artifact);
    setDialogOpen(true);
  };

  // Close dialog
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  // Handle feedback dialog
  const handleOpenFeedbackDialog = () => {
    setFeedbackDialogOpen(true);
  };

  const handleCloseFeedbackDialog = () => {
    setFeedbackDialogOpen(false);
    setFeedbackContent('');
  };

  const handleSubmitFeedback = async () => {
    if (!selectedArtifact || !feedbackContent.trim() || !sessionId) {
      return;
    }

    try {
      // Call the API to submit feedback
      const response = await fetch(`/api/agents/dynamic-collaboration-v2/feedback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          artifactId: selectedArtifact.id,
          feedback: feedbackContent,
          from: 'User',
          timestamp: new Date().toISOString()
        }),
      });

      if (response.ok) {
        // Show success message
        alert('Feedback submitted successfully');

        // Close the dialog
        handleCloseFeedbackDialog();

        // Refresh the artifacts
        if (onRefresh) {
          onRefresh();
        }
      } else {
        // Show error message
        alert('Failed to submit feedback');
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      alert('Error submitting feedback');
    }
  };

  // Get artifact icon based on type
  const getArtifactIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'market-research':
      case 'research':
        return <BarChartIcon />;
      case 'seo-keyword':
      case 'keyword':
      case 'keywords':
        return <TuneIcon />;
      case 'content-strategy':
      case 'strategy':
        return <AssignmentIcon />;
      default:
        return <DescriptionIcon />;
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  return (
    <Box>
      <Paper elevation={1} sx={{ p: 2, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Artifacts</Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <TextField
              placeholder="Search artifacts..."
              size="small"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ width: 200 }}
            />

            <FormControl size="small" sx={{ minWidth: 120, mr: 1 }}>
              <InputLabel>Type</InputLabel>
              <Select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                label="Type"
                startAdornment={
                  <InputAdornment position="start">
                    <FilterListIcon />
                  </InputAdornment>
                }
              >
                {artifactTypes.map(type => (
                  <MenuItem key={type} value={type}>
                    {type === 'all' ? 'All Types' : type}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 150 }}>
              <InputLabel>Phase</InputLabel>
              <Select
                value={filterPhase}
                onChange={(e) => setFilterPhase(e.target.value)}
                label="Phase"
              >
                {phases.map(phase => (
                  <MenuItem key={phase.value} value={phase.value}>
                    {phase.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Box>

        {loading && artifacts.length === 0 ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
            <CircularProgress />
          </Box>
        ) : filteredArtifacts.length === 0 ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
            <Typography variant="body1" color="text.secondary">
              {artifacts.length === 0 ? 'No artifacts available' : 'No artifacts match your filters'}
            </Typography>
          </Box>
        ) : (
          <Grid container spacing={2}>
            {filteredArtifacts.map(artifact => (
              <Grid item xs={12} sm={6} md={4} key={artifact.id}>
                <Card
                  variant="outlined"
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'all 0.2s',
                    '&:hover': {
                      boxShadow: 3,
                      transform: 'translateY(-2px)'
                    }
                  }}
                >
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Box sx={{ mr: 1, color: 'primary.main' }}>
                        {getArtifactIcon(artifact.type)}
                      </Box>
                      <Typography variant="h6" component="div" noWrap>
                        {artifact.name}
                      </Typography>
                      {artifact.contributedToCompletedGoals && (
                        <Tooltip title="Contributed to completed goals">
                          <CheckCircleIcon color="success" sx={{ ml: 1 }} />
                        </Tooltip>
                      )}
                    </Box>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Chip
                        label={artifact.type}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                      <Typography variant="caption" color="text.secondary">
                        {formatTime(artifact.timestamp)}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        Created by: {artifact.creatorName}
                      </Typography>
                      <Chip
                        label={artifact.phaseName}
                        size="small"
                        color="secondary"
                        variant="outlined"
                        sx={{ ml: 1 }}
                      />
                    </Box>

                    {artifact.relatedGoals.length > 0 && (
                      <Box sx={{ mb: 1 }}>
                        <Typography variant="body2" color="text.secondary">
                          Related Goals:
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                          {artifact.relatedGoals.map((goal: any) => (
                            <Chip
                              key={goal.id}
                              label={goal.description}
                              size="small"
                              color={goal.status === 'completed' ? 'success' : 'default'}
                              variant="outlined"
                              sx={{ mb: 0.5 }}
                            />
                          ))}
                        </Box>
                      </Box>
                    )}

                    {artifact.hasFeedback && (
                      <Box sx={{ mb: 1 }}>
                        <Typography variant="body2" color="text.secondary">
                          Feedback: {artifact.feedback.length} comment{artifact.feedback.length !== 1 ? 's' : ''}
                        </Typography>
                      </Box>
                    )}

                    <Box
                      sx={{
                        height: 60,
                        overflow: 'hidden',
                        position: 'relative',
                        '&::after': {
                          content: '""',
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          height: 40,
                          background: 'linear-gradient(transparent, white)'
                        }
                      }}
                    >
                      <Typography variant="body2">
                        {artifact.content.substring(0, 150)}
                        {artifact.content.length > 150 ? '...' : ''}
                      </Typography>
                    </Box>
                  </CardContent>

                  <CardActions>
                    <Button
                      startIcon={<VisibilityIcon />}
                      onClick={() => handleArtifactClick(artifact)}
                      size="small"
                    >
                      View Details
                    </Button>

                    {artifact.qualityScore > 0 && (
                      <Tooltip title="Quality Score">
                        <Chip
                          label={`${artifact.qualityScore}/100`}
                          size="small"
                          color={
                            artifact.qualityScore >= 80 ? 'success' :
                            artifact.qualityScore >= 60 ? 'primary' :
                            artifact.qualityScore >= 40 ? 'warning' : 'error'
                          }
                          sx={{ ml: 'auto' }}
                        />
                      </Tooltip>
                    )}
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Paper>

      {/* Artifact Detail Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        {selectedArtifact && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box sx={{ mr: 1, color: 'primary.main' }}>
                    {getArtifactIcon(selectedArtifact.type)}
                  </Box>
                  <Typography variant="h6">{selectedArtifact.name}</Typography>
                </Box>
                <IconButton onClick={handleCloseDialog}>
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>

            <Divider />

            <DialogContent>
              <Box sx={{ mb: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Type
                    </Typography>
                    <Typography variant="body1">
                      {selectedArtifact.type}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Created By
                    </Typography>
                    <Typography variant="body1">
                      {selectedArtifact.creatorName}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Created At
                    </Typography>
                    <Typography variant="body1">
                      {formatTime(selectedArtifact.timestamp)}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Workflow Phase
                    </Typography>
                    <Chip
                      label={selectedArtifact.phaseName}
                      size="small"
                      color="secondary"
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Status
                    </Typography>
                    <Chip
                      label={selectedArtifact.status}
                      size="small"
                      color={
                        selectedArtifact.status === 'completed' ? 'success' :
                        selectedArtifact.status === 'in-progress' ? 'primary' : 'default'
                      }
                    />
                  </Grid>

                  {selectedArtifact.qualityScore > 0 && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Quality Score
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box
                          sx={{
                            width: '100%',
                            height: 10,
                            bgcolor: 'grey.200',
                            borderRadius: 5,
                            mr: 1
                          }}
                        >
                          <Box
                            sx={{
                              width: `${selectedArtifact.qualityScore}%`,
                              height: '100%',
                              bgcolor:
                                selectedArtifact.qualityScore >= 80 ? 'success.main' :
                                selectedArtifact.qualityScore >= 60 ? 'primary.main' :
                                selectedArtifact.qualityScore >= 40 ? 'warning.main' : 'error.main',
                              borderRadius: 5
                            }}
                          />
                        </Box>
                        <Typography variant="body2">
                          {selectedArtifact.qualityScore}/100
                        </Typography>
                      </Box>
                    </Grid>
                  )}

                  {/* Related Goals Section */}
                  {selectedArtifact.relatedGoals && selectedArtifact.relatedGoals.length > 0 && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Related Goals
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selectedArtifact.relatedGoals.map((goal: any) => (
                          <Chip
                            key={goal.id}
                            label={goal.description}
                            size="small"
                            color={goal.status === 'completed' ? 'success' : 'default'}
                            icon={goal.status === 'completed' ? <CheckCircleIcon /> : undefined}
                            sx={{ mb: 0.5 }}
                          />
                        ))}
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="h6" gutterBottom>
                Content
              </Typography>

              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  maxHeight: 400,
                  overflow: 'auto',
                  bgcolor: 'grey.50'
                }}
              >
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {selectedArtifact.content}
                </ReactMarkdown>
              </Paper>

              {/* Feedback Section */}
              {selectedArtifact.feedback && selectedArtifact.feedback.length > 0 && (
                <>
                  <Divider sx={{ my: 2 }} />

                  <Typography variant="h6" gutterBottom>
                    Feedback & Evaluations
                  </Typography>

                  <Box sx={{ mb: 2 }}>
                    {selectedArtifact.feedback.map((feedback: any, index: number) => (
                      <Paper
                        key={index}
                        variant="outlined"
                        sx={{ p: 2, mb: 2, bgcolor: 'background.paper' }}
                      >
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="subtitle2">
                            From: {feedback.from || 'Anonymous'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {feedback.timestamp ? formatTime(feedback.timestamp) : 'Unknown time'}
                          </Typography>
                        </Box>

                        <Typography variant="body1" paragraph>
                          {feedback.content || feedback.feedback || 'No content'}
                        </Typography>

                        {feedback.evaluation && (
                          <Box sx={{ mt: 1 }}>
                            <Typography variant="subtitle2" gutterBottom>
                              Evaluation Metrics
                            </Typography>

                            {feedback.evaluation.score !== undefined && (
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                <Typography variant="body2" sx={{ mr: 1 }}>
                                  Overall Score:
                                </Typography>
                                <Chip
                                  label={`${Math.round(feedback.evaluation.score * 100)}%`}
                                  size="small"
                                  color={
                                    feedback.evaluation.score >= 0.7 ? 'success' :
                                    feedback.evaluation.score >= 0.4 ? 'warning' : 'error'
                                  }
                                />
                              </Box>
                            )}

                            {feedback.evaluation.criteriaResults && feedback.evaluation.criteriaResults.length > 0 && (
                              <Box sx={{ mt: 1 }}>
                                {feedback.evaluation.criteriaResults.map((criterion: any, idx: number) => (
                                  <Box key={idx} sx={{ mb: 1 }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                      <Typography variant="body2" sx={{ fontWeight: 'bold', mr: 1 }}>
                                        {criterion.criterion}:
                                      </Typography>
                                      <Chip
                                        label={`${Math.round(criterion.score * 100)}%`}
                                        size="small"
                                        color={
                                          criterion.score >= 0.7 ? 'success' :
                                          criterion.score >= 0.4 ? 'warning' : 'error'
                                        }
                                      />
                                    </Box>

                                    {criterion.feedback && (
                                      <Typography variant="body2" sx={{ ml: 2 }}>
                                        {criterion.feedback}
                                      </Typography>
                                    )}
                                  </Box>
                                ))}
                              </Box>
                            )}
                          </Box>
                        )}
                      </Paper>
                    ))}
                  </Box>
                </>
              )}

              {/* Iterations Section */}
              {selectedArtifact.iterations && selectedArtifact.iterations.length > 1 && (
                <>
                  <Divider sx={{ my: 2 }} />

                  <Typography variant="h6" gutterBottom>
                    Iterations & Improvements
                  </Typography>

                  <Box sx={{ mb: 2 }}>
                    <Tabs value={activeTab} onChange={handleTabChange} variant="scrollable" scrollButtons="auto">
                      {selectedArtifact.iterations.map((iteration: any, idx: number) => (
                        <Tab key={idx} label={`Version ${idx + 1}`} />
                      ))}
                    </Tabs>

                    {selectedArtifact.iterations.map((iteration: any, idx: number) => (
                      <Box key={idx} sx={{ display: activeTab === idx ? 'block' : 'none', mt: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Version {idx + 1} - {iteration.agent ? agentNames[iteration.agent] || iteration.agent : 'Unknown'}
                        </Typography>

                        {iteration.timestamp && (
                          <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                            {formatTime(iteration.timestamp)}
                          </Typography>
                        )}

                        {iteration.improvementPlan && (
                          <Box sx={{ mt: 1, mb: 2 }}>
                            <Typography variant="subtitle2" gutterBottom>
                              Improvement Plan
                            </Typography>
                            <Paper variant="outlined" sx={{ p: 1, bgcolor: 'background.paper' }}>
                              <Typography variant="body2">
                                {iteration.improvementPlan}
                              </Typography>
                            </Paper>
                          </Box>
                        )}
                      </Box>
                    ))}
                  </Box>
                </>
              )}
            </DialogContent>

            <DialogActions>
              <Button
                startIcon={<FeedbackIcon />}
                onClick={handleOpenFeedbackDialog}
                color="primary"
              >
                Add Feedback
              </Button>
              <Button onClick={handleCloseDialog}>Close</Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Feedback Dialog */}
      <Dialog
        open={feedbackDialogOpen}
        onClose={handleCloseFeedbackDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Add Feedback</Typography>
            <IconButton onClick={handleCloseFeedbackDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Your feedback will be sent to the agents to improve this artifact.
          </Typography>

          <TextField
            autoFocus
            margin="dense"
            id="feedback"
            label="Your Feedback"
            fullWidth
            multiline
            rows={4}
            value={feedbackContent}
            onChange={(e) => setFeedbackContent(e.target.value)}
          />
        </DialogContent>

        <DialogActions>
          <Button onClick={handleCloseFeedbackDialog}>Cancel</Button>
          <Button
            onClick={handleSubmitFeedback}
            variant="contained"
            color="primary"
            disabled={!feedbackContent.trim()}
          >
            Submit Feedback
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ArtifactGallery;
