/**
 * Integration Tests for Feedback Regeneration System
 * 
 * Tests the complete flow from human feedback to artifact regeneration
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { UnifiedFeedbackProcessor } from '../../src/core/feedback/unified-processor';

// Mock the workflow engine and feedback loop system
const mockGetArtifact = jest.fn();
const mockUpdateArtifact = jest.fn();
const mockStoreArtifact = jest.fn();

jest.mock('../../src/core/workflow/singleton', () => ({
  getWorkflowEngine: () => ({
    getArtifact: mockGetArtifact,
    updateArtifact: mockUpdateArtifact,
    storeArtifact: mockStoreArtifact
  })
}));

const mockGenerateFeedback = jest.fn();
const mockProvideFeedback = jest.fn();
const mockIncorporateFeedback = jest.fn();

jest.mock('../../src/app/(payload)/api/agents/dynamic-collaboration-v3/utils/feedback-loop-system', () => ({
  FeedbackLoopSystem: jest.fn().mockImplementation(() => ({
    generateFeedback: mockGenerateFeedback,
    provideFeedback: mockProvideFeedback,
    incorporateFeedback: mockIncorporateFeedback
  }))
}));

describe('Feedback Regeneration Integration', () => {
  let processor: UnifiedFeedbackProcessor;
  let mockArtifact: any;

  beforeEach(() => {
    processor = new UnifiedFeedbackProcessor('test-session');

    mockArtifact = {
      id: 'test-artifact-123',
      title: 'Test Article',
      content: 'This is a test article content.',
      type: 'content_draft',
      version: 1,
      status: 'pending_approval',
      createdAt: new Date().toISOString(),
      metadata: {}
    };

    // Setup mock implementations
    mockGetArtifact.mockResolvedValue(mockArtifact);
    mockUpdateArtifact.mockResolvedValue(true);
    mockStoreArtifact.mockResolvedValue(true);
    mockGenerateFeedback.mockResolvedValue({ feedback: 'AI generated feedback' });
    mockProvideFeedback.mockResolvedValue(true);
    mockIncorporateFeedback.mockResolvedValue('improved-content-id');
  });

  afterEach(() => {
    jest.clearAllMocks();
    mockGetArtifact.mockClear();
    mockUpdateArtifact.mockClear();
    mockStoreArtifact.mockClear();
    mockGenerateFeedback.mockClear();
    mockProvideFeedback.mockClear();
    mockIncorporateFeedback.mockClear();
  });

  describe('Human Feedback Processing', () => {
    it('should process actionable feedback and trigger regeneration', async () => {
      const feedback = 'Please improve the clarity and add more specific examples.';
      const approver = 'test-reviewer';

      const result = await processor.processHumanFeedback(
        mockArtifact.id,
        feedback,
        approver,
        true // isRejection
      );

      expect(result).toBeTruthy();
      expect(result?.humanFeedback.feedback).toBe(feedback);
      expect(result?.humanFeedback.approver).toBe(approver);
      expect(result?.humanFeedback.feedbackType).toBe('rejection');
      expect(result?.status).toBe('pending');
    });

    it('should not trigger regeneration for non-actionable feedback', async () => {
      const feedback = 'Bad'; // Too short and not actionable
      const approver = 'test-reviewer';

      const result = await processor.processHumanFeedback(
        mockArtifact.id,
        feedback,
        approver,
        true
      );

      expect(result).toBeNull();
    });

    it('should extract specific areas from feedback', async () => {
      const feedback = 'The content lacks clarity and has accuracy issues. Please improve the writing style.';
      const approver = 'test-reviewer';

      const result = await processor.processHumanFeedback(
        mockArtifact.id,
        feedback,
        approver,
        true
      );

      expect(result?.humanFeedback.specificAreas).toContain('clarity');
      expect(result?.humanFeedback.specificAreas).toContain('accuracy');
      expect(result?.humanFeedback.specificAreas).toContain('style');
    });
  });

  describe('Feedback Quality Validation', () => {
    const testCases = [
      { feedback: '', expected: false, description: 'empty feedback' },
      { feedback: 'Bad', expected: false, description: 'too short feedback' },
      { feedback: 'This is good', expected: false, description: 'non-actionable feedback' },
      { feedback: 'Please improve the content quality', expected: true, description: 'actionable feedback' },
      { feedback: 'Add more details and clarify the main points', expected: true, description: 'specific actionable feedback' },
      { feedback: 'The structure needs to be better organized', expected: true, description: 'structural feedback' }
    ];

    testCases.forEach(({ feedback, expected, description }) => {
      it(`should ${expected ? 'accept' : 'reject'} ${description}`, async () => {
        const result = await processor.processHumanFeedback(
          mockArtifact.id,
          feedback,
          'test-reviewer',
          true
        );

        if (expected) {
          expect(result).toBeTruthy();
        } else {
          expect(result).toBeNull();
        }
      });
    });
  });

  describe('Specific Area Extraction', () => {
    const testCases = [
      {
        feedback: 'The content quality is poor',
        expectedAreas: ['content quality']
      },
      {
        feedback: 'This is confusing and unclear',
        expectedAreas: ['clarity']
      },
      {
        feedback: 'The information is wrong and inaccurate',
        expectedAreas: ['accuracy']
      },
      {
        feedback: 'The structure is bad and missing important details',
        expectedAreas: ['structure', 'completeness']
      },
      {
        feedback: 'Improve the writing style and tone',
        expectedAreas: ['style']
      }
    ];

    testCases.forEach(({ feedback, expectedAreas }) => {
      it(`should extract areas ${expectedAreas.join(', ')} from feedback`, async () => {
        const result = await processor.processHumanFeedback(
          mockArtifact.id,
          feedback,
          'test-reviewer',
          true
        );

        expect(result).toBeTruthy();
        expectedAreas.forEach(area => {
          expect(result?.humanFeedback.specificAreas).toContain(area);
        });
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle errors gracefully during regeneration', async () => {
      // Mock an error in the regeneration process
      const mockError = new Error('Regeneration failed');
      
      // This would need to be implemented based on the actual error handling
      // For now, we just test that the processor doesn't crash
      const feedback = 'Please improve this content';
      
      await expect(
        processor.processHumanFeedback(
          mockArtifact.id,
          feedback,
          'test-reviewer',
          true
        )
      ).resolves.toBeTruthy();
    });

    it('should handle missing artifacts gracefully', async () => {
      const feedback = 'Please improve this content';
      
      // Test with non-existent artifact
      await expect(
        processor.processHumanFeedback(
          'non-existent-artifact',
          feedback,
          'test-reviewer',
          true
        )
      ).rejects.toThrow();
    });
  });

  describe('Regeneration Request Tracking', () => {
    it('should create proper regeneration request structure', async () => {
      const feedback = 'Please add more examples and improve clarity';
      const approver = 'test-reviewer';

      const result = await processor.processHumanFeedback(
        mockArtifact.id,
        feedback,
        approver,
        true
      );

      expect(result).toMatchObject({
        id: expect.any(String),
        originalArtifactId: mockArtifact.id,
        humanFeedback: {
          artifactId: mockArtifact.id,
          feedback,
          approver,
          timestamp: expect.any(String),
          feedbackType: 'rejection',
          specificAreas: expect.any(Array)
        },
        status: 'pending',
        createdAt: expect.any(String)
      });
    });

    it('should track feedback cycles for analytics', async () => {
      const trackSpy = jest.spyOn(processor, 'trackFeedbackCycle');
      
      await processor.trackFeedbackCycle(
        'original-123',
        'test feedback',
        'new-456'
      );

      expect(trackSpy).toHaveBeenCalledWith(
        'original-123',
        'test feedback',
        'new-456'
      );
    });
  });
});

describe('API Integration', () => {
  it('should integrate with approval API endpoint', async () => {
    // This would test the actual API endpoint integration
    // For now, we just verify the structure is correct
    const mockRequest = {
      artifactId: 'test-artifact-123',
      approved: false,
      approver: 'test-reviewer',
      feedback: 'Please improve the content quality and add more examples',
      reason: 'Content needs improvement'
    };

    // Verify the request structure matches what the API expects
    expect(mockRequest).toMatchObject({
      artifactId: expect.any(String),
      approved: expect.any(Boolean),
      approver: expect.any(String),
      feedback: expect.any(String),
      reason: expect.any(String)
    });
  });
});
