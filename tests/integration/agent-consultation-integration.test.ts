/**
 * Integration Tests for Fresh Dynamic Agent Consultation System
 *
 * Tests the integration between workflow system and fresh agent consultation system
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { DynamicAgentConsultationService } from '../../src/core/workflow/dynamic-agent-consultation-service';

// Mock the consultation manager
jest.mock('../../src/app/(payload)/api/agents/collaborative-iteration/utils/consultation-manager', () => ({
  ConsultationManager: jest.fn().mockImplementation(() => ({
    requestConsultation: jest.fn().mockResolvedValue('consultation-123'),
    getConsultation: jest.fn().mockResolvedValue({
      id: 'consultation-123',
      response: 'Mock agent response with valuable insights',
      confidence: 0.85,
      suggestions: [
        { area: 'keyword optimization', suggestion: 'Focus on long-tail keywords', priority: 'high' },
        { area: 'content structure', suggestion: 'Add more subheadings', priority: 'medium' }
      ]
    })
  }))
}));

describe('Agent Consultation Integration', () => {
  let consultationService: WorkflowAgentConsultationService;

  beforeEach(() => {
    consultationService = new WorkflowAgentConsultationService();
  });

  afterEach(() => {
    consultationService.clearMetrics();
    jest.clearAllMocks();
  });

  describe('Basic Agent Consultation', () => {
    it('should successfully request consultation from agents', async () => {
      const config = {
        enabled: true,
        triggers: [
          {
            type: 'always' as const,
            agents: ['seo-keyword', 'market-research'],
            priority: 'high' as const
          }
        ],
        maxConsultations: 2,
        timeoutMs: 5000,
        fallbackBehavior: 'continue' as const
      };

      const context = {
        topic: 'sustainable fashion trends',
        contentType: 'blog-post',
        targetAudience: 'eco-conscious consumers'
      };

      const results = await consultationService.consultAgentForStep(
        'workflow-123',
        'keyword-research',
        'AI_GENERATION',
        context,
        config
      );

      expect(results).toHaveLength(2);
      expect(results[0]).toMatchObject({
        consultationId: expect.any(String),
        agentId: expect.any(String),
        response: expect.any(String),
        confidence: expect.any(Number),
        suggestions: expect.any(Array)
      });
    });

    it('should handle consultation timeouts gracefully', async () => {
      const config = {
        enabled: true,
        triggers: [
          {
            type: 'always' as const,
            agents: ['seo-keyword'],
            priority: 'high' as const
          }
        ],
        maxConsultations: 1,
        timeoutMs: 100, // Very short timeout
        fallbackBehavior: 'continue' as const
      };

      const context = {
        topic: 'test topic',
        contentType: 'blog-post'
      };

      const results = await consultationService.consultAgentForStep(
        'workflow-123',
        'test-step',
        'AI_GENERATION',
        context,
        config
      );

      // Should return empty results due to timeout, but not throw error
      expect(results).toEqual([]);
    });
  });

  describe('Consultation Triggers', () => {
    it('should trigger consultation based on quality threshold', async () => {
      const config = {
        enabled: true,
        triggers: [
          {
            type: 'quality_threshold' as const,
            condition: { threshold: 0.8 },
            agents: ['content-strategy'],
            priority: 'medium' as const
          }
        ],
        maxConsultations: 1,
        timeoutMs: 5000,
        fallbackBehavior: 'continue' as const
      };

      // Context with low quality score should trigger consultation
      const lowQualityContext = {
        topic: 'test topic',
        qualityScore: 0.6 // Below threshold
      };

      const results = await consultationService.consultAgentForStep(
        'workflow-123',
        'test-step',
        'AI_GENERATION',
        lowQualityContext,
        config
      );

      expect(results).toHaveLength(1);

      // Context with high quality score should not trigger consultation
      const highQualityContext = {
        topic: 'test topic',
        qualityScore: 0.9 // Above threshold
      };

      const noResults = await consultationService.consultAgentForStep(
        'workflow-456',
        'test-step',
        'AI_GENERATION',
        highQualityContext,
        config
      );

      expect(noResults).toHaveLength(0);
    });

    it('should trigger consultation based on feedback keywords', async () => {
      const config = {
        enabled: true,
        triggers: [
          {
            type: 'feedback_keywords' as const,
            condition: { keywords: ['seo', 'keywords', 'optimization'] },
            agents: ['seo-keyword'],
            priority: 'high' as const
          }
        ],
        maxConsultations: 1,
        timeoutMs: 5000,
        fallbackBehavior: 'continue' as const
      };

      // Context with matching feedback keywords should trigger consultation
      const feedbackContext = {
        topic: 'test topic',
        feedback: 'This content needs better SEO optimization and keyword targeting'
      };

      const results = await consultationService.consultAgentForStep(
        'workflow-123',
        'test-step',
        'AI_GENERATION',
        feedbackContext,
        config
      );

      expect(results).toHaveLength(1);

      // Context without matching keywords should not trigger consultation
      const noFeedbackContext = {
        topic: 'test topic',
        feedback: 'This content is great, just needs minor formatting changes'
      };

      const noResults = await consultationService.consultAgentForStep(
        'workflow-456',
        'test-step',
        'AI_GENERATION',
        noFeedbackContext,
        config
      );

      expect(noResults).toHaveLength(0);
    });

    it('should trigger consultation based on content complexity', async () => {
      const config = {
        enabled: true,
        triggers: [
          {
            type: 'content_complexity' as const,
            condition: { threshold: 0.5 },
            agents: ['content-strategy'],
            priority: 'medium' as const
          }
        ],
        maxConsultations: 1,
        timeoutMs: 5000,
        fallbackBehavior: 'continue' as const
      };

      // Complex content should trigger consultation
      const complexContext = {
        topic: 'advanced machine learning algorithms',
        content: 'This is a very long technical article about complex algorithms and implementation details with API references and framework architecture considerations...',
        targetAudience: 'technical experts'
      };

      const results = await consultationService.consultAgentForStep(
        'workflow-123',
        'test-step',
        'AI_GENERATION',
        complexContext,
        config
      );

      expect(results).toHaveLength(1);

      // Simple content should not trigger consultation
      const simpleContext = {
        topic: 'simple topic',
        content: 'Short simple content',
        targetAudience: 'general audience'
      };

      const noResults = await consultationService.consultAgentForStep(
        'workflow-456',
        'test-step',
        'AI_GENERATION',
        simpleContext,
        config
      );

      expect(noResults).toHaveLength(0);
    });
  });

  describe('Consultation Metrics', () => {
    it('should track consultation metrics correctly', async () => {
      const config = {
        enabled: true,
        triggers: [
          {
            type: 'always' as const,
            agents: ['seo-keyword'],
            priority: 'high' as const
          }
        ],
        maxConsultations: 1,
        timeoutMs: 5000,
        fallbackBehavior: 'continue' as const
      };

      const context = {
        topic: 'test topic',
        contentType: 'blog-post'
      };

      // Perform consultation
      await consultationService.consultAgentForStep(
        'workflow-123',
        'test-step',
        'AI_GENERATION',
        context,
        config
      );

      // Check metrics
      const metrics = consultationService.getMetrics();
      expect(metrics.totalConsultations).toBe(1);
      expect(metrics.successRate).toBeGreaterThan(0);
      expect(metrics.averageResponseTime).toBeGreaterThan(0);
      expect(metrics.agentUtilization['seo-keyword']).toBe(1);
    });

    it('should handle multiple consultations and update metrics', async () => {
      const config = {
        enabled: true,
        triggers: [
          {
            type: 'always' as const,
            agents: ['seo-keyword', 'market-research'],
            priority: 'high' as const
          }
        ],
        maxConsultations: 2,
        timeoutMs: 5000,
        fallbackBehavior: 'continue' as const
      };

      const context = {
        topic: 'test topic',
        contentType: 'blog-post'
      };

      // Perform multiple consultations
      await consultationService.consultAgentForStep(
        'workflow-123',
        'test-step-1',
        'AI_GENERATION',
        context,
        config
      );

      await consultationService.consultAgentForStep(
        'workflow-123',
        'test-step-2',
        'AI_GENERATION',
        context,
        config
      );

      // Check metrics
      const metrics = consultationService.getMetrics();
      expect(metrics.totalConsultations).toBe(2);
      expect(metrics.agentUtilization['seo-keyword']).toBe(2);
      expect(metrics.agentUtilization['market-research']).toBe(2);
    });
  });

  describe('Error Handling', () => {
    it('should handle consultation failures gracefully with continue fallback', async () => {
      // Mock consultation failure
      const mockConsultationManager = require('../../src/app/(payload)/api/agents/collaborative-iteration/utils/consultation-manager').ConsultationManager;
      mockConsultationManager.mockImplementation(() => ({
        requestConsultation: jest.fn().mockRejectedValue(new Error('Agent unavailable')),
        getConsultation: jest.fn().mockRejectedValue(new Error('Consultation failed'))
      }));

      const config = {
        enabled: true,
        triggers: [
          {
            type: 'always' as const,
            agents: ['seo-keyword'],
            priority: 'high' as const
          }
        ],
        maxConsultations: 1,
        timeoutMs: 5000,
        fallbackBehavior: 'continue' as const
      };

      const context = {
        topic: 'test topic'
      };

      // Should not throw error with continue fallback
      const results = await consultationService.consultAgentForStep(
        'workflow-123',
        'test-step',
        'AI_GENERATION',
        context,
        config
      );

      expect(results).toEqual([]);
    });

    it('should throw error with fail fallback behavior', async () => {
      // Mock consultation failure
      const mockConsultationManager = require('../../src/app/(payload)/api/agents/collaborative-iteration/utils/consultation-manager').ConsultationManager;
      mockConsultationManager.mockImplementation(() => ({
        requestConsultation: jest.fn().mockRejectedValue(new Error('Agent unavailable')),
        getConsultation: jest.fn().mockRejectedValue(new Error('Consultation failed'))
      }));

      const config = {
        enabled: true,
        triggers: [
          {
            type: 'always' as const,
            agents: ['seo-keyword'],
            priority: 'high' as const
          }
        ],
        maxConsultations: 1,
        timeoutMs: 5000,
        fallbackBehavior: 'fail' as const
      };

      const context = {
        topic: 'test topic'
      };

      // Should throw error with fail fallback
      await expect(
        consultationService.consultAgentForStep(
          'workflow-123',
          'test-step',
          'AI_GENERATION',
          context,
          config
        )
      ).rejects.toThrow();
    });
  });
});
