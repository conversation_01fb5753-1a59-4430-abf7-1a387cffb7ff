/**
 * Integration Tests for Workflow Engine with Dynamic Agent Consultation
 * 
 * Tests the integration of the fresh agent consultation system with the existing workflow engine
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { WorkflowEngine } from '../../src/core/workflow/engine';
import { ISimplifiedStateStore } from '../../src/core/state/types';
import { IAIManager } from '../../src/core/ai/types';
import { 
  Workflow, 
  WorkflowStep, 
  StepType, 
  AgentConsultationConfig,
  ExecutionStatus,
  StepStatus 
} from '../../src/core/workflow/types';

// Mock dependencies
const mockStateStore: ISimplifiedStateStore = {
  getWorkflow: jest.fn(),
  setWorkflow: jest.fn(),
  getExecution: jest.fn(),
  setExecution: jest.fn(),
  getArtifact: jest.fn(),
  setArtifact: jest.fn(),
  deleteArtifact: jest.fn(),
  listArtifacts: jest.fn(),
  getApprovalGate: jest.fn(),
  setApprovalGate: jest.fn(),
  deleteApprovalGate: jest.fn(),
  listApprovalGates: jest.fn(),
  getContent: jest.fn(),
  setContent: jest.fn(),
  getAllWorkflows: jest.fn().mockResolvedValue([]),
  update: jest.fn()
};

const mockAIManager: IAIManager = {
  generate: jest.fn().mockResolvedValue({
    content: 'Generated AI content',
    usage: { tokens: 100 }
  }),
  getProviders: jest.fn().mockReturnValue(['openai']),
  validateConfig: jest.fn().mockReturnValue(true)
};

describe('Workflow Engine Integration with Agent Consultation', () => {
  let workflowEngine: WorkflowEngine;
  let mockWorkflow: Workflow;
  let mockExecution: any;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Initialize workflow engine
    workflowEngine = new WorkflowEngine(mockStateStore, mockAIManager);

    // Create mock workflow with agent consultation
    const consultationConfig: AgentConsultationConfig = {
      enabled: true,
      triggers: [
        {
          type: 'always',
          agents: ['seo-keyword', 'market-research'],
          priority: 'high'
        }
      ],
      maxConsultations: 2,
      timeoutMs: 30000,
      fallbackBehavior: 'continue'
    };

    mockWorkflow = {
      id: 'test-workflow',
      name: 'Test Workflow with Agent Consultation',
      description: 'Test workflow for agent consultation integration',
      version: '1.0.0',
      steps: [
        {
          id: 'topic-input',
          name: 'Topic Input',
          type: StepType.TEXT_INPUT,
          config: {
            prompt: 'Enter the topic for your content',
            placeholder: 'e.g., sustainable fashion trends'
          },
          inputs: [],
          outputs: ['topic'],
          dependencies: []
        },
        {
          id: 'enhanced-content-creation',
          name: 'Enhanced Content Creation',
          type: StepType.AI_GENERATION,
          config: {
            aiConfig: {
              provider: 'openai',
              model: 'gpt-4',
              prompt: 'Write a comprehensive blog post about {{topic}} for {{targetAudience}}',
              temperature: 0.7
            }
          },
          inputs: ['topic', 'targetAudience'],
          outputs: ['content'],
          dependencies: ['topic-input'],
          consultationConfig
        }
      ],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: {
        category: 'test',
        tags: ['test', 'agent-consultation'],
        difficulty: 'easy',
        estimatedTime: 30,
        userId: 'test-user'
      }
    };

    mockExecution = {
      id: 'test-execution-123',
      workflowId: 'test-workflow',
      status: ExecutionStatus.RUNNING,
      inputs: {
        topic: 'sustainable fashion trends',
        targetAudience: 'eco-conscious consumers'
      },
      outputs: {},
      stepResults: {
        'topic-input': {
          stepId: 'topic-input',
          status: StepStatus.COMPLETED,
          inputs: {},
          outputs: { topic: 'sustainable fashion trends' },
          startedAt: new Date().toISOString(),
          completedAt: new Date().toISOString(),
          stepType: StepType.TEXT_INPUT
        }
      },
      progress: 50,
      startedAt: new Date().toISOString()
    };

    // Setup mock implementations
    (mockStateStore.getWorkflow as jest.Mock).mockResolvedValue(mockWorkflow);
    (mockStateStore.getExecution as jest.Mock).mockResolvedValue(mockExecution);
    (mockStateStore.setExecution as jest.Mock).mockResolvedValue(undefined);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Enhanced AI Generation Step Integration', () => {
    it('should execute AI generation step with agent consultation enabled', async () => {
      // Mock the enhanced AI generation to be called
      const enhancedAIGenerationSpy = jest.spyOn(workflowEngine as any, 'executeEnhancedAIGeneration');
      enhancedAIGenerationSpy.mockResolvedValue({
        content: {
          title: 'Sustainable Fashion Trends - Enhanced with Agent Insights',
          content: 'AI-generated content enhanced with SEO and market research insights',
          metadata: {
            seoOptimized: true,
            marketResearched: true,
            enhancedWithAgents: true
          }
        },
        consultationSummary: {
          totalConsultations: 2,
          consultedAgents: ['seo-keyword', 'market-research'],
          averageConfidence: 0.85
        }
      });

      const stepResult = await workflowEngine.executeStep('test-execution-123', 'enhanced-content-creation');

      expect(stepResult).toBeDefined();
      expect(stepResult.status).toBe(StepStatus.COMPLETED);
      expect(stepResult.outputs.content).toBeDefined();
      expect(stepResult.outputs.content.metadata.enhancedWithAgents).toBe(true);
      expect(stepResult.outputs.consultationSummary).toBeDefined();
      expect(enhancedAIGenerationSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'enhanced-content-creation',
          consultationConfig: expect.objectContaining({
            enabled: true
          })
        }),
        expect.objectContaining({
          topic: 'sustainable fashion trends',
          targetAudience: 'eco-conscious consumers'
        }),
        'test-execution-123'
      );
    });

    it('should fall back to regular AI generation when consultation is disabled', async () => {
      // Modify workflow to disable consultation
      const stepWithoutConsultation = {
        ...mockWorkflow.steps[1],
        consultationConfig: {
          enabled: false,
          triggers: [],
          maxConsultations: 0,
          timeoutMs: 0,
          fallbackBehavior: 'continue' as const
        }
      };

      const workflowWithoutConsultation = {
        ...mockWorkflow,
        steps: [mockWorkflow.steps[0], stepWithoutConsultation]
      };

      (mockStateStore.getWorkflow as jest.Mock).mockResolvedValue(workflowWithoutConsultation);

      const stepResult = await workflowEngine.executeStep('test-execution-123', 'enhanced-content-creation');

      expect(stepResult).toBeDefined();
      expect(stepResult.status).toBe(StepStatus.COMPLETED);
      expect(stepResult.outputs.content).toBeDefined();
      expect(stepResult.outputs.consultationSummary).toBeUndefined();
    });

    it('should handle consultation failures gracefully with continue fallback', async () => {
      // Mock consultation failure
      const enhancedAIGenerationSpy = jest.spyOn(workflowEngine as any, 'executeEnhancedAIGeneration');
      enhancedAIGenerationSpy.mockResolvedValue({
        content: {
          title: 'Sustainable Fashion Trends',
          content: 'AI-generated content without agent enhancement',
          metadata: {
            enhancedWithAgents: false
          }
        },
        consultationSummary: {
          totalConsultations: 0,
          consultedAgents: [],
          averageConfidence: 0
        }
      });

      const stepResult = await workflowEngine.executeStep('test-execution-123', 'enhanced-content-creation');

      expect(stepResult).toBeDefined();
      expect(stepResult.status).toBe(StepStatus.COMPLETED);
      expect(stepResult.outputs.content).toBeDefined();
      expect(stepResult.outputs.content.metadata.enhancedWithAgents).toBe(false);
    });
  });

  describe('Workflow Template Integration', () => {
    it('should support consultation configuration in workflow templates', async () => {
      const step = mockWorkflow.steps.find(s => s.id === 'enhanced-content-creation');
      
      expect(step?.consultationConfig).toBeDefined();
      expect(step?.consultationConfig?.enabled).toBe(true);
      expect(step?.consultationConfig?.triggers).toHaveLength(1);
      expect(step?.consultationConfig?.triggers[0].agents).toContain('seo-keyword');
      expect(step?.consultationConfig?.triggers[0].agents).toContain('market-research');
    });

    it('should validate consultation configuration during workflow creation', async () => {
      const invalidConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'invalid-trigger-type' as any,
            agents: ['non-existent-agent' as any],
            priority: 'high' as const
          }
        ],
        maxConsultations: -1, // Invalid
        timeoutMs: 0, // Invalid
        fallbackBehavior: 'invalid-behavior' as any
      };

      const workflowWithInvalidConfig = {
        name: mockWorkflow.name,
        description: mockWorkflow.description,
        version: mockWorkflow.version,
        metadata: mockWorkflow.metadata,
        steps: [
          mockWorkflow.steps[0],
          {
            ...mockWorkflow.steps[1],
            consultationConfig: invalidConsultationConfig
          }
        ]
      };

      // The workflow engine should validate and handle invalid configurations
      await expect(async () => {
        await workflowEngine.createWorkflow(workflowWithInvalidConfig);
      }).not.toThrow(); // Should handle gracefully, not throw
    });
  });

  describe('Consultation Result Integration', () => {
    it('should store consultation results in step outputs', async () => {
      const enhancedAIGenerationSpy = jest.spyOn(workflowEngine as any, 'executeEnhancedAIGeneration');
      enhancedAIGenerationSpy.mockResolvedValue({
        content: {
          title: 'Enhanced Content',
          content: 'Content with agent insights'
        },
        consultationSummary: {
          totalConsultations: 2,
          consultedAgents: ['seo-keyword', 'market-research'],
          averageConfidence: 0.85,
          keyInsights: [
            { area: 'seo', suggestion: 'Use primary keyword in title', priority: 'high' },
            { area: 'market', suggestion: 'Target eco-conscious consumers', priority: 'high' }
          ]
        },
        agentInsights: {
          'seo-keyword': {
            confidence: 0.9,
            keyRecommendations: ['Optimize for "sustainable fashion" keyword'],
            processingTime: 1500
          },
          'market-research': {
            confidence: 0.8,
            keyRecommendations: ['Focus on millennial demographics'],
            processingTime: 2000
          }
        }
      });

      const stepResult = await workflowEngine.executeStep('test-execution-123', 'enhanced-content-creation');

      expect(stepResult.outputs.consultationSummary).toBeDefined();
      expect(stepResult.outputs.consultationSummary.totalConsultations).toBe(2);
      expect(stepResult.outputs.consultationSummary.consultedAgents).toContain('seo-keyword');
      expect(stepResult.outputs.consultationSummary.consultedAgents).toContain('market-research');
      
      expect(stepResult.outputs.agentInsights).toBeDefined();
      expect(stepResult.outputs.agentInsights['seo-keyword']).toBeDefined();
      expect(stepResult.outputs.agentInsights['market-research']).toBeDefined();
    });

    it('should include consultation metadata in workflow execution state', async () => {
      const enhancedAIGenerationSpy = jest.spyOn(workflowEngine as any, 'executeEnhancedAIGeneration');
      enhancedAIGenerationSpy.mockResolvedValue({
        content: { title: 'Test Content', content: 'Test content' },
        consultationSummary: { totalConsultations: 1 }
      });

      await workflowEngine.executeStep('test-execution-123', 'enhanced-content-creation');

      // Verify that execution state was updated with consultation metadata
      expect(mockStateStore.setExecution).toHaveBeenCalledWith(
        expect.objectContaining({
          stepResults: expect.objectContaining({
            'enhanced-content-creation': expect.objectContaining({
              outputs: expect.objectContaining({
                consultationSummary: expect.objectContaining({
                  totalConsultations: 1
                })
              })
            })
          })
        })
      );
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should continue workflow execution when consultation fails with continue fallback', async () => {
      const enhancedAIGenerationSpy = jest.spyOn(workflowEngine as any, 'executeEnhancedAIGeneration');
      enhancedAIGenerationSpy.mockResolvedValue({
        content: {
          title: 'Content without consultation',
          content: 'Generated without agent consultation due to failure'
        },
        consultationSummary: {
          totalConsultations: 0,
          consultedAgents: [],
          errors: ['Agent consultation timeout']
        }
      });

      const stepResult = await workflowEngine.executeStep('test-execution-123', 'enhanced-content-creation');

      expect(stepResult.status).toBe(StepStatus.COMPLETED);
      expect(stepResult.outputs.content).toBeDefined();
      expect(stepResult.outputs.consultationSummary.totalConsultations).toBe(0);
    });

    it('should fail workflow execution when consultation fails with fail fallback', async () => {
      // Modify consultation config to use fail fallback
      const stepWithFailFallback = {
        ...mockWorkflow.steps[1],
        consultationConfig: {
          ...mockWorkflow.steps[1].consultationConfig!,
          fallbackBehavior: 'fail' as const
        }
      };

      const workflowWithFailFallback = {
        ...mockWorkflow,
        steps: [mockWorkflow.steps[0], stepWithFailFallback]
      };

      (mockStateStore.getWorkflow as jest.Mock).mockResolvedValue(workflowWithFailFallback);

      const enhancedAIGenerationSpy = jest.spyOn(workflowEngine as any, 'executeEnhancedAIGeneration');
      enhancedAIGenerationSpy.mockRejectedValue(new Error('Agent consultation failed'));

      await expect(
        workflowEngine.executeStep('test-execution-123', 'enhanced-content-creation')
      ).rejects.toThrow('Agent consultation failed');
    });
  });

  describe('Performance and Monitoring', () => {
    it('should track consultation performance metrics', async () => {
      const enhancedAIGenerationSpy = jest.spyOn(workflowEngine as any, 'executeEnhancedAIGeneration');
      enhancedAIGenerationSpy.mockResolvedValue({
        content: { title: 'Test', content: 'Test' },
        consultationSummary: {
          totalConsultations: 2,
          averageConfidence: 0.85,
          totalProcessingTime: 3500,
          consultedAgents: ['seo-keyword', 'market-research']
        }
      });

      const stepResult = await workflowEngine.executeStep('test-execution-123', 'enhanced-content-creation');

      expect(stepResult.outputs.consultationSummary.totalProcessingTime).toBeDefined();
      expect(stepResult.outputs.consultationSummary.averageConfidence).toBeGreaterThan(0);
    });

    it('should provide consultation metrics for workflow monitoring', async () => {
      // This would be implemented as part of the workflow engine's monitoring capabilities
      const metrics = await workflowEngine.getExecutionMetrics('test-execution-123');
      
      expect(metrics).toBeDefined();
      // Metrics should include consultation-related data when available
    });
  });
});
