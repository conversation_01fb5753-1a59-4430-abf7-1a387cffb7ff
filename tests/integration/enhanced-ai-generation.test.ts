/**
 * Integration Tests for Enhanced AI Generation Step
 * 
 * Tests the integration between AI generation and dynamic agent consultation
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { EnhancedAIGenerationStep } from '../../src/core/workflow/enhanced-ai-generation-step';
import { WorkflowStep, StepType, AgentConsultationConfig } from '../../src/core/workflow/types';

describe('Enhanced AI Generation Step Integration', () => {
  let enhancedStep: EnhancedAIGenerationStep;

  beforeEach(() => {
    enhancedStep = new EnhancedAIGenerationStep();
  });

  afterEach(() => {
    // Clean up any resources if needed
  });

  describe('Basic AI Generation', () => {
    it('should execute AI generation without consultation', async () => {
      const step: WorkflowStep = {
        id: 'content-creation',
        name: 'Content Creation',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            prompt: 'Write a blog post about {{topic}}',
            model: 'gpt-4',
            temperature: 0.7
          }
        },
        inputs: ['topic'],
        outputs: ['content'],
        dependencies: []
      };

      const inputs = {
        topic: 'sustainable fashion trends',
        targetAudience: 'eco-conscious consumers'
      };

      const result = await enhancedStep.executeAIGenerationWithConsultation(
        step,
        inputs,
        'test-execution-123'
      );

      expect(result.outputs).toBeDefined();
      expect(result.outputs.content).toBeDefined();
      expect(result.outputs.content.title).toContain('sustainable fashion trends');
      expect(result.consultationResults).toEqual([]);
    });

    it('should execute AI generation with agent consultation enabled', async () => {
      const consultationConfig: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'always',
            agents: ['seo-keyword', 'market-research'],
            priority: 'high'
          }
        ],
        maxConsultations: 2,
        timeoutMs: 10000,
        fallbackBehavior: 'continue'
      };

      const step: WorkflowStep = {
        id: 'content-creation',
        name: 'Content Creation',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            prompt: 'Write a comprehensive blog post about {{topic}} for {{targetAudience}}',
            model: 'gpt-4',
            temperature: 0.7
          }
        },
        inputs: ['topic', 'targetAudience'],
        outputs: ['content'],
        dependencies: [],
        consultationConfig
      };

      const inputs = {
        topic: 'sustainable fashion trends',
        targetAudience: 'eco-conscious consumers',
        contentType: 'blog-post'
      };

      const result = await enhancedStep.executeAIGenerationWithConsultation(
        step,
        inputs,
        'test-execution-123'
      );

      expect(result.outputs).toBeDefined();
      expect(result.outputs.content).toBeDefined();
      expect(result.consultationResults).toBeDefined();
      expect(result.consultationResults!.length).toBeGreaterThan(0);
      expect(result.enhancedContent).toBeDefined();
      
      // Check that consultation summary is included
      expect(result.outputs.consultationSummary).toBeDefined();
      expect(result.outputs.agentInsights).toBeDefined();
    });
  });

  describe('Agent Consultation Integration', () => {
    it('should enhance content with SEO keyword agent consultation', async () => {
      const consultationConfig: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'always',
            agents: ['seo-keyword'],
            priority: 'high'
          }
        ],
        maxConsultations: 1,
        timeoutMs: 10000,
        fallbackBehavior: 'continue'
      };

      const step: WorkflowStep = {
        id: 'seo-content-creation',
        name: 'SEO Content Creation',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            prompt: 'Write an SEO-optimized blog post about {{topic}}',
            model: 'gpt-4'
          }
        },
        inputs: ['topic', 'primaryKeyword'],
        outputs: ['content'],
        dependencies: [],
        consultationConfig
      };

      const inputs = {
        topic: 'sustainable fashion trends',
        primaryKeyword: 'sustainable fashion',
        targetAudience: 'eco-conscious consumers'
      };

      const result = await enhancedStep.executeAIGenerationWithConsultation(
        step,
        inputs,
        'test-execution-123'
      );

      expect(result.consultationResults).toHaveLength(1);
      expect(result.consultationResults![0].agentId).toBe('seo-keyword');
      expect(result.enhancedContent?.agentRecommendations?.seo).toBeDefined();
      expect(result.outputs.content.metadata.seoOptimized).toBe(true);
    });

    it('should enhance content with market research agent consultation', async () => {
      const consultationConfig: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'always',
            agents: ['market-research'],
            priority: 'high'
          }
        ],
        maxConsultations: 1,
        timeoutMs: 10000,
        fallbackBehavior: 'continue'
      };

      const step: WorkflowStep = {
        id: 'market-content-creation',
        name: 'Market-Informed Content Creation',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            prompt: 'Write a market-informed blog post about {{topic}}',
            model: 'gpt-4'
          }
        },
        inputs: ['topic', 'industry'],
        outputs: ['content'],
        dependencies: [],
        consultationConfig
      };

      const inputs = {
        topic: 'sustainable fashion trends',
        industry: 'fashion',
        targetAudience: 'eco-conscious consumers'
      };

      const result = await enhancedStep.executeAIGenerationWithConsultation(
        step,
        inputs,
        'test-execution-123'
      );

      expect(result.consultationResults).toHaveLength(1);
      expect(result.consultationResults![0].agentId).toBe('market-research');
      expect(result.enhancedContent?.agentRecommendations?.market).toBeDefined();
      expect(result.outputs.content.metadata.marketResearched).toBe(true);
    });

    it('should enhance content with content strategy agent consultation', async () => {
      const consultationConfig: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'content_complexity',
            condition: { threshold: 0.5 },
            agents: ['content-strategy'],
            priority: 'medium'
          }
        ],
        maxConsultations: 1,
        timeoutMs: 10000,
        fallbackBehavior: 'continue'
      };

      const step: WorkflowStep = {
        id: 'strategic-content-creation',
        name: 'Strategic Content Creation',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            prompt: 'Write a strategically planned blog post about {{topic}}',
            model: 'gpt-4'
          }
        },
        inputs: ['topic', 'goals'],
        outputs: ['content'],
        dependencies: [],
        consultationConfig
      };

      const inputs = {
        topic: 'advanced machine learning algorithms',
        goals: ['educate', 'establish expertise', 'generate leads'],
        targetAudience: 'technical experts',
        complexity: 0.8
      };

      const result = await enhancedStep.executeAIGenerationWithConsultation(
        step,
        inputs,
        'test-execution-123'
      );

      expect(result.consultationResults).toHaveLength(1);
      expect(result.consultationResults![0].agentId).toBe('content-strategy');
      expect(result.enhancedContent?.agentRecommendations?.strategy).toBeDefined();
      expect(result.outputs.content.metadata.strategicallyPlanned).toBe(true);
    });
  });

  describe('Multi-Agent Consultation', () => {
    it('should coordinate multiple agents for comprehensive content enhancement', async () => {
      const consultationConfig: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'always',
            agents: ['seo-keyword', 'market-research', 'content-strategy'],
            priority: 'high'
          }
        ],
        maxConsultations: 3,
        timeoutMs: 15000,
        fallbackBehavior: 'continue'
      };

      const step: WorkflowStep = {
        id: 'comprehensive-content-creation',
        name: 'Comprehensive Content Creation',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            prompt: 'Write a comprehensive, well-researched blog post about {{topic}}',
            model: 'gpt-4'
          }
        },
        inputs: ['topic', 'targetAudience', 'goals'],
        outputs: ['content'],
        dependencies: [],
        consultationConfig
      };

      const inputs = {
        topic: 'sustainable fashion trends',
        targetAudience: 'eco-conscious consumers',
        goals: ['educate', 'inspire action', 'build brand awareness'],
        contentType: 'blog-post',
        industry: 'fashion'
      };

      const result = await enhancedStep.executeAIGenerationWithConsultation(
        step,
        inputs,
        'test-execution-123'
      );

      expect(result.consultationResults).toHaveLength(3);
      
      const agentIds = result.consultationResults!.map(r => r.agentId);
      expect(agentIds).toContain('seo-keyword');
      expect(agentIds).toContain('market-research');
      expect(agentIds).toContain('content-strategy');

      expect(result.enhancedContent?.agentRecommendations?.seo).toBeDefined();
      expect(result.enhancedContent?.agentRecommendations?.market).toBeDefined();
      expect(result.enhancedContent?.agentRecommendations?.strategy).toBeDefined();

      expect(result.outputs.consultationSummary.totalConsultations).toBe(3);
      expect(result.outputs.consultationSummary.consultedAgents).toHaveLength(3);
    });

    it('should handle partial consultation failures gracefully', async () => {
      const consultationConfig: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'always',
            agents: ['seo-keyword', 'market-research'],
            priority: 'high'
          }
        ],
        maxConsultations: 2,
        timeoutMs: 50, // Very short timeout to simulate failures
        fallbackBehavior: 'continue'
      };

      const step: WorkflowStep = {
        id: 'resilient-content-creation',
        name: 'Resilient Content Creation',
        type: StepType.AI_GENERATION,
        config: {
          aiConfig: {
            prompt: 'Write a blog post about {{topic}}',
            model: 'gpt-4'
          }
        },
        inputs: ['topic'],
        outputs: ['content'],
        dependencies: [],
        consultationConfig
      };

      const inputs = {
        topic: 'sustainable fashion trends'
      };

      const result = await enhancedStep.executeAIGenerationWithConsultation(
        step,
        inputs,
        'test-execution-123'
      );

      // Should complete successfully even if some consultations fail
      expect(result.outputs).toBeDefined();
      expect(result.outputs.content).toBeDefined();
      // Consultation results may be empty due to timeouts, but that's expected
      expect(Array.isArray(result.consultationResults)).toBe(true);
    });
  });

  describe('System Health and Monitoring', () => {
    it('should provide consultation metrics', async () => {
      const metrics = enhancedStep.getConsultationMetrics();
      
      expect(metrics).toBeDefined();
      expect(metrics.totalConsultations).toBeGreaterThanOrEqual(0);
      expect(metrics.successRate).toBeGreaterThanOrEqual(0);
      expect(metrics.agentUtilization).toBeDefined();
    });

    it('should provide agent status information', async () => {
      const agentStatus = await enhancedStep.getAgentStatus();
      
      expect(agentStatus).toBeDefined();
      expect(Array.isArray(agentStatus)).toBe(true);
      expect(agentStatus.length).toBe(3); // seo-keyword, market-research, content-strategy
      
      agentStatus.forEach(status => {
        expect(status.agentId).toBeDefined();
        expect(status.isRegistered).toBe(true);
        expect(status.capabilities).toBeDefined();
        expect(Array.isArray(status.capabilities)).toBe(true);
      });
    });

    it('should perform health check on agent system', async () => {
      const healthCheck = await enhancedStep.performHealthCheck();
      
      expect(healthCheck).toBeDefined();
      expect(healthCheck.overallHealth).toMatch(/healthy|degraded|unhealthy/);
      expect(healthCheck.agentHealth).toBeDefined();
      expect(Array.isArray(healthCheck.issues)).toBe(true);
      
      // All agents should be healthy in test environment
      expect(healthCheck.overallHealth).toBe('healthy');
    });
  });
});
