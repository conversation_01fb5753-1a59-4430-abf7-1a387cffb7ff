/**
 * Integration Tests for Fresh Dynamic Agent Consultation System
 * 
 * Tests the new dynamic agent consultation system that integrates with workflows
 * without modifying the existing goal-based-collaboration codebase
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Import the fresh agent consultation system (to be implemented)
import { DynamicAgentConsultationService } from '../../src/core/workflow/dynamic-agent-consultation-service';
import { WorkflowAgentBridge } from '../../src/core/workflow/workflow-agent-bridge';
import { SeoKeywordAgent } from '../../src/core/agents/seo-keyword-agent';
import { MarketResearchAgent } from '../../src/core/agents/market-research-agent';
import { ContentStrategyAgent } from '../../src/core/agents/content-strategy-agent';
import { AgentConsultationConfig, ConsultationTrigger, ConsultationResult } from '../../src/core/workflow/types';

describe('Fresh Dynamic Agent Consultation System', () => {
  let consultationService: DynamicAgentConsultationService;
  let agentBridge: WorkflowAgentBridge;
  let seoAgent: SeoKeywordAgent;
  let marketAgent: MarketResearchAgent;
  let strategyAgent: ContentStrategyAgent;

  beforeEach(() => {
    // Initialize fresh agent system
    seoAgent = new SeoKeywordAgent();
    marketAgent = new MarketResearchAgent();
    strategyAgent = new ContentStrategyAgent();
    
    agentBridge = new WorkflowAgentBridge({
      'seo-keyword': seoAgent,
      'market-research': marketAgent,
      'content-strategy': strategyAgent
    });
    
    consultationService = new DynamicAgentConsultationService(agentBridge);
  });

  afterEach(() => {
    consultationService.clearMetrics();
    jest.clearAllMocks();
  });

  describe('Agent Initialization', () => {
    it('should initialize all specialized agents correctly', () => {
      expect(seoAgent).toBeDefined();
      expect(seoAgent.getAgentId()).toBe('seo-keyword');
      expect(seoAgent.getCapabilities()).toContain('keyword-research');
      
      expect(marketAgent).toBeDefined();
      expect(marketAgent.getAgentId()).toBe('market-research');
      expect(marketAgent.getCapabilities()).toContain('market-analysis');
      
      expect(strategyAgent).toBeDefined();
      expect(strategyAgent.getAgentId()).toBe('content-strategy');
      expect(strategyAgent.getCapabilities()).toContain('content-planning');
    });

    it('should register agents with the bridge correctly', () => {
      const registeredAgents = agentBridge.getRegisteredAgents();
      expect(registeredAgents).toHaveLength(3);
      expect(registeredAgents).toContain('seo-keyword');
      expect(registeredAgents).toContain('market-research');
      expect(registeredAgents).toContain('content-strategy');
    });
  });

  describe('Dynamic Agent Selection', () => {
    it('should select appropriate agents based on content type', async () => {
      const context = {
        topic: 'sustainable fashion trends',
        contentType: 'blog-post',
        targetAudience: 'eco-conscious consumers'
      };

      const selectedAgents = await consultationService.selectAgentsForContext(context);
      
      expect(selectedAgents).toContain('seo-keyword');
      expect(selectedAgents).toContain('market-research');
      expect(selectedAgents.length).toBeGreaterThan(0);
    });

    it('should select agents based on feedback keywords', async () => {
      const context = {
        topic: 'test topic',
        feedback: 'This content needs better SEO optimization and keyword targeting'
      };

      const selectedAgents = await consultationService.selectAgentsForContext(context);
      
      expect(selectedAgents).toContain('seo-keyword');
    });

    it('should select strategy agent for complex content', async () => {
      const context = {
        topic: 'advanced machine learning algorithms',
        content: 'This is a very long technical article about complex algorithms...',
        targetAudience: 'technical experts',
        complexity: 0.8
      };

      const selectedAgents = await consultationService.selectAgentsForContext(context);
      
      expect(selectedAgents).toContain('content-strategy');
    });
  });

  describe('Consultation Triggers', () => {
    it('should trigger consultation based on always trigger', async () => {
      const config: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'always',
            agents: ['seo-keyword', 'market-research'],
            priority: 'high'
          }
        ],
        maxConsultations: 2,
        timeoutMs: 5000,
        fallbackBehavior: 'continue'
      };

      const context = {
        topic: 'sustainable fashion trends',
        contentType: 'blog-post'
      };

      const shouldTrigger = await consultationService.shouldTriggerConsultation(config, context);
      expect(shouldTrigger).toBe(true);
    });

    it('should trigger consultation based on quality threshold', async () => {
      const config: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'quality_threshold',
            condition: { threshold: 0.8 },
            agents: ['content-strategy'],
            priority: 'medium'
          }
        ],
        maxConsultations: 1,
        timeoutMs: 5000,
        fallbackBehavior: 'continue'
      };

      // Low quality should trigger
      const lowQualityContext = { topic: 'test', qualityScore: 0.6 };
      const shouldTriggerLow = await consultationService.shouldTriggerConsultation(config, lowQualityContext);
      expect(shouldTriggerLow).toBe(true);

      // High quality should not trigger
      const highQualityContext = { topic: 'test', qualityScore: 0.9 };
      const shouldTriggerHigh = await consultationService.shouldTriggerConsultation(config, highQualityContext);
      expect(shouldTriggerHigh).toBe(false);
    });

    it('should trigger consultation based on feedback keywords', async () => {
      const config: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'feedback_keywords',
            condition: { keywords: ['seo', 'keywords', 'optimization'] },
            agents: ['seo-keyword'],
            priority: 'high'
          }
        ],
        maxConsultations: 1,
        timeoutMs: 5000,
        fallbackBehavior: 'continue'
      };

      // Matching keywords should trigger
      const feedbackContext = {
        topic: 'test',
        feedback: 'This content needs better SEO optimization'
      };
      const shouldTrigger = await consultationService.shouldTriggerConsultation(config, feedbackContext);
      expect(shouldTrigger).toBe(true);

      // Non-matching keywords should not trigger
      const noFeedbackContext = {
        topic: 'test',
        feedback: 'This content is great, just needs formatting'
      };
      const shouldNotTrigger = await consultationService.shouldTriggerConsultation(config, noFeedbackContext);
      expect(shouldNotTrigger).toBe(false);
    });
  });

  describe('Agent Consultation Execution', () => {
    it('should execute consultation with SEO keyword agent', async () => {
      const context = {
        topic: 'sustainable fashion trends',
        targetAudience: 'eco-conscious consumers',
        primaryKeyword: 'sustainable fashion'
      };

      const result = await consultationService.consultAgent(
        'seo-keyword',
        'workflow-123',
        'keyword-research',
        context
      );

      expect(result).toBeDefined();
      expect(result.agentId).toBe('seo-keyword');
      expect(result.response).toBeDefined();
      expect(result.confidence).toBeGreaterThan(0);
      expect(result.suggestions).toBeDefined();
      expect(Array.isArray(result.suggestions)).toBe(true);
    });

    it('should execute consultation with market research agent', async () => {
      const context = {
        topic: 'sustainable fashion trends',
        targetAudience: 'eco-conscious consumers',
        industry: 'fashion'
      };

      const result = await consultationService.consultAgent(
        'market-research',
        'workflow-123',
        'market-analysis',
        context
      );

      expect(result).toBeDefined();
      expect(result.agentId).toBe('market-research');
      expect(result.response).toBeDefined();
      expect(result.confidence).toBeGreaterThan(0);
      expect(result.suggestions).toBeDefined();
    });

    it('should execute consultation with content strategy agent', async () => {
      const context = {
        topic: 'sustainable fashion trends',
        targetAudience: 'eco-conscious consumers',
        contentType: 'blog-post',
        goals: ['increase awareness', 'drive engagement']
      };

      const result = await consultationService.consultAgent(
        'content-strategy',
        'workflow-123',
        'content-planning',
        context
      );

      expect(result).toBeDefined();
      expect(result.agentId).toBe('content-strategy');
      expect(result.response).toBeDefined();
      expect(result.confidence).toBeGreaterThan(0);
      expect(result.suggestions).toBeDefined();
    });
  });

  describe('Multi-Agent Consultation', () => {
    it('should execute parallel consultations with multiple agents', async () => {
      const config: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'always',
            agents: ['seo-keyword', 'market-research', 'content-strategy'],
            priority: 'high'
          }
        ],
        maxConsultations: 3,
        timeoutMs: 10000,
        fallbackBehavior: 'continue'
      };

      const context = {
        topic: 'sustainable fashion trends',
        contentType: 'blog-post',
        targetAudience: 'eco-conscious consumers'
      };

      const results = await consultationService.consultMultipleAgents(
        'workflow-123',
        'content-creation',
        context,
        config
      );

      expect(results).toHaveLength(3);
      expect(results.map(r => r.agentId)).toContain('seo-keyword');
      expect(results.map(r => r.agentId)).toContain('market-research');
      expect(results.map(r => r.agentId)).toContain('content-strategy');
    });

    it('should handle consultation timeouts gracefully', async () => {
      const config: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'always',
            agents: ['seo-keyword'],
            priority: 'high'
          }
        ],
        maxConsultations: 1,
        timeoutMs: 100, // Very short timeout
        fallbackBehavior: 'continue'
      };

      const context = { topic: 'test topic' };

      const results = await consultationService.consultMultipleAgents(
        'workflow-123',
        'test-step',
        context,
        config
      );

      // Should handle timeout gracefully
      expect(Array.isArray(results)).toBe(true);
    });
  });

  describe('Consultation Metrics and Monitoring', () => {
    it('should track consultation metrics correctly', async () => {
      const context = { topic: 'test topic' };

      await consultationService.consultAgent(
        'seo-keyword',
        'workflow-123',
        'test-step',
        context
      );

      const metrics = consultationService.getMetrics();
      expect(metrics.totalConsultations).toBe(1);
      expect(metrics.successfulConsultations).toBe(1);
      expect(metrics.averageResponseTime).toBeGreaterThan(0);
      expect(metrics.agentUtilization['seo-keyword']).toBe(1);
    });

    it('should track failed consultations', async () => {
      // Mock agent failure
      jest.spyOn(seoAgent, 'processConsultation').mockRejectedValue(new Error('Agent error'));

      const context = { topic: 'test topic' };

      try {
        await consultationService.consultAgent(
          'seo-keyword',
          'workflow-123',
          'test-step',
          context
        );
      } catch (error) {
        // Expected to fail
      }

      const metrics = consultationService.getMetrics();
      expect(metrics.totalConsultations).toBe(1);
      expect(metrics.failedConsultations).toBe(1);
      expect(metrics.successRate).toBe(0);
    });
  });

  describe('Error Handling and Fallbacks', () => {
    it('should handle agent unavailability with continue fallback', async () => {
      const config: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'always',
            agents: ['non-existent-agent'],
            priority: 'high'
          }
        ],
        maxConsultations: 1,
        timeoutMs: 5000,
        fallbackBehavior: 'continue'
      };

      const context = { topic: 'test topic' };

      const results = await consultationService.consultMultipleAgents(
        'workflow-123',
        'test-step',
        context,
        config
      );

      expect(results).toEqual([]);
    });

    it('should throw error with fail fallback behavior', async () => {
      const config: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'always',
            agents: ['non-existent-agent'],
            priority: 'high'
          }
        ],
        maxConsultations: 1,
        timeoutMs: 5000,
        fallbackBehavior: 'fail'
      };

      const context = { topic: 'test topic' };

      await expect(
        consultationService.consultMultipleAgents(
          'workflow-123',
          'test-step',
          context,
          config
        )
      ).rejects.toThrow();
    });
  });
});
