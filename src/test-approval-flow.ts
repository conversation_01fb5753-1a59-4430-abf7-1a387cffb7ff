/**
 * Test Script for Approval Flow
 * Verifies that the approval system works correctly
 */

import { userManager } from './utils/user-manager';

async function testApprovalFlow() {
  console.log('🧪 Testing Enhanced Workflow Approval Flow...\n');

  // Test 1: User Manager
  console.log('1. Testing User Manager:');
  const currentUser = userManager.getCurrentUser();
  console.log('   Current User:', currentUser);
  console.log('   User ID:', userManager.getCurrentUserId());
  console.log('   Can Approve:', userManager.canApprove());
  console.log('   Default Approvers:', userManager.getDefaultApprovers());
  console.log('   ✅ User Manager working\n');

  // Test 2: Template with Approval Gates
  console.log('2. Testing Template with Approval Gates:');
  try {
    const response = await fetch('/api/workflow/create');
    const result = await response.json();
    
    if (result.success) {
      const approvalTemplate = result.data.templates.find((t: any) => 
        t.id === 'blog-post-seo-approval'
      );
      
      if (approvalTemplate) {
        console.log('   ✅ Found approval template:', approvalTemplate.name);
        console.log('   Steps with approval gates:', 
          approvalTemplate.workflow.steps
            .filter((s: any) => s.type === 'approval_gate')
            .map((s: any) => s.name)
        );
      } else {
        console.log('   ❌ Approval template not found');
      }
    } else {
      console.log('   ❌ Failed to load templates:', result.error);
    }
  } catch (error) {
    console.log('   ❌ Error loading templates:', error);
  }
  console.log('');

  // Test 3: Create Test Workflow
  console.log('3. Testing Workflow Creation:');
  try {
    const createResponse = await fetch('/api/workflow/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        templateId: 'blog-post-seo-approval',
        inputs: {
          topic: 'Test Approval Flow',
          target_audience: 'Developers',
          primary_keyword: 'approval testing'
        }
      })
    });

    const createResult = await createResponse.json();
    
    if (createResult.success) {
      console.log('   ✅ Workflow created successfully');
      console.log('   Execution ID:', createResult.data.executionId);
      
      // Wait a moment for execution to start
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Check execution status
      const statusResponse = await fetch(`/api/workflow/create?executionId=${createResult.data.executionId}`);
      const statusResult = await statusResponse.json();
      
      if (statusResult.success) {
        console.log('   Execution Status:', statusResult.data.execution.status);
        console.log('   Steps:', statusResult.data.steps.length);
        
        // Look for approval gates
        const approvalSteps = statusResult.data.steps.filter((s: any) => 
          s.status === 'waiting_approval' || s.approvalRequired
        );
        
        if (approvalSteps.length > 0) {
          console.log('   ✅ Found approval steps:', approvalSteps.length);
          approvalSteps.forEach((step: any) => {
            console.log(`     - ${step.stepId}: ${step.status} (Artifact: ${step.artifactId || 'N/A'})`);
          });
        } else {
          console.log('   ⚠️  No approval steps found yet (may still be processing)');
        }
      } else {
        console.log('   ❌ Failed to get execution status:', statusResult.error);
      }
    } else {
      console.log('   ❌ Failed to create workflow:', createResult.error);
    }
  } catch (error) {
    console.log('   ❌ Error creating workflow:', error);
  }
  console.log('');

  console.log('🎯 Test Summary:');
  console.log('   - User management: ✅ Working');
  console.log('   - Template loading: Check console above');
  console.log('   - Workflow creation: Check console above');
  console.log('   - Approval flow: Check console above');
  console.log('\n📋 Next Steps:');
  console.log('   1. Open /workflow/enhanced in browser');
  console.log('   2. Create "SEO Blog Post with Approval Gates" workflow');
  console.log('   3. Switch to Visual Workflow tab');
  console.log('   4. Wait for approval gates to appear');
  console.log('   5. Click approval buttons to test flow');
}

// Export for use in browser console or Node.js
if (typeof window !== 'undefined') {
  // Browser environment
  (window as any).testApprovalFlow = testApprovalFlow;
  console.log('🔧 Test function available as window.testApprovalFlow()');
} else {
  // Node.js environment
  testApprovalFlow().catch(console.error);
}

export { testApprovalFlow };
