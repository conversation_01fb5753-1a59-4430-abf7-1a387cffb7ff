/**
 * Goal Orchestrator Client
 *
 * A client-side utility for interacting with the goal orchestrator API
 */

import { v4 as uuidv4 } from 'uuid';
import {
  WorkflowPhase,
  MessageType,
  GoalType,
  GoalStatus,
  ArtifactStatus,
  SessionStatus
} from '../app/(payload)/api/agents/dynamic-collaboration-v3';

/**
 * Content Generation Parameters
 */
export interface ContentGenerationParams {
  topic: string;
  contentType?: 'blog-article' | 'product-page' | 'buying-guide';
  targetAudience?: string;
  tone?: string;
  keywords?: string[];
  additionalInstructions?: string;
  referenceUrls?: string[];
}

/**
 * Goal Orchestrator Client
 */
export class GoalOrchestratorClient {
  private apiEndpoint: string;
  private jsonrpcEndpoint: string;

  constructor(
    apiEndpoint = '/api/agents/dynamic-collaboration-v3/goal-based',
    jsonrpcEndpoint = '/api/agents/dynamic-collaboration-v3/jsonrpc'
  ) {
    this.apiEndpoint = apiEndpoint;
    this.jsonrpcEndpoint = jsonrpcEndpoint;
  }

  /**
   * Initialize a new collaboration session
   */
  async initiate(params: ContentGenerationParams): Promise<{ sessionId: string }> {
    const response = await fetch(this.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.error) {
      throw new Error(result.error);
    }

    return {
      sessionId: result.sessionId
    };
  }

  /**
   * Get the current state of a session
   */
  async getState(sessionId: string): Promise<any> {
    try {
      const response = await fetch(`${this.apiEndpoint}?sessionId=${sessionId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.error(`HTTP error ${response.status}: ${response.statusText}`);
        // Return null instead of throwing an error
        return null;
      }

      const result = await response.json();

      if (result.error) {
        console.error(`API error: ${result.error}`);
        // Return null instead of throwing an error
        return null;
      }

      // If the response is in the format { state: ... }
      if (result.state) {
        return result.state;
      }

      // If the response is the state object directly
      return result;
    } catch (error) {
      console.error('Error fetching state:', error);
      // Return null instead of throwing an error
      return null;
    }
  }

  /**
   * Send a JSONRPC request
   */
  private async sendJsonRpcRequest(method: string, params: any): Promise<any> {
    try {
      const request = {
        jsonrpc: '2.0',
        method,
        params,
        id: uuidv4()
      };

      console.log(`Sending JSON-RPC request: ${method}`, params);

      const response = await fetch(this.jsonrpcEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        console.error(`HTTP error ${response.status}: ${response.statusText}`);
        throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log(`JSON-RPC response for ${method}:`, result);

      if (result.error) {
        console.error(`JSONRPC error for ${method}:`, result.error);
        throw new Error(`JSONRPC error: ${result.error.message || JSON.stringify(result.error)}`);
      }

      return result.result;
    } catch (error) {
      console.error(`Error in JSON-RPC request for ${method}:`, error);
      throw error;
    }
  }

  /**
   * Get goals for a session
   */
  async getGoals(sessionId: string): Promise<any> {
    return this.sendJsonRpcRequest('goal.list', { sessionId });
  }

  /**
   * Get artifacts for a session
   */
  async getArtifacts(sessionId: string, type?: string): Promise<any> {
    return this.sendJsonRpcRequest('artifact.list', { sessionId, type });
  }

  /**
   * Get workflow progress
   */
  async getWorkflowProgress(sessionId: string): Promise<any> {
    return this.sendJsonRpcRequest('workflow.getProgress', { sessionId });
  }

  /**
   * Send user feedback
   */
  async sendFeedback(sessionId: string, artifactId: string, feedback: any): Promise<any> {
    return this.sendJsonRpcRequest('feedback.provide', {
      sessionId,
      artifactId,
      fromAgent: 'user',
      toAgent: 'system',
      feedback
    });
  }

  /**
   * Progress the session
   */
  async progressSession(sessionId: string, steps: number = 1): Promise<any> {
    // Use the correct progress endpoint
    const progressEndpoint = '/api/agents/dynamic-collaboration-v3/progress';

    const response = await fetch(progressEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sessionId,
        steps
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.error) {
      throw new Error(result.error);
    }

    return result;
  }

  /**
   * Send a message to the session
   */
  async sendMessage(sessionId: string, content: string): Promise<any> {
    // Use the correct message endpoint
    const messageEndpoint = '/api/agents/dynamic-collaboration-v3/message';

    const response = await fetch(messageEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sessionId,
        content
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.error) {
      throw new Error(result.error);
    }

    return result;
  }

  /**
   * Save edited article
   */
  async saveArticle(sessionId: string, content: string): Promise<any> {
    return this.sendJsonRpcRequest('artifact.update', {
      sessionId,
      type: 'final-article',
      content
    });
  }

  /**
   * Get artifact evaluation
   */
  async getArtifactEvaluation(sessionId: string, artifactId: string): Promise<any> {
    try {
      console.log(`Fetching evaluation for artifact ${artifactId} in session ${sessionId}`);

      const result = await this.sendJsonRpcRequest('artifact.getEvaluation', {
        sessionId,
        artifactId
      });

      console.log('Evaluation result:', result);
      return result?.evaluation;
    } catch (error) {
      console.error('Error fetching artifact evaluation:', error);
      return null;
    }
  }

  /**
   * Request artifact evaluation
   */
  async requestArtifactEvaluation(sessionId: string, artifactId: string, goalId: string): Promise<any> {
    return this.sendJsonRpcRequest('artifact.requestEvaluation', {
      sessionId,
      artifactId,
      goalId
    });
  }

  /**
   * Fix goals for a session
   */
  async fixGoals(sessionId: string): Promise<any> {
    // Use the correct debug endpoint
    const debugEndpoint = '/api/agents/dynamic-collaboration-v3/debug';

    const response = await fetch(debugEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sessionId,
        action: 'fixGoals'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.error) {
      throw new Error(result.error);
    }

    return result;
  }



  /**
   * Process goals with fixed processor
   */
  async processGoalsFixed(sessionId: string): Promise<any> {
    // Use the correct debug endpoint
    const debugEndpoint = '/api/agents/dynamic-collaboration-v3/debug';

    const response = await fetch(debugEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sessionId,
        action: 'processGoalsFixed'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.error) {
      throw new Error(result.error);
    }

    return result;
  }

  /**
   * Force progress for a session
   */
  async forceProgress(sessionId: string): Promise<any> {
    // Use the correct debug endpoint
    const debugEndpoint = '/api/agents/dynamic-collaboration-v3/debug';

    const response = await fetch(debugEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sessionId,
        action: 'forceProgress'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.error) {
      throw new Error(result.error);
    }

    return result;
  }

  /**
   * Activate the next pending goal
   */
  async activateNextGoal(sessionId: string): Promise<any> {
    // Use the correct debug endpoint
    const debugEndpoint = '/api/agents/dynamic-collaboration-v3/debug';

    const response = await fetch(debugEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sessionId,
        action: 'activateNextGoal'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.error) {
      throw new Error(result.error);
    }

    return result;
  }

  /**
   * Create an artifact for a goal
   */
  async createArtifact(sessionId: string, goalId: string, type: string, title: string, content: any): Promise<any> {
    // Use the correct artifact endpoint
    const artifactEndpoint = '/api/agents/dynamic-collaboration-v3/artifact';

    const response = await fetch(artifactEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sessionId,
        goalId,
        type,
        title,
        content
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.error) {
      throw new Error(result.error);
    }

    return result;
  }

  /**
   * Get artifacts for a session
   */
  async getArtifacts(sessionId: string, goalId?: string): Promise<any> {
    // Use the correct artifact endpoint
    const artifactEndpoint = '/api/agents/dynamic-collaboration-v3/artifact';

    const url = new URL(artifactEndpoint);
    url.searchParams.append('sessionId', sessionId);
    if (goalId) {
      url.searchParams.append('goalId', goalId);
    }

    const response = await fetch(url.toString());

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.error) {
      throw new Error(result.error);
    }

    return result;
  }

  /**
   * Approve an artifact
   */
  async approveArtifact(sessionId: string, artifactId: string): Promise<any> {
    return this.sendJsonRpcRequest('artifact.approve', {
      sessionId,
      artifactId
    });
  }

}

// Export a singleton instance
export const goalOrchestratorClient = new GoalOrchestratorClient();
export default goalOrchestratorClient;
