/**
 * Dynamic Collaboration Client V2
 *
 * A client-side utility for interacting with the dynamic collaboration JSONRPC V2 API
 */

import { v4 as uuidv4 } from 'uuid';
import {
  DynamicWorkflowPhase,
  DynamicMessageType,
  DynamicAgentMessage,
  DynamicCollaborationState
} from '../app/(payload)/api/agents/dynamic-collaboration-v2/state';

// JSONRPC interface types
interface JsonRpcRequest {
  jsonrpc: string;
  method: string;
  params: any;
  id: string | number;
}

interface JsonRpcSuccessResponse {
  jsonrpc: string;
  result: any;
  id: string | number;
}

interface JsonRpcErrorResponse {
  jsonrpc: string;
  error: {
    code: number;
    message: string;
    data?: any;
  };
  id: string | number | null;
}

type JsonRpcResponse = JsonRpcSuccessResponse | JsonRpcErrorResponse;

/**
 * Content Generation Parameters
 */
export interface ContentGenerationParams {
  topic: string;
  contentType?: 'blog-article' | 'product-page' | 'buying-guide';
  targetAudience?: string;
  tone?: string;
  keywords?: string[];
  additionalInstructions?: string;
  referenceUrls?: string[];
}

/**
 * Dynamic Collaboration Client V2
 */
export class DynamicCollaborationClientV2 {
  private apiEndpoint: string;

  constructor(apiEndpoint = '/api/agents/dynamic-collaboration-v2/jsonrpc-v2') {
    this.apiEndpoint = apiEndpoint;
  }

  /**
   * Send a JSONRPC request to the API
   */
  private async sendRequest(method: string, params: any): Promise<any> {
    const request: JsonRpcRequest = {
      jsonrpc: '2.0',
      method,
      params,
      id: uuidv4()
    };

    const response = await fetch(this.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const jsonResponse = await response.json() as JsonRpcResponse;

    if ('error' in jsonResponse) {
      throw new Error(`JSONRPC error ${jsonResponse.error.code}: ${jsonResponse.error.message}`);
    }

    return jsonResponse.result;
  }

  /**
   * Initialize a new collaboration session
   */
  async initiate(params: ContentGenerationParams): Promise<{ sessionId: string, success: boolean }> {
    return this.sendRequest('initiate', params);
  }

  /**
   * Get the current state of a session
   */
  async getState(sessionId: string): Promise<{ state: DynamicCollaborationState }> {
    return this.sendRequest('getState', { sessionId });
  }

  /**
   * Transition to a new phase
   */
  async transitionPhase(sessionId: string, phase: DynamicWorkflowPhase): Promise<{ success: boolean }> {
    return this.sendRequest('transitionPhase', { sessionId, phase });
  }

  /**
   * Process a user message
   */
  async processUserMessage(sessionId: string, messageId: string): Promise<{ success: boolean }> {
    return this.sendRequest('processUserMessage', { sessionId, messageId });
  }

  /**
   * Add a user message and process it
   */
  async addUserMessage(sessionId: string, content: string): Promise<{ messageId: string, conversationId: string }> {
    return this.sendRequest('addUserMessage', { sessionId, content });
  }

  /**
   * Complete a goal by type (for testing purposes)
   */
  async completeGoal(sessionId: string, goalType: string): Promise<{ success: boolean }> {
    return this.sendRequest('completeGoal', { sessionId, goalType });
  }

  /**
   * Handle feedback and improve artifact
   */
  async handleFeedback(sessionId: string, messageId: string): Promise<{ success: boolean }> {
    return this.sendRequest('handleFeedback', { sessionId, messageId });
  }

  /**
   * Compatibility methods for the old API
   */

  /**
   * Create a new collaboration session (compatibility with old API)
   */
  async createSession(params: ContentGenerationParams): Promise<{ sessionId: string }> {
    const result = await this.initiate(params);
    return { sessionId: result.sessionId };
  }

  /**
   * Get a session by ID (compatibility with old API)
   */
  async getSession(sessionId: string): Promise<DynamicCollaborationState> {
    const result = await this.getState(sessionId);
    return result.state;
  }

  /**
   * Send a message (compatibility with old API)
   */
  async sendMessage(
    sessionId: string,
    content: any,
    type: DynamicMessageType = DynamicMessageType.USER_MESSAGE,
    to: string = 'system'
  ): Promise<{ messageId: string }> {
    const result = await this.addUserMessage(sessionId, typeof content === 'string' ? content : JSON.stringify(content));
    return { messageId: result.messageId };
  }

  /**
   * Transition to a new phase (compatibility with old API)
   */
  async transitionToPhase(sessionId: string, phase: DynamicWorkflowPhase): Promise<any> {
    return this.transitionPhase(sessionId, phase);
  }

  /**
   * Update a session (compatibility with old API)
   */
  async updateSession(sessionId: string, action: string, phase?: DynamicWorkflowPhase): Promise<any> {
    if (action === 'transition-phase' && phase) {
      return this.transitionPhase(sessionId, phase);
    }
    throw new Error(`Action ${action} not supported in V2 API`);
  }
}

// Export a singleton instance
export const dynamicCollaborationClientV2 = new DynamicCollaborationClientV2();
export default dynamicCollaborationClientV2;
