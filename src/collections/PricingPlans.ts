import { CollectionConfig } from 'payload'

export const PricingPlans: CollectionConfig = {
  slug: 'pricing-plans',
  admin: {
    useAsTitle: 'title',
    group: 'Product Content',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'product',
      type: 'relationship',
      relationTo: 'products',
      required: true,
    },
    {
      name: 'subtitle',
      type: 'text',
    },
    {
      name: 'price',
      type: 'text',
      required: true,
    },
    {
      name: 'billingFrequency',
      type: 'text',
      defaultValue: '/user/month',
    },
    {
      name: 'description',
      type: 'textarea',
    },
    {
      name: 'userLimit',
      type: 'text',
      admin: {
        description: 'E.g., "Up to 10 users" or "Unlimited"',
      }
    },
    {
      name: 'features',
      type: 'array',
      fields: [
        {
          name: 'feature',
          type: 'text',
        }
      ]
    },
    {
      name: 'popular',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'advantages',
      type: 'array',
      fields: [
        {
          name: 'advantage',
          type: 'text',
        }
      ]
    },
    {
      name: 'idealFor',
      type: 'textarea',
    },
    {
      name: 'ctaText',
      type: 'text',
      defaultValue: 'Start Free Trial',
    },
    {
      name: 'ctaLink',
      type: 'text',
    },
    {
      name: 'planType',
      type: 'select',
      defaultValue: 'monthly',
      options: [
        { label: 'Monthly', value: 'monthly' },
        { label: 'Annual', value: 'annual' },
        { label: 'Both', value: 'both' },
      ],
    },
    {
      name: 'annualDiscount',
      type: 'number',
      admin: {
        description: 'Percentage discount for annual billing (e.g., 17)',
        condition: (data, siblingData) => 
          siblingData?.planType === 'annual' || siblingData?.planType === 'both',
      }
    },
    {
      name: 'annualPrice',
      type: 'text',
      admin: {
        description: 'Price for annual billing (if different)',
        condition: (data, siblingData) => 
          siblingData?.planType === 'annual' || siblingData?.planType === 'both',
      }
    }
  ]
}