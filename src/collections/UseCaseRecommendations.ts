import { CollectionConfig } from 'payload'

export const UseCaseRecommendations: CollectionConfig = {
  slug: 'use-case-recommendations',
  admin: {
    useAsTitle: 'title',
    group: 'Comparison Content',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
    },
    {
      name: 'icon',
      type: 'select',
      options: [
        { label: 'Users', value: 'Users' },
        { label: 'Zap', value: 'Zap' },
        { label: 'Clock', value: 'Clock' },
        { label: 'Shield', value: 'Shield' },
        { label: 'MessageSquare', value: 'MessageSquare' },
        { label: 'TrendingUp', value: 'TrendingUp' },
        { label: 'Settings', value: 'Settings' },
        { label: 'FileText', value: 'FileText' },
        { label: 'Database', value: 'Database' },
      ],
    },
    {
      name: 'themeColor',
      type: 'select',
      defaultValue: 'blue',
      options: [
        { label: 'Blue', value: 'blue' },
        { label: 'Purple', value: 'purple' },
        { label: 'Indigo', value: 'indigo' },
        { label: 'Green', value: 'green' },
        { label: 'Yellow', value: 'yellow' },
      ],
    },
    {
      name: 'recommendations',
      type: 'array',
      fields: [
        {
          name: 'type',
          type: 'select',
          options: [
            { label: 'Product', value: 'product' },
            { label: 'Custom Link', value: 'custom' },
          ],
          defaultValue: 'product',
        },
        {
          name: 'productRecommendation',
          type: 'relationship',
          relationTo: 'products',
          admin: {
            condition: (data, siblingData) => siblingData?.type === 'product',
          }
        },
        {
          name: 'customLink',
          type: 'group',
          admin: {
            condition: (data, siblingData) => siblingData?.type === 'custom',
          },
          fields: [
            {
              name: 'text',
              type: 'text',
            },
            {
              name: 'url',
              type: 'text',
            }
          ]
        },
        {
          name: 'rationale',
          type: 'textarea',
          admin: {
            description: 'Why this product is recommended for this use case',
          }
        }
      ]
    },
    {
      name: 'applicableProduct',
      type: 'relationship',
      relationTo: 'products',
      admin: {
        description: 'If this is a use case for alternatives to a specific product',
      }
    },
    {
      name: 'comparisonFocus',
      type: 'select',
      options: [
        { label: 'General', value: 'general' },
        { label: 'Price-conscious', value: 'price' },
        { label: 'Feature-rich', value: 'features' },
        { label: 'Easy implementation', value: 'implementation' },
        { label: 'Small business', value: 'smb' },
        { label: 'Enterprise', value: 'enterprise' },
      ],
      defaultValue: 'general',
    }
  ]
}