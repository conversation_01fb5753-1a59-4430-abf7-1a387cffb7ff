import { CollectionConfig } from 'payload'

export const Authors: <AUTHORS>
  slug: 'authors',
  admin: {
    useAsTitle: 'name',
    group: 'People',
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'role',
      type: 'text',
    },
    {
      name: 'avatar',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'bio',
      type: 'textarea',
    },
    {
      name: 'social',
      type: 'group',
      fields: [
        {
          name: 'linkedin',
          type: 'text',
        },
        {
          name: 'twitter',
          type: 'text',
        }
      ]
    },
    {
      name: 'expertise',
      type: 'array',
      fields: [
        {
          name: 'area',
          type: 'text',
        }
      ],
      admin: {
        description: 'Areas of expertise (e.g., "CRM Software", "Marketing Technology")',
      }
    },
    {
      name: 'email',
      type: 'email',
      admin: {
        description: 'For internal use only, not displayed publicly',
        position: 'sidebar',
      }
    },
    {
      name: 'featuredAuthor',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Display in featured authors sections',
        position: 'sidebar',
      }
    },
    {
      name: 'authoredProducts',
      type: 'relationship',
      relationTo: 'products',
      hasMany: true,
      admin: {
        description: 'Products this author has written about',
      }
    }
  ]
}