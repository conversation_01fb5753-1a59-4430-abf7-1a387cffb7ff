import { CollectionConfig } from 'payload'

export const ProductFeatures: CollectionConfig = {
  slug: 'product-features',
  admin: {
    useAsTitle: 'title',
    group: 'Product Content',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'product',
      type: 'relationship',
      relationTo: 'products',
      required: true,
    },
    {
      name: 'icon',
      type: 'select',
      options: [
        { label: 'Star', value: 'Star' },
        { label: 'MessageSquare', value: 'MessageSquare' },
        { label: 'Zap', value: 'Zap' },
        { label: 'FileText', value: 'FileText' },
        { label: 'Users', value: 'Users' },
        { label: 'Shield', value: 'Shield' },
        { label: 'Clock', value: 'Clock' },
        { label: 'Calendar', value: 'Calendar' },
        { label: 'CheckCircle2', value: 'CheckCircle2' },
        { label: 'Database', value: 'Database' },
        { label: 'Settings', value: 'Settings' },
        { label: 'CloudCog', value: 'CloudCog' },
        { label: 'TrendingUp', value: 'TrendingUp' },
      ],
    },
    {
      name: 'description',
      type: 'textarea',
    },
    {
      name: 'category',
      type: 'select',
      options: [
        { label: 'Find & Attract Leads', value: 'find-attract-leads' },
        { label: 'Close More Deals', value: 'close-more-deals' },
        { label: 'Support & Retain Customers', value: 'support-retain-customers' },
        { label: 'Understand What\'s Working', value: 'understand-whats-working' },
        { label: 'Automate & Save Time', value: 'automate-save-time' },
        { label: 'Customize & Connect', value: 'customize-connect' },
        { label: 'Collaborate Across Teams', value: 'collaborate-teams' },
      ],
      required: true,
    },
    {
      name: 'question',
      type: 'text',
      admin: {
        description: 'Question this feature answers (optional)',
      },
    },
    {
      name: 'technicalSpecs',
      type: 'array',
      fields: [
        {
          name: 'spec',
          type: 'text',
        }
      ]
    },
    {
      name: 'themeColor',
      type: 'select',
      defaultValue: 'blue',
      options: [
        { label: 'Blue', value: 'blue' },
        { label: 'Purple', value: 'purple' },
        { label: 'Indigo', value: 'indigo' },
        { label: 'Violet', value: 'violet' },
        { label: 'Sky', value: 'sky' },
        { label: 'Navy', value: 'navy' },
        { label: 'Teal', value: 'teal' },
      ],
    },
    {
      name: 'ctaLink',
      type: 'text',
      admin: {
        description: 'Optional link to redirect to when clicked (e.g. /features/details)',
      }
    },
    {
      name: 'ctaText',
      type: 'text',
      admin: {
        description: 'Text for the call-to-action button (if link provided)',
      }
    }
  ]
}