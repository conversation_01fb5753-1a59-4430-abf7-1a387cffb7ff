import { CollectionConfig } from 'payload'
import { AIHelperField } from '@/components/AI/AIHelperField'
import SimplifiedA<PERSON>rompt<PERSON>ield from '@/components/AI/SimplifiedAIPromptField'
export const Products: CollectionConfig = {
  slug: 'products',
  admin: {
    useAsTitle: 'name',
    group: 'Product Content',
  },
  fields: [
    // Basic Info
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'slug',
      type: 'text',
      admin: {
        position: 'sidebar',
      },
      hooks: {
        beforeValidate: [
          ({ data }) => {
            // Generate slug from name if not provided
            return data.name?.toLowerCase().replace(/ /g, '-') || ''
          },
        ],
      },
    },
    {
      name: 'logo',
      type: 'upload',
      relationTo: 'media',
      required: true,
    },
    {
      name: 'featuredImage',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Main header image for product pages',
      },
    },
    {
      name: 'overview',
      type: 'textarea',
      admin: {
        components: {
          Field: {
            path: '@/components/AI/AIHelperField',
            exportName: 'AIHelperField'
          }
        }
      }
    },
    {
      name: 'overviewRich',
      type: 'richText',
      admin: {
        components: {
          Field: {
            path: '@/components/AI/AIHelperField',
            exportName: 'AIHelperField'
          }
        }
      }
    },
    {
      name: 'descriptionAIHelper',
      type: 'ui',
      admin: {
        description: 'Generate content for the description field using AI',
        components: {
          Field:  {
            path: '@/components/AI/SimplifiedAIPromptField',
            exportName: 'AIHelperField'
          }
        },
        // Optional: explicitly state target field
        targetFieldName: 'description'
      }
    },
    // Metadata
    {
      name: 'meta',
      type: 'group',
      fields: [
        {
          name: 'lastUpdated',
          type: 'date',
        },
        {
          name: 'industryCategory',
          type: 'select',
          options: [
            { label: 'CRM', value: 'crm' },
            { label: 'Marketing', value: 'marketing' },
            { label: 'Sales', value: 'sales' },
            // Add more categories
          ],
        },
        {
          name: 'tags',
          type: 'array',
          fields: [
            {
              name: 'tag',
              type: 'text',
            }
          ]
        }
      ]
    },
    
    // Relationships
    {
      name: 'seller',
      type: 'relationship',
      relationTo: 'sellers',
      required: true,
    },
    {
      name: 'alternatives',
      type: 'relationship',
      relationTo: 'products',
      hasMany: true,
      admin: {
        description: 'Products that are alternatives to this one',
      }
    },
    
    // Rating & Stats
    {
      name: 'rating',
      type: 'group',
      fields: [
        {
          name: 'overall',
          type: 'number',
          min: 0,
          max: 5,
          admin: {
            description: 'Overall rating out of 5',
          }
        },
        {
          name: 'usability',
          type: 'number',
          min: 0,
          max: 5,
        },
        {
          name: 'features',
          type: 'number',
          min: 0,
          max: 5,
        },
        {
          name: 'support',
          type: 'number',
          min: 0,
          max: 5,
        },
        {
          name: 'value',
          type: 'number',
          min: 0,
          max: 5,
        },
        {
          name: 'reviewCount',
          type: 'number',
        }
      ]
    },
    
    // Expert Take
    {
      name: 'expertView',
      type: 'group',
      fields: [
        {
          name: 'strengths',
          type: 'array',
          fields: [
            {
              name: 'text',
              type: 'text',
            }
          ]
        },
        {
          name: 'considerations',
          type: 'array',
          fields: [
            {
              name: 'text',
              type: 'text',
            }
          ]
        },
        {
          name: 'weaknesses',
          type: 'array',
          fields: [
            {
              name: 'text',
              type: 'text',
            }
          ]
        },
        {
          name: 'summary',
          type: 'textarea',
        },
        {
          name: 'award',
          type: 'select',
          options: [
            { label: 'None', value: 'none' },
            { label: "Editor's Choice", value: 'editors-choice' },
            { label: 'Best Value', value: 'best-value' },
            { label: 'Best for Small Business', value: 'best-small-business' },
          ]
        }
      ]
    },
    
    // Research Attribution
    {
      name: 'researchers',
      type: 'array',
      fields: [
        {
          name: 'person',
          type: 'relationship',
          relationTo: 'authors',
        },
        {
          name: 'role',
          type: 'select',
          options: [
            { label: 'Researcher', value: 'researcher' },
            { label: 'Fact-checker', value: 'fact-checker' },
            { label: 'Editor', value: 'editor' },
          ]
        }
      ]
    }
  ]
}