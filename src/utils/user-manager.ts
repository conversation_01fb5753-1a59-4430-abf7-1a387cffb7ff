/**
 * User Management Utility
 * Provides consistent user identification across the workflow system
 */

export interface WorkflowUser {
  id: string;
  email?: string;
  name?: string;
  role?: 'admin' | 'editor' | 'reviewer' | 'user';
}

export class UserManager {
  private static instance: UserManager;
  private currentUser: WorkflowUser | null = null;

  private constructor() {}

  static getInstance(): UserManager {
    if (!UserManager.instance) {
      UserManager.instance = new UserManager();
    }
    return UserManager.instance;
  }

  /**
   * Get the current user for workflow operations
   * In a real implementation, this would integrate with your auth system
   */
  getCurrentUser(): WorkflowUser {
    if (this.currentUser) {
      return this.currentUser;
    }

    // Default user for demo purposes
    return {
      id: 'default-user',
      email: '<EMAIL>',
      name: 'Demo User',
      role: 'admin'
    };
  }

  /**
   * Set the current user (for testing or manual override)
   */
  setCurrentUser(user: WorkflowUser): void {
    this.currentUser = user;
  }

  /**
   * Get user ID for workflow operations
   */
  getCurrentUserId(): string {
    return this.getCurrentUser().id;
  }

  /**
   * Check if user can approve artifacts
   */
  canApprove(userId?: string): boolean {
    const user = userId ? { id: userId } : this.getCurrentUser();
    // In a real implementation, this would check permissions
    return true;
  }

  /**
   * Get default approvers for workflow steps
   */
  getDefaultApprovers(): string[] {
    return [this.getCurrentUserId()];
  }

  /**
   * Format user display name
   */
  getUserDisplayName(userId: string): string {
    if (userId === this.getCurrentUserId()) {
      const user = this.getCurrentUser();
      return user.name || user.email || user.id;
    }
    return userId;
  }
}

// Export singleton instance
export const userManager = UserManager.getInstance();
