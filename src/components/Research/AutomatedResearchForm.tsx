'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
TextInput,
TextArea,
Select,
Checkbox,
ProgressBar,
PreviewCard,
FormButton,
PreviewField
} from './FormFields';
import './formStyles.scss';

// Define the category options
const CATEGORIES = [
{ value: 'crm', label: 'CRM' },
{ value: 'marketing', label: 'Marketing' },
{ value: 'sales', label: 'Sales' },
];

// Define the depth options
const RESEARCH_DEPTH_OPTIONS = [
{ value: 'basic', label: 'Basic (faster, less detailed)' },
{ value: 'standard', label: 'Standard (balanced)' },
{ value: 'comprehensive', label: 'Comprehensive (slower, more detailed)' },
];

// Define the icon options
const ICON_OPTIONS = [
{ value: 'Star', label: 'Star' },
{ value: 'MessageSquare', label: 'Message Square' },
{ value: 'Zap', label: 'Zap' },
{ value: 'FileText', label: 'File Text' },
{ value: 'Users', label: 'Users' },
{ value: 'Shield', label: 'Shield' },
{ value: 'Clock', label: 'Clock' },
{ value: 'Calendar', label: 'Calendar' },
{ value: 'CheckCircle2', label: 'Check Circle' },
{ value: 'Database', label: 'Database' },
{ value: 'Settings', label: 'Settings' },
{ value: 'CloudCog', label: 'Cloud Cog' },
{ value: 'TrendingUp', label: 'Trending Up' },
];

// Define the category options for features
const FEATURE_CATEGORY_OPTIONS = [
{ value: 'find-attract-leads', label: 'Find & Attract Leads' },
{ value: 'close-more-deals', label: 'Close More Deals' },
{ value: 'support-retain-customers', label: 'Support & Retain Customers' },
{ value: 'understand-whats-working', label: 'Understand What\'s Working' },
{ value: 'automate-save-time', label: 'Automate & Save Time' },
{ value: 'customize-connect', label: 'Customize & Connect' },
{ value: 'collaborate-teams', label: 'Collaborate Across Teams' },
];

// Define the FAQ category options
const FAQ_CATEGORY_OPTIONS = [
{ value: 'general', label: 'General' },
{ value: 'features', label: 'Features' },
{ value: 'pricing', label: 'Pricing' },
{ value: 'support', label: 'Support' },
{ value: 'implementation', label: 'Implementation' },
{ value: 'alternatives', label: 'Alternatives' },
];

// Interface for the research form state
interface ResearchFormState {
productName: string;
category: string;
generateSeller: boolean;
sellerName: string;
existingSeller: string | null;
researchDepth: 'basic' | 'standard' | 'comprehensive';
generateFeatures: boolean;
generatePricing: boolean;
generateFAQs: boolean;
}

// Interface for API response
interface ResearchResponse {
success: boolean;
message: string;
data?: {
  productId?: number;
  sellerId?: number;
};
preview?: {
  product: any;
  seller?: any;
  features?: any[];
  pricingPlans?: any[];
  faqs?: any[];
};
error?: string;
}

// Main form components
const AdvancedResearchForm: React.FC = () => {
const router = useRouter();

// Initialize form state
const [formState, setFormState] = useState<ResearchFormState>({
  productName: '',
  category: 'crm',
  generateSeller: true,
  sellerName: '',
  existingSeller: null,
  researchDepth: 'standard',
  generateFeatures: true,
  generatePricing: true,
  generateFAQs: true,
});

// UI states
const [isLoading, setIsLoading] = useState(false);
const [error, setError] = useState<string | null>(null);
const [preview, setPreview] = useState<ResearchResponse['preview'] | null>(null);
const [existingSellers, setExistingSellers] = useState<Array<{id: number, name: string}>>([]);
const [previewStep, setPreviewStep] = useState(0);
const [isSubmitting, setIsSubmitting] = useState(false);
const [successMessage, setSuccessMessage] = useState<string | null>(null);
const [editablePreview, setEditablePreview] = useState<any>(null);
const [researchProgress, setResearchProgress] = useState<{
  stage: string;
  progress: number;
  message: string;
} | null>(null);

// Research progress messages
const progressMessages = {
  initial: 'Initializing research process...',
  product: 'Researching product information...',
  seller: 'Gathering seller information...',
  features: 'Identifying key product features...',
  pricing: 'Generating pricing plans...',
  faqs: 'Creating frequently asked questions...',
  final: 'Finishing up and preparing results...'
};

// Fetch existing sellers for dropdown
useEffect(() => {
  const fetchSellers = async () => {
    try {
      const response = await fetch('/api/sellers?limit=100');
      const data = await response.json();
      if (data.docs) {
        setExistingSellers(data.docs.map((seller: any) => ({
          id: seller.id,
          name: seller.name
        })));
      }
    } catch (err) {
      console.error('Error fetching sellers:', err);
    }
  };

  fetchSellers();
}, []);

// Handle form input changes
const handleInputChange = (name: string, value: any) => {
  setFormState(prev => ({ ...prev, [name]: value }));
  
  // Clear preview when input changes
  if (preview) {
    setPreview(null);
  }
};

// Simulate progress update for better UX
const simulateProgressUpdates = () => {
  // Start with product research
  setResearchProgress({
    stage: 'product',
    progress: 20,
    message: progressMessages.product
  });

  // Simulate progress updates
  const stages = ['product', 'seller', 'features', 'pricing', 'faqs', 'final'];
  const progressValues = [20, 40, 60, 80, 90, 100];
  
  stages.forEach((stage, index) => {
    if (index === 0) return; // Skip first one as we already set it
    
    setTimeout(() => {
      setResearchProgress({
        stage,
        progress: progressValues[index],
        message: progressMessages[stage as keyof typeof progressMessages]
      });
    }, 2000 * (index + 1)); // Stagger updates for realistic feel
  });
};

// Handle final submission to save to CMS
const handleSaveToDatabase = async () => {
  setIsSubmitting(true);
  setError(null);

  try {
    // Use the edited preview data
    const finalSubmitData = {
      ...formState,
      preview: false // Request actual save
    };

    const response = await fetch('/api/automated-research', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(finalSubmitData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to save content to database');
    }

    const data: ResearchResponse = await response.json();
    
    if (!data.success) {
      throw new Error(data.message || 'Failed to save content');
    }

    // Show success message
    setSuccessMessage('Content successfully generated and saved to the database!');
    
    // Redirect to the product page after a short delay
    if (data.data?.productId) {
      setTimeout(() => {
        router.push(`/admin/collections/products/${data.data.productId}`);
      }, 2000);
    }
    
  } catch (err) {
    setError(err instanceof Error ? err.message : 'An unknown error occurred');
    setIsSubmitting(false);
  }
};

// Handle preview navigation
const handleNextPreview = () => {
  setPreviewStep(prev => Math.min(prev + 1, 4)); // Max 5 steps (0-4)
};

const handlePrevPreview = () => {
  setPreviewStep(prev => Math.max(prev - 1, 0));
};
// Handle edits in the preview
const handlePreviewEdit = (section: string, index: number | null, field: string, value: any) => {
  setEditablePreview(prev => {
    if (!prev) return prev;
    
    const newPreview = {...prev};
    
    // Handle different sections
    if (section === 'product') {
      if (field.includes('.')) {
        // Handle nested fields like expertView.strengths[0].text
        const [parentField, childField] = field.split('.');
        if (index !== null && Array.isArray(newPreview.product[parentField])) {
          newPreview.product[parentField][index][childField] = value;
        } else if (newPreview.product[parentField]) {
          const nestedObj = {...newPreview.product[parentField]};
          nestedObj[childField] = value;
          newPreview.product[parentField] = nestedObj;
        }
      } else {
        // Handle direct fields
        newPreview.product = {...newPreview.product, [field]: value};
      }
    } 
    else if (section === 'seller') {
      if (field.includes('.')) {
        const [parentField, childField] = field.split('.');
        if (index !== null && Array.isArray(newPreview.seller[parentField])) {
          newPreview.seller[parentField][index][childField] = value;
        }
      } else {
        newPreview.seller = {...newPreview.seller, [field]: value};
      }
    }
    else if (section === 'features' && Array.isArray(newPreview.features)) {
      if (index !== null) {
        const updatedFeatures = [...newPreview.features];
        updatedFeatures[index] = {...updatedFeatures[index], [field]: value};
        newPreview.features = updatedFeatures;
      }
    }
    else if (section === 'pricingPlans' && Array.isArray(newPreview.pricingPlans)) {
      if (index !== null) {
        if (field.includes('.')) {
          // Handle nested fields like features[0].feature
          const [parentField, childField] = field.split('.');
          if (parentField === 'features' && Array.isArray(newPreview.pricingPlans[index].features)) {
            const featureIndex = parseInt(childField);
            const updatedFeatures = [...newPreview.pricingPlans[index].features];
            updatedFeatures[featureIndex].feature = value;
            
            const updatedPlans = [...newPreview.pricingPlans];
            updatedPlans[index] = {...updatedPlans[index], features: updatedFeatures};
            newPreview.pricingPlans = updatedPlans;
          }
        } else {
          const updatedPlans = [...newPreview.pricingPlans];
          updatedPlans[index] = {...updatedPlans[index], [field]: value};
          newPreview.pricingPlans = updatedPlans;
        }
      }
    }
    else if (section === 'faqs' && Array.isArray(newPreview.faqs)) {
      if (index !== null) {
        const updatedFaqs = [...newPreview.faqs];
        updatedFaqs[index] = {...updatedFaqs[index], [field]: value};
        newPreview.faqs = updatedFaqs;
      }
    }
    
    return newPreview;
  });
};

// Initialize editable preview when preview data arrives
useEffect(() => {
  if (preview && !editablePreview) {
    setEditablePreview({...preview});
  }
}, [preview]);
// Render pricing plans preview section
const renderPricingPlansPreview = () => {
  if (!formState.generatePricing || !editablePreview) return (
    <div className="preview-section">
      <PreviewCard title="Pricing Plans">
        <p>No pricing plans were generated based on your selection.</p>
      </PreviewCard>
    </div>
  );

  if (!editablePreview.pricingPlans || !editablePreview.pricingPlans.length) return (
    <div className="preview-section">
      <PreviewCard title="Pricing Plans">
        <p>No pricing plan data available.</p>
      </PreviewCard>
    </div>
  );
  
  return (
    <div className="preview-section">
      <h3 className="section-title">Pricing Plans</h3>
      {editablePreview.pricingPlans.map((plan: any, index: number) => (
        <PreviewCard key={index} title={`Plan ${index + 1}`}>
          <PreviewField label="Title">
            <TextInput
              label=""
              value={plan.title}
              onChange={(value) => handlePreviewEdit('pricingPlans', index, 'title', value)}
            />
          </PreviewField>
          
          {plan.subtitle && (
            <PreviewField label="Subtitle">
              <TextInput
                label=""
                value={plan.subtitle}
                onChange={(value) => handlePreviewEdit('pricingPlans', index, 'subtitle', value)}
              />
            </PreviewField>
          )}
          
          <div className="preview-field-row">
            <div className="preview-field-col">
              <PreviewField label="Price">
                <TextInput
                  label=""
                  value={plan.price}
                  onChange={(value) => handlePreviewEdit('pricingPlans', index, 'price', value)}
                />
              </PreviewField>
            </div>
            
            <div className="preview-field-col">
              <PreviewField label="Billing Frequency">
                <TextInput
                  label=""
                  value={plan.billingFrequency}
                  onChange={(value) => handlePreviewEdit('pricingPlans', index, 'billingFrequency', value)}
                />
              </PreviewField>
            </div>
          </div>
          
          <PreviewField label="Description">
            <TextArea
              label=""
              value={plan.description}
              onChange={(value) => handlePreviewEdit('pricingPlans', index, 'description', value)}
              rows={2}
            />
          </PreviewField>
          
          {plan.features && plan.features.length > 0 && (
            <PreviewField label="Features">
              <ul className="preview-list">
                {plan.features.map((feature: any, i: number) => (
                  <li key={i} className="preview-list__item">
                    <TextInput
                      label=""
                      value={feature.feature}
                      onChange={(value) => handlePreviewEdit('pricingPlans', index, `features.${i}`, value)}
                    />
                  </li>
                ))}
              </ul>
            </PreviewField>
          )}
          
          <div className="preview-field">
            <Checkbox
              label="Mark as Popular"
              checked={plan.popular === true}
              onChange={(checked) => handlePreviewEdit('pricingPlans', index, 'popular', checked)}
            />
          </div>
        </PreviewCard>
      ))}
    </div>
  );
};
// Render product preview section
const renderProductPreview = () => {
  if (!editablePreview || !editablePreview.product) return null;
  
  return (
    <div className="preview-section">
      <PreviewCard title="Product Overview">
        <PreviewField label="Name">
          <TextInput
            label=""
            value={editablePreview.product.name}
            onChange={(value) => handlePreviewEdit('product', null, 'name', value)}
          />
        </PreviewField>
        
        <PreviewField label="Category">
          <Select
            label=""
            value={editablePreview.product.meta?.industryCategory || formState.category}
            options={CATEGORIES}
            onChange={(value) => handlePreviewEdit('product', null, 'meta.industryCategory', value)}
          />
        </PreviewField>
        
        <PreviewField label="Overview">
          <TextArea
            label=""
            value={editablePreview.product.overview}
            onChange={(value) => handlePreviewEdit('product', null, 'overview', value)}
            rows={6}
          />
        </PreviewField>
        
        {editablePreview.product.expertView && (
          <>
            <PreviewField label="Strengths">
              <ul className="preview-list">
                {editablePreview.product.expertView.strengths?.map((item: any, i: number) => (
                  <li key={i} className="preview-list__item">
                    <TextInput
                      label=""
                      value={item.text}
                      onChange={(value) => handlePreviewEdit('product', i, 'expertView.strengths.text', value)}
                    />
                  </li>
                ))}
              </ul>
            </PreviewField>
            
            <PreviewField label="Considerations">
              <ul className="preview-list">
                {editablePreview.product.expertView.considerations?.map((item: any, i: number) => (
                  <li key={i} className="preview-list__item">
                    <TextInput
                      label=""
                      value={item.text}
                      onChange={(value) => handlePreviewEdit('product', i, 'expertView.considerations.text', value)}
                    />
                  </li>
                ))}
              </ul>
            </PreviewField>
            
            <PreviewField label="Weaknesses">
              <ul className="preview-list">
                {editablePreview.product.expertView.weaknesses?.map((item: any, i: number) => (
                  <li key={i} className="preview-list__item">
                    <TextInput
                      label=""
                      value={item.text}
                      onChange={(value) => handlePreviewEdit('product', i, 'expertView.weaknesses.text', value)}
                    />
                  </li>
                ))}
              </ul>
            </PreviewField>
          </>
        )}
      </PreviewCard>
    </div>
  );
};

// Render seller preview section
const renderSellerPreview = () => {
  if (!formState.generateSeller || !editablePreview) return (
    <div className="preview-section">
      <PreviewCard title="Seller Information">
        <p>No seller information was generated based on your selection.</p>
      </PreviewCard>
    </div>
  );

  if (!editablePreview.seller) return (
    <div className="preview-section">
      <PreviewCard title="Seller Information">
        <p>No seller information available.</p>
      </PreviewCard>
    </div>
  );
  
  return (
    <div className="preview-section">
      <PreviewCard title="Seller Information">
        <PreviewField label="Name">
          <TextInput
            label=""
            value={editablePreview.seller.name}
            onChange={(value) => handlePreviewEdit('seller', null, 'name', value)}
          />
        </PreviewField>
        
        <PreviewField label="Description">
          <TextArea
            label=""
            value={editablePreview.seller.description}
            onChange={(value) => handlePreviewEdit('seller', null, 'description', value)}
            rows={4}
          />
        </PreviewField>
        
        {editablePreview.seller.foundedYear && (
          <PreviewField label="Founded Year">
            <TextInput
              label=""
              value={editablePreview.seller.foundedYear.toString()}
              onChange={(value) => handlePreviewEdit('seller', null, 'foundedYear', parseInt(value) || 0)}
            />
          </PreviewField>
        )}
        
        {editablePreview.seller.headquarters && (
          <PreviewField label="Headquarters">
            <TextInput
              label=""
              value={editablePreview.seller.headquarters}
              onChange={(value) => handlePreviewEdit('seller', null, 'headquarters', value)}
            />
          </PreviewField>
        )}
        
        {editablePreview.seller.keyFacts && editablePreview.seller.keyFacts.length > 0 && (
          <PreviewField label="Key Facts">
            <ul className="preview-list">
              {editablePreview.seller.keyFacts.map((item: any, i: number) => (
                <li key={i} className="preview-list__item">
                  <TextInput
                    label=""
                    value={item.fact}
                    onChange={(value) => handlePreviewEdit('seller', i, 'keyFacts.fact', value)}
                  />
                </li>
              ))}
            </ul>
          </PreviewField>
        )}
      </PreviewCard>
    </div>
  );
};
// Render features preview section
const renderFeaturesPreview = () => {
  if (!formState.generateFeatures || !editablePreview) return (
    <div className="preview-section">
      <PreviewCard title="Product Features">
        <p>No features were generated based on your selection.</p>
      </PreviewCard>
    </div>
  );

  if (!editablePreview.features || !editablePreview.features.length) return (
    <div className="preview-section">
      <PreviewCard title="Product Features">
        <p>No feature data available.</p>
      </PreviewCard>
    </div>
  );
  
  return (
    <div className="preview-section">
      <h3 className="section-title">Product Features</h3>
      {editablePreview.features.map((feature: any, index: number) => (
        <PreviewCard key={index} title={`Feature ${index + 1}`}>
          <PreviewField label="Title">
            <TextInput
              label=""
              value={feature.title}
              onChange={(value) => handlePreviewEdit('features', index, 'title', value)}
            />
          </PreviewField>
          
          <PreviewField label="Description">
            <TextArea
              label=""
              value={feature.description}
              onChange={(value) => handlePreviewEdit('features', index, 'description', value)}
              rows={3}
            />
          </PreviewField>
          
          <div className="preview-field-row">
            <div className="preview-field-col">
              <PreviewField label="Category">
                <Select
                  label=""
                  value={feature.category}
                  options={FEATURE_CATEGORY_OPTIONS}
                  onChange={(value) => handlePreviewEdit('features', index, 'category', value)}
                />
              </PreviewField>
            </div>
            
            <div className="preview-field-col">
              <PreviewField label="Icon">
                <Select
                  label=""
                  value={feature.icon}
                  options={ICON_OPTIONS}
                  onChange={(value) => handlePreviewEdit('features', index, 'icon', value)}
                  small
                />
              </PreviewField>
            </div>
          </div>
        </PreviewCard>
      ))}
    </div>
  );
};

// Render FAQs preview section
const renderFAQsPreview = () => {
  if (!formState.generateFAQs || !editablePreview) return (
    <div className="preview-section">
      <PreviewCard title="Frequently Asked Questions">
        <p>No FAQs were generated based on your selection.</p>
      </PreviewCard>
    </div>
  );

  if (!editablePreview.faqs || !editablePreview.faqs.length) return (
    <div className="preview-section">
      <PreviewCard title="Frequently Asked Questions">
        <p>No FAQ data available.</p>
      </PreviewCard>
    </div>
  );
  
  return (
    <div className="preview-section">
      <h3 className="section-title">Frequently Asked Questions</h3>
      {editablePreview.faqs.map((faq: any, index: number) => (
        <PreviewCard key={index} title={`FAQ ${index + 1}`}>
          <PreviewField label="Question">
            <TextInput
              label=""
              value={faq.question}
              onChange={(value) => handlePreviewEdit('faqs', index, 'question', value)}
            />
          </PreviewField>
          
          <PreviewField label="Answer">
            <TextArea
              label=""
              value={typeof faq.answer === 'string' ? faq.answer : 
                     (faq.answer && faq.answer.root && faq.answer.root.children && 
                      faq.answer.root.children[0] && faq.answer.root.children[0].children && 
                      faq.answer.root.children[0].children[0] && 
                      faq.answer.root.children[0].children[0].text) || ''}
              onChange={(value) => handlePreviewEdit('faqs', index, 'answer', value)}
              rows={3}
            />
          </PreviewField>
          
          <PreviewField label="Category">
            <Select
              label=""
              value={faq.category}
              options={FAQ_CATEGORY_OPTIONS}
              onChange={(value) => handlePreviewEdit('faqs', index, 'category', value)}
            />
          </PreviewField>
        </PreviewCard>
      ))}
    </div>
  );
};

// Render the preview content based on current preview step
const renderPreviewContent = () => {
  if (!preview || !editablePreview) return null;

  switch (previewStep) {
    case 0: // Product overview
      return renderProductPreview();
    case 1: // Seller information
      return renderSellerPreview();
    case 2: // Features
      return renderFeaturesPreview();
    case 3: // Pricing plans
      return renderPricingPlansPreview();
    case 4: // FAQs
      return renderFAQsPreview();
    default:
      return null;
  }
};

// Updated handleGenerateResearch function
const handleGenerateResearch = async (event: React.FormEvent) => {
  event.preventDefault(); // Prevent form submission

  setIsLoading(true);
  setError(null);
  setPreview(null);
  
  // Start showing progress immediately
  setResearchProgress({
    stage: 'initial',
    progress: 10,
    message: progressMessages.initial
  });

  // Simulate progress updates
  simulateProgressUpdates();

  try {
    // Validate form
    if (!formState.productName) {
      throw new Error('Product name is required');
    }
    
    if (formState.generateSeller && !formState.existingSeller && !formState.sellerName) {
      throw new Error('Seller name is required when generating a new seller');
    }

    // Prepare the request payload
    const requestData = {
      ...formState,
      preview: true, // Indicate that this is a preview request
    };

    // Call the API
    const response = await fetch('/api/automated-research', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to generate research preview');
    }

    const data: ResearchResponse = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'Failed to generate research preview');
    }

    // Update the preview state with the response data
    setPreview(data.preview || null);
    
    // Initialize editable preview state
    setEditablePreview(data.preview || null);
    
    // Reset preview step to start at the beginning
    setPreviewStep(0);
    
    // Complete the progress
    setResearchProgress({
      stage: 'final',
      progress: 100,
      message: 'Content generation complete!'
    });
    
    // After a delay, hide the progress indicator
    setTimeout(() => {
      setResearchProgress(null);
    }, 1500);
  } catch (err) {
    setError(err instanceof Error ? err.message : 'An unknown error occurred');
    setResearchProgress(null);
  } finally {
    setIsLoading(false);
  }
};

// Render the main form
const renderForm = () => (
  <form onSubmit={handleGenerateResearch}>
    <div className="form-section">
      <h3>Product Information</h3>
      
      <TextInput 
        label="Product Name *" 
        value={formState.productName} 
        onChange={(value) => handleInputChange('productName', value)}
        placeholder="e.g. Salesforce, HubSpot, Zendesk"
        required
      />
      
      <Select 
        label="Product Category *" 
        value={formState.category}
        options={CATEGORIES}
        onChange={(value) => handleInputChange('category', value)}
        required
      />
    </div>

    <div className="form-section">
      <h3>Seller Information</h3>
      
      <Checkbox 
        label="Generate seller information" 
        checked={formState.generateSeller}
        onChange={(checked) => handleInputChange('generateSeller', checked)}
      />
      
      {formState.generateSeller && (
        <>
          <Select
            label="Select existing seller (optional)"
            value={formState.existingSeller || ''}
            options={[
              { value: '', label: '-- Create new seller --' },
              ...existingSellers.map(seller => ({
                value: seller.id.toString(),
                label: seller.name
              }))
            ]}
            onChange={(value) => handleInputChange('existingSeller', value)}
          />
          
          {!formState.existingSeller && (
            <TextInput
              label="Seller Name *"
              value={formState.sellerName}
              onChange={(value) => handleInputChange('sellerName', value)}
              placeholder="e.g. Salesforce Inc., HubSpot Inc."
              required={formState.generateSeller && !formState.existingSeller}
            />
          )}
        </>
      )}
    </div>

    <div className="form-section">
      <h3>Content Generation Options</h3>
      
      <Select
        label="Research Depth"
        value={formState.researchDepth}
        options={RESEARCH_DEPTH_OPTIONS}
        onChange={(value) => handleInputChange('researchDepth', value)}
      />
      
      <div className="checkbox-group">
        <Checkbox
          label="Generate product features"
          checked={formState.generateFeatures}
          onChange={(checked) => handleInputChange('generateFeatures', checked)}
        />
        
        <Checkbox
          label="Generate pricing plans"
          checked={formState.generatePricing}
          onChange={(checked) => handleInputChange('generatePricing', checked)}
        />
        
        <Checkbox
          label="Generate FAQs"
          checked={formState.generateFAQs}
          onChange={(checked) => handleInputChange('generateFAQs', checked)}
        />
      </div>
    </div>

<div className="form-actions">
  <FormButton
    type="submit"
    disabled={isLoading}
    variant="primary"
    icon={
      isLoading ? (
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          width="20" 
          height="20" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round"
          className="loading-spinner"
        >
          <path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83"></path>
        </svg>
      ) : (
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          width="20" 
          height="20" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round"
        >
          <path d="M5 12h14"></path>
          <path d="M12 5v14"></path>
        </svg>
      )
    }
  >
    {isLoading ? 'Generating...' : 'Generate Preview'}
  </FormButton>
  
  <FormButton
    onClick={() => {
      // Reset form to defaults
      setFormState({
        productName: '',
        category: 'crm',
        generateSeller: true,
        sellerName: '',
        existingSeller: null,
        researchDepth: 'standard',
        generateFeatures: true,
        generatePricing: true,
        generateFAQs: true,
      });
    }}
    variant="secondary"
    disabled={isLoading}
  >
    Reset Form
  </FormButton>
</div>

{/* Add spinner animation */}
<style jsx>{`
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .loading-spinner {
    animation: spin 1.5s linear infinite;
  }
`}</style>
  </form>
);

return (
  <div className="automated-research-form">
    <h2>Advanced Product Research & Content Generation</h2>
    
    {/* Success message */}
    {successMessage && (
      <div className="success-message">
        {successMessage}
      </div>
    )}
    
    {/* Error message */}
    {error && (
      <div className="error-message">
        {error}
      </div>
    )}

    {/* Main form */}
    {!preview && !researchProgress && renderForm()}

    {/* Progress indicator */}
    {researchProgress && (
      <ProgressBar
        stage={researchProgress.stage}
        progress={researchProgress.progress}
        message={researchProgress.message}
      />
    )}

    {/* Preview section */}
    {preview && (
      <div className="preview-container">
        <h3>Content Preview</h3>
        <p className="preview-instructions">
          Review the generated content before saving to the database. Navigate through the different sections below.
        </p>
        
        <div className="preview-navigation">
          <FormButton
            onClick={handlePrevPreview}
            disabled={previewStep === 0}
            variant="secondary"
            size="small"
          >
            ← Previous
          </FormButton>
          
          <div className="preview-steps">
            <span className={`step ${previewStep === 0 ? 'active' : ''}`}>Overview</span>
            <span className={`step ${previewStep === 1 ? 'active' : ''}`}>Seller</span>
            <span className={`step ${previewStep === 2 ? 'active' : ''}`}>Features</span>
            <span className={`step ${previewStep === 3 ? 'active' : ''}`}>Pricing</span>
            <span className={`step ${previewStep === 4 ? 'active' : ''}`}>FAQs</span>
          </div>
          
          <FormButton
            onClick={handleNextPreview}
            disabled={previewStep === 4}
            variant="secondary"
            size="small"
          >
            Next →
          </FormButton>
        </div>
        
        <div className="preview-content-container">
          {renderPreviewContent()}
        </div>
        
        <div className="form-actions">
          <FormButton
            onClick={() => {
              setPreview(null);
              setPreviewStep(0);
            }}
            variant="secondary"
          >
            ← Back to Form
          </FormButton>
          
          <FormButton
            onClick={handleSaveToDatabase}
            disabled={isSubmitting}
            variant="primary"
          >
            {isSubmitting ? 'Saving...' : 'Save to Database'}
          </FormButton>
        </div>
      </div>
    )}
  </div>
);
};

export default AdvancedResearchForm;