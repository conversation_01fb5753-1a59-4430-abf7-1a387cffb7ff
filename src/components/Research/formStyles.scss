// src/components/Research/formStyles.scss
/* Enhanced styling for research form input fields */

.edit-field {
    &__container {
      position: relative;
      margin-bottom: 1rem;
      transition: all 0.2s ease;
      
      &:focus-within {
        transform: translateY(-2px);
      }
    }
    
    &__label {
      display: block;
      font-size: 0.85rem;
      font-weight: 600;
      margin-bottom: 0.35rem;
      color: var(--theme-elevation-800);
    }
    
    &__input,
    &__textarea,
    &__select {
      width: 100%;
      padding: 0.65rem 0.75rem;
      border: 1px solid var(--theme-elevation-150);
      border-radius: 4px;
      font-family: var(--font-body);
      font-size: 0.95rem;
      color: var(--theme-elevation-900);
      background-color: white;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
      transition: all 0.2s ease;
      
      &:hover {
        border-color: var(--theme-elevation-300);
      }
      
      &:focus {
        border-color: var(--theme-primary-500);
        box-shadow: 0 0 0 3px rgba(var(--theme-primary-500-rgb), 0.15);
        outline: none;
      }
      
      &::placeholder {
        color: var(--theme-elevation-400);
      }
    }
    
    &__textarea {
      min-height: 100px;
      resize: vertical;
      line-height: 1.5;
    }
    
    &__select {
      appearance: none;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236B7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: right 0.75rem center;
      background-size: 16px 16px;
      padding-right: 2.5rem;
    }
    
    &__select-small {
      min-width: 140px;
      width: auto;
    }
  }
  
  /* Preview Content Styling */
  
  .preview {
    &-card {
      background-color: white;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
      padding: 1.25rem;
      margin-bottom: 1.5rem;
      border: 1px solid var(--theme-elevation-100);
      transition: all 0.2s ease;
      
      &:hover {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
      
      &__header {
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid var(--theme-elevation-100);
        
        h4 {
          margin: 0;
          font-size: 1.15rem;
          font-weight: 600;
          color: var(--theme-elevation-900);
        }
      }
      
      &__body {
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }
      
      &__footer {
        margin-top: 1rem;
        padding-top: 0.75rem;
        border-top: 1px solid var(--theme-elevation-100);
        display: flex;
        justify-content: flex-end;
        gap: 0.75rem;
      }
    }
    
    &-field {
      position: relative;
      
      &__label {
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.025em;
        color: var(--theme-elevation-600);
        margin-bottom: 0.35rem;
      }
    }
    
    &-list {
      margin: 0;
      padding: 0 0 0 1.25rem;
      
      &__item {
        margin-bottom: 0.5rem;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  
    &-field-row {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 0.5rem;
    }
    
    &-field-col {
      flex: 1 1 calc(50% - 0.5rem);
      min-width: 200px;
    }
  }
  
  /* Progress Bar Styling */
  
  .research-progress {
    position: relative;
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    text-align: center;
    
    &__title {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 1.25rem;
      color: var(--theme-elevation-900);
    }
    
    &__container {
      width: 100%;
      height: 10px;
      background-color: var(--theme-elevation-100);
      border-radius: 8px;
      margin: 1rem 0;
      overflow: hidden;
      box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.08);
    }
    
    &__bar {
      height: 100%;
      background: linear-gradient(90deg, var(--theme-primary-400) 0%, var(--theme-primary-600) 100%);
      border-radius: 8px;
      transition: width 0.5s ease;
    }
    
    &__stage {
      font-size: 0.85rem;
      font-weight: 500;
      color: var(--theme-elevation-700);
      margin-top: 0.75rem;
      display: flex;
      align-items: center;
      justify-content: center;
      
      svg {
        margin-right: 0.5rem;
        color: var(--theme-primary-500);
      }
    }
    
    &__message {
      font-size: 0.9rem;
      color: var(--theme-elevation-600);
      margin-top: 0.5rem;
    }
  }
  
  /* Button Styling */
  
  .form-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.6rem 1.2rem;
    font-size: 0.95rem;
    font-weight: 500;
    border-radius: 6px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 0.5rem;
    
    &--primary {
      background-color: var(--theme-primary-500);
      color: white;
      
      &:hover {
        background-color: var(--theme-primary-600);
      }
      
      &:active {
        background-color: var(--theme-primary-700);
      }
      
      &:disabled {
        background-color: var(--theme-elevation-200);
        color: var(--theme-elevation-500);
        cursor: not-allowed;
      }
    }
    
    &--secondary {
      background-color: white;
      color: var(--theme-elevation-800);
      border-color: var(--theme-elevation-200);
      
      &:hover {
        background-color: var(--theme-elevation-50);
        border-color: var(--theme-elevation-300);
      }
      
      &:active {
        background-color: var(--theme-elevation-100);
      }
      
      &:disabled {
        background-color: white;
        color: var(--theme-elevation-400);
        border-color: var(--theme-elevation-150);
        cursor: not-allowed;
      }
    }
    
    &--small {
      padding: 0.4rem 0.8rem;
      font-size: 0.85rem;
      border-radius: 4px;
    }
    
    &--icon {
      width: 38px;
      height: 38px;
      padding: 0;
      border-radius: 50%;
      
      svg {
        width: 18px;
        height: 18px;
      }
    }
  }
  
  /* Checkbox Styling */
  
  .checkbox-field {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    
    &__input {
      appearance: none;
      width: 20px;
      height: 20px;
      border: 2px solid var(--theme-elevation-300);
      border-radius: 4px;
      margin: 0;
      cursor: pointer;
      position: relative;
      transition: all 0.2s ease;
      
      &:hover {
        border-color: var(--theme-primary-400);
      }
      
      &:checked {
        background-color: var(--theme-primary-500);
        border-color: var(--theme-primary-500);
        
        &:after {
          content: '';
          position: absolute;
          top: 4px;
          left: 7px;
          width: 5px;
          height: 9px;
          border: solid white;
          border-width: 0 2px 2px 0;
          transform: rotate(45deg);
        }
      }
      
      &:disabled {
        background-color: var(--theme-elevation-150);
        border-color: var(--theme-elevation-200);
        cursor: not-allowed;
      }
    }
    
    &__label {
      font-size: 0.95rem;
      color: var(--theme-elevation-800);
      margin: 0;
      cursor: pointer;
    }
  }
  
  /* Navigation Styles */
  
  .preview-navigation {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    background-color: var(--theme-elevation-50);
    border-radius: 8px;
    margin-bottom: 1.5rem;
  }
  
  .preview-steps {
    display: flex;
    flex: 1;
    justify-content: space-between;
    overflow-x: auto;
    padding: 0 0.5rem;
    
    &::-webkit-scrollbar {
      height: 3px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: var(--theme-elevation-200);
      border-radius: 3px;
    }
    
    .step {
      position: relative;
      padding: 0.5rem 1rem;
      white-space: nowrap;
      font-size: 0.9rem;
      color: var(--theme-elevation-700);
      cursor: pointer;
      border-radius: 6px;
      transition: all 0.2s ease;
      
      &:hover {
        background-color: var(--theme-elevation-100);
      }
      
      &.active {
        background-color: var(--theme-primary-500);
        color: white;
        font-weight: 500;
      }
      
      &:not(:last-child):after {
        content: '';
        position: absolute;
        right: -20px;
        top: 50%;
        transform: translateY(-50%);
        width: 16px;
        height: 2px;
        background-color: var(--theme-elevation-200);
      }
    }
  }
  
  /* Form Section Styling */
  .form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--theme-elevation-50);
    border: 1px solid var(--theme-elevation-100);
    border-radius: 6px;
    
    h3 {
      margin-top: 0;
      margin-bottom: 1rem;
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--theme-elevation-800);
    }
  }
  
  .form-field {
    margin-bottom: 1rem;
    
    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: var(--theme-elevation-800);
    }
    
    &.checkbox {
      display: flex;
      align-items: center;
      
      label {
        margin-bottom: 0;
        margin-left: 0.5rem;
      }
    }
  }
  
  .form-actions {
    margin-top: 2rem;
    display: flex;
    justify-content: space-between;
  }
  
  /* Success and Error Messages */
  .success-message {
    margin: 1rem 0;
    padding: 0.75rem;
    background-color: var(--theme-success-50);
    border: 1px solid var(--theme-success-500);
    color: var(--theme-success-600);
    border-radius: 4px;
  }
  
  .error-message {
    margin: 1rem 0;
    padding: 0.75rem;
    background-color: var(--theme-error-50);
    border: 1px solid var(--theme-error-500);
    color: var(--theme-error-600);
    border-radius: 4px;
  }
  
  /* Preview Content Container */
  .preview-content-container {
    min-height: 350px;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--theme-elevation-50);
    border: 1px solid var(--theme-elevation-100);
    border-radius: 6px;
  }
  
  /* Media Queries for Responsive Design */
  @media (max-width: 768px) {
    .preview-steps {
      .step {
        &:not(:last-child):after {
          display: none;
        }
      }
    }
    
    .preview-field-row {
      flex-direction: column;
    }
    
    .form-actions {
      flex-direction: column;
      gap: 1rem;
      
      button {
        width: 100%;
      }
    }
  }
  .preview-steps {
    display: flex;
    flex: 1;
    justify-content: space-between;
    overflow-x: auto;
    padding: 0 0.5rem;
    
    &::-webkit-scrollbar {
      height: 3px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: var(--theme-elevation-200);
      border-radius: 3px;
    }
    
    .step {
      position: relative;
      padding: 0.5rem 1rem;
      white-space: nowrap;
      font-size: 0.9rem;
      color: var(--theme-elevation-700);
      cursor: pointer;
      border-radius: 6px;
      transition: all 0.2s ease;
      
      &:hover {
        background-color: var(--theme-elevation-100);
      }
      
      &.active {
        background-color: var(--theme-primary-500);
        color: white; /* Explicitly setting text color to white */
        font-weight: 500;
      }
      
      &:not(:last-child):after {
        content: '';
        position: absolute;
        right: -20px;
        top: 50%;
        transform: translateY(-50%);
        width: 16px;
        height: 2px;
        background-color: var(--theme-elevation-200);
      }
    }
  }
  .form-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.6rem 1.2rem;
    font-size: 0.95rem;
    font-weight: 500;
    border-radius: 6px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 0.5rem;
    
    &--primary {
      background-color: var(--theme-primary-500);
      color: white !important; /* Force white text */
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      
      &:hover {
        background-color: var(--theme-primary-600);
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }
      
      &:active {
        background-color: var(--theme-primary-700);
        transform: translateY(0);
      }
      
      &:disabled {
        background-color: var(--theme-elevation-200);
        color: var(--theme-elevation-500) !important;
        cursor: not-allowed;
        box-shadow: none;
        transform: none;
      }
    }
    
    &--secondary {
      background-color: white;
      color: var(--theme-elevation-800);
      border-color: var(--theme-elevation-200);
      
      &:hover {
        background-color: var(--theme-elevation-50);
        border-color: var(--theme-elevation-300);
        transform: translateY(-2px);
      }
      
      &:active {
        background-color: var(--theme-elevation-100);
        transform: translateY(0);
      }
      
      &:disabled {
        background-color: white;
        color: var(--theme-elevation-400);
        border-color: var(--theme-elevation-150);
        cursor: not-allowed;
        transform: none;
      }
    }
    
    &--small {
      padding: 0.4rem 0.8rem;
      font-size: 0.85rem;
      border-radius: 4px;
    }
  }