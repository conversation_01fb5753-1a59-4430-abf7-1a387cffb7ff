// src/components/Research/FormFields.tsx
import React from 'react';
import './formStyles.scss';

interface TextInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
}

export const TextInput: React.FC<TextInputProps> = ({
  label,
  value,
  onChange,
  placeholder = '',
  required = false,
  disabled = false,
}) => {
  return (
    <div className="edit-field__container">
      <label className="edit-field__label">
        {label} {required && <span className="required">*</span>}
      </label>
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        className="edit-field__input"
      />
    </div>
  );
};

interface TextAreaProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  rows?: number;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
}

export const TextArea: React.FC<TextAreaProps> = ({
  label,
  value,
  onChange,
  rows = 4,
  placeholder = '',
  required = false,
  disabled = false,
}) => {
  return (
    <div className="edit-field__container">
      <label className="edit-field__label">
        {label} {required && <span className="required">*</span>}
      </label>
      <textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        rows={rows}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        className="edit-field__textarea"
      />
    </div>
  );
};

interface SelectProps {
  label: string;
  value: string;
  options: Array<{ value: string; label: string }>;
  onChange: (value: string) => void;
  required?: boolean;
  disabled?: boolean;
  small?: boolean;
}

export const Select: React.FC<SelectProps> = ({
  label,
  value,
  options,
  onChange,
  required = false,
  disabled = false,
  small = false,
}) => {
  return (
    <div className="edit-field__container">
      <label className="edit-field__label">
        {label} {required && <span className="required">*</span>}
      </label>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        required={required}
        disabled={disabled}
        className={`edit-field__select ${small ? 'edit-field__select-small' : ''}`}
      >
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
};

interface CheckboxProps {
  label: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
}

export const Checkbox: React.FC<CheckboxProps> = ({
  label,
  checked,
  onChange,
  disabled = false,
}) => {
  return (
    <label className="checkbox-field">
      <input
        type="checkbox"
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        disabled={disabled}
        className="checkbox-field__input"
      />
      <span className="checkbox-field__label">{label}</span>
    </label>
  );
};

interface ProgressBarProps {
  stage: string;
  progress: number;
  message: string;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  stage,
  progress,
  message,
}) => {
  return (
    <div className="research-progress">
      <h3 className="research-progress__title">Generating Content</h3>
      <div className="research-progress__container">
        <div 
          className="research-progress__bar" 
          style={{width: `${progress}%`}}
        ></div>
      </div>
      <div className="research-progress__stage">
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          width="16" 
          height="16" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round"
        >
          <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
          <path d="M12 6v6l4 2"></path>
        </svg>
        <span>{stage}</span>
      </div>
      <p className="research-progress__message">{message}</p>
    </div>
  );
};

interface PreviewCardProps {
  title: string;
  children: React.ReactNode;
  actions?: React.ReactNode;
}

export const PreviewCard: React.FC<PreviewCardProps> = ({
  title,
  children,
  actions,
}) => {
  return (
    <div className="preview-card">
      <div className="preview-card__header">
        <h4>{title}</h4>
      </div>
      <div className="preview-card__body">
        {children}
      </div>
      {actions && (
        <div className="preview-card__footer">
          {actions}
        </div>
      )}
    </div>
  );
};

// Updated FormButton component
interface FormButtonProps {
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'primary' | 'secondary';
  size?: 'default' | 'small';
  disabled?: boolean;
  icon?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

export const FormButton: React.FC<FormButtonProps> = ({
  onClick,
  type = 'button',
  variant = 'primary',
  size = 'default',
  disabled = false,
  icon,
  children,
  className = '',
}) => {
  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`form-button form-button--${variant} ${size === 'small' ? 'form-button--small' : ''} ${className}`}
    >
      {icon && <span className="button-icon">{icon}</span>}
      <span className="button-text">{children}</span>
    </button>
  );
};

interface PreviewFieldProps {
  label: string;
  children: React.ReactNode;
}

export const PreviewField: React.FC<PreviewFieldProps> = ({
  label,
  children,
}) => {
  return (
    <div className="preview-field">
      <div className="preview-field__label">{label}</div>
      <div className="preview-field__content">
        {children}
      </div>
    </div>
  );
};