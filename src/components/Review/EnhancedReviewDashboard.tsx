/**
 * Enhanced Review Dashboard
 * Multi-reviewer support with deadlines and collaborative features
 */

'use client';

import { useState, useEffect } from 'react';

interface Reviewer {
  id: string;
  name: string;
  email: string;
  role: 'editor' | 'content_manager' | 'seo_specialist' | 'subject_expert';
}

interface ReviewAssignment {
  id: string;
  contentId: string;
  reviewerId: string;
  assignedBy: string;
  assignedAt: string;
  deadline?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'completed' | 'overdue' | 'escalated';
  completedAt?: string;
}

interface ContentItem {
  id: string;
  title: string;
  type: string;
  content: string;
  status: string;
  createdAt: string;
}

export default function EnhancedReviewDashboard() {
  const [assignments, setAssignments] = useState<ReviewAssignment[]>([]);
  const [reviewers, setReviewers] = useState<Reviewer[]>([]);
  const [contentItems, setContentItems] = useState<ContentItem[]>([]);
  const [selectedAssignment, setSelectedAssignment] = useState<ReviewAssignment | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Mock data for demonstration
  useEffect(() => {
    loadMockData();
  }, []);

  const loadMockData = () => {
    // Mock reviewers
    const mockReviewers: Reviewer[] = [
      { id: '1', name: 'Sarah Johnson', email: '<EMAIL>', role: 'editor' },
      { id: '2', name: 'Mike Chen', email: '<EMAIL>', role: 'content_manager' },
      { id: '3', name: 'Lisa Rodriguez', email: '<EMAIL>', role: 'seo_specialist' },
      { id: '4', name: 'David Kim', email: '<EMAIL>', role: 'subject_expert' }
    ];

    // Mock assignments
    const mockAssignments: ReviewAssignment[] = [
      {
        id: 'rev_1',
        contentId: 'content_1',
        reviewerId: '1',
        assignedBy: 'system',
        assignedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        deadline: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(),
        priority: 'high',
        status: 'in_progress'
      },
      {
        id: 'rev_2',
        contentId: 'content_2',
        reviewerId: '2',
        assignedBy: 'system',
        assignedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        deadline: new Date(Date.now() + 20 * 60 * 60 * 1000).toISOString(),
        priority: 'medium',
        status: 'pending'
      },
      {
        id: 'rev_3',
        contentId: 'content_3',
        reviewerId: '3',
        assignedBy: 'system',
        assignedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        deadline: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
        priority: 'urgent',
        status: 'overdue'
      }
    ];

    // Mock content items
    const mockContent: ContentItem[] = [
      {
        id: 'content_1',
        title: 'How to Set Up a Home Office',
        type: 'how-to-guide',
        content: 'A comprehensive guide to setting up an effective home office...',
        status: 'pending_review',
        createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'content_2',
        title: '10 Best Productivity Apps for Entrepreneurs',
        type: 'listicle',
        content: 'Discover the top productivity apps that can help entrepreneurs...',
        status: 'pending_review',
        createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'content_3',
        title: 'MacBook Air M2 Review',
        type: 'product-review',
        content: 'An in-depth review of the Apple MacBook Air M2...',
        status: 'pending_review',
        createdAt: new Date(Date.now() - 7 * 60 * 60 * 1000).toISOString()
      }
    ];

    setReviewers(mockReviewers);
    setAssignments(mockAssignments);
    setContentItems(mockContent);
  };

  const getReviewerName = (reviewerId: string): string => {
    const reviewer = reviewers.find(r => r.id === reviewerId);
    return reviewer ? reviewer.name : 'Unknown Reviewer';
  };

  const getContentTitle = (contentId: string): string => {
    const content = contentItems.find(c => c.id === contentId);
    return content ? content.title : 'Unknown Content';
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'overdue': return 'text-red-600 bg-red-100';
      case 'in_progress': return 'text-blue-600 bg-blue-100';
      case 'escalated': return 'text-purple-600 bg-purple-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatTimeRemaining = (deadline: string): string => {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const diff = deadlineDate.getTime() - now.getTime();
    
    if (diff < 0) {
      const hours = Math.abs(Math.floor(diff / (1000 * 60 * 60)));
      return `${hours}h overdue`;
    }
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    if (hours < 24) {
      return `${hours}h remaining`;
    }
    
    const days = Math.floor(hours / 24);
    return `${days}d remaining`;
  };

  const assignReviewer = async (contentId: string, reviewerId: string) => {
    setLoading(true);
    try {
      // In a real implementation, this would call the API
      const newAssignment: ReviewAssignment = {
        id: `rev_${Date.now()}`,
        contentId,
        reviewerId,
        assignedBy: 'current_user',
        assignedAt: new Date().toISOString(),
        deadline: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        priority: 'medium',
        status: 'pending'
      };
      
      setAssignments(prev => [...prev, newAssignment]);
      setError('');
    } catch (err) {
      setError('Failed to assign reviewer');
    } finally {
      setLoading(false);
    }
  };

  const updateAssignmentStatus = async (assignmentId: string, status: string) => {
    setAssignments(prev => 
      prev.map(assignment => 
        assignment.id === assignmentId 
          ? { ...assignment, status: status as any, completedAt: status === 'completed' ? new Date().toISOString() : undefined }
          : assignment
      )
    );
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-8">Review Dashboard</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Review Assignments */}
        <div className="lg:col-span-2">
          <h2 className="text-xl font-semibold mb-4">Active Review Assignments</h2>
          
          {assignments.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              <p>No active review assignments</p>
            </div>
          ) : (
            <div className="space-y-4">
              {assignments.map(assignment => (
                <div 
                  key={assignment.id} 
                  className="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => setSelectedAssignment(assignment)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{getContentTitle(assignment.contentId)}</h4>
                    <div className="flex gap-2">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getPriorityColor(assignment.priority)}`}>
                        {assignment.priority}
                      </span>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(assignment.status)}`}>
                        {assignment.status.replace('_', ' ')}
                      </span>
                    </div>
                  </div>
                  
                  <div className="text-sm text-gray-600 mb-3">
                    <p>Reviewer: {getReviewerName(assignment.reviewerId)}</p>
                    <p>Assigned: {new Date(assignment.assignedAt).toLocaleString()}</p>
                    {assignment.deadline && (
                      <p className={assignment.status === 'overdue' ? 'text-red-600 font-medium' : ''}>
                        Deadline: {formatTimeRemaining(assignment.deadline)}
                      </p>
                    )}
                  </div>

                  {/* Quick Actions */}
                  <div className="flex gap-2">
                    {assignment.status === 'pending' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          updateAssignmentStatus(assignment.id, 'in_progress');
                        }}
                        className="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                      >
                        Start Review
                      </button>
                    )}
                    {assignment.status === 'in_progress' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          updateAssignmentStatus(assignment.id, 'completed');
                        }}
                        className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                      >
                        Complete Review
                      </button>
                    )}
                    {assignment.status === 'overdue' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          updateAssignmentStatus(assignment.id, 'escalated');
                        }}
                        className="text-xs bg-purple-600 text-white px-2 py-1 rounded hover:bg-purple-700"
                      >
                        Escalate
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Reviewer Panel */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Reviewers</h2>
          
          <div className="space-y-3">
            {reviewers.map(reviewer => {
              const reviewerAssignments = assignments.filter(a => a.reviewerId === reviewer.id);
              const pendingCount = reviewerAssignments.filter(a => a.status === 'pending' || a.status === 'in_progress').length;
              const overdueCount = reviewerAssignments.filter(a => a.status === 'overdue').length;
              
              return (
                <div key={reviewer.id} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{reviewer.name}</h4>
                    <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                      {reviewer.role.replace('_', ' ')}
                    </span>
                  </div>
                  
                  <div className="text-sm text-gray-600 mb-2">
                    <p>{reviewer.email}</p>
                  </div>

                  <div className="flex gap-2 text-xs">
                    <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded">
                      {pendingCount} pending
                    </span>
                    {overdueCount > 0 && (
                      <span className="bg-red-100 text-red-700 px-2 py-1 rounded">
                        {overdueCount} overdue
                      </span>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Content Awaiting Assignment */}
          <div className="mt-8">
            <h3 className="text-lg font-semibold mb-3">Assign Reviews</h3>
            
            {contentItems.filter(c => !assignments.some(a => a.contentId === c.id)).map(content => (
              <div key={content.id} className="border rounded-lg p-3 mb-3">
                <h4 className="font-medium mb-1">{content.title}</h4>
                <p className="text-sm text-gray-600 mb-2">{content.type}</p>
                
                <select
                  onChange={(e) => {
                    if (e.target.value) {
                      assignReviewer(content.id, e.target.value);
                      e.target.value = '';
                    }
                  }}
                  className="w-full text-xs px-2 py-1 border rounded"
                  disabled={loading}
                >
                  <option value="">Assign reviewer...</option>
                  {reviewers.map(reviewer => (
                    <option key={reviewer.id} value={reviewer.id}>
                      {reviewer.name} ({reviewer.role})
                    </option>
                  ))}
                </select>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Assignment Detail Modal */}
      {selectedAssignment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Review Assignment Details</h3>
              <button
                onClick={() => setSelectedAssignment(null)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Content</label>
                <p className="text-sm">{getContentTitle(selectedAssignment.contentId)}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Reviewer</label>
                <p className="text-sm">{getReviewerName(selectedAssignment.reviewerId)}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Status</label>
                <span className={`inline-block px-2 py-1 rounded text-xs font-medium ${getStatusColor(selectedAssignment.status)}`}>
                  {selectedAssignment.status.replace('_', ' ')}
                </span>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Priority</label>
                <span className={`inline-block px-2 py-1 rounded text-xs font-medium ${getPriorityColor(selectedAssignment.priority)}`}>
                  {selectedAssignment.priority}
                </span>
              </div>
              
              {selectedAssignment.deadline && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Deadline</label>
                  <p className="text-sm">{formatTimeRemaining(selectedAssignment.deadline)}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
