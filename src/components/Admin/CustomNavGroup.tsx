'use client'
import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

/**
 * Custom navigation component to add the automated research tool to the admin sidebar
 */
const CustomNavGroup: React.FC = () => {
  const pathname = usePathname()

  return (
    <div className="nav-group custom-nav-group">
      <div className="nav-group__label">Content Generation</div>
      <div className="nav-group__content">
        <Link
          href="/admin/auto-research"
          className={`nav__link ${pathname === '/admin/auto-research' ? 'nav__link--active' : ''}`}
          prefetch={false}
        >
          <span className="nav__link-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M14 3v4a1 1 0 0 0 1 1h4" />
              <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z" />
              <path d="M9 17h6" />
              <path d="M9 13h6" />
            </svg>
          </span>
          <span className="nav__link-label">Automated Research</span>
        </Link>
        <Link
          href="/admin/content-generation"
          className={`nav__link ${pathname === '/admin/content-generation' ? 'nav__link--active' : ''}`}
          prefetch={false}
        >
          <span className="nav__link-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M12 5v14" />
              <path d="M18 13l-6 6" />
              <path d="M6 13l6 6" />
              <path d="M18 11l-6-6" />
              <path d="M6 11l6-6" />
            </svg>
          </span>
          <span className="nav__link-label">AI Content Generation</span>
        </Link>
        <Link
          href="/admin/enhanced-collaboration"
          className={`nav__link ${pathname === '/admin/enhanced-collaboration' ? 'nav__link--active' : ''}`}
          prefetch={false}
        >
          <span className="nav__link-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </span>
          <span className="nav__link-label">Enhanced Collaboration</span>
        </Link>
        <Link
          href="/admin/iterative-collaboration"
          className={`nav__link ${pathname === '/admin/iterative-collaboration' ? 'nav__link--active' : ''}`}
          prefetch={false}
        >
          <span className="nav__link-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
              <path d="M3 3v5h5"></path>
              <path d="M12 7v5l4 2"></path>
            </svg>
          </span>
          <span className="nav__link-label">Iterative Collaboration</span>
        </Link>

        <Link
          href="/admin/collaborative-dashboard"
          className={`nav__link ${pathname === '/admin/collaborative-dashboard' ? 'nav__link--active' : ''}`}
          prefetch={false}
        >
          <span className="nav__link-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="3" y1="9" x2="21" y2="9"></line>
              <line x1="9" y1="21" x2="9" y2="9"></line>
            </svg>
          </span>
          <span className="nav__link-label">Collaborative Dashboard</span>
        </Link>

        <Link
          href="/dynamic-collaboration-v3"
          className={`nav__link ${pathname === '/dynamic-collaboration-v3' ? 'nav__link--active' : ''}`}
          prefetch={false}
        >
          <span className="nav__link-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
              <path d="M7 10h10"></path>
              <path d="M7 14h10"></path>
              <path d="M12 22V2"></path>
            </svg>
          </span>
          <span className="nav__link-label">Dynamic Collaboration V3</span>
        </Link>

        <Link
          href="/goal-based-collaboration"
          className={`nav__link ${pathname === '/goal-based-collaboration' ? 'nav__link--active' : ''}`}
          prefetch={false}
        >
          <span className="nav__link-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
              <path d="M2 17l10 5 10-5"></path>
              <path d="M2 12l10 5 10-5"></path>
            </svg>
          </span>
          <span className="nav__link-label">Goal-Based Collaboration</span>
        </Link>
      </div>

      <style jsx>{`
        .custom-nav-group {
          margin-bottom: 1rem;
        }

        .nav-group__label {
          text-transform: uppercase;
          font-size: 0.7rem;
          font-weight: 600;
          color: var(--theme-elevation-400);
          padding: 0 var(--base);
          margin-bottom: 0.25rem;
        }

        .nav-group__content {
          display: flex;
          flex-direction: column;
        }

        .nav__link {
          display: flex;
          align-items: center;
          padding: 0.5rem var(--base);
          color: var(--theme-elevation-800);
          text-decoration: none;
          border-radius: 4px;
          margin: 0 0.5rem 0.25rem 0.5rem;
        }

        .nav__link:hover {
          background-color: var(--theme-elevation-100);
        }

        .nav__link--active {
          background-color: var(--theme-elevation-100);
          color: var(--theme-text);
        }

        .nav__link-icon {
          margin-right: 0.5rem;
          display: flex;
          align-items: center;
          width: 24px;
          height: 24px;
          color: var(--theme-elevation-500);
        }

        .nav__link-label {
          font-size: 0.9rem;
        }
      `}</style>
    </div>
  )
}

export default CustomNavGroup