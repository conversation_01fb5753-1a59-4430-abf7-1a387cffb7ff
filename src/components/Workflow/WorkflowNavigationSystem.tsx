/**
 * Workflow Navigation System
 * 
 * Provides consistent navigation and status awareness across all workflow pages
 * Shows users where they are and how to get to the next step
 */

import React from 'react';
import { useRouter } from 'next/navigation';

export interface NavigationState {
  currentPage: 'unified' | 'execution' | 'review' | 'results';
  executionId?: string;
  reviewId?: string;
  workflowStatus?: 'running' | 'waiting_review' | 'completed' | 'failed';
  canNavigateBack?: boolean;
  canNavigateForward?: boolean;
  nextStepUrl?: string;
  nextStepLabel?: string;
}

interface Props {
  navigationState: NavigationState;
  workflowName?: string;
  showBreadcrumbs?: boolean;
  showStatusBadge?: boolean;
  showActionButtons?: boolean;
}

export default function WorkflowNavigationSystem({
  navigationState,
  workflowName = 'Workflow',
  showBreadcrumbs = true,
  showStatusBadge = true,
  showActionButtons = true
}: Props) {
  const router = useRouter();

  const getBreadcrumbs = () => {
    const breadcrumbs = [
      { label: 'Workflows', url: '/workflow', active: false },
      { label: workflowName, url: '/workflow/unified', active: navigationState.currentPage === 'unified' }
    ];

    if (navigationState.executionId) {
      breadcrumbs.push({
        label: 'Execution',
        url: `/workflow/execution/${navigationState.executionId}`,
        active: navigationState.currentPage === 'execution'
      });
    }

    if (navigationState.reviewId) {
      breadcrumbs.push({
        label: 'Review',
        url: `/review/${navigationState.reviewId}`,
        active: navigationState.currentPage === 'review'
      });
    }

    if (navigationState.currentPage === 'results' && navigationState.executionId) {
      breadcrumbs.push({
        label: 'Results',
        url: `/workflow/results/${navigationState.executionId}`,
        active: true
      });
    }

    return breadcrumbs;
  };

  const getStatusInfo = () => {
    switch (navigationState.workflowStatus) {
      case 'running':
        return {
          label: 'Running',
          color: 'bg-blue-100 text-blue-800',
          icon: '🔄',
          description: 'Workflow is currently executing'
        };
      case 'waiting_review':
        return {
          label: 'Awaiting Review',
          color: 'bg-yellow-100 text-yellow-800',
          icon: '⏸',
          description: 'Waiting for your review and approval'
        };
      case 'completed':
        return {
          label: 'Completed',
          color: 'bg-green-100 text-green-800',
          icon: '✅',
          description: 'Workflow completed successfully'
        };
      case 'failed':
        return {
          label: 'Failed',
          color: 'bg-red-100 text-red-800',
          icon: '❌',
          description: 'Workflow encountered an error'
        };
      default:
        return {
          label: 'Unknown',
          color: 'bg-gray-100 text-gray-800',
          icon: '❓',
          description: 'Status unknown'
        };
    }
  };

  const getPageTitle = () => {
    switch (navigationState.currentPage) {
      case 'unified':
        return 'Workflow Dashboard';
      case 'execution':
        return 'Workflow Execution';
      case 'review':
        return 'Content Review';
      case 'results':
        return 'Workflow Results';
      default:
        return 'Workflow';
    }
  };

  const getPageDescription = () => {
    switch (navigationState.currentPage) {
      case 'unified':
        return 'Start and monitor your AI-powered content workflows';
      case 'execution':
        return 'Real-time workflow execution with AI agent collaboration';
      case 'review':
        return 'Review AI-generated content and provide feedback';
      case 'results':
        return 'View final results and publish to your CMS';
      default:
        return 'AI-powered content workflow system';
    }
  };

  const handleNavigation = (url: string) => {
    router.push(url);
  };

  const statusInfo = getStatusInfo();
  const breadcrumbs = getBreadcrumbs();

  return (
    <div className="bg-white border-b border-gray-200 px-6 py-4">
      {/* Breadcrumbs */}
      {showBreadcrumbs && (
        <nav className="flex mb-4" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-2">
            {breadcrumbs.map((crumb, index) => (
              <li key={index} className="flex items-center">
                {index > 0 && (
                  <span className="text-gray-400 mx-2">/</span>
                )}
                <button
                  onClick={() => handleNavigation(crumb.url)}
                  className={`text-sm font-medium transition-colors ${
                    crumb.active
                      ? 'text-blue-600 cursor-default'
                      : 'text-gray-500 hover:text-gray-700 cursor-pointer'
                  }`}
                  disabled={crumb.active}
                >
                  {crumb.label}
                </button>
              </li>
            ))}
          </ol>
        </nav>
      )}

      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{getPageTitle()}</h1>
              <p className="text-sm text-gray-600 mt-1">{getPageDescription()}</p>
            </div>

            {/* Status Badge */}
            {showStatusBadge && navigationState.workflowStatus && (
              <div className="flex items-center space-x-2">
                <div
                  className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusInfo.color}`}
                  title={statusInfo.description}
                >
                  <span className="mr-1">{statusInfo.icon}</span>
                  {statusInfo.label}
                </div>
              </div>
            )}
          </div>

          {/* Execution ID */}
          {navigationState.executionId && (
            <div className="mt-2 text-xs text-gray-500">
              Execution ID: <span className="font-mono">{navigationState.executionId}</span>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        {showActionButtons && (
          <div className="flex items-center space-x-3">
            {/* Back Button */}
            {navigationState.canNavigateBack && (
              <button
                onClick={() => router.back()}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                ← Back
              </button>
            )}

            {/* Next Step Button */}
            {navigationState.nextStepUrl && navigationState.nextStepLabel && (
              <button
                onClick={() => handleNavigation(navigationState.nextStepUrl!)}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 transition-colors"
              >
                {navigationState.nextStepLabel} →
              </button>
            )}

            {/* Refresh Button */}
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              title="Refresh page"
            >
              🔄
            </button>

            {/* Dashboard Button */}
            <button
              onClick={() => handleNavigation('/workflow')}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Dashboard
            </button>
          </div>
        )}
      </div>

      {/* Context-Specific Alerts */}
      {navigationState.workflowStatus === 'waiting_review' && navigationState.currentPage !== 'review' && (
        <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <span className="text-yellow-600 mr-2">⏸</span>
              <div>
                <h4 className="text-sm font-medium text-yellow-800">Review Required</h4>
                <p className="text-sm text-yellow-700">Your content is ready for review and approval.</p>
              </div>
            </div>
            {navigationState.reviewId && (
              <button
                onClick={() => handleNavigation(`/review/${navigationState.reviewId}`)}
                className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors text-sm font-medium"
              >
                Review Now →
              </button>
            )}
          </div>
        </div>
      )}

      {navigationState.workflowStatus === 'completed' && navigationState.currentPage !== 'results' && (
        <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <span className="text-green-600 mr-2">✅</span>
              <div>
                <h4 className="text-sm font-medium text-green-800">Workflow Completed</h4>
                <p className="text-sm text-green-700">Your content is ready! View results and publish to your CMS.</p>
              </div>
            </div>
            {navigationState.executionId && (
              <button
                onClick={() => handleNavigation(`/workflow/results/${navigationState.executionId}`)}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm font-medium"
              >
                View Results →
              </button>
            )}
          </div>
        </div>
      )}

      {navigationState.workflowStatus === 'failed' && (
        <div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <span className="text-red-600 mr-2">❌</span>
              <div>
                <h4 className="text-sm font-medium text-red-800">Workflow Failed</h4>
                <p className="text-sm text-red-700">The workflow encountered an error. You can restart or contact support.</p>
              </div>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handleNavigation('/workflow/unified')}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm font-medium"
              >
                Restart Workflow
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Helper hook for managing navigation state
export function useWorkflowNavigation(
  currentPage: NavigationState['currentPage'],
  executionId?: string,
  reviewId?: string
) {
  const [workflowStatus, setWorkflowStatus] = React.useState<NavigationState['workflowStatus']>();
  const [nextStepUrl, setNextStepUrl] = React.useState<string>();
  const [nextStepLabel, setNextStepLabel] = React.useState<string>();

  // Poll for workflow status if we have an execution ID
  React.useEffect(() => {
    if (!executionId) return;

    const pollStatus = async () => {
      try {
        const response = await fetch(`/api/workflow/execution/${executionId}`);
        const data = await response.json();

        if (data.success && data.data) {
          setWorkflowStatus(data.data.status);

          // Set next step based on status
          if (data.data.status === 'waiting_review' && currentPage !== 'review') {
            const reviewStepData = data.data.steps?.find((s: any) => s.outputs?.review_url);
            const reviewUrl = reviewStepData?.outputs?.review_url || `/review/${executionId}`;
            setNextStepUrl(reviewUrl);
            setNextStepLabel('Review Content');
          } else if (data.data.status === 'completed' && currentPage !== 'results') {
            setNextStepUrl(`/workflow/results/${executionId}`);
            setNextStepLabel('View Results');
          }
        }
      } catch (error) {
        console.error('Navigation status polling error:', error);
      }
    };

    pollStatus();
    const interval = setInterval(pollStatus, 5000);
    return () => clearInterval(interval);
  }, [executionId, currentPage]);

  const navigationState: NavigationState = {
    currentPage,
    executionId,
    reviewId,
    workflowStatus,
    canNavigateBack: currentPage !== 'unified',
    canNavigateForward: !!nextStepUrl,
    nextStepUrl,
    nextStepLabel
  };

  return navigationState;
}
