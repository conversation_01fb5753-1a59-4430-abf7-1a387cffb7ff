/**
 * Approval Flow Explanation Component
 * Helps users understand how the approval workflow system works
 */

'use client';

import { useState } from 'react';

interface ApprovalFlowExplanationProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ApprovalFlowExplanation({ isOpen, onClose }: ApprovalFlowExplanationProps) {
  const [activeStep, setActiveStep] = useState(0);

  const steps = [
    {
      title: "1. Workflow Starts",
      description: "Your workflow begins executing steps sequentially",
      icon: "🚀",
      details: "The workflow engine processes each step in order, executing AI generation, data processing, and other automated tasks."
    },
    {
      title: "2. Reaches Approval Gate",
      description: "Workflow encounters a step that requires human approval",
      icon: "🚪",
      details: "When the workflow reaches an approval gate step, it creates an artifact (like generated content) and pauses execution."
    },
    {
      title: "3. Workflow Pauses",
      description: "Entire workflow execution stops and waits",
      icon: "⏸️",
      details: "The workflow engine pauses all execution. No further steps will be processed until the approval decision is made."
    },
    {
      title: "4. Artifact Created",
      description: "The system creates an artifact for review",
      icon: "📄",
      details: "An artifact containing the step's output (content, data, etc.) is created with 'Pending Approval' status."
    },
    {
      title: "5. User Reviews",
      description: "You review the artifact and make a decision",
      icon: "👀",
      details: "You can examine the artifact content, quality, and decide whether to approve or reject it."
    },
    {
      title: "6. Decision Made",
      description: "You approve or reject the artifact",
      icon: "✅❌",
      details: "Your decision determines what happens next: approval continues the workflow, rejection stops it."
    },
    {
      title: "7. Workflow Resumes/Stops",
      description: "Based on your decision, workflow continues or stops",
      icon: "▶️🛑",
      details: "If approved, the workflow resumes from the next step. If rejected, the workflow stops and marks the execution as failed."
    }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">How Approval Workflows Work</h2>
              <p className="text-blue-100 mt-1">Understanding the approval gate system</p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Step Navigation */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Workflow Steps</h3>
              <div className="text-sm text-gray-500">
                Step {activeStep + 1} of {steps.length}
              </div>
            </div>
            
            {/* Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((activeStep + 1) / steps.length) * 100}%` }}
              />
            </div>

            {/* Step Indicators */}
            <div className="flex justify-between">
              {steps.map((step, index) => (
                <button
                  key={index}
                  onClick={() => setActiveStep(index)}
                  className={`
                    flex flex-col items-center p-2 rounded-lg transition-all
                    ${index === activeStep 
                      ? 'bg-blue-100 text-blue-700' 
                      : index < activeStep 
                      ? 'bg-green-100 text-green-700' 
                      : 'bg-gray-100 text-gray-500'
                    }
                  `}
                >
                  <div className="text-2xl mb-1">{step.icon}</div>
                  <div className="text-xs text-center font-medium">{step.title}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Active Step Details */}
          <div className="bg-gray-50 rounded-lg p-6 mb-6">
            <div className="flex items-center mb-4">
              <div className="text-4xl mr-4">{steps[activeStep].icon}</div>
              <div>
                <h4 className="text-xl font-bold text-gray-900">{steps[activeStep].title}</h4>
                <p className="text-gray-600">{steps[activeStep].description}</p>
              </div>
            </div>
            <p className="text-gray-700 leading-relaxed">{steps[activeStep].details}</p>
          </div>

          {/* Navigation */}
          <div className="flex justify-between">
            <button
              onClick={() => setActiveStep(Math.max(0, activeStep - 1))}
              disabled={activeStep === 0}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              ← Previous
            </button>
            
            <button
              onClick={() => setActiveStep(Math.min(steps.length - 1, activeStep + 1))}
              disabled={activeStep === steps.length - 1}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next →
            </button>
          </div>

          {/* Key Points */}
          <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-semibold text-yellow-800 mb-2">🔑 Key Points to Remember:</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• The workflow completely pauses at approval gates</li>
              <li>• No further steps execute until you make a decision</li>
              <li>• Approval continues the workflow, rejection stops it</li>
              <li>• You can provide feedback with your decision</li>
              <li>• The visual workflow shows real-time status updates</li>
            </ul>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 rounded-b-lg">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Got it!
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
