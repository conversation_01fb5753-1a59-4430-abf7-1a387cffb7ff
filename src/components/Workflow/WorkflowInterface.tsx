/**
 * Unified Workflow Interface
 * Integrates template selection, input configuration, and execution
 */

'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface Template {
  id: string;
  name: string;
  description: string;
  sampleInputs: Record<string, any>;
  instructions: string;
  workflow: {
    metadata: {
      category: string;
      difficulty: string;
      estimatedTime: string;
    };
  };
}

interface WorkflowInterfaceProps {
  selectedTemplate?: string | null;
  onBack?: () => void;
  onWorkflowComplete?: (workflowId: string) => void;
  onWorkflowCreated?: (workflowId: string) => void;
  onReviewCreated?: (reviewId: string) => void;
}

export default function WorkflowInterface({
  selectedTemplate,
  onBack,
  onWorkflowComplete,
  onWorkflowCreated,
  onReviewCreated
}: WorkflowInterfaceProps) {
  const router = useRouter();
  const [templates, setTemplates] = useState<Template[]>([]);
  const [currentTemplate, setCurrentTemplate] = useState<Template | null>(null);
  const [inputs, setInputs] = useState<Record<string, any>>({});
  const [userApiKey, setUserApiKey] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [step, setStep] = useState<'template' | 'inputs' | 'execution' | 'results'>('template');
  const [executionId, setExecutionId] = useState<string | null>(null);
  const [executionStatus, setExecutionStatus] = useState<any>(null);

  useEffect(() => {
    loadTemplates();
  }, []);

  useEffect(() => {
    if (selectedTemplate && templates.length > 0) {
      const template = templates.find(t => t.id === selectedTemplate);
      if (template) {
        selectTemplate(template);
      }
    }
  }, [selectedTemplate, templates]);

  const loadTemplates = async () => {
    try {
      const response = await fetch('/api/workflow/create');
      const result = await response.json();

      if (result.success) {
        setTemplates(result.data.templates);
      } else {
        setError('Failed to load templates');
      }
    } catch (err) {
      setError('Failed to load templates');
      console.error(err);
    }
  };

  const selectTemplate = (template: Template) => {
    setCurrentTemplate(template);
    setInputs(template.sampleInputs);
    setError('');
    setStep('inputs');
  };

  const updateInput = (key: string, value: string) => {
    setInputs(prev => ({ ...prev, [key]: value }));
  };

  const executeWorkflow = async () => {
    if (!currentTemplate) return;

    setLoading(true);
    setError('');
    setStep('execution');

    try {
      const response = await fetch('/api/workflow/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          templateId: currentTemplate.id,
          inputs,
          userApiKey: userApiKey || undefined
        })
      });

      const result = await response.json();

      if (result.success) {
        setExecutionId(result.data.executionId);

        // Notify parent components with execution ID for visual workflow
        if (onWorkflowCreated) {
          onWorkflowCreated(result.data.executionId);
        }

        // Start polling for status
        checkExecutionStatus(result.data.executionId);
      } else {
        setError(result.error || 'Failed to execute workflow');
        setStep('inputs');
      }
    } catch (err) {
      setError('Failed to execute workflow');
      setStep('inputs');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const checkExecutionStatus = async (execId: string) => {
    try {
      const response = await fetch(`/api/workflow/create?executionId=${execId}`);
      const result = await response.json();

      if (result.success) {
        setExecutionStatus(result.data);
        
        if (result.data.execution.status === 'completed') {
          setStep('results');
          if (onWorkflowComplete) {
            onWorkflowComplete(execId);
          }
        } else if (result.data.execution.status === 'failed') {
          setError('Workflow execution failed');
          setStep('inputs');
        } else {
          // Continue polling
          setTimeout(() => checkExecutionStatus(execId), 2000);
        }
      }
    } catch (err) {
      console.error('Failed to check execution status:', err);
      setTimeout(() => checkExecutionStatus(execId), 5000);
    }
  };

  const getDifficultyColor = (difficulty: string | undefined) => {
    switch (difficulty) {
      case 'Easy': return 'bg-green-100 text-green-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderTemplateSelection = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Choose a Template</h2>
        <p className="text-gray-600">Select a template that matches your content goals</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {templates.map(template => (
          <div
            key={template.id}
            onClick={() => selectTemplate(template)}
            className="bg-white border border-gray-200 rounded-lg p-6 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer group"
          >
            <div className="flex items-start justify-between mb-3">
              <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-900">
                {template.name}
              </h3>
              <span className={`px-2 py-1 text-xs font-medium rounded ${getDifficultyColor(template.workflow?.metadata?.difficulty || 'Easy')}`}>
                {template.workflow?.metadata?.difficulty || 'Easy'}
              </span>
            </div>
            
            <p className="text-gray-600 mb-4 text-sm">{template.description}</p>
            
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-500">{template.workflow?.metadata?.category || 'General'}</span>
              <span className="text-gray-500">{template.workflow?.metadata?.estimatedTime || '10-15 minutes'}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderInputConfiguration = () => (
    <div className="max-w-2xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">{currentTemplate?.name}</h2>
          <p className="text-gray-600">{currentTemplate?.description}</p>
        </div>
        <button
          onClick={() => setStep('template')}
          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
        >
          ← Change Template
        </button>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-medium text-blue-900 mb-2">Instructions</h3>
        <p className="text-blue-800 text-sm">{currentTemplate?.instructions}</p>
      </div>

      <div className="space-y-4">
        {Object.entries(inputs).map(([key, value]) => (
          <div key={key}>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </label>
            {key.includes('description') || key.includes('instructions') ? (
              <textarea
                value={value}
                onChange={(e) => updateInput(key, e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                placeholder={`Enter ${key.replace(/_/g, ' ')}`}
              />
            ) : (
              <input
                type="text"
                value={value}
                onChange={(e) => updateInput(key, e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder={`Enter ${key.replace(/_/g, ' ')}`}
              />
            )}
          </div>
        ))}

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            API Key (Optional - BYOK)
          </label>
          <input
            type="password"
            value={userApiKey}
            onChange={(e) => setUserApiKey(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Your OpenAI API key (optional)"
          />
          <p className="text-xs text-gray-500 mt-1">
            Provide your own API key for cost control and privacy
          </p>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      )}

      <div className="flex justify-between">
        <button
          onClick={() => setStep('template')}
          className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
        >
          Back
        </button>
        <button
          onClick={executeWorkflow}
          disabled={loading}
          className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Starting...' : 'Start Workflow'}
        </button>
      </div>
    </div>
  );

  const renderExecution = () => (
    <div className="max-w-2xl mx-auto text-center space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Workflow Running</h2>
        <p className="text-gray-600">Your content is being generated...</p>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">
          {executionStatus ? 
            `Progress: ${Math.round(executionStatus.execution.progress || 0)}%` : 
            'Initializing workflow...'
          }
        </p>
      </div>

      {executionStatus && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-2">Current Status</h3>
          <p className="text-sm text-gray-600">
            Status: {executionStatus.execution.status}
          </p>
          {executionStatus.execution.currentStep && (
            <p className="text-sm text-gray-600">
              Current Step: {executionStatus.execution.currentStep}
            </p>
          )}
        </div>
      )}
    </div>
  );

  const renderResults = () => (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Workflow Complete!</h2>
        <p className="text-gray-600">Your content has been generated successfully</p>
      </div>

      <div className="flex justify-center space-x-4">
        <button
          onClick={() => router.push(`/workflow/results/${executionId}`)}
          className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          View Full Results
        </button>
        <button
          onClick={() => {
            setStep('template');
            setCurrentTemplate(null);
            setInputs({});
            setExecutionId(null);
            setExecutionStatus(null);
          }}
          className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
        >
          Create Another
        </button>
        {onBack && (
          <button
            onClick={onBack}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
          >
            Back to Dashboard
          </button>
        )}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {onBack && (
                <button
                  onClick={onBack}
                  className="text-gray-600 hover:text-gray-900"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
              )}
              <h1 className="text-xl font-semibold text-gray-900">Content Workflow</h1>
            </div>
            
            {/* Progress Steps */}
            <div className="flex items-center space-x-2">
              {['Template', 'Configure', 'Execute', 'Results'].map((stepName, index) => {
                const stepKeys = ['template', 'inputs', 'execution', 'results'];
                const currentStepIndex = stepKeys.indexOf(step);
                const isActive = index === currentStepIndex;
                const isCompleted = index < currentStepIndex;
                
                return (
                  <div key={stepName} className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      isActive ? 'bg-blue-600 text-white' :
                      isCompleted ? 'bg-green-600 text-white' :
                      'bg-gray-200 text-gray-600'
                    }`}>
                      {isCompleted ? '✓' : index + 1}
                    </div>
                    <span className={`ml-2 text-sm ${
                      isActive ? 'text-blue-600 font-medium' :
                      isCompleted ? 'text-green-600' :
                      'text-gray-500'
                    }`}>
                      {stepName}
                    </span>
                    {index < 3 && (
                      <svg className="w-4 h-4 mx-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {step === 'template' && renderTemplateSelection()}
        {step === 'inputs' && renderInputConfiguration()}
        {step === 'execution' && renderExecution()}
        {step === 'results' && renderResults()}
      </div>
    </div>
  );
}
