/**
 * Intuitive Flow Manager
 * 
 * Provides clear navigation and status updates throughout the workflow process
 * Shows users exactly what's happening and what to expect next
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export interface FlowStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'active' | 'completed' | 'waiting' | 'failed';
  url?: string;
  estimatedTime?: string;
  completedAt?: string;
}

export interface FlowState {
  executionId: string;
  currentStepIndex: number;
  steps: FlowStep[];
  overallStatus: 'running' | 'waiting_review' | 'completed' | 'failed';
  nextAction?: {
    type: 'review' | 'view_results' | 'restart';
    url: string;
    label: string;
    description: string;
  };
}

interface Props {
  executionId: string;
  workflowName: string;
  onFlowUpdate?: (flowState: FlowState) => void;
}

export default function IntuitiveFlowManager({ executionId, workflowName, onFlowUpdate }: Props) {
  const router = useRouter();
  const [flowState, setFlowState] = useState<FlowState | null>(null);
  const [isPolling, setIsPolling] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<string>('');

  // Initialize flow state
  useEffect(() => {
    const initialFlow: FlowState = {
      executionId,
      currentStepIndex: 0,
      overallStatus: 'running',
      steps: [
        {
          id: 'topic-input',
          name: 'Topic Input',
          description: 'Processing your topic and requirements',
          status: 'completed'
        },
        {
          id: 'keyword-research',
          name: 'AI Keyword Research',
          description: 'AI agents researching keywords and market insights',
          status: 'active',
          estimatedTime: '2-3 minutes'
        },
        {
          id: 'content-creation',
          name: 'Content Generation',
          description: 'Creating high-quality content with AI agent collaboration',
          status: 'pending',
          estimatedTime: '3-5 minutes'
        },
        {
          id: 'human-review',
          name: 'Human Review',
          description: 'Your review and approval required',
          status: 'pending',
          estimatedTime: 'Your time'
        },
        {
          id: 'final-results',
          name: 'Final Results',
          description: 'View and publish your completed content',
          status: 'pending'
        }
      ]
    };

    setFlowState(initialFlow);
    onFlowUpdate?.(initialFlow);
  }, [executionId, onFlowUpdate]);

  // Poll for workflow status updates
  useEffect(() => {
    if (!isPolling || !executionId) return;

    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/workflow/execution/${executionId}`);
        const data = await response.json();

        if (data.success && data.data) {
          updateFlowFromExecution(data.data);
          setLastUpdate(new Date().toLocaleTimeString());
        }
      } catch (error) {
        console.error('Flow polling error:', error);
      }
    }, 3000); // Poll every 3 seconds

    return () => clearInterval(pollInterval);
  }, [isPolling, executionId]);

  const updateFlowFromExecution = (execution: any) => {
    if (!flowState) return;

    const updatedSteps = [...flowState.steps];
    let currentStepIndex = flowState.currentStepIndex;
    let overallStatus = execution.status;
    let nextAction = undefined;

    // Update step statuses based on execution data
    execution.steps?.forEach((step: any) => {
      const flowStepIndex = updatedSteps.findIndex(fs => fs.id === step.id);
      if (flowStepIndex !== -1) {
        updatedSteps[flowStepIndex] = {
          ...updatedSteps[flowStepIndex],
          status: step.status === 'completed' ? 'completed' :
                  step.status === 'running' ? 'active' :
                  step.status === 'waiting_review' ? 'waiting' :
                  step.status === 'failed' ? 'failed' : 'pending',
          completedAt: step.status === 'completed' ? new Date().toISOString() : undefined
        };

        if (step.status === 'active' || step.status === 'running') {
          currentStepIndex = flowStepIndex;
        }
      }
    });

    // Handle special states
    if (execution.status === 'waiting_review') {
      const reviewStep = updatedSteps.find(s => s.id === 'human-review');
      if (reviewStep) {
        reviewStep.status = 'waiting';
        currentStepIndex = updatedSteps.indexOf(reviewStep);
        
        // Find review URL from execution steps
        const reviewStepData = execution.steps?.find((s: any) => s.outputs?.review_url);
        const reviewUrl = reviewStepData?.outputs?.review_url;
        
        nextAction = {
          type: 'review',
          url: reviewUrl || `/review/${execution.id}`,
          label: 'Review Content Now',
          description: 'Your content is ready for review. Click to approve or request changes.'
        };
      }
    } else if (execution.status === 'completed') {
      updatedSteps.forEach(step => {
        if (step.status !== 'failed') {
          step.status = 'completed';
        }
      });
      currentStepIndex = updatedSteps.length - 1;
      
      nextAction = {
        type: 'view_results',
        url: `/workflow/results/${execution.id}`,
        label: 'View Results & Publish',
        description: 'Your content is ready! View the final results and publish to your CMS.'
      };

      // Stop polling when completed
      setIsPolling(false);
    }

    const newFlowState: FlowState = {
      ...flowState,
      currentStepIndex,
      steps: updatedSteps,
      overallStatus,
      nextAction
    };

    setFlowState(newFlowState);
    onFlowUpdate?.(newFlowState);
  };

  const handleStepClick = (step: FlowStep, index: number) => {
    if (step.url) {
      router.push(step.url);
    } else if (step.status === 'waiting' && flowState?.nextAction) {
      router.push(flowState.nextAction.url);
    }
  };

  const handleNextAction = () => {
    if (flowState?.nextAction) {
      router.push(flowState.nextAction.url);
    }
  };

  if (!flowState) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Workflow Progress</h2>
          <p className="text-sm text-gray-600">{workflowName}</p>
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-500">Execution ID</div>
          <div className="text-xs font-mono text-gray-700">{executionId}</div>
          {lastUpdate && (
            <div className="text-xs text-gray-500 mt-1">Updated: {lastUpdate}</div>
          )}
        </div>
      </div>

      {/* Progress Steps */}
      <div className="space-y-4 mb-6">
        {flowState.steps.map((step, index) => (
          <div
            key={step.id}
            className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
              step.status === 'active' ? 'border-blue-500 bg-blue-50' :
              step.status === 'completed' ? 'border-green-500 bg-green-50' :
              step.status === 'waiting' ? 'border-yellow-500 bg-yellow-50' :
              step.status === 'failed' ? 'border-red-500 bg-red-50' :
              'border-gray-200 bg-gray-50'
            }`}
            onClick={() => handleStepClick(step, index)}
          >
            {/* Step Icon */}
            <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-4 ${
              step.status === 'active' ? 'bg-blue-500 text-white' :
              step.status === 'completed' ? 'bg-green-500 text-white' :
              step.status === 'waiting' ? 'bg-yellow-500 text-white' :
              step.status === 'failed' ? 'bg-red-500 text-white' :
              'bg-gray-300 text-gray-600'
            }`}>
              {step.status === 'completed' ? '✓' :
               step.status === 'active' ? '⟳' :
               step.status === 'waiting' ? '⏸' :
               step.status === 'failed' ? '✗' :
               index + 1}
            </div>

            {/* Step Content */}
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-gray-900">{step.name}</h3>
                <div className="flex items-center space-x-2 text-sm">
                  {step.status === 'active' && (
                    <span className="text-blue-600 font-medium">In Progress</span>
                  )}
                  {step.status === 'waiting' && (
                    <span className="text-yellow-600 font-medium">Action Required</span>
                  )}
                  {step.status === 'completed' && step.completedAt && (
                    <span className="text-green-600 font-medium">Completed</span>
                  )}
                  {step.estimatedTime && step.status !== 'completed' && (
                    <span className="text-gray-500">~{step.estimatedTime}</span>
                  )}
                </div>
              </div>
              <p className="text-sm text-gray-600 mt-1">{step.description}</p>
              
              {step.status === 'waiting' && (
                <div className="mt-2 text-sm text-yellow-700 font-medium">
                  👆 Click here to continue
                </div>
              )}
            </div>

            {/* Step Arrow */}
            {(step.status === 'waiting' || step.url) && (
              <div className="flex-shrink-0 ml-4">
                <span className="text-gray-400">→</span>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Next Action Button */}
      {flowState.nextAction && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-blue-900">{flowState.nextAction.label}</h4>
              <p className="text-sm text-blue-700 mt-1">{flowState.nextAction.description}</p>
            </div>
            <button
              onClick={handleNextAction}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              {flowState.nextAction.label} →
            </button>
          </div>
        </div>
      )}

      {/* Overall Status */}
      <div className="mt-4 text-center">
        <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
          flowState.overallStatus === 'running' ? 'bg-blue-100 text-blue-800' :
          flowState.overallStatus === 'waiting_review' ? 'bg-yellow-100 text-yellow-800' :
          flowState.overallStatus === 'completed' ? 'bg-green-100 text-green-800' :
          'bg-red-100 text-red-800'
        }`}>
          {flowState.overallStatus === 'running' && '🔄 Workflow Running'}
          {flowState.overallStatus === 'waiting_review' && '⏸ Waiting for Your Review'}
          {flowState.overallStatus === 'completed' && '✅ Workflow Completed'}
          {flowState.overallStatus === 'failed' && '❌ Workflow Failed'}
        </div>
      </div>
    </div>
  );
}
