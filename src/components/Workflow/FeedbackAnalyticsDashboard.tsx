/**
 * Feedback Analytics Dashboard
 * 
 * Displays feedback metrics and insights for improving the review process
 */

'use client';

import { useState, useEffect } from 'react';

interface FeedbackMetrics {
  totalFeedbackCount: number;
  rejectionRate: number;
  regenerationSuccessRate: number;
  averageRegenerationTime: number;
  commonFeedbackAreas: { area: string; count: number }[];
  feedbackQualityScore: number;
}

interface FeedbackPattern {
  area: string;
  keywords: string[];
  frequency: number;
  successRate: number;
  averageImprovementScore: number;
}

interface FeedbackAnalyticsDashboardProps {
  className?: string;
}

export default function FeedbackAnalyticsDashboard({ className = '' }: FeedbackAnalyticsDashboardProps) {
  const [metrics, setMetrics] = useState<FeedbackMetrics | null>(null);
  const [patterns, setPatterns] = useState<FeedbackPattern[]>([]);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadAnalytics();
  }, []);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      
      // In a real implementation, this would call an API endpoint
      // For now, we'll simulate some data
      const mockMetrics: FeedbackMetrics = {
        totalFeedbackCount: 45,
        rejectionRate: 0.32,
        regenerationSuccessRate: 0.78,
        averageRegenerationTime: 12000, // 12 seconds
        commonFeedbackAreas: [
          { area: 'clarity', count: 18 },
          { area: 'content quality', count: 15 },
          { area: 'accuracy', count: 12 },
          { area: 'structure', count: 8 },
          { area: 'style', count: 6 }
        ],
        feedbackQualityScore: 0.73
      };

      const mockPatterns: FeedbackPattern[] = [
        {
          area: 'clarity',
          keywords: ['unclear', 'confusing', 'clarify', 'explain'],
          frequency: 18,
          successRate: 0.85,
          averageImprovementScore: 0.82
        },
        {
          area: 'content quality',
          keywords: ['improve', 'better', 'quality', 'enhance'],
          frequency: 15,
          successRate: 0.73,
          averageImprovementScore: 0.76
        }
      ];

      const mockSuggestions = [
        'Be specific about what needs improvement',
        'Provide examples of desired changes',
        'Focus on clarity - it has an 85% success rate',
        'Use constructive language for better outcomes'
      ];

      setMetrics(mockMetrics);
      setPatterns(mockPatterns);
      setSuggestions(mockSuggestions);
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (milliseconds: number) => {
    const seconds = Math.round(milliseconds / 1000);
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const formatPercentage = (value: number) => {
    return `${Math.round(value * 100)}%`;
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        <p className="text-gray-500">No analytics data available</p>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border ${className}`}>
      <div className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">
          Feedback Analytics Dashboard
        </h3>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-blue-600">
              {metrics.totalFeedbackCount}
            </div>
            <div className="text-sm text-blue-800">Total Feedback</div>
          </div>

          <div className="bg-yellow-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-yellow-600">
              {formatPercentage(metrics.rejectionRate)}
            </div>
            <div className="text-sm text-yellow-800">Rejection Rate</div>
          </div>

          <div className="bg-green-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-green-600">
              {formatPercentage(metrics.regenerationSuccessRate)}
            </div>
            <div className="text-sm text-green-800">Success Rate</div>
          </div>

          <div className="bg-purple-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-purple-600">
              {formatTime(metrics.averageRegenerationTime)}
            </div>
            <div className="text-sm text-purple-800">Avg. Regen Time</div>
          </div>
        </div>

        {/* Common Feedback Areas */}
        <div className="mb-8">
          <h4 className="text-md font-medium text-gray-900 mb-4">
            Most Common Feedback Areas
          </h4>
          <div className="space-y-2">
            {metrics.commonFeedbackAreas.map((area, index) => (
              <div key={area.area} className="flex items-center justify-between">
                <span className="text-sm text-gray-700 capitalize">{area.area}</span>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full"
                      style={{
                        width: `${(area.count / metrics.commonFeedbackAreas[0].count) * 100}%`
                      }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-500 w-8">{area.count}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Feedback Quality Score */}
        <div className="mb-8">
          <h4 className="text-md font-medium text-gray-900 mb-4">
            Feedback Quality Score
          </h4>
          <div className="flex items-center space-x-4">
            <div className="flex-1 bg-gray-200 rounded-full h-3">
              <div
                className={`h-3 rounded-full ${
                  metrics.feedbackQualityScore >= 0.8
                    ? 'bg-green-500'
                    : metrics.feedbackQualityScore >= 0.6
                    ? 'bg-yellow-500'
                    : 'bg-red-500'
                }`}
                style={{ width: `${metrics.feedbackQualityScore * 100}%` }}
              ></div>
            </div>
            <span className="text-sm font-medium">
              {formatPercentage(metrics.feedbackQualityScore)}
            </span>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Based on specificity, actionability, and constructiveness
          </p>
        </div>

        {/* Improvement Suggestions */}
        <div>
          <h4 className="text-md font-medium text-gray-900 mb-4">
            Suggestions for Better Feedback
          </h4>
          <div className="space-y-2">
            {suggestions.map((suggestion, index) => (
              <div key={index} className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">💡</span>
                <span className="text-sm text-gray-700">{suggestion}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
