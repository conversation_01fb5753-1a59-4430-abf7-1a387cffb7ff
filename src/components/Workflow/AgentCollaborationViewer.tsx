/**
 * Agent Collaboration Viewer Component
 * 
 * Real-time visualization of agent collaboration sessions with human intervention capabilities
 */

import React, { useState, useEffect } from 'react';
import { CollaborationResult, CollaborationRound } from '../../core/agents/AgentCollaborationEngine';

export interface HumanInteraction {
  type: 'add-requirement' | 'provide-feedback' | 'resolve-conflict' | 'round-select';
  roundNumber?: number;
  data?: any;
}

export interface AgentCollaborationViewerProps {
  collaborationResult: CollaborationResult;
  activeRound?: number;
  isLive?: boolean;
  onInteraction?: (interaction: HumanInteraction) => void;
  onRoundSelect?: (interaction: HumanInteraction) => void;
}

const AGENT_NAMES: Record<string, string> = {
  'seo-keyword': 'SEO Keyword Agent',
  'market-research': 'Market Research Agent',
  'content-strategy': 'Content Strategy Agent',
  'technical-writer': 'Technical Writer Agent',
  'tech-specialist': 'Tech Specialist Agent'
};

const AGENT_ICONS: Record<string, string> = {
  'seo-keyword': '🔍',
  'market-research': '📊',
  'content-strategy': '📝',
  'technical-writer': '✍️',
  'tech-specialist': '⚡'
};

export const AgentCollaborationViewer: React.FC<AgentCollaborationViewerProps> = ({
  collaborationResult,
  activeRound = 0,
  isLive = false,
  onInteraction,
  onRoundSelect
}) => {
  const [selectedRound, setSelectedRound] = useState(activeRound);
  const [isExpanded, setIsExpanded] = useState(true);

  useEffect(() => {
    setSelectedRound(activeRound);
  }, [activeRound]);

  const handleRoundSelect = (roundIndex: number) => {
    setSelectedRound(roundIndex);
    if (onRoundSelect) {
      onRoundSelect({ type: 'round-select', roundNumber: collaborationResult.rounds[roundIndex]?.number || roundIndex + 1 });
    }
  };

  const getAgentName = (agentId: string): string => {
    return AGENT_NAMES[agentId] || agentId;
  };

  const getAgentIcon = (agentId: string): string => {
    return AGENT_ICONS[agentId] || '🤖';
  };

  const formatConfidence = (confidence: number): string => {
    return `${Math.round(confidence * 100)}%`;
  };

  const currentRound = collaborationResult.rounds[selectedRound];

  return (
    <div className="agent-collaboration-viewer bg-white rounded-lg shadow-lg p-6 max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <h3 className="text-xl font-semibold text-gray-800">
            🤝 Agent Collaboration Progress
          </h3>
          {isLive && (
            <div className="flex items-center space-x-2">
              <div 
                className="w-3 h-3 bg-green-500 rounded-full animate-pulse"
                data-testid="live-indicator"
              />
              <span 
                className="text-sm text-green-600 font-medium"
                data-testid="collaboration-status"
              >
                Active
              </span>
            </div>
          )}
        </div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-gray-500 hover:text-gray-700"
        >
          {isExpanded ? '📖' : '📕'}
        </button>
      </div>

      {isExpanded && (
        <>
          {/* Collaboration Timeline */}
          <div className="collaboration-timeline mb-6">
            <div className="flex space-x-4 overflow-x-auto pb-4">
              {collaborationResult.rounds.map((round, index) => (
                <div
                  key={index}
                  data-testid={`round-${round.number}`}
                  className={`round-item flex-shrink-0 p-4 rounded-lg border-2 cursor-pointer transition-all ${
                    selectedRound === index
                      ? 'border-blue-500 bg-blue-50 active'
                      : 'border-gray-200 bg-gray-50 hover:border-gray-300'
                  }`}
                  onClick={() => handleRoundSelect(index)}
                >
                  <div className="round-header mb-2">
                    <span className="block text-sm font-medium text-gray-700">
                      Round {round.number}
                    </span>
                    <span className="text-xs text-gray-500">
                      {round.agentInputs.size} agents
                    </span>
                  </div>
                  <div 
                    className="agent-participation flex space-x-1"
                    data-testid={`round-${round.number}-participants`}
                  >
                    {Array.from(round.agentInputs.keys()).map(agentId => (
                      <div
                        key={agentId}
                        className="agent-avatar w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-xs"
                        title={getAgentName(agentId)}
                        data-testid={`agent-icon-${agentId}`}
                      >
                        {getAgentIcon(agentId)}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Agent Conversation View */}
          <div className="agent-conversation bg-gray-50 rounded-lg p-4 mb-6">
            <h4 className="text-lg font-medium text-gray-800 mb-4">
              {currentRound ? `Round ${currentRound.number} Collaboration` : 'Collaboration Details'}
            </h4>

            {currentRound ? (
              <div className="space-y-4">
                {Array.from(currentRound.agentInputs.entries()).map(([agentId, input]) => (
                  <div key={agentId} className="agent-message bg-white rounded-lg p-4 border border-gray-200">
                    <div className="message-header flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{getAgentIcon(agentId)}</span>
                        <span className="font-medium text-gray-800">
                          {getAgentName(agentId)}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">Confidence:</span>
                        <span className="text-sm font-medium text-blue-600">
                          {formatConfidence(input.confidence)}
                        </span>
                      </div>
                    </div>
                    
                    <div className="message-content space-y-3">
                      <div>
                        <h5 className="text-sm font-medium text-gray-700 mb-1">Reasoning:</h5>
                        <p className="text-sm text-gray-600">{input.reasoning}</p>
                      </div>
                      
                      {input.suggestions.length > 0 && (
                        <div>
                          <h5 className="text-sm font-medium text-gray-700 mb-1">Suggestions:</h5>
                          <ul className="text-sm text-gray-600 space-y-1">
                            {input.suggestions.map((suggestion, idx) => (
                              <li key={idx} className="flex items-start space-x-2">
                                <span className="text-blue-500 mt-1">•</span>
                                <span>{suggestion}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                Select a round to view collaboration details
              </div>
            )}
          </div>

          {/* Consensus Summary */}
          <div className="consensus-summary bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 mb-6">
            <h4 className="text-lg font-medium text-gray-800 mb-3">Collaboration Summary</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  Consensus: {formatConfidence(collaborationResult.consensus.confidence)}
                </div>
                <div className="text-sm text-gray-600">Agreement Level</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  Quality Score: {Math.round(collaborationResult.consensus.qualityScore)}
                </div>
                <div className="text-sm text-gray-600">Overall Quality</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {collaborationResult.rounds.length}
                </div>
                <div className="text-sm text-gray-600">Collaboration Rounds</div>
              </div>
            </div>

            {/* Agreements */}
            {collaborationResult.consensus.agreements.length > 0 && (
              <div className="mb-4">
                <h5 className="text-sm font-medium text-gray-700 mb-2">Agreements</h5>
                <div className="space-y-1">
                  {collaborationResult.consensus.agreements.map((agreement, idx) => (
                    <div key={idx} className="flex items-start space-x-2 text-sm">
                      <span className="text-green-500 mt-1">✓</span>
                      <span className="text-gray-600">{agreement}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Final Recommendations */}
            {collaborationResult.consensus.finalRecommendations.length > 0 && (
              <div>
                <h5 className="text-sm font-medium text-gray-700 mb-2">Final Recommendations</h5>
                <div className="space-y-1">
                  {collaborationResult.consensus.finalRecommendations.map((recommendation, idx) => (
                    <div key={idx} className="flex items-start space-x-2 text-sm">
                      <span className="text-blue-500 mt-1">→</span>
                      <span className="text-gray-600">{recommendation}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Human Intervention Panel */}
          {onInteraction && (
            <div className="human-intervention bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="text-lg font-medium text-gray-800 mb-3">💬 Join the Collaboration</h4>
              <div className="intervention-options flex flex-wrap gap-2">
                <button
                  onClick={() => onInteraction({ type: 'add-requirement' })}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm"
                >
                  Add Requirement
                </button>
                <button
                  onClick={() => onInteraction({ type: 'provide-feedback' })}
                  className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors text-sm"
                >
                  Provide Feedback
                </button>
                <button
                  onClick={() => onInteraction({ type: 'resolve-conflict' })}
                  className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors text-sm"
                >
                  Resolve Conflict
                </button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};
