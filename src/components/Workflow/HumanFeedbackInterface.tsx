/**
 * Human Feedback Interface Component
 * 
 * Handles human feedback collection, artifact versioning, and agent collaboration
 */

'use client';

import { useState, useEffect } from 'react';

interface Artifact {
  id: string;
  type: string;
  content: any;
  version: string;
  previousVersionId?: string;
  metadata: {
    title: string;
    description: string;
    createdAt: string;
    updatedAt: string;
    qualityScore?: number;
    wordCount?: number;
    readabilityScore?: number;
  };
  feedback?: {
    id: string;
    content: string;
    timestamp: string;
    userId: string;
    processed: boolean;
    agentConsultations?: Array<{
      agentId: string;
      suggestions: any[];
      confidence: number;
    }>;
  }[];
  status: 'draft' | 'pending_review' | 'approved' | 'rejected' | 'regenerating';
}

interface Props {
  workflowExecutionId: string;
  stepId: string;
  artifact: Artifact | null;
  onFeedbackSubmit: (feedback: string) => void;
  onArtifactApprove: (artifactId: string) => void;
  onArtifactReject: (artifactId: string, feedback: string) => void;
  onNotification: (message: string) => void;
}

export default function HumanFeedbackInterface({
  workflowExecutionId,
  stepId,
  artifact,
  onFeedbackSubmit,
  onArtifactApprove,
  onArtifactReject,
  onNotification
}: Props) {
  const [feedback, setFeedback] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [artifactVersions, setArtifactVersions] = useState<Artifact[]>([]);
  const [selectedVersion, setSelectedVersion] = useState<string | null>(null);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [agentCollaborationResults, setAgentCollaborationResults] = useState<any[]>([]);
  const [isRegenerating, setIsRegenerating] = useState(false);

  useEffect(() => {
    if (artifact) {
      loadArtifactVersions(artifact.id);
      setSelectedVersion(artifact.id);
    }
  }, [artifact]);

  const loadArtifactVersions = async (artifactId: string) => {
    try {
      const response = await fetch(`/api/artifacts/${artifactId}/versions`);
      const result = await response.json();
      
      if (result.success) {
        setArtifactVersions(result.data.versions);
      }
    } catch (error) {
      console.error('Failed to load artifact versions:', error);
      onNotification('Failed to load artifact versions');
    }
  };

  const handleFeedbackSubmit = async () => {
    if (!feedback.trim() || !artifact) return;

    try {
      setIsSubmitting(true);

      // Submit feedback
      const feedbackResponse = await fetch('/api/artifacts/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          artifactId: artifact.id,
          workflowExecutionId,
          stepId,
          feedback: feedback.trim(),
          userId: 'current-user' // In real app, get from auth
        })
      });

      const feedbackResult = await feedbackResponse.json();
      
      if (feedbackResult.success) {
        onFeedbackSubmit(feedback);
        
        // Trigger agent collaboration on feedback
        await triggerAgentCollaboration(artifact.id, feedback);
        
        setFeedback('');
        onNotification('Feedback submitted and agents consulted');
      } else {
        onNotification(`Failed to submit feedback: ${feedbackResult.error}`);
      }
    } catch (error) {
      console.error('Failed to submit feedback:', error);
      onNotification('Failed to submit feedback');
    } finally {
      setIsSubmitting(false);
    }
  };

  const triggerAgentCollaboration = async (artifactId: string, feedbackContent: string) => {
    try {
      const response = await fetch('/api/agents/collaboration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          artifactId,
          feedback: feedbackContent,
          workflowExecutionId,
          stepId,
          collaborationType: 'feedback_analysis'
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setAgentCollaborationResults(result.data.consultations);
        onNotification(`${result.data.consultations.length} agents consulted on feedback`);
      }
    } catch (error) {
      console.error('Failed to trigger agent collaboration:', error);
    }
  };

  const handleRegenerateArtifact = async () => {
    if (!artifact) return;

    try {
      setIsRegenerating(true);
      
      const response = await fetch('/api/artifacts/regenerate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          artifactId: artifact.id,
          workflowExecutionId,
          stepId,
          agentSuggestions: agentCollaborationResults,
          feedback: artifact.feedback?.map(f => f.content).join('\n')
        })
      });

      const result = await response.json();
      
      if (result.success) {
        onNotification('Artifact regeneration started with agent collaboration');
        // Reload versions to show new version
        await loadArtifactVersions(artifact.id);
      } else {
        onNotification(`Failed to regenerate artifact: ${result.error}`);
      }
    } catch (error) {
      console.error('Failed to regenerate artifact:', error);
      onNotification('Failed to regenerate artifact');
    } finally {
      setIsRegenerating(false);
    }
  };

  const handleApprove = () => {
    if (!artifact) return;
    onArtifactApprove(artifact.id);
    onNotification('Artifact approved');
  };

  const handleReject = () => {
    if (!artifact || !feedback.trim()) {
      onNotification('Please provide feedback before rejecting');
      return;
    }
    
    onArtifactReject(artifact.id, feedback);
    setFeedback('');
    onNotification('Artifact rejected with feedback');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'pending_review': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'regenerating': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatContent = (content: any, type: string) => {
    if (type === 'text' || type === 'blog-post') {
      return content.content || content;
    }
    return JSON.stringify(content, null, 2);
  };

  if (!artifact) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="text-center py-8">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Artifact Available</h3>
          <p className="text-gray-600">
            An artifact will appear here when the workflow generates content for review.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Artifact Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">{artifact.metadata.title}</h2>
            <p className="text-sm text-gray-600">{artifact.metadata.description}</p>
          </div>
          
          <div className="flex items-center space-x-3">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(artifact.status)}`}>
              {artifact.status.replace('_', ' ')}
            </span>
            <span className="text-sm text-gray-500">v{artifact.version}</span>
          </div>
        </div>

        {/* Artifact Metadata */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-semibold text-gray-900">{artifact.metadata.wordCount || 'N/A'}</div>
            <div className="text-sm text-gray-600">Words</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-semibold text-gray-900">
              {artifact.metadata.qualityScore ? Math.round(artifact.metadata.qualityScore * 100) + '%' : 'N/A'}
            </div>
            <div className="text-sm text-gray-600">Quality Score</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-semibold text-gray-900">
              {artifact.metadata.readabilityScore ? Math.round(artifact.metadata.readabilityScore * 100) + '%' : 'N/A'}
            </div>
            <div className="text-sm text-gray-600">Readability</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-semibold text-gray-900">{artifactVersions.length}</div>
            <div className="text-sm text-gray-600">Versions</div>
          </div>
        </div>

        {/* Version History Toggle */}
        <div className="flex items-center justify-between">
          <button
            onClick={() => setShowVersionHistory(!showVersionHistory)}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            {showVersionHistory ? 'Hide' : 'Show'} Version History ({artifactVersions.length})
          </button>
          
          {agentCollaborationResults.length > 0 && (
            <button
              onClick={handleRegenerateArtifact}
              disabled={isRegenerating}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 transition-colors"
            >
              {isRegenerating ? 'Regenerating...' : '🤖 Regenerate with Agent Insights'}
            </button>
          )}
        </div>

        {/* Version History */}
        {showVersionHistory && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <h4 className="font-medium text-gray-900 mb-3">Version History</h4>
            <div className="space-y-2">
              {artifactVersions.map((version) => (
                <div
                  key={version.id}
                  className={`flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedVersion === version.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedVersion(version.id)}
                >
                  <div>
                    <div className="font-medium text-gray-900">Version {version.version}</div>
                    <div className="text-sm text-gray-500">
                      {new Date(version.metadata.updatedAt).toLocaleString()}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(version.status)}`}>
                      {version.status}
                    </span>
                    {version.feedback && version.feedback.length > 0 && (
                      <span className="text-xs text-gray-500">
                        {version.feedback.length} feedback
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Artifact Content */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Artifact Content</h3>
        <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
          <pre className="whitespace-pre-wrap text-sm text-gray-800">
            {formatContent(artifact.content, artifact.type)}
          </pre>
        </div>
      </div>

      {/* Agent Collaboration Results */}
      {agentCollaborationResults.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Agent Collaboration Results ({agentCollaborationResults.length})
          </h3>
          <div className="space-y-4">
            {agentCollaborationResults.map((result, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{result.agentId}</h4>
                  <span className="text-sm text-gray-600">
                    {Math.round(result.confidence * 100)}% confidence
                  </span>
                </div>
                <div className="space-y-2">
                  {result.suggestions.map((suggestion: any, suggestionIndex: number) => (
                    <div key={suggestionIndex} className="bg-gray-50 rounded p-3">
                      <div className="font-medium text-sm text-gray-900">{suggestion.area}</div>
                      <div className="text-sm text-gray-600">{suggestion.suggestion}</div>
                      <div className="text-xs text-gray-500 mt-1">Priority: {suggestion.priority}</div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Feedback Interface */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Provide Feedback</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Your Feedback
            </label>
            <textarea
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              placeholder="Provide specific feedback on what needs to be improved..."
            />
          </div>

          {/* Previous Feedback */}
          {artifact.feedback && artifact.feedback.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Previous Feedback</h4>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {artifact.feedback.map((fb) => (
                  <div key={fb.id} className="bg-gray-50 rounded p-3">
                    <div className="text-sm text-gray-800">{fb.content}</div>
                    <div className="text-xs text-gray-500 mt-1">
                      {new Date(fb.timestamp).toLocaleString()}
                      {fb.processed && ' • Processed by agents'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <button
              onClick={handleFeedbackSubmit}
              disabled={!feedback.trim() || isSubmitting}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {isSubmitting ? 'Submitting...' : '💬 Submit Feedback & Consult Agents'}
            </button>

            <div className="flex items-center space-x-3">
              <button
                onClick={handleReject}
                disabled={!feedback.trim()}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors"
              >
                ❌ Reject
              </button>
              
              <button
                onClick={handleApprove}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                ✅ Approve
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
