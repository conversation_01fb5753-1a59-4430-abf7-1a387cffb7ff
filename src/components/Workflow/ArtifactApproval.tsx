/**
 * Artifact Approval Component
 * Handle approval/rejection of workflow artifacts
 */

'use client';

import { useState, useEffect } from 'react';
import { userManager } from '../../utils/user-manager';
import RegenerationStatus from '../workflow/RegenerationStatus';

interface Artifact {
  id: string;
  stepId: string;
  executionId: string;
  type: string;
  title: string;
  content: any;
  status: string;
  version: number;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

interface ApprovalStatus {
  artifactId: string;
  status: string;
  approvals: any[];
  requiredApprovals: number;
  pendingApprovers: string[];
  canProceed: boolean;
  escalated: boolean;
  escalationLevel: number;
}

interface ArtifactApprovalProps {
  artifactId: string;
  onApprovalComplete?: (artifactId: string, approved: boolean) => void;
  currentUser?: string;
}

export default function ArtifactApproval({
  artifactId,
  onApprovalComplete,
  currentUser
}: ArtifactApprovalProps) {
  const effectiveUser = currentUser || userManager.getCurrentUserId();
  const [artifact, setArtifact] = useState<Artifact | null>(null);
  const [approvalStatus, setApprovalStatus] = useState<ApprovalStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [feedback, setFeedback] = useState('');
  const [decision, setDecision] = useState<'approve' | 'reject' | ''>('');
  const [regenerationRequest, setRegenerationRequest] = useState<any>(null);
  const [showFeedbackTemplates, setShowFeedbackTemplates] = useState(false);

  useEffect(() => {
    loadArtifactData();
  }, [artifactId]);

  const loadArtifactData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/workflow/approval?artifactId=${artifactId}`);
      const result = await response.json();

      if (result.success) {
        setArtifact(result.data.artifact);
        setApprovalStatus(result.data.approvalStatus);
      } else {
        setError(result.error || 'Failed to load artifact data');
      }
    } catch (err) {
      setError('Failed to load artifact data');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const feedbackTemplates = [
    "The content needs more specific examples and details.",
    "Please improve the clarity and readability of this content.",
    "The tone doesn't match our target audience. Please adjust.",
    "Add more data and statistics to support the claims.",
    "The structure needs improvement for better flow.",
    "Please fact-check and verify all information provided."
  ];

  const handleTemplateSelect = (template: string) => {
    setFeedback(template);
    setShowFeedbackTemplates(false);
  };

  const submitApproval = async () => {
    if (!decision) {
      setError('Please select approve or reject');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      const response = await fetch('/api/workflow/approval', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          artifactId,
          approved: decision === 'approve',
          approver: effectiveUser,
          feedback,
          reason: decision === 'reject' ? feedback : undefined
        })
      });

      const result = await response.json();

      if (result.success) {
        setApprovalStatus(result.data.approvalStatus);

        // Check if regeneration was triggered
        if (result.data.regenerationRequest) {
          setRegenerationRequest(result.data.regenerationRequest);
        }

        if (onApprovalComplete) {
          onApprovalComplete(artifactId, decision === 'approve');
        }

        // Reset form
        setDecision('');
        setFeedback('');
      } else {
        setError(result.error || 'Failed to submit approval');
      }
    } catch (err) {
      setError('Failed to submit approval');
      console.error(err);
    } finally {
      setSubmitting(false);
    }
  };

  const renderArtifactContent = () => {
    if (!artifact) return null;

    // Handle different artifact types
    switch (artifact.type) {
      case 'keyword_research':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Keyword Research Results</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <pre className="whitespace-pre-wrap text-sm">
                {typeof artifact.content === 'string' 
                  ? artifact.content 
                  : JSON.stringify(artifact.content, null, 2)
                }
              </pre>
            </div>
          </div>
        );
      
      case 'content_draft':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Content Draft</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="prose max-w-none">
                {typeof artifact.content === 'string' 
                  ? <div dangerouslySetInnerHTML={{ __html: artifact.content }} />
                  : <pre className="whitespace-pre-wrap text-sm">
                      {JSON.stringify(artifact.content, null, 2)}
                    </pre>
                }
              </div>
            </div>
          </div>
        );
      
      default:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{artifact.title}</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <pre className="whitespace-pre-wrap text-sm">
                {typeof artifact.content === 'string' 
                  ? artifact.content 
                  : JSON.stringify(artifact.content, null, 2)
                }
              </pre>
            </div>
          </div>
        );
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading artifact...</span>
      </div>
    );
  }

  if (!artifact) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-600">Artifact not found</p>
      </div>
    );
  }

  const isAlreadyProcessed = approvalStatus?.status === 'approved' || approvalStatus?.status === 'rejected';
  // Force show buttons for testing - ignore all logic
  const canApprove = true;

  // Debug logging
  console.log('Approval Debug:', {
    artifactId,
    currentUser: effectiveUser,
    approvalStatus,
    isAlreadyProcessed,
    canApprove,
    pendingApprovers: approvalStatus?.pendingApprovers
  });

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                🔍 Artifact Approval Required
              </h1>
              <p className="text-gray-600 mt-1">
                The workflow is paused and waiting for your approval to continue
              </p>
              {artifact && (
                <div className="mt-2 text-sm text-gray-500">
                  <span className="font-medium">Execution:</span> {artifact.executionId} •
                  <span className="font-medium ml-2">Step:</span> {artifact.stepId}
                </div>
              )}
            </div>
            <div className="flex flex-col items-end space-y-2">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                approvalStatus?.status === 'approved' ? 'bg-green-100 text-green-800' :
                approvalStatus?.status === 'rejected' ? 'bg-red-100 text-red-800' :
                'bg-yellow-100 text-yellow-800'
              }`}>
                {approvalStatus?.status === 'pending_approval' ? 'Waiting for Approval' :
                 approvalStatus?.status || 'pending'}
              </span>
              {approvalStatus?.status === 'pending_approval' && (
                <div className="text-xs text-gray-500 text-right">
                  ⏸️ Workflow execution paused
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {/* Regeneration Status */}
      {regenerationRequest && (
        <RegenerationStatus
          artifactId={artifactId}
          regenerationRequest={regenerationRequest}
          onRegenerationComplete={(newArtifactId) => {
            // Optionally navigate to the new artifact or refresh
            console.log('Regeneration completed:', newArtifactId);
          }}
        />
      )}

      {/* Artifact Details */}
      <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
        <div className="grid grid-cols-2 gap-4 mb-6 text-sm">
          <div>
            <span className="font-medium text-gray-700">Type:</span>
            <span className="ml-2 text-gray-900">{artifact?.type || 'Unknown'}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Created:</span>
            <span className="ml-2 text-gray-900">{artifact?.createdAt ? new Date(artifact.createdAt).toLocaleString() : 'Unknown'}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Version:</span>
            <span className="ml-2 text-gray-900">{artifact?.version || 'Unknown'}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Created by:</span>
            <span className="ml-2 text-gray-900">{artifact?.createdBy || 'Unknown'}</span>
          </div>
        </div>

        {/* Artifact Content */}
        {renderArtifactContent()}
      </div>

      {/* Approval Actions - Always show for testing */}
      {true && (
        <div className="bg-white rounded-lg shadow-lg border-2 border-yellow-200 p-6">
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-3">
              <span className="text-white font-bold">!</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Your Approval is Required</h3>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <p className="text-sm text-yellow-800">
              <strong>⏸️ Workflow is paused</strong> - The entire workflow execution is waiting for your decision.
              Once you approve or reject this artifact, the workflow will either continue to the next step or stop.
            </p>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded text-red-700">
              {error}
            </div>
          )}

          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                What is your decision?
              </label>
              <div className="grid grid-cols-2 gap-4">
                <label className={`
                  flex items-center justify-center p-4 border-2 rounded-lg cursor-pointer transition-all
                  ${decision === 'approve'
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-300 hover:border-green-300 hover:bg-green-50'
                  }
                `}>
                  <input
                    type="radio"
                    name="decision"
                    value="approve"
                    checked={decision === 'approve'}
                    onChange={(e) => setDecision(e.target.value as 'approve')}
                    className="sr-only"
                  />
                  <div className="text-center">
                    <div className="text-2xl mb-2">✅</div>
                    <div className="font-medium">Approve</div>
                    <div className="text-xs">Continue workflow</div>
                  </div>
                </label>

                <label className={`
                  flex items-center justify-center p-4 border-2 rounded-lg cursor-pointer transition-all
                  ${decision === 'reject'
                    ? 'border-red-500 bg-red-50 text-red-700'
                    : 'border-gray-300 hover:border-red-300 hover:bg-red-50'
                  }
                `}>
                  <input
                    type="radio"
                    name="decision"
                    value="reject"
                    checked={decision === 'reject'}
                    onChange={(e) => setDecision(e.target.value as 'reject')}
                    className="sr-only"
                  />
                  <div className="text-center">
                    <div className="text-2xl mb-2">❌</div>
                    <div className="font-medium">Reject</div>
                    <div className="text-xs">Stop workflow</div>
                  </div>
                </label>
              </div>
            </div>

            {decision && (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    {decision === 'reject' ? 'Rejection Reason (Required)' : 'Feedback (Optional)'}
                  </label>
                  {decision === 'reject' && (
                    <button
                      type="button"
                      onClick={() => setShowFeedbackTemplates(!showFeedbackTemplates)}
                      className="text-sm text-blue-600 hover:text-blue-800"
                    >
                      {showFeedbackTemplates ? 'Hide' : 'Show'} Templates
                    </button>
                  )}
                </div>

                {showFeedbackTemplates && decision === 'reject' && (
                  <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-sm text-blue-800 mb-2">Common feedback templates:</p>
                    <div className="space-y-1">
                      {feedbackTemplates.map((template, index) => (
                        <button
                          key={index}
                          type="button"
                          onClick={() => handleTemplateSelect(template)}
                          className="block w-full text-left text-sm p-2 hover:bg-blue-100 rounded transition-colors"
                        >
                          {template}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  placeholder={
                    decision === 'approve'
                      ? 'Optional: Add any feedback or comments...'
                      : decision === 'reject'
                      ? 'Required: Please provide specific feedback for improvement. This will help AI generate a better version.'
                      : ''
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={4}
                />

                {decision === 'reject' && feedback.trim().length > 0 && (
                  <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center text-sm text-green-800">
                      <span className="mr-2">🤖</span>
                      <span>
                        <strong>AI Regeneration:</strong> Your feedback will be used to automatically
                        generate an improved version of this artifact.
                      </span>
                    </div>
                  </div>
                )}
              </div>
            )}

            {decision && (
              <div className="flex space-x-3">
                <button
                  onClick={submitApproval}
                  disabled={!decision || submitting || (decision === 'reject' && !feedback.trim())}
                  className={`
                    flex-1 px-6 py-3 rounded-lg font-medium text-lg transition-all
                    ${decision === 'approve'
                      ? 'bg-green-600 text-white hover:bg-green-700 shadow-lg'
                      : decision === 'reject'
                      ? 'bg-red-600 text-white hover:bg-red-700 shadow-lg'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }
                    disabled:opacity-50 disabled:cursor-not-allowed
                  `}
                >
                  {submitting ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Submitting...
                    </div>
                  ) : (
                    `${decision === 'approve' ? '✅ Approve & Continue Workflow' : '❌ Reject & Stop Workflow'}`
                  )}
                </button>

                <button
                  onClick={() => {
                    setDecision('');
                    setFeedback('');
                    setError('');
                  }}
                  className="px-4 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  Reset
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Already Processed */}
      {isAlreadyProcessed && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Approval Status</h3>
          <p className="text-gray-600">
            This artifact has already been {approvalStatus?.status}. 
            {approvalStatus?.status === 'approved' && ' The workflow will continue automatically.'}
            {approvalStatus?.status === 'rejected' && ' The workflow has been stopped.'}
          </p>
        </div>
      )}
    </div>
  );
}
