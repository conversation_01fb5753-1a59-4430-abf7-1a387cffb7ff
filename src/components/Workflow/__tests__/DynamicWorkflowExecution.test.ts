/**
 * Dynamic Workflow Execution Tests
 * 
 * Test-driven development for workflow integration with agent collaboration
 */

import { DynamicWorkflowExecution } from '../DynamicWorkflowExecution';
import { AgentCollaborationEngine } from '../../../core/agents/AgentCollaborationEngine';
import { BaseAgent } from '../../../core/agents/base-agent';
import { AgentId, AgentCapability } from '../../../core/agents/types';

// Mock workflow step
interface WorkflowStep {
  id: string;
  name: string;
  type: string;
  config?: any;
}

// Mock agent for testing
class MockSeoAgent extends BaseAgent {
  constructor() {
    super('seo-keyword' as AgentId, ['keyword-research'] as AgentCapability[]);
  }

  protected async executeConsultation(request: any) {
    return {
      response: {
        keywords: ['test keyword', 'seo optimization'],
        recommendations: ['Use primary keyword in title']
      },
      confidence: 0.85,
      reasoning: 'Strong keyword analysis based on topic',
      suggestions: [{
        area: 'keyword-optimization',
        suggestion: 'Focus on primary keyword',
        priority: 'high' as const,
        confidence: 0.9,
        actionable: true
      }]
    };
  }
}

class MockMarketAgent extends BaseAgent {
  constructor() {
    super('market-research' as AgentId, ['market-analysis'] as AgentCapability[]);
  }

  protected async executeConsultation(request: any) {
    return {
      response: {
        marketSize: 'large',
        targetAudience: 'professionals',
        recommendations: ['Focus on B2B market']
      },
      confidence: 0.8,
      reasoning: 'Market analysis shows strong demand',
      suggestions: [{
        area: 'market-targeting',
        suggestion: 'Target enterprise customers',
        priority: 'medium' as const,
        confidence: 0.8,
        actionable: true
      }]
    };
  }
}

describe('DynamicWorkflowExecution', () => {
  let dynamicExecution: DynamicWorkflowExecution;
  let collaborationEngine: AgentCollaborationEngine;
  let mockSeoAgent: MockSeoAgent;
  let mockMarketAgent: MockMarketAgent;

  beforeEach(() => {
    collaborationEngine = new AgentCollaborationEngine();
    mockSeoAgent = new MockSeoAgent();
    mockMarketAgent = new MockMarketAgent();
    
    // Register agents
    collaborationEngine.registerAgent(mockSeoAgent);
    collaborationEngine.registerAgent(mockMarketAgent);
    
    dynamicExecution = new DynamicWorkflowExecution(collaborationEngine);
  });

  describe('Agent Selection for Steps', () => {
    test('should select relevant agents for keyword research step', async () => {
      const step: WorkflowStep = {
        id: 'keyword-step',
        name: 'Keyword Research',
        type: 'keyword-research'
      };

      const inputs = {
        topic: 'AI startups 2025',
        targetAudience: 'entrepreneurs'
      };

      const selectedAgents = await dynamicExecution.selectAgentsForStep(step, inputs);
      
      expect(selectedAgents).toContain('seo-keyword');
      expect(selectedAgents).toContain('market-research');
    });

    test('should select relevant agents for content creation step', async () => {
      const step: WorkflowStep = {
        id: 'content-step',
        name: 'Content Creation',
        type: 'content-creation'
      };

      const inputs = {
        topic: 'AI trends',
        targetAudience: 'developers'
      };

      const selectedAgents = await dynamicExecution.selectAgentsForStep(step, inputs);
      
      expect(selectedAgents.length).toBeGreaterThan(0);
      expect(selectedAgents).toContain('seo-keyword');
    });

    test('should return empty array for topic input step', async () => {
      const step: WorkflowStep = {
        id: 'input-step',
        name: 'Topic Input',
        type: 'topic-input'
      };

      const inputs = { topic: 'test topic' };

      const selectedAgents = await dynamicExecution.selectAgentsForStep(step, inputs);
      
      expect(selectedAgents).toHaveLength(0);
    });
  });

  describe('Step Execution with Collaboration', () => {
    test('should execute step without collaboration when no agents needed', async () => {
      const step: WorkflowStep = {
        id: 'input-step',
        name: 'Topic Input',
        type: 'topic-input'
      };

      const inputs = { topic: 'test topic' };

      const result = await dynamicExecution.executeStepWithCollaboration(step, inputs);
      
      expect(result).toBeDefined();
      expect(result.artifact).toBeDefined();
      expect(result.collaboration).toBeNull();
      expect(result.quality).toBe('standard');
    });

    test('should execute step with single agent collaboration', async () => {
      const step: WorkflowStep = {
        id: 'seo-step',
        name: 'SEO Optimization',
        type: 'seo-optimization'
      };

      const inputs = {
        topic: 'AI startups',
        targetAudience: 'entrepreneurs',
        primaryKeyword: 'AI startup'
      };

      const result = await dynamicExecution.executeStepWithCollaboration(step, inputs);
      
      expect(result).toBeDefined();
      expect(result.artifact).toBeDefined();
      expect(result.collaboration).toBeDefined();
      expect(result.collaboration?.consensus.confidence).toBeGreaterThan(0);
      expect(result.quality).toMatch(/high|medium/);
    });

    test('should execute step with multi-agent collaboration', async () => {
      const step: WorkflowStep = {
        id: 'content-step',
        name: 'Content Creation',
        type: 'content-creation'
      };

      const inputs = {
        topic: 'AI startups 2025',
        targetAudience: 'entrepreneurs',
        primaryKeyword: 'AI startup trends'
      };

      const result = await dynamicExecution.executeStepWithCollaboration(step, inputs);
      
      expect(result).toBeDefined();
      expect(result.artifact).toBeDefined();
      expect(result.collaboration).toBeDefined();
      expect(result.collaboration?.session.agents.length).toBeGreaterThan(1);
      expect(result.collaboration?.rounds.length).toBeGreaterThan(0);
      expect(result.quality).toMatch(/high|medium/);
    });

    test('should handle collaboration below quality threshold', async () => {
      // Mock low confidence collaboration
      const lowConfidenceAgent = new MockSeoAgent();
      lowConfidenceAgent['executeConsultation'] = async () => ({
        response: { keywords: ['low quality'] },
        confidence: 0.3, // Below threshold
        reasoning: 'Low confidence analysis',
        suggestions: []
      });

      const lowConfidenceEngine = new AgentCollaborationEngine();
      lowConfidenceEngine.registerAgent(lowConfidenceAgent);
      
      const lowConfidenceExecution = new DynamicWorkflowExecution(lowConfidenceEngine);

      const step: WorkflowStep = {
        id: 'seo-step',
        name: 'SEO Optimization',
        type: 'seo-optimization'
      };

      const inputs = { topic: 'test topic' };

      const result = await lowConfidenceExecution.executeStepWithCollaboration(step, inputs);
      
      expect(result).toBeDefined();
      expect(result.quality).toBe('medium'); // Should fall back to medium quality
    });
  });

  describe('Dynamic Agent Selection', () => {
    test('should add technical writer for technical audience', async () => {
      const step: WorkflowStep = {
        id: 'content-step',
        name: 'Content Creation',
        type: 'content-creation'
      };

      const inputs = {
        topic: 'API development',
        targetAudience: 'technical developers'
      };

      const selectedAgents = await dynamicExecution.selectAgentsForStep(step, inputs);
      
      // Should include base agents plus dynamic selection
      expect(selectedAgents.length).toBeGreaterThan(0);
    });

    test('should add tech specialist for AI/technology topics', async () => {
      const step: WorkflowStep = {
        id: 'content-step',
        name: 'Content Creation',
        type: 'content-creation'
      };

      const inputs = {
        topic: 'AI machine learning trends',
        targetAudience: 'developers'
      };

      const selectedAgents = await dynamicExecution.selectAgentsForStep(step, inputs);
      
      expect(selectedAgents.length).toBeGreaterThan(0);
    });
  });
});
