import React, { useState, useEffect } from 'react';

interface ReviewContent {
  contentId: string;
  title: string;
  content: string;
  agentInsights?: Array<{
    agentId: string;
    insight: string;
    confidence: number;
    recommendations: string[];
  }>;
  qualityScore?: number;
  recommendations?: string[];
}

interface WorkflowExecution {
  id: string;
  status: 'running' | 'paused' | 'completed' | 'failed' | 'waiting_review';
  progress: number;
  currentStep?: string;
  steps: Array<{
    id: string;
    name: string;
    status: string;
    outputs?: any;
    artifactId?: string;
  }>;
}

interface Props {
  execution: WorkflowExecution;
  onApprove: () => void;
  onReject: (feedback: string) => void;
  isSubmitting?: boolean;
}

export default function HumanReviewInterface({ 
  execution, 
  onApprove, 
  onReject, 
  isSubmitting = false 
}: Props) {
  const [reviewContent, setReviewContent] = useState<ReviewContent | null>(null);
  const [feedback, setFeedback] = useState('');
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchReviewContent();
  }, [execution.id]);

  const fetchReviewContent = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/workflow/execution/${execution.id}/review`);
      
      if (response.ok) {
        const data = await response.json();
        setReviewContent(data.reviewContent);
      } else {
        // Fallback to execution steps if review endpoint doesn't exist
        const contentSteps = execution.steps.filter(step => step.outputs && step.outputs.content);
        if (contentSteps.length > 0) {
          const latestStep = contentSteps[contentSteps.length - 1];
          setReviewContent({
            contentId: latestStep.id,
            title: latestStep.name,
            content: latestStep.outputs.content,
            qualityScore: latestStep.outputs.qualityScore || 85
          });
        }
      }
    } catch (error) {
      console.error('Failed to fetch review content:', error);
      // Fallback to execution steps
      const contentSteps = execution.steps.filter(step => step.outputs && step.outputs.content);
      if (contentSteps.length > 0) {
        const latestStep = contentSteps[contentSteps.length - 1];
        setReviewContent({
          contentId: latestStep.id,
          title: latestStep.name,
          content: latestStep.outputs.content,
          qualityScore: latestStep.outputs.qualityScore || 85
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprove = () => {
    onApprove();
  };

  const handleReject = () => {
    if (feedback.trim()) {
      onReject(feedback);
      setFeedback('');
      setShowFeedbackForm(false);
    }
  };

  const getQualityScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Loading Review Content...</h2>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Human Review Required</h2>
        <p className="text-gray-600">
          The workflow has paused for your review. Please examine the generated content and provide your decision.
        </p>
      </div>

      {reviewContent && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content Review */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">{reviewContent.title}</h3>
                {reviewContent.qualityScore && (
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">Quality Score:</span>
                    <span className={`text-lg font-bold ${getQualityScoreColor(reviewContent.qualityScore)}`}>
                      {reviewContent.qualityScore}%
                    </span>
                  </div>
                )}
              </div>

              <div className="prose max-w-none">
                <div className="bg-gray-50 p-4 rounded-lg border max-h-96 overflow-y-auto">
                  <div className="whitespace-pre-wrap text-sm text-gray-800">
                    {reviewContent.content}
                  </div>
                </div>
              </div>

              {reviewContent.recommendations && reviewContent.recommendations.length > 0 && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">AI Recommendations:</h4>
                  <ul className="text-sm text-gray-700 space-y-1">
                    {reviewContent.recommendations.map((rec, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-blue-500 mt-1">•</span>
                        <span>{rec}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Decision Panel */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Review Decision</h3>
              
              {!showFeedbackForm ? (
                <div className="flex space-x-4">
                  <button
                    onClick={handleApprove}
                    disabled={isSubmitting}
                    className="flex-1 px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Processing...</span>
                      </>
                    ) : (
                      <>
                        <span>✅</span>
                        <span>Approve & Continue</span>
                      </>
                    )}
                  </button>
                  <button
                    onClick={() => setShowFeedbackForm(true)}
                    disabled={isSubmitting}
                    className="flex-1 px-6 py-3 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                  >
                    <span>❌</span>
                    <span>Reject & Provide Feedback</span>
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <label htmlFor="feedback" className="block text-sm font-medium text-gray-700 mb-2">
                      Please provide specific feedback for improvements:
                    </label>
                    <textarea
                      id="feedback"
                      value={feedback}
                      onChange={(e) => setFeedback(e.target.value)}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Describe what needs to be improved, changed, or added..."
                    />
                  </div>
                  <div className="flex space-x-3">
                    <button
                      onClick={handleReject}
                      disabled={!feedback.trim() || isSubmitting}
                      className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Submit Feedback
                    </button>
                    <button
                      onClick={() => {
                        setShowFeedbackForm(false);
                        setFeedback('');
                      }}
                      disabled={isSubmitting}
                      className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Agent Insights Sidebar */}
          <div className="space-y-6">
            {reviewContent.agentInsights && reviewContent.agentInsights.length > 0 && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Agent Insights</h3>
                <div className="space-y-4">
                  {reviewContent.agentInsights.map((insight, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-sm font-medium text-gray-900">{insight.agentId}</h4>
                        <span className="text-xs text-gray-500">
                          {Math.round(insight.confidence * 100)}% confidence
                        </span>
                      </div>
                      <p className="text-sm text-gray-700 mb-2">{insight.insight}</p>
                      {insight.recommendations && insight.recommendations.length > 0 && (
                        <div>
                          <div className="text-xs font-medium text-gray-600 mb-1">Recommendations:</div>
                          <ul className="text-xs text-gray-600 space-y-1">
                            {insight.recommendations.map((rec, recIndex) => (
                              <li key={recIndex} className="flex items-start space-x-1">
                                <span>•</span>
                                <span>{rec}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Workflow Context */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Workflow Context</h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Execution ID:</span>
                  <span className="font-mono text-gray-900">{execution.id.slice(-8)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Progress:</span>
                  <span className="text-gray-900">{execution.progress}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Steps Completed:</span>
                  <span className="text-gray-900">
                    {execution.steps.filter(s => s.status === 'completed').length} / {execution.steps.length}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
