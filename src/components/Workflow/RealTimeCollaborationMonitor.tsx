/**
 * Real-Time Collaboration Monitor Component
 * 
 * Provides real-time monitoring of agent collaboration sessions with WebSocket connectivity,
 * agent status tracking, performance metrics, and human intervention alerts.
 */

import React, { useState, useEffect, useRef } from 'react';
import { CollaborationSession } from '../../core/agents/AgentCollaborationEngine';

export interface AgentActivity {
  agentId: string;
  status: 'analyzing' | 'waiting' | 'responding' | 'completed' | 'timeout' | 'idle';
  lastSeen: string;
}

export interface CollaborationMetrics {
  averageResponseTime?: number;
  consensusVelocity?: number;
  agentParticipation?: number;
  qualityScore?: number;
}

export interface ProgressData {
  currentRound: number;
  totalRounds: number;
  consensusLevel: number;
  participatingAgents: number;
}

export interface CollaborationAlert {
  id: string;
  type: 'consensus-stalled' | 'agent-timeout' | 'quality-low' | 'intervention-needed';
  message: string;
  severity: 'info' | 'warning' | 'error';
  timestamp: string;
}

export interface CollaborationUpdate {
  sessionId: string;
  type: 'round-started' | 'round-completed' | 'agent-joined' | 'agent-left' | 'consensus-updated';
  data: any;
}

export interface RealTimeCollaborationMonitorProps {
  sessionId: string;
  activeSessions?: CollaborationSession[];
  agentActivities?: AgentActivity[];
  metrics?: CollaborationMetrics;
  progressData?: ProgressData;
  alerts?: CollaborationAlert[];
  onSessionUpdate?: (update: CollaborationUpdate) => void;
  onAgentActivity?: (activity: AgentActivity) => void;
  onAlertDismiss?: (alertId: string) => void;
  reconnectInterval?: number;
  agentTimeoutMs?: number;
}

const AGENT_STATUS_COLORS = {
  analyzing: 'bg-blue-500',
  waiting: 'bg-yellow-500',
  responding: 'bg-green-500',
  completed: 'bg-gray-500',
  timeout: 'bg-red-500',
  idle: 'bg-gray-300'
};

const AGENT_STATUS_LABELS = {
  analyzing: 'Analyzing',
  waiting: 'Waiting',
  responding: 'Responding',
  completed: 'Completed',
  timeout: 'Timeout',
  idle: 'Idle'
};

export const RealTimeCollaborationMonitor: React.FC<RealTimeCollaborationMonitorProps> = ({
  sessionId,
  activeSessions = [],
  agentActivities = [],
  metrics = {},
  progressData,
  alerts = [],
  onSessionUpdate,
  onAgentActivity,
  onAlertDismiss,
  reconnectInterval = 5000,
  agentTimeoutMs = 30000
}) => {
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting');
  const [processedActivities, setProcessedActivities] = useState<AgentActivity[]>([]);
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Process agent activities to detect timeouts
  useEffect(() => {
    const now = Date.now();
    const processed = agentActivities.map(activity => {
      const lastSeenTime = new Date(activity.lastSeen).getTime();
      const isTimeout = now - lastSeenTime > agentTimeoutMs && activity.status !== 'completed';
      
      return {
        ...activity,
        status: isTimeout ? 'timeout' as const : activity.status
      };
    });
    
    setProcessedActivities(processed);
  }, [agentActivities, agentTimeoutMs]);

  // WebSocket connection management
  useEffect(() => {
    // Skip WebSocket in test environment
    if (typeof window === 'undefined' || process.env.NODE_ENV === 'test') {
      setConnectionStatus('connected');
      return;
    }

    const connectWebSocket = () => {
      try {
        const wsUrl = `ws://localhost:3000/api/collaboration/ws?sessionId=${sessionId}`;
        const ws = new WebSocket(wsUrl);

        ws.addEventListener('open', () => {
          setConnectionStatus('connected');
          console.log('🔗 WebSocket connected for collaboration monitoring');
        });

        ws.addEventListener('message', (event) => {
          try {
            const update: CollaborationUpdate = JSON.parse(event.data);
            if (onSessionUpdate) {
              onSessionUpdate(update);
            }
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        });

        ws.addEventListener('close', () => {
          setConnectionStatus('disconnected');
          console.log('🔌 WebSocket disconnected, attempting reconnection...');

          // Attempt reconnection
          reconnectTimeoutRef.current = setTimeout(() => {
            connectWebSocket();
          }, reconnectInterval);
        });

        ws.addEventListener('error', (error) => {
          console.error('WebSocket error:', error);
          setConnectionStatus('disconnected');
        });

        wsRef.current = ws;
      } catch (error) {
        console.error('Failed to create WebSocket connection:', error);
        setConnectionStatus('disconnected');
      }
    };

    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [sessionId, reconnectInterval, onSessionUpdate]);

  const getStatusColor = (status: AgentActivity['status']): string => {
    return AGENT_STATUS_COLORS[status] || 'bg-gray-400';
  };

  const getStatusLabel = (status: AgentActivity['status']): string => {
    return AGENT_STATUS_LABELS[status] || status;
  };

  const formatTime = (seconds: number): string => {
    return `${seconds.toFixed(1)}s`;
  };

  const formatPercentage = (value: number): string => {
    return `${Math.round(value * 100)}%`;
  };

  return (
    <div className="real-time-collaboration-monitor bg-white rounded-lg shadow-lg p-6">
      {/* Header with Connection Status */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-gray-800">
          📡 Real-Time Collaboration Monitor
        </h3>
        <div className="flex items-center space-x-2">
          <div 
            className={`w-3 h-3 rounded-full ${
              connectionStatus === 'connected' ? 'bg-green-500' : 
              connectionStatus === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
            }`}
          />
          <span 
            className="text-sm font-medium"
            data-testid="connection-status"
          >
            {connectionStatus === 'connected' ? 'Connected' : 
             connectionStatus === 'connecting' ? 'Connecting' : 'Disconnected'}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Active Collaborations */}
        <div className="collaboration-sessions">
          <h4 className="text-lg font-medium text-gray-800 mb-3">Active Collaborations</h4>
          <div className="space-y-3">
            {activeSessions.length > 0 ? (
              activeSessions.map(session => (
                <div key={session.id} className="bg-gray-50 rounded-lg p-3 border border-gray-200">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-gray-800">{session.id}</span>
                    <span className="text-sm text-gray-600">{session.agents.length} agents</span>
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    {session.task.objective}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-gray-500 text-sm">No active collaborations</div>
            )}
          </div>
        </div>

        {/* Agent Activity */}
        <div className="agent-activity">
          <h4 className="text-lg font-medium text-gray-800 mb-3">Agent Activity</h4>
          <div className="space-y-2">
            {processedActivities.map(activity => (
              <div 
                key={activity.agentId}
                className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                data-testid={`agent-activity-${activity.agentId}`}
              >
                <div className="flex items-center space-x-3">
                  <div 
                    className={`w-3 h-3 rounded-full ${getStatusColor(activity.status)}`}
                    data-testid={`status-${activity.status}`}
                  />
                  <span className="font-medium text-gray-800">{activity.agentId}</span>
                </div>
                <span className="text-sm text-gray-600">
                  {getStatusLabel(activity.status)}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Progress Data */}
      {progressData && (
        <div className="progress-section mt-6">
          <h4 className="text-lg font-medium text-gray-800 mb-3">Progress</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                Round {progressData.currentRound} of {progressData.totalRounds}
              </div>
              <div className="text-sm text-gray-600">Current Round</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {formatPercentage(progressData.consensusLevel)}
              </div>
              <div className="text-sm text-gray-600">Consensus Level</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {progressData.participatingAgents}
              </div>
              <div className="text-sm text-gray-600">Active Agents</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {Math.round((progressData.currentRound / progressData.totalRounds) * 100)}%
              </div>
              <div className="text-sm text-gray-600">Complete</div>
            </div>
          </div>
        </div>
      )}

      {/* Performance Metrics */}
      {Object.keys(metrics).length > 0 && (
        <div className="metrics-section mt-6">
          <h4 className="text-lg font-medium text-gray-800 mb-3">Performance Metrics</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {metrics.averageResponseTime && (
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {formatTime(metrics.averageResponseTime)}
                </div>
                <div className="text-sm text-gray-600">Avg Response</div>
              </div>
            )}
            {metrics.consensusVelocity && (
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {formatPercentage(metrics.consensusVelocity)}
                </div>
                <div className="text-sm text-gray-600">Consensus Velocity</div>
              </div>
            )}
            {metrics.agentParticipation && (
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {formatPercentage(metrics.agentParticipation)}
                </div>
                <div className="text-sm text-gray-600">Participation</div>
              </div>
            )}
            {metrics.qualityScore && (
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {metrics.qualityScore}
                </div>
                <div className="text-sm text-gray-600">Quality Score</div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Intervention Alerts */}
      {alerts.length > 0 && (
        <div className="alerts-section mt-6">
          <h4 className="text-lg font-medium text-gray-800 mb-3">Intervention Needed</h4>
          <div className="space-y-2">
            {alerts.map(alert => (
              <div 
                key={alert.id}
                className={`p-3 rounded-lg border-l-4 ${
                  alert.severity === 'error' ? 'bg-red-50 border-red-500' :
                  alert.severity === 'warning' ? 'bg-yellow-50 border-yellow-500' :
                  'bg-blue-50 border-blue-500'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-800">
                    {alert.message}
                  </span>
                  {onAlertDismiss && (
                    <button
                      onClick={() => onAlertDismiss(alert.id)}
                      className="text-gray-400 hover:text-gray-600"
                      data-testid={`dismiss-alert-${alert.id}`}
                    >
                      ✕
                    </button>
                  )}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {new Date(alert.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
