/**
 * Regeneration Status Component
 * 
 * Shows the status of artifact regeneration based on human feedback
 */

'use client';

import { useState, useEffect } from 'react';

interface RegenerationRequest {
  id: string;
  originalArtifactId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: string;
  completedAt?: string;
  newArtifactId?: string;
  error?: string;
  humanFeedback?: {
    feedback: string;
    approver: string;
    timestamp: string;
  };
}

interface RegenerationStatusProps {
  artifactId: string;
  regenerationRequest?: RegenerationRequest;
  onRegenerationComplete?: (newArtifactId: string) => void;
}

export default function RegenerationStatus({ 
  artifactId, 
  regenerationRequest,
  onRegenerationComplete 
}: RegenerationStatusProps) {
  const [status, setStatus] = useState<RegenerationRequest | null>(regenerationRequest || null);
  const [isPolling, setIsPolling] = useState(false);

  // Poll for status updates when regeneration is in progress
  useEffect(() => {
    if (status && (status.status === 'pending' || status.status === 'processing')) {
      setIsPolling(true);
      const interval = setInterval(async () => {
        try {
          // In a real implementation, this would call an API to get status
          // For now, we'll simulate status updates
          console.log('Polling regeneration status...');
          
          // Simulate completion after 10 seconds for demo
          if (status.status === 'processing') {
            const elapsed = Date.now() - new Date(status.createdAt).getTime();
            if (elapsed > 10000) { // 10 seconds
              const completedStatus = {
                ...status,
                status: 'completed' as const,
                completedAt: new Date().toISOString(),
                newArtifactId: `regenerated-${artifactId}-${Date.now()}`
              };
              setStatus(completedStatus);
              setIsPolling(false);
              
              if (onRegenerationComplete && completedStatus.newArtifactId) {
                onRegenerationComplete(completedStatus.newArtifactId);
              }
            }
          }
        } catch (error) {
          console.error('Error polling regeneration status:', error);
          setIsPolling(false);
        }
      }, 2000); // Poll every 2 seconds

      return () => {
        clearInterval(interval);
        setIsPolling(false);
      };
    }
  }, [status, artifactId, onRegenerationComplete]);

  if (!status) {
    return null;
  }

  const getStatusIcon = () => {
    switch (status.status) {
      case 'pending':
        return '⏳';
      case 'processing':
        return '🔄';
      case 'completed':
        return '✅';
      case 'failed':
        return '❌';
      default:
        return '❓';
    }
  };

  const getStatusColor = () => {
    switch (status.status) {
      case 'pending':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'processing':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'completed':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'failed':
        return 'bg-red-50 border-red-200 text-red-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  const getStatusMessage = () => {
    switch (status.status) {
      case 'pending':
        return 'Regeneration request queued';
      case 'processing':
        return 'AI is improving the artifact based on your feedback...';
      case 'completed':
        return 'Artifact has been improved and resubmitted for approval';
      case 'failed':
        return 'Regeneration failed. Please try again or contact support.';
      default:
        return 'Unknown status';
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  };

  return (
    <div className={`border rounded-lg p-4 mb-4 ${getStatusColor()}`}>
      <div className="flex items-start space-x-3">
        <div className="text-2xl">{getStatusIcon()}</div>
        
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-lg">
              Artifact Regeneration
            </h3>
            <span className="text-sm opacity-75">
              {formatTimeAgo(status.createdAt)}
            </span>
          </div>
          
          <p className="mb-3">
            {getStatusMessage()}
          </p>

          {status.status === 'processing' && (
            <div className="mb-3">
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                <span className="text-sm">
                  Processing your feedback and generating improvements...
                </span>
              </div>
              <div className="mt-2 bg-white bg-opacity-50 rounded-full h-2">
                <div className="bg-current h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
              </div>
            </div>
          )}

          {status.humanFeedback && (
            <div className="bg-white bg-opacity-50 rounded-lg p-3 mb-3">
              <h4 className="font-medium text-sm mb-1">Your Feedback:</h4>
              <p className="text-sm italic">"{status.humanFeedback.feedback}"</p>
              <p className="text-xs mt-1 opacity-75">
                - {status.humanFeedback.approver}
              </p>
            </div>
          )}

          {status.status === 'completed' && status.newArtifactId && (
            <div className="flex space-x-2">
              <button
                onClick={() => {
                  // Navigate to the new artifact
                  window.location.href = `/workflow/approval/${status.newArtifactId}`;
                }}
                className="px-4 py-2 bg-white bg-opacity-80 rounded-lg hover:bg-opacity-100 transition-colors text-sm font-medium"
              >
                View Improved Artifact
              </button>
            </div>
          )}

          {status.status === 'failed' && status.error && (
            <div className="bg-white bg-opacity-50 rounded-lg p-3">
              <h4 className="font-medium text-sm mb-1">Error Details:</h4>
              <p className="text-sm">{status.error}</p>
            </div>
          )}

          {status.completedAt && (
            <div className="text-xs opacity-75 mt-2">
              Completed {formatTimeAgo(status.completedAt)}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
