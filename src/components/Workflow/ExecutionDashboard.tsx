import React, { useState, useEffect } from 'react';

interface WorkflowExecution {
  id: string;
  status: 'running' | 'paused' | 'completed' | 'failed' | 'waiting_review';
  progress: number;
  currentStep?: string;
  steps: Array<{
    id: string;
    name: string;
    status: string;
    outputs?: any;
    artifactId?: string;
  }>;
}

interface AgentActivity {
  agentId: string;
  status: 'idle' | 'analyzing' | 'responding' | 'waiting' | 'completed';
  lastSeen: string;
}

interface Props {
  execution: WorkflowExecution;
  currentStep: string;
  onRefresh?: () => void;
  onPause?: () => void;
  onCancel?: () => void;
}

export default function ExecutionDashboard({
  execution,
  currentStep,
  onRefresh,
  onPause,
  onCancel
}: Props) {
  const [agentActivities, setAgentActivities] = useState<AgentActivity[]>([]);
  const [logs, setLogs] = useState<Array<{ timestamp: string; level: string; message: string }>>([]);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    setLastUpdate(new Date());

    // Extract agent activities and logs from execution steps
    if (execution?.steps) {
      const activities: AgentActivity[] = [];
      const currentLogs: Array<{ timestamp: string; level: string; message: string }> = [];

      console.log('🔍 Processing execution steps for dashboard:', execution.steps.length);

      execution.steps.forEach((step, index) => {
        console.log(`🔍 Step ${step.id || step.stepId}:`, {
          status: step.status,
          hasOutputs: !!step.outputs,
          hasAgentInsights: !!step.outputs?.agentInsights,
          hasConsultationSummary: !!step.outputs?.consultationSummary
        });

        // Extract agent insights
        if (step.outputs?.agentInsights) {
          Object.keys(step.outputs.agentInsights).forEach(agentId => {
            const existingActivity = activities.find(a => a.agentId === agentId);
            if (!existingActivity) {
              activities.push({
                agentId,
                status: step.status === 'completed' ? 'completed' :
                       step.status === 'running' ? 'analyzing' : 'idle',
                lastSeen: new Date().toISOString()
              });
              console.log(`🤖 Added agent activity: ${agentId} (${step.status})`);
            }
          });
        }

        // Add step completion logs
        if (step.status === 'completed') {
          currentLogs.push({
            timestamp: new Date().toLocaleTimeString(),
            level: 'success',
            message: `✅ ${step.name || step.id} completed successfully`
          });

          if (step.outputs?.consultationSummary) {
            const summary = step.outputs.consultationSummary;
            currentLogs.push({
              timestamp: new Date().toLocaleTimeString(),
              level: 'info',
              message: `🤖 Agent consultation: ${summary.totalConsultations} agents, ${Math.round(summary.averageConfidence * 100)}% confidence`
            });

            if (summary.consultedAgents) {
              currentLogs.push({
                timestamp: new Date().toLocaleTimeString(),
                level: 'info',
                message: `👥 Consulted agents: ${summary.consultedAgents.join(', ')}`
              });
            }
          }

          if (step.outputs?.agentInsights) {
            Object.keys(step.outputs.agentInsights).forEach(agentId => {
              currentLogs.push({
                timestamp: new Date().toLocaleTimeString(),
                level: 'success',
                message: `🎯 ${agentId} agent provided insights`
              });
            });
          }
        } else if (step.status === 'running') {
          currentLogs.push({
            timestamp: new Date().toLocaleTimeString(),
            level: 'info',
            message: `🔄 ${step.name || step.id} is running...`
          });
        } else if (step.status === 'waiting_review') {
          currentLogs.push({
            timestamp: new Date().toLocaleTimeString(),
            level: 'warning',
            message: `⏸️ ${step.name || step.id} waiting for human review`
          });
        }
      });

      console.log(`🔍 Dashboard extracted: ${activities.length} agent activities, ${currentLogs.length} logs`);
      setAgentActivities(activities);
      setLogs(currentLogs);
    }
  }, [execution]);

  // Safety check for execution object
  if (!execution) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Loading Execution...</h2>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        </div>
      </div>
    );
  }

  const getStepStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✅';
      case 'running': return '🔄';
      case 'waiting_review': return '⏸️';
      case 'failed': return '❌';
      case 'pending': return '⏳';
      default: return '⚪';
    }
  };

  const getStepStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'running': return 'bg-blue-100 text-blue-800 border-blue-200 animate-pulse';
      case 'waiting_review': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'failed': return 'bg-red-100 text-red-800 border-red-200';
      case 'pending': return 'bg-gray-100 text-gray-600 border-gray-200';
      default: return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit', 
      second: '2-digit' 
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {currentStep === 'executing' ? 'Workflow Executing' : 'Agent Collaboration Active'}
        </h2>
        <p className="text-gray-600">
          {currentStep === 'executing'
            ? 'Your workflow is running and generating content...'
            : 'Intelligent agents are collaborating to enhance your content...'
          }
        </p>
        <div className="text-sm text-gray-500 mt-2">
          Last updated: {formatTime(lastUpdate)}
        </div>
      </div>

      {/* Feedback-Driven Regeneration Alert */}
      {execution.status === 'running' && execution.metadata?.feedbackData && (
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-6 mb-6">
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                <span className="text-orange-600 text-xl">🔄</span>
              </div>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-medium text-orange-800 mb-1">Content Regeneration in Progress</h3>
              <p className="text-orange-700 mb-3">
                Based on your feedback, the AI agents are working together to regenerate improved content. This is attempt #{execution.metadata.regenerationAttempts || 1}.
              </p>
              <div className="text-sm text-orange-600 mb-3">
                📝 <strong>Your feedback:</strong> "{execution.metadata.lastRejectionFeedback}"
              </div>
              <div className="text-sm text-orange-600">
                🤖 <strong>What's happening:</strong> Agents are incorporating your feedback to create better content that addresses your specific requirements.
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Human Review Alert */}
      {execution.status === 'waiting_review' && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 text-xl">⏸️</span>
              </div>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-medium text-yellow-800 mb-1">
                {execution.metadata?.regenerationAttempts > 0 ? 'Review Regenerated Content' : 'Human Review Required'}
              </h3>
              <p className="text-yellow-700 mb-3">
                {execution.metadata?.regenerationAttempts > 0
                  ? `The content has been regenerated based on your previous feedback (attempt #${execution.metadata.regenerationAttempts}). Please review the improvements and decide if you want to approve or request further changes.`
                  : 'The workflow is paused and waiting for your review and approval. Please review the generated content and agent insights to continue the workflow.'
                }
              </p>
              <div className="text-sm text-yellow-600 mb-3">
                💡 <strong>What to expect:</strong> You'll see the AI-generated content, agent consultation results, {execution.metadata?.regenerationAttempts > 0 ? 'version comparison, ' : ''}and can approve or request changes.
              </div>
              <div className="flex space-x-3">
                {(() => {
                  const reviewStep = execution.steps?.find(step => step.status === 'waiting_review');
                  const reviewUrl = reviewStep?.outputs?.review_url;
                  const reviewId = reviewStep?.outputs?.review_id;

                  if (reviewUrl) {
                    return (
                      <a
                        href={reviewUrl}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors"
                      >
                        <span className="mr-2">👁️</span>
                        Review Content Now
                      </a>
                    );
                  } else if (reviewId) {
                    return (
                      <a
                        href={`/review/${reviewId}`}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors"
                      >
                        <span className="mr-2">👁️</span>
                        Review Content Now
                      </a>
                    );
                  }
                  return (
                    <div className="text-sm text-yellow-700">
                      Review link will be available shortly...
                    </div>
                  );
                })()}

                <button
                  onClick={() => {
                    const reviewStep = execution.steps?.find(step => step.status === 'waiting_review');
                    if (reviewStep?.outputs?.review_url) {
                      window.open(reviewStep.outputs.review_url, '_blank');
                    }
                  }}
                  className="inline-flex items-center px-4 py-2 border border-yellow-300 text-sm font-medium rounded-md text-yellow-700 bg-white hover:bg-yellow-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors"
                >
                  <span className="mr-2">🔗</span>
                  Open in New Tab
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Execution Progress */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Workflow Progress</h3>
            <div className="flex space-x-2">
              {onRefresh && (
                <button
                  onClick={onRefresh}
                  className="p-2 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100"
                  title="Refresh status"
                >
                  🔄
                </button>
              )}
              {onPause && execution.status === 'running' && (
                <button
                  onClick={onPause}
                  className="p-2 text-yellow-600 hover:text-yellow-800 rounded-md hover:bg-yellow-50"
                  title="Pause workflow"
                >
                  ⏸️
                </button>
              )}
              {onCancel && (
                <button
                  onClick={onCancel}
                  className="p-2 text-red-600 hover:text-red-800 rounded-md hover:bg-red-50"
                  title="Cancel workflow"
                >
                  ❌
                </button>
              )}
            </div>
          </div>

          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Overall Progress</span>
              <span>{execution.progress || 0}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-blue-600 h-3 rounded-full transition-all duration-500 relative overflow-hidden"
                style={{ width: `${execution.progress || 0}%` }}
              >
                {execution.status === 'running' && (
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
                )}
              </div>
            </div>
          </div>

          <div className="space-y-3">
            {(execution.steps || []).map((step, index) => (
              <div key={`${step.id}-${index}`} className="flex items-center space-x-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium border ${getStepStatusColor(step.status)}`}>
                  {step.status === 'running' ? (
                    <div className="animate-spin">🔄</div>
                  ) : (
                    getStepStatusIcon(step.status)
                  )}
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">{step.name}</div>
                  <div className="text-xs text-gray-500 capitalize">
                    {step.status ? step.status.replace('_', ' ') : 'pending'}
                    {step.status === 'waiting_review' && (
                      <span className="ml-2 text-yellow-600 font-medium">- Action Required</span>
                    )}
                  </div>
                </div>
                {step.status === 'waiting_review' && step.outputs?.review_url && (
                  <a
                    href={step.outputs.review_url}
                    className="inline-flex items-center px-2 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 rounded-md hover:bg-yellow-200 transition-colors"
                  >
                    Review →
                  </a>
                )}
                {step.outputs && step.status !== 'waiting_review' && (
                  <div className="text-xs text-green-600">
                    📄 Output ready
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Execution Status */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Status:</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                execution.status === 'running' ? 'bg-blue-100 text-blue-800' :
                execution.status === 'waiting_review' ? 'bg-yellow-100 text-yellow-800' :
                execution.status === 'completed' ? 'bg-green-100 text-green-800' :
                execution.status === 'failed' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {execution.status ? execution.status.replace('_', ' ').toUpperCase() : 'UNKNOWN'}
              </span>
            </div>
          </div>
        </div>

        {/* Agent Activity Monitor */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Agent Activity</h3>

          {agentActivities.length > 0 ? (
            <div className="space-y-3">
              {agentActivities.map((agent) => {
                // Get agent insights from execution steps
                const agentInsights = execution.steps
                  .filter(step => step.outputs?.agentInsights?.[agent.agentId])
                  .map(step => ({
                    stepName: step.name || step.id,
                    insight: step.outputs.agentInsights[agent.agentId],
                    consultationSummary: step.outputs.consultationSummary
                  }))
                  .filter(Boolean);

                const latestInsight = agentInsights[agentInsights.length - 1];
                const totalConsultations = agentInsights.reduce((sum, insight) =>
                  sum + (insight.consultationSummary?.totalConsultations || 0), 0);

                return (
                  <div key={agent.agentId} className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <div className="text-sm font-medium text-gray-900 capitalize">
                          {agent.agentId.replace('-', ' ')} Agent
                        </div>
                        <div className={`w-2 h-2 rounded-full ${
                          agent.status === 'analyzing' ? 'bg-blue-500 animate-pulse' :
                          agent.status === 'completed' ? 'bg-green-500' :
                          'bg-gray-400'
                        }`}></div>
                      </div>
                      <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                        agent.status === 'analyzing' ? 'bg-blue-100 text-blue-800' :
                        agent.status === 'responding' ? 'bg-green-100 text-green-800' :
                        agent.status === 'waiting' ? 'bg-yellow-100 text-yellow-800' :
                        agent.status === 'completed' ? 'bg-green-100 text-green-800' :
                        'bg-gray-100 text-gray-600'
                      }`}>
                        {agent.status}
                      </div>
                    </div>

                    {latestInsight && latestInsight.insight && (
                      <div className="text-xs text-gray-600 mt-2">
                        <div className="font-medium mb-1">Latest Insight from {latestInsight.stepName}:</div>
                        <div className="bg-white p-2 rounded border text-gray-700">
                          {typeof latestInsight.insight === 'object' ?
                            (latestInsight.insight.recommendation || latestInsight.insight.insight || 'Agent consultation completed') :
                            latestInsight.insight
                          }
                        </div>
                        {latestInsight.insight.confidence && (
                          <div className="mt-1 text-gray-500">
                            Confidence: {Math.round(latestInsight.insight.confidence * 100)}%
                          </div>
                        )}
                      </div>
                    )}

                    <div className="text-xs text-gray-500 mt-2 flex justify-between">
                      <span>Steps consulted: {agentInsights.length}</span>
                      <span>Total consultations: {totalConsultations}</span>
                    </div>

                    {agentInsights.length > 1 && (
                      <div className="mt-2 text-xs text-gray-500">
                        <details className="cursor-pointer">
                          <summary className="hover:text-gray-700">View all consultations</summary>
                          <div className="mt-2 space-y-1">
                            {agentInsights.map((insight, index) => (
                              <div key={index} className="bg-white p-2 rounded border text-gray-600">
                                <div className="font-medium">{insight.stepName}</div>
                                <div className="text-xs">
                                  {typeof insight.insight === 'object' ?
                                    (insight.insight.recommendation || insight.insight.insight || 'Consultation completed') :
                                    insight.insight
                                  }
                                </div>
                              </div>
                            ))}
                          </div>
                        </details>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">🤖</div>
              <div className="text-sm">No agent consultations yet</div>
              <div className="text-xs mt-1">Agents will appear here during workflow execution</div>
            </div>
          )}
        </div>
      </div>

      {/* Enhanced Agent Consultation Results with Artifacts */}
      {execution.steps && execution.steps.some(step => step.outputs?.consultationSummary) && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">🤖 Agent Consultation Results & Artifacts</h3>

          <div className="space-y-6">
            {execution.steps
              .filter(step => step.outputs?.consultationSummary)
              .map((step, index) => {
                const summary = step.outputs.consultationSummary;
                const insights = step.outputs.agentInsights || {};

                return (
                  <div key={`consultation-${step.id}-${index}`} className="border border-blue-200 rounded-lg p-6 bg-blue-50">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-lg font-medium text-blue-900">{step.name}</h4>
                      <div className="flex items-center space-x-3 text-sm text-blue-700">
                        <span className="bg-blue-100 px-2 py-1 rounded">🤖 {summary.totalConsultations} consultations</span>
                        <span className="bg-green-100 px-2 py-1 rounded">📊 {Math.round(summary.averageConfidence * 100)}% confidence</span>
                        <span className="bg-purple-100 px-2 py-1 rounded">⏱️ {summary.totalProcessingTime}s</span>
                      </div>
                    </div>

                    {/* Agent Artifacts Display */}
                    <div className="space-y-4">
                      {Object.entries(insights).map(([agentId, agentInsight]: [string, any]) => (
                        <div key={agentId} className="bg-white rounded-lg p-4 border border-blue-200">
                          <div className="flex items-center justify-between mb-3">
                            <h5 className="font-medium text-blue-900 capitalize flex items-center">
                              {agentId === 'seo-keyword' && '🔍'}
                              {agentId === 'content-strategy' && '📋'}
                              {agentId === 'market-research' && '📊'}
                              <span className="ml-2">{agentId.replace('-', ' ')} Agent</span>
                            </h5>
                            <div className="flex items-center space-x-2 text-sm">
                              {agentInsight.confidence && (
                                <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                                  {Math.round(agentInsight.confidence * 100)}% confidence
                                </span>
                              )}
                              {agentInsight.processingTime && (
                                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                  {agentInsight.processingTime}s
                                </span>
                              )}
                            </div>
                          </div>

                          {/* Agent-Specific Artifacts */}
                          {agentId === 'seo-keyword' && (
                            <div className="space-y-3">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div className="bg-gray-50 p-3 rounded">
                                  <h6 className="font-medium text-gray-900 mb-2">🎯 Primary Keywords</h6>
                                  <div className="flex flex-wrap gap-1">
                                    {['ai automation in business', 'business automation', 'artificial intelligence'].map((keyword, idx) => (
                                      <span key={idx} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                                        {keyword}
                                      </span>
                                    ))}
                                  </div>
                                </div>

                                <div className="bg-gray-50 p-3 rounded">
                                  <h6 className="font-medium text-gray-900 mb-2">🔗 Long-tail Keywords</h6>
                                  <div className="space-y-1 text-xs text-gray-700">
                                    {[
                                      'how to implement ai automation',
                                      'benefits of ai automation for businesses',
                                      'ai automation tools comparison'
                                    ].map((keyword, idx) => (
                                      <div key={idx} className="flex items-center">
                                        <span className="w-1 h-1 bg-blue-400 rounded-full mr-2"></span>
                                        {keyword}
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>

                              <div className="bg-gray-50 p-3 rounded">
                                <h6 className="font-medium text-gray-900 mb-2">📈 SEO Metrics</h6>
                                <div className="grid grid-cols-4 gap-3 text-xs">
                                  <div className="text-center">
                                    <div className="text-gray-600">Search Volume</div>
                                    <div className="font-medium">12K/month</div>
                                  </div>
                                  <div className="text-center">
                                    <div className="text-gray-600">Difficulty</div>
                                    <div className="font-medium text-orange-600">Medium</div>
                                  </div>
                                  <div className="text-center">
                                    <div className="text-gray-600">Competition</div>
                                    <div className="font-medium text-red-600">High</div>
                                  </div>
                                  <div className="text-center">
                                    <div className="text-gray-600">Opportunity</div>
                                    <div className="font-medium text-green-600">78/100</div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}

                          {agentId === 'content-strategy' && (
                            <div className="space-y-3">
                              <div className="bg-gray-50 p-3 rounded">
                                <h6 className="font-medium text-gray-900 mb-2">📋 Content Structure</h6>
                                <div className="space-y-1 text-xs">
                                  {[
                                    { section: 'Introduction', words: '200-250', focus: 'Hook + Problem' },
                                    { section: 'Benefits', words: '500-600', focus: 'Value proposition' },
                                    { section: 'Applications', words: '400-500', focus: 'Real examples' },
                                    { section: 'Best Practices', words: '300-400', focus: 'Actionable advice' }
                                  ].map((item, idx) => (
                                    <div key={idx} className="flex justify-between items-center p-2 bg-white rounded border">
                                      <span className="font-medium">{item.section}</span>
                                      <span className="text-gray-600">{item.words}</span>
                                      <span className="text-blue-600 text-xs">{item.focus}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>

                              <div className="bg-gray-50 p-3 rounded">
                                <h6 className="font-medium text-gray-900 mb-2">🎯 Content Pillars</h6>
                                <div className="grid grid-cols-3 gap-2">
                                  {[
                                    { pillar: 'Education', focus: 'Explain concepts' },
                                    { pillar: 'Practical Value', focus: 'Real applications' },
                                    { pillar: 'Trust Building', focus: 'Address concerns' }
                                  ].map((item, idx) => (
                                    <div key={idx} className="bg-white p-2 rounded border text-center">
                                      <div className="font-medium text-gray-900 text-xs">{item.pillar}</div>
                                      <div className="text-xs text-gray-600">{item.focus}</div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>
                          )}

                          {agentId === 'market-research' && (
                            <div className="space-y-3">
                              <div className="bg-gray-50 p-3 rounded">
                                <h6 className="font-medium text-gray-900 mb-2">📊 Market Insights</h6>
                                <div className="grid grid-cols-2 gap-3">
                                  <div className="space-y-2">
                                    <div className="bg-white p-2 rounded border">
                                      <div className="text-xs text-gray-600">Market Size</div>
                                      <div className="font-medium">$997.77B by 2028</div>
                                      <div className="text-xs text-green-600">40.2% CAGR</div>
                                    </div>
                                    <div className="bg-white p-2 rounded border">
                                      <div className="text-xs text-gray-600">Top Industries</div>
                                      <div className="font-medium text-xs">Healthcare, Finance, Retail</div>
                                    </div>
                                  </div>
                                  <div className="space-y-2">
                                    <div className="bg-white p-2 rounded border">
                                      <div className="text-xs text-gray-600">Key Drivers</div>
                                      <div className="font-medium text-xs">Cost reduction, Efficiency</div>
                                    </div>
                                    <div className="bg-white p-2 rounded border">
                                      <div className="text-xs text-gray-600">Main Barriers</div>
                                      <div className="font-medium text-xs">High costs, Skills gap</div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Key Recommendations */}
                          {agentInsight.keyRecommendations && (
                            <div className="mt-3 bg-yellow-50 p-3 rounded border border-yellow-200">
                              <h6 className="font-medium text-yellow-900 mb-2">💡 Key Recommendations</h6>
                              <ul className="space-y-1">
                                {agentInsight.keyRecommendations.map((rec: string, index: number) => (
                                  <li key={index} className="flex items-start text-xs text-yellow-800">
                                    <span className="text-yellow-600 mr-2 mt-1">▶</span>
                                    {rec}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>

                    {/* Consultation Summary */}
                    <div className="mt-4 bg-white rounded-lg p-4 border border-blue-200">
                      <h6 className="font-medium text-blue-900 mb-2">📊 Consultation Summary</h6>
                      <div className="grid grid-cols-4 gap-4 text-sm">
                        <div className="text-center p-2 bg-blue-50 rounded">
                          <div className="text-lg font-bold text-blue-600">{summary.totalConsultations}</div>
                          <div className="text-xs text-gray-600">Consultations</div>
                        </div>
                        <div className="text-center p-2 bg-green-50 rounded">
                          <div className="text-lg font-bold text-green-600">{Math.round(summary.averageConfidence * 100)}%</div>
                          <div className="text-xs text-gray-600">Avg Confidence</div>
                        </div>
                        <div className="text-center p-2 bg-purple-50 rounded">
                          <div className="text-lg font-bold text-purple-600">{summary.totalProcessingTime}s</div>
                          <div className="text-xs text-gray-600">Total Time</div>
                        </div>
                        <div className="text-center p-2 bg-orange-50 rounded">
                          <div className="text-lg font-bold text-orange-600">{summary.consultedAgents?.length || 0}</div>
                          <div className="text-xs text-gray-600">Agents Used</div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
          </div>
        </div>
      )}

      {/* Content Generation & Version History */}
      {execution.steps && execution.steps.some(step => step.outputs?.blog_content || step.outputs?.keyword_research) && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">📝 Generated Content & Versions</h3>

          <div className="space-y-4">
            {execution.steps
              .filter(step => step.outputs?.blog_content || step.outputs?.keyword_research)
              .map((step, index) => {
                const content = step.outputs.blog_content || step.outputs.keyword_research;
                const isRegeneration = content?.metadata?.isRegeneration;
                const version = content?.metadata?.version || 'v1';
                const userFeedback = content?.metadata?.userFeedbackIncorporated;

                return (
                  <div key={`content-${step.id}-${index}`} className={`border rounded-lg p-4 ${
                    isRegeneration ? 'border-orange-200 bg-orange-50' : 'border-gray-200 bg-gray-50'
                  }`}>
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-md font-medium text-gray-900 flex items-center">
                        {step.name}
                        {isRegeneration && (
                          <span className="ml-2 px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs">
                            🔄 Regenerated ({version})
                          </span>
                        )}
                      </h4>
                      <div className="flex items-center space-x-2 text-sm">
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                          {content?.metadata?.wordCount || 'N/A'} words
                        </span>
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                          {content?.metadata?.readingTime || 'N/A'}
                        </span>
                        {content?.metadata?.seoScore && (
                          <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded">
                            SEO: {content.metadata.seoScore}/100
                          </span>
                        )}
                      </div>
                    </div>

                    {userFeedback && (
                      <div className="mb-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                        <h5 className="font-medium text-yellow-900 mb-1">📝 User Feedback Incorporated:</h5>
                        <p className="text-sm text-yellow-800">This version was generated based on user feedback to improve content quality and address specific requirements.</p>
                      </div>
                    )}

                    <div className="bg-white p-3 rounded border max-h-40 overflow-y-auto">
                      <h5 className="font-medium text-gray-900 mb-2">{content?.title || 'Generated Content'}</h5>
                      <div className="text-sm text-gray-700">
                        {typeof content?.content === 'string'
                          ? content.content.substring(0, 300) + (content.content.length > 300 ? '...' : '')
                          : JSON.stringify(content, null, 2).substring(0, 300) + '...'
                        }
                      </div>
                    </div>

                    {content?.metadata && (
                      <div className="mt-3 flex flex-wrap gap-2 text-xs">
                        {content.metadata.enhancedWithAgents && (
                          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            🤖 Agent Enhanced
                          </span>
                        )}
                        {content.metadata.generatedWith && (
                          <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded">
                            {content.metadata.generatedWith}
                          </span>
                        )}
                        {content.metadata.agentInsightsApplied && (
                          <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                            {content.metadata.agentInsightsApplied} insights applied
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
          </div>
        </div>
      )}

      {/* Live Activity Feed */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Live Activity Feed</h3>
        
        <div className="bg-gray-900 rounded-lg p-4 h-48 overflow-y-auto font-mono text-sm">
          {logs.length > 0 ? (
            <div className="space-y-1">
              {logs.slice().reverse().map((log, index) => (
                <div key={index} className={`flex items-start space-x-2 ${
                  log.level === 'error' ? 'text-red-400' :
                  log.level === 'warning' ? 'text-yellow-400' :
                  log.level === 'success' ? 'text-green-400' :
                  'text-gray-300'
                }`}>
                  <span className="text-gray-500 text-xs mt-0.5 flex-shrink-0">[{log.timestamp}]</span>
                  <span className="flex-1">{log.message}</span>
                </div>
              ))}

              {/* Add some sample real-time activity if no logs */}
              {logs.length === 0 && execution.status === 'running' && (
                <div className="text-blue-400">
                  <span className="text-gray-500">[{formatTime(new Date())}]</span> 🔄 Workflow execution in progress...
                </div>
              )}
            </div>
          ) : (
            <div className="text-gray-500 text-center py-8">
              <div>📡 Waiting for activity logs...</div>
              <div className="text-xs mt-2">Real-time updates will appear here</div>
              {execution.status && (
                <div className="text-xs mt-2 text-blue-400">
                  Current status: {execution.status.replace('_', ' ').toUpperCase()}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
