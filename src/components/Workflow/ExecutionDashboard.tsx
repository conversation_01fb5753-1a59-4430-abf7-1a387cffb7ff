import React, { useState, useEffect } from 'react';

interface WorkflowExecution {
  id: string;
  status: 'running' | 'paused' | 'completed' | 'failed' | 'waiting_review';
  progress: number;
  currentStep?: string;
  steps: Array<{
    id: string;
    name: string;
    status: string;
    outputs?: any;
    artifactId?: string;
  }>;
}

interface AgentActivity {
  agentId: string;
  status: 'idle' | 'analyzing' | 'responding' | 'waiting' | 'completed';
  lastSeen: string;
}

interface Props {
  execution: WorkflowExecution;
  currentStep: string;
  onRefresh?: () => void;
  onPause?: () => void;
  onCancel?: () => void;
}

export default function ExecutionDashboard({ 
  execution, 
  currentStep, 
  onRefresh, 
  onPause, 
  onCancel 
}: Props) {
  const [agentActivities, setAgentActivities] = useState<AgentActivity[]>([]);
  const [logs, setLogs] = useState<Array<{ timestamp: string; level: string; message: string }>>([]);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    setLastUpdate(new Date());
  }, [execution]);

  const getStepStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✅';
      case 'running': return '🔄';
      case 'waiting_review': return '⏸️';
      case 'failed': return '❌';
      case 'pending': return '⏳';
      default: return '⚪';
    }
  };

  const getStepStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'running': return 'bg-blue-100 text-blue-800 border-blue-200 animate-pulse';
      case 'waiting_review': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'failed': return 'bg-red-100 text-red-800 border-red-200';
      case 'pending': return 'bg-gray-100 text-gray-600 border-gray-200';
      default: return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit', 
      second: '2-digit' 
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {currentStep === 'executing' ? 'Workflow Executing' : 'Agent Collaboration Active'}
        </h2>
        <p className="text-gray-600">
          {currentStep === 'executing'
            ? 'Your workflow is running and generating content...'
            : 'Intelligent agents are collaborating to enhance your content...'
          }
        </p>
        <div className="text-sm text-gray-500 mt-2">
          Last updated: {formatTime(lastUpdate)}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Execution Progress */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Workflow Progress</h3>
            <div className="flex space-x-2">
              {onRefresh && (
                <button
                  onClick={onRefresh}
                  className="p-2 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100"
                  title="Refresh status"
                >
                  🔄
                </button>
              )}
              {onPause && execution.status === 'running' && (
                <button
                  onClick={onPause}
                  className="p-2 text-yellow-600 hover:text-yellow-800 rounded-md hover:bg-yellow-50"
                  title="Pause workflow"
                >
                  ⏸️
                </button>
              )}
              {onCancel && (
                <button
                  onClick={onCancel}
                  className="p-2 text-red-600 hover:text-red-800 rounded-md hover:bg-red-50"
                  title="Cancel workflow"
                >
                  ❌
                </button>
              )}
            </div>
          </div>

          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Overall Progress</span>
              <span>{execution.progress || 0}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-blue-600 h-3 rounded-full transition-all duration-500 relative overflow-hidden"
                style={{ width: `${execution.progress || 0}%` }}
              >
                {execution.status === 'running' && (
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
                )}
              </div>
            </div>
          </div>

          <div className="space-y-3">
            {(execution.steps || []).map((step, index) => (
              <div key={step.id} className="flex items-center space-x-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium border ${getStepStatusColor(step.status)}`}>
                  {step.status === 'running' ? (
                    <div className="animate-spin">🔄</div>
                  ) : (
                    getStepStatusIcon(step.status)
                  )}
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">{step.name}</div>
                  <div className="text-xs text-gray-500 capitalize">{step.status.replace('_', ' ')}</div>
                </div>
                {step.outputs && (
                  <div className="text-xs text-green-600">
                    📄 Output ready
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Execution Status */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Status:</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                execution.status === 'running' ? 'bg-blue-100 text-blue-800' :
                execution.status === 'waiting_review' ? 'bg-yellow-100 text-yellow-800' :
                execution.status === 'completed' ? 'bg-green-100 text-green-800' :
                execution.status === 'failed' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {execution.status ? execution.status.replace('_', ' ').toUpperCase() : 'UNKNOWN'}
              </span>
            </div>
          </div>
        </div>

        {/* Agent Activity Monitor */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Agent Activity</h3>
          
          {agentActivities.length > 0 ? (
            <div className="space-y-3">
              {agentActivities.map((agent) => (
                <div key={agent.agentId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{agent.agentId}</div>
                    <div className="text-xs text-gray-500">Last seen: {agent.lastSeen}</div>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                    agent.status === 'analyzing' ? 'bg-blue-100 text-blue-800' :
                    agent.status === 'responding' ? 'bg-green-100 text-green-800' :
                    agent.status === 'waiting' ? 'bg-yellow-100 text-yellow-800' :
                    agent.status === 'completed' ? 'bg-gray-100 text-gray-800' :
                    'bg-gray-100 text-gray-600'
                  }`}>
                    {agent.status}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">🤖</div>
              <div className="text-sm">No active agent consultations</div>
              <div className="text-xs mt-1">Agents will appear here when collaboration is enabled</div>
            </div>
          )}
        </div>
      </div>

      {/* Live Activity Feed */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Live Activity Feed</h3>
        
        <div className="bg-gray-900 rounded-lg p-4 h-48 overflow-y-auto font-mono text-sm">
          {logs.length > 0 ? (
            logs.map((log, index) => (
              <div key={index} className={`mb-1 ${
                log.level === 'error' ? 'text-red-400' :
                log.level === 'warning' ? 'text-yellow-400' :
                log.level === 'success' ? 'text-green-400' :
                'text-gray-300'
              }`}>
                <span className="text-gray-500">[{log.timestamp}]</span> {log.message}
              </div>
            ))
          ) : (
            <div className="text-gray-500 text-center py-8">
              <div>📡 Waiting for activity logs...</div>
              <div className="text-xs mt-2">Real-time updates will appear here</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
