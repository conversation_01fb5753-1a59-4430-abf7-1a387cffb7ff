import React, { useState, useEffect } from 'react';

interface WorkflowExecution {
  id: string;
  status: 'running' | 'paused' | 'completed' | 'failed' | 'waiting_review';
  progress: number;
  currentStep?: string;
  steps: Array<{
    id: string;
    name: string;
    status: string;
    outputs?: any;
    artifactId?: string;
  }>;
}

interface AgentActivity {
  agentId: string;
  status: 'idle' | 'analyzing' | 'responding' | 'waiting' | 'completed';
  lastSeen: string;
}

interface Props {
  execution: WorkflowExecution;
  currentStep: string;
  onRefresh?: () => void;
  onPause?: () => void;
  onCancel?: () => void;
}

export default function ExecutionDashboard({
  execution,
  currentStep,
  onRefresh,
  onPause,
  onCancel
}: Props) {
  const [agentActivities, setAgentActivities] = useState<AgentActivity[]>([]);
  const [logs, setLogs] = useState<Array<{ timestamp: string; level: string; message: string }>>([]);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    setLastUpdate(new Date());

    // Extract agent activities and logs from execution steps
    if (execution?.steps) {
      const activities: AgentActivity[] = [];
      const currentLogs: Array<{ timestamp: string; level: string; message: string }> = [];

      console.log('🔍 Processing execution steps for dashboard:', execution.steps.length);

      execution.steps.forEach((step, index) => {
        console.log(`🔍 Step ${step.id || step.stepId}:`, {
          status: step.status,
          hasOutputs: !!step.outputs,
          hasAgentInsights: !!step.outputs?.agentInsights,
          hasConsultationSummary: !!step.outputs?.consultationSummary
        });

        // Extract agent insights
        if (step.outputs?.agentInsights) {
          Object.keys(step.outputs.agentInsights).forEach(agentId => {
            const existingActivity = activities.find(a => a.agentId === agentId);
            if (!existingActivity) {
              activities.push({
                agentId,
                status: step.status === 'completed' ? 'completed' :
                       step.status === 'running' ? 'analyzing' : 'idle',
                lastSeen: new Date().toISOString()
              });
              console.log(`🤖 Added agent activity: ${agentId} (${step.status})`);
            }
          });
        }

        // Add step completion logs
        if (step.status === 'completed') {
          currentLogs.push({
            timestamp: new Date().toLocaleTimeString(),
            level: 'success',
            message: `✅ ${step.name || step.id} completed successfully`
          });

          if (step.outputs?.consultationSummary) {
            const summary = step.outputs.consultationSummary;
            currentLogs.push({
              timestamp: new Date().toLocaleTimeString(),
              level: 'info',
              message: `🤖 Agent consultation: ${summary.totalConsultations} agents, ${Math.round(summary.averageConfidence * 100)}% confidence`
            });

            if (summary.consultedAgents) {
              currentLogs.push({
                timestamp: new Date().toLocaleTimeString(),
                level: 'info',
                message: `👥 Consulted agents: ${summary.consultedAgents.join(', ')}`
              });
            }
          }

          if (step.outputs?.agentInsights) {
            Object.keys(step.outputs.agentInsights).forEach(agentId => {
              currentLogs.push({
                timestamp: new Date().toLocaleTimeString(),
                level: 'success',
                message: `🎯 ${agentId} agent provided insights`
              });
            });
          }
        } else if (step.status === 'running') {
          currentLogs.push({
            timestamp: new Date().toLocaleTimeString(),
            level: 'info',
            message: `🔄 ${step.name || step.id} is running...`
          });
        } else if (step.status === 'waiting_review') {
          currentLogs.push({
            timestamp: new Date().toLocaleTimeString(),
            level: 'warning',
            message: `⏸️ ${step.name || step.id} waiting for human review`
          });
        }
      });

      console.log(`🔍 Dashboard extracted: ${activities.length} agent activities, ${currentLogs.length} logs`);
      setAgentActivities(activities);
      setLogs(currentLogs);
    }
  }, [execution]);

  // Safety check for execution object
  if (!execution) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Loading Execution...</h2>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        </div>
      </div>
    );
  }

  const getStepStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✅';
      case 'running': return '🔄';
      case 'waiting_review': return '⏸️';
      case 'failed': return '❌';
      case 'pending': return '⏳';
      default: return '⚪';
    }
  };

  const getStepStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'running': return 'bg-blue-100 text-blue-800 border-blue-200 animate-pulse';
      case 'waiting_review': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'failed': return 'bg-red-100 text-red-800 border-red-200';
      case 'pending': return 'bg-gray-100 text-gray-600 border-gray-200';
      default: return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit', 
      second: '2-digit' 
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {currentStep === 'executing' ? 'Workflow Executing' : 'Agent Collaboration Active'}
        </h2>
        <p className="text-gray-600">
          {currentStep === 'executing'
            ? 'Your workflow is running and generating content...'
            : 'Intelligent agents are collaborating to enhance your content...'
          }
        </p>
        <div className="text-sm text-gray-500 mt-2">
          Last updated: {formatTime(lastUpdate)}
        </div>
      </div>

      {/* Human Review Alert */}
      {execution.status === 'waiting_review' && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 text-xl">⏸️</span>
              </div>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-medium text-yellow-800 mb-1">Human Review Required</h3>
              <p className="text-yellow-700 mb-3">
                The workflow is paused and waiting for your review and approval. Please review the generated content and agent insights to continue the workflow.
              </p>
              <div className="text-sm text-yellow-600 mb-3">
                💡 <strong>What to expect:</strong> You'll see the AI-generated content, agent consultation results, and can approve or request changes.
              </div>
              <div className="flex space-x-3">
                {(() => {
                  const reviewStep = execution.steps?.find(step => step.status === 'waiting_review');
                  const reviewUrl = reviewStep?.outputs?.review_url;
                  const reviewId = reviewStep?.outputs?.review_id;

                  if (reviewUrl) {
                    return (
                      <a
                        href={reviewUrl}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors"
                      >
                        <span className="mr-2">👁️</span>
                        Review Content Now
                      </a>
                    );
                  } else if (reviewId) {
                    return (
                      <a
                        href={`/review/${reviewId}`}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors"
                      >
                        <span className="mr-2">👁️</span>
                        Review Content Now
                      </a>
                    );
                  }
                  return (
                    <div className="text-sm text-yellow-700">
                      Review link will be available shortly...
                    </div>
                  );
                })()}

                <button
                  onClick={() => {
                    const reviewStep = execution.steps?.find(step => step.status === 'waiting_review');
                    if (reviewStep?.outputs?.review_url) {
                      window.open(reviewStep.outputs.review_url, '_blank');
                    }
                  }}
                  className="inline-flex items-center px-4 py-2 border border-yellow-300 text-sm font-medium rounded-md text-yellow-700 bg-white hover:bg-yellow-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors"
                >
                  <span className="mr-2">🔗</span>
                  Open in New Tab
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Execution Progress */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Workflow Progress</h3>
            <div className="flex space-x-2">
              {onRefresh && (
                <button
                  onClick={onRefresh}
                  className="p-2 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100"
                  title="Refresh status"
                >
                  🔄
                </button>
              )}
              {onPause && execution.status === 'running' && (
                <button
                  onClick={onPause}
                  className="p-2 text-yellow-600 hover:text-yellow-800 rounded-md hover:bg-yellow-50"
                  title="Pause workflow"
                >
                  ⏸️
                </button>
              )}
              {onCancel && (
                <button
                  onClick={onCancel}
                  className="p-2 text-red-600 hover:text-red-800 rounded-md hover:bg-red-50"
                  title="Cancel workflow"
                >
                  ❌
                </button>
              )}
            </div>
          </div>

          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Overall Progress</span>
              <span>{execution.progress || 0}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-blue-600 h-3 rounded-full transition-all duration-500 relative overflow-hidden"
                style={{ width: `${execution.progress || 0}%` }}
              >
                {execution.status === 'running' && (
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
                )}
              </div>
            </div>
          </div>

          <div className="space-y-3">
            {(execution.steps || []).map((step, index) => (
              <div key={`${step.id}-${index}`} className="flex items-center space-x-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium border ${getStepStatusColor(step.status)}`}>
                  {step.status === 'running' ? (
                    <div className="animate-spin">🔄</div>
                  ) : (
                    getStepStatusIcon(step.status)
                  )}
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">{step.name}</div>
                  <div className="text-xs text-gray-500 capitalize">
                    {step.status ? step.status.replace('_', ' ') : 'pending'}
                    {step.status === 'waiting_review' && (
                      <span className="ml-2 text-yellow-600 font-medium">- Action Required</span>
                    )}
                  </div>
                </div>
                {step.status === 'waiting_review' && step.outputs?.review_url && (
                  <a
                    href={step.outputs.review_url}
                    className="inline-flex items-center px-2 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 rounded-md hover:bg-yellow-200 transition-colors"
                  >
                    Review →
                  </a>
                )}
                {step.outputs && step.status !== 'waiting_review' && (
                  <div className="text-xs text-green-600">
                    📄 Output ready
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Execution Status */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Status:</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                execution.status === 'running' ? 'bg-blue-100 text-blue-800' :
                execution.status === 'waiting_review' ? 'bg-yellow-100 text-yellow-800' :
                execution.status === 'completed' ? 'bg-green-100 text-green-800' :
                execution.status === 'failed' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {execution.status ? execution.status.replace('_', ' ').toUpperCase() : 'UNKNOWN'}
              </span>
            </div>
          </div>
        </div>

        {/* Agent Activity Monitor */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Agent Activity</h3>

          {agentActivities.length > 0 ? (
            <div className="space-y-3">
              {agentActivities.map((agent) => {
                // Get agent insights from execution steps
                const agentInsights = execution.steps
                  .filter(step => step.outputs?.agentInsights?.[agent.agentId])
                  .map(step => ({
                    stepName: step.name || step.id,
                    insight: step.outputs.agentInsights[agent.agentId],
                    consultationSummary: step.outputs.consultationSummary
                  }))
                  .filter(Boolean);

                const latestInsight = agentInsights[agentInsights.length - 1];
                const totalConsultations = agentInsights.reduce((sum, insight) =>
                  sum + (insight.consultationSummary?.totalConsultations || 0), 0);

                return (
                  <div key={agent.agentId} className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <div className="text-sm font-medium text-gray-900 capitalize">
                          {agent.agentId.replace('-', ' ')} Agent
                        </div>
                        <div className={`w-2 h-2 rounded-full ${
                          agent.status === 'analyzing' ? 'bg-blue-500 animate-pulse' :
                          agent.status === 'completed' ? 'bg-green-500' :
                          'bg-gray-400'
                        }`}></div>
                      </div>
                      <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                        agent.status === 'analyzing' ? 'bg-blue-100 text-blue-800' :
                        agent.status === 'responding' ? 'bg-green-100 text-green-800' :
                        agent.status === 'waiting' ? 'bg-yellow-100 text-yellow-800' :
                        agent.status === 'completed' ? 'bg-green-100 text-green-800' :
                        'bg-gray-100 text-gray-600'
                      }`}>
                        {agent.status}
                      </div>
                    </div>

                    {latestInsight && latestInsight.insight && (
                      <div className="text-xs text-gray-600 mt-2">
                        <div className="font-medium mb-1">Latest Insight from {latestInsight.stepName}:</div>
                        <div className="bg-white p-2 rounded border text-gray-700">
                          {typeof latestInsight.insight === 'object' ?
                            (latestInsight.insight.recommendation || latestInsight.insight.insight || 'Agent consultation completed') :
                            latestInsight.insight
                          }
                        </div>
                        {latestInsight.insight.confidence && (
                          <div className="mt-1 text-gray-500">
                            Confidence: {Math.round(latestInsight.insight.confidence * 100)}%
                          </div>
                        )}
                      </div>
                    )}

                    <div className="text-xs text-gray-500 mt-2 flex justify-between">
                      <span>Steps consulted: {agentInsights.length}</span>
                      <span>Total consultations: {totalConsultations}</span>
                    </div>

                    {agentInsights.length > 1 && (
                      <div className="mt-2 text-xs text-gray-500">
                        <details className="cursor-pointer">
                          <summary className="hover:text-gray-700">View all consultations</summary>
                          <div className="mt-2 space-y-1">
                            {agentInsights.map((insight, index) => (
                              <div key={index} className="bg-white p-2 rounded border text-gray-600">
                                <div className="font-medium">{insight.stepName}</div>
                                <div className="text-xs">
                                  {typeof insight.insight === 'object' ?
                                    (insight.insight.recommendation || insight.insight.insight || 'Consultation completed') :
                                    insight.insight
                                  }
                                </div>
                              </div>
                            ))}
                          </div>
                        </details>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">🤖</div>
              <div className="text-sm">No agent consultations yet</div>
              <div className="text-xs mt-1">Agents will appear here during workflow execution</div>
            </div>
          )}
        </div>
      </div>

      {/* Consultation Outputs */}
      {execution.steps && execution.steps.some(step => step.outputs?.consultationSummary) && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Agent Consultation Results</h3>

          <div className="space-y-4">
            {execution.steps
              .filter(step => step.outputs?.consultationSummary)
              .map((step, index) => {
                const summary = step.outputs.consultationSummary;
                const insights = step.outputs.agentInsights || {};

                return (
                  <div key={`consultation-${step.id}-${index}`} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-md font-medium text-gray-900">{step.name}</h4>
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <span>🤖 {summary.totalConsultations} consultations</span>
                        <span>•</span>
                        <span>📊 {Math.round(summary.averageConfidence * 100)}% confidence</span>
                      </div>
                    </div>

                    {summary.consultedAgents && (
                      <div className="mb-3">
                        <div className="text-sm text-gray-600 mb-2">Consulted Agents:</div>
                        <div className="flex flex-wrap gap-2">
                          {summary.consultedAgents.map((agentId: string) => (
                            <span
                              key={agentId}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                            >
                              {agentId.replace('-', ' ')}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {Object.keys(insights).length > 0 && (
                      <div className="space-y-2">
                        <div className="text-sm text-gray-600 mb-2">Key Insights:</div>
                        {Object.entries(insights).map(([agentId, insight]: [string, any]) => (
                          <div key={agentId} className="bg-gray-50 rounded-md p-3">
                            <div className="text-sm font-medium text-gray-900 mb-1 capitalize">
                              {agentId.replace('-', ' ')} Agent
                            </div>
                            <div className="text-sm text-gray-700">
                              {typeof insight === 'object' ?
                                (insight.recommendation || insight.insight || 'Consultation completed') :
                                insight
                              }
                            </div>
                            {insight.confidence && (
                              <div className="text-xs text-gray-500 mt-1">
                                Confidence: {Math.round(insight.confidence * 100)}%
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                );
              })}
          </div>
        </div>
      )}

      {/* Live Activity Feed */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Live Activity Feed</h3>
        
        <div className="bg-gray-900 rounded-lg p-4 h-48 overflow-y-auto font-mono text-sm">
          {logs.length > 0 ? (
            <div className="space-y-1">
              {logs.slice().reverse().map((log, index) => (
                <div key={index} className={`flex items-start space-x-2 ${
                  log.level === 'error' ? 'text-red-400' :
                  log.level === 'warning' ? 'text-yellow-400' :
                  log.level === 'success' ? 'text-green-400' :
                  'text-gray-300'
                }`}>
                  <span className="text-gray-500 text-xs mt-0.5 flex-shrink-0">[{log.timestamp}]</span>
                  <span className="flex-1">{log.message}</span>
                </div>
              ))}

              {/* Add some sample real-time activity if no logs */}
              {logs.length === 0 && execution.status === 'running' && (
                <div className="text-blue-400">
                  <span className="text-gray-500">[{formatTime(new Date())}]</span> 🔄 Workflow execution in progress...
                </div>
              )}
            </div>
          ) : (
            <div className="text-gray-500 text-center py-8">
              <div>📡 Waiting for activity logs...</div>
              <div className="text-xs mt-2">Real-time updates will appear here</div>
              {execution.status && (
                <div className="text-xs mt-2 text-blue-400">
                  Current status: {execution.status.replace('_', ' ').toUpperCase()}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
