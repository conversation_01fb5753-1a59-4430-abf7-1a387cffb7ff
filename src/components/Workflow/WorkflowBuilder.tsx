/**
 * Workflow Builder Component with Agent Consultation
 *
 * Enhanced workflow builder with real backend integration and agent consultation configuration
 */

'use client';

import { useState, useEffect, useId } from 'react';

interface WorkflowStep {
  id: string;
  name: string;
  type: string;
  config?: any;
  inputs?: string[];
  outputs?: string[];
  dependencies?: string[];
  consultationConfig?: {
    enabled: boolean;
    triggers?: Array<{
      type: string;
      agents: string[];
      priority: string;
      condition?: any;
    }>;
    maxConsultations?: number;
    timeoutMs?: number;
    fallbackBehavior?: string;
  };
}

interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  steps: WorkflowStep[];
  consultationEnabled: boolean;
  agentCount: number;
}

interface AgentCapability {
  agentId: string;
  capabilities: string[];
  isAvailable: boolean;
}

interface Props {
  selectedTemplate: WorkflowTemplate | null;
  onWorkflowCreate: (workflow: any) => void;
  enableAgentConsultation?: boolean;
  onNotification: (message: string) => void;
}

export default function WorkflowBuilder({
  selectedTemplate,
  onWorkflowCreate,
  enableAgentConsultation = true,
  onNotification
}: Props) {
  const uniqueId = useId();
  const [workflowName, setWorkflowName] = useState('');
  const [workflowDescription, setWorkflowDescription] = useState('');
  const [workflowSteps, setWorkflowSteps] = useState<WorkflowStep[]>([]);
  const [availableAgents, setAvailableAgents] = useState<AgentCapability[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [activeStepIndex, setActiveStepIndex] = useState<number | null>(null);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  useEffect(() => {
    if (selectedTemplate) {
      setWorkflowName(selectedTemplate.name);
      setWorkflowDescription(selectedTemplate.description);

      // Ensure all steps have required properties
      const normalizedSteps = (selectedTemplate.steps || []).map(step => ({
        ...step,
        config: step.config || {},
        inputs: step.inputs || [],
        outputs: step.outputs || [],
        dependencies: step.dependencies || [],
        consultationConfig: step.consultationConfig ? {
          ...step.consultationConfig,
          triggers: step.consultationConfig.triggers || [],
          maxConsultations: step.consultationConfig.maxConsultations || 3,
          timeoutMs: step.consultationConfig.timeoutMs || 30000,
          fallbackBehavior: step.consultationConfig.fallbackBehavior || 'continue'
        } : undefined
      }));

      setWorkflowSteps(normalizedSteps);
    }
    loadAvailableAgents();
  }, [selectedTemplate]);

  useEffect(() => {
    validateWorkflow();
  }, [workflowName, workflowSteps]);

  const loadAvailableAgents = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/agents/selection/capabilities');
      const result = await response.json();

      if (result.success) {
        setAvailableAgents(result.data.agents);
      } else {
        onNotification('Failed to load available agents');
      }
    } catch (error) {
      console.error('Failed to load agents:', error);
      onNotification('Failed to load available agents');
    } finally {
      setIsLoading(false);
    }
  };

  const validateWorkflow = () => {
    const errors: string[] = [];

    if (!workflowName.trim()) {
      errors.push('Workflow name is required');
    }

    if (workflowSteps.length === 0) {
      errors.push('At least one workflow step is required');
    }

    // Validate step dependencies
    workflowSteps.forEach((step, index) => {
      // Check if dependencies exist and is an array
      if (step.dependencies && Array.isArray(step.dependencies)) {
        step.dependencies.forEach(depId => {
          const depExists = workflowSteps.some(s => s.id === depId);
          if (!depExists) {
            errors.push(`Step "${step.name}" has invalid dependency: ${depId}`);
          }
        });
      }

      // Validate agent consultation config
      if (step.consultationConfig?.enabled) {
        if (!step.consultationConfig.triggers || step.consultationConfig.triggers.length === 0) {
          errors.push(`Step "${step.name}" has agent consultation enabled but no triggers configured`);
        } else {
          step.consultationConfig.triggers.forEach(trigger => {
            if (!trigger.agents || trigger.agents.length === 0) {
              errors.push(`Step "${step.name}" has a trigger with no agents selected`);
            }
          });
        }
      }
    });

    setValidationErrors(errors);
  };

  const handleCreateWorkflow = async () => {
    if (validationErrors.length > 0) {
      onNotification('Please fix validation errors before creating workflow');
      return;
    }

    try {
      setIsSaving(true);

      const workflow = {
        id: `workflow-${uniqueId}-${Math.random().toString(36).substr(2, 9)}`,
        name: workflowName,
        description: workflowDescription,
        steps: workflowSteps,
        template: selectedTemplate,
        agentConsultationEnabled: enableAgentConsultation && workflowSteps.some(step => step.consultationConfig?.enabled),
        createdAt: new Date().toISOString(),
        version: '1.0.0',
        metadata: {
          // Required fields for WorkflowEngine validation
          category: selectedTemplate?.category || 'blog',
          difficulty: selectedTemplate?.difficulty || 'easy',
          estimatedTime: calculateEstimatedTime(),
          // Additional metadata
          totalSteps: workflowSteps.length,
          agentEnabledSteps: workflowSteps.filter(step => step.consultationConfig?.enabled).length,
          tags: selectedTemplate?.tags || [],
          featured: selectedTemplate?.featured || false
        }
      };

      // Save workflow to backend
      const response = await fetch('/api/workflow/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(workflow)
      });

      const result = await response.json();

      if (result.success) {
        onWorkflowCreate(workflow);
        onNotification(`Workflow "${workflowName}" created successfully`);
      } else {
        onNotification(`Failed to create workflow: ${result.error}`);
      }
    } catch (error) {
      console.error('Failed to create workflow:', error);
      onNotification('Failed to create workflow');
    } finally {
      setIsSaving(false);
    }
  };

  const calculateEstimatedTime = () => {
    // Base time per step type
    const stepTimes = {
      'TEXT_INPUT': 2,
      'AI_GENERATION': 15,
      'HUMAN_REVIEW': 10,
      'CSV_IMPORT': 3,
      'CSV_EXPORT': 2,
      'URL_FETCH': 5,
      'LOOP': 20
    };

    let totalTime = 0;
    workflowSteps.forEach(step => {
      const baseTime = stepTimes[step.type as keyof typeof stepTimes] || 10;
      let stepTime = baseTime;

      // Add time for agent consultation
      if (step.consultationConfig?.enabled && step.consultationConfig.triggers) {
        const agentTime = step.consultationConfig.triggers.reduce((sum, trigger) => {
          return sum + ((trigger.agents?.length || 0) * 3); // 3 minutes per agent
        }, 0);
        stepTime += agentTime;
      }

      totalTime += stepTime;
    });

    return totalTime;
  };

  const updateStepConsultationConfig = (stepIndex: number, config: any) => {
    setWorkflowSteps(prev => prev.map((step, index) =>
      index === stepIndex
        ? { ...step, consultationConfig: { ...step.consultationConfig, ...config } }
        : step
    ));
  };

  const toggleStepConsultation = (stepIndex: number) => {
    setWorkflowSteps(prev => prev.map((step, index) =>
      index === stepIndex
        ? {
            ...step,
            consultationConfig: {
              ...step.consultationConfig,
              enabled: !step.consultationConfig?.enabled,
              triggers: step.consultationConfig?.triggers || [{
                type: 'always',
                agents: ['seo-keyword'],
                priority: 'medium'
              }],
              maxConsultations: step.consultationConfig?.maxConsultations || 3,
              timeoutMs: step.consultationConfig?.timeoutMs || 30000,
              fallbackBehavior: step.consultationConfig?.fallbackBehavior || 'continue'
            }
          }
        : step
    ));
  };

  const addAgentToStep = (stepIndex: number, triggerIndex: number, agentId: string) => {
    setWorkflowSteps(prev => prev.map((step, index) => {
      if (index === stepIndex && step.consultationConfig && step.consultationConfig.triggers) {
        const newTriggers = [...step.consultationConfig.triggers];
        if (newTriggers[triggerIndex] && newTriggers[triggerIndex].agents && !newTriggers[triggerIndex].agents.includes(agentId)) {
          newTriggers[triggerIndex].agents.push(agentId);
        }
        return {
          ...step,
          consultationConfig: {
            ...step.consultationConfig,
            triggers: newTriggers
          }
        };
      }
      return step;
    }));
  };

  const removeAgentFromStep = (stepIndex: number, triggerIndex: number, agentId: string) => {
    setWorkflowSteps(prev => prev.map((step, index) => {
      if (index === stepIndex && step.consultationConfig && step.consultationConfig.triggers) {
        const newTriggers = [...step.consultationConfig.triggers];
        if (newTriggers[triggerIndex] && newTriggers[triggerIndex].agents) {
          newTriggers[triggerIndex].agents = newTriggers[triggerIndex].agents.filter(id => id !== agentId);
        }
        return {
          ...step,
          consultationConfig: {
            ...step.consultationConfig,
            triggers: newTriggers
          }
        };
      }
      return step;
    }));
  };

  const getStepIcon = (type: string) => {
    switch (type) {
      case 'TEXT_INPUT': return '📝';
      case 'AI_GENERATION': return '🤖';
      case 'HUMAN_REVIEW': return '👤';
      case 'CSV_IMPORT': return '📊';
      case 'CSV_EXPORT': return '💾';
      case 'URL_FETCH': return '🌐';
      case 'LOOP': return '🔄';
      default: return '⚙️';
    }
  };

  const getAgentIcon = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return '🔍';
      case 'market-research': return '📊';
      case 'content-strategy': return '📝';
      default: return '🤖';
    }
  };

  const getAgentName = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return 'SEO Keyword Agent';
      case 'market-research': return 'Market Research Agent';
      case 'content-strategy': return 'Content Strategy Agent';
      default: return agentId;
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading workflow builder...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Build Your Workflow</h2>
        <p className="text-gray-600">
          Configure your workflow with intelligent agent consultation capabilities
        </p>
      </div>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Validation Errors</h3>
              <div className="mt-2 text-sm text-red-700">
                <ul className="list-disc pl-5 space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Template Info */}
      {selectedTemplate && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center space-x-3">
            <span className="text-2xl">📋</span>
            <div className="flex-1">
              <h3 className="font-medium text-gray-900">{selectedTemplate.name}</h3>
              <p className="text-sm text-gray-600">{selectedTemplate.description}</p>
              <div className="flex items-center space-x-4 mt-2">
                <span className="text-sm text-gray-500">
                  {selectedTemplate.steps?.length || 0} steps
                </span>
                {selectedTemplate.consultationEnabled && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    🤖 Agent-Enhanced ({selectedTemplate.agentCount} agents)
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Basic Workflow Configuration */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Configuration</h3>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Workflow Name *
            </label>
            <input
              type="text"
              value={workflowName}
              onChange={(e) => setWorkflowName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter workflow name..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={workflowDescription}
              onChange={(e) => setWorkflowDescription(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              placeholder="Describe what this workflow does..."
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{workflowSteps.length}</div>
              <div className="text-sm text-gray-600">Total Steps</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {workflowSteps.filter(step => step.consultationConfig?.enabled).length}
              </div>
              <div className="text-sm text-gray-600">Agent-Enhanced Steps</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{calculateEstimatedTime()}</div>
              <div className="text-sm text-gray-600">Est. Time (min)</div>
            </div>
          </div>
        </div>
      </div>

      {/* Workflow Steps Configuration */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Workflow Steps</h3>
          <span className="text-sm text-gray-500">
            Configure agent consultation for each step
          </span>
        </div>

        {workflowSteps.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 mb-4">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">No Steps Configured</h4>
            <p className="text-gray-600">
              Select a template to automatically configure workflow steps.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {workflowSteps.map((step, stepIndex) => (
              <div key={step.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{getStepIcon(step.type)}</span>
                    <div>
                      <h4 className="font-medium text-gray-900">{step.name}</h4>
                      <p className="text-sm text-gray-500">Type: {step.type}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    {step.consultationConfig?.enabled && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        🤖 Agent Consultation
                      </span>
                    )}
                    <button
                      onClick={() => setActiveStepIndex(activeStepIndex === stepIndex ? null : stepIndex)}
                      className="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      {activeStepIndex === stepIndex ? 'Collapse' : 'Configure'}
                    </button>
                  </div>
                </div>

                {/* Step Dependencies */}
                {step.dependencies && step.dependencies.length > 0 && (
                  <div className="mb-3">
                    <span className="text-sm text-gray-600">
                      Depends on: {step.dependencies.join(', ')}
                    </span>
                  </div>
                )}

                {/* Agent Consultation Toggle */}
                {enableAgentConsultation && (
                  <div className="mb-3">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={step.consultationConfig?.enabled || false}
                        onChange={() => toggleStepConsultation(stepIndex)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm font-medium text-gray-700">
                        Enable Agent Consultation for this step
                      </span>
                    </label>
                  </div>
                )}

                {/* Expanded Configuration */}
                {activeStepIndex === stepIndex && step.consultationConfig?.enabled && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <h5 className="font-medium text-gray-900 mb-3">Agent Consultation Configuration</h5>

                    {/* Consultation Settings */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Max Consultations
                        </label>
                        <input
                          type="number"
                          min="1"
                          max="10"
                          value={step.consultationConfig.maxConsultations}
                          onChange={(e) => updateStepConsultationConfig(stepIndex, {
                            maxConsultations: parseInt(e.target.value) || 3
                          })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Timeout (seconds)
                        </label>
                        <input
                          type="number"
                          min="5"
                          max="300"
                          value={step.consultationConfig.timeoutMs / 1000}
                          onChange={(e) => updateStepConsultationConfig(stepIndex, {
                            timeoutMs: (parseInt(e.target.value) || 30) * 1000
                          })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Fallback Behavior
                        </label>
                        <select
                          value={step.consultationConfig.fallbackBehavior}
                          onChange={(e) => updateStepConsultationConfig(stepIndex, {
                            fallbackBehavior: e.target.value
                          })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="continue">Continue</option>
                          <option value="fail">Fail</option>
                        </select>
                      </div>
                    </div>

                    {/* Agent Selection */}
                    <div>
                      <h6 className="text-sm font-medium text-gray-700 mb-2">Select Agents</h6>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        {availableAgents.map((agent) => {
                          const isSelected = step.consultationConfig?.triggers?.[0]?.agents?.includes(agent.agentId) || false;

                          return (
                            <label
                              key={agent.agentId}
                              className={`flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                                isSelected
                                  ? 'border-blue-500 bg-blue-50'
                                  : 'border-gray-200 hover:border-gray-300'
                              }`}
                            >
                              <input
                                type="checkbox"
                                checked={isSelected}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    addAgentToStep(stepIndex, 0, agent.agentId);
                                  } else {
                                    removeAgentFromStep(stepIndex, 0, agent.agentId);
                                  }
                                }}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                              <span className="text-lg">{getAgentIcon(agent.agentId)}</span>
                              <div className="flex-1">
                                <div className="text-sm font-medium text-gray-900">
                                  {getAgentName(agent.agentId)}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {agent.capabilities.length} capabilities
                                </div>
                              </div>
                              <div className={`w-2 h-2 rounded-full ${agent.isAvailable ? 'bg-green-500' : 'bg-red-500'}`}></div>
                            </label>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-600">
          {validationErrors.length === 0 ? (
            <span className="text-green-600">✅ Workflow is valid and ready to create</span>
          ) : (
            <span className="text-red-600">❌ Please fix validation errors above</span>
          )}
        </div>

        <div className="flex space-x-4">
          <button
            onClick={() => {
              setWorkflowName('');
              setWorkflowDescription('');
              setWorkflowSteps([]);
              setActiveStepIndex(null);
              onNotification('Workflow configuration reset');
            }}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
          >
            Reset
          </button>

          <button
            onClick={handleCreateWorkflow}
            disabled={validationErrors.length > 0 || isSaving}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSaving ? 'Creating...' : 'Create Workflow'}
          </button>
        </div>
      </div>
    </div>
  );
}
