'use client'

import React, { useState, useEffect } from 'react'
import { ContentType, ContentBriefState } from './Dashboard'
import { TextInput, TextArea, Select, Checkbox, FormButton } from '../Research/FormFields'

// Define the category options
const CATEGORIES = [
  { value: 'crm', label: 'CRM' },
  { value: 'marketing', label: 'Marketing' },
  { value: 'sales', label: 'Sales' },
  { value: 'accounting', label: 'Accounting' },
  { value: 'project-management', label: 'Project Management' },
  { value: 'hr', label: 'HR & Recruiting' },
  { value: 'ecommerce', label: 'E-Commerce' },
]

// Define the tone options
const TONE_OPTIONS = [
  { value: 'formal', label: 'Formal' },
  { value: 'friendly', label: 'Friendly' },
  { value: 'expert', label: 'Expert' },
  { value: 'conversational', label: 'Conversational' },
  { value: 'authoritative', label: 'Authoritative' },
]

// Define the audience options
const AUDIENCE_OPTIONS = [
  { value: 'small-business', label: 'Small Business Owners' },
  { value: 'enterprise', label: 'Enterprise Organizations' },
  { value: 'startups', label: 'Startups' },
  { value: 'marketers', label: 'Marketers' },
  { value: 'developers', label: 'Developers' },
  { value: 'executives', label: 'Executives' },
  { value: 'general', label: 'General Audience' },
]

interface ContentBriefFormProps {
  contentType: ContentType
  initialValues: ContentBriefState | null
  onSubmit: (data: ContentBriefState) => void
  onBack: () => void
  isLoading: boolean
}

const ContentBriefForm: React.FC<ContentBriefFormProps> = ({
  contentType,
  initialValues,
  onSubmit,
  onBack,
  isLoading
}) => {
  // Initialize form state
  const [formState, setFormState] = useState<ContentBriefState>({
    contentType,
    topicFocus: '',
    category: 'crm',
    targetAudience: 'general',
    primaryKeywords: [],
    tonePreference: 'expert',
    competitorUrls: [],
    internalData: null,
    generalInstructions: ''
  })
  
  // Form validation state
  const [errors, setErrors] = useState<{
    topicFocus?: string
  }>({})
  
  // Temporary state for keywords and competitor URLs input
  const [keywordsInput, setKeywordsInput] = useState('')
  const [competitorUrlInput, setCompetitorUrlInput] = useState('')
  
  // Update form state when initialValues or contentType changes
  useEffect(() => {
    if (initialValues) {
      setFormState(initialValues)
      
      // Update temporary inputs
      setKeywordsInput(initialValues.primaryKeywords.join(', '))
      setCompetitorUrlInput(initialValues.competitorUrls?.join('\n') || '')
    } else {
      setFormState(prev => ({
        ...prev,
        contentType
      }))
    }
  }, [initialValues, contentType])
  
  // Handle form input changes
  const handleInputChange = (name: string, value: any) => {
    setFormState(prev => ({ ...prev, [name]: value }))
    
    // Clear validation errors when field is updated
    if (errors[name as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }))
    }
  }
  
  // Handle keywords input
  const handleKeywordsChange = (value: string) => {
    setKeywordsInput(value)
    
    // Parse keywords from comma-separated list
    const keywords = value
      .split(',')
      .map(keyword => keyword.trim())
      .filter(keyword => keyword.length > 0)
    
    handleInputChange('primaryKeywords', keywords)
  }
  
  // Handle competitor URLs input
  const handleCompetitorUrlsChange = (value: string) => {
    setCompetitorUrlInput(value)
    
    // Parse URLs from newline-separated list
    const urls = value
      .split('\n')
      .map(url => url.trim())
      .filter(url => url.length > 0)
    
    handleInputChange('competitorUrls', urls)
  }
  
  // Validate form before submission
  const validateForm = (): boolean => {
    const newErrors: {
      topicFocus?: string
    } = {}
    
    if (!formState.topicFocus.trim()) {
      newErrors.topicFocus = 'Content topic is required'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  
  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (validateForm()) {
      onSubmit(formState)
    }
  }
  
  // Get content type specific title
  const getContentTypeTitle = (): string => {
    switch (contentType) {
      case 'product-page':
        return 'Product Page'
      case 'blog-article':
        return 'Blog Article'
      case 'buying-guide':
        return 'Buying Guide'
      default:
        return 'Content'
    }
  }
  
  return (
    <div className="content-brief-form">
      <div className="form-header">
        <button className="back-button" onClick={onBack} disabled={isLoading}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <line x1="19" y1="12" x2="5" y2="12"></line>
            <polyline points="12 19 5 12 12 5"></polyline>
          </svg>
          Back
        </button>
        <h2>Create {getContentTypeTitle()} Brief</h2>
      </div>
      
      <form onSubmit={handleSubmit}>
        <div className="form-section">
          <h3>Basic Information</h3>
          
          <TextInput
            label="Content Topic Focus"
            value={formState.topicFocus}
            onChange={(value) => handleInputChange('topicFocus', value)}
            placeholder={`e.g., "Comparison of Enterprise CRM Software"`}
            required
            disabled={isLoading}
          />
          {errors.topicFocus && <div className="error-message">{errors.topicFocus}</div>}
          
          <Select
            label="Software Category"
            value={formState.category}
            options={CATEGORIES}
            onChange={(value) => handleInputChange('category', value)}
            required
            disabled={isLoading}
          />
          
          <Select
            label="Target Audience"
            value={formState.targetAudience}
            options={AUDIENCE_OPTIONS}
            onChange={(value) => handleInputChange('targetAudience', value)}
            required
            disabled={isLoading}
          />
          
          <Select
            label="Tone Preference"
            value={formState.tonePreference}
            options={TONE_OPTIONS}
            onChange={(value) => handleInputChange('tonePreference', value)}
            required
            disabled={isLoading}
          />
        </div>
        
        <div className="form-section">
          <h3>SEO & Research</h3>
          
          <TextArea
            label="Primary Keywords"
            value={keywordsInput}
            onChange={handleKeywordsChange}
            placeholder="Enter keywords separated by commas"
            rows={3}
            disabled={isLoading}
          />
          
          <TextArea
            label="Competitor References (Optional)"
            value={competitorUrlInput}
            onChange={handleCompetitorUrlsChange}
            placeholder="Enter competitor URLs, one per line"
            rows={4}
            disabled={isLoading}
          />
        </div>
        
        <div className="form-section">
          <h3>Additional Instructions</h3>
          
          <TextArea
            label="General Instructions (Optional)"
            value={formState.generalInstructions || ''}
            onChange={(value) => handleInputChange('generalInstructions', value)}
            placeholder="Any specific instructions or requirements for this content"
            rows={4}
            disabled={isLoading}
          />
        </div>
        
        <div className="form-actions">
          <FormButton
            type="submit"
            disabled={isLoading}
            icon={
              isLoading ? (
                <svg
                  className="spinner"
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M5 12h14"></path>                  
                  <path d="M5 12h14"></path>
                                    <path d="M12 5l7 7-7 7"></path>
                                  </svg>
                                )
                              }
                            >
                              {isLoading ? 'Generating...' : 'Generate Content'}
                            </FormButton>
                          </div>
                        </form>
                        
                        <style jsx>{`
                          .content-brief-form {
                            margin-bottom: 2rem;
                          }
                          
                          .form-header {
                            display: flex;
                            align-items: center;
                            margin-bottom: 1.5rem;
                          }
                          
                          .back-button {
                            display: flex;
                            align-items: center;
                            background: none;
                            border: none;
                            color: var(--theme-elevation-500);
                            padding: 0.5rem;
                            margin-right: 1rem;
                            cursor: pointer;
                            font-size: 0.9rem;
                          }
                          
                          .back-button svg {
                            margin-right: 0.25rem;
                          }
                          
                          .back-button:hover {
                            color: var(--theme-elevation-800);
                          }
                          
                          .back-button:disabled {
                            opacity: 0.5;
                            cursor: not-allowed;
                          }
                          
                          h2 {
                            font-size: 1.5rem;
                            font-weight: 600;
                            margin: 0;
                          }
                          
                          .form-section {
                            margin-bottom: 2rem;
                            padding-bottom: 1.5rem;
                            border-bottom: 1px solid var(--theme-elevation-100);
                          }
                          
                          .form-section:last-child {
                            border-bottom: none;
                          }
                          
                          h3 {
                            font-size: 1.2rem;
                            font-weight: 600;
                            margin-bottom: 1rem;
                            color: var(--theme-elevation-800);
                          }
                          
                          .error-message {
                            color: #cc0000;
                            font-size: 0.9rem;
                            margin-top: -0.5rem;
                            margin-bottom: 1rem;
                          }
                          
                          .form-actions {
                            display: flex;
                            justify-content: flex-end;
                            margin-top: 2rem;
                          }
                          
                          .spinner {
                            animation: spin 1.5s linear infinite;
                          }
                          
                          @keyframes spin {
                            0% {
                              transform: rotate(0deg);
                            }
                            100% {
                              transform: rotate(360deg);
                            }
                          }
                        `}</style>
                      </div>
                    )
                  }
                  
                  export default ContentBriefForm