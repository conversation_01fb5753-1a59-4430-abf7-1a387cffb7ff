'use client'

import React, { useState } from 'react'

interface SimpleContentPreviewProps {
  content: any
  onPublish?: () => void
}

// Helper function to extract content from LangChain message format if needed
const extractContent = (content: any): any => {
  // If it's a string, try to parse it as JSON
  if (typeof content === 'string') {
    try {
      const parsed = JSON.parse(content);
      // Check if it's a LangChain message object
      if (parsed.lc === 1 && 
          parsed.type === 'constructor' && 
          parsed.kwargs && 
          parsed.kwargs.content) {
        return parsed.kwargs.content;
      }
      return content;
    } catch (e) {
      // If parsing fails, return the original string
      return content;
    }
  }
  
  // If it's an object, check if it's a LangChain message object
  if (typeof content === 'object' && content !== null) {
    // Check for LangChain message structure
    if (content.lc === 1 && 
        content.type === 'constructor' && 
        content.kwargs && 
        content.kwargs.content) {
      return content.kwargs.content;
    }
    
    // If it's an array, process each item
    if (Array.isArray(content)) {
      return content.map(item => extractContent(item));
    }
    
    // If it's an object, process each property
    const processed: any = {};
    for (const key in content) {
      processed[key] = extractContent(content[key]);
    }
    return processed;
  }
  
  // Default case - return unchanged
  return content;
};

const SimpleContentPreview: React.FC<SimpleContentPreviewProps> = ({
  content,
  onPublish
}) => {
  // State for tabs
  const [activeTab, setActiveTab] = useState<'article' | 'metadata' | 'reasoning'>('article');
  
  // Ensure content is available
  if (!content) {
    return (
      <div className="text-center py-8 text-gray-500">
        No content available
      </div>
    )
  }
  
  // Process content to handle any LangChain message formats
  const processedContent = extractContent(content);
  
  // Render section based on type
  const renderSection = (section: any, index: number) => {
    const processedSectionContent = extractContent(section.content);
    
    switch (section.type?.toLowerCase()) {
      case 'heading':
        return (
          <div key={section.id || index} className="mb-6">
            <h2 className="text-2xl font-bold mb-4">{section.title}</h2>
            <div className="prose max-w-none">
              {typeof processedSectionContent === 'string' 
                ? processedSectionContent.split('\n').map((paragraph, i) => (
                    paragraph.trim() ? <p key={i} className="mb-4">{paragraph}</p> : null
                  ))
                : JSON.stringify(processedSectionContent)
              }
            </div>
          </div>
        );
        
      case 'text':
        return (
          <div key={section.id || index} className="mb-6">
            <div className="prose max-w-none">
              {typeof processedSectionContent === 'string' 
                ? processedSectionContent.split('\n').map((paragraph, i) => (
                    paragraph.trim() ? <p key={i} className="mb-4">{paragraph}</p> : null
                  ))
                : JSON.stringify(processedSectionContent)
              }
            </div>
          </div>
        );
        
      case 'list':
        const listItems = Array.isArray(processedSectionContent) 
          ? processedSectionContent 
          : typeof processedSectionContent === 'string' 
            ? processedSectionContent.split('\n').filter(item => item.trim() !== '') 
            : [];
            
        return (
          <div key={section.id || index} className="mb-6">
            {section.title && <h3 className="text-xl font-semibold mb-3">{section.title}</h3>}
            <ul className="list-disc pl-6 space-y-2">
              {listItems.map((item, i) => (
                <li key={i}>{item}</li>
              ))}
            </ul>
          </div>
        );
        
      case 'faq':
        const faqItems = Array.isArray(processedSectionContent) 
          ? processedSectionContent 
          : [];
          
        return (
          <div key={section.id || index} className="mb-6">
            {section.title && <h3 className="text-xl font-semibold mb-3">{section.title}</h3>}
            <div className="space-y-4">
              {faqItems.map((faq: any, i: number) => (
                <div key={i} className="border-b pb-4">
                  <h4 className="font-medium text-lg mb-2">{faq.question}</h4>
                  <div className="prose">
                    {typeof faq.answer === 'string' 
                      ? faq.answer.split('\n').map((paragraph: string, j: number) => (
                          paragraph.trim() ? <p key={j} className="mb-2">{paragraph}</p> : null
                        ))
                      : JSON.stringify(faq.answer)
                    }
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
        
      default:
        // For any other section type or if type is missing
        return (
          <div key={section.id || index} className="mb-6">
            {section.title && <h3 className="text-xl font-semibold mb-3">{section.title}</h3>}
            <div className="prose max-w-none">
              {typeof processedSectionContent === 'string' 
                ? processedSectionContent.split('\n').map((paragraph, i) => (
                    paragraph.trim() ? <p key={i} className="mb-4">{paragraph}</p> : null
                  ))
                : JSON.stringify(processedSectionContent)
              }
            </div>
          </div>
        );
    }
  };

  return (
    <div className="content-preview">
      <div className="mb-6 border-b">
        <div className="flex space-x-4">
          <button 
            className={`px-4 py-2 ${activeTab === 'article' ? 'border-b-2 border-blue-500 text-blue-600 font-medium' : 'text-gray-600'}`}
            onClick={() => setActiveTab('article')}
          >
            Complete Article
          </button>
          <button 
            className={`px-4 py-2 ${activeTab === 'metadata' ? 'border-b-2 border-blue-500 text-blue-600 font-medium' : 'text-gray-600'}`}
            onClick={() => setActiveTab('metadata')}
          >
            Metadata & SEO
          </button>
          <button 
            className={`px-4 py-2 ${activeTab === 'reasoning' ? 'border-b-2 border-blue-500 text-blue-600 font-medium' : 'text-gray-600'}`}
            onClick={() => setActiveTab('reasoning')}
          >
            Reasoning
          </button>
        </div>
      </div>
      
      {activeTab === 'article' && (
        <div className="article-view">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-4">{processedContent.title || 'Untitled'}</h1>
            <div className="text-gray-600 italic mb-6">{processedContent.metaDescription || 'No meta description available'}</div>
            
            {/* Full article content */}
            <div className="article-content">
              {processedContent.sections && processedContent.sections.length > 0 ? (
                processedContent.sections.map((section: any, index: number) => renderSection(section, index))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No content sections available
                </div>
              )}
            </div>
          </div>
          
          {onPublish && (
            <div className="actions mt-6 flex justify-end">
              <button 
                onClick={onPublish}
                className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
              >
                Publish Content
              </button>
            </div>
          )}
        </div>
      )}
      
      {activeTab === 'metadata' && (
        <div className="metadata-view">
          <div className="content-meta grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="meta-item p-4 border rounded-md">
              <div className="meta-label font-medium mb-1">Title</div>
              <div className="meta-title">{processedContent.title || 'Untitled'}</div>
            </div>
            
            <div className="meta-item p-4 border rounded-md">
              <div className="meta-label font-medium mb-1">Meta Description</div>
              <div className="meta-description">{processedContent.metaDescription || 'No meta description available'}</div>
            </div>

            <div className="meta-item p-4 border rounded-md">
              <div className="meta-label font-medium mb-1">Topic</div>
              <div>{processedContent.topic || 'No topic specified'}</div>
            </div>

            <div className="meta-item p-4 border rounded-md">
              <div className="meta-label font-medium mb-1">Content Type</div>
              <div>{processedContent.contentType || 'Not specified'}</div>
            </div>

            <div className="meta-item p-4 border rounded-md">
              <div className="meta-label font-medium mb-1">Target Audience</div>
              <div>{processedContent.targetAudience || 'Not specified'}</div>
            </div>

            <div className="meta-item p-4 border rounded-md">
              <div className="meta-label font-medium mb-1">Tone</div>
              <div>{processedContent.tone || 'Not specified'}</div>
            </div>
          </div>
          
          {processedContent.seoScore && (
            <div className="seo-score mt-6 p-4 border rounded-md">
              <h3 className="text-lg font-semibold mb-4">SEO Score</h3>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="p-3 text-center bg-gray-50 rounded-md">
                  <div className="text-sm text-gray-500 mb-1">Overall</div>
                  <div className="text-xl font-bold text-blue-600">{processedContent.seoScore.overall}/100</div>
                </div>
                <div className="p-3 text-center bg-gray-50 rounded-md">
                  <div className="text-sm text-gray-500 mb-1">Keywords</div>
                  <div className="text-xl font-bold text-blue-600">{processedContent.seoScore.keywordUsage}/100</div>
                </div>
                <div className="p-3 text-center bg-gray-50 rounded-md">
                  <div className="text-sm text-gray-500 mb-1">Readability</div>
                  <div className="text-xl font-bold text-blue-600">{processedContent.seoScore.readability}/100</div>
                </div>
                <div className="p-3 text-center bg-gray-50 rounded-md">
                  <div className="text-sm text-gray-500 mb-1">Structure</div>
                  <div className="text-xl font-bold text-blue-600">{processedContent.seoScore.structure}/100</div>
                </div>
                <div className="p-3 text-center bg-gray-50 rounded-md">
                  <div className="text-sm text-gray-500 mb-1">Meta Data</div>
                  <div className="text-xl font-bold text-blue-600">{processedContent.seoScore.metaData}/100</div>
                </div>
              </div>
            </div>
          )}
          
          {processedContent.internalLinks && processedContent.internalLinks.length > 0 && (
            <div className="internal-links mt-6 p-4 border rounded-md">
              <h3 className="text-lg font-semibold mb-4">Internal Linking Suggestions</h3>
              <div className="divide-y">
                {processedContent.internalLinks.map((link: any, index: number) => (
                  <div key={index} className="py-3">
                    <div className="font-medium">{link.anchorText}</div>
                    <div className="text-sm text-blue-600">{link.targetURL}</div>
                    <div className="text-sm text-gray-500 mt-1">
                      Links to: {link.targetTitle} ({link.relevance}% relevance)
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {processedContent.improvementSuggestions && processedContent.improvementSuggestions.length > 0 && (
            <div className="improvement-suggestions mt-6 p-4 border rounded-md">
              <h3 className="text-lg font-semibold mb-4">Improvement Suggestions</h3>
              <div className="divide-y">
                {processedContent.improvementSuggestions.map((suggestion: any, index: number) => (
                  <div key={index} className="py-3">
                    <div className="flex items-center">
                      <span className={`inline-block w-2 h-2 rounded-full mr-2 ${suggestion.priority === 'high' ? 'bg-red-500' : suggestion.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'}`}></span>
                      <span className="font-medium">{suggestion.area}</span>
                      <span className="ml-2 text-xs px-2 py-1 rounded-full bg-gray-100">{suggestion.priority} priority</span>
                    </div>
                    <div className="mt-1 ml-4">{suggestion.suggestion}</div>
                    {suggestion.reasoning && (
                      <div className="mt-2 ml-4 text-sm text-gray-600 italic">
                        <span className="font-medium">Decision:</span> {suggestion.reasoning.decision}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {onPublish && (
            <div className="actions mt-6 flex justify-end">
              <button 
                onClick={onPublish}
                className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
              >
                Publish Content
              </button>
            </div>
          )}
        </div>
      )}
      
      {/* Reasoning tab */}
      {activeTab === 'reasoning' && (
        <div className="reasoning-view p-6 bg-white rounded-lg shadow-sm">
          <h2 className="text-2xl font-bold mb-6">Chain-of-Thought Reasoning</h2>
          
          {processedContent.iterations && processedContent.iterations.length > 0 ? (
            <div className="iterations space-y-8">
              {processedContent.iterations.map((iteration: any, index: number) => (
                <div key={index} className="iteration p-4 border rounded-md">
                  <h3 className="text-lg font-semibold mb-2">Iteration {iteration.version}</h3>
                  <div className="text-sm text-gray-500 mb-4">{new Date(iteration.timestamp).toLocaleString()}</div>
                  
                  {iteration.reasoning ? (
                    <div className="reasoning-details">
                      {iteration.reasoning.thoughts && iteration.reasoning.thoughts.length > 0 && (
                        <div className="mb-4">
                          <h4 className="font-medium mb-2">Thoughts</h4>
                          <ul className="list-disc pl-5 space-y-1">
                            {iteration.reasoning.thoughts.map((thought: string, i: number) => (
                              <li key={i} className="text-gray-700">{thought}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      {iteration.reasoning.considerations && iteration.reasoning.considerations.length > 0 && (
                        <div className="mb-4">
                          <h4 className="font-medium mb-2">Considerations</h4>
                          <ul className="list-disc pl-5 space-y-1">
                            {iteration.reasoning.considerations.map((consideration: string, i: number) => (
                              <li key={i} className="text-gray-700">{consideration}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      {iteration.reasoning.alternatives && iteration.reasoning.alternatives.length > 0 && (
                        <div className="mb-4">
                          <h4 className="font-medium mb-2">Alternatives Considered</h4>
                          <ul className="list-disc pl-5 space-y-1">
                            {iteration.reasoning.alternatives.map((alternative: string, i: number) => (
                              <li key={i} className="text-gray-700">{alternative}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      {iteration.reasoning.decision && (
                        <div className="mb-4">
                          <h4 className="font-medium mb-2">Decision</h4>
                          <div className="p-3 bg-blue-50 rounded text-gray-800">
                            {iteration.reasoning.decision}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-gray-500 italic">No reasoning data available for this iteration</div>
                  )}
                </div>
              ))}
            </div>
          ) : processedContent.reasoning ? (
            <div className="reasoning-details p-4 border rounded-md">
              {processedContent.reasoning.thoughts && processedContent.reasoning.thoughts.length > 0 && (
                <div className="mb-4">
                  <h4 className="font-medium mb-2">Thoughts</h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {processedContent.reasoning.thoughts.map((thought: string, i: number) => (
                      <li key={i} className="text-gray-700">{thought}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {processedContent.reasoning.considerations && processedContent.reasoning.considerations.length > 0 && (
                <div className="mb-4">
                  <h4 className="font-medium mb-2">Considerations</h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {processedContent.reasoning.considerations.map((consideration: string, i: number) => (
                      <li key={i} className="text-gray-700">{consideration}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {processedContent.reasoning.alternatives && processedContent.reasoning.alternatives.length > 0 && (
                <div className="mb-4">
                  <h4 className="font-medium mb-2">Alternatives Considered</h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {processedContent.reasoning.alternatives.map((alternative: string, i: number) => (
                      <li key={i} className="text-gray-700">{alternative}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {processedContent.reasoning.decision && (
                <div className="mb-4">
                  <h4 className="font-medium mb-2">Decision</h4>
                  <div className="p-3 bg-blue-50 rounded text-gray-800">
                    {processedContent.reasoning.decision}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No reasoning data available
            </div>
          )}
        </div>
      )}
      
      <style jsx>{`
        .article-view {
          max-width: 800px;
          margin: 0 auto;
        }
        
        .prose {
          color: #374151;
          line-height: 1.75;
        }
        
        .prose p {
          margin-bottom: 1.25em;
        }
        
        .prose h1 {
          font-size: 2.25em;
          font-weight: 700;
          margin-top: 0;
          margin-bottom: 0.8888889em;
          line-height: 1.1111111;
        }
        
        .prose h2 {
          font-size: 1.5em;
          font-weight: 700;
          margin-top: 2em;
          margin-bottom: 1em;
          line-height: 1.3333333;
        }
        
        .prose h3 {
          font-size: 1.25em;
          font-weight: 600;
          margin-top: 1.6em;
          margin-bottom: 0.6em;
          line-height: 1.6;
        }
        
        .prose ul {
          margin-top: 1.25em;
          margin-bottom: 1.25em;
        }
        
        .prose li {
          margin-top: 0.5em;
          margin-bottom: 0.5em;
        }
      `}</style>
    </div>
  )
}

export default SimpleContentPreview
