// src/components/ContentGeneration/ContentTypeSelector.tsx
'use client'

import React from 'react'
import { ContentType } from './Dashboard'

interface ContentTypeOption {
  type: ContentType
  title: string
  description: string
  icon: React.ReactNode
}

interface ContentTypeSelectorProps {
  onSelect: (type: ContentType) => void
}

const ContentTypeSelector: React.FC<ContentTypeSelectorProps> = ({ onSelect }) => {
  // Define content type options
  const contentTypeOptions: ContentTypeOption[] = [
    {
      type: 'product-page',
      title: 'Product Page',
      description: 'Create a comprehensive product page with features, pricing, and comparisons.',
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="48"
          height="48"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
          <line x1="8" y1="21" x2="16" y2="21"></line>
          <line x1="12" y1="17" x2="12" y2="21"></line>
        </svg>
      )
    },
    {
      type: 'blog-article',
      title: 'Blog Article',
      description: 'Generate an informative blog article with introduction, body, and conclusion.',
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="48"
          height="48"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10 9 9 9 8 9"></polyline>
        </svg>
      )
    },
    {
      type: 'buying-guide',
      title: 'Buying Guide',
      description: 'Create a detailed buying guide with considerations, top picks, and recommendations.',
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="48"
          height="48"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <circle cx="9" cy="21" r="1"></circle>
          <circle cx="20" cy="21" r="1"></circle>
          <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
        </svg>
      )
    }
  ]

  return (
    <div className="content-type-selector">
      <h2>Select Content Type</h2>
      <p className="description">Choose the type of content you want to generate:</p>
      
      <div className="content-type-options">
        {contentTypeOptions.map((option) => (
          <div 
            key={option.type}
            className="content-type-card"
            onClick={() => onSelect(option.type)}
          >
            <div className="content-type-icon">{option.icon}</div>
            <h3>{option.title}</h3>
            <p>{option.description}</p>
          </div>
        ))}
      </div>
      
      <style jsx>{`
        .content-type-selector {
          margin-bottom: 2rem;
        }
        
        h2 {
          font-size: 1.5rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
        }
        
        .description {
          color: var(--theme-elevation-500);
          margin-bottom: 1.5rem;
        }
        
        .content-type-options {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 1.5rem;
        }
        
        .content-type-card {
          background-color: var(--theme-elevation-50);
          border: 1px solid var(--theme-elevation-100);
          border-radius: 8px;
          padding: 1.5rem;
          transition: all 0.2s ease;
          cursor: pointer;
          text-align: center;
        }
        
        .content-type-card:hover {
          border-color: var(--theme-elevation-300);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .content-type-icon {
          display: flex;
          justify-content: center;
          margin-bottom: 1rem;
          color: var(--theme-elevation-800);
        }
        
        .content-type-card h3 {
          font-size: 1.2rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
        }
        
        .content-type-card p {
          color: var(--theme-elevation-600);
          font-size: 0.9rem;
          line-height: 1.4;
        }
      `}</style>
    </div>
  )
}

export default ContentTypeSelector