'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Snackbar,
  Alert,
  CircularProgress
} from '@mui/material';
import { useRouter } from 'next/navigation';

// Components
import ArticleInitiationForm from '../DynamicCollaborationV3/ArticleInitiationFormV3';

/**
 * GoalBasedDashboardHome component
 * This is a simplified version of the dashboard that only shows the form to start a new session
 */
const GoalBasedDashboardHome: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Debug logging function
  const debugLog = (message: string, data?: any) => {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0]; // HH:MM:SS
    console.log(`[${timestamp}] ${message}`, data || '');
  };

  // Redirect to session page
  const redirectToSessionPage = (sid: string) => {
    if (typeof window !== 'undefined' && sid) {
      debugLog(`Redirecting to session page for ID: ${sid}`);
      // Use path parameter format as required
      router.push(`/goal-based-collaboration/${sid}`);
    }
  };

  // Start a new collaboration session using direct API call
  const startNewSession = async (formData: any) => {
    debugLog('=== STARTING NEW SESSION DIRECTLY ===');
    setLoading(true);
    setError(null);

    try {
      debugLog('Starting new session with form data:', formData);

      // Make a direct API call to initiate the session
      debugLog('Making direct API call to initiate session');
      const response = await fetch('/api/agents/dynamic-collaboration-v3/goal-based', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData),
        cache: 'no-store'
      });

      debugLog('Session initiation response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        debugLog(`HTTP error ${response.status}: ${response.statusText}`, errorText);
        throw new Error(`HTTP error ${response.status}: ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      debugLog('Session initiation result:', result);

      if (!result) {
        debugLog('ERROR: No result returned from initiate call');
        throw new Error('No result returned from the server');
      }

      if (!result.sessionId) {
        debugLog('ERROR: No sessionId in result:', result);
        throw new Error('No session ID returned from the server');
      }

      const newSessionId = result.sessionId;
      debugLog(`Setting session ID to: ${newSessionId}`);

      // Redirect to the session page
      redirectToSessionPage(newSessionId);

      // Show success message
      setSnackbar({
        open: true,
        message: 'New goal-based collaboration session started successfully',
        severity: 'success'
      });

      // Wait a moment for the session to be fully initialized on the backend
      debugLog('Waiting for session to initialize on the backend...');
      await new Promise(resolve => setTimeout(resolve, 1000));

      debugLog('=== SESSION INITIALIZATION COMPLETE ===');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      console.error('Error starting new session:', errorMessage);
      debugLog('ERROR starting new session:', errorMessage);
      setError(errorMessage);

      setSnackbar({
        open: true,
        message: `Error: ${errorMessage}`,
        severity: 'error'
      });
    } finally {
      debugLog('Setting loading to false');
      setLoading(false);
    }
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Box>
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      {loading ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            Creating Session
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Please wait while we set up your collaboration session...
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', flexDirection: 'column', alignItems: 'center', mt: 3 }}>
            <CircularProgress size={40} sx={{ mb: 2 }} />
            <Typography variant="caption" color="text.secondary">
              This may take a few moments as we initialize the AI agents and set up the workflow.
            </Typography>
          </Box>
        </Paper>
      ) : (
        <ArticleInitiationForm onSubmit={startNewSession} loading={loading} />
      )}
    </Box>
  );
};

export default GoalBasedDashboardHome;
