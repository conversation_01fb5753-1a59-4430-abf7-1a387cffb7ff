'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  IconButton,
  Tooltip,
  Card,
  CardContent,
  Divider,
  Chip,
  useTheme
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import InfoIcon from '@mui/icons-material/Info';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import dynamic from 'next/dynamic';

// Dynamically import ForceGraph2D with no SSR to avoid window is not defined error
const ForceGraph2D = dynamic(() => import('react-force-graph-2d'), {
  ssr: false,
  loading: () => <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
    <CircularProgress />
  </Box>
});

interface GraphNode {
  id: string;
  name: string;
  val: number;
  color: string;
  type: 'agent' | 'artifact' | 'goal';
  x?: number;
  y?: number;
}

interface GraphLink {
  source: string;
  target: string;
  value: number;
  type: string;
  label?: string;
  color?: string;
}

interface AgentCollaborationGraphForGoalsProps {
  sessionId: string;
  state: any;
  loading?: boolean;
  onRefresh?: () => void;
}

const AgentCollaborationGraphForGoals: React.FC<AgentCollaborationGraphForGoalsProps> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const theme = useTheme();
  const graphRef = useRef<any>();
  const [graphData, setGraphData] = useState<{ nodes: GraphNode[], links: GraphLink[] }>({ nodes: [], links: [] });
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [selectedLink, setSelectedLink] = useState<GraphLink | null>(null);
  const [hoveredNode, setHoveredNode] = useState<GraphNode | null>(null);

  // Agent colors and names
  const agentColors: Record<string, string> = {
    'market-research': theme.palette.primary.main,
    'seo-keyword': theme.palette.secondary.main,
    'content-strategy': theme.palette.info.main,
    'content-generation': theme.palette.success.main,
    'seo-optimization': theme.palette.warning.main,
    'system': theme.palette.grey[500],
    'user': theme.palette.error.main
  };

  const agentNames: Record<string, string> = {
    'market-research': 'Market Research',
    'seo-keyword': 'SEO Keyword',
    'content-strategy': 'Content Strategy',
    'content-generation': 'Content Writer',
    'seo-optimization': 'SEO Optimizer',
    'system': 'System',
    'user': 'User'
  };

  // Build graph data from state
  useEffect(() => {
    if (!state) return;

    const nodes: GraphNode[] = [];
    const links: GraphLink[] = [];
    const nodeMap = new Map<string, GraphNode>();
    const linkMap = new Map<string, GraphLink>();

    // Add agent nodes
    const addAgentNode = (agentId: string) => {
      if (!nodeMap.has(agentId)) {
        const node: GraphNode = {
          id: agentId,
          name: agentNames[agentId] || agentId,
          val: 5, // Base size
          color: agentColors[agentId] || theme.palette.grey[500],
          type: 'agent'
        };
        nodes.push(node);
        nodeMap.set(agentId, node);
      }
    };

    // Add artifact nodes
    if (state.artifacts) {
      Object.entries(state.artifacts).forEach(([artifactId, artifact]: [string, any]) => {
        if (artifact) {
          const node: GraphNode = {
            id: artifactId,
            name: artifact.name || `Artifact ${artifactId.substring(0, 6)}`,
            val: 3,
            color: theme.palette.grey[300],
            type: 'artifact'
          };
          nodes.push(node);
          nodeMap.set(artifactId, node);

          // Add link from creator to artifact
          if (artifact.creator) {
            addAgentNode(artifact.creator);
            const linkId = `${artifact.creator}-${artifactId}`;
            if (!linkMap.has(linkId)) {
              const link: GraphLink = {
                source: artifact.creator,
                target: artifactId,
                value: 1,
                type: 'created',
                label: 'created',
                color: theme.palette.success.light
              };
              links.push(link);
              linkMap.set(linkId, link);
            }
          }
        }
      });
    }

    // Add goal nodes
    if (state.goals && state.goals.byId) {
      Object.entries(state.goals.byId).forEach(([goalId, goal]: [string, any]) => {
        const node: GraphNode = {
          id: goalId,
          name: goal.description || `Goal ${goalId.substring(0, 6)}`,
          val: 4,
          color: goal.status === 'COMPLETED' ? theme.palette.success.light : 
                 goal.status === 'ACTIVE' ? theme.palette.info.light : 
                 theme.palette.grey[400],
          type: 'goal'
        };
        nodes.push(node);
        nodeMap.set(goalId, node);

        // Add links from assigned agents to goal
        if (goal.assignedTo) {
          const assignedAgents = Array.isArray(goal.assignedTo) ? goal.assignedTo : [goal.assignedTo];
          assignedAgents.forEach(agentId => {
            addAgentNode(agentId);
            const linkId = `${agentId}-${goalId}`;
            if (!linkMap.has(linkId)) {
              const link: GraphLink = {
                source: agentId,
                target: goalId,
                value: 1,
                type: 'assigned',
                label: 'assigned to',
                color: theme.palette.info.main
              };
              links.push(link);
              linkMap.set(linkId, link);
            }
          });
        }
      });
    }

    // Add links between goals and artifacts
    if (state.goals?.byId && state.artifacts) {
      Object.entries(state.artifacts).forEach(([artifactId, artifact]: [string, any]) => {
        if (artifact && artifact.goalId && nodeMap.has(artifact.goalId) && nodeMap.has(artifactId)) {
          const linkId = `${artifact.goalId}-${artifactId}`;
          if (!linkMap.has(linkId)) {
            const link: GraphLink = {
              source: artifact.goalId,
              target: artifactId,
              value: 1,
              type: 'produced',
              label: 'produced',
              color: theme.palette.success.main
            };
            links.push(link);
            linkMap.set(linkId, link);
          }
        }
      });
    }

    setGraphData({ nodes, links });
  }, [state, theme]);

  // Handle node click
  const handleNodeClick = (node: GraphNode) => {
    setSelectedNode(node === selectedNode ? null : node);
    setSelectedLink(null);
  };

  // Handle link click
  const handleLinkClick = (link: GraphLink) => {
    setSelectedLink(link === selectedLink ? null : link);
    setSelectedNode(null);
  };

  // Zoom controls
  const zoomIn = () => {
    if (graphRef.current) {
      const currentZoom = graphRef.current.zoom();
      graphRef.current.zoom(currentZoom * 1.2, 400);
    }
  };

  const zoomOut = () => {
    if (graphRef.current) {
      const currentZoom = graphRef.current.zoom();
      graphRef.current.zoom(currentZoom / 1.2, 400);
    }
  };

  const resetZoom = () => {
    if (graphRef.current) {
      graphRef.current.zoomToFit(400, 50);
    }
  };

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2, height: '100%', minHeight: '500px' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Agent Collaboration Network</Typography>
        <Box>
          <Tooltip title="Zoom In">
            <IconButton onClick={zoomIn} size="small">
              <ZoomInIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Zoom Out">
            <IconButton onClick={zoomOut} size="small">
              <ZoomOutIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Reset View">
            <IconButton onClick={resetZoom} size="small">
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      <Box sx={{ display: 'flex', height: 'calc(100% - 50px)', minHeight: '450px' }}>
        <Box sx={{ flexGrow: 1, position: 'relative', border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <CircularProgress />
            </Box>
          ) : graphData.nodes.length === 0 ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <Typography variant="body1" color="text.secondary">
                No collaboration data available
              </Typography>
            </Box>
          ) : (
            <ForceGraph2D
              ref={graphRef}
              graphData={graphData}
              nodeLabel={(node: GraphNode) => `${node.name} (${node.type})`}
              nodeColor={(node: GraphNode) => node.color}
              nodeVal={(node: GraphNode) => node.val}
              linkWidth={(link: GraphLink) => Math.sqrt(link.value) * 0.5}
              linkColor={(link: GraphLink) => link.color || theme.palette.grey[400]}
              linkDirectionalParticles={4}
              linkDirectionalParticleWidth={(link: GraphLink) => Math.sqrt(link.value) * 0.5}
              onNodeClick={handleNodeClick}
              onLinkClick={handleLinkClick}
              onNodeHover={setHoveredNode}
              cooldownTicks={100}
              linkDirectionalParticleSpeed={0.01}
              nodeCanvasObject={(node, ctx, globalScale) => {
                const label = node.name;
                const fontSize = 12/globalScale;
                ctx.font = `${fontSize}px Sans-Serif`;
                const textWidth = ctx.measureText(label).width;
                const bckgDimensions = [textWidth, fontSize].map(n => n + fontSize * 0.2);

                ctx.fillStyle = node.color;
                ctx.beginPath();
                ctx.arc(node.x!, node.y!, node.val, 0, 2 * Math.PI, false);
                ctx.fill();

                // Draw label for hovered or selected node
                if (node === hoveredNode || node === selectedNode) {
                  ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                  ctx.fillRect(
                    node.x! - bckgDimensions[0] / 2,
                    node.y! - node.val - bckgDimensions[1] - 2,
                    bckgDimensions[0],
                    bckgDimensions[1]
                  );

                  ctx.textAlign = 'center';
                  ctx.textBaseline = 'middle';
                  ctx.fillStyle = '#000';
                  ctx.fillText(
                    label,
                    node.x!,
                    node.y! - node.val - fontSize / 2 - 2
                  );
                }
              }}
            />
          )}
        </Box>
      </Box>
    </Paper>
  );
};

export default AgentCollaborationGraphForGoals;
