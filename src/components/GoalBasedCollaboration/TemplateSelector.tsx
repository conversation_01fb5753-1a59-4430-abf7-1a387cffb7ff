'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Grid,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ArticleIcon from '@mui/icons-material/Article';
import ListIcon from '@mui/icons-material/List';
import BusinessIcon from '@mui/icons-material/Business';
import EditIcon from '@mui/icons-material/Edit';
import CodeIcon from '@mui/icons-material/Code';
import HelpIcon from '@mui/icons-material/Help';

interface TemplateOption {
  id: string;
  name: string;
  description: string;
  category: string;
  difficulty: string;
  estimatedTime: string;
  icon: React.ReactNode;
  featured: boolean;
}

interface TemplateSelectorProps {
  onTemplateSelect: (templateId: string, customParams?: any) => void;
  onClose: () => void;
  open: boolean;
}

const ENHANCED_TEMPLATES: TemplateOption[] = [
  {
    id: 'blog-article',
    name: 'SEO Blog Article',
    description: 'Comprehensive SEO-optimized blog posts with keyword research and optimization',
    category: 'Blog',
    difficulty: 'Medium',
    estimatedTime: '20-30 minutes',
    icon: <ArticleIcon />,
    featured: true
  },
  {
    id: 'how-to-guide',
    name: 'How-To Guide',
    description: 'Step-by-step instructional content with clear actionable steps and troubleshooting',
    category: 'Educational',
    difficulty: 'Medium',
    estimatedTime: '25-35 minutes',
    icon: <HelpIcon />,
    featured: true
  },
  {
    id: 'listicle',
    name: 'Listicle',
    description: 'Engaging list-based articles optimized for social sharing and readability',
    category: 'Blog',
    difficulty: 'Easy',
    estimatedTime: '15-25 minutes',
    icon: <ListIcon />,
    featured: true
  },
  {
    id: 'case-study',
    name: 'Case Study',
    description: 'In-depth case studies showcasing real-world results and success stories',
    category: 'Business',
    difficulty: 'Advanced',
    estimatedTime: '30-45 minutes',
    icon: <BusinessIcon />,
    featured: true
  },
  {
    id: 'opinion-piece',
    name: 'Opinion Piece',
    description: 'Thought-provoking opinion articles that establish thought leadership',
    category: 'Editorial',
    difficulty: 'Advanced',
    estimatedTime: '25-35 minutes',
    icon: <EditIcon />,
    featured: true
  },
  {
    id: 'technical-tutorial',
    name: 'Technical Tutorial',
    description: 'Comprehensive technical tutorials with code examples and best practices',
    category: 'Technical',
    difficulty: 'Advanced',
    estimatedTime: '40-60 minutes',
    icon: <CodeIcon />,
    featured: true
  }
];

const TemplateSelector: React.FC<TemplateSelectorProps> = ({
  onTemplateSelect,
  onClose,
  open
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTemplate, setSelectedTemplate] = useState<TemplateOption | null>(null);
  const [customParams, setCustomParams] = useState<any>({});

  const categories = ['all', ...Array.from(new Set(ENHANCED_TEMPLATES.map(t => t.category)))];

  const filteredTemplates = selectedCategory === 'all' 
    ? ENHANCED_TEMPLATES 
    : ENHANCED_TEMPLATES.filter(t => t.category === selectedCategory);

  const featuredTemplates = filteredTemplates.filter(t => t.featured);
  const otherTemplates = filteredTemplates.filter(t => !t.featured);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'success';
      case 'Medium': return 'warning';
      case 'Advanced': return 'error';
      default: return 'default';
    }
  };

  const handleTemplateSelect = (template: TemplateOption) => {
    setSelectedTemplate(template);
  };

  const handleConfirmSelection = () => {
    if (selectedTemplate) {
      onTemplateSelect(selectedTemplate.id, customParams);
      onClose();
    }
  };

  const renderTemplateCard = (template: TemplateOption) => (
    <Card 
      key={template.id}
      sx={{ 
        height: '100%',
        cursor: 'pointer',
        border: selectedTemplate?.id === template.id ? 2 : 1,
        borderColor: selectedTemplate?.id === template.id ? 'primary.main' : 'divider',
        '&:hover': {
          boxShadow: 3,
          borderColor: 'primary.light'
        }
      }}
      onClick={() => handleTemplateSelect(template)}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ mr: 2, color: 'primary.main' }}>
            {template.icon}
          </Box>
          <Typography variant="h6" component="h3">
            {template.name}
          </Typography>
          {template.featured && (
            <Chip 
              label="Featured" 
              size="small" 
              color="primary" 
              sx={{ ml: 'auto' }}
            />
          )}
        </Box>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {template.description}
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Chip 
            label={template.category} 
            size="small" 
            variant="outlined"
          />
          <Chip 
            label={template.difficulty} 
            size="small" 
            color={getDifficultyColor(template.difficulty) as any}
          />
          <Chip 
            label={template.estimatedTime} 
            size="small" 
            variant="outlined"
          />
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { minHeight: '80vh' }
      }}
    >
      <DialogTitle>
        <Typography variant="h5" component="h2">
          Choose Content Template
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Select a template that best fits your content goals
        </Typography>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel>Category</InputLabel>
            <Select
              value={selectedCategory}
              label="Category"
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              {categories.map(category => (
                <MenuItem key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        {featuredTemplates.length > 0 && (
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Featured Templates
            </Typography>
            <Grid container spacing={2}>
              {featuredTemplates.map(template => (
                <Grid item xs={12} sm={6} md={4} key={template.id}>
                  {renderTemplateCard(template)}
                </Grid>
              ))}
            </Grid>
          </Box>
        )}

        {otherTemplates.length > 0 && (
          <Box>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Other Templates
            </Typography>
            <Grid container spacing={2}>
              {otherTemplates.map(template => (
                <Grid item xs={12} sm={6} md={4} key={template.id}>
                  {renderTemplateCard(template)}
                </Grid>
              ))}
            </Grid>
          </Box>
        )}

        {selectedTemplate && (
          <Accordion sx={{ mt: 3 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">
                Customize {selectedTemplate.name}
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Additional Instructions"
                    multiline
                    rows={3}
                    value={customParams.additionalInstructions || ''}
                    onChange={(e) => setCustomParams({
                      ...customParams,
                      additionalInstructions: e.target.value
                    })}
                    placeholder="Any specific requirements or preferences..."
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Reference URLs"
                    multiline
                    rows={3}
                    value={customParams.referenceUrls || ''}
                    onChange={(e) => setCustomParams({
                      ...customParams,
                      referenceUrls: e.target.value.split('\n').filter(url => url.trim())
                    })}
                    placeholder="One URL per line..."
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>
          Cancel
        </Button>
        <Button 
          variant="contained" 
          onClick={handleConfirmSelection}
          disabled={!selectedTemplate}
        >
          Use Template
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TemplateSelector;
