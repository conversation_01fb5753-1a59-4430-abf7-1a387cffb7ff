'use client';

import React from 'react';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  LinearProgress,
  Chip,
  useTheme
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';

// Import types from our V3 implementation
import { 
  GoalStatus,
  GoalType
} from '../../app/(payload)/api/agents/dynamic-collaboration-v3';

interface GoalProgressTrackerProps {
  sessionId: string;
  state: any;
  loading?: boolean;
  onRefresh?: () => void;
}

const GoalProgressTracker: React.FC<GoalProgressTrackerProps> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const theme = useTheme();

  // Calculate overall progress
  const calculateOverallProgress = (): number => {
    if (!state || !state.goals) return 0;
    
    const goals = Object.values(state.goals);
    if (goals.length === 0) return 0;
    
    const totalProgress = goals.reduce((sum: number, goal: any) => sum + (goal.progress || 0), 0);
    return Math.round(totalProgress / goals.length);
  };

  // Get status icon
  const getStatusIcon = (status: GoalStatus) => {
    switch (status) {
      case GoalStatus.COMPLETED:
        return <CheckCircleIcon sx={{ color: theme.palette.success.main }} />;
      case GoalStatus.FAILED:
        return <ErrorIcon sx={{ color: theme.palette.error.main }} />;
      case GoalStatus.ACTIVE:
        return <PlayArrowIcon sx={{ color: theme.palette.primary.main }} />;
      case GoalStatus.PENDING:
        return <HourglassEmptyIcon sx={{ color: theme.palette.grey[500] }} />;
      case GoalStatus.PAUSED:
        return <PauseIcon sx={{ color: theme.palette.warning.main }} />;
      default:
        return <HourglassEmptyIcon sx={{ color: theme.palette.grey[500] }} />;
    }
  };

  // Get goal type label
  const getGoalTypeLabel = (type: GoalType): string => {
    switch (type) {
      case GoalType.RESEARCH:
        return 'Research';
      case GoalType.CONTENT:
        return 'Content';
      case GoalType.QUALITY:
        return 'Quality';
      case GoalType.MARKET_RESEARCH:
        return 'Market Research';
      case GoalType.KEYWORD_ANALYSIS:
        return 'Keyword Analysis';
      case GoalType.CONTENT_STRATEGY:
        return 'Content Strategy';
      case GoalType.CONTENT_CREATION:
        return 'Content Creation';
      case GoalType.SEO_OPTIMIZATION:
        return 'SEO Optimization';
      case GoalType.QUALITY_ASSESSMENT:
        return 'Quality Assessment';
      default:
        return type;
    }
  };

  // Get goal type color
  const getGoalTypeColor = (type: GoalType): string => {
    switch (type) {
      case GoalType.RESEARCH:
        return theme.palette.info.main;
      case GoalType.CONTENT:
        return theme.palette.primary.main;
      case GoalType.QUALITY:
        return theme.palette.success.main;
      case GoalType.MARKET_RESEARCH:
        return theme.palette.info.light;
      case GoalType.KEYWORD_ANALYSIS:
        return theme.palette.info.dark;
      case GoalType.CONTENT_STRATEGY:
        return theme.palette.primary.light;
      case GoalType.CONTENT_CREATION:
        return theme.palette.primary.dark;
      case GoalType.SEO_OPTIMIZATION:
        return theme.palette.success.light;
      case GoalType.QUALITY_ASSESSMENT:
        return theme.palette.success.dark;
      default:
        return theme.palette.grey[500];
    }
  };

  return (
    <Paper sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Goal Progress</Typography>
        {onRefresh && (
          <Tooltip title="Refresh">
            <IconButton onClick={onRefresh} size="small" disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        )}
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
          <CircularProgress />
        </Box>
      ) : !state || !state.goals ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
          <Typography variant="body1" color="text.secondary">
            No goals available
          </Typography>
        </Box>
      ) : (
        <>
          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Overall Progress
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                {calculateOverallProgress()}%
              </Typography>
            </Box>
            <LinearProgress 
              variant="determinate" 
              value={calculateOverallProgress()} 
              sx={{ height: 10, borderRadius: 5 }}
            />
          </Box>

          <Divider sx={{ my: 2 }} />

          <List sx={{ maxHeight: 300, overflow: 'auto' }}>
            {Object.values(state.goals)
              .sort((a: any, b: any) => {
                // Sort by status (active first, then pending, then completed, then failed)
                const statusOrder = {
                  [GoalStatus.ACTIVE]: 0,
                  [GoalStatus.PENDING]: 1,
                  [GoalStatus.PAUSED]: 2,
                  [GoalStatus.COMPLETED]: 3,
                  [GoalStatus.FAILED]: 4
                };
                
                const statusDiff = statusOrder[a.status] - statusOrder[b.status];
                if (statusDiff !== 0) return statusDiff;
                
                // If same status, sort by progress (descending)
                return (b.progress || 0) - (a.progress || 0);
              })
              .map((goal: any) => (
                <ListItem key={goal.id} sx={{ px: 0 }}>
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    {getStatusIcon(goal.status)}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="body2" noWrap sx={{ maxWidth: '150px' }}>
                          {goal.description}
                        </Typography>
                        <Chip
                          label={getGoalTypeLabel(goal.type)}
                          size="small"
                          sx={{
                            ml: 1,
                            backgroundColor: getGoalTypeColor(goal.type),
                            color: '#fff',
                            fontSize: '0.6rem',
                            height: 20
                          }}
                        />
                      </Box>
                    }
                    secondary={
                      <Box sx={{ mt: 0.5 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                          <Typography variant="caption" color="text.secondary">
                            Progress
                          </Typography>
                          <Typography variant="caption" fontWeight="bold">
                            {goal.progress || 0}%
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={goal.progress || 0}
                          sx={{
                            height: 4,
                            borderRadius: 2,
                            backgroundColor: theme.palette.grey[200],
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: getGoalTypeColor(goal.type)
                            }
                          }}
                        />
                      </Box>
                    }
                  />
                </ListItem>
              ))}
          </List>

          <Divider sx={{ my: 2 }} />

          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Box>
              <Typography variant="body2" color="text.secondary">
                Active Goals
              </Typography>
              <Typography variant="h6">
                {Object.values(state.goals).filter((g: any) => g.status === GoalStatus.ACTIVE).length}
              </Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">
                Completed Goals
              </Typography>
              <Typography variant="h6">
                {Object.values(state.goals).filter((g: any) => g.status === GoalStatus.COMPLETED).length}
              </Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">
                Total Goals
              </Typography>
              <Typography variant="h6">
                {Object.values(state.goals).length}
              </Typography>
            </Box>
          </Box>
        </>
      )}
    </Paper>
  );
};

export default GoalProgressTracker;
