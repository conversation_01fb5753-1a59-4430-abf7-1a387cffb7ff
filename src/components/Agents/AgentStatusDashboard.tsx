/**
 * Agent Status Dashboard Component
 * 
 * Displays the status and health of all registered agents
 */

'use client';

import { useState } from 'react';

interface AgentStatus {
  agentId: string;
  isRegistered: boolean;
  isAvailable: boolean;
  capabilities: string[];
  status: {
    agentId: string;
    isAvailable: boolean;
    activeConsultations: number;
    maxConcurrentConsultations: number;
    enabled: boolean;
  } | null;
}

interface HealthCheck {
  overallHealth: 'healthy' | 'degraded' | 'unhealthy';
  agentHealth: Record<string, 'healthy' | 'unhealthy'>;
  issues: string[];
}

interface AgentMetrics {
  totalConsultations: number;
  successfulConsultations: number;
  failedConsultations: number;
  averageResponseTime: number;
  averageConfidence: number;
  successRate: number;
  agentUtilization: Record<string, number>;
  lastUpdated: string;
}

interface Props {
  agentStatus: AgentStatus[];
  healthCheck: HealthCheck | null;
  metrics: AgentMetrics | null;
  onRefresh: () => void;
}

export default function AgentStatusDashboard({ agentStatus, healthCheck, metrics, onRefresh }: Props) {
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);

  const getAgentIcon = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return '🔍';
      case 'market-research': return '📊';
      case 'content-strategy': return '📝';
      default: return '🤖';
    }
  };

  const getAgentName = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return 'SEO Keyword Agent';
      case 'market-research': return 'Market Research Agent';
      case 'content-strategy': return 'Content Strategy Agent';
      default: return agentId;
    }
  };

  const getStatusColor = (isAvailable: boolean, isHealthy: boolean) => {
    if (!isAvailable) return 'bg-red-100 text-red-800';
    if (!isHealthy) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  const getStatusText = (isAvailable: boolean, isHealthy: boolean) => {
    if (!isAvailable) return 'Unavailable';
    if (!isHealthy) return 'Degraded';
    return 'Available';
  };

  const formatCapabilities = (capabilities: string[]) => {
    return capabilities.map(cap => 
      cap.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')
    ).join(', ');
  };

  const getUtilizationPercentage = (agentId: string) => {
    if (!metrics?.agentUtilization) return 0;
    const total = Object.values(metrics.agentUtilization).reduce((sum, count) => sum + count, 0);
    const agentCount = metrics.agentUtilization[agentId] || 0;
    return total > 0 ? Math.round((agentCount / total) * 100) : 0;
  };

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Agents */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 text-lg">🤖</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Agents</p>
              <p className="text-2xl font-semibold text-gray-900">{agentStatus.length}</p>
            </div>
          </div>
        </div>

        {/* Available Agents */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 text-lg">✅</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Available</p>
              <p className="text-2xl font-semibold text-gray-900">
                {agentStatus.filter(agent => agent.isAvailable).length}
              </p>
            </div>
          </div>
        </div>

        {/* System Health */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                healthCheck?.overallHealth === 'healthy' ? 'bg-green-100' :
                healthCheck?.overallHealth === 'degraded' ? 'bg-yellow-100' : 'bg-red-100'
              }`}>
                <span className="text-lg">
                  {healthCheck?.overallHealth === 'healthy' ? '💚' :
                   healthCheck?.overallHealth === 'degraded' ? '💛' : '❤️'}
                </span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">System Health</p>
              <p className="text-2xl font-semibold text-gray-900 capitalize">
                {healthCheck?.overallHealth || 'Unknown'}
              </p>
            </div>
          </div>
        </div>

        {/* Success Rate */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <span className="text-purple-600 text-lg">📈</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className="text-2xl font-semibold text-gray-900">
                {metrics ? Math.round(metrics.successRate * 100) : 0}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Health Issues Alert */}
      {healthCheck?.issues && healthCheck.issues.length > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">System Issues Detected</h3>
              <div className="mt-2 text-sm text-yellow-700">
                <ul className="list-disc pl-5 space-y-1">
                  {healthCheck.issues.map((issue, index) => (
                    <li key={index}>{issue}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Agent Status Grid */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Agent Status</h3>
          <p className="text-sm text-gray-600">Monitor individual agent health and performance</p>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {agentStatus.map((agent) => {
              const isHealthy = healthCheck?.agentHealth[agent.agentId] === 'healthy';
              const utilization = getUtilizationPercentage(agent.agentId);
              
              return (
                <div
                  key={agent.agentId}
                  className={`border rounded-lg p-4 cursor-pointer transition-all ${
                    selectedAgent === agent.agentId 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedAgent(selectedAgent === agent.agentId ? null : agent.agentId)}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{getAgentIcon(agent.agentId)}</span>
                      <div>
                        <h4 className="font-medium text-gray-900">{getAgentName(agent.agentId)}</h4>
                        <p className="text-sm text-gray-500">{agent.agentId}</p>
                      </div>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(agent.isAvailable, isHealthy)}`}>
                      {getStatusText(agent.isAvailable, isHealthy)}
                    </span>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Capabilities:</span>
                      <span className="text-gray-900 text-right">{agent.capabilities.length}</span>
                    </div>
                    
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Utilization:</span>
                      <span className="text-gray-900">{utilization}%</span>
                    </div>

                    {agent.status && (
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Active:</span>
                        <span className="text-gray-900">
                          {agent.status.activeConsultations}/{agent.status.maxConcurrentConsultations}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Expanded Details */}
                  {selectedAgent === agent.agentId && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="space-y-3">
                        <div>
                          <h5 className="text-sm font-medium text-gray-900 mb-1">Capabilities</h5>
                          <p className="text-sm text-gray-600">{formatCapabilities(agent.capabilities)}</p>
                        </div>
                        
                        {agent.status && (
                          <div>
                            <h5 className="text-sm font-medium text-gray-900 mb-1">Configuration</h5>
                            <div className="text-sm text-gray-600 space-y-1">
                              <div>Max Concurrent: {agent.status.maxConcurrentConsultations}</div>
                              <div>Enabled: {agent.status.enabled ? 'Yes' : 'No'}</div>
                              <div>Registered: {agent.isRegistered ? 'Yes' : 'No'}</div>
                            </div>
                          </div>
                        )}

                        {metrics?.agentUtilization[agent.agentId] && (
                          <div>
                            <h5 className="text-sm font-medium text-gray-900 mb-1">Usage Statistics</h5>
                            <div className="text-sm text-gray-600">
                              Total Consultations: {metrics.agentUtilization[agent.agentId]}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
