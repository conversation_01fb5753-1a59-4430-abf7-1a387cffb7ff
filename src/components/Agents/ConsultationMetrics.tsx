/**
 * Consultation Metrics Component
 * 
 * Displays detailed metrics and analytics for agent consultations
 */

'use client';

import { useState } from 'react';

interface AgentMetrics {
  totalConsultations: number;
  successfulConsultations: number;
  failedConsultations: number;
  averageResponseTime: number;
  averageConfidence: number;
  successRate: number;
  agentUtilization: Record<string, number>;
  lastUpdated: string;
}

interface AgentStatus {
  agentId: string;
  isRegistered: boolean;
  isAvailable: boolean;
  capabilities: string[];
  status: any;
}

interface Props {
  metrics: AgentMetrics | null;
  agentStatus: AgentStatus[];
  onRefresh: () => void;
}

export default function ConsultationMetrics({ metrics, agentStatus, onRefresh }: Props) {
  const [selectedTimeframe, setSelectedTimeframe] = useState<'1h' | '24h' | '7d' | 'all'>('24h');

  const formatResponseTime = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatConfidence = (confidence: number) => {
    return `${Math.round(confidence * 100)}%`;
  };

  const getAgentName = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return 'SEO Keyword';
      case 'market-research': return 'Market Research';
      case 'content-strategy': return 'Content Strategy';
      default: return agentId;
    }
  };

  const getAgentIcon = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return '🔍';
      case 'market-research': return '📊';
      case 'content-strategy': return '📝';
      default: return '🤖';
    }
  };

  const getUtilizationData = () => {
    if (!metrics?.agentUtilization) return [];
    
    const total = Object.values(metrics.agentUtilization).reduce((sum, count) => sum + count, 0);
    
    return Object.entries(metrics.agentUtilization).map(([agentId, count]) => ({
      agentId,
      count,
      percentage: total > 0 ? (count / total) * 100 : 0
    }));
  };

  const getPerformanceColor = (value: number, type: 'success' | 'confidence' | 'response') => {
    switch (type) {
      case 'success':
        if (value >= 0.95) return 'text-green-600';
        if (value >= 0.8) return 'text-yellow-600';
        return 'text-red-600';
      case 'confidence':
        if (value >= 0.8) return 'text-green-600';
        if (value >= 0.6) return 'text-yellow-600';
        return 'text-red-600';
      case 'response':
        if (value <= 2000) return 'text-green-600';
        if (value <= 5000) return 'text-yellow-600';
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  if (!metrics) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Metrics Available</h3>
          <p className="text-gray-600 mb-4">
            No consultation data has been collected yet. Start using agent consultations to see metrics here.
          </p>
          <button
            onClick={onRefresh}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Refresh Data
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Timeframe Selector */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Consultation Analytics</h3>
          <div className="flex space-x-2">
            {(['1h', '24h', '7d', 'all'] as const).map((timeframe) => (
              <button
                key={timeframe}
                onClick={() => setSelectedTimeframe(timeframe)}
                className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                  selectedTimeframe === timeframe
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {timeframe === 'all' ? 'All Time' : timeframe.toUpperCase()}
              </button>
            ))}
          </div>
        </div>

        <div className="text-sm text-gray-500 mb-4">
          Last updated: {new Date(metrics.lastUpdated).toLocaleString()}
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Consultations */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 text-lg">💬</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Consultations</p>
              <p className="text-2xl font-semibold text-gray-900">{metrics.totalConsultations}</p>
            </div>
          </div>
        </div>

        {/* Success Rate */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 text-lg">✅</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className={`text-2xl font-semibold ${getPerformanceColor(metrics.successRate, 'success')}`}>
                {Math.round(metrics.successRate * 100)}%
              </p>
            </div>
          </div>
        </div>

        {/* Average Response Time */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <span className="text-yellow-600 text-lg">⏱️</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg Response Time</p>
              <p className={`text-2xl font-semibold ${getPerformanceColor(metrics.averageResponseTime, 'response')}`}>
                {formatResponseTime(metrics.averageResponseTime)}
              </p>
            </div>
          </div>
        </div>

        {/* Average Confidence */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <span className="text-purple-600 text-lg">🎯</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg Confidence</p>
              <p className={`text-2xl font-semibold ${getPerformanceColor(metrics.averageConfidence, 'confidence')}`}>
                {formatConfidence(metrics.averageConfidence)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Success/Failure Breakdown */}
        <div className="bg-white rounded-lg shadow p-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Consultation Results</h4>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-4 h-4 bg-green-500 rounded"></div>
                <span className="text-sm font-medium text-gray-700">Successful</span>
              </div>
              <div className="text-right">
                <div className="text-lg font-semibold text-gray-900">{metrics.successfulConsultations}</div>
                <div className="text-sm text-gray-500">
                  {Math.round((metrics.successfulConsultations / metrics.totalConsultations) * 100)}%
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-4 h-4 bg-red-500 rounded"></div>
                <span className="text-sm font-medium text-gray-700">Failed</span>
              </div>
              <div className="text-right">
                <div className="text-lg font-semibold text-gray-900">{metrics.failedConsultations}</div>
                <div className="text-sm text-gray-500">
                  {Math.round((metrics.failedConsultations / metrics.totalConsultations) * 100)}%
                </div>
              </div>
            </div>

            {/* Visual Progress Bar */}
            <div className="mt-4">
              <div className="flex rounded-lg overflow-hidden h-2">
                <div 
                  className="bg-green-500" 
                  style={{ width: `${(metrics.successfulConsultations / metrics.totalConsultations) * 100}%` }}
                ></div>
                <div 
                  className="bg-red-500" 
                  style={{ width: `${(metrics.failedConsultations / metrics.totalConsultations) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Agent Utilization */}
        <div className="bg-white rounded-lg shadow p-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Agent Utilization</h4>
          <div className="space-y-4">
            {getUtilizationData().map(({ agentId, count, percentage }) => (
              <div key={agentId} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="text-lg">{getAgentIcon(agentId)}</span>
                  <span className="text-sm font-medium text-gray-700">{getAgentName(agentId)}</span>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-gray-900">{count}</div>
                  <div className="text-sm text-gray-500">{Math.round(percentage)}%</div>
                </div>
              </div>
            ))}

            {/* Visual Utilization Chart */}
            <div className="mt-4">
              <div className="flex rounded-lg overflow-hidden h-2">
                {getUtilizationData().map(({ agentId, percentage }, index) => (
                  <div
                    key={agentId}
                    className={`${
                      index === 0 ? 'bg-blue-500' : 
                      index === 1 ? 'bg-green-500' : 'bg-purple-500'
                    }`}
                    style={{ width: `${percentage}%` }}
                  ></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Insights */}
      <div className="bg-white rounded-lg shadow p-6">
        <h4 className="text-lg font-medium text-gray-900 mb-4">Performance Insights</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-3xl mb-2">
              {metrics.successRate >= 0.95 ? '🟢' : metrics.successRate >= 0.8 ? '🟡' : '🔴'}
            </div>
            <h5 className="font-medium text-gray-900">System Reliability</h5>
            <p className="text-sm text-gray-600 mt-1">
              {metrics.successRate >= 0.95 ? 'Excellent' : 
               metrics.successRate >= 0.8 ? 'Good' : 'Needs Attention'}
            </p>
          </div>

          <div className="text-center">
            <div className="text-3xl mb-2">
              {metrics.averageResponseTime <= 2000 ? '🟢' : 
               metrics.averageResponseTime <= 5000 ? '🟡' : '🔴'}
            </div>
            <h5 className="font-medium text-gray-900">Response Speed</h5>
            <p className="text-sm text-gray-600 mt-1">
              {metrics.averageResponseTime <= 2000 ? 'Fast' : 
               metrics.averageResponseTime <= 5000 ? 'Moderate' : 'Slow'}
            </p>
          </div>

          <div className="text-center">
            <div className="text-3xl mb-2">
              {metrics.averageConfidence >= 0.8 ? '🟢' : 
               metrics.averageConfidence >= 0.6 ? '🟡' : '🔴'}
            </div>
            <h5 className="font-medium text-gray-900">Confidence Level</h5>
            <p className="text-sm text-gray-600 mt-1">
              {metrics.averageConfidence >= 0.8 ? 'High' : 
               metrics.averageConfidence >= 0.6 ? 'Medium' : 'Low'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
