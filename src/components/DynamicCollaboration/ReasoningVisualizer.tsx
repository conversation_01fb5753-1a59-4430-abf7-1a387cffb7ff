'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  useTheme,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import PsychologyIcon from '@mui/icons-material/Psychology';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import FactCheckIcon from '@mui/icons-material/FactCheck';
import AssignmentIcon from '@mui/icons-material/Assignment';
import { DynamicCollaborationState, DynamicAgentMessage, DynamicMessageType } from '../../app/(payload)/api/agents/dynamic-collaboration-v2/state';

interface ReasoningVisualizerProps {
  sessionId: string;
  state: DynamicCollaborationState | null;
  loading?: boolean;
  onRefresh?: () => void;
}

const ReasoningVisualizer: React.FC<ReasoningVisualizerProps> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const [expandedAgent, setExpandedAgent] = useState<string | null>(null);
  const [agentReasoning, setAgentReasoning] = useState<Record<string, DynamicAgentMessage[]>>({});
  const [decisionTimeline, setDecisionTimeline] = useState<DynamicAgentMessage[]>([]);

  // Agent colors and names
  const agentColors: Record<string, string> = {
    'market-research': theme.palette.primary.main,
    'seo-keyword': theme.palette.secondary.main,
    'content-strategy': theme.palette.info.main,
    'content-generation': theme.palette.success.main,
    'seo-optimization': theme.palette.warning.main,
    'system': theme.palette.grey[500],
    'user': theme.palette.error.main
  };

  const agentNames: Record<string, string> = {
    'market-research': 'Market Research',
    'seo-keyword': 'SEO Keyword',
    'content-strategy': 'Content Strategy',
    'content-generation': 'Content Writer',
    'seo-optimization': 'SEO Optimizer',
    'system': 'System',
    'user': 'User'
  };

  // Process messages from state
  useEffect(() => {
    if (!state || !state.dynamicMessages) {
      setAgentReasoning({});
      setDecisionTimeline([]);
      return;
    }

    // Group messages by agent
    const messagesByAgent: Record<string, DynamicAgentMessage[]> = {};
    const decisionsWithReasoning: DynamicAgentMessage[] = [];

    Object.values(state.dynamicMessages).forEach(message => {
      // Skip messages without reasoning
      if (!message.reasoning) return;

      // Add to agent's messages
      if (!messagesByAgent[message.from]) {
        messagesByAgent[message.from] = [];
      }
      messagesByAgent[message.from].push(message);

      // Add to decisions timeline if it has a decision
      if (message.reasoning.decision) {
        decisionsWithReasoning.push(message);
      }
    });

    // Sort messages by timestamp
    Object.keys(messagesByAgent).forEach(agentId => {
      messagesByAgent[agentId].sort((a, b) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );
    });

    // Sort decisions by timestamp
    decisionsWithReasoning.sort((a, b) =>
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

    setAgentReasoning(messagesByAgent);
    setDecisionTimeline(decisionsWithReasoning);
  }, [state]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Toggle agent expansion
  const handleAgentExpansion = (agentId: string) => {
    setExpandedAgent(expandedAgent === agentId ? null : agentId);
  };

  // Format time
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get message type icon
  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case DynamicMessageType.GOAL_UPDATE:
        return <AssignmentIcon />;
      case DynamicMessageType.ARTIFACT_UPDATE:
        return <FactCheckIcon />;
      case DynamicMessageType.FEEDBACK_REQUEST:
      case DynamicMessageType.FEEDBACK_RESPONSE:
        return <CheckCircleIcon />;
      default:
        return <LightbulbIcon />;
    }
  };

  // Render agent reasoning tab
  const renderAgentReasoningTab = () => {
    const agentIds = Object.keys(agentReasoning).filter(id => id !== 'system' && id !== 'user');

    if (loading && agentIds.length === 0) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
          <CircularProgress />
        </Box>
      );
    }

    if (agentIds.length === 0) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
          <Typography variant="body1" color="text.secondary">
            No agent reasoning data available
          </Typography>
        </Box>
      );
    }

    return (
      <Box>
        {agentIds.map(agentId => (
          <Accordion
            key={agentId}
            expanded={expandedAgent === agentId}
            onChange={() => handleAgentExpansion(agentId)}
            sx={{ mb: 2 }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              sx={{
                borderLeft: '4px solid',
                borderLeftColor: agentColors[agentId] || theme.palette.grey[500]
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar
                  sx={{
                    bgcolor: agentColors[agentId] || theme.palette.grey[500],
                    mr: 1.5
                  }}
                >
                  <PsychologyIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6">
                    {agentNames[agentId] || agentId}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {agentReasoning[agentId].length} reasoning entries
                  </Typography>
                </Box>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <List>
                {agentReasoning[agentId].map((message, index) => (
                  <ListItem
                    key={message.id}
                    alignItems="flex-start"
                    sx={{
                      mb: 2,
                      p: 2,
                      bgcolor: 'grey.50',
                      borderRadius: 1
                    }}
                  >
                    <Box sx={{ width: '100%' }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <ListItemIcon sx={{ minWidth: 'auto', mr: 1 }}>
                            {getMessageTypeIcon(message.type)}
                          </ListItemIcon>
                          <Typography variant="subtitle1">
                            {message.type === DynamicMessageType.GOAL_UPDATE
                              ? `Goal: ${message.content.description || 'Unknown'}`
                              : message.type === DynamicMessageType.ARTIFACT_UPDATE
                              ? `Artifact: ${message.content.name || 'Unknown'}`
                              : `Message to ${Array.isArray(message.to)
                                ? message.to.map(id => agentNames[id] || id).join(', ')
                                : agentNames[message.to] || message.to}`
                            }
                          </Typography>
                        </Box>
                        <Typography variant="caption" color="text.secondary">
                          {formatTime(message.timestamp)}
                        </Typography>
                      </Box>

                      <Divider sx={{ my: 1 }} />

                      {message.reasoning && (
                        <Box>
                          {message.reasoning.thoughts && message.reasoning.thoughts.length > 0 && (
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="subtitle2" gutterBottom>
                                Thoughts:
                              </Typography>
                              <List dense disablePadding>
                                {message.reasoning.thoughts.map((thought, i) => (
                                  <ListItem key={i} sx={{ py: 0.5 }}>
                                    <ListItemIcon sx={{ minWidth: 28 }}>
                                      <LightbulbIcon fontSize="small" color="primary" />
                                    </ListItemIcon>
                                    <ListItemText primary={thought} />
                                  </ListItem>
                                ))}
                              </List>
                            </Box>
                          )}

                          {message.reasoning.considerations && message.reasoning.considerations.length > 0 && (
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="subtitle2" gutterBottom>
                                Considerations:
                              </Typography>
                              <List dense disablePadding>
                                {message.reasoning.considerations.map((consideration, i) => (
                                  <ListItem key={i} sx={{ py: 0.5 }}>
                                    <ListItemIcon sx={{ minWidth: 28 }}>
                                      <FactCheckIcon fontSize="small" color="secondary" />
                                    </ListItemIcon>
                                    <ListItemText primary={consideration} />
                                  </ListItem>
                                ))}
                              </List>
                            </Box>
                          )}

                          {message.reasoning.decision && (
                            <Box sx={{ mb: 1 }}>
                              <Typography variant="subtitle2" gutterBottom>
                                Decision:
                              </Typography>
                              <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                                {message.reasoning.decision}
                              </Typography>
                            </Box>
                          )}

                          {message.reasoning.confidence !== undefined && (
                            <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                              <Typography variant="body2" sx={{ mr: 1 }}>
                                Confidence:
                              </Typography>
                              <Box
                                sx={{
                                  width: 200,
                                  height: 8,
                                  bgcolor: 'grey.200',
                                  borderRadius: 4,
                                  mr: 1
                                }}
                              >
                                <Box
                                  sx={{
                                    width: `${message.reasoning.confidence * 100}%`,
                                    height: '100%',
                                    bgcolor:
                                      message.reasoning.confidence >= 0.8 ? 'success.main' :
                                      message.reasoning.confidence >= 0.6 ? 'primary.main' :
                                      message.reasoning.confidence >= 0.4 ? 'warning.main' : 'error.main',
                                    borderRadius: 4
                                  }}
                                />
                              </Box>
                              <Typography variant="body2" fontWeight="medium">
                                {Math.round(message.reasoning.confidence * 100)}%
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      )}
                    </Box>
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        ))}
      </Box>
    );
  };

  // Render decision timeline tab
  const renderDecisionTimelineTab = () => {
    if (loading && decisionTimeline.length === 0) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
          <CircularProgress />
        </Box>
      );
    }

    if (decisionTimeline.length === 0) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
          <Typography variant="body1" color="text.secondary">
            No decision timeline data available
          </Typography>
        </Box>
      );
    }

    return (
      <Timeline position="alternate">
        {decisionTimeline.map((message, index) => (
          <TimelineItem key={message.id}>
            <TimelineOppositeContent color="text.secondary">
              {formatTime(message.timestamp)}
            </TimelineOppositeContent>
            <TimelineSeparator>
              <TimelineDot sx={{ bgcolor: agentColors[message.from] || theme.palette.grey[500] }}>
                {getMessageTypeIcon(message.type)}
              </TimelineDot>
              {index < decisionTimeline.length - 1 && <TimelineConnector />}
            </TimelineSeparator>
            <TimelineContent>
              <Card variant="outlined">
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Avatar
                      sx={{
                        bgcolor: agentColors[message.from] || theme.palette.grey[500],
                        width: 24,
                        height: 24,
                        mr: 1
                      }}
                    >
                      <PsychologyIcon fontSize="small" />
                    </Avatar>
                    <Typography variant="subtitle1">
                      {agentNames[message.from] || message.from}
                    </Typography>
                  </Box>

                  <Typography variant="body1" fontWeight="medium" gutterBottom>
                    {message.reasoning?.decision}
                  </Typography>

                  {message.reasoning?.confidence !== undefined && (
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <Typography variant="body2" sx={{ mr: 1 }}>
                        Confidence:
                      </Typography>
                      <Chip
                        label={`${Math.round(message.reasoning.confidence * 100)}%`}
                        size="small"
                        color={
                          message.reasoning.confidence >= 0.8 ? 'success' :
                          message.reasoning.confidence >= 0.6 ? 'primary' :
                          message.reasoning.confidence >= 0.4 ? 'warning' : 'error'
                        }
                      />
                    </Box>
                  )}

                  {message.reasoning?.thoughts && message.reasoning.thoughts.length > 0 && (
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="caption" color="text.secondary">
                        Key thought: {message.reasoning.thoughts[0]}
                      </Typography>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </TimelineContent>
          </TimelineItem>
        ))}
      </Timeline>
    );
  };

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2 }}>
      <Typography variant="h6" gutterBottom>
        Agent Reasoning
      </Typography>

      <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
        <Tab label="Agent Reasoning" />
        <Tab label="Decision Timeline" />
      </Tabs>

      {activeTab === 0 && renderAgentReasoningTab()}
      {activeTab === 1 && renderDecisionTimelineTab()}
    </Paper>
  );
};

export default ReasoningVisualizer;
