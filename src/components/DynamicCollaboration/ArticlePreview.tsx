'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  CircularProgress,
  Divider,
  Chip,
  Grid,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  <PERSON>nackbar,
  Alert,
  useTheme
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DownloadIcon from '@mui/icons-material/Download';
import PublishIcon from '@mui/icons-material/Publish';
import dynamic from 'next/dynamic';
import remarkGfm from 'remark-gfm';

// Dynamically import ReactMarkdown to avoid SSR issues
const ReactMarkdown = dynamic(() => import('react-markdown'), { ssr: false });
import { DynamicCollaborationState, DynamicWorkflowPhase } from '../../app/(payload)/api/agents/dynamic-collaboration-v2/state';
import { dynamicCollaborationClientV2 } from '../../lib/dynamic-collaboration-client-v2';

interface ArticlePreviewProps {
  sessionId: string;
  state: DynamicCollaborationState | null;
  loading?: boolean;
  onRefresh?: () => void;
}

const ArticlePreview: React.FC<ArticlePreviewProps> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const theme = useTheme();
  const [article, setArticle] = useState<string>('');
  const [title, setTitle] = useState<string>('');
  const [editMode, setEditMode] = useState<boolean>(false);
  const [editedContent, setEditedContent] = useState<string>('');
  const [editedTitle, setEditedTitle] = useState<string>('');
  const [saving, setSaving] = useState<boolean>(false);
  const [publishDialogOpen, setPublishDialogOpen] = useState<boolean>(false);
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' | 'info' | 'warning' }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Extract article content from state
  useEffect(() => {
    if (!state) return;

    // Look for the final article artifact
    if (state.artifacts && state.generatedArtifacts) {
      // First try to find an artifact with type 'final-article' or similar
      const finalArticleTypes = ['final-article', 'article', 'blog-post', 'content', 'seo-optimized-content'];

      // Check if we're in the finalization phase - prioritize artifacts from this phase
      const isFinalizationPhase = state.currentPhase === DynamicWorkflowPhase.FINALIZATION;

      // First look for artifacts from the SEO optimization agent if we're in finalization
      if (isFinalizationPhase) {
        const seoOptimizedArtifacts = state.generatedArtifacts
          .map(id => state.artifacts[id])
          .filter(artifact =>
            artifact &&
            artifact.creator === 'seo-optimization'
          );

        if (seoOptimizedArtifacts.length > 0) {
          // Sort by timestamp (newest first)
          seoOptimizedArtifacts.sort((a, b) =>
            new Date(b.timestamp || '').getTime() - new Date(a.timestamp || '').getTime()
          );

          // Use the most recent SEO optimized artifact
          const latestSeoArtifact = seoOptimizedArtifacts[0];
          extractAndSetArticleContent(latestSeoArtifact, state);
          return;
        }
      }

      // Then try to find by specific article types
      for (const type of finalArticleTypes) {
        const articleArtifact = state.generatedArtifacts
          .map(id => state.artifacts[id])
          .find(artifact =>
            artifact &&
            artifact.type &&
            artifact.type.toLowerCase().includes(type)
          );

        if (articleArtifact) {
          extractAndSetArticleContent(articleArtifact, state);
          return;
        }
      }

      // If no final article found, try to construct one from content-generation artifacts
      const contentArtifacts = state.generatedArtifacts
        .map(id => state.artifacts[id])
        .filter(artifact =>
          artifact &&
          (artifact.creator === 'content-generation' || artifact.creator === 'seo-optimization')
        );

      if (contentArtifacts.length > 0) {
        // Sort by timestamp (newest first)
        contentArtifacts.sort((a, b) =>
          new Date(b.timestamp || '').getTime() - new Date(a.timestamp || '').getTime()
        );

        // Use the most recent content artifact
        const latestArtifact = contentArtifacts[0];
        extractAndSetArticleContent(latestArtifact, state);
      } else if (state.topic) {
        // If no content artifacts found, use the topic as title
        setTitle(state.topic);
        setEditedTitle(state.topic);
      }
    }
  }, [state]);

  // Helper function to extract and set article content
  const extractAndSetArticleContent = (artifact: any, state: DynamicCollaborationState) => {
    if (!artifact) return;

    // Extract title
    let extractedTitle = '';
    if (artifact.name) {
      extractedTitle = artifact.name;
    } else if (artifact.data && artifact.data.title) {
      extractedTitle = artifact.data.title;
    } else if (state.topic) {
      extractedTitle = state.topic;
    }

    // Extract content
    let content = '';
    if (typeof artifact.content === 'string') {
      content = artifact.content;
    } else if (typeof artifact.text === 'string') {
      content = artifact.text;
    } else if (artifact.data) {
      if (typeof artifact.data === 'string') {
        content = artifact.data;
      } else if (typeof artifact.data === 'object') {
        // Try to extract content from common fields
        const possibleContentFields = ['content', 'text', 'body', 'article', 'markdown', 'optimizedContent', 'finalContent'];
        for (const field of possibleContentFields) {
          if (artifact.data[field] && typeof artifact.data[field] === 'string') {
            content = artifact.data[field];
            break;
          }
        }

        // If no specific field found, stringify the object
        if (!content) {
          content = JSON.stringify(artifact.data, null, 2);
        }
      }
    }

    // If content is JSON, try to parse it to extract content
    if (content.trim().startsWith('{') && content.trim().endsWith('}')) {
      try {
        const parsedContent = JSON.parse(content);
        if (parsedContent.content) {
          content = parsedContent.content;
        } else if (parsedContent.text) {
          content = parsedContent.text;
        } else if (parsedContent.body) {
          content = parsedContent.body;
        }
      } catch (e) {
        // If parsing fails, keep the original content
        console.log('Failed to parse JSON content', e);
      }
    }

    setTitle(extractedTitle);
    setArticle(content);
    setEditedTitle(extractedTitle);
    setEditedContent(content);
  };

  // Toggle edit mode
  const handleToggleEditMode = () => {
    if (editMode) {
      // Save changes
      setTitle(editedTitle);
      setArticle(editedContent);
    } else {
      // Enter edit mode
      setEditedTitle(title);
      setEditedContent(article);
    }

    setEditMode(!editMode);
  };

  // Save article changes
  const handleSaveArticle = async () => {
    if (!sessionId) return;

    setSaving(true);

    try {
      // Create a user message with the edited content
      const messageResult = await dynamicCollaborationClientV2.addUserMessage(
        sessionId,
        JSON.stringify({
          action: 'save-article',
          title: editedTitle,
          content: editedContent
        })
      );

      // Update local state
      setTitle(editedTitle);
      setArticle(editedContent);
      setEditMode(false);

      // Show success message
      setSnackbar({
        open: true,
        message: 'Article saved successfully',
        severity: 'success'
      });

      // Refresh data
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error('Error saving article:', error);

      // Show error message
      setSnackbar({
        open: true,
        message: `Error saving article: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error'
      });
    } finally {
      setSaving(false);
    }
  };

  // Copy article to clipboard
  const handleCopyArticle = () => {
    navigator.clipboard.writeText(`# ${title}\n\n${article}`);

    setSnackbar({
      open: true,
      message: 'Article copied to clipboard',
      severity: 'success'
    });
  };

  // Download article as markdown
  const handleDownloadArticle = () => {
    const content = `# ${title}\n\n${article}`;
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title.replace(/[^a-z0-9]/gi, '-').toLowerCase()}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    setSnackbar({
      open: true,
      message: 'Article downloaded as markdown',
      severity: 'success'
    });
  };

  // Open publish dialog
  const handleOpenPublishDialog = () => {
    setPublishDialogOpen(true);
  };

  // Close publish dialog
  const handleClosePublishDialog = () => {
    setPublishDialogOpen(false);
  };

  // Publish article
  const handlePublishArticle = async () => {
    if (!sessionId) return;

    setSaving(true);

    try {
      // Create a user message with the publish action
      const messageResult = await dynamicCollaborationClientV2.addUserMessage(
        sessionId,
        JSON.stringify({
          action: 'publish-article',
          title: title,
          content: article,
          status: 'published'
        })
      );

      // Close dialog
      setPublishDialogOpen(false);

      // Show success message
      setSnackbar({
        open: true,
        message: 'Article published successfully',
        severity: 'success'
      });

      // Refresh data
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error('Error publishing article:', error);

      // Show error message
      setSnackbar({
        open: true,
        message: `Error publishing article: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error'
      });
    } finally {
      setSaving(false);
      setPublishDialogOpen(false);
    }
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Box>
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      <Paper elevation={1} sx={{ p: 2, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Article Preview</Typography>
          <Box>
            <Tooltip title={editMode ? 'Save Changes' : 'Edit Article'}>
              <IconButton
                onClick={handleToggleEditMode}
                color={editMode ? 'primary' : 'default'}
                disabled={saving || loading}
              >
                {editMode ? <SaveIcon /> : <EditIcon />}
              </IconButton>
            </Tooltip>

            <Tooltip title="Copy to Clipboard">
              <IconButton
                onClick={handleCopyArticle}
                disabled={saving || loading || !article}
              >
                <ContentCopyIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Download as Markdown">
              <IconButton
                onClick={handleDownloadArticle}
                disabled={saving || loading || !article}
              >
                <DownloadIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Publish Article">
              <IconButton
                onClick={handleOpenPublishDialog}
                disabled={saving || loading || !article}
                color="secondary"
              >
                <PublishIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {loading && !article ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
            <CircularProgress />
          </Box>
        ) : !article ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
            <Typography variant="body1" color="text.secondary">
              No article content available yet
            </Typography>
          </Box>
        ) : (
          <Box>
            {editMode ? (
              <Box>
                <TextField
                  fullWidth
                  label="Article Title"
                  value={editedTitle}
                  onChange={(e) => setEditedTitle(e.target.value)}
                  variant="outlined"
                  sx={{ mb: 2 }}
                />

                <TextField
                  fullWidth
                  label="Article Content (Markdown)"
                  value={editedContent}
                  onChange={(e) => setEditedContent(e.target.value)}
                  multiline
                  rows={20}
                  variant="outlined"
                  sx={{ mb: 2 }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                  <Button
                    variant="outlined"
                    onClick={() => setEditMode(false)}
                    disabled={saving}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    onClick={handleSaveArticle}
                    disabled={saving}
                    startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
                  >
                    Save Changes
                  </Button>
                </Box>
              </Box>
            ) : (
              <Box>
                <Typography variant="h4" gutterBottom>
                  {title}
                </Typography>

                <Divider sx={{ my: 2 }} />

                <Box sx={{
                  p: 2,
                  bgcolor: 'grey.50',
                  borderRadius: 1,
                  '& img': {
                    maxWidth: '100%',
                    height: 'auto'
                  }
                }}>
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>
                    {article}
                  </ReactMarkdown>
                </Box>
              </Box>
            )}
          </Box>
        )}
      </Paper>

      {/* Article metadata */}
      {article && state && (
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Article Details
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Topic:
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {state.topic || 'N/A'}
                </Typography>

                <Typography variant="body2" color="text.secondary">
                  Content Type:
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {state.contentType || 'N/A'}
                </Typography>

                <Typography variant="body2" color="text.secondary">
                  Target Audience:
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {state.targetAudience || 'N/A'}
                </Typography>

                <Typography variant="body2" color="text.secondary">
                  Tone:
                </Typography>
                <Typography variant="body1">
                  {state.tone || 'N/A'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Keywords
                </Typography>
                {state.keywords && state.keywords.length > 0 ? (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {state.keywords.map((keyword, index) => (
                      <Chip
                        key={index}
                        label={keyword}
                        size="small"
                        sx={{ mb: 0.5 }}
                      />
                    ))}
                  </Box>
                ) : (
                  <Typography variant="body1" color="text.secondary">
                    No keywords available
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Statistics
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Word Count:
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {article.split(/\s+/).filter(Boolean).length}
                </Typography>

                <Typography variant="body2" color="text.secondary">
                  Character Count:
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {article.length}
                </Typography>

                <Typography variant="body2" color="text.secondary">
                  Reading Time:
                </Typography>
                <Typography variant="body1">
                  {Math.max(1, Math.round(article.split(/\s+/).filter(Boolean).length / 200))} min read
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Publish Dialog */}
      <Dialog
        open={publishDialogOpen}
        onClose={handleClosePublishDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Publish Article</DialogTitle>
        <DialogContent>
          <Typography variant="body1" paragraph>
            Are you sure you want to publish this article?
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Title: {title}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePublishDialog}>Cancel</Button>
          <Button
            onClick={handlePublishArticle}
            variant="contained"
            color="primary"
            disabled={saving}
            startIcon={saving ? <CircularProgress size={20} /> : <PublishIcon />}
          >
            Publish
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ArticlePreview;
