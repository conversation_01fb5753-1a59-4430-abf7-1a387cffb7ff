'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Chip,
  Avatar,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tooltip,
  IconButton,
  Collapse,
  Badge,
  useTheme
} from '@mui/material';
import PsychologyIcon from '@mui/icons-material/Psychology';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ChatIcon from '@mui/icons-material/Chat';
import AssignmentIcon from '@mui/icons-material/Assignment';
import MemoryIcon from '@mui/icons-material/Memory';
import { DynamicCollaborationState } from '../../app/(payload)/api/agents/dynamic-collaboration-v2/state';

interface AgentStatus {
  id: string;
  name: string;
  status: 'idle' | 'active' | 'completed';
  currentGoal?: string;
  lastActivity?: string;
  messageCount: number;
  artifactCount: number;
  specialization: string;
}

interface AgentStatusPanelProps {
  sessionId: string;
  state: DynamicCollaborationState | null;
  loading?: boolean;
  onRefresh?: () => void;
}

const AgentStatusPanel: React.FC<AgentStatusPanelProps> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const theme = useTheme();
  const [agents, setAgents] = useState<AgentStatus[]>([]);
  const [expandedAgent, setExpandedAgent] = useState<string | null>(null);

  // Agent colors and specializations
  const agentColors: Record<string, string> = {
    'market-research': theme.palette.primary.main,
    'seo-keyword': theme.palette.secondary.main,
    'content-strategy': theme.palette.info.main,
    'content-generation': theme.palette.success.main,
    'seo-optimization': theme.palette.warning.main,
    'system': theme.palette.grey[500],
    'user': theme.palette.error.main
  };

  const agentNames: Record<string, string> = {
    'market-research': 'Market Research',
    'seo-keyword': 'SEO Keyword',
    'content-strategy': 'Content Strategy',
    'content-generation': 'Content Writer',
    'seo-optimization': 'SEO Optimizer',
    'system': 'System',
    'user': 'User'
  };

  const agentSpecializations: Record<string, string> = {
    'market-research': 'Audience and competitor analysis',
    'seo-keyword': 'Keyword research and optimization',
    'content-strategy': 'Content planning and structure',
    'content-generation': 'Article writing and formatting',
    'seo-optimization': 'SEO optimization and enhancement',
    'system': 'Workflow orchestration',
    'user': 'Human feedback and guidance'
  };

  // Extract agent information from state
  useEffect(() => {
    if (!state) return;

    const extractedAgents: AgentStatus[] = [];
    const activeAgentIds = new Set<string>();

    // Get active agents from goals
    if (state.goals && state.activeGoals) {
      state.activeGoals.forEach(goalId => {
        const goal = state.goals[goalId];
        if (goal && goal.assignedTo) {
          goal.assignedTo.forEach(agentId => {
            activeAgentIds.add(agentId);
          });
        }
      });
    }

    // Count messages per agent
    const messageCountByAgent: Record<string, number> = {};
    if (state.dynamicMessages) {
      Object.values(state.dynamicMessages).forEach(message => {
        if (message.from) {
          messageCountByAgent[message.from] = (messageCountByAgent[message.from] || 0) + 1;
        }
      });
    }

    // Count artifacts per agent
    const artifactCountByAgent: Record<string, number> = {};
    if (state.artifacts) {
      Object.values(state.artifacts).forEach(artifact => {
        if (artifact.creator) {
          artifactCountByAgent[artifact.creator] = (artifactCountByAgent[artifact.creator] || 0) + 1;
        }
      });
    }

    // Get current goals for agents
    const currentGoalByAgent: Record<string, string> = {};
    if (state.goals && state.activeGoals) {
      state.activeGoals.forEach(goalId => {
        const goal = state.goals[goalId];
        if (goal && goal.assignedTo) {
          goal.assignedTo.forEach(agentId => {
            currentGoalByAgent[agentId] = goal.description;
          });
        }
      });
    }

    // Get last activity timestamp for agents
    const lastActivityByAgent: Record<string, string> = {};
    if (state.dynamicMessages) {
      Object.values(state.dynamicMessages).forEach(message => {
        if (message.from && message.timestamp) {
          if (!lastActivityByAgent[message.from] ||
              new Date(message.timestamp) > new Date(lastActivityByAgent[message.from])) {
            lastActivityByAgent[message.from] = message.timestamp;
          }
        }
      });
    }

    // Create agent status objects
    const knownAgentIds = new Set([
      ...Object.keys(messageCountByAgent),
      ...Object.keys(artifactCountByAgent),
      ...activeAgentIds
    ]);

    // Add standard agents even if they haven't been active yet
    ['market-research', 'seo-keyword', 'content-strategy', 'content-generation', 'seo-optimization'].forEach(id => {
      knownAgentIds.add(id);
    });

    knownAgentIds.forEach(agentId => {
      if (agentId === 'system' || agentId === 'user') return; // Skip system and user

      extractedAgents.push({
        id: agentId,
        name: agentNames[agentId] || agentId,
        status: activeAgentIds.has(agentId) ? 'active' :
                (messageCountByAgent[agentId] || artifactCountByAgent[agentId]) ? 'completed' : 'idle',
        currentGoal: currentGoalByAgent[agentId],
        lastActivity: lastActivityByAgent[agentId],
        messageCount: messageCountByAgent[agentId] || 0,
        artifactCount: artifactCountByAgent[agentId] || 0,
        specialization: agentSpecializations[agentId] || 'General assistance'
      });
    });

    // Sort agents: active first, then completed, then idle
    extractedAgents.sort((a, b) => {
      if (a.status === 'active' && b.status !== 'active') return -1;
      if (a.status !== 'active' && b.status === 'active') return 1;
      if (a.status === 'completed' && b.status === 'idle') return -1;
      if (a.status === 'idle' && b.status === 'completed') return 1;
      return 0;
    });

    setAgents(extractedAgents);
  }, [state]);

  // Toggle agent expansion
  const toggleAgentExpansion = (agentId: string) => {
    setExpandedAgent(expandedAgent === agentId ? null : agentId);
  };

  // Format time
  const formatTime = (timestamp?: string) => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <Box>
      <Paper elevation={1} sx={{ p: 2, borderRadius: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Agent Status Overview
        </Typography>

        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={4}>
            <Card variant="outlined">
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary">
                  {agents.filter(a => a.status === 'active').length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Active Agents
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={4}>
            <Card variant="outlined">
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="success.main">
                  {state?.generatedArtifacts?.length || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Generated Artifacts
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={4}>
            <Card variant="outlined">
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="info.main">
                  {Object.keys(state?.dynamicMessages || {}).length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Agent Messages
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>

      <Typography variant="h6" gutterBottom>
        Agent Details
      </Typography>

      {loading && agents.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
          <CircularProgress />
        </Box>
      ) : agents.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
          <Typography variant="body1" color="text.secondary">
            No agent data available
          </Typography>
        </Box>
      ) : (
        <Grid container spacing={2}>
          {agents.map(agent => (
            <Grid item xs={12} md={6} key={agent.id}>
              <Card
                variant={agent.status === 'active' ? 'elevation' : 'outlined'}
                elevation={agent.status === 'active' ? 3 : 1}
                sx={{
                  borderLeft: '4px solid',
                  borderLeftColor: agentColors[agent.id] || theme.palette.grey[500],
                  position: 'relative',
                  overflow: 'visible'
                }}
              >
                {agent.status === 'active' && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: -8,
                      right: -8,
                      bgcolor: 'primary.main',
                      color: 'white',
                      borderRadius: '50%',
                      width: 24,
                      height: 24,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '0.75rem',
                      fontWeight: 'bold',
                      boxShadow: 1
                    }}
                  >
                    <MemoryIcon fontSize="small" />
                  </Box>
                )}

                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar
                        sx={{
                          bgcolor: agentColors[agent.id] || theme.palette.grey[500],
                          mr: 1.5
                        }}
                      >
                        <PsychologyIcon />
                      </Avatar>

                      <Box>
                        <Typography variant="h6" component="div">
                          {agent.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {agent.specialization}
                        </Typography>
                      </Box>
                    </Box>

                    <Box>
                      <Chip
                        label={agent.status}
                        color={
                          agent.status === 'active' ? 'primary' :
                          agent.status === 'completed' ? 'success' : 'default'
                        }
                        size="small"
                        icon={
                          agent.status === 'active' ? <HourglassEmptyIcon /> :
                          agent.status === 'completed' ? <CheckCircleIcon /> : undefined
                        }
                      />
                    </Box>
                  </Box>

                  <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="Messages sent">
                        <Badge badgeContent={agent.messageCount} color="primary" max={99}>
                          <ChatIcon color="action" />
                        </Badge>
                      </Tooltip>

                      <Tooltip title="Artifacts created">
                        <Badge badgeContent={agent.artifactCount} color="secondary" max={99}>
                          <AssignmentIcon color="action" />
                        </Badge>
                      </Tooltip>
                    </Box>

                    <IconButton
                      size="small"
                      onClick={() => toggleAgentExpansion(agent.id)}
                    >
                      {expandedAgent === agent.id ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                  </Box>

                  <Collapse in={expandedAgent === agent.id}>
                    <Box sx={{ mt: 2 }}>
                      <Divider sx={{ my: 1 }} />

                      <List dense disablePadding>
                        {agent.currentGoal && (
                          <ListItem disablePadding sx={{ py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 36 }}>
                              <AssignmentIcon fontSize="small" />
                            </ListItemIcon>
                            <ListItemText
                              primary="Current Goal"
                              secondary={agent.currentGoal}
                              primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                              secondaryTypographyProps={{ variant: 'body2' }}
                            />
                          </ListItem>
                        )}

                        {agent.lastActivity && (
                          <ListItem disablePadding sx={{ py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 36 }}>
                              <HourglassEmptyIcon fontSize="small" />
                            </ListItemIcon>
                            <ListItemText
                              primary="Last Activity"
                              secondary={formatTime(agent.lastActivity)}
                              primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                              secondaryTypographyProps={{ variant: 'body2' }}
                            />
                          </ListItem>
                        )}

                        <ListItem disablePadding sx={{ py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 36 }}>
                            <ChatIcon fontSize="small" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Messages Sent"
                            secondary={agent.messageCount}
                            primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                            secondaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>

                        <ListItem disablePadding sx={{ py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 36 }}>
                            <AssignmentIcon fontSize="small" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Artifacts Created"
                            secondary={agent.artifactCount}
                            primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                            secondaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      </List>
                    </Box>
                  </Collapse>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
};

export default AgentStatusPanel;
