'use client';

import React from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Card, 
  CardContent,
  Divider,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import LightbulbOutlinedIcon from '@mui/icons-material/LightbulbOutlined';
import PsychologyIcon from '@mui/icons-material/Psychology';
import FactCheckIcon from '@mui/icons-material/FactCheck';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';

interface ReasoningDisplayProps {
  state: any;
}

export const ReasoningDisplay: React.FC<ReasoningDisplayProps> = ({ state }) => {
  // Map agent IDs to colors and icons
  const agentConfig: Record<string, { color: string; icon: React.ReactNode; name: string }> = {
    'content-generation': {
      color: '#4caf50',
      icon: <AutoAwesomeIcon />,
      name: 'Content Generation'
    },
    'seo-optimization': {
      color: '#2196f3',
      icon: <PsychologyIcon />,
      name: 'SEO Optimization'
    },
    'market-research': {
      color: '#ff9800',
      icon: <FactCheckIcon />,
      name: 'Market Research'
    },
    'content-strategy': {
      color: '#9c27b0',
      icon: <LightbulbOutlinedIcon />,
      name: 'Content Strategy'
    },
    'seo-keyword': {
      color: '#00bcd4',
      icon: <LightbulbOutlinedIcon />,
      name: 'SEO Keyword'
    }
  };

  // Get agent config with fallback
  const getAgentConfig = (agentId: string) => {
    return agentConfig[agentId] || {
      color: '#757575',
      icon: <LightbulbOutlinedIcon />,
      name: 'Unknown Agent'
    };
  };

  // Format confidence as percentage
  const formatConfidence = (confidence: number): string => {
    return `${Math.round(confidence * 100)}%`;
  };

  // Check if there are any reasoning decisions in the state
  const hasReasoningData = state.decisions && state.decisions.length > 0;
  console.log("Reasoning Data: ", state, "decisions: ", state.decisions);
  // Render reasoning data
const renderReasoningData = () => {
  if (!Array.isArray(state.decisions) || state.decisions.length === 0) {
    return (
      <Typography variant="body2" color="textSecondary">
        No reasoning data available yet. As agents make decisions, their reasoning will appear here.
      </Typography>
    );
  }

  // Group decisions by agent
  const decisionsByAgent: Record<string, any[]> = {};
  state.decisions.forEach((decision: any) => {
    const agentId = decision.agent || 'unknown';
    if (!decisionsByAgent[agentId]) {
      decisionsByAgent[agentId] = [];
    }
    decisionsByAgent[agentId].push(decision);
  });

  return (
    <Box sx={{ mt: 2 }}>
      {Object.entries(decisionsByAgent).map(([agentId, decisions]) => {
        const agentConf = getAgentConfig(agentId);

        return (
          <Accordion key={agentId} defaultExpanded sx={{ mb: 2 }}>
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              sx={{
                bgcolor: `${agentConf.color}15`,
                borderLeft: `4px solid ${agentConf.color}`
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: agentConf.color, width: 32, height: 32, mr: 1.5 }}>
                  {agentConf.icon}
                </Avatar>
                <Typography variant="subtitle1">{agentConf.name} Reasoning</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <List>
                {decisions.map((decision: any, index: number) => (
                  <Card key={`decision-${index}`} variant="outlined" sx={{ mb: 2 }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="subtitle2">
                          {decision.process || decision.context || 'Decision'}
                        </Typography>
                        {decision.confidence !== undefined && (
                          <Chip
                            label={`Confidence: ${formatConfidence(decision.confidence)}`}
                            size="small"
                            color={decision.confidence > 0.7 ? "success" :
                              decision.confidence > 0.4 ? "warning" : "error"}
                          />
                        )}
                      </Box>
                      <Divider sx={{ mb: 1.5 }} />

                      {/* Thoughts */}
                      {Array.isArray(decision.thoughts) && decision.thoughts.length > 0 && (
                        <Box sx={{ mb: 1.5 }}>
                          <Typography variant="body2" fontWeight="medium">Thoughts:</Typography>
                          <List dense disablePadding>
                            {decision.thoughts.map((thoughtObj: any, i: number) => (
                              <ListItem key={`thought-${i}`} disablePadding sx={{ py: 0.5 }}>
                                <ListItemIcon sx={{ minWidth: 36 }}>
                                  <LightbulbOutlinedIcon fontSize="small" color="primary" />
                                </ListItemIcon>
                                <ListItemText
                                  primary={thoughtObj.thought}
                                  secondary={
                                    thoughtObj.confidence !== undefined
                                      ? `Confidence: ${formatConfidence(thoughtObj.confidence)}`
                                      : undefined
                                  }
                                />
                              </ListItem>
                            ))}
                          </List>
                        </Box>
                      )}

                      {/* Considerations */}
                      {Array.isArray(decision.considerations) && decision.considerations.length > 0 && (
                        <Box sx={{ mb: 1.5 }}>
                          <Typography variant="body2" fontWeight="medium">Considerations:</Typography>
                          <List dense disablePadding>
                            {decision.considerations.map((consideration: any, i: number) => (
                              <ListItem key={`consideration-${i}`} disablePadding sx={{ py: 0.5 }}>
                                <ListItemIcon sx={{ minWidth: 36 }}>
                                  <FactCheckIcon fontSize="small" color="secondary" />
                                </ListItemIcon>
                                <ListItemText
                                  primary={`${consideration.factor}: ${consideration.explanation}`}
                                  secondary={consideration.impact ? `Impact: ${consideration.impact}` : undefined}
                                />
                              </ListItem>
                            ))}
                          </List>
                        </Box>
                      )}

                      {/* Decision */}
                      {decision.decision && (
                        <Box sx={{ mt: 1.5 }}>
                          <Typography variant="body2" fontWeight="medium">Decision:</Typography>
                          <Typography variant="body2">{decision.decision}</Typography>
                        </Box>
                      )}

                      {/* Outcome */}
                      {decision.outcome && (
                        <Box sx={{ mt: 1.5 }}>
                          <Typography variant="body2" fontWeight="medium">Outcome:</Typography>
                          <Typography variant="body2">{decision.outcome}</Typography>
                        </Box>
                      )}

                      {/* Reasoning (string) */}
                      {decision.reasoning && (
                        <Box sx={{ mt: 1.5 }}>
                          <Typography variant="body2" fontWeight="medium">Reasoning:</Typography>
                          <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>{decision.reasoning}</Typography>
                        </Box>
                      )}

                      <Typography variant="caption" color="textSecondary" sx={{ display: 'block', mt: 1.5 }}>
                        {decision.timestamp ? new Date(decision.timestamp).toLocaleString() : ''}
                      </Typography>
                    </CardContent>
                  </Card>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        );
      })}
    </Box>
  );
};

  return (
    <Paper elevation={0} sx={{ p: 2, mb: 3, bgcolor: 'background.default' }}>
      <Typography variant="subtitle1" gutterBottom>
        Agent Reasoning
      </Typography>
      {renderReasoningData()}
    </Paper>
  );
};
