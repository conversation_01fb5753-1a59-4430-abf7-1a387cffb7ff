/**
 * Main Navigation Component
 * Navigation for all system features
 */

'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState, useEffect } from 'react';

interface NavItem {
  href: string;
  label: string;
  description: string;
  icon: string;
  category: 'core' | 'enhanced' | 'advanced';
}

const navItems: NavItem[] = [
  // Core Features
  {
    href: '/workflow',
    label: 'Workflow System',
    description: 'Basic workflow execution with templates',
    icon: '⚙️',
    category: 'core'
  },

  // Enhanced Features (Phase 1)
  {
    href: '/bulk-operations',
    label: 'Bulk Operations',
    description: 'CSV import/export and batch processing',
    icon: '📊',
    category: 'enhanced'
  },
  {
    href: '/enhanced-review',
    label: 'Enhanced Review',
    description: 'Multi-reviewer system with deadlines',
    icon: '👥',
    category: 'enhanced'
  },

  // Advanced Features (Future)
  {
    href: '/visual-builder',
    label: 'Visual Builder',
    description: 'Drag-and-drop workflow designer',
    icon: '🎨',
    category: 'advanced'
  },
  {
    href: '/cms-integration',
    label: 'CMS Integration',
    description: 'WordPress, Shopify publishing',
    icon: '🔗',
    category: 'advanced'
  },
  {
    href: '/knowledge-base',
    label: 'Knowledge Base',
    description: 'Research agent and content analysis',
    icon: '🧠',
    category: 'advanced'
  }
];

export default function MainNavigation() {
  const pathname = usePathname();
  const [systemStats, setSystemStats] = useState({
    totalTemplates: 7,
    activeWorkflows: 0,
    pendingReviews: 0,
    completedToday: 0
  });

  useEffect(() => {
    // In a real implementation, this would fetch actual stats
    setSystemStats({
      totalTemplates: 7,
      activeWorkflows: Math.floor(Math.random() * 5) + 1,
      pendingReviews: Math.floor(Math.random() * 10) + 2,
      completedToday: Math.floor(Math.random() * 15) + 5
    });
  }, []);

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'core': return 'border-green-200 bg-green-50';
      case 'enhanced': return 'border-blue-200 bg-blue-50';
      case 'advanced': return 'border-purple-200 bg-purple-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'core': return 'Core Features';
      case 'enhanced': return 'Enhanced Features (Phase 1)';
      case 'advanced': return 'Advanced Features (Coming Soon)';
      default: return '';
    }
  };

  const groupedItems = navItems.reduce((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = [];
    }
    acc[item.category].push(item);
    return acc;
  }, {} as Record<string, NavItem[]>);

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-4">Content Generation Platform</h1>
        <p className="text-lg text-gray-600">
          AI-powered content creation with human-in-the-loop review and workflow automation
        </p>
      </div>

      {/* Feature Categories */}
      {Object.entries(groupedItems).map(([category, items]) => (
        <div key={category} className="mb-8">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800">
            {getCategoryLabel(category)}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {items.map((item) => {
              const isActive = pathname === item.href;
              const isAdvanced = item.category === 'advanced';

              return (
                <div
                  key={item.href}
                  className={`border-2 rounded-lg p-6 transition-all duration-200 ${
                    isActive
                      ? 'border-blue-500 bg-blue-50 shadow-lg'
                      : getCategoryColor(item.category)
                  } ${
                    isAdvanced
                      ? 'opacity-60 cursor-not-allowed'
                      : 'hover:shadow-md hover:border-blue-300 cursor-pointer'
                  }`}
                >
                  {isAdvanced ? (
                    <div>
                      <div className="flex items-center mb-3">
                        <span className="text-2xl mr-3">{item.icon}</span>
                        <h3 className="text-lg font-semibold text-gray-700">{item.label}</h3>
                      </div>
                      <p className="text-sm text-gray-500 mb-4">{item.description}</p>
                      <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-700">
                        Coming Soon
                      </div>
                    </div>
                  ) : (
                    <Link href={item.href} className="block">
                      <div className="flex items-center mb-3">
                        <span className="text-2xl mr-3">{item.icon}</span>
                        <h3 className="text-lg font-semibold text-gray-800">{item.label}</h3>
                      </div>
                      <p className="text-sm text-gray-600 mb-4">{item.description}</p>
                      <div className="flex items-center text-blue-600 text-sm font-medium">
                        {isActive ? 'Currently Active' : 'Open →'}
                      </div>
                    </Link>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      ))}

      {/* System Status */}
      <div className="mt-12 p-6 bg-gray-100 rounded-lg">
        <h3 className="text-lg font-semibold mb-4">System Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded border">
            <h4 className="font-medium text-green-600">✅ Core System</h4>
            <p className="text-sm text-gray-600">Fully operational with 3 templates</p>
          </div>
          <div className="bg-white p-4 rounded border">
            <h4 className="font-medium text-blue-600">🚀 Enhanced Features</h4>
            <p className="text-sm text-gray-600">Phase 1 implementation in progress</p>
          </div>
          <div className="bg-white p-4 rounded border">
            <h4 className="font-medium text-purple-600">🔮 Advanced Features</h4>
            <p className="text-sm text-gray-600">Planned for Phase 2-3</p>
          </div>
        </div>
      </div>

      {/* Live System Stats */}
      <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border text-center">
          <div className="text-2xl font-bold text-blue-600">{systemStats.totalTemplates}</div>
          <div className="text-sm text-gray-600">Templates Available</div>
        </div>
        <div className="bg-white p-4 rounded-lg border text-center">
          <div className="text-2xl font-bold text-green-600">{systemStats.activeWorkflows}</div>
          <div className="text-sm text-gray-600">Active Workflows</div>
        </div>
        <div className="bg-white p-4 rounded-lg border text-center">
          <div className="text-2xl font-bold text-yellow-600">{systemStats.pendingReviews}</div>
          <div className="text-sm text-gray-600">Pending Reviews</div>
        </div>
        <div className="bg-white p-4 rounded-lg border text-center">
          <div className="text-2xl font-bold text-purple-600">{systemStats.completedToday}</div>
          <div className="text-sm text-gray-600">Completed Today</div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-8 bg-white p-6 rounded-lg border">
        <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link
            href="/workflow"
            className="flex items-center justify-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
          >
            <div className="text-center">
              <div className="text-2xl mb-2">🚀</div>
              <div className="font-medium text-blue-800">Start New Workflow</div>
              <div className="text-sm text-blue-600">Create content with AI</div>
            </div>
          </Link>

          <Link
            href="/bulk-operations"
            className="flex items-center justify-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
          >
            <div className="text-center">
              <div className="text-2xl mb-2">📊</div>
              <div className="font-medium text-green-800">Bulk Operations</div>
              <div className="text-sm text-green-600">Process multiple items</div>
            </div>
          </Link>

          <Link
            href="/enhanced-review"
            className="flex items-center justify-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors"
          >
            <div className="text-center">
              <div className="text-2xl mb-2">👥</div>
              <div className="font-medium text-purple-800">Review Dashboard</div>
              <div className="text-sm text-purple-600">Manage content reviews</div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}
