'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Divider,
  TextField,
  Chip,
  Grid,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  Alert,
  useTheme
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DownloadIcon from '@mui/icons-material/Download';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import dynamic from 'next/dynamic';
import remarkGfm from 'remark-gfm';

// Dynamically import ReactMarkdown to avoid SSR issues
const ReactMarkdown = dynamic(() => import('react-markdown'), { ssr: false });

// Import types from our V3 implementation
import { 
  CollaborationState, 
  GoalType,
  WorkflowPhase
} from '../../app/(payload)/api/agents/dynamic-collaboration-v3';

interface ArticlePreviewV3Props {
  sessionId: string;
  state: CollaborationState | null;
  loading?: boolean;
}

const ArticlePreviewV3: React.FC<ArticlePreviewV3Props> = ({
  sessionId,
  state,
  loading = false
}) => {
  const theme = useTheme();
  const [editMode, setEditMode] = useState(false);
  const [articleContent, setArticleContent] = useState<string>('');
  const [originalContent, setOriginalContent] = useState<string>('');
  const [articleTitle, setArticleTitle] = useState<string>('');
  const [copySuccess, setCopySuccess] = useState<boolean>(false);
  const [saveSuccess, setSaveSuccess] = useState<boolean>(false);

  // Find the final article content
  useEffect(() => {
    if (!state) {
      setArticleContent('');
      setArticleTitle('');
      setOriginalContent('');
      return;
    }

    // First try to find the SEO-optimized article
    const seoGoals = Object.values(state.goals).filter(goal => 
      goal.type === GoalType.SEO_OPTIMIZATION
    );

    if (seoGoals.length > 0 && seoGoals[0].artifactIds && seoGoals[0].artifactIds.length > 0) {
      const seoArtifactId = seoGoals[0].artifactIds[0];
      const seoArtifact = state.artifacts[seoArtifactId];
      
      if (seoArtifact) {
        // Extract content based on structure
        let content = '';
        let title = '';
        
        if (typeof seoArtifact.content === 'string') {
          content = seoArtifact.content;
          title = seoArtifact.title;
        } else if (seoArtifact.content.content) {
          content = seoArtifact.content.content;
          title = seoArtifact.content.title || seoArtifact.title;
        }
        
        setArticleContent(content);
        setArticleTitle(title);
        setOriginalContent(content);
        return;
      }
    }

    // If no SEO article, try to find the content creation article
    const contentGoals = Object.values(state.goals).filter(goal => 
      goal.type === GoalType.CONTENT_CREATION
    );

    if (contentGoals.length > 0 && contentGoals[0].artifactIds && contentGoals[0].artifactIds.length > 0) {
      const contentArtifactId = contentGoals[0].artifactIds[0];
      const contentArtifact = state.artifacts[contentArtifactId];
      
      if (contentArtifact) {
        // Extract content based on structure
        let content = '';
        let title = '';
        
        if (typeof contentArtifact.content === 'string') {
          content = contentArtifact.content;
          title = contentArtifact.title;
        } else if (contentArtifact.content.content) {
          content = contentArtifact.content.content;
          title = contentArtifact.content.title || contentArtifact.title;
        }
        
        setArticleContent(content);
        setArticleTitle(title);
        setOriginalContent(content);
        return;
      }
    }

    // If no content found, set empty
    setArticleContent('');
    setArticleTitle('');
    setOriginalContent('');
  }, [state]);

  // Toggle edit mode
  const toggleEditMode = () => {
    setEditMode(!editMode);
    
    // If exiting edit mode, reset to original content
    if (editMode) {
      setArticleContent(originalContent);
    }
  };

  // Save edited content
  const saveContent = () => {
    setOriginalContent(articleContent);
    setEditMode(false);
    setSaveSuccess(true);
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSaveSuccess(false);
    }, 3000);
  };

  // Copy content to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(articleContent);
    setCopySuccess(true);
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setCopySuccess(false);
    }, 3000);
  };

  // Download content as markdown
  const downloadMarkdown = () => {
    const element = document.createElement('a');
    const file = new Blob([articleContent], {type: 'text/markdown'});
    element.href = URL.createObjectURL(file);
    element.download = `${articleTitle.replace(/[^a-z0-9]/gi, '-').toLowerCase()}.md`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  // Check if article is ready
  const isArticleReady = (): boolean => {
    return !!articleContent && !!articleTitle;
  };

  // Check if workflow is complete
  const isWorkflowComplete = (): boolean => {
    return state?.currentPhase === WorkflowPhase.FINALIZATION;
  };

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Article Preview</Typography>
        <Box>
          {isArticleReady() && (
            <>
              <Tooltip title={editMode ? "Cancel Editing" : "Edit Content"}>
                <IconButton onClick={toggleEditMode} color={editMode ? "error" : "primary"}>
                  <EditIcon />
                </IconButton>
              </Tooltip>
              
              {editMode ? (
                <Tooltip title="Save Changes">
                  <IconButton onClick={saveContent} color="success">
                    <SaveIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <>
                  <Tooltip title="Copy to Clipboard">
                    <IconButton onClick={copyToClipboard}>
                      <ContentCopyIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Download as Markdown">
                    <IconButton onClick={downloadMarkdown}>
                      <DownloadIcon />
                    </IconButton>
                  </Tooltip>
                </>
              )}
            </>
          )}
        </Box>
      </Box>

      {/* Status messages */}
      {copySuccess && (
        <Alert severity="success" sx={{ mb: 2 }}>
          Content copied to clipboard!
        </Alert>
      )}
      
      {saveSuccess && (
        <Alert severity="success" sx={{ mb: 2 }}>
          Changes saved successfully!
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : !isArticleReady() ? (
        <Box sx={{ textAlign: 'center', p: 4 }}>
          <Typography variant="body1" color="text.secondary">
            {state ? 'Article content not available yet. Please wait for the content creation phase to complete.' : 'No session data available'}
          </Typography>
        </Box>
      ) : (
        <Box>
          {/* Article metadata */}
          <Card variant="outlined" sx={{ mb: 3 }}>
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} md={8}>
                  <Typography variant="h5" gutterBottom>
                    {articleTitle}
                  </Typography>
                  {state && (
                    <>
                      <Chip 
                        label={state.contentType} 
                        color="primary" 
                        size="small" 
                        sx={{ mr: 1, mb: 1 }} 
                      />
                      <Chip 
                        label={`Audience: ${state.targetAudience}`} 
                        color="secondary" 
                        size="small" 
                        sx={{ mr: 1, mb: 1 }} 
                      />
                      <Chip 
                        label={`Tone: ${state.tone}`} 
                        variant="outlined" 
                        size="small" 
                        sx={{ mr: 1, mb: 1 }} 
                      />
                    </>
                  )}
                </Grid>
                <Grid item xs={12} md={4} sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
                  {isWorkflowComplete() ? (
                    <Chip 
                      icon={<CheckCircleIcon />} 
                      label="Workflow Complete" 
                      color="success" 
                      variant="outlined" 
                    />
                  ) : (
                    <Chip 
                      label="Workflow In Progress" 
                      color="warning" 
                      variant="outlined" 
                    />
                  )}
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Article content */}
          {editMode ? (
            <TextField
              fullWidth
              multiline
              rows={20}
              value={articleContent}
              onChange={(e) => setArticleContent(e.target.value)}
              variant="outlined"
              sx={{ mb: 2, fontFamily: 'monospace' }}
            />
          ) : (
            <Paper 
              variant="outlined" 
              sx={{ 
                p: 3, 
                maxHeight: 'calc(100vh - 300px)', 
                overflow: 'auto',
                bgcolor: 'grey.50'
              }}
            >
              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                {articleContent}
              </ReactMarkdown>
            </Paper>
          )}

          {/* Keywords */}
          {state && state.keywords && state.keywords.length > 0 && (
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Keywords
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {state.keywords.map((keyword, index) => (
                  <Chip 
                    key={index} 
                    label={keyword} 
                    size="small" 
                    variant="outlined" 
                  />
                ))}
              </Box>
            </Box>
          )}
        </Box>
      )}
    </Paper>
  );
};

export default ArticlePreviewV3;
