'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  IconButton,
  Tooltip,
  Card,
  CardContent,
  Divider,
  Chip,
  useTheme
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import InfoIcon from '@mui/icons-material/Info';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import dynamic from 'next/dynamic';

// Import types from our V3 implementation
import {
  CollaborationState,
  Goal,
  GoalStatus,
  GoalType,
  Artifact,
  Message,
  MessageType
} from '../../app/(payload)/api/agents/dynamic-collaboration-v3';

// Dynamically import ForceGraph2D with no SSR to avoid window is not defined error
const ForceGraph2D = dynamic(() => import('react-force-graph-2d'), {
  ssr: false,
  loading: () => <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
    <CircularProgress />
  </Box>
});

interface GraphNode {
  id: string;
  name: string;
  val: number;
  color: string;
  type: 'agent' | 'artifact' | 'goal';
  x?: number;
  y?: number;
}

interface GraphLink {
  source: string;
  target: string;
  value: number;
  type: string;
  label?: string;
  color?: string;
}

interface AgentCollaborationGraphV3Props {
  sessionId: string;
  state: CollaborationState | null;
  loading?: boolean;
  onRefresh?: () => void;
}

const AgentCollaborationGraphV3: React.FC<AgentCollaborationGraphV3Props> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const theme = useTheme();
  const graphRef = useRef<any>();
  const [graphData, setGraphData] = useState<{ nodes: GraphNode[], links: GraphLink[] }>({ nodes: [], links: [] });
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [selectedLink, setSelectedLink] = useState<GraphLink | null>(null);
  const [hoveredNode, setHoveredNode] = useState<GraphNode | null>(null);

  // Zoom controls
  const zoomIn = () => {
    if (graphRef.current) {
      const currentZoom = graphRef.current.zoom();
      graphRef.current.zoom(currentZoom * 1.2, 400);
    }
  };

  const zoomOut = () => {
    if (graphRef.current) {
      const currentZoom = graphRef.current.zoom();
      graphRef.current.zoom(currentZoom / 1.2, 400);
    }
  };

  const resetZoom = () => {
    if (graphRef.current) {
      graphRef.current.zoomToFit(400);
    }
  };

  // Handle node click
  const handleNodeClick = (node: GraphNode) => {
    setSelectedNode(node);
    setSelectedLink(null);
  };

  // Handle link click
  const handleLinkClick = (link: GraphLink) => {
    setSelectedLink(link);
    setSelectedNode(null);
  };

  // Build graph data from state
  useEffect(() => {
    if (!state) {
      setGraphData({ nodes: [], links: [] });
      return;
    }

    const nodes: GraphNode[] = [];
    const links: GraphLink[] = [];
    const nodeMap: Record<string, boolean> = {};

    // Add agent nodes
    const agentIds = new Set<string>();

    // Extract agent IDs from messages
    if (state.messages && state.messages.byId) {
      Object.values(state.messages.byId).forEach(message => {
        if (message.from !== 'system' && message.from !== 'user') {
          agentIds.add(message.from);
        }

        if (typeof message.to === 'string' && message.to !== 'system' && message.to !== 'user' && message.to !== 'all') {
          agentIds.add(message.to);
        } else if (Array.isArray(message.to)) {
          message.to.forEach(recipient => {
            if (recipient !== 'system' && recipient !== 'user' && recipient !== 'all') {
              agentIds.add(recipient);
            }
          });
        }
      });
    }

    // Add agent nodes
    agentIds.forEach(agentId => {
      const nodeId = `agent-${agentId}`;
      nodes.push({
        id: nodeId,
        name: agentId,
        val: 20,
        color: theme.palette.primary.main,
        type: 'agent'
      });
      nodeMap[nodeId] = true;
    });

    // Add goal nodes
    if (state.goals && state.goals.byId) {
      Object.values(state.goals.byId).forEach(goal => {
        const nodeId = `goal-${goal.id}`;
        nodes.push({
          id: nodeId,
          name: goal.description.length > 30 ? goal.description.substring(0, 30) + '...' : goal.description,
          val: 15,
          color: goal.status === GoalStatus.COMPLETED ? theme.palette.success.main :
                goal.status === GoalStatus.IN_PROGRESS ? theme.palette.info.main :
                goal.status === GoalStatus.BLOCKED ? theme.palette.error.main :
                theme.palette.grey[400],
          type: 'goal'
        });
        nodeMap[nodeId] = true;

        // Add links between goals and assigned agents
        if (goal.assignedTo) {
          const agentNodeId = `agent-${goal.assignedTo}`;
          if (nodeMap[agentNodeId]) {
            links.push({
              source: agentNodeId,
              target: nodeId,
              value: 3,
              type: 'assigned',
              label: 'assigned to',
              color: theme.palette.primary.light
            });
          }
        }

        // Add links between goals and their dependencies
        if (goal.dependencies && goal.dependencies.length > 0) {
          goal.dependencies.forEach(depId => {
            const depNodeId = `goal-${depId}`;
            if (nodeMap[depNodeId]) {
              links.push({
                source: depNodeId,
                target: nodeId,
                value: 2,
                type: 'depends',
                label: 'depends on',
                color: theme.palette.warning.main
              });
            }
          });
        }
      });
    }

    // Add artifact nodes
    if (state.artifacts) {
      Object.values(state.artifacts).forEach(artifact => {
        const nodeId = `artifact-${artifact.id}`;
        nodes.push({
          id: nodeId,
          name: artifact.title.length > 30 ? artifact.title.substring(0, 30) + '...' : artifact.title,
          val: 12,
          color: theme.palette.secondary.main,
          type: 'artifact'
        });
        nodeMap[nodeId] = true;

        // Add links between artifacts and their creators
        const creatorNodeId = `agent-${artifact.createdBy}`;
        if (nodeMap[creatorNodeId]) {
          links.push({
            source: creatorNodeId,
            target: nodeId,
            value: 2,
            type: 'created',
            label: 'created',
            color: theme.palette.secondary.light
          });
        }

        // Add links between artifacts and their associated goals
        if (artifact.goalId) {
          const goalNodeId = `goal-${artifact.goalId}`;
          if (nodeMap[goalNodeId]) {
            links.push({
              source: goalNodeId,
              target: nodeId,
              value: 3,
              type: 'produced',
              label: 'produced',
              color: theme.palette.success.light
            });
          }
        }
      });
    }

    // Add communication links between agents
    const communicationLinks: Record<string, { count: number, messages: Message[] }> = {};

    if (state.messages && state.messages.byId) {
      Object.values(state.messages.byId).forEach(message => {
        if (message.from !== 'system' && message.from !== 'user' && message.type === MessageType.AGENT) {
          const recipients = typeof message.to === 'string' ? [message.to] : message.to;

          recipients.forEach(recipient => {
            if (recipient !== 'all' && recipient !== 'system' && recipient !== 'user') {
              const sourceNodeId = `agent-${message.from}`;
              const targetNodeId = `agent-${recipient}`;

              if (nodeMap[sourceNodeId] && nodeMap[targetNodeId]) {
                const linkKey = `${sourceNodeId}-${targetNodeId}`;

                if (!communicationLinks[linkKey]) {
                  communicationLinks[linkKey] = { count: 0, messages: [] };
                }

                communicationLinks[linkKey].count += 1;
                communicationLinks[linkKey].messages.push(message);
              }
            }
          });
        }
      });
    }

    // Add communication links to the graph
    Object.entries(communicationLinks).forEach(([linkKey, data]) => {
      const [source, target] = linkKey.split('-');

      links.push({
        source,
        target,
        value: Math.min(5, 1 + data.count / 2),
        type: 'communication',
        label: `${data.count} messages`,
        color: theme.palette.info.light
      });
    });

    setGraphData({ nodes, links });
  }, [state, theme]);

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2, height: '100%', minHeight: '500px' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Agent Collaboration Network</Typography>
        <Box>
          <Tooltip title="Zoom In">
            <IconButton onClick={zoomIn} size="small">
              <ZoomInIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Zoom Out">
            <IconButton onClick={zoomOut} size="small">
              <ZoomOutIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Reset View">
            <IconButton onClick={resetZoom} size="small">
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      <Box sx={{ display: 'flex', height: 'calc(100% - 50px)', minHeight: '450px' }}>
        <Box sx={{ flexGrow: 1, position: 'relative', border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <CircularProgress />
            </Box>
          ) : graphData.nodes.length === 0 ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <Typography variant="body1" color="text.secondary">
                No collaboration data available
              </Typography>
            </Box>
          ) : (
            <ForceGraph2D
              ref={graphRef}
              graphData={graphData}
              nodeLabel={(node: GraphNode) => `${node.name} (${node.type})`}
              nodeColor={(node: GraphNode) => node.color}
              nodeVal={(node: GraphNode) => node.val}
              linkWidth={(link: GraphLink) => Math.sqrt(link.value) * 0.5}
              linkColor={(link: GraphLink) => link.color || theme.palette.grey[400]}
              linkDirectionalParticles={4}
              linkDirectionalParticleWidth={(link: GraphLink) => Math.sqrt(link.value) * 0.5}
              onNodeClick={handleNodeClick}
              onLinkClick={handleLinkClick}
              onNodeHover={setHoveredNode}
              cooldownTicks={100}
              linkDirectionalParticleSpeed={0.01}
              nodeCanvasObject={(node, ctx, globalScale) => {
                const label = node.name;
                const fontSize = 12/globalScale;
                ctx.font = `${fontSize}px Sans-Serif`;
                const textWidth = ctx.measureText(label).width;
                const bckgDimensions = [textWidth, fontSize].map(n => n + fontSize * 0.2);

                ctx.fillStyle = node.color;
                ctx.beginPath();
                ctx.arc(node.x || 0, node.y || 0, node.val, 0, 2 * Math.PI);
                ctx.fill();

                if (globalScale >= 0.8) {
                  ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                  ctx.fillRect(
                    (node.x || 0) - bckgDimensions[0] / 2,
                    (node.y || 0) + node.val + fontSize,
                    bckgDimensions[0],
                    bckgDimensions[1]
                  );

                  ctx.textAlign = 'center';
                  ctx.textBaseline = 'middle';
                  ctx.fillStyle = '#000';
                  ctx.fillText(
                    label,
                    (node.x || 0),
                    (node.y || 0) + node.val + fontSize + bckgDimensions[1] / 2
                  );
                }
              }}
              linkCanvasObjectMode={() => 'after'}
              linkCanvasObject={(link, ctx, globalScale) => {
                if (globalScale >= 1 && link.label) {
                  const start = link.source as GraphNode;
                  const end = link.target as GraphNode;

                  if (!start || !end || typeof start.x !== 'number' || typeof end.x !== 'number') {
                    return;
                  }

                  const textPos = {
                    x: start.x + (end.x - start.x) * 0.5,
                    y: start.y + (end.y - start.y) * 0.5
                  };

                  const fontSize = 10/globalScale;
                  ctx.font = `${fontSize}px Sans-Serif`;
                  const textWidth = ctx.measureText(link.label).width;
                  const bckgDimensions = [textWidth, fontSize].map(n => n + fontSize * 0.2);

                  ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                  ctx.fillRect(
                    textPos.x - bckgDimensions[0] / 2,
                    textPos.y - bckgDimensions[1] / 2,
                    bckgDimensions[0],
                    bckgDimensions[1]
                  );

                  ctx.textAlign = 'center';
                  ctx.textBaseline = 'middle';
                  ctx.fillStyle = '#666';
                  ctx.fillText(link.label, textPos.x, textPos.y);
                }
              }}
            />
          )}
        </Box>

        {/* Selection info panel */}
        <Box sx={{ width: 250, ml: 2, display: { xs: 'none', md: 'block' } }}>
          {selectedNode && (
            <Card variant="outlined">
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  {selectedNode.name}
                </Typography>
                <Chip
                  label={selectedNode.type}
                  size="small"
                  sx={{ mb: 1 }}
                  color={
                    selectedNode.type === 'agent' ? 'primary' :
                    selectedNode.type === 'goal' ? 'info' : 'secondary'
                  }
                />
                <Divider sx={{ my: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  {selectedNode.type === 'agent' ? 'Agent ID: ' :
                   selectedNode.type === 'goal' ? 'Goal ID: ' : 'Artifact ID: '}
                  {selectedNode.id.split('-')[1]}
                </Typography>
              </CardContent>
            </Card>
          )}

          {selectedLink && (
            <Card variant="outlined">
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  {selectedLink.type.charAt(0).toUpperCase() + selectedLink.type.slice(1)} Link
                </Typography>
                <Divider sx={{ my: 1 }} />
                <Typography variant="body2">
                  From: {(selectedLink.source as GraphNode).name}
                </Typography>
                <Typography variant="body2">
                  To: {(selectedLink.target as GraphNode).name}
                </Typography>
                {selectedLink.label && (
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {selectedLink.label}
                  </Typography>
                )}
              </CardContent>
            </Card>
          )}

          {!selectedNode && !selectedLink && (
            <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Click on a node or link to see details.
              </Typography>
              <Divider sx={{ my: 1 }} />
              <Typography variant="caption" display="block">
                <Box component="span" sx={{ display: 'inline-block', width: 12, height: 12, borderRadius: '50%', bgcolor: 'primary.main', mr: 1 }} />
                Agents
              </Typography>
              <Typography variant="caption" display="block">
                <Box component="span" sx={{ display: 'inline-block', width: 12, height: 12, borderRadius: '50%', bgcolor: 'secondary.main', mr: 1 }} />
                Artifacts
              </Typography>
              <Typography variant="caption" display="block">
                <Box component="span" sx={{ display: 'inline-block', width: 12, height: 12, borderRadius: '50%', bgcolor: 'info.main', mr: 1 }} />
                Goals (In Progress)
              </Typography>
              <Typography variant="caption" display="block">
                <Box component="span" sx={{ display: 'inline-block', width: 12, height: 12, borderRadius: '50%', bgcolor: 'success.main', mr: 1 }} />
                Goals (Completed)
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
    </Paper>
  );
};

export default AgentCollaborationGraphV3;
