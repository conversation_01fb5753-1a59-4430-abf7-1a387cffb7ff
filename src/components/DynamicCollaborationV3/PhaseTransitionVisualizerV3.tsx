'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Button,
  CircularProgress,
  Chip,
  Grid,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Collapse,
  IconButton,
  useTheme
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import DescriptionIcon from '@mui/icons-material/Description';
import AssignmentIcon from '@mui/icons-material/Assignment';
import SearchIcon from '@mui/icons-material/Search';
import CreateIcon from '@mui/icons-material/Create';
import SpellcheckIcon from '@mui/icons-material/Spellcheck';
import PublishIcon from '@mui/icons-material/Publish';

// Import types from our V3 implementation
import { 
  CollaborationState, 
  WorkflowPhase,
  GoalType,
  GoalStatus,
  MessageType
} from '../../app/(payload)/api/agents/dynamic-collaboration-v3';

interface PhaseTransitionVisualizerV3Props {
  sessionId: string;
  state: CollaborationState | null;
  loading?: boolean;
}

const PhaseTransitionVisualizerV3: React.FC<PhaseTransitionVisualizerV3Props> = ({
  sessionId,
  state,
  loading = false
}) => {
  const theme = useTheme();
  const [expandedPhases, setExpandedPhases] = useState<Record<string, boolean>>({});
  const [phaseTransitions, setPhaseTransitions] = useState<any[]>([]);

  // Toggle phase expansion
  const togglePhaseExpansion = (phase: string) => {
    setExpandedPhases({
      ...expandedPhases,
      [phase]: !expandedPhases[phase]
    });
  };

  // Get phase icon
  const getPhaseIcon = (phase: WorkflowPhase) => {
    switch (phase) {
      case WorkflowPhase.PLANNING:
        return <AssignmentIcon />;
      case WorkflowPhase.RESEARCH:
        return <SearchIcon />;
      case WorkflowPhase.CREATION:
        return <CreateIcon />;
      case WorkflowPhase.REVIEW:
        return <SpellcheckIcon />;
      case WorkflowPhase.FINALIZATION:
        return <PublishIcon />;
      default:
        return <AssignmentIcon />;
    }
  };

  // Get phase description
  const getPhaseDescription = (phase: WorkflowPhase): string => {
    switch (phase) {
      case WorkflowPhase.PLANNING:
        return 'Define goals and plan the workflow';
      case WorkflowPhase.RESEARCH:
        return 'Conduct market research and keyword analysis';
      case WorkflowPhase.CREATION:
        return 'Develop content strategy and create content';
      case WorkflowPhase.REVIEW:
        return 'Optimize content for SEO and assess quality';
      case WorkflowPhase.FINALIZATION:
        return 'Finalize and publish the content';
      default:
        return '';
    }
  };

  // Get phase goals
  const getPhaseGoals = (phase: WorkflowPhase): GoalType[] => {
    switch (phase) {
      case WorkflowPhase.RESEARCH:
        return [GoalType.MARKET_RESEARCH, GoalType.KEYWORD_ANALYSIS];
      case WorkflowPhase.CREATION:
        return [GoalType.CONTENT_STRATEGY, GoalType.CONTENT_CREATION];
      case WorkflowPhase.REVIEW:
        return [GoalType.SEO_OPTIMIZATION, GoalType.QUALITY_ASSESSMENT];
      default:
        return [];
    }
  };

  // Get goal type display name
  const getGoalTypeDisplay = (type: GoalType): string => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Extract phase transitions from messages
  useEffect(() => {
    if (!state) {
      setPhaseTransitions([]);
      return;
    }

    const transitions: any[] = [];
    
    // Find phase transition messages
    Object.values(state.messages).forEach(message => {
      if (message.type === MessageType.WORKFLOW_TRANSITION) {
        transitions.push({
          timestamp: message.timestamp,
          from: message.content.oldPhase,
          to: message.content.newPhase,
          reasoning: message.reasoning
        });
      }
    });
    
    // Sort by timestamp
    transitions.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    
    setPhaseTransitions(transitions);
  }, [state]);

  // Get active step based on current phase
  const getActiveStep = (): number => {
    if (!state) return 0;
    
    switch (state.currentPhase) {
      case WorkflowPhase.PLANNING:
        return 0;
      case WorkflowPhase.RESEARCH:
        return 1;
      case WorkflowPhase.CREATION:
        return 2;
      case WorkflowPhase.REVIEW:
        return 3;
      case WorkflowPhase.FINALIZATION:
        return 4;
      default:
        return 0;
    }
  };

  // Check if a phase is completed
  const isPhaseCompleted = (phase: WorkflowPhase): boolean => {
    if (!state) return false;
    
    const phaseIndex = [
      WorkflowPhase.PLANNING,
      WorkflowPhase.RESEARCH,
      WorkflowPhase.CREATION,
      WorkflowPhase.REVIEW,
      WorkflowPhase.FINALIZATION
    ].indexOf(phase);
    
    const currentPhaseIndex = [
      WorkflowPhase.PLANNING,
      WorkflowPhase.RESEARCH,
      WorkflowPhase.CREATION,
      WorkflowPhase.REVIEW,
      WorkflowPhase.FINALIZATION
    ].indexOf(state.currentPhase);
    
    return phaseIndex < currentPhaseIndex;
  };

  // Check if phase goals are completed
  const arePhaseGoalsCompleted = (phase: WorkflowPhase): boolean => {
    if (!state) return false;
    
    const phaseGoalTypes = getPhaseGoals(phase);
    if (phaseGoalTypes.length === 0) return true;
    
    // Find goals of these types
    const phaseGoals = Object.values(state.goals).filter(goal => 
      phaseGoalTypes.includes(goal.type as GoalType)
    );
    
    if (phaseGoals.length === 0) return false;
    
    // Check if all goals are completed
    return phaseGoals.every(goal => goal.status === GoalStatus.COMPLETED);
  };

  // Get phase artifacts
  const getPhaseArtifacts = (phase: WorkflowPhase): any[] => {
    if (!state) return [];
    
    const phaseGoalTypes = getPhaseGoals(phase);
    if (phaseGoalTypes.length === 0) return [];
    
    // Find goals of these types
    const phaseGoals = Object.values(state.goals).filter(goal => 
      phaseGoalTypes.includes(goal.type as GoalType)
    );
    
    // Get artifact IDs from these goals
    const artifactIds: string[] = [];
    phaseGoals.forEach(goal => {
      if (goal.artifactIds) {
        artifactIds.push(...goal.artifactIds);
      }
    });
    
    // Get artifacts
    return artifactIds.map(id => state.artifacts[id]).filter(Boolean);
  };

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2 }}>
      <Typography variant="h6" gutterBottom>
        Workflow Progress
      </Typography>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : !state ? (
        <Box sx={{ textAlign: 'center', p: 4 }}>
          <Typography variant="body1" color="text.secondary">
            No workflow data available
          </Typography>
        </Box>
      ) : (
        <Stepper activeStep={getActiveStep()} orientation="horizontal">
          {[
            WorkflowPhase.PLANNING,
            WorkflowPhase.RESEARCH,
            WorkflowPhase.CREATION,
            WorkflowPhase.REVIEW,
            WorkflowPhase.FINALIZATION
          ].map((phase, index) => (
            <Step key={phase} completed={isPhaseCompleted(phase)}>
              <StepLabel
                StepIconProps={{
                  icon: getPhaseIcon(phase)
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="body2">{phase}</Typography>
                  <IconButton 
                    size="small" 
                    onClick={() => togglePhaseExpansion(phase)}
                    sx={{ ml: 0.5 }}
                  >
                    {expandedPhases[phase] ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
                  </IconButton>
                </Box>
              </StepLabel>
              <Collapse in={expandedPhases[phase]} timeout="auto" unmountOnExit>
                <Box sx={{ mt: 2, mb: 3, mx: 1 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {getPhaseDescription(phase)}
                  </Typography>
                  
                  {getPhaseGoals(phase).length > 0 && (
                    <>
                      <Typography variant="subtitle2" gutterBottom sx={{ mt: 1 }}>
                        Phase Goals
                      </Typography>
                      <List dense disablePadding>
                        {getPhaseGoals(phase).map(goalType => {
                          // Find a goal of this type
                          const goal = Object.values(state.goals).find(g => g.type === goalType);
                          
                          return (
                            <ListItem key={goalType} sx={{ py: 0.5 }}>
                              <ListItemIcon sx={{ minWidth: 30 }}>
                                {goal && goal.status === GoalStatus.COMPLETED ? (
                                  <CheckCircleIcon fontSize="small" color="success" />
                                ) : (
                                  <RadioButtonUncheckedIcon fontSize="small" />
                                )}
                              </ListItemIcon>
                              <ListItemText 
                                primary={getGoalTypeDisplay(goalType)}
                                secondary={goal ? `Status: ${goal.status}` : 'Not started'}
                              />
                            </ListItem>
                          );
                        })}
                      </List>
                    </>
                  )}
                  
                  {getPhaseArtifacts(phase).length > 0 && (
                    <>
                      <Typography variant="subtitle2" gutterBottom sx={{ mt: 1 }}>
                        Phase Artifacts
                      </Typography>
                      <List dense disablePadding>
                        {getPhaseArtifacts(phase).map(artifact => (
                          <ListItem key={artifact.id} sx={{ py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 30 }}>
                              <DescriptionIcon fontSize="small" color="primary" />
                            </ListItemIcon>
                            <ListItemText 
                              primary={artifact.title}
                              secondary={`Type: ${artifact.type}`}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </>
                  )}
                </Box>
              </Collapse>
            </Step>
          ))}
        </Stepper>
      )}

      {/* Phase Transitions */}
      {phaseTransitions.length > 0 && (
        <Box sx={{ mt: 4 }}>
          <Typography variant="subtitle1" gutterBottom>
            Phase Transitions
          </Typography>
          <Grid container spacing={2}>
            {phaseTransitions.map((transition, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle2" gutterBottom>
                      {transition.from} → {transition.to}
                    </Typography>
                    <Typography variant="caption" color="text.secondary" display="block">
                      {new Date(transition.timestamp).toLocaleString()}
                    </Typography>
                    <Divider sx={{ my: 1 }} />
                    {transition.reasoning && (
                      <Box>
                        <Typography variant="body2">
                          <strong>Reasoning:</strong> {transition.reasoning.decision}
                        </Typography>
                        {transition.reasoning.thoughts && transition.reasoning.thoughts.length > 0 && (
                          <Typography variant="body2" sx={{ mt: 1 }}>
                            <strong>Thoughts:</strong> {transition.reasoning.thoughts[0]}
                          </Typography>
                        )}
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
    </Paper>
  );
};

export default PhaseTransitionVisualizerV3;
