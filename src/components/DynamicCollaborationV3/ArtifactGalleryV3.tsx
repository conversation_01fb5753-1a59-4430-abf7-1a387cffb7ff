'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  TextField,
  InputAdornment,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  CircularProgress,
  Divider,
  Tooltip,
  useTheme,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import VisibilityIcon from '@mui/icons-material/Visibility';
import TuneIcon from '@mui/icons-material/Tune';
import FeedbackIcon from '@mui/icons-material/Feedback';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ArticleIcon from '@mui/icons-material/Article';
import AnalyticsIcon from '@mui/icons-material/Analytics';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import SpellcheckIcon from '@mui/icons-material/Spellcheck';
import BiotechIcon from '@mui/icons-material/Biotech';
import dynamic from 'next/dynamic';
import remarkGfm from 'remark-gfm';

// Dynamically import ReactMarkdown to avoid SSR issues
const ReactMarkdown = dynamic(() => import('react-markdown'), { ssr: false });

// Import types from our V3 implementation
import {
  CollaborationState,
  Artifact,
  ArtifactStatus,
  WorkflowPhase
} from '../../app/(payload)/api/agents/dynamic-collaboration-v3';

interface ArtifactGalleryV3Props {
  sessionId: string;
  state: CollaborationState | null;
  loading?: boolean;
  onRefresh?: () => void;
}

const ArtifactGalleryV3: React.FC<ArtifactGalleryV3Props> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const theme = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterPhase, setFilterPhase] = useState('all');
  const [selectedArtifact, setSelectedArtifact] = useState<Artifact | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [artifacts, setArtifacts] = useState<Artifact[]>([]);
  const [activeTab, setActiveTab] = useState(0);

  // Format timestamp
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  // Get artifact icon based on type
  const getArtifactIcon = (type: string) => {
    switch (type) {
      case 'market-research':
        return <BiotechIcon />;
      case 'keyword-analysis':
        return <AnalyticsIcon />;
      case 'content-strategy':
        return <LightbulbIcon />;
      case 'content-creation':
        return <ArticleIcon />;
      case 'seo-optimization':
        return <SpellcheckIcon />;
      case 'quality-assessment':
        return <CheckCircleIcon />;
      default:
        return <ArticleIcon />;
    }
  };

  // Handle artifact click
  const handleArtifactClick = (artifact: Artifact) => {
    setSelectedArtifact(artifact);
    setDialogOpen(true);
  };

  // Close dialog
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Filter artifacts
  useEffect(() => {
    if (!state) {
      setArtifacts([]);
      return;
    }

    // Get all artifacts as an array
    const artifactArray = state.artifacts ? Object.values(state.artifacts) : [];

    // Apply filters
    let filteredArtifacts = artifactArray;

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filteredArtifacts = filteredArtifacts.filter(artifact =>
        artifact.title.toLowerCase().includes(searchLower) ||
        artifact.type.toLowerCase().includes(searchLower)
      );
    }

    // Apply type filter
    if (filterType !== 'all') {
      filteredArtifacts = filteredArtifacts.filter(artifact =>
        artifact.type === filterType
      );
    }

    // Apply phase filter (this would require mapping artifacts to phases)
    if (filterPhase !== 'all') {
      // This is a simplified approach - in a real implementation, you'd need to map artifacts to phases
      const phaseArtifactTypes: Record<string, string[]> = {
        [WorkflowPhase.RESEARCH]: ['market-research', 'keyword-analysis'],
        [WorkflowPhase.CREATION]: ['content-strategy', 'content-creation'],
        [WorkflowPhase.REVIEW]: ['seo-optimization', 'quality-assessment'],
        [WorkflowPhase.FINALIZATION]: ['final-article']
      };

      const typesForPhase = phaseArtifactTypes[filterPhase as WorkflowPhase] || [];
      filteredArtifacts = filteredArtifacts.filter(artifact =>
        typesForPhase.includes(artifact.type)
      );
    }

    // Sort by creation date (newest first)
    filteredArtifacts.sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    setArtifacts(filteredArtifacts);
  }, [state, searchTerm, filterType, filterPhase]);

  // Get artifact types for filter
  const artifactTypes = state && state.artifacts ?
    Array.from(new Set(Object.values(state.artifacts).map(a => a.type))) :
    [];

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Artifacts</Typography>
        <Box>
          {onRefresh && (
            <Tooltip title="Refresh">
              <IconButton onClick={onRefresh} disabled={loading}>
                <SearchIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>

      {/* Search and filters */}
      <Box sx={{ mb: 2 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search artifacts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                )
              }}
              size="small"
            />
          </Grid>
          <Grid item xs={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Type</InputLabel>
              <Select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                label="Type"
              >
                <MenuItem value="all">All Types</MenuItem>
                {artifactTypes.map(type => (
                  <MenuItem key={type} value={type}>
                    {type.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Phase</InputLabel>
              <Select
                value={filterPhase}
                onChange={(e) => setFilterPhase(e.target.value)}
                label="Phase"
              >
                <MenuItem value="all">All Phases</MenuItem>
                <MenuItem value={WorkflowPhase.PLANNING}>Planning</MenuItem>
                <MenuItem value={WorkflowPhase.RESEARCH}>Research</MenuItem>
                <MenuItem value={WorkflowPhase.CREATION}>Creation</MenuItem>
                <MenuItem value={WorkflowPhase.REVIEW}>Review</MenuItem>
                <MenuItem value={WorkflowPhase.FINALIZATION}>Finalization</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      {/* Artifacts grid */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : artifacts.length === 0 ? (
        <Box sx={{ textAlign: 'center', p: 4 }}>
          <Typography variant="body1" color="text.secondary">
            No artifacts found
          </Typography>
        </Box>
      ) : (
        <Grid container spacing={2}>
          {artifacts.map((artifact) => (
            <Grid item xs={12} sm={6} md={4} key={artifact.id}>
              <Card
                variant="outlined"
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'all 0.2s',
                  '&:hover': {
                    boxShadow: 3
                  }
                }}
              >
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Box sx={{ mr: 1, color: 'primary.main' }}>
                      {getArtifactIcon(artifact.type)}
                    </Box>
                    <Typography variant="h6" component="div" noWrap>
                      {artifact.title}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Chip
                      label={artifact.type.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                    <Typography variant="caption" color="text.secondary">
                      {formatTime(artifact.createdAt)}
                    </Typography>
                  </Box>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Created by: {artifact.createdBy}
                  </Typography>

                  <Chip
                    label={artifact.status}
                    size="small"
                    color={
                      artifact.status === ArtifactStatus.APPROVED ? 'success' :
                      artifact.status === ArtifactStatus.REVIEW ? 'warning' :
                      artifact.status === ArtifactStatus.REJECTED ? 'error' :
                      'default'
                    }
                    sx={{ mb: 1 }}
                  />
                </CardContent>

                <CardActions>
                  <Button
                    startIcon={<VisibilityIcon />}
                    onClick={() => handleArtifactClick(artifact)}
                    size="small"
                  >
                    View Details
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Artifact detail dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        {selectedArtifact && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ mr: 1, color: 'primary.main' }}>
                  {getArtifactIcon(selectedArtifact.type)}
                </Box>
                {selectedArtifact.title}
              </Box>
            </DialogTitle>
            <DialogContent dividers>
              <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
                <Tab label="Content" />
                <Tab label="Metadata" />
              </Tabs>

              {activeTab === 0 && (
                <Paper
                  variant="outlined"
                  sx={{
                    p: 2,
                    maxHeight: 400,
                    overflow: 'auto',
                    bgcolor: 'grey.50'
                  }}
                >
                  {typeof selectedArtifact.content === 'string' ? (
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {selectedArtifact.content}
                    </ReactMarkdown>
                  ) : (
                    <pre style={{ whiteSpace: 'pre-wrap' }}>
                      {JSON.stringify(selectedArtifact.content, null, 2)}
                    </pre>
                  )}
                </Paper>
              )}

              {activeTab === 1 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>Artifact Details</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        <strong>Type:</strong> {selectedArtifact.type}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        <strong>Status:</strong> {selectedArtifact.status}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        <strong>Created At:</strong> {formatTime(selectedArtifact.createdAt)}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        <strong>Updated At:</strong> {formatTime(selectedArtifact.updatedAt)}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        <strong>Created By:</strong> {selectedArtifact.createdBy}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        <strong>Version:</strong> {selectedArtifact.version}
                      </Typography>
                    </Grid>
                    {selectedArtifact.goalId && (
                      <Grid item xs={12}>
                        <Typography variant="body2">
                          <strong>Associated Goal:</strong> {selectedArtifact.goalId}
                        </Typography>
                      </Grid>
                    )}
                  </Grid>

                  {selectedArtifact.metadata && (
                    <>
                      <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>Metadata</Typography>
                      <Paper variant="outlined" sx={{ p: 2, bgcolor: 'grey.50' }}>
                        <pre style={{ whiteSpace: 'pre-wrap' }}>
                          {JSON.stringify(selectedArtifact.metadata, null, 2)}
                        </pre>
                      </Paper>
                    </>
                  )}
                </Box>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDialog}>Close</Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Paper>
  );
};

export default ArtifactGalleryV3;
