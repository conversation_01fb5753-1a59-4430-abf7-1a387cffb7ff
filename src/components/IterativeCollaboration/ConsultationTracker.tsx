// src/components/IterativeCollaboration/ConsultationTracker.tsx

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Badge
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import QuestionAnswerIcon from '@mui/icons-material/QuestionAnswer';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PendingIcon from '@mui/icons-material/Pending';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import PriorityHighIcon from '@mui/icons-material/PriorityHigh';
import LowPriorityIcon from '@mui/icons-material/LowPriority';
import FlagIcon from '@mui/icons-material/Flag';

// Import types from our iterative collaboration system
import { Consultation, IterativeArtifact } from '../../app/(payload)/api/agents/collaborative-iteration/types';

interface ConsultationTrackerProps {
  consultations: Consultation[];
  artifacts: Record<string, IterativeArtifact>;
}

const ConsultationTracker: React.FC<ConsultationTrackerProps> = ({ consultations, artifacts }) => {
  const [selectedConsultation, setSelectedConsultation] = useState<string | null>(null);

  // Format timestamp to a readable format
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  // Get agent color
  const getAgentColor = (agentId: string) => {
    switch (agentId) {
      case 'market-research':
        return '#4caf50'; // green
      case 'seo-keyword':
        return '#2196f3'; // blue
      case 'content-strategy':
        return '#9c27b0'; // purple
      case 'content-generation':
        return '#ff9800'; // orange
      case 'seo-optimization':
        return '#f44336'; // red
      case 'system':
        return '#607d8b'; // blue-grey
      default:
        return '#9e9e9e'; // grey
    }
  };

  // Get agent name
  const getAgentName = (agentId: string) => {
    switch (agentId) {
      case 'market-research':
        return 'Market Research Agent';
      case 'seo-keyword':
        return 'SEO Keyword Agent';
      case 'content-strategy':
        return 'Content Strategy Agent';
      case 'content-generation':
        return 'Content Generation Agent';
      case 'seo-optimization':
        return 'SEO Optimization Agent';
      case 'system':
        return 'System';
      default:
        return agentId;
    }
  };

  // Get priority icon
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return <PriorityHighIcon color="error" />;
      case 'medium':
        return <FlagIcon color="warning" />;
      case 'low':
        return <LowPriorityIcon color="info" />;
      default:
        return null;
    }
  };

  // Render consultation list
  const renderConsultationList = () => (
    <Box sx={{ mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        Consultations ({consultations.length})
      </Typography>
      
      {consultations.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1">
            No consultations have occurred yet. Agents will consult each other as they collaborate.
          </Typography>
        </Paper>
      ) : (
        <List sx={{ width: '100%', bgcolor: 'background.paper' }}>
          {consultations.map((consultation) => {
            const artifact = artifacts[consultation.artifactId];
            
            return (
              <Card 
                key={consultation.id} 
                variant="outlined" 
                sx={{ 
                  mb: 2,
                  cursor: 'pointer',
                  border: selectedConsultation === consultation.id ? 2 : 1,
                  borderColor: selectedConsultation === consultation.id ? 'primary.main' : 'divider'
                }}
                onClick={() => setSelectedConsultation(consultation.id)}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Avatar 
                      sx={{ 
                        bgcolor: getAgentColor(consultation.fromAgent),
                        width: 32,
                        height: 32,
                        fontSize: '0.875rem',
                        mr: 1
                      }}
                    >
                      {consultation.fromAgent.charAt(0).toUpperCase()}
                    </Avatar>
                    <ArrowForwardIcon sx={{ mx: 1 }} />
                    <Avatar 
                      sx={{ 
                        bgcolor: getAgentColor(consultation.toAgent),
                        width: 32,
                        height: 32,
                        fontSize: '0.875rem',
                        mr: 1
                      }}
                    >
                      {consultation.toAgent.charAt(0).toUpperCase()}
                    </Avatar>
                    <Box sx={{ ml: 1, flexGrow: 1 }}>
                      <Typography variant="body2" component="div">
                        {getAgentName(consultation.fromAgent)} → {getAgentName(consultation.toAgent)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {formatTimestamp(consultation.timestamp)}
                      </Typography>
                    </Box>
                    <Badge 
                      color={consultation.incorporated ? "success" : "warning"} 
                      badgeContent={consultation.incorporated ? "Incorporated" : "Pending"}
                      sx={{ ml: 1 }}
                    />
                  </Box>
                  
                  <Divider sx={{ my: 1 }} />
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                      Artifact:
                    </Typography>
                    <Chip 
                      label={artifact ? artifact.name : `Unknown (${consultation.artifactId})`}
                      size="small"
                      variant="outlined"
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ mx: 1 }}>
                      Version:
                    </Typography>
                    <Chip 
                      label={consultation.version}
                      size="small"
                      variant="outlined"
                    />
                  </Box>
                  
                  <Typography variant="body2" sx={{ mt: 1, fontWeight: 'medium' }}>
                    Feedback:
                  </Typography>
                  <Typography variant="body2" sx={{ mt: 0.5 }}>
                    {consultation.feedback.length > 100 
                      ? `${consultation.feedback.substring(0, 100)}...` 
                      : consultation.feedback}
                  </Typography>
                  
                  {consultation.suggestions.length > 0 && (
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                        Suggestions: {consultation.suggestions.length}
                      </Typography>
                    </Box>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </List>
      )}
    </Box>
  );

  // Render consultation details
  const renderConsultationDetails = () => {
    if (!selectedConsultation) return null;
    
    const consultation = consultations.find(c => c.id === selectedConsultation);
    if (!consultation) return null;
    
    const artifact = artifacts[consultation.artifactId];
    
    return (
      <Box sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Consultation Details
        </Typography>
        
        <Card variant="outlined">
          <CardContent>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  From Agent
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar 
                    sx={{ 
                      bgcolor: getAgentColor(consultation.fromAgent),
                      width: 24,
                      height: 24,
                      fontSize: '0.75rem',
                      mr: 1
                    }}
                  >
                    {consultation.fromAgent.charAt(0).toUpperCase()}
                  </Avatar>
                  <Typography variant="body1">
                    {getAgentName(consultation.fromAgent)}
                  </Typography>
                </Box>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  To Agent
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar 
                    sx={{ 
                      bgcolor: getAgentColor(consultation.toAgent),
                      width: 24,
                      height: 24,
                      fontSize: '0.75rem',
                      mr: 1
                    }}
                  >
                    {consultation.toAgent.charAt(0).toUpperCase()}
                  </Avatar>
                  <Typography variant="body1">
                    {getAgentName(consultation.toAgent)}
                  </Typography>
                </Box>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Timestamp
                </Typography>
                <Typography variant="body1">
                  {formatTimestamp(consultation.timestamp)}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Status
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {consultation.incorporated ? (
                    <CheckCircleIcon color="success" sx={{ mr: 1 }} />
                  ) : (
                    <PendingIcon color="warning" sx={{ mr: 1 }} />
                  )}
                  <Typography variant="body1">
                    {consultation.incorporated ? 'Incorporated' : 'Pending'}
                  </Typography>
                </Box>
              </Grid>
              
              <Grid item xs={12}>
                <Divider sx={{ my: 1 }} />
              </Grid>
              
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="text.secondary">
                  Artifact
                </Typography>
                <Typography variant="body1">
                  {artifact ? artifact.name : `Unknown (${consultation.artifactId})`}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Type: {artifact ? artifact.type : 'Unknown'}, Version: {consultation.version}
                </Typography>
              </Grid>
              
              <Grid item xs={12}>
                <Divider sx={{ my: 1 }} />
              </Grid>
              
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="text.secondary">
                  Feedback
                </Typography>
                <Typography variant="body1" sx={{ mt: 1, whiteSpace: 'pre-wrap' }}>
                  {consultation.feedback}
                </Typography>
              </Grid>
              
              {consultation.suggestions.length > 0 && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 2 }}>
                    Suggestions
                  </Typography>
                  <List>
                    {consultation.suggestions.map((suggestion, index) => (
                      <ListItem key={index} alignItems="flex-start" sx={{ px: 0 }}>
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          {getPriorityIcon(suggestion.priority)}
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Typography variant="body2" component="span" fontWeight="medium">
                                {suggestion.area}
                              </Typography>
                              <Chip 
                                label={suggestion.priority} 
                                size="small" 
                                color={
                                  suggestion.priority === 'high' ? 'error' :
                                  suggestion.priority === 'medium' ? 'warning' : 'info'
                                }
                                sx={{ ml: 1 }}
                              />
                            </Box>
                          }
                          secondary={suggestion.suggestion}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>
      </Box>
    );
  };

  return (
    <Box>
      {renderConsultationList()}
      {renderConsultationDetails()}
    </Box>
  );
};

export default ConsultationTracker;
