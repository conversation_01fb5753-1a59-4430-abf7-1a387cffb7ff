'use client'
import React, { useState } from 'react'
import { useField } from '@payloadcms/ui'
import { Button } from '@payloadcms/ui'
import type { TextFieldClientComponent } from 'payload'

export const AIHelperField: TextFieldClientComponent = (props) => {
  const { path, field } = props
  const { value, setValue } = useField({ path })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleGenerateContent = async () => {
    setIsLoading(true)
    setError(null)
    try {
      // Replace this with your actual AI generation logic
      // This could be a call to an AI service like OpenAI, Anthropic, etc.
      const response = await fetch('/api/generate-ai-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          context: field.context || '', // Optional context for generation
          fieldName: field.name
        }),
      })
      
      if (!response.ok) {
        throw new Error('Failed to generate content')
      }
      
      const data = await response.json()
      console.log("Data", data);
      setValue(data.generatedContent)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err)
      setError(errorMessage)
      console.error(err)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="ai-helper-field">
      <div className="ai-helper-field__input">
        <textarea
          name={field.name}
          value={value || ''}
          onChange={(e) => setValue(e.target.value)}
          className="field-input"
        />
      </div>
      <div className="ai-helper-field__actions">
        <Button
          onClick={handleGenerateContent}
          disabled={isLoading}
          className="ai-generate-button"
        >
          {isLoading ? 'Generating...' : 'Generate with AI'}
        </Button>
        {error && (
          <div className="ai-helper-field__error">
            {error}
          </div>
        )}
      </div>
    </div>
  )
}

// Optional: Add some custom styles
import './ai-helper-field.scss'