@import '~@payloadcms/ui/scss';

.ai-helper-field {
  position: relative;
  margin-bottom: var(--theme-spacing-base);

  &__input {
    margin-bottom: var(--theme-spacing-sm);
  }

  &__actions {
    display: flex;
    align-items: center;
    gap: var(--theme-spacing-sm);
  }

  &__error {
    color: var(--theme-error-500);
    font-size: var(--theme-font-size-xs);
    margin-left: var(--theme-spacing-sm);
  }

  .ai-generate-button {
    background-color: var(--theme-success-500);
    color: var(--theme-text-color-inverse);
    
    &:hover {
      background-color: var(--theme-success-600);
    }

    &:disabled {
      background-color: var(--theme-gray-300);
      cursor: not-allowed;
    }
  }

  .field-input {
    width: 100%;
    min-height: 150px;
    resize: vertical;
  }
}