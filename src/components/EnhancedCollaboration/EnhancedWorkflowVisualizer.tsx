'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Stepper,
  Step,
  StepL<PERSON>l,
  StepContent,
  <PERSON>ton,
  Divider,
  Chip,
  LinearProgress,
  Grid,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  CircularProgress,
  <PERSON><PERSON>,
  Badge,
  Avatar
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import PauseIcon from '@mui/icons-material/Pause';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import VisibilityIcon from '@mui/icons-material/Visibility';
import HistoryIcon from '@mui/icons-material/History';
import PsychologyIcon from '@mui/icons-material/Psychology';
import EditIcon from '@mui/icons-material/Edit';
import RateReviewIcon from '@mui/icons-material/RateReview';
import SearchIcon from '@mui/icons-material/Search';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { IterativeCollaborationState, WorkflowProgress } from '../../app/(payload)/api/agents/collaborative-iteration/types';

// Define the workflow phases for the new workflow orchestrator
const workflowPhases = [
  {
    id: 'initialization',
    label: 'Initialization',
    description: 'Setting up the collaborative workflow and preparing agents',
    icon: <PsychologyIcon />,
    color: '#3f51b5',
    estimatedTime: '1 minute'
  },
  {
    id: 'research',
    label: 'Research Phase',
    description: 'Market research, keyword analysis, and content strategy development',
    icon: <SearchIcon />,
    color: '#2196f3',
    estimatedTime: '3-5 minutes'
  },
  {
    id: 'creation',
    label: 'Content Creation',
    description: 'Generating high-quality content based on research and strategy',
    icon: <EditIcon />,
    color: '#009688',
    estimatedTime: '4-6 minutes'
  },
  {
    id: 'review',
    label: 'Review & Optimization',
    description: 'SEO optimization and final content review',
    icon: <RateReviewIcon />,
    color: '#ff9800',
    estimatedTime: '2-4 minutes'
  },
  {
    id: 'finalization',
    label: 'Finalization',
    description: 'Preparing the final optimized content for publication',
    icon: <CheckCircleIcon />,
    color: '#4caf50',
    estimatedTime: '1 minute'
  }
];

interface EnhancedWorkflowVisualizerProps {
  sessionId: string;
  state: Partial<IterativeCollaborationState>;
  onRefresh?: () => void;
  onPauseWorkflow?: () => void;
  onResumeWorkflow?: () => void;
  onViewArtifact?: (artifactId: string) => void;
  loading?: boolean;
}

interface AgentStatus {
  id: string;
  name: string;
  status: 'idle' | 'active' | 'complete' | 'error';
  lastActivity?: string;
  contributions?: number;
  icon?: React.ReactNode;
  color?: string;
}

/**
 * Enhanced component to visualize the collaborative workflow between agents
 * Provides real-time progress tracking and phase visualization
 */
const EnhancedWorkflowVisualizer: React.FC<EnhancedWorkflowVisualizerProps> = ({
  sessionId,
  state,
  onRefresh,
  onPauseWorkflow,
  onResumeWorkflow,
  onViewArtifact,
  loading = false
}) => {
  const [activePhaseIndex, setActivePhaseIndex] = useState<number>(0);
  const [phaseProgress, setPhaseProgress] = useState<number>(0);
  const [overallProgress, setOverallProgress] = useState<number>(0);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState<string>('');
  const [agentStatuses, setAgentStatuses] = useState<AgentStatus[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Determine current phase and progress based on state
  useEffect(() => {
    if (!state) return;

    try {
      // Determine current phase
      const currentPhase = state.currentPhase || state.collaborationState;

      // Map API phase names to our workflow phase IDs
      let mappedPhase = currentPhase;
      if (currentPhase === 'content-generation') {
        mappedPhase = 'creation';
      }

      console.log('Current phase from API:', currentPhase);
      console.log('Mapped phase for UI:', mappedPhase);

      // Find the phase index, defaulting to 0 if not found
      let phaseIndex = 0;
      if (mappedPhase) {
        const foundIndex = workflowPhases.findIndex(phase => phase.id === mappedPhase);
        phaseIndex = foundIndex >= 0 ? foundIndex : 0;
      }

      console.log('Phase index:', phaseIndex);
      console.log('Available phases:', workflowPhases.map(p => p.id));

      // Ensure phaseIndex is valid
      if (phaseIndex < 0 || phaseIndex >= workflowPhases.length) {
        console.warn(`Invalid phase index: ${phaseIndex}, defaulting to 0`);
        phaseIndex = 0;
      }

      setActivePhaseIndex(phaseIndex);

      // Calculate progress
      calculateProgress(state);

      // Update agent statuses
      updateAgentStatuses(state);

      // Estimate time remaining (with valid phase index)
      estimateTimeRemaining(phaseIndex);

      // Check for errors
      if (state.status === 'failed' || state.currentPhase === 'error') {
        setError('Workflow encountered an error. Please check the logs for details.');
      } else {
        setError(null);
      }
    } catch (error) {
      console.error('Error updating workflow visualizer:', error);
      setError('Error updating workflow visualization. Please refresh the page.');

      // Set safe default values
      setActivePhaseIndex(0);
      setPhaseProgress(0);
      setOverallProgress(0);
      setEstimatedTimeRemaining('Calculating...');
    }
  }, [state]);

  // Calculate progress percentages
  const calculateProgress = (state: Partial<IterativeCollaborationState>) => {
    // Calculate phase-specific progress
    let currentPhaseProgress = 0;

    if (state.workflowProgress) {
      const progress = state.workflowProgress;

      switch (state.currentPhase) {
        case 'initialization':
          // Initialization phase is quick
          currentPhaseProgress = 100;
          break;
        case 'research':
          // Calculate research phase progress based on completed research tasks
          if (progress.marketResearchComplete && progress.keywordResearchComplete && progress.contentStrategyComplete) {
            currentPhaseProgress = 100;
          } else if (progress.marketResearchComplete && progress.keywordResearchComplete) {
            currentPhaseProgress = 75;
          } else if (progress.marketResearchComplete) {
            currentPhaseProgress = 50;
          } else {
            currentPhaseProgress = 25;
          }
          break;
        case 'creation':
          // Content generation phase progress
          currentPhaseProgress = progress.contentGenerationComplete ? 100 : 50;
          break;
        case 'review':
          // Review phase progress
          currentPhaseProgress = progress.seoOptimizationComplete ? 100 : 50;
          break;
        case 'finalization':
          currentPhaseProgress = 100; // If we're in finalization, it's complete
          break;
        default:
          currentPhaseProgress = 50; // Default to 50% if no specific indicators
      }
    } else {
      // Fallback calculation based on iterations
      const maxIterations = state.maxIterations || 5;
      const currentIterations = state.iterations || 0;
      currentPhaseProgress = Math.min(100, (currentIterations / maxIterations) * 100);
    }

    setPhaseProgress(currentPhaseProgress);

    // Calculate overall progress
    const totalPhases = workflowPhases.length;
    const completedPhases = activePhaseIndex;
    const overallPercent = ((completedPhases + (currentPhaseProgress / 100)) / totalPhases) * 100;
    setOverallProgress(Math.round(overallPercent));
  };

  // Update agent statuses based on state
  const updateAgentStatuses = (state: Partial<IterativeCollaborationState>) => {
    const agentStates = state.agentStates || {};
    const messages = state.messages || [];

    // Define our agents
    const agents: AgentStatus[] = [
      {
        id: 'market-research',
        name: 'Market Research',
        status: 'idle',
        icon: <SearchIcon />,
        color: '#2196f3'
      },
      {
        id: 'seo-keyword',
        name: 'SEO Keyword',
        status: 'idle',
        icon: <SearchIcon />,
        color: '#ff9800'
      },
      {
        id: 'content-strategy',
        name: 'Content Strategy',
        status: 'idle',
        icon: <PsychologyIcon />,
        color: '#3f51b5'
      },
      {
        id: 'content-generation',
        name: 'Content Generation',
        status: 'idle',
        icon: <EditIcon />,
        color: '#009688'
      },
      {
        id: 'seo-optimization',
        name: 'SEO Optimization',
        status: 'idle',
        icon: <RateReviewIcon />,
        color: '#f44336'
      }
    ];

    // Update status based on agent states and messages
    agents.forEach(agent => {
      const agentState = agentStates[agent.id];

      if (agentState) {
        // Check if agent has generated artifacts
        if (agentState.generatedArtifacts && agentState.generatedArtifacts.length > 0) {
          agent.status = 'complete';
          agent.contributions = agentState.generatedArtifacts.length;
        }
        // Check if agent has processed requests
        else if (agentState.processedRequests && agentState.processedRequests.length > 0) {
          agent.status = 'active';
          agent.contributions = agentState.processedRequests.length;
        }

        // Set last activity time if available
        if (agentState.lastUpdated) {
          agent.lastActivity = new Date(agentState.lastUpdated).toLocaleTimeString();
        }
      }

      // Check recent messages to determine if agent is currently active
      const recentMessages = messages
        .filter(m => m.from === agent.id || m.to === agent.id)
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      if (recentMessages.length > 0) {
        const latestMessage = recentMessages[0];
        const messageTime = new Date(latestMessage.timestamp).getTime();
        const currentTime = new Date().getTime();
        const timeDiff = currentTime - messageTime;

        // If message is within last 30 seconds, agent is active
        if (timeDiff < 30000 && agent.status !== 'complete') {
          agent.status = 'active';
          agent.lastActivity = new Date(latestMessage.timestamp).toLocaleTimeString();
        }
      }
    });

    setAgentStatuses(agents);
  };

  // Estimate remaining time based on current phase and progress
  const estimateTimeRemaining = (phaseIndex: number) => {
    // Validate phaseIndex to ensure it's within bounds
    if (phaseIndex < 0 || phaseIndex >= workflowPhases.length) {
      setEstimatedTimeRemaining('Calculating...');
      return;
    }

    // If we're at the last phase and it's complete
    if (phaseIndex >= workflowPhases.length - 1 && phaseProgress >= 95) {
      setEstimatedTimeRemaining('Complete');
      return;
    }

    // Calculate remaining time based on current phase and remaining phases
    let remainingMinutes = 0;

    try {
      // Add time for current phase (proportional to remaining progress)
      // Make sure the current phase exists and has an estimatedTime property
      if (workflowPhases[phaseIndex] && workflowPhases[phaseIndex].estimatedTime) {
        const currentPhaseTime = parseTimeRange(workflowPhases[phaseIndex].estimatedTime);
        remainingMinutes += currentPhaseTime * (1 - (phaseProgress / 100));
      } else {
        // Default value if estimatedTime is missing
        remainingMinutes += 3;
      }

      // Add time for remaining phases
      for (let i = phaseIndex + 1; i < workflowPhases.length; i++) {
        if (workflowPhases[i] && workflowPhases[i].estimatedTime) {
          remainingMinutes += parseTimeRange(workflowPhases[i].estimatedTime);
        } else {
          // Default value if estimatedTime is missing
          remainingMinutes += 3;
        }
      }

      // Format the time
      if (remainingMinutes < 1) {
        setEstimatedTimeRemaining('Less than a minute');
      } else if (remainingMinutes < 60) {
        setEstimatedTimeRemaining(`~${Math.ceil(remainingMinutes)} minutes`);
      } else {
        const hours = Math.floor(remainingMinutes / 60);
        const minutes = Math.ceil(remainingMinutes % 60);
        setEstimatedTimeRemaining(`~${hours}h ${minutes}m`);
      }
    } catch (error) {
      console.error('Error calculating estimated time:', error);
      setEstimatedTimeRemaining('Calculating...');
    }
  };

  // Parse time range string (e.g., "1-2 minutes") to average minutes
  const parseTimeRange = (timeRange: string): number => {
    if (!timeRange || typeof timeRange !== 'string') {
      return 3; // Default if timeRange is invalid
    }

    try {
      // Handle range format like "3-5 minutes"
      const rangeMatches = timeRange.match(/(\d+)-(\d+)/);
      if (rangeMatches && rangeMatches.length >= 3) {
        const min = parseInt(rangeMatches[1]);
        const max = parseInt(rangeMatches[2]);
        return (min + max) / 2;
      }

      // Handle single number format like "1 minute" or "5 minutes"
      const singleMatches = timeRange.match(/(\d+)\s*minute/);
      if (singleMatches && singleMatches.length >= 2) {
        return parseInt(singleMatches[1]);
      }

      // Handle numeric string
      if (/^\d+$/.test(timeRange)) {
        return parseInt(timeRange);
      }

      // Default fallback
      return 3;
    } catch (error) {
      console.error('Error parsing time range:', error);
      return 3; // Default to 3 minutes if parsing fails
    }
  };

  // Get color for agent status
  const getStatusColor = (status: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (status) {
      case 'complete':
        return 'success';
      case 'active':
        return 'primary';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" gutterBottom>
          Workflow Progress
        </Typography>

        <Box>
          {state.status === 'active' ? (
            <Tooltip title="Pause Workflow">
              <IconButton
                color="primary"
                onClick={onPauseWorkflow}
                disabled={!onPauseWorkflow || loading}
              >
                <PauseIcon />
              </IconButton>
            </Tooltip>
          ) : state.status === 'paused' ? (
            <Tooltip title="Resume Workflow">
              <IconButton
                color="primary"
                onClick={onResumeWorkflow}
                disabled={!onResumeWorkflow || loading}
              >
                <PlayArrowIcon />
              </IconButton>
            </Tooltip>
          ) : null}

          <Tooltip title="Refresh Status">
            <IconButton
              color="primary"
              onClick={onRefresh}
              disabled={!onRefresh || loading}
            >
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Overall Progress */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Overall Progress
          </Typography>
          <Typography variant="body2" fontWeight="medium">
            {overallProgress}%
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={overallProgress}
          sx={{ height: 8, borderRadius: 4 }}
        />

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} />
            <Typography variant="body2" color="text.secondary">
              Est. time remaining: {estimatedTimeRemaining}
            </Typography>
          </Box>

          <Chip
            label={state.status || 'Initializing'}
            color={
              state.status === 'active' ? 'primary' :
              state.status === 'completed' ? 'success' :
              state.status === 'paused' ? 'warning' :
              state.status === 'failed' ? 'error' : 'default'
            }
            size="small"
          />
        </Box>
      </Box>

      {/* Workflow Phase Stepper */}
      <Stepper activeStep={activePhaseIndex} orientation="vertical" sx={{ mb: 4 }}>
        {workflowPhases.map((phase, index) => (
          <Step key={phase.id} completed={index < activePhaseIndex}>
            <StepLabel
              StepIconProps={{
                icon: index === activePhaseIndex ? (
                  <CircularProgress size={24} thickness={6} variant="determinate" value={phaseProgress} />
                ) : phase.icon
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                <Typography variant="subtitle1">
                  {phase.label}
                </Typography>
                {index === activePhaseIndex && (
                  <Chip
                    label={`${phaseProgress}%`}
                    size="small"
                    color="primary"
                  />
                )}
              </Box>
            </StepLabel>
            <StepContent>
              <Typography variant="body2" color="text.secondary" paragraph>
                {phase.description}
              </Typography>

              {index === activePhaseIndex && (
                <LinearProgress
                  variant="determinate"
                  value={phaseProgress}
                  sx={{ height: 6, borderRadius: 3, mb: 2 }}
                />
              )}

              <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                <AccessTimeIcon fontSize="inherit" sx={{ mr: 0.5 }} />
                Estimated time: {phase.estimatedTime}
              </Typography>
            </StepContent>
          </Step>
        ))}
      </Stepper>

      {/* Active Agents */}
      <Typography variant="h6" gutterBottom>
        Agent Status
      </Typography>

      <Grid container spacing={2} sx={{ mb: 2 }}>
        {agentStatuses.map((agent) => (
          <Grid item xs={12} sm={6} md={4} key={agent.id}>
            <Paper
              variant="outlined"
              sx={{
                p: 2,
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                bgcolor: agent.status === 'active' ? 'rgba(33, 150, 243, 0.04)' : 'transparent',
                borderColor: agent.status === 'active' ? 'primary.main' : 'divider'
              }}
            >
              <Avatar
                sx={{
                  bgcolor: agent.status === 'active' ? 'primary.main' :
                          agent.status === 'complete' ? 'success.main' : 'grey.300',
                  color: '#fff'
                }}
              >
                {agent.icon}
              </Avatar>

              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="subtitle2">
                  {agent.name}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip
                    label={agent.status.charAt(0).toUpperCase() + agent.status.slice(1)}
                    size="small"
                    color={getStatusColor(agent.status)}
                  />
                  {agent.lastActivity && (
                    <Typography variant="caption" color="text.secondary">
                      Last active: {agent.lastActivity}
                    </Typography>
                  )}
                </Box>
              </Box>
            </Paper>
          </Grid>
        ))}
      </Grid>
    </Paper>
  );
};

export default EnhancedWorkflowVisualizer;
