// src/components/EnhancedCollaboration/ReasoningVisualizer.tsx

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Chip,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import LightbulbOutlinedIcon from '@mui/icons-material/LightbulbOutlined';
import TipsAndUpdatesIcon from '@mui/icons-material/TipsAndUpdates';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

interface Message {
  id: string;
  timestamp: string;
  from: string;
  to: string;
  role: string;
  parts: any[];
  conversationId: string;
  reasoning?: {
    thoughts: string[];
    considerations: string[];
    alternatives?: string[];
    decision: string;
    confidence: number;
  };
  intentions?: string[];
  artifactReferences?: string[];
}

interface Decision {
  id: string;
  timestamp: string;
  topic: string;
  options: Array<{
    id: string;
    description: string;
    pros: string[];
    cons: string[];
    votes: Array<{
      agent: string;
      score: number;
      reasoning: string;
    }>;
  }>;
  selectedOption: string;
  reasoning: string;
  confidence: number;
  contributors: string[];
}

interface ReasoningVisualizerProps {
  messages: Message[];
  decisions: Decision[];
}

const ReasoningVisualizer: React.FC<ReasoningVisualizerProps> = ({ messages, decisions }) => {
  const [activeTab, setActiveTab] = useState<number>(0);
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };
  
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };
  
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'success';
    if (confidence >= 0.5) return 'warning';
    return 'error';
  };
  
  const renderThoughtProcess = () => {
    return (
      <Box>
        {messages.length === 0 ? (
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body1" color="text.secondary">
              No agent messages with reasoning found yet.
            </Typography>
          </Paper>
        ) : (
          messages.map((message, index) => (
            <Card key={index} variant="outlined" sx={{ mb: 2 }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <TipsAndUpdatesIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6">
                      {message.from}
                    </Typography>
                  </Box>
                  <Typography variant="caption" color="text.secondary">
                    {formatTimestamp(message.timestamp)}
                  </Typography>
                </Box>
                
                {message.reasoning && (
                  <Box>
                    <Accordion defaultExpanded>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant="subtitle1">
                          Thoughts
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <ul style={{ paddingLeft: '1.5rem', margin: 0 }}>
                          {message.reasoning.thoughts.map((thought, i) => (
                            <li key={i}>
                              <Typography variant="body2" sx={{ mb: 0.5 }}>
                                {thought}
                              </Typography>
                            </li>
                          ))}
                        </ul>
                      </AccordionDetails>
                    </Accordion>
                    
                    <Accordion>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant="subtitle1">
                          Considerations
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <ul style={{ paddingLeft: '1.5rem', margin: 0 }}>
                          {message.reasoning.considerations.map((consideration, i) => (
                            <li key={i}>
                              <Typography variant="body2" sx={{ mb: 0.5 }}>
                                {consideration}
                              </Typography>
                            </li>
                          ))}
                        </ul>
                      </AccordionDetails>
                    </Accordion>
                    
                    {message.reasoning.alternatives && message.reasoning.alternatives.length > 0 && (
                      <Accordion>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography variant="subtitle1">
                            Alternatives Considered
                          </Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <ul style={{ paddingLeft: '1.5rem', margin: 0 }}>
                            {message.reasoning.alternatives.map((alternative, i) => (
                              <li key={i}>
                                <Typography variant="body2" sx={{ mb: 0.5 }}>
                                  {alternative}
                                </Typography>
                              </li>
                            ))}
                          </ul>
                        </AccordionDetails>
                      </Accordion>
                    )}
                    
                    <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        Decision
                      </Typography>
                      <Typography variant="body1">
                        {message.reasoning.decision}
                      </Typography>
                      
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                        <Typography variant="subtitle2" sx={{ mr: 1 }}>
                          Confidence:
                        </Typography>
                        <Box sx={{ width: '100%', mr: 1 }}>
                          <LinearProgress 
                            variant="determinate" 
                            value={message.reasoning.confidence * 100} 
                            color={getConfidenceColor(message.reasoning.confidence)} 
                          />
                        </Box>
                        <Box sx={{ minWidth: 35 }}>
                          <Typography variant="body2" color="text.secondary">
                            {Math.round(message.reasoning.confidence * 100)}%
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                )}
                
                {message.intentions && message.intentions.length > 0 && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Agent Intentions
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {message.intentions.map((intention, i) => (
                        <Chip key={i} label={intention} size="small" />
                      ))}
                    </Box>
                  </Box>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </Box>
    );
  };
  
  const renderDecisionHistory = () => {
    return (
      <Box>
        {decisions.length === 0 ? (
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body1" color="text.secondary">
              No collaborative decisions have been made yet.
            </Typography>
          </Paper>
        ) : (
          decisions.map((decision, index) => (
            <Card key={index} variant="outlined" sx={{ mb: 2 }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <CompareArrowsIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6">
                      {decision.topic}
                    </Typography>
                  </Box>
                  <Typography variant="caption" color="text.secondary">
                    {formatTimestamp(decision.timestamp)}
                  </Typography>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Options Considered
                  </Typography>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Option</TableCell>
                          <TableCell>Pros</TableCell>
                          <TableCell>Cons</TableCell>
                          <TableCell>Votes</TableCell>
                          <TableCell>Selected</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {decision.options.map((option) => (
                          <TableRow key={option.id} sx={{ 
                            bgcolor: option.id === decision.selectedOption ? 'rgba(76, 175, 80, 0.08)' : undefined 
                          }}>
                            <TableCell>{option.description}</TableCell>
                            <TableCell>
                              <ul style={{ margin: 0, paddingLeft: '1rem' }}>
                                {option.pros.map((pro, i) => (
                                  <li key={i}>
                                    <Typography variant="body2">{pro}</Typography>
                                  </li>
                                ))}
                              </ul>
                            </TableCell>
                            <TableCell>
                              <ul style={{ margin: 0, paddingLeft: '1rem' }}>
                                {option.cons.map((con, i) => (
                                  <li key={i}>
                                    <Typography variant="body2">{con}</Typography>
                                  </li>
                                ))}
                              </ul>
                            </TableCell>
                            <TableCell>
                              {option.votes.map((vote, i) => (
                                <Typography key={i} variant="body2">
                                  {vote.agent}: {vote.score}/10
                                </Typography>
                              ))}
                            </TableCell>
                            <TableCell>
                              {option.id === decision.selectedOption && (
                                <CheckCircleIcon color="success" />
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
                
                <Box sx={{ mt: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Final Decision
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="body1" gutterBottom>
                      {decision.reasoning}
                    </Typography>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                      <Typography variant="subtitle2" sx={{ mr: 1 }}>
                        Confidence:
                      </Typography>
                      <Box sx={{ width: '100%', mr: 1 }}>
                        <LinearProgress 
                          variant="determinate" 
                          value={decision.confidence * 100} 
                          color={getConfidenceColor(decision.confidence)} 
                        />
                      </Box>
                      <Box sx={{ minWidth: 35 }}>
                        <Typography variant="body2" color="text.secondary">
                          {Math.round(decision.confidence * 100)}%
                        </Typography>
                      </Box>
                    </Box>
                  </Paper>
                </Box>
                
                <Divider sx={{ my: 2 }} />
                
                <Box>
                  <Typography variant="caption" color="text.secondary">
                    Contributors: {decision.contributors.join(', ')}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          ))
        )}
      </Box>
    );
  };
  
  const renderInsightsDashboard = () => {
    // Aggregate confidence scores by agent
    const agentConfidence: Record<string, number[]> = {};
    messages.forEach(message => {
      if (message.reasoning && message.reasoning.confidence) {
        if (!agentConfidence[message.from]) {
          agentConfidence[message.from] = [];
        }
        agentConfidence[message.from].push(message.reasoning.confidence);
      }
    });
    
    // Calculate average confidence by agent
    const avgConfidence: Record<string, number> = {};
    Object.entries(agentConfidence).forEach(([agent, scores]) => {
      const sum = scores.reduce((acc, score) => acc + score, 0);
      avgConfidence[agent] = sum / scores.length;
    });
    
    // Count most common considerations across all agents
    const considerationCounts: Record<string, number> = {};
    messages.forEach(message => {
      if (message.reasoning && message.reasoning.considerations) {
        message.reasoning.considerations.forEach(consideration => {
          // Normalize consideration text to group similar ones
          const normalizedConsideration = consideration.toLowerCase().trim();
          considerationCounts[normalizedConsideration] = (considerationCounts[normalizedConsideration] || 0) + 1;
        });
      }
    });
    
    // Get top considerations
    const topConsiderations = Object.entries(considerationCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5);
    
    return (
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Agent Confidence Patterns
            </Typography>
            
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Agent</TableCell>
                    <TableCell>Average Confidence</TableCell>
                    <TableCell>Confidence Level</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {Object.entries(avgConfidence).map(([agent, confidence]) => (
                    <TableRow key={agent}>
                      <TableCell>{agent}</TableCell>
                      <TableCell>{Math.round(confidence * 100)}%</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Box sx={{ width: '100%', mr: 1 }}>
                            <LinearProgress 
                              variant="determinate" 
                              value={confidence * 100} 
                              color={getConfidenceColor(confidence)} 
                            />
                          </Box>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Top Considerations
            </Typography>
            
            {topConsiderations.length > 0 ? (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Consideration</TableCell>
                      <TableCell>Frequency</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {topConsiderations.map(([consideration, count], index) => (
                      <TableRow key={index}>
                        <TableCell>{consideration}</TableCell>
                        <TableCell>{count}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
                No considerations data available yet.
              </Typography>
            )}
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Agent Decision Patterns
            </Typography>
            
            {decisions.length > 0 ? (
              <Box>
                <Typography variant="body2" gutterBottom>
                  Total collaborative decisions: {decisions.length}
                </Typography>
                
                <Typography variant="subtitle2" sx={{ mt: 2 }} gutterBottom>
                  Agent Contribution to Decisions
                </Typography>
                
                {/* Count agent contributions to decisions */}
                {(() => {
                  const contributionCounts: Record<string, number> = {};
                  decisions.forEach(decision => {
                    decision.contributors.forEach(contributor => {
                      contributionCounts[contributor] = (contributionCounts[contributor] || 0) + 1;
                    });
                  });
                  
                  return (
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Agent</TableCell>
                            <TableCell>Contributions</TableCell>
                            <TableCell>Percentage</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {Object.entries(contributionCounts).map(([agent, count], index) => (
                            <TableRow key={index}>
                              <TableCell>{agent}</TableCell>
                              <TableCell>{count}</TableCell>
                              <TableCell>
                                {Math.round((count / decisions.length) * 100)}%
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  );
                })()}
              </Box>
            ) : (
              <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
                No decision data available yet.
              </Typography>
            )}
          </Paper>
        </Grid>
      </Grid>
    );
  };
  
  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs 
          value={activeTab} 
          onChange={handleTabChange} 
          variant="fullWidth"
        >
          <Tab icon={<LightbulbOutlinedIcon />} label="Thought Processes" />
          <Tab icon={<CompareArrowsIcon />} label="Decision History" />
          <Tab icon={<TipsAndUpdatesIcon />} label="Insights" />
        </Tabs>
      </Box>
      
      <Box sx={{ p: 1 }}>
        {activeTab === 0 && renderThoughtProcess()}
        {activeTab === 1 && renderDecisionHistory()}
        {activeTab === 2 && renderInsightsDashboard()}
      </Box>
    </Box>
  );
};

export default ReasoningVisualizer;
