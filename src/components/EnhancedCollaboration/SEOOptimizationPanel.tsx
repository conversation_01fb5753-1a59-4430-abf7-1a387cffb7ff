'use client'

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Button,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';
import SearchIcon from '@mui/icons-material/Search';
import TitleIcon from '@mui/icons-material/Title';
import DescriptionIcon from '@mui/icons-material/Description';
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';
import TextFieldsIcon from '@mui/icons-material/TextFields';
import LinkIcon from '@mui/icons-material/Link';
import PsychologyIcon from '@mui/icons-material/Psychology';
import ViewCarouselIcon from '@mui/icons-material/ViewCarousel';
import CodeIcon from '@mui/icons-material/Code';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

// Define the props interface
interface SEOOptimizationPanelProps {
  seoOptimization: {
    overallScore: number;
    onPageSeo: {
      titleTag: {
        score: number;
        original: string;
        optimized: string;
        suggestions: string[];
      };
      metaDescription: {
        score: number;
        original: string;
        optimized: string;
        suggestions: string[];
      };
      headings: {
        score: number;
        original: string[];
        optimized: string[];
        suggestions: string[];
      };
      content: {
        score: number;
        keywordDensity: Record<string, number>;
        suggestions: string[];
      };
      internalLinking: {
        score: number;
        suggestions: string[];
      };
    };
    semanticSeo: {
      score: number;
      topicClusters: string[];
      relatedEntities: string[];
      suggestions: string[];
    };
    serpFeatures: {
      score: number;
      featuredSnippetPotential: number;
      faqSchemaPotential: number;
      howToSchemaPotential: number;
      suggestions: string[];
    };
    structuredData: {
      score: number;
      recommendedSchemas: string[];
      schemaExamples: Record<string, any>;
      suggestions: string[];
    };
    suggestions: string[];
  };
  analysis?: string;
  recommendations?: string;
  prioritizedTasks?: string[];
  keywordSuggestions?: string[];
}

/**
 * Component to display comprehensive SEO optimization metrics and recommendations
 */
const SEOOptimizationPanel: React.FC<SEOOptimizationPanelProps> = ({
  seoOptimization,
  analysis,
  recommendations,
  prioritizedTasks,
  keywordSuggestions
}) => {
  const [expandedSection, setExpandedSection] = useState<string | false>('panel1');
  const [showSchemaExamples, setShowSchemaExamples] = useState<boolean>(false);

  const handleChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedSection(isExpanded ? panel : false);
  };

  // Helper function to get color based on score
  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'success.main';
    if (score >= 0.6) return 'warning.main';
    return 'error.main';
  };

  // Helper function to format score as percentage
  const formatScore = (score: number) => `${Math.round(score * 100)}%`;

  return (
    <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h5" gutterBottom>
        SEO Optimization Analysis
      </Typography>
      <Divider sx={{ mb: 3 }} />

      {/* Overall Score */}
      <Box sx={{ mb: 4, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box
            sx={{
              width: 80,
              height: 80,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: `conic-gradient(${getScoreColor(seoOptimization.overallScore)} ${seoOptimization.overallScore * 360}deg, #e0e0e0 0)`,
              mr: 2
            }}
          >
            <Box
              sx={{
                width: 70,
                height: 70,
                borderRadius: '50%',
                bgcolor: 'background.paper',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                {formatScore(seoOptimization.overallScore)}
              </Typography>
            </Box>
          </Box>
          <Box>
            <Typography variant="h6">Overall SEO Score</Typography>
            <Typography variant="body2" color="text.secondary">
              Based on comprehensive analysis of on-page, semantic, and structured data factors
            </Typography>
          </Box>
        </Box>
        <Chip
          label={seoOptimization.overallScore >= 0.7 ? 'SEO Optimized' : 'Needs SEO Improvement'}
          color={seoOptimization.overallScore >= 0.7 ? 'success' : 'warning'}
          sx={{ fontWeight: 'bold' }}
        />
      </Box>

      {/* SEO Categories */}
      <Typography variant="h6" gutterBottom>
        SEO Categories
      </Typography>
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="outlined">
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <SearchIcon sx={{ mr: 1, color: getScoreColor(seoOptimization.onPageSeo.titleTag.score) }} />
                <Typography variant="subtitle1">
                  On-Page SEO
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={((
                  seoOptimization.onPageSeo.titleTag.score +
                  seoOptimization.onPageSeo.metaDescription.score +
                  seoOptimization.onPageSeo.headings.score +
                  seoOptimization.onPageSeo.content.score +
                  seoOptimization.onPageSeo.internalLinking.score
                ) / 5) * 100}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  mb: 1,
                  bgcolor: 'grey.200',
                  '& .MuiLinearProgress-bar': {
                    bgcolor: getScoreColor(
                      (
                        seoOptimization.onPageSeo.titleTag.score +
                        seoOptimization.onPageSeo.metaDescription.score +
                        seoOptimization.onPageSeo.headings.score +
                        seoOptimization.onPageSeo.content.score +
                        seoOptimization.onPageSeo.internalLinking.score
                      ) / 5
                    )
                  }
                }}
              />
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="outlined">
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <PsychologyIcon sx={{ mr: 1, color: getScoreColor(seoOptimization.semanticSeo.score) }} />
                <Typography variant="subtitle1">
                  Semantic SEO
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={seoOptimization.semanticSeo.score * 100}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  mb: 1,
                  bgcolor: 'grey.200',
                  '& .MuiLinearProgress-bar': {
                    bgcolor: getScoreColor(seoOptimization.semanticSeo.score)
                  }
                }}
              />
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="outlined">
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <ViewCarouselIcon sx={{ mr: 1, color: getScoreColor(seoOptimization.serpFeatures.score) }} />
                <Typography variant="subtitle1">
                  SERP Features
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={seoOptimization.serpFeatures.score * 100}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  mb: 1,
                  bgcolor: 'grey.200',
                  '& .MuiLinearProgress-bar': {
                    bgcolor: getScoreColor(seoOptimization.serpFeatures.score)
                  }
                }}
              />
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="outlined">
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <CodeIcon sx={{ mr: 1, color: getScoreColor(seoOptimization.structuredData.score) }} />
                <Typography variant="subtitle1">
                  Structured Data
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={seoOptimization.structuredData.score * 100}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  mb: 1,
                  bgcolor: 'grey.200',
                  '& .MuiLinearProgress-bar': {
                    bgcolor: getScoreColor(seoOptimization.structuredData.score)
                  }
                }}
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Prioritized Tasks */}
      {prioritizedTasks && prioritizedTasks.length > 0 && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Prioritized SEO Tasks
          </Typography>
          <List dense>
            {prioritizedTasks.map((task, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  <Chip 
                    label={index + 1} 
                    size="small" 
                    color={index < 2 ? 'error' : index < 4 ? 'warning' : 'success'} 
                  />
                </ListItemIcon>
                <ListItemText primary={task} />
              </ListItem>
            ))}
          </List>
        </Box>
      )}

      {/* Keyword Density */}
      <Accordion expanded={expandedSection === 'panel1'} onChange={handleChange('panel1')}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <TextFieldsIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="subtitle1">Keyword Analysis</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <TableContainer component={Paper} variant="outlined">
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Keyword</TableCell>
                  <TableCell align="right">Density</TableCell>
                  <TableCell align="right">Status</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {Object.entries(seoOptimization.onPageSeo.content.keywordDensity).map(([keyword, density]) => (
                  <TableRow key={keyword}>
                    <TableCell component="th" scope="row">
                      {keyword}
                    </TableCell>
                    <TableCell align="right">{density.toFixed(2)}%</TableCell>
                    <TableCell align="right">
                      <Chip
                        size="small"
                        label={
                          density >= 0.5 && density <= 2.5
                            ? 'Optimal'
                            : density < 0.5
                            ? 'Too Low'
                            : 'Too High'
                        }
                        color={
                          density >= 0.5 && density <= 2.5
                            ? 'success'
                            : density < 0.5
                            ? 'warning'
                            : 'error'
                        }
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {keywordSuggestions && keywordSuggestions.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Suggested Additional Keywords:
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {keywordSuggestions.map((keyword, index) => (
                  <Chip key={index} label={keyword} size="small" />
                ))}
              </Box>
            </Box>
          )}
        </AccordionDetails>
      </Accordion>

      {/* Analysis */}
      {analysis && (
        <Accordion expanded={expandedSection === 'panel2'} onChange={handleChange('panel2')}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <SearchIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="subtitle1">SEO Analysis</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ maxHeight: '400px', overflow: 'auto' }}>
              <ReactMarkdown remarkPlugins={[remarkGfm]}>{analysis}</ReactMarkdown>
            </Box>
          </AccordionDetails>
        </Accordion>
      )}

      {/* Recommendations */}
      {recommendations && (
        <Accordion expanded={expandedSection === 'panel3'} onChange={handleChange('panel3')}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <InfoIcon sx={{ mr: 1, color: 'info.main' }} />
              <Typography variant="subtitle1">SEO Recommendations</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ maxHeight: '400px', overflow: 'auto' }}>
              <ReactMarkdown remarkPlugins={[remarkGfm]}>{recommendations}</ReactMarkdown>
            </Box>
          </AccordionDetails>
        </Accordion>
      )}
    </Paper>
  );
};

export default SEOOptimizationPanel;
