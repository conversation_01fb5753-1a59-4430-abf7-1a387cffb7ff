import React, { useEffect, useState } from 'react';
import { Box, Typography, Divider, Paper, Chip, CircularProgress } from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import AutorenewIcon from '@mui/icons-material/Autorenew';

export interface AgentLogEntry {
  timestamp: Date;
  level: 'info' | 'debug' | 'warn' | 'error' | 'success';
  message: string;
  agent?: string;
  phase?: string;
  context?: Record<string, any>;
}

interface AgentActivityLoggerProps {
  sessionId: string;
  logs: AgentLogEntry[];
  maxEntries?: number;
  showTimestamps?: boolean;
  autoScroll?: boolean;
  compact?: boolean;
}

/**
 * Component to display agent activity logs in the dashboard
 */
const AgentActivityLogger: React.FC<AgentActivityLoggerProps> = ({
  sessionId,
  logs,
  maxEntries = 50,
  showTimestamps = true,
  autoScroll = true,
  compact = false
}) => {
  const [visibleLogs, setVisibleLogs] = useState<AgentLogEntry[]>([]);
  const logContainerRef = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Keep only the most recent logs up to maxEntries
    setVisibleLogs(logs.slice(-maxEntries));
    
    // Auto-scroll to bottom when new logs are added
    if (autoScroll && logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logs, maxEntries, autoScroll]);

  // Format timestamp to show only relevant parts
  const formatTimestamp = (timestamp: Date): string => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false });
  };

  // Get icon based on log level
  const getLogIcon = (level: string) => {
    switch (level) {
      case 'info':
        return <InfoIcon fontSize="small" color="info" />;
      case 'error':
        return <ErrorIcon fontSize="small" color="error" />;
      case 'warn':
        return <WarningIcon fontSize="small" color="warning" />;
      case 'success':
        return <CheckCircleIcon fontSize="small" color="success" />;
      case 'debug':
        return <AutorenewIcon fontSize="small" color="disabled" />;
      default:
        return <InfoIcon fontSize="small" color="info" />;
    }
  };

  // Get color for agent chip
  const getAgentColor = (agent?: string): string => {
    if (!agent) return 'default';
    
    const agentColors: Record<string, string> = {
      'content-generation': 'primary',
      'market-research': 'success',
      'seo-keyword': 'info',
      'content-strategy': 'secondary',
      'seo-optimization': 'warning',
      'system': 'default'
    };
    
    return agentColors[agent.toLowerCase()] || 'default';
  };

  return (
    <Paper 
      elevation={1} 
      sx={{ 
        height: compact ? '200px' : '300px', 
        overflow: 'auto',
        p: compact ? 1 : 2,
        bgcolor: 'background.paper',
        borderRadius: 2
      }}
      ref={logContainerRef}
    >
      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'medium', mb: 1 }}>
        Agent Activity Log
      </Typography>
      <Divider sx={{ mb: 1 }} />
      
      {visibleLogs.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70%' }}>
          <CircularProgress size={20} sx={{ mr: 1 }} />
          <Typography variant="body2" color="text.secondary">
            Waiting for agent activity...
          </Typography>
        </Box>
      ) : (
        visibleLogs.map((log, index) => (
          <Box 
            key={index} 
            sx={{ 
              mb: 1, 
              py: 0.5, 
              px: compact ? 0.5 : 1,
              borderLeft: '3px solid',
              borderColor: `${log.level}.main`,
              '&:hover': { bgcolor: 'action.hover' }
            }}
          >
            <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'flex-start' }}>
              <Box sx={{ mr: 1, mt: '2px' }}>{getLogIcon(log.level)}</Box>
              <Box sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 0.5 }}>
                    {log.agent && (
                      <Chip 
                        label={log.agent} 
                        size="small" 
                        color={getAgentColor(log.agent) as any}
                        variant="outlined"
                        sx={{ height: 20, fontSize: '0.7rem' }}
                      />
                    )}
                    {log.phase && (
                      <Chip 
                        label={log.phase} 
                        size="small" 
                        color="default"
                        variant="outlined"
                        sx={{ height: 20, fontSize: '0.7rem' }}
                      />
                    )}
                  </Box>
                  {showTimestamps && (
                    <Typography variant="caption" color="text.secondary" sx={{ ml: 1, flexShrink: 0 }}>
                      {formatTimestamp(log.timestamp)}
                    </Typography>
                  )}
                </Box>
                <Typography variant="body2" component="div" sx={{ wordWrap: 'break-word' }}>
                  {log.message}
                </Typography>
                {!compact && log.context && Object.keys(log.context).length > 0 && (
                  <Box sx={{ mt: 0.5, bgcolor: 'action.hover', p: 0.5, borderRadius: 1 }}>
                    <Typography variant="caption" component="pre" sx={{ 
                      fontSize: '0.7rem', 
                      whiteSpace: 'pre-wrap',
                      wordBreak: 'break-all',
                      m: 0
                    }}>
                      {JSON.stringify(log.context, null, 2)}
                    </Typography>
                  </Box>
                )}
              </Box>
            </Box>
            {index < visibleLogs.length - 1 && <Divider sx={{ mt: 1 }} />}
          </Box>
        ))
      )}
    </Paper>
  );
};

export default AgentActivityLogger;
