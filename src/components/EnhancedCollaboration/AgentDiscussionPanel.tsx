import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Paper,
  Divider,
  Card,
  CardContent,
  Avatar,
  Chip,
  TextField,
  Button,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import SendIcon from '@mui/icons-material/Send';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

// Agent color and name mappings
const agentColors: Record<string, string> = {
  'market-research': '#ff9800',
  'seo-keyword': '#00bcd4',
  'content-strategy': '#9c27b0',
  'content-generation': '#4caf50',
  'seo-optimization': '#2196f3',
  'system': '#757575',
  'user': '#f44336'
};

const agentNames: Record<string, string> = {
  'market-research': 'Market Research',
  'seo-keyword': 'SEO Keyword',
  'content-strategy': 'Content Strategy',
  'content-generation': 'Content Writer',
  'seo-optimization': 'SEO Optimizer',
  'system': 'System',
  'user': 'You'
};

// Props for the component
interface AgentDiscussionPanelProps {
  messages: any[];
  sessionId: string;
  onSendMessage?: (text: string) => void;
  showInput?: boolean;
}

/**
 * Component to display agent discussions in a chat-like interface
 * Shows messages between agents with support for markdown content
 */
const AgentDiscussionPanel: React.FC<AgentDiscussionPanelProps> = ({
  messages = [],
  sessionId,
  onSendMessage,
  showInput = true
}) => {
  const [messageInput, setMessageInput] = useState('');
  const [filter, setFilter] = useState<string | null>(null);

  // Sort messages by timestamp
  const sortedMessages = [...messages].sort(
    (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );

  // Filter messages based on selected agent
  const filteredMessages = filter
    ? sortedMessages.filter(msg => msg.from === filter || (typeof msg.to === 'string' && msg.to === filter))
    : sortedMessages;

  // Get all unique agents involved in the conversation
  const allAgents = Array.from(new Set([
    ...messages.map(m => m.from),
    ...messages.map(m => Array.isArray(m.to) ? m.to : [m.to]).flat()
  ])).filter(Boolean);

  // Enhanced grouping of discussions by thread with better handling of ARTIFACT_DELIVERY messages
  const discussions: Record<string, any[]> = {};

  // First pass: Create threads and add messages
  filteredMessages.forEach(message => {
    // Use conversationId as the thread ID if available, otherwise use parent message ID
    const threadId = message.conversationId ||
                    message.inReplyTo ||
                    (message.metadata?.inReplyTo) ||
                    message.id ||
                    'default-thread'; // Fallback to ensure all messages are displayed

    if (!discussions[threadId]) {
      discussions[threadId] = [];
    }
    discussions[threadId].push(message);
  });

  // Second pass: Ensure ARTIFACT_DELIVERY messages are properly grouped
  // If an ARTIFACT_DELIVERY message has originalMessageId, move it to the thread of that message
  filteredMessages.forEach(message => {
    if (message.type === 'ARTIFACT_DELIVERY' && message.content?.originalMessageId) {
      const originalMsgId = message.content.originalMessageId;

      // Find the thread containing the original message
      let foundInThread = false;
      for (const [threadId, messages] of Object.entries(discussions)) {
        // Check if this thread contains the original message
        if (messages.some(msg => msg.id === originalMsgId)) {
          // If the message is not already in this thread, move it
          if (!messages.some(msg => msg.id === message.id)) {
            console.log(`Moving ARTIFACT_DELIVERY message ${message.id} to thread with original message ${originalMsgId}`);

            // Remove from current thread
            for (const [currentThreadId, currentMessages] of Object.entries(discussions)) {
              if (currentThreadId !== threadId) {
                const index = currentMessages.findIndex(msg => msg.id === message.id);
                if (index !== -1) {
                  discussions[currentThreadId] = currentMessages.filter(msg => msg.id !== message.id);
                  // If thread is now empty, remove it
                  if (discussions[currentThreadId].length === 0) {
                    delete discussions[currentThreadId];
                  }
                }
              }
            }

            // Add to the thread with the original message
            discussions[threadId].push(message);
          }

          foundInThread = true;
          break;
        }
      }

      // If original message thread not found, keep in current thread
      if (!foundInThread) {
        console.log(`Could not find thread for original message ${originalMsgId}, keeping ARTIFACT_DELIVERY message in current thread`);
      }
    }
  });

  // Enhanced recursive function to find text content in nested objects
  const findTextContent = (obj: any): string | null => {
    if (!obj) return null;

    // If it's a string, return it directly
    if (typeof obj === 'string') return obj;

    // Check for text field directly (our new field)
    if (obj.text && typeof obj.text === 'string') return obj.text;

    // Check for common direct fields
    if (obj.content?.text && typeof obj.content.text === 'string') return obj.content.text;
    if (obj.message && typeof obj.message === 'string') return obj.message;
    if (obj.message) return findTextContent(obj.message);

    // Special handling for artifact delivery messages
    if (obj.artifactId || obj.artifactType) {
      console.log('Found artifact delivery message, checking for artifact content');

      // Check for artifact content
      if (obj.artifact) {
        console.log('Found artifact in message, checking for content');
        const artifactContent = findTextContent(obj.artifact);
        if (artifactContent) return artifactContent;
      }
    }

    // Look in common fields
    const possibleTextFields = ['text', 'content', 'message', 'question', 'answer', 'feedback', 'body', 'description', 'data', 'value'];
    for (const field of possibleTextFields) {
      if (typeof obj[field] === 'string' && obj[field].trim()) {
        return obj[field];
      }

      // Check one level deeper for these important fields
      if (obj[field] && typeof obj[field] === 'object') {
        for (const subField of ['text', 'content', 'body', 'data', 'message']) {
          if (typeof obj[field][subField] === 'string' && obj[field][subField].trim()) {
            return obj[field][subField];
          }
        }
      }
    }

    // Handle arrays - check if any array element contains text content
    if (Array.isArray(obj)) {
      for (const item of obj) {
        if (typeof item === 'string' && item.trim()) {
          return item;
        }
        if (typeof item === 'object' && item !== null) {
          const nestedText = findTextContent(item);
          if (nestedText) return nestedText;
        }
      }
    }

    // Recursively search all object properties
    for (const key in obj) {
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        const nestedText = findTextContent(obj[key]);
        if (nestedText) return nestedText;
      }
    }

    return null;
  };

  // Enhanced render message content function with better artifact handling
  const renderMessageContent = (content: any, message: any) => {
    if (!content) {
      return <Typography>No content available</Typography>;
    }

    // Special handling for ARTIFACT_DELIVERY messages
    if (message && message.type === 'ARTIFACT_DELIVERY') {
      console.log('Rendering ARTIFACT_DELIVERY message:', message.id);

      // Try to extract artifact content
      let artifactContent = null;
      let artifactTitle = '';
      let artifactType = '';

      // Check if we have an artifact ID in the message content
      if (content.artifactId) {
        console.log(`Found artifact ID in message: ${content.artifactId}`);
        artifactType = content.artifactType || 'unknown';

        // If the artifact object is directly included in the message
        if (content.artifact) {
          console.log('Artifact object found in message');

          // First try to get the text field directly from the artifact content
          if (content.artifact.content && content.artifact.content.text) {
            console.log('Found text field in artifact content');
            artifactContent = content.artifact.content.text;
          } else {
            // Otherwise use our recursive function
            artifactContent = findTextContent(content.artifact);
          }

          artifactTitle = content.artifact.name || content.artifact.title || `${artifactType} Artifact`;
        }

        // If we still don't have content, try to find the artifact in the session state
        // This would require access to the session state, which we don't have directly in this component
        // For now, we'll just use what's available in the message
      }

      // If we couldn't find artifact content in the artifact object, try other fields
      if (!artifactContent) {
        console.log('Searching for artifact content in other fields');

        // Try to extract from common fields
        const possibleContentFields = [
          content.content,
          content.data,
          content.text,
          content.message,
          content.artifact?.content?.text, // Direct access to text field
          content.artifact?.content,
          content.artifact?.data?.content,
          content.artifact?.text
        ];

        // Find the first substantial content field
        for (const field of possibleContentFields) {
          if (field && typeof field === 'string' && field.trim()) {
            artifactContent = field;
            break;
          }
        }

        // If still no content, try recursive search
        if (!artifactContent) {
          artifactContent = findTextContent(content);
        }
      }

      // If we found content, display it in a nice format
      if (artifactContent) {
        const displayTitle = artifactTitle || `${artifactType.charAt(0).toUpperCase() + artifactType.slice(1)} Artifact`;

        return (
          <Box>
            <Typography variant="subtitle2" color="primary" gutterBottom>
              {displayTitle}:
            </Typography>
            <Paper variant="outlined" sx={{ p: 1, maxHeight: '300px', overflow: 'auto' }}>
              <ReactMarkdown remarkPlugins={[remarkGfm]}>{artifactContent}</ReactMarkdown>
            </Paper>
          </Box>
        );
      }

      // If we still don't have content, show a message with the artifact ID
      return (
        <Box>
          <Typography variant="subtitle2" color="primary" gutterBottom>
            {artifactType.charAt(0).toUpperCase() + artifactType.slice(1)} Artifact:
          </Typography>
          <Paper variant="outlined" sx={{ p: 1 }}>
            <Typography variant="body2">
              Artifact ID: {content.artifactId}
              {content.originalMessageId && ` (In response to message: ${content.originalMessageId.substring(0, 8)}...)`}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              The detailed content of this artifact is available in the Artifacts tab.
            </Typography>
          </Paper>
        </Box>
      );
    }

    // Special handling for content generation messages with forceRealContent flag
    if (message &&
        (message.from === 'content-generation' || message.to === 'content-generation') &&
        message.content?.forceRealContent === true) {

      // If this is a request to generate content
      if (message.to === 'content-generation') {
        // Just display the normal content for the request
        const textContent = findTextContent(content);
        if (textContent) {
          return <ReactMarkdown remarkPlugins={[remarkGfm]}>{textContent}</ReactMarkdown>;
        }
      }

      // If this is a response from content-generation (potentially containing the article)
      if (message.from === 'content-generation') {
        // Try to extract the article content
        const possibleContentFields = [
          content.content,
          content.data,
          content.text,
          content.response,
          content.artifact?.content,
          content.artifact?.data?.content
        ];

        // Find the first substantial content field
        for (const field of possibleContentFields) {
          if (field && typeof field === 'string' && field.length > 200) {
            return (
              <Box>
                <Typography variant="subtitle2" color="primary" gutterBottom>
                  Generated Content:
                </Typography>
                <Paper variant="outlined" sx={{ p: 1, maxHeight: '300px', overflow: 'auto' }}>
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>{field}</ReactMarkdown>
                </Paper>
              </Box>
            );
          }
        }
      }
    }

    // Standard content rendering
    if (typeof content === 'string') {
      return <ReactMarkdown remarkPlugins={[remarkGfm]}>{content}</ReactMarkdown>;
    } else if (content?.text) {
      return <ReactMarkdown remarkPlugins={[remarkGfm]}>{content.text}</ReactMarkdown>;
    } else if (typeof content === 'object') {
      // Try to find text content in nested objects
      const textContent = findTextContent(content);
      if (textContent) {
        return <ReactMarkdown remarkPlugins={[remarkGfm]}>{textContent}</ReactMarkdown>;
      }
      // Fall back to displaying JSON
      return (
        <pre style={{ fontSize: '0.85rem', whiteSpace: 'pre-wrap', overflowX: 'auto' }}>
          {JSON.stringify(content, null, 2)}
        </pre>
      );
    }
    return <Typography>No content available</Typography>;
  };

  // Handle sending a message
  const handleSendMessage = () => {
    if (messageInput.trim() && onSendMessage) {
      onSendMessage(messageInput);
      setMessageInput('');
    }
  };

  // Handle key press in the message input
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Helper function to get a descriptive title for a thread based on its messages
  const getThreadTitle = (messages: any[]): string => {
    if (!messages || messages.length === 0) {
      return 'Empty Thread';
    }

    // Get the first message type as the base
    const firstMessage = messages[0];
    let baseType = firstMessage?.type || 'Conversation';

    // Check for artifact delivery messages
    const hasArtifactDelivery = messages.some(msg => msg.type === 'ARTIFACT_DELIVERY');

    // Check for specific message types to create more descriptive titles
    if (hasArtifactDelivery) {
      // Find the artifact type if available
      const artifactMsg = messages.find(msg => msg.type === 'ARTIFACT_DELIVERY');
      const artifactType = artifactMsg?.content?.artifactType || 'Content';

      if (baseType === 'INITIAL_REQUEST') {
        return `${artifactType.charAt(0).toUpperCase() + artifactType.slice(1)} Request & Delivery`;
      } else {
        return `${artifactType.charAt(0).toUpperCase() + artifactType.slice(1)} Artifact Delivery`;
      }
    }

    // Format other common message types
    switch (baseType) {
      case 'INITIAL_REQUEST':
        return 'Initial Content Request';
      case 'CONSULTATION_REQUEST':
        return 'Consultation Request';
      case 'CONSULTATION_RESPONSE':
        return 'Consultation Feedback';
      case 'ITERATION_REQUEST':
        return 'Content Iteration Request';
      case 'ITERATION_RESPONSE':
        return 'Content Iteration Response';
      case 'FINAL_OUTPUT':
        return 'Final Content Output';
      case 'DISCUSSION_START':
        return 'Discussion Thread';
      case 'DISCUSSION_SYNTHESIS':
        return 'Discussion Summary';
      case 'ARTIFACT_REQUEST':
        return 'Artifact Request';
      default:
        // Format the message type for display (convert from UPPER_CASE to Title Case)
        return baseType
          .split('_')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' ');
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Agent Discussions</Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          {allAgents.map(agentId => (
            <Chip
              key={agentId}
              avatar={<Avatar sx={{ bgcolor: agentColors[agentId] || '#757575' }}>
                {(agentNames[agentId] || agentId).charAt(0)}
              </Avatar>}
              label={agentNames[agentId] || agentId}
              onClick={() => setFilter(filter === agentId ? null : agentId)}
              color={filter === agentId ? "primary" : "default"}
              variant={filter === agentId ? "filled" : "outlined"}
              size="small"
            />
          ))}
        </Box>
      </Box>

      <Divider sx={{ mb: 2 }} />

      {/* Message Threads */}
      <Box sx={{ maxHeight: '60vh', overflow: 'auto', pb: 2 }}>
        {Object.entries(discussions).map(([threadId, messages]) => (
          <Accordion
            key={threadId}
            defaultExpanded={true}
            sx={{ mb: 2, overflow: 'visible' }}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">
                {getThreadTitle(messages)}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ ml: 2 }}>
                {messages.length} messages
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Box>
                {messages.map((message, index) => {
                  // Handle undefined message type
                  if (!message.type) {
                    console.warn(`Message with ID ${message.id} has undefined type, defaulting to REQUEST`);
                    message.type = 'REQUEST';
                  }

                  const fromAgent = agentNames[message.from] || message.from;
                  const toAgent = Array.isArray(message.to)
                    ? message.to.map(t => agentNames[t] || t).join(', ')
                    : agentNames[message.to] || message.to;
                  const fromColor = agentColors[message.from] || '#757575';

                  // Determine if the message is from the user (align right) or an agent (align left)
                  const isFromUser = message.from === 'user';

                  return (
                    <Box
                      key={`${message.id}-${index}`}
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: isFromUser ? 'flex-end' : 'flex-start',
                        mb: 2
                      }}
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          mb: 0.5,
                          flexDirection: isFromUser ? 'row-reverse' : 'row'
                        }}
                      >
                        <Avatar
                          sx={{
                            bgcolor: fromColor,
                            width: 28,
                            height: 28,
                            fontSize: '0.8rem',
                            mr: isFromUser ? 0 : 1,
                            ml: isFromUser ? 1 : 0
                          }}
                        >
                          {fromAgent.charAt(0)}
                        </Avatar>
                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                          {fromAgent}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mx: 0.5 }}>
                          →
                        </Typography>
                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                          {toAgent}
                        </Typography>
                      </Box>

                      <Card
                        sx={{
                          maxWidth: '80%',
                          ml: isFromUser ? 'auto' : 0,
                          mr: isFromUser ? 0 : 'auto',
                          borderRadius: 2,
                          borderTopLeftRadius: isFromUser ? 2 : 0,
                          borderTopRightRadius: isFromUser ? 0 : 2,
                          bgcolor: isFromUser ? 'primary.light' : 'background.paper',
                          borderLeft: !isFromUser ? `4px solid ${fromColor}` : 'none',
                          borderRight: isFromUser ? `4px solid ${fromColor}` : 'none',
                        }}
                      >
                        <CardContent sx={{ py: 1, px: 2, '&:last-child': { pb: 1 } }}>
                          <Box sx={{ fontSize: '0.85rem' }}>
                            {renderMessageContent(message.content, message)}
                          </Box>

                          <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{
                              display: 'block',
                              textAlign: 'right',
                              mt: 0.5
                            }}
                          >
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </Typography>
                        </CardContent>
                      </Card>

                      {/* Show reasoning if available */}
                      {message.reasoning && (
                        <Tooltip title="Click to see agent reasoning">
                          <Button
                            size="small"
                            variant="text"
                            onClick={(e) => e.stopPropagation()}
                            sx={{
                              ml: isFromUser ? 'auto' : 0,
                              mr: isFromUser ? 0 : 'auto',
                              mt: 0.5
                            }}
                          >
                            View reasoning
                          </Button>
                        </Tooltip>
                      )}
                    </Box>
                  );
                })}
              </Box>
            </AccordionDetails>
          </Accordion>
        ))}
      </Box>

      {/* Input for sending messages */}
      {showInput && onSendMessage && (
        <Paper
          elevation={1}
          component="form"
          onSubmit={(e) => {
            e.preventDefault();
            handleSendMessage();
          }}
          sx={{
            p: 2,
            display: 'flex',
            alignItems: 'flex-end',
            gap: 1,
            bgcolor: 'background.paper',
            mt: 2
          }}
        >
          <TextField
            fullWidth
            label="Send a message to the agents"
            multiline
            rows={2}
            value={messageInput}
            onChange={(e) => setMessageInput(e.target.value)}
            onKeyDown={handleKeyPress}
            variant="outlined"
          />
          <Button
            variant="contained"
            color="primary"
            endIcon={<SendIcon />}
            onClick={handleSendMessage}
            disabled={!messageInput.trim()}
          >
            Send
          </Button>
        </Paper>
      )}
    </Box>
  );
};

export default AgentDiscussionPanel;
export { AgentDiscussionPanel };
