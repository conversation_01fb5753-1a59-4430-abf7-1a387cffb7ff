// src/components/EnhancedCollaboration/AgentReasoningVisualizer.tsx

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Chip,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Tooltip
} from '@mui/material';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent
} from '@mui/lab';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import LightbulbOutlinedIcon from '@mui/icons-material/LightbulbOutlined';
import TipsAndUpdatesIcon from '@mui/icons-material/TipsAndUpdates';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import PsychologyIcon from '@mui/icons-material/Psychology';
import FactCheckIcon from '@mui/icons-material/FactCheck';
import BalanceIcon from '@mui/icons-material/Balance';
import RateReviewIcon from '@mui/icons-material/RateReview';
import AssignmentTurnedInIcon from '@mui/icons-material/AssignmentTurnedIn';

// Define the props interface
interface AgentReasoningVisualizerProps {
  iterations: any[]; // Array of iterations with reasoning data
  agentId?: string; // Optional agent ID to filter by
}

// Map agent IDs to colors and icons
const agentConfig: Record<string, { color: string; icon: React.ReactNode; name: string }> = {
  'content-generation': {
    color: '#4caf50',
    icon: <RateReviewIcon />,
    name: 'Content Generation'
  },
  'seo-optimization': {
    color: '#2196f3',
    icon: <PsychologyIcon />,
    name: 'SEO Optimization'
  },
  'market-research': {
    color: '#ff9800',
    icon: <FactCheckIcon />,
    name: 'Market Research'
  },
  'content-strategy': {
    color: '#9c27b0',
    icon: <CompareArrowsIcon />,
    name: 'Content Strategy'
  },
  'seo-keyword': {
    color: '#00bcd4',
    icon: <TipsAndUpdatesIcon />,
    name: 'SEO Keyword'
  }
};

// Helper function to get agent config with fallback
const getAgentConfig = (agentId: string) => {
  return agentConfig[agentId] || {
    color: '#757575',
    icon: <LightbulbOutlinedIcon />,
    name: 'Unknown Agent'
  };
};

// Helper function to format confidence as percentage
const formatConfidence = (confidence: number): string => {
  return `${Math.round(confidence * 100)}%`;
};

/**
 * Component to visualize agent reasoning processes
 */
const AgentReasoningVisualizer: React.FC<AgentReasoningVisualizerProps> = ({ iterations, agentId }) => {
  const [selectedTab, setSelectedTab] = useState(0);

  // Filter iterations by agent if specified
  const filteredIterations = agentId
    ? iterations.filter(iteration => iteration.agent === agentId)
    : iterations;

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
  };

  return (
    <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
      <Typography variant="h5" gutterBottom>
        Agent Reasoning Visualization
      </Typography>
      <Divider sx={{ mb: 2 }} />
      
      <Tabs value={selectedTab} onChange={handleTabChange} aria-label="agent reasoning tabs">
        <Tab label="Timeline View" />
        <Tab label="Detailed View" />
        <Tab label="Comparison View" />
      </Tabs>
      
      {/* Timeline View */}
      <Box hidden={selectedTab !== 0} sx={{ mt: 2 }}>
        <Timeline position="alternate">
          {filteredIterations.map((iteration, index) => {
            const agentConf = getAgentConfig(iteration.agent);
            return (
              <TimelineItem key={`timeline-${index}`}>
                <TimelineOppositeContent color="text.secondary">
                  {new Date(iteration.timestamp).toLocaleString()}
                </TimelineOppositeContent>
                <TimelineSeparator>
                  <TimelineDot sx={{ bgcolor: agentConf.color }}>
                    {agentConf.icon}
                  </TimelineDot>
                  {index < filteredIterations.length - 1 && <TimelineConnector />}
                </TimelineSeparator>
                <TimelineContent>
                  <Card>
                    <CardContent>
                      <Typography variant="subtitle1">
                        {agentConf.name} - v{iteration.version}
                      </Typography>
                      {iteration.reasoning && (
                        <Accordion>
                          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                            <Typography>Reasoning Process</Typography>
                          </AccordionSummary>
                          <AccordionDetails>
                            <Typography variant="body2" gutterBottom>
                              {iteration.reasoning.process}
                            </Typography>
                            
                            <Typography variant="subtitle2" sx={{ mt: 1 }}>Steps:</Typography>
                            <List dense>
                              {iteration.reasoning.steps.map((step: string, stepIdx: number) => (
                                <ListItem key={`step-${stepIdx}`}>
                                  <ListItemIcon sx={{ minWidth: 36 }}>
                                    <AssignmentTurnedInIcon fontSize="small" />
                                  </ListItemIcon>
                                  <ListItemText primary={step} />
                                </ListItem>
                              ))}
                            </List>
                            
                            {iteration.reasoning.thoughts && (
                              <>
                                <Typography variant="subtitle2" sx={{ mt: 1 }}>Thoughts:</Typography>
                                <List dense>
                                  {iteration.reasoning.thoughts.map((thought: string, thoughtIdx: number) => (
                                    <ListItem key={`thought-${thoughtIdx}`}>
                                      <ListItemIcon sx={{ minWidth: 36 }}>
                                        <LightbulbOutlinedIcon fontSize="small" />
                                      </ListItemIcon>
                                      <ListItemText primary={thought} />
                                    </ListItem>
                                  ))}
                                </List>
                              </>
                            )}
                            
                            {iteration.reasoning.considerations && (
                              <>
                                <Typography variant="subtitle2" sx={{ mt: 1 }}>Considerations:</Typography>
                                <List dense>
                                  {iteration.reasoning.considerations.map((consideration: string, consIdx: number) => (
                                    <ListItem key={`consideration-${consIdx}`}>
                                      <ListItemIcon sx={{ minWidth: 36 }}>
                                        <BalanceIcon fontSize="small" />
                                      </ListItemIcon>
                                      <ListItemText primary={consideration} />
                                    </ListItem>
                                  ))}
                                </List>
                              </>
                            )}
                            
                            {iteration.reasoning.decision && (
                              <Box sx={{ mt: 1 }}>
                                <Typography variant="subtitle2">Decision:</Typography>
                                <Typography variant="body2">{iteration.reasoning.decision}</Typography>
                              </Box>
                            )}
                            
                            {iteration.reasoning.confidence !== undefined && (
                              <Box sx={{ mt: 1, display: 'flex', alignItems: 'center' }}>
                                <Typography variant="subtitle2" sx={{ mr: 1 }}>Confidence:</Typography>
                                <Chip 
                                  label={formatConfidence(iteration.reasoning.confidence)} 
                                  color={iteration.reasoning.confidence > 0.7 ? "success" : 
                                         iteration.reasoning.confidence > 0.4 ? "warning" : "error"} 
                                  size="small" 
                                />
                              </Box>
                            )}
                          </AccordionDetails>
                        </Accordion>
                      )}
                      
                      {/* Display SEO data if available */}
                      {iteration.seoData && (
                        <Accordion>
                          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                            <Typography>SEO Analysis</Typography>
                          </AccordionSummary>
                          <AccordionDetails>
                            <Box sx={{ mb: 1 }}>
                              <Typography variant="subtitle2">SEO Score:</Typography>
                              <Chip 
                                label={`${iteration.seoData.score}/100`} 
                                color={iteration.seoData.score > 70 ? "success" : 
                                       iteration.seoData.score > 50 ? "warning" : "error"} 
                              />
                            </Box>
                            
                            <Typography variant="subtitle2">Recommendations:</Typography>
                            <List dense>
                              {iteration.seoData.recommendations.slice(0, 5).map((rec: any, recIdx: number) => (
                                <ListItem key={`rec-${recIdx}`}>
                                  <ListItemIcon sx={{ minWidth: 36 }}>
                                    <TipsAndUpdatesIcon fontSize="small" />
                                  </ListItemIcon>
                                  <ListItemText 
                                    primary={rec.recommendation || rec} 
                                    secondary={rec.priority ? `Priority: ${rec.priority}` : undefined}
                                  />
                                </ListItem>
                              ))}
                            </List>
                          </AccordionDetails>
                        </Accordion>
                      )}
                    </CardContent>
                  </Card>
                </TimelineContent>
              </TimelineItem>
            );
          })}
        </Timeline>
      </Box>
      
      {/* Detailed View */}
      <Box hidden={selectedTab !== 1} sx={{ mt: 2 }}>
        <Grid container spacing={2}>
          {filteredIterations.map((iteration, index) => {
            const agentConf = getAgentConfig(iteration.agent);
            return (
              <Grid item xs={12} md={6} key={`detailed-${index}`}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ bgcolor: agentConf.color, mr: 2 }}>
                        {agentConf.icon}
                      </Avatar>
                      <Box>
                        <Typography variant="h6">{agentConf.name}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          Version {iteration.version} • {new Date(iteration.timestamp).toLocaleString()}
                        </Typography>
                      </Box>
                    </Box>
                    
                    <Divider sx={{ mb: 2 }} />
                    
                    {iteration.reasoning && (
                      <>
                        <Typography variant="subtitle1" gutterBottom>Reasoning Process</Typography>
                        <Typography variant="body2" paragraph>{iteration.reasoning.process}</Typography>
                        
                        <Grid container spacing={2}>
                          <Grid item xs={12} md={6}>
                            <Typography variant="subtitle2" gutterBottom>Steps</Typography>
                            <List dense>
                              {iteration.reasoning.steps.map((step: string, stepIdx: number) => (
                                <ListItem key={`detailed-step-${stepIdx}`}>
                                  <ListItemIcon sx={{ minWidth: 36 }}>
                                    <AssignmentTurnedInIcon fontSize="small" />
                                  </ListItemIcon>
                                  <ListItemText primary={step} />
                                </ListItem>
                              ))}
                            </List>
                          </Grid>
                          
                          <Grid item xs={12} md={6}>
                            {iteration.reasoning.thoughts && (
                              <>
                                <Typography variant="subtitle2" gutterBottom>Thoughts</Typography>
                                <List dense>
                                  {iteration.reasoning.thoughts.map((thought: string, thoughtIdx: number) => (
                                    <ListItem key={`detailed-thought-${thoughtIdx}`}>
                                      <ListItemIcon sx={{ minWidth: 36 }}>
                                        <LightbulbOutlinedIcon fontSize="small" />
                                      </ListItemIcon>
                                      <ListItemText primary={thought} />
                                    </ListItem>
                                  ))}
                                </List>
                              </>
                            )}
                          </Grid>
                        </Grid>
                        
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="subtitle2" gutterBottom>Decision</Typography>
                          <Paper variant="outlined" sx={{ p: 1 }}>
                            <Typography variant="body2">{iteration.reasoning.decision}</Typography>
                          </Paper>
                        </Box>
                        
                        {iteration.reasoning.confidence !== undefined && (
                          <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
                            <Typography variant="subtitle2" sx={{ mr: 1 }}>Confidence:</Typography>
                            <Chip 
                              label={formatConfidence(iteration.reasoning.confidence)} 
                              color={iteration.reasoning.confidence > 0.7 ? "success" : 
                                     iteration.reasoning.confidence > 0.4 ? "warning" : "error"} 
                            />
                          </Box>
                        )}
                      </>
                    )}
                    
                    {/* Display SEO data if available */}
                    {iteration.seoData && (
                      <Box sx={{ mt: 3 }}>
                        <Typography variant="subtitle1" gutterBottom>SEO Analysis</Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Typography variant="subtitle2" sx={{ mr: 1 }}>Score:</Typography>
                          <Chip 
                            label={`${iteration.seoData.score}/100`} 
                            color={iteration.seoData.score > 70 ? "success" : 
                                   iteration.seoData.score > 50 ? "warning" : "error"} 
                          />
                        </Box>
                        
                        <Typography variant="subtitle2" gutterBottom>Top Recommendations</Typography>
                        <List dense>
                          {iteration.seoData.recommendations.map((rec: any, recIdx: number) => (
                            <ListItem key={`detailed-rec-${recIdx}`}>
                              <ListItemIcon sx={{ minWidth: 36 }}>
                                <TipsAndUpdatesIcon fontSize="small" color={
                                  rec.priority === 'high' ? 'error' :
                                  rec.priority === 'medium' ? 'warning' : 'info'
                                } />
                              </ListItemIcon>
                              <ListItemText 
                                primary={rec.recommendation || rec} 
                                secondary={
                                  <>
                                    {rec.priority && <span>Priority: {rec.priority}</span>}
                                    {rec.section && <span> • Section: {rec.section}</span>}
                                  </>
                                }
                              />
                            </ListItem>
                          ))}
                        </List>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      </Box>
      
      {/* Comparison View */}
      <Box hidden={selectedTab !== 2} sx={{ mt: 2 }}>
        {filteredIterations.length > 1 ? (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Evolution of Agent Reasoning
              </Typography>
              
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Timeline>
                  {filteredIterations.map((iteration, index) => {
                    const agentConf = getAgentConfig(iteration.agent);
                    const prevIteration = index > 0 ? filteredIterations[index - 1] : null;
                    
                    // Calculate changes between iterations
                    let changes = {
                      confidenceChange: prevIteration && 
                        iteration.reasoning?.confidence !== undefined && 
                        prevIteration.reasoning?.confidence !== undefined ?
                        iteration.reasoning.confidence - prevIteration.reasoning.confidence : 0,
                      seoScoreChange: prevIteration && 
                        iteration.seoData?.score !== undefined && 
                        prevIteration.seoData?.score !== undefined ?
                        iteration.seoData.score - prevIteration.seoData.score : 0
                    };
                    
                    return (
                      <TimelineItem key={`comparison-${index}`}>
                        <TimelineSeparator>
                          <TimelineDot sx={{ bgcolor: agentConf.color }}>
                            {agentConf.icon}
                          </TimelineDot>
                          {index < filteredIterations.length - 1 && <TimelineConnector />}
                        </TimelineSeparator>
                        <TimelineContent>
                          <Card>
                            <CardContent>
                              <Typography variant="subtitle1">
                                {agentConf.name} - v{iteration.version}
                              </Typography>
                              
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                                {iteration.reasoning?.confidence !== undefined && (
                                  <Tooltip title="Agent confidence in this iteration">
                                    <Chip 
                                      label={`Confidence: ${formatConfidence(iteration.reasoning.confidence)}`} 
                                      size="small"
                                      color={iteration.reasoning.confidence > 0.7 ? "success" : 
                                             iteration.reasoning.confidence > 0.4 ? "warning" : "error"}
                                    />
                                  </Tooltip>
                                )}
                                
                                {changes.confidenceChange !== 0 && (
                                  <Tooltip title="Change in confidence from previous iteration">
                                    <Chip 
                                      label={`Δ Confidence: ${changes.confidenceChange > 0 ? '+' : ''}${formatConfidence(changes.confidenceChange)}`} 
                                      size="small"
                                      color={changes.confidenceChange > 0 ? "success" : 
                                             changes.confidenceChange < 0 ? "error" : "default"}
                                    />
                                  </Tooltip>
                                )}
                                
                                {iteration.seoData?.score !== undefined && (
                                  <Tooltip title="SEO score for this iteration">
                                    <Chip 
                                      label={`SEO Score: ${iteration.seoData.score}/100`} 
                                      size="small"
                                      color={iteration.seoData.score > 70 ? "success" : 
                                             iteration.seoData.score > 50 ? "warning" : "error"}
                                    />
                                  </Tooltip>
                                )}
                                
                                {changes.seoScoreChange !== 0 && (
                                  <Tooltip title="Change in SEO score from previous iteration">
                                    <Chip 
                                      label={`Δ SEO: ${changes.seoScoreChange > 0 ? '+' : ''}${changes.seoScoreChange}`} 
                                      size="small"
                                      color={changes.seoScoreChange > 0 ? "success" : 
                                             changes.seoScoreChange < 0 ? "error" : "default"}
                                    />
                                  </Tooltip>
                                )}
                              </Box>
                              
                              {iteration.reasoning?.decision && (
                                <Box sx={{ mt: 2 }}>
                                  <Typography variant="subtitle2">Decision:</Typography>
                                  <Typography variant="body2">{iteration.reasoning.decision}</Typography>
                                </Box>
                              )}
                              
                              {index > 0 && (
                                <Box sx={{ mt: 2 }}>
                                  <Typography variant="subtitle2">Changes from previous version:</Typography>
                                  <Typography variant="body2">{iteration.changes || "No change description available"}</Typography>
                                </Box>
                              )}
                            </CardContent>
                          </Card>
                        </TimelineContent>
                      </TimelineItem>
                    );
                  })}
                </Timeline>
              </Paper>
            </Grid>
          </Grid>
        ) : (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body1" color="text.secondary">
              At least two iterations are needed for comparison view
            </Typography>
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default AgentReasoningVisualizer;
