// src/components/EnhancedCollaboration/ArtifactPanel.tsx
import React, { useState, useEffect, useMemo } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Tabs, 
  Tab, 
  List, 
  ListItem, 
  ListItemButton, 
  ListItemText, 
  Chip, 
  Button, 
  CircularProgress, 
  TextField, 
  Alert, 
  Divider, 
  Accordion, 
  AccordionSummary, 
  AccordionDetails 
} from '@mui/material';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

interface Artifact {
  id: string;
  name?: string;
  title?: string;
  type?: string;
  creator?: string;
  createdBy?: string;
  agent?: string;
  timestamp?: string;
  createdAt?: string;
  created?: string;
  date?: string;
  data?: any;
  content?: any;
  text?: string;
  reasoningSteps?: any[];
  iterations?: any[];
  status?: string;
  qualityScore?: number;
  metadata?: Record<string, any>;
  [key: string]: any;
}

interface ArtifactPanelProps {
  artifacts: Artifact[] | Record<string, Artifact> | any;
  refreshContent: () => void;
  loading: boolean;
}

// Helper function to format dates
const formatDate = (dateString?: string): string => {
  if (!dateString) return 'Unknown';
  try {
    const date = new Date(dateString);
    return date.toLocaleString();
  } catch (e) {
    return dateString || 'Unknown';
  }
};

const ArtifactPanel: React.FC<ArtifactPanelProps> = ({ 
  artifacts,
  refreshContent,
  loading
}): React.ReactElement => {
  const [selectedArtifact, setSelectedArtifact] = useState<Artifact | null>(null);
  const [artifactContent, setArtifactContent] = useState<string>('');
  const [contentSource, setContentSource] = useState<string>('');
  const [expandedAgent, setExpandedAgent] = useState<string | false>('market-research');
  const [directArtifacts, setDirectArtifacts] = useState<Artifact[]>([]);
  const [loadingDirectArtifacts, setLoadingDirectArtifacts] = useState<boolean>(false);
  const [directArtifactsError, setDirectArtifactsError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<number>(0);

  // Handle accordion expansion
  const handleAccordionChange = (agentId: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedAgent(isExpanded ? agentId : false);
  };
  
  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };
  
  // Define normalization function using useMemo to avoid recreation on each render
  const normalizeArtifacts = useMemo(() => {
    return (artifactsInput: any): Artifact[] => {
      if (!artifactsInput) {
        console.log('No artifacts to normalize');
        return [];
      }
      
      console.log('Normalizing artifacts in ArtifactPanel:', typeof artifactsInput);
      let normalizedArtifacts: Artifact[] = [];
      
      // Case 1: Artifacts is the full state object with an artifacts property
      if (artifactsInput && typeof artifactsInput === 'object' && 'artifacts' in artifactsInput) {
        const artifactsData = artifactsInput.artifacts;
        
        // Handle object with IDs as keys
        if (artifactsData && typeof artifactsData === 'object' && !Array.isArray(artifactsData)) {
          normalizedArtifacts = Object.entries(artifactsData).map(([id, data]) => {
            if (!data || typeof data !== 'object') return { id } as Artifact;
            
            const artifactData = data as Record<string, any>;
            return {
              id: artifactData.id || id,
              name: artifactData.name || artifactData.title || 'Unnamed',
              type: artifactData.type || 'unknown',
              createdBy: artifactData.createdBy || artifactData.creator || artifactData.agent || 'Unknown',
              createdAt: artifactData.createdAt || artifactData.timestamp || artifactData.created || artifactData.date || new Date().toISOString(),
              content: artifactData.content || artifactData.text || artifactData.data || '',
              ...artifactData // Include all other properties
            } as Artifact;
          });
        } 
        // Handle array
        else if (Array.isArray(artifactsData)) {
          normalizedArtifacts = artifactsData;
        }
      }
      // Case 2: Artifacts is already an object with IDs as keys (direct Redis structure)
      else if (artifactsInput && typeof artifactsInput === 'object' && !Array.isArray(artifactsInput) && Object.keys(artifactsInput).length > 0) {
        // Check if the structure seems like artifact entries with IDs as keys
        const hasArtifactProperties = Object.values(artifactsInput).some(v => 
          v !== null && typeof v === 'object' && 
          ((v as any).type !== undefined || (v as any).createdBy !== undefined || (v as any).iterations !== undefined)
        );
        
        if (hasArtifactProperties) {
          normalizedArtifacts = Object.entries(artifactsInput).map(([id, data]) => {
            if (!data || typeof data !== 'object') return { id } as Artifact;
            
            const artifactData = data as Record<string, any>;
            return {
              id: artifactData.id || id,
              name: artifactData.name || artifactData.title || 'Unnamed',
              type: artifactData.type || 'unknown',
              createdBy: artifactData.createdBy || artifactData.creator || artifactData.agent || 'Unknown',
              createdAt: artifactData.createdAt || artifactData.timestamp || artifactData.created || artifactData.date || new Date().toISOString(),
              content: artifactData.content || artifactData.text || artifactData.data || '',
              ...artifactData
            } as Artifact;
          });
        }
      }
      // Case 3: Artifacts is already an array
      else if (Array.isArray(artifactsInput)) {
        normalizedArtifacts = artifactsInput;
      }
      
      console.log(`Normalized ${normalizedArtifacts.length} artifacts`);
      return normalizedArtifacts;
    };
  }, []);
  
  // Apply normalization to artifacts whenever they change
  useEffect(() => {
    const normalizedData = normalizeArtifacts(artifacts);
    setDirectArtifacts(normalizedData);
  }, [artifacts, normalizeArtifacts]);

  // Group artifacts by agent
  const artifactsByAgent = useMemo(() => {
    const groupedArtifacts: Record<string, Artifact[]> = {};
    
    directArtifacts.forEach(artifact => {
      const agent = artifact.createdBy || artifact.agent || artifact.creator || 'unknown';
      if (!groupedArtifacts[agent]) {
        groupedArtifacts[agent] = [];
      }
      groupedArtifacts[agent].push(artifact);
    });
    
    return groupedArtifacts;
  }, [directArtifacts]);

  // Group artifacts by type
  const artifactsByType = useMemo(() => {
    const groupedArtifacts: Record<string, Artifact[]> = {};
    
    directArtifacts.forEach(artifact => {
      const type = artifact.type || 'unknown';
      if (!groupedArtifacts[type]) {
        groupedArtifacts[type] = [];
      }
      groupedArtifacts[type].push(artifact);
    });
    
    return groupedArtifacts;
  }, [directArtifacts]);

  // Handle artifact selection
  const handleArtifactSelect = (artifact: Artifact) => {
    setSelectedArtifact(artifact);
    
    // Set content to display based on artifact type and structure
    let content = '';
    let source = '';
    
    if (artifact.content) {
      source = 'content';
      if (typeof artifact.content === 'string') {
        content = artifact.content;
      } else if (typeof artifact.content === 'object') {
        try {
          content = JSON.stringify(artifact.content, null, 2);
        } catch (e) {
          content = 'Unable to display content';
        }
      }
    } else if (artifact.text) {
      source = 'text';
      content = artifact.text as string;
    } else if (artifact.data) {
      source = 'data';
      try {
        content = typeof artifact.data === 'string' ? artifact.data : JSON.stringify(artifact.data, null, 2);
      } catch (e) {
        content = 'Unable to display data';
      }
    } else if (artifact.iterations && artifact.iterations.length > 0) {
      source = 'iterations';
      const latestIteration = artifact.iterations[artifact.iterations.length - 1];
      
      if (latestIteration.content) {
        try {
          content = typeof latestIteration.content === 'string' 
            ? latestIteration.content 
            : JSON.stringify(latestIteration.content, null, 2);
        } catch (e) {
          content = 'Unable to display iteration content';
        }
      } else {
        content = 'No content in latest iteration';
      }
    } else {
      content = 'No content available for this artifact';
    }
    
    setArtifactContent(content);
    setContentSource(source);
  };

  // Clear artifact selection
  const handleClearSelection = () => {
    setSelectedArtifact(null);
    setArtifactContent('');
    setContentSource('');
  };

  // Function to determine the appropriate display format for content
  const renderContent = (content: string) => {
    // Try to detect if the content is JSON
    try {
      const trimmed = content.trim();
      if ((trimmed.startsWith('{') && trimmed.endsWith('}')) || 
          (trimmed.startsWith('[') && trimmed.endsWith(']'))) {
        // Parse and re-stringify to format JSON properly
        const parsedJson = JSON.parse(trimmed);
        return (
          <Box component="pre" sx={{ 
            margin: 0, 
            whiteSpace: 'pre-wrap', 
            overflow: 'auto', 
            backgroundColor: '#f5f5f5',
            padding: 2,
            borderRadius: 1
          }}>
            {JSON.stringify(parsedJson, null, 2)}
          </Box>
        );
      }
    } catch (e) {
      // Not JSON or invalid JSON, continue to other formats
    }
    
    // Check if content looks like markdown
    if (content.includes('#') || content.includes('*') || content.includes('```') || 
        content.includes('---') || content.includes('| ---') || content.includes('[')) {
      return (
        <Box sx={{ px: 2 }}>
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {content}
          </ReactMarkdown>
        </Box>
      );
    }
    
    // Default to plain text
    return (
      <Typography sx={{ whiteSpace: 'pre-wrap', p: 2 }}>
        {content}
      </Typography>
    );
  };

  // Get iteration details if available
  const getIterationDetails = (artifact: Artifact) => {
    if (!artifact.iterations || artifact.iterations.length === 0) {
      return null;
    }
    
    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle1">Iterations</Typography>
        <List dense>
          {artifact.iterations.map((iteration: any, index: number) => (
            <ListItem key={index} disablePadding>
              <ListItemButton onClick={() => {
                const content = typeof iteration.content === 'string' 
                  ? iteration.content 
                  : JSON.stringify(iteration.content, null, 2);
                setArtifactContent(content);
                setContentSource(`iteration-${index}`);
              }}>
                <ListItemText 
                  primary={`Version ${iteration.version || index + 1}`} 
                  secondary={`${formatDate(iteration.timestamp)} by ${iteration.agent || 'Unknown'}`} 
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>
    );
  };

  // Get reasoning details if available
  const getReasoningDetails = (artifact: Artifact) => {
    const reasoning = artifact.reasoning || 
                     (artifact.metadata && artifact.metadata.reasoning) ||
                     (artifact.iterations && artifact.iterations.length > 0 && artifact.iterations[0].reasoning);
                     
    if (!reasoning) {
      return null;
    }
    
    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle1">Reasoning</Typography>
        <Paper variant="outlined" sx={{ p: 2, mt: 1 }}>
          {reasoning.process && (
            <Typography variant="subtitle2" gutterBottom>
              Process: {reasoning.process}
            </Typography>
          )}
          
          {reasoning.confidence && (
            <Typography variant="body2" gutterBottom>
              Confidence: {(reasoning.confidence * 100).toFixed(0)}%
            </Typography>
          )}
          
          {reasoning.steps && reasoning.steps.length > 0 && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="subtitle2" gutterBottom>Steps:</Typography>
              <List dense>
                {reasoning.steps.map((step: string, index: number) => (
                  <ListItem key={index}>
                    <ListItemText primary={`${index + 1}. ${step}`} />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
          
          {reasoning.conclusion && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="subtitle2" gutterBottom>Conclusion:</Typography>
              <Typography variant="body2">{reasoning.conclusion}</Typography>
            </Box>
          )}
        </Paper>
      </Box>
    );
  useEffect(() => {
    const fetchDirectArtifacts = async () => {
      // Extract session ID from artifacts prop if it's the full state object
      let sessionId = null;
      if (artifacts && typeof artifacts === 'object' && 'id' in artifacts) {
        sessionId = artifacts.id;
      }
      
      if (!sessionId) {
        console.log('ArtifactPanel: Cannot fetch direct artifacts - no session ID found');
        return;
      }
      
      try {
        setLoadingDirectArtifacts(true);
        setDirectArtifactsError(null);
        
        console.log(`ArtifactPanel: Fetching artifacts directly for session ${sessionId}`);
        const response = await fetch(`/api/collaborative-agents/artifacts?sessionId=${sessionId}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch artifacts: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('ArtifactPanel: Direct artifacts response:', data);
        
        if (data.artifacts && Array.isArray(data.artifacts)) {
          setDirectArtifacts(data.artifacts);
          console.log(`ArtifactPanel: Successfully fetched ${data.artifacts.length} artifacts directly`);
        } else {
          console.log('ArtifactPanel: No artifacts returned from direct endpoint');
          setDirectArtifacts([]);
        }
      } catch (error) {
        console.error('Error fetching artifacts directly:', error);
        setDirectArtifactsError(error instanceof Error ? error.message : 'Failed to fetch artifacts');
      } finally {
        setLoadingDirectArtifacts(false);
      }
    };
    
    // Only fetch if we have artifacts (which might contain the session ID)
    if (artifacts && !loading) {
      fetchDirectArtifacts();
    }
  }, [artifacts, loading]);

  const normalizeArtifacts = useMemo(() => {
    return (artifactsInput: any): Artifact[] => {
      if (!artifactsInput) return [];
      
      let normalizedArtifacts: Artifact[] = [];
      
      // Case 1: Full state object with artifacts property
      if (artifactsInput && typeof artifactsInput === 'object' && 'artifacts' in artifactsInput) {
        const artifactsData = artifactsInput.artifacts;
        
        // Handle object with IDs as keys
        if (artifactsData && typeof artifactsData === 'object' && !Array.isArray(artifactsData)) {
          normalizedArtifacts = Object.entries(artifactsData).map(([id, data]) => {
            if (!data || typeof data !== 'object') return { id } as Artifact;
            
            const artifactData = data as Record<string, any>;
            return {
              id: artifactData.id || id,
              name: artifactData.name || artifactData.title || 'Unnamed',
              type: artifactData.type || 'unknown',
              createdBy: artifactData.createdBy || artifactData.creator || artifactData.agent || 'Unknown',
              createdAt: artifactData.createdAt || artifactData.timestamp || artifactData.created || artifactData.date || new Date().toISOString(),
              content: artifactData.content || artifactData.text || artifactData.data || '',
              ...artifactData // Include all other properties
            } as Artifact;
          });
        } 
        // Handle array
        else if (Array.isArray(artifactsData)) {
          normalizedArtifacts = artifactsData;
        }
      }
      // Case 2: Direct object with IDs as keys
      else if (artifactsInput && typeof artifactsInput === 'object' && !Array.isArray(artifactsInput)) {
        const hasArtifactProperties = Object.values(artifactsInput).some(v => 
          v !== null && typeof v === 'object' && 
          ((v as any).type !== undefined || (v as any).createdBy !== undefined || (v as any).iterations !== undefined)
        );
        
        if (hasArtifactProperties) {
          normalizedArtifacts = Object.entries(artifactsInput).map(([id, data]) => {
            if (!data || typeof data !== 'object') return { id } as Artifact;
            
            const artifactData = data as Record<string, any>;
            return {
              id: artifactData.id || id,
              name: artifactData.name || artifactData.title || 'Unnamed',
              type: artifactData.type || 'unknown',
              createdBy: artifactData.createdBy || artifactData.creator || artifactData.agent || 'Unknown',
              createdAt: artifactData.createdAt || artifactData.timestamp || artifactData.created || artifactData.date || new Date().toISOString(),
              content: artifactData.content || artifactData.text || artifactData.data || '',
              ...artifactData
            } as Artifact;
          });
        }
      }
      // Case 3: Already an array
      else if (Array.isArray(artifactsInput)) {
        normalizedArtifacts = artifactsInput;
      }
      
      return normalizedArtifacts;
    };
  }, []);
  useEffect(() => {
    const normalizedData = normalizeArtifacts(artifacts);
    setDirectArtifacts(normalizedData);
  }, [artifacts, normalizeArtifacts]);
  // Get the agent ID from various possible properties
  const getAgentId = (artifact: Artifact): string => {
    if (!artifact) return 'unknown';
    
    const possibleProps = ['agent', 'createdBy', 'creator'];
    for (const prop of possibleProps) {
      if (artifact[prop] && typeof artifact[prop] === 'string') {
        return artifact[prop] as string;
      }
    }
    
    // If we can't find a proper agent ID, try to extract from type or content
    if (artifact.type) {
      if (artifact.type.includes('market-research')) return 'market-research';
      if (artifact.type.includes('seo-keyword')) return 'seo-keyword';
      if (artifact.type.includes('content-strategy')) return 'content-strategy';
      if (artifact.type.includes('content-generation')) return 'content-generation';
      if (artifact.type.includes('seo-optimization')) return 'seo-optimization';
    }
    
    return 'unknown';
  };
  
  // Get a display name for any agent ID
  const getAgentDisplayName = (agentId: string): string => {
    const displayNames: {[key: string]: string} = {
      'market-research': 'Market Research',
      'seo-keyword': 'SEO Keywords',
      'content-strategy': 'Content Strategy',
      'content-generation': 'Content Generation',
      'seo-optimization': 'SEO Optimization',
      'editorial-review': 'Editorial Review',
      'user': 'User'
    };
    
    return displayNames[agentId] || 
           agentId.charAt(0).toUpperCase() + 
           agentId.slice(1).replace(/-/g, ' ');
  };
  
  // Get the agent name for an artifact
  const getAgentName = (artifact: Artifact): string => {
    if (!artifact) return 'Unknown Agent';
    
    // Extract agent ID from the artifact
    const agentId = getAgentId(artifact);
    
    // Convert to display name
    return getAgentDisplayName(agentId);
  };
  
  // Get the color for an artifact based on its agent
  const getArtifactColor = (artifact: Artifact): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    if (!artifact) return 'default';
    
    const agentId = getAgentId(artifact);
    
    const colorMap: {[key: string]: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'} = {
      'market-research': 'primary',
      'seo-keyword': 'secondary',
      'content-strategy': 'info',
      'content-generation': 'success',
      'seo-optimization': 'warning',
      'editorial-review': 'error',
      'user': 'default'
    };
    
    return colorMap[agentId] || 'default';
  };
  
  // Function to extract readable content from an artifact
  const extractContent = (artifact: Artifact): string => {
    if (!artifact) return 'No content available';
    
    // Handle special artifact types
    if (artifact.type === 'market-research') {
      const result = formatMarketResearchContent(artifact);
      setContentSource(result.source);
      return result.content;
    }
    
    // General content extraction logic
    const possibleContentProps = ['content', 'text', 'data'];
    
    for (const prop of possibleContentProps) {
      if (artifact[prop]) {
        const content = artifact[prop];
        
        // If content is a string, use it directly
        if (typeof content === 'string') {
          setContentSource(`${prop} (text)`);
          return content;
        }
        
        // If content is an object, try to extract meaningful content
        if (typeof content === 'object' && content !== null) {
          // If it has a text or content property, use that
          if (content.text && typeof content.text === 'string') {
            setContentSource(`${prop}.text`);
            return content.text;
          }
          
          if (content.content && typeof content.content === 'string') {
            setContentSource(`${prop}.content`);
            return content.content;
          }
          
          // Otherwise stringify the object
          setContentSource(`${prop} (JSON)`);
          return JSON.stringify(content, null, 2);
        }
      }
    }
    
    // If we couldn't find content, stringify the whole artifact
    setContentSource('full artifact (JSON)');
    return JSON.stringify(artifact, null, 2);
  };

  // Select an artifact to view
  const handleSelectArtifact = (artifact: Artifact) => {
    setSelectedArtifact(artifact);
    setArtifactContent(extractContent(artifact));
  };
  
  // Get a human-readable timestamp for an artifact
  const getArtifactTimestamp = (artifact: Artifact): string => {
    if (!artifact) return 'Unknown date';
    
    const dateProps = ['createdAt', 'created', 'timestamp', 'date'];
    for (const prop of dateProps) {
      if (artifact[prop] && typeof artifact[prop] === 'string') {
        try {
          const date = new Date(artifact[prop] as string);
          return date.toLocaleString();
        } catch (e) {
          return artifact[prop] as string;
        }
      }
    }
    
    return 'No date available';
  };

  // Format the artifact name
  const getArtifactLabel = (artifact: Artifact): string => {
    if (!artifact) return 'Untitled Artifact';
    
    return artifact.name || artifact.title || 
           (artifact.type ? `${artifact.type.charAt(0).toUpperCase() + artifact.type.slice(1)} Artifact` : 'Untitled Artifact');
  };

  // Group artifacts by agent
  const groupArtifactsByAgent = (): {[key: string]: Artifact[]} => {
    const normalized = safeArtifacts;
    const result: {[key: string]: Artifact[]} = {};
    
    console.log(`ArtifactPanel: Grouping ${normalized.length} artifacts by agent`);
    
    // Process each artifact
    normalized.forEach(artifact => {
      const agentId = getAgentId(artifact);
      
      // Initialize array for this agent if needed
      if (!result[agentId]) {
        result[agentId] = [];
      }
      
      // Check if we already have this artifact (by ID) in this agent group
      const existingIdx = result[agentId].findIndex(a => a.id === artifact.id);
      
      if (existingIdx >= 0) {
        // Update existing artifact with any new information
        result[agentId][existingIdx] = { ...result[agentId][existingIdx], ...artifact };
      } else {
        // Add new artifact
        result[agentId].push(artifact);
      }
    });
    
    // Sort artifacts within each group - newest first
    Object.keys(result).forEach(agentId => {
      result[agentId].sort((a, b) => {
        // Try to extract dates for comparison
        const dateA = a.createdAt || a.created || a.timestamp || a.date;
        const dateB = b.createdAt || b.created || b.timestamp || b.date;
        
        if (dateA && dateB) {
          // If both have dates, compare them (newest first)
          const timeA = new Date(dateA).getTime();
          const timeB = new Date(dateB).getTime();
          return timeB - timeA; // Newest first
        }
        
        // If one has a date and the other doesn't, the one with a date comes first
        if (dateA) return -1;
        if (dateB) return 1;
        
        // If neither has a date, compare by name
        const nameA = a.name || a.title || '';
        const nameB = b.name || b.title || '';
        return nameA.localeCompare(nameB);
      });
    });
    
    // Log the grouping results
    console.log('ArtifactPanel: Grouped artifacts by agent:', 
                Object.keys(result).map(agentId => `${agentId}: ${result[agentId].length}`));
    
    return result;
  };

  // Render the type badge for an artifact
  const renderArtifactTypeBadge = (artifact: Artifact) => {
    if (!artifact || !artifact.type) return null;
    
    const typeString = artifact.type.toLowerCase();
    let label = artifact.type;
    let color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' = 'default';
    
    // Determine color and label based on artifact type
    if (typeString.includes('market') || typeString.includes('research')) {
      label = 'Research';
      color = 'primary';
    } else if (typeString.includes('seo') && typeString.includes('keyword')) {
      label = 'Keywords';
      color = 'secondary';
    } else if (typeString.includes('strategy')) {
      label = 'Strategy';
      color = 'info';
    } else if (typeString.includes('content') && 
              (typeString.includes('generation') || typeString.includes('article') || 
               typeString.includes('blog') || typeString.includes('product'))) {
      label = typeString.includes('blog') ? 'Blog' : 
              typeString.includes('article') ? 'Article' : 
              typeString.includes('product') ? 'Product' : 'Content';
      color = 'success';
    } else if (typeString.includes('seo') && typeString.includes('optimization')) {
      label = 'SEO';
      color = 'warning';
    } else if (typeString.includes('review') || typeString.includes('editorial')) {
      label = 'Review';
      color = 'error';
    }
    
    return (
      <Chip 
        size="small" 
        color={color} 
        label={label.charAt(0).toUpperCase() + label.slice(1)} 
      />
    );
  };

  // Special formatting for market research artifacts
  const formatMarketResearchContent = (artifact: Artifact): { content: string, source: string } => {
    if (!artifact) {
      return { content: '', source: 'empty' };
    }
    
    // Extract the data property which should contain the market research data
    const data = artifact.data || artifact.content || {};
    
    // If it's a string, try to parse as JSON
    let parsedData: any = data;
    if (typeof data === 'string') {
      try {
        parsedData = JSON.parse(data);
      } catch (e) {
        // If not JSON, return as markdown
        return {
          content: data,
          source: 'text content'
        };
      }
    }
    
    // Format the research data into markdown
    let formattedContent = `# Market Research: ${artifact.name || 'Untitled'}

`;
    
    // Format the research sections
    if (parsedData.targetAudience) {
      formattedContent += `## Target Audience

${parsedData.targetAudience}

`;
    }
    
    if (parsedData.competitorAnalysis) {
      formattedContent += `## Competitor Analysis

${parsedData.competitorAnalysis}

`;
    }
    
    if (parsedData.marketTrends) {
      formattedContent += `## Market Trends

${parsedData.marketTrends}

`;
    }
    
    // Handle other common sections
    const knownSections = ['targetAudience', 'competitorAnalysis', 'marketTrends'];
    Object.entries(parsedData).forEach(([key, value]) => {
      if (!knownSections.includes(key) && value && typeof value === 'string') {
        formattedContent += `### ${key}

${value}

`;
      }
    });
    
    // If we couldn't extract any meaningful data, fallback to the raw JSON
    if (formattedContent === `# Market Research: ${artifact.name || 'Untitled'}

`) {
      return {
        content: JSON.stringify(data, null, 2),
        source: 'raw data (JSON)'
      };
    }
    
    return {
      content: formattedContent,
      source: 'formatted market research data'
    };
  };
  
  const safeArtifacts = normalizeArtifacts(artifacts);
  
  // Group artifacts by agent
  const groupedArtifacts = groupArtifactsByAgent();

  // Sort agent IDs for consistent display
  const sortedAgentIds = Object.keys(groupedArtifacts).sort();
  
  return (
    <div className="artifact-panel">
      <h2 className="panel-title">Artifacts</h2>
      
      <div className="panel-content">
        <div className="artifact-list">
          {loading ? (
            <div className="loading-container">
              <CircularProgress size={40} />
              <p>Loading artifacts...</p>
            </div>
          ) : sortedAgentIds.length > 0 ? (
            <div className="artifacts-by-agent">
              {sortedAgentIds.map(agentId => (
                <Accordion 
                  key={agentId}
                  expanded={expandedAgent === agentId}
                  onChange={handleAccordionChange(agentId)}
                  className="agent-group"
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    className="agent-header"
                  >
                    <Box sx={{ display: 'flex', gap: 1, mb: 1, flexWrap: 'wrap' }}>
                      {agentId === 'market-research' && <Chip size="small" color="primary" label="Research" />}
                      {agentId === 'seo-keyword' && <Chip size="small" color="secondary" label="SEO Keywords" />}
                      {agentId === 'content-strategy' && <Chip size="small" color="info" label="Content Strategy" />}
                      {agentId === 'content-generation' && <Chip size="small" color="success" label="Content Generation" />}
                      {agentId === 'seo-optimization' && <Chip size="small" color="warning" label="SEO Optimization" />}
                      {agentId === 'editorial-review' && <Chip size="small" color="error" label="Editorial Review" />}
                      {agentId === 'user' && <Chip size="small" color="default" label="User" />}
                      {!['market-research', 'seo-keyword', 'content-strategy', 'content-generation', 'seo-optimization', 'editorial-review', 'user'].includes(agentId) && 
                        <Chip size="small" color="default" label={getAgentDisplayName(agentId)} />}
                    </Box>
                    <div className="agent-info">
                      <strong>{getAgentName({ agent: agentId })}</strong>
                      <span className="artifact-count">
                        {groupedArtifacts[agentId].length} artifact{groupedArtifacts[agentId].length !== 1 ? 's' : ''}
                      </span>
                    </div>
                  </AccordionSummary>
                  <AccordionDetails className="agent-artifacts">
                    <div className="artifact-items">
                      {groupedArtifacts[agentId].map((artifact, index) => (
                        <div 
                          key={artifact.id || index} 
                          className={`artifact-item ${selectedArtifact === artifact ? 'selected' : ''}`}
                          onClick={() => handleSelectArtifact(artifact)}
                        >
                          <div className="artifact-header">
                            <div className="artifact-title">
                              {getArtifactLabel(artifact)}
                            </div>
                            {renderArtifactTypeBadge(artifact)}
                          </div>
                          <div className="artifact-metadata">
                            <span className="artifact-timestamp">
                              {getArtifactTimestamp(artifact)}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </AccordionDetails>
                </Accordion>
              ))}
            </div>
          ) : (
            <div className="no-artifacts">
              <p>No artifacts available</p>
              {loadingDirectArtifacts ? (
                <div className="loading-direct-artifacts">
                  <CircularProgress size={24} />
                  <p>Fetching artifacts directly...</p>
                </div>
              ) : directArtifactsError ? (
                <div className="direct-artifacts-error">
                  <p>Error: {directArtifactsError}</p>
                  <Button onClick={refreshContent} variant="outlined" size="small">
                    Retry
                  </Button>
                </div>
              ) : (
                <Button onClick={refreshContent} variant="outlined" size="small">
                  Refresh
                </Button>
              )}
            </div>
          )}
        </div>
        
        <div className="artifact-content">
          {selectedArtifact ? (
            <div className="content-container">
              <div className="content-header">
                <h3>{getArtifactLabel(selectedArtifact)}</h3>
                <div className="content-metadata">
                  <span className="content-agent">{getAgentName(selectedArtifact)}</span>
                  <span className="content-timestamp">{getArtifactTimestamp(selectedArtifact)}</span>
                  {contentSource && <span className="content-source">Source: {contentSource}</span>}
                </div>
              </div>
              <div className="content-body">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {artifactContent}
                </ReactMarkdown>
              </div>
            </div>
          ) : (
            <div className="no-selection">
              <p>Select an artifact to view its content</p>
            </div>
          )}
        </div>
      </div>

      <style jsx>{`
        .artifact-panel {
          display: flex;
          flex-direction: column;
          height: 100%;
          width: 100%;
          padding: 1rem;
          box-sizing: border-box;
        }
        
        .panel-title {
          margin-top: 0;
          margin-bottom: 1rem;
          font-size: 1.5rem;
          color: #333;
        }
        
        .panel-content {
          display: flex;
          flex: 1;
          gap: 1rem;
          height: calc(100% - 4rem);
        }
        
        .artifact-list {
          flex: 0 0 40%;
          overflow-y: auto;
          border: 1px solid #e0e0e0;
          border-radius: 4px;
          background-color: #f9f9f9;
          max-width: 40%;
        }
        
        .artifacts-by-agent {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }
        
        .agent-group {
          margin-bottom: 0.5rem !important;
          box-shadow: none !important;
          border: 1px solid #e0e0e0 !important;
        }
        
        .agent-header {
          min-height: 48px;
        }
        
        .agent-info {
          display: flex;
          flex-direction: column;
        }
        
        .artifact-count {
          font-size: 0.8rem;
          color: #666;
          margin-top: 0.1rem;
        }
        
        .agent-artifacts {
          padding: 0 !important;
        }
        
        .artifact-items {
          display: flex;
          flex-direction: column;
          width: 100%;
        }
        
        .artifact-item {
          padding: 0.75rem;
          border-bottom: 1px solid #e0e0e0;
          cursor: pointer;
          transition: background-color 0.2s;
        }
        
        .artifact-item:hover {
          background-color: #f0f0f0;
        }
        
        .artifact-item.selected {
          background-color: #e3f2fd;
          border-left: 3px solid #2196f3;
        }
        
        .artifact-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 0.5rem;
        }
        
        .artifact-title {
          font-weight: 500;
          flex: 1;
        }
        
        .artifact-metadata {
          display: flex;
          gap: 1rem;
          font-size: 0.8rem;
          color: #666;
        }
        
        .artifact-timestamp {
          white-space: nowrap;
        }
        
        .artifact-content {
          flex: 1;
          overflow-y: auto;
          border: 1px solid #e0e0e0;
          border-radius: 4px;
          padding: 1rem;
          background-color: white;
        }
        
        .no-selection {
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #666;
        }
        
        .no-artifacts {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          color: #666;
          gap: 1rem;
        }
        
        .loading-container {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          color: #666;
          gap: 1rem;
        }
        
        .content-container {
          height: 100%;
          display: flex;
          flex-direction: column;
        }
        
        .content-header {
          margin-bottom: 1rem;
          padding-bottom: 1rem;
          border-bottom: 1px solid #e0e0e0;
        }
        
        .content-header h3 {
          margin-top: 0;
          margin-bottom: 0.5rem;
        }
        
        .content-metadata {
          display: flex;
          flex-wrap: wrap;
          gap: 1rem;
          font-size: 0.9rem;
          color: #666;
        }
        
        .content-agent {
          font-weight: 500;
        }
        
        .content-timestamp {
          white-space: nowrap;
        }
        
        .content-source {
          font-style: italic;
        }
        
        .content-body {
          flex: 1;
          overflow-y: auto;
          padding-right: 0.5rem;
        }
        
        /* New styles for direct artifacts */
        .loading-direct-artifacts {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.5rem;
          padding: 1rem;
          text-align: center;
        }
        
        .direct-artifacts-error {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.5rem;
          color: #d32f2f;
          text-align: center;
        }
      `}</style>
    </div>
  );
};

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab label="By Agent" />
          <Tab label="By Type" />
        </Tabs>
      </Box>
      
      {/* Loading indicator */}
      {(loading || loadingDirectArtifacts) && (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      )}
      
      {/* Error message */}
      {directArtifactsError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {directArtifactsError}
        </Alert>
      )}
      
      {/* Empty state */}
      {!loading && !loadingDirectArtifacts && directArtifacts.length === 0 && (
        <Box sx={{ textAlign: 'center', p: 4 }}>
          <Typography variant="body1" color="text.secondary">
            No artifacts available
          </Typography>
          <Button onClick={refreshContent} sx={{ mt: 2 }}>
            Refresh
          </Button>
        </Box>
      )}
      
      {/* Content panel */}
      <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
        {/* Artifact list panel */}
        <Box sx={{ width: { xs: '100%', md: '35%' }, height: selectedArtifact ? '700px' : 'auto', overflow: 'auto' }}>
          {/* Tab 1: Grouped by Agent */}
          <Box sx={{ display: activeTab === 0 ? 'block' : 'none' }}>
            {Object.entries(artifactsByAgent).map(([agent, agentArtifacts]) => (
              <Accordion 
                key={agent} 
                expanded={expandedAgent === agent} 
                onChange={handleAccordionChange(agent)}
                sx={{ mb: 1 }}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography>
                    {agent} ({agentArtifacts.length})
                  </Typography>
                </AccordionSummary>
                <AccordionDetails sx={{ p: 0 }}>
                  <List dense disablePadding>
                    {agentArtifacts.map((artifact) => (
                      <ListItem 
                        key={artifact.id} 
                        disablePadding
                        secondaryAction={
                          <Chip 
                            size="small" 
                            label={artifact.status || 'draft'} 
                            color={
                              artifact.status === 'completed' ? 'success' : 
                              artifact.status === 'in-progress' ? 'info' : 
                              'default'
                            }
                          />
                        }
                      >
                        <ListItemButton 
                          selected={selectedArtifact?.id === artifact.id}
                          onClick={() => handleArtifactSelect(artifact)}
                        >
                          <ListItemText 
                            primary={artifact.name || artifact.title || 'Unnamed'}
                            secondary={`${artifact.type || 'Unknown Type'} - ${formatDate(artifact.createdAt)}`}
                          />
                        </ListItemButton>
                      </ListItem>
                    ))}
                  </List>
                </AccordionDetails>
              </Accordion>
            ))}
          </Box>
          
          {/* Tab 2: Grouped by Type */}
          <Box sx={{ display: activeTab === 1 ? 'block' : 'none' }}>
            {Object.entries(artifactsByType).map(([type, typeArtifacts]) => (
              <Accordion 
                key={type} 
                defaultExpanded={true}
                sx={{ mb: 1 }}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography>
                    {type} ({typeArtifacts.length})
                  </Typography>
                </AccordionSummary>
                <AccordionDetails sx={{ p: 0 }}>
                  <List dense disablePadding>
                    {typeArtifacts.map((artifact) => (
                      <ListItem 
                        key={artifact.id} 
                        disablePadding
                        secondaryAction={
                          <Chip 
                            size="small" 
                            label={artifact.status || 'draft'} 
                            color={
                              artifact.status === 'completed' ? 'success' : 
                              artifact.status === 'in-progress' ? 'info' : 
                              'default'
                            }
                          />
                        }
                      >
                        <ListItemButton 
                          selected={selectedArtifact?.id === artifact.id}
                          onClick={() => handleArtifactSelect(artifact)}
                        >
                          <ListItemText 
                            primary={artifact.name || artifact.title || 'Unnamed'}
                            secondary={`${artifact.createdBy || artifact.agent || 'Unknown'} - ${formatDate(artifact.createdAt)}`}
                          />
                        </ListItemButton>
                      </ListItem>
                    ))}
                  </List>
                </AccordionDetails>
              </Accordion>
            ))}
          </Box>
        </Box>
        
        {/* Artifact detail panel */}
        <Box sx={{ 
          width: { xs: '100%', md: '65%' }, 
          display: selectedArtifact ? 'block' : 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'background.paper',
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 1,
          p: 0,
          height: '700px',
          overflow: 'auto'
        }}>
          {selectedArtifact ? (
            <Box>
              <Box sx={{ 
                p: 2, 
                bgcolor: 'background.default', 
                borderBottom: '1px solid', 
                borderColor: 'divider',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <Box>
                  <Typography variant="h6">{selectedArtifact.name || selectedArtifact.title || 'Unnamed Artifact'}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {selectedArtifact.type || 'Unknown Type'} • Created by {selectedArtifact.createdBy || selectedArtifact.creator || selectedArtifact.agent || 'Unknown'} • {formatDate(selectedArtifact.createdAt)}
                  </Typography>
                </Box>
                <Button size="small" onClick={handleClearSelection}>
                  Close
                </Button>
              </Box>
              
              <Box sx={{ p: 0 }}>
                {/* Metadata section */}
                <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
                  <Typography variant="subtitle2" gutterBottom>Metadata</Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {selectedArtifact.status && (
                      <Chip 
                        size="small" 
                        label={`Status: ${selectedArtifact.status}`} 
                        color={
                          selectedArtifact.status === 'completed' ? 'success' : 
                          selectedArtifact.status === 'in-progress' ? 'info' : 
                          'default'
                        }
                      />
                    )}
                    {selectedArtifact.currentVersion && (
                      <Chip size="small" label={`Version: ${selectedArtifact.currentVersion}`} />
                    )}
                    {selectedArtifact.qualityScore !== undefined && (
                      <Chip 
                        size="small" 
                        label={`Quality: ${typeof selectedArtifact.qualityScore === 'number' ? 
                          `${(selectedArtifact.qualityScore * 100).toFixed(0)}%` : 
                          selectedArtifact.qualityScore}`} 
                      />
                    )}
                    {selectedArtifact.iterations && (
                      <Chip size="small" label={`Iterations: ${selectedArtifact.iterations.length}`} />
                    )}
                  </Box>
                </Box>
                
                {/* Content section */}
                <Box sx={{ p: 0, borderBottom: '1px solid', borderColor: 'divider' }}>
                  <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
                    <Typography variant="subtitle1">Content</Typography>
                    <Typography variant="caption" color="text.secondary">
                      Source: {contentSource}
                    </Typography>
                  </Box>
                  {renderContent(artifactContent)}
                </Box>
                
                {/* Iterations section */}
                {selectedArtifact && getIterationDetails(selectedArtifact)}
                
                {/* Reasoning section */}
                {selectedArtifact && getReasoningDetails(selectedArtifact)}
              </Box>
            </Box>
          ) : (
            <Typography variant="body1" color="text.secondary" sx={{ p: 4 }}>
              Select an artifact to view its details
            </Typography>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default ArtifactPanel;
