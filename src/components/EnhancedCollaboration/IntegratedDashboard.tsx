import React, { useState, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  Divider,
  Button,
  TextField,
  CircularProgress,
  Alert,
  Snackbar,
  Tabs,
  Tab,
  Grid,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Chip,
  FormHelperText,
  Stack
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import CollaborativeWorkflowVisualizer from './CollaborativeWorkflowVisualizer';
import ChainOfThoughtVisualizer from './ChainOfThoughtVisualizer';
import AgentDiscussionPanel from './AgentDiscussionPanel';
import ArticleViewer from './ArticleViewer';

/**
 * Integrated Dashboard Component for Collaborative Agent Content Generation
 * Combines workflow visualization, agent discussions, reasoning display, and article editing
 */
const IntegratedDashboard: React.FC = () => {
  // State
  const [sessionId, setSessionId] = useState<string>('');
  const [collaborationState, setCollaborationState] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' | 'info' | 'warning' }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Form inputs
  const [topic, setTopic] = useState<string>('');
  const [contentType, setContentType] = useState<'blog-article' | 'product-page' | 'buying-guide'>('blog-article');
  const [targetAudience, setTargetAudience] = useState<string>('');
  const [tone, setTone] = useState<string>('professional');
  const [keywordInput, setKeywordInput] = useState<string>('');
  const [keywords, setKeywords] = useState<string[]>([]);
  const [additionalInstructions, setAdditionalInstructions] = useState<string>('');

  const router = useRouter();
  const searchParams = useSearchParams();

  // Auto-refresh effect for active sessions
  useEffect(() => {
    let refreshTimer: NodeJS.Timeout | null = null;

    // Auto-refresh function to poll for updates every 5 seconds
    const refreshActiveSession = () => {
      if (sessionId && collaborationState?.status === 'active') {
        console.log('Auto-refreshing session:', sessionId);
        fetchCollaborationState(sessionId);
      }
    };

    // Set up timer if we have an active session
    if (sessionId && collaborationState?.status === 'active') {
      refreshTimer = setInterval(refreshActiveSession, 5000);
    }

    // Clean up on unmount
    return () => {
      if (refreshTimer) {
        clearInterval(refreshTimer);
      }
    };
  }, [sessionId, collaborationState?.status]);

  // Initial load effect
  useEffect(() => {
    // Check if there's a session ID in the URL
    const id = searchParams.get('id');
    if (id) {
      setSessionId(id);
      fetchCollaborationState(id);
    }
  }, [searchParams]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Fetch the current state of a collaboration session
  const fetchCollaborationState = async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/orchestrated-collaboration?sessionId=${id}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch collaboration state: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success && data.state) {
        setCollaborationState(data.state);

        // If there's a completed content and we're on the form tab, switch to article tab
        if (data.finalOutput && activeTab === 0) {
          setActiveTab(3); // Article tab
        }
      } else {
        throw new Error(data.error || 'Failed to fetch collaboration state');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Error fetching collaboration state:', err);
    } finally {
      setLoading(false);
    }
  };

  // Start a new collaboration session
  const startCollaboration = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validate inputs
      if (!topic) {
        throw new Error('Topic is required');
      }

      if (!targetAudience) {
        throw new Error('Target audience is required');
      }

      if (keywords.length === 0) {
        throw new Error('At least one keyword is required');
      }

      // Create request payload
      const payload = {
        topic,
        contentType,
        targetAudience,
        tone,
        keywords,
        additionalInstructions
      };

      // Show a message that the collaboration is starting
      setSnackbar({
        open: true,
        message: 'Starting collaborative content generation...',
        severity: 'info'
      });

      // Send request to the orchestrated API endpoint
      const response = await fetch('/api/orchestrated-collaboration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`Failed to start collaboration: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success && data.sessionId) {
        // Set the session ID
        setSessionId(data.sessionId);

        // Update URL with session ID
        router.push(`/enhanced-collaboration?id=${data.sessionId}`);

        // Set the state
        setCollaborationState(data.state);

        // Success message
        setSnackbar({
          open: true,
          message: 'Collaboration started successfully!',
          severity: 'success'
        });

        // Switch to the workflow tab
        setActiveTab(1);
      } else {
        throw new Error(data.error || 'Failed to start collaboration');
      }
    } catch (err: any) {
      setError(err.message);
      setSnackbar({
        open: true,
        message: `Error: ${err.message}`,
        severity: 'error'
      });
      console.error('Error starting collaboration:', err);
    } finally {
      setLoading(false);
    }
  };

  // Send a message to the agents
  const sendMessageToAgents = async (text: string) => {
    if (!sessionId || !text.trim()) return;

    try {
      setLoading(true);

      const message = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: 'user',
        to: 'system',
        type: 'QUESTION',
        content: { text }
      };

      // Send message to the API
      const response = await fetch('/api/orchestrated-collaboration/message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId,
          message
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to send message: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setCollaborationState(data.state);
        setSnackbar({
          open: true,
          message: 'Message sent successfully',
          severity: 'success'
        });
      } else {
        throw new Error(data.error || 'Failed to send message');
      }
    } catch (err: any) {
      setError(err.message);
      setSnackbar({
        open: true,
        message: `Error: ${err.message}`,
        severity: 'error'
      });
      console.error('Error sending message:', err);
    } finally {
      setLoading(false);
    }
  };

  // Save edited article
  const saveArticle = async (updatedArticle: any) => {
    // In a real implementation, this would save to the backend
    setSnackbar({
      open: true,
      message: 'Article saved successfully',
      severity: 'success'
    });

    // For now, just update the local state
    if (collaborationState && collaborationState.finalOutput) {
      setCollaborationState({
        ...collaborationState,
        finalOutput: updatedArticle
      });
    }
  };

  // Add a keyword
  const addKeyword = () => {
    if (keywordInput.trim() && !keywords.includes(keywordInput.trim())) {
      setKeywords([...keywords, keywordInput.trim()]);
      setKeywordInput('');
    }
  };

  // Remove a keyword
  const removeKeyword = (keywordToRemove: string) => {
    setKeywords(keywords.filter(keyword => keyword !== keywordToRemove));
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Check if we have a final article
  const hasFinalArticle = collaborationState?.finalOutput || false;

  return (
    <Box sx={{ p: 2 }}>
      {/* Header */}
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h4">
            Collaborative Content Generation
          </Typography>
          {sessionId && (
            <Box>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => fetchCollaborationState(sessionId)}
                disabled={loading}
                sx={{ mr: 1 }}
              >
                Refresh
              </Button>
              <Button
                variant="contained"
                color="primary"
                startIcon={<PlayArrowIcon />}
                onClick={() => setActiveTab(0)}
              >
                New Session
              </Button>
            </Box>
          )}
        </Box>

        {sessionId && (
          <Alert severity="info" sx={{ mb: 2 }}>
            Session ID: <strong>{sessionId}</strong>
            {collaborationState?.status && (
              <> • Status: <strong>{collaborationState.status}</strong></>
            )}
            {collaborationState?.progress !== undefined && (
              <> • Progress: <strong>{collaborationState.progress}%</strong></>
            )}
          </Alert>
        )}

        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab label="New Content" />
          <Tab label="Workflow" disabled={!sessionId} />
          <Tab label="Agent Discussions" disabled={!sessionId} />
          <Tab label="Reasoning" disabled={!sessionId} />
          <Tab label="Article" disabled={!hasFinalArticle} />
        </Tabs>
      </Paper>

      {/* Error display */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Loading indicator */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Tab content */}
      <Box sx={{ mt: 2 }}>
        {/* Tab 0: New Content Form */}
        {activeTab === 0 && (
          <Paper elevation={2} sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom>
              Create New Content
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Topic"
                  value={topic}
                  onChange={(e) => setTopic(e.target.value)}
                  required
                  helperText="Enter the main topic for your content"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Content Type</InputLabel>
                  <Select
                    value={contentType}
                    onChange={(e) => setContentType(e.target.value as any)}
                    label="Content Type"
                  >
                    <MenuItem value="blog-article">Blog Article</MenuItem>
                    <MenuItem value="product-page">Product Page</MenuItem>
                    <MenuItem value="buying-guide">Buying Guide</MenuItem>
                  </Select>
                  <FormHelperText>Select the type of content to generate</FormHelperText>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Tone</InputLabel>
                  <Select
                    value={tone}
                    onChange={(e) => setTone(e.target.value)}
                    label="Tone"
                  >
                    <MenuItem value="professional">Professional</MenuItem>
                    <MenuItem value="casual">Casual</MenuItem>
                    <MenuItem value="friendly">Friendly</MenuItem>
                    <MenuItem value="authoritative">Authoritative</MenuItem>
                    <MenuItem value="technical">Technical</MenuItem>
                  </Select>
                  <FormHelperText>Select the tone for your content</FormHelperText>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Target Audience"
                  value={targetAudience}
                  onChange={(e) => setTargetAudience(e.target.value)}
                  required
                  helperText="Describe your target audience (e.g., 'Marketing professionals aged 25-45')"
                />
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ mb: 1 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Keywords
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <TextField
                      label="Add a keyword"
                      value={keywordInput}
                      onChange={(e) => setKeywordInput(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addKeyword()}
                      sx={{ flexGrow: 1 }}
                    />
                    <Button
                      variant="outlined"
                      onClick={addKeyword}
                      disabled={!keywordInput.trim()}
                    >
                      Add
                    </Button>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                  {keywords.map((keyword, index) => (
                    <Chip
                      key={index}
                      label={keyword}
                      onDelete={() => removeKeyword(keyword)}
                      color={index === 0 ? "primary" : "default"}
                    />
                  ))}
                </Box>
                {keywords.length === 0 && (
                  <Typography variant="caption" color="error">
                    At least one keyword is required
                  </Typography>
                )}
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Additional Instructions"
                  value={additionalInstructions}
                  onChange={(e) => setAdditionalInstructions(e.target.value)}
                  multiline
                  rows={3}
                  helperText="Optional: Provide any specific instructions or requirements"
                />
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                  <Button
                    variant="contained"
                    color="primary"
                    size="large"
                    onClick={startCollaboration}
                    disabled={loading || !topic || !targetAudience || keywords.length === 0}
                  >
                    Start Collaboration
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        )}

        {/* Tab 1: Workflow Visualization */}
        {activeTab === 1 && sessionId && collaborationState && (
          <CollaborativeWorkflowVisualizer
            sessionId={sessionId}
            state={collaborationState}
            onRefresh={() => fetchCollaborationState(sessionId)}
          />
        )}

        {/* Tab 2: Agent Discussions */}
        {activeTab === 2 && sessionId && collaborationState && (
          <AgentDiscussionPanel
            messages={collaborationState.messages || []}
            sessionId={sessionId}
            onSendMessage={sendMessageToAgents}
            showInput={true}
          />
        )}

        {/* Tab 3: Agent Reasoning */}
        {activeTab === 3 && sessionId && collaborationState && (
          <ChainOfThoughtVisualizer
            iterations={collaborationState.iterations || []}
            messages={collaborationState.messages || []}
            consultations={collaborationState.consultations || []}
            artifacts={collaborationState.artifacts || []}
            decisions={collaborationState.decisions || []}
          />
        )}

        {/* Tab 4: Article */}
        {activeTab === 4 && hasFinalArticle && (
          <ArticleViewer
            article={collaborationState.finalOutput}
            onSave={saveArticle}
            sessionId={sessionId}
          />
        )}
      </Box>

      {/* Status Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default IntegratedDashboard;
