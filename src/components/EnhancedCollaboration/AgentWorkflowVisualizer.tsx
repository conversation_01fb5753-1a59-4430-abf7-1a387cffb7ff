import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, CircularProgress, Tooltip, Chip } from '@mui/material';
import PsychologyIcon from '@mui/icons-material/Psychology';
import HistoryIcon from '@mui/icons-material/History';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import SearchIcon from '@mui/icons-material/Search';
import EditIcon from '@mui/icons-material/Edit';
import RateReviewIcon from '@mui/icons-material/RateReview';

/**
 * Visualizes the collaborative workflow between agents in real-time
 */
interface AgentWorkflowVisualizerProps {
  sessionId: string | null;
  sessionState: any;
  loading?: boolean;
}

interface AgentStatus {
  name: string;
  status: 'idle' | 'active' | 'complete';
  lastActivity?: string;
  contributions?: number;
  icon: React.ReactNode;
  color: string;
}

const AgentWorkflowVisualizer: React.FC<AgentWorkflowVisualizerProps> = ({
  sessionId,
  sessionState,
  loading = false
}) => {
  const [agents, setAgents] = useState<AgentStatus[]>([]);
  const [activePhase, setActivePhase] = useState<string | null>(null);

  // Determine agent status and active workflow phase based on session state
  useEffect(() => {
    if (!sessionState) return;
    
    // Determine workflow phase
    const phase = sessionState.currentPhase || determinePhaseFromState(sessionState);
    setActivePhase(phase);
    
    // Extract agent information from session state
    const agentsData = extractAgentData(sessionState);
    setAgents(agentsData);
  }, [sessionState]);
  
  // Helper function to determine phase from state
  const determinePhaseFromState = (state: any): string => {
    if (!state) return 'initialization';
    
    if (state.status === 'completed') return 'complete';
    
    if (state.messages && Array.isArray(state.messages)) {
      // Look for phase indicators in messages
      const phaseMessages = state.messages.filter((m: any) => 
        m.from === 'system' && m.content && typeof m.content === 'string' && 
        (m.content.includes('phase') || m.content.includes('Phase'))
      );
      
      if (phaseMessages.length > 0) {
        const lastPhaseMsg = phaseMessages[phaseMessages.length - 1];
        
        if (lastPhaseMsg.content.includes('planning') || lastPhaseMsg.content.includes('Planning')) {
          return 'planning';
        } else if (lastPhaseMsg.content.includes('discussion') || lastPhaseMsg.content.includes('Discussion')) {
          return 'discussion';
        } else if (lastPhaseMsg.content.includes('execution') || lastPhaseMsg.content.includes('Execution') || 
                  lastPhaseMsg.content.includes('generation') || lastPhaseMsg.content.includes('Generation')) {
          return 'execution';
        } else if (lastPhaseMsg.content.includes('review') || lastPhaseMsg.content.includes('Review') ||
                  lastPhaseMsg.content.includes('feedback') || lastPhaseMsg.content.includes('Feedback')) {
          return 'review';
        } else if (lastPhaseMsg.content.includes('optimization') || lastPhaseMsg.content.includes('Optimization')) {
          return 'optimization';
        }
      }
    }
    
    return 'processing';
  };
  
  // Helper function to extract agent data from session state
  const extractAgentData = (state: any): AgentStatus[] => {
    const defaultAgents: AgentStatus[] = [
      {
        name: 'Content Generation',
        status: 'idle',
        icon: <EditIcon />,
        color: 'primary.main'
      },
      {
        name: 'Market Research',
        status: 'idle',
        icon: <TrendingUpIcon />,
        color: 'success.main'
      },
      {
        name: 'SEO Keyword',
        status: 'idle',
        icon: <SearchIcon />,
        color: 'info.main'
      },
      {
        name: 'Content Strategy',
        status: 'idle',
        icon: <MenuBookIcon />,
        color: 'secondary.main'
      },
      {
        name: 'SEO Optimization',
        status: 'idle',
        icon: <RateReviewIcon />,
        color: 'warning.main'
      }
    ];
    
    if (!state || !state.messages || !Array.isArray(state.messages)) {
      return defaultAgents;
    }
    
    // Count agent contributions and determine status
    const agentContributions = countAgentContributions(state.messages);
    const activeAgents = determineActiveAgents(state.messages, state.status);
    const completedAgents = determineCompletedAgents(state);
    
    // Update agent statuses
    return defaultAgents.map(agent => {
      const agentKey = agent.name.toLowerCase().replace(/\s+/g, '-');
      const isActive = activeAgents.includes(agentKey);
      const isComplete = completedAgents.includes(agentKey);
      
      return {
        ...agent,
        status: isComplete ? 'complete' : (isActive ? 'active' : 'idle'),
        contributions: agentContributions[agentKey] || 0,
        lastActivity: findLastActivity(state.messages, agentKey)
      };
    });
  };
  
  // Helper function to count agent contributions
  const countAgentContributions = (messages: any[]): Record<string, number> => {
    const contributions: Record<string, number> = {};
    
    if (!messages || !Array.isArray(messages)) return contributions;
    
    messages.forEach((msg: any) => {
      if (msg.from && msg.from !== 'system' && msg.from !== 'user') {
        const agentKey = msg.from.toLowerCase().replace(/\s+/g, '-');
        contributions[agentKey] = (contributions[agentKey] || 0) + 1;
      }
    });
    
    return contributions;
  };
  
  // Helper function to determine active agents based on recent messages
  const determineActiveAgents = (messages: any[], status: string): string[] => {
    if (!messages || !Array.isArray(messages) || status === 'completed') {
      return [];
    }
    
    // Look at the 5 most recent messages
    const recentMessages = messages.slice(-5);
    const activeAgents: string[] = [];
    
    recentMessages.forEach((msg: any) => {
      if (msg.from && msg.from !== 'system' && msg.from !== 'user') {
        const agentKey = msg.from.toLowerCase().replace(/\s+/g, '-');
        if (!activeAgents.includes(agentKey)) {
          activeAgents.push(agentKey);
        }
      }
    });
    
    return activeAgents;
  };
  
  // Helper function to determine completed agents
  const determineCompletedAgents = (state: any): string[] => {
    if (!state || state.status !== 'completed') return [];
    
    // For completed sessions, consider agents that contributed as completed
    const contributingAgents = Object.keys(countAgentContributions(state.messages || []));
    
    return contributingAgents;
  };
  
  // Helper function to find last activity timestamp for an agent
  const findLastActivity = (messages: any[], agentKey: string): string | undefined => {
    if (!messages || !Array.isArray(messages)) return undefined;
    
    // Search backwards for the most recent message from this agent
    for (let i = messages.length - 1; i >= 0; i--) {
      const msg = messages[i];
      const msgAgentKey = msg.from?.toLowerCase().replace(/\s+/g, '-');
      
      if (msgAgentKey === agentKey && msg.timestamp) {
        // Return formatted timestamp
        return new Date(msg.timestamp).toLocaleTimeString();
      }
    }
    
    return undefined;
  };
  
  // Render workflow phase name
  const renderPhaseName = (phase: string | null): string => {
    if (!phase) return 'Initializing';
    
    switch (phase) {
      case 'initialization': return 'Initialization';
      case 'planning': return 'Planning Phase';
      case 'discussion': return 'Discussion Phase';
      case 'execution': return 'Execution Phase';
      case 'review': return 'Review Phase';
      case 'optimization': return 'Optimization Phase';
      case 'complete': return 'Completed';
      default: return 'Processing';
    }
  };
  
  // Get color for phase
  const getPhaseColor = (phase: string | null): string => {
    if (!phase) return 'grey.500';
    
    switch (phase) {
      case 'initialization': return 'info.light';
      case 'planning': return 'info.main';
      case 'discussion': return 'secondary.main';
      case 'execution': return 'primary.main';
      case 'review': return 'warning.main';
      case 'optimization': return 'success.main';
      case 'complete': return 'success.dark';
      default: return 'grey.500';
    }
  };
  
  // Get icon for phase
  const getPhaseIcon = (phase: string | null): React.ReactNode => {
    if (!phase) return <AutorenewIcon />;
    
    switch (phase) {
      case 'initialization': return <HistoryIcon />;
      case 'planning': return <PsychologyIcon />;
      case 'discussion': return <PsychologyIcon />;
      case 'execution': return <EditIcon />;
      case 'review': return <RateReviewIcon />;
      case 'optimization': return <SearchIcon />;
      case 'complete': return <CheckCircleIcon />;
      default: return <AutorenewIcon />;
    }
  };
  
  // Renders status indicator for an agent
  const renderAgentStatus = (status: 'idle' | 'active' | 'complete'): React.ReactNode => {
    switch (status) {
      case 'active':
        return <CircularProgress size={16} thickness={6} sx={{ color: 'primary.main' }} />;
        
      case 'complete':
        return <CheckCircleIcon sx={{ color: 'success.main', fontSize: 16 }} />;
        
      case 'idle':
      default:
        return <CircularProgress size={16} thickness={6} sx={{ color: 'grey.400' }} variant="determinate" value={0} />;
    }
  };

  if (!sessionId) {
    return null;
  }

  return (
    <Paper
      elevation={1}
      sx={{
        p: 2,
        borderRadius: 2,
        bgcolor: 'background.paper',
        width: '100%',
        mt: 2,
        mb: 2
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <PsychologyIcon sx={{ color: 'primary.main', mr: 1 }} />
        <Typography variant="h6" component="h2" sx={{ flexGrow: 1 }}>
          Agent Collaboration Workflow
        </Typography>
        
        <Chip
          icon={getPhaseIcon(activePhase) as React.ReactElement}
          label={renderPhaseName(activePhase)}
          sx={{
            bgcolor: `${getPhaseColor(activePhase)}`,
            color: 'white',
            fontWeight: 'medium'
          }}
        />
      </Box>
      
      <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
        {agents.map((agent, index) => (
          <Tooltip
            key={index}
            title={`${agent.name}: ${agent.status === 'active' ? 'Currently active' : 
                               agent.status === 'complete' ? 'Work completed' : 'Waiting'}
                   ${agent.lastActivity ? `\nLast active: ${agent.lastActivity}` : ''}
                   ${agent.contributions ? `\nContributions: ${agent.contributions}` : ''}`}
            arrow
          >
            <Paper
              elevation={agent.status === 'active' ? 2 : 0}
              sx={{
                p: 1.5,
                borderRadius: 2,
                display: 'flex',
                alignItems: 'center',
                gap: 1.5,
                bgcolor: agent.status === 'active' ? 'rgba(0,0,0,0.02)' : 'transparent',
                border: '1px solid',
                borderColor: agent.status === 'active' ? agent.color : 'grey.200',
                minWidth: 180,
                position: 'relative',
                overflow: 'hidden',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: 2
                },
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '5px',
                  height: '100%',
                  backgroundColor: agent.color
                }
              }}
            >
              <Box 
                sx={{ 
                  p: 0.8, 
                  borderRadius: '50%',
                  bgcolor: `${agent.status === 'active' ? `${agent.color}` : 'grey.100'}25`,
                  display: 'flex', 
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: agent.status === 'active' ? agent.color : 'grey.500'
                }}
              >
                {agent.icon}
              </Box>
              
              <Box sx={{ flexGrow: 1 }}>
                <Typography 
                  variant="subtitle2" 
                  component="div" 
                  sx={{ fontWeight: agent.status === 'active' ? 'bold' : 'medium' }}
                >
                  {agent.name}
                </Typography>
                
                {agent.contributions !== undefined && (
                  <Typography variant="caption" color="text.secondary">
                    {agent.contributions} contribution{agent.contributions !== 1 ? 's' : ''}
                  </Typography>
                )}
              </Box>
              
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', ml: 'auto' }}>
                {renderAgentStatus(agent.status)}
              </Box>
            </Paper>
          </Tooltip>
        ))}
      </Box>
      
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
          <CircularProgress size={24} />
        </Box>
      )}
    </Paper>
  );
};

export default AgentWorkflowVisualizer;
