// src/components/EnhancedCollaboration/ArtifactPanel.tsx
import React, { useState, useEffect, useMemo } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Tabs, 
  Tab, 
  List, 
  ListItem, 
  ListItemButton, 
  ListItemText, 
  Chip, 
  Button, 
  CircularProgress, 
  TextField, 
  Alert, 
  Divider, 
  Accordion, 
  AccordionSummary, 
  AccordionDetails 
} from '@mui/material';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

interface Artifact {
  id: string;
  name?: string;
  title?: string;
  type?: string;
  creator?: string;
  createdBy?: string;
  agent?: string;
  timestamp?: string;
  createdAt?: string;
  created?: string;
  date?: string;
  data?: any;
  content?: any;
  text?: string;
  reasoningSteps?: any[];
  iterations?: any[];
  status?: string;
  qualityScore?: number;
  metadata?: Record<string, any>;
  [key: string]: any;
}

interface ArtifactPanelProps {
  artifacts: Artifact[] | Record<string, Artifact> | any;
  refreshContent: () => void;
  loading: boolean;
}

// Helper function to format dates
const formatDate = (dateString?: string): string => {
  if (!dateString) return 'Unknown';
  try {
    const date = new Date(dateString);
    return date.toLocaleString();
  } catch (e) {
    return dateString || 'Unknown';
  }
};

const ArtifactPanel: React.FC<ArtifactPanelProps> = ({ 
  artifacts,
  refreshContent,
  loading
}): React.ReactElement => {
  const [selectedArtifact, setSelectedArtifact] = useState<Artifact | null>(null);
  const [artifactContent, setArtifactContent] = useState<string>('');
  const [contentSource, setContentSource] = useState<string>('');
  const [expandedAgent, setExpandedAgent] = useState<string | false>('market-research');
  const [directArtifacts, setDirectArtifacts] = useState<Artifact[]>([]);
  const [loadingDirectArtifacts, setLoadingDirectArtifacts] = useState<boolean>(false);
  const [directArtifactsError, setDirectArtifactsError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<number>(0);

  // Handle accordion expansion
  const handleAccordionChange = (agentId: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedAgent(isExpanded ? agentId : false);
  };
  
  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Define normalization function using useMemo to avoid recreation on each render
  const normalizeArtifacts = useMemo(() => {
    return (artifactsInput: any): Artifact[] => {
      if (!artifactsInput) {
        console.log('No artifacts to normalize');
        return [];
      }
      
      console.log('Normalizing artifacts in ArtifactPanel:', typeof artifactsInput);
      let normalizedArtifacts: Artifact[] = [];
      
      // Case 1: Artifacts is the full state object with an artifacts property
      if (artifactsInput && typeof artifactsInput === 'object' && 'artifacts' in artifactsInput) {
        const artifactsData = artifactsInput.artifacts;
        
        // Handle object with IDs as keys
        if (artifactsData && typeof artifactsData === 'object' && !Array.isArray(artifactsData)) {
          normalizedArtifacts = Object.entries(artifactsData).map(([id, data]) => {
            if (!data || typeof data !== 'object') return { id } as Artifact;
            
            const artifactData = data as Record<string, any>;
            return {
              id: artifactData.id || id,
              name: artifactData.name || artifactData.title || 'Unnamed',
              type: artifactData.type || 'unknown',
              createdBy: artifactData.createdBy || artifactData.creator || artifactData.agent || 'Unknown',
              createdAt: artifactData.createdAt || artifactData.timestamp || artifactData.created || artifactData.date || new Date().toISOString(),
              content: artifactData.content || artifactData.text || artifactData.data || '',
              ...artifactData // Include all other properties
            } as Artifact;
          });
        } 
        // Handle array
        else if (Array.isArray(artifactsData)) {
          normalizedArtifacts = artifactsData;
        }
      }
      // Case 2: Artifacts is already an object with IDs as keys (direct Redis structure)
      else if (artifactsInput && typeof artifactsInput === 'object' && !Array.isArray(artifactsInput) && Object.keys(artifactsInput).length > 0) {
        // Check if the structure seems like artifact entries with IDs as keys
        const hasArtifactProperties = Object.values(artifactsInput).some(v => 
          v !== null && typeof v === 'object' && 
          ((v as any).type !== undefined || (v as any).createdBy !== undefined || (v as any).iterations !== undefined)
        );
        
        if (hasArtifactProperties) {
          normalizedArtifacts = Object.entries(artifactsInput).map(([id, data]) => {
            if (!data || typeof data !== 'object') return { id } as Artifact;
            
            const artifactData = data as Record<string, any>;
            return {
              id: artifactData.id || id,
              name: artifactData.name || artifactData.title || 'Unnamed',
              type: artifactData.type || 'unknown',
              createdBy: artifactData.createdBy || artifactData.creator || artifactData.agent || 'Unknown',
              createdAt: artifactData.createdAt || artifactData.timestamp || artifactData.created || artifactData.date || new Date().toISOString(),
              content: artifactData.content || artifactData.text || artifactData.data || '',
              ...artifactData
            } as Artifact;
          });
        }
      }
      // Case 3: Artifacts is already an array
      else if (Array.isArray(artifactsInput)) {
        normalizedArtifacts = artifactsInput;
      }
      
      console.log(`Normalized ${normalizedArtifacts.length} artifacts`);
      return normalizedArtifacts;
    };
  }, []);
  
  // Apply normalization to artifacts whenever they change
  useEffect(() => {
    const normalizedData = normalizeArtifacts(artifacts);
    setDirectArtifacts(normalizedData);
  }, [artifacts, normalizeArtifacts]);

  // Group artifacts by agent
  const artifactsByAgent = useMemo(() => {
    const groupedArtifacts: Record<string, Artifact[]> = {};
    
    directArtifacts.forEach(artifact => {
      const agent = artifact.createdBy || artifact.agent || artifact.creator || 'unknown';
      if (!groupedArtifacts[agent]) {
        groupedArtifacts[agent] = [];
      }
      groupedArtifacts[agent].push(artifact);
    });
    
    return groupedArtifacts;
  }, [directArtifacts]);

  // Group artifacts by type
  const artifactsByType = useMemo(() => {
    const groupedArtifacts: Record<string, Artifact[]> = {};
    
    directArtifacts.forEach(artifact => {
      const type = artifact.type || 'unknown';
      if (!groupedArtifacts[type]) {
        groupedArtifacts[type] = [];
      }
      groupedArtifacts[type].push(artifact);
    });
    
    return groupedArtifacts;
  }, [directArtifacts]);

  // Handle artifact selection
  const handleArtifactSelect = (artifact: Artifact) => {
    setSelectedArtifact(artifact);
    
    // Set content to display based on artifact type and structure
    let content = '';
    let source = '';
    
    if (artifact.content) {
      source = 'content';
      if (typeof artifact.content === 'string') {
        content = artifact.content;
      } else if (typeof artifact.content === 'object') {
        try {
          content = JSON.stringify(artifact.content, null, 2);
        } catch (e) {
          content = 'Unable to display content';
        }
      }
    } else if (artifact.text) {
      source = 'text';
      content = artifact.text as string;
    } else if (artifact.data) {
      source = 'data';
      try {
        content = typeof artifact.data === 'string' ? artifact.data : JSON.stringify(artifact.data, null, 2);
      } catch (e) {
        content = 'Unable to display data';
      }
    } else if (artifact.iterations && artifact.iterations.length > 0) {
      source = 'iterations';
      const latestIteration = artifact.iterations[artifact.iterations.length - 1];
      
      if (latestIteration.content) {
        try {
          content = typeof latestIteration.content === 'string' 
            ? latestIteration.content 
            : JSON.stringify(latestIteration.content, null, 2);
        } catch (e) {
          content = 'Unable to display iteration content';
        }
      } else {
        content = 'No content in latest iteration';
      }
    } else {
      content = 'No content available for this artifact';
    }
    
    setArtifactContent(content);
    setContentSource(source);
  };

  // Clear artifact selection
  const handleClearSelection = () => {
    setSelectedArtifact(null);
    setArtifactContent('');
    setContentSource('');
  };

  // Function to determine the appropriate display format for content
  const renderContent = (content: string) => {
    // Try to detect if the content is JSON
    try {
      const trimmed = content.trim();
      if ((trimmed.startsWith('{') && trimmed.endsWith('}')) || 
          (trimmed.startsWith('[') && trimmed.endsWith(']'))) {
        // Parse and re-stringify to format JSON properly
        const parsedJson = JSON.parse(trimmed);
        return (
          <Box component="pre" sx={{ 
            margin: 0, 
            whiteSpace: 'pre-wrap', 
            overflow: 'auto', 
            backgroundColor: '#f5f5f5',
            padding: 2,
            borderRadius: 1
          }}>
            {JSON.stringify(parsedJson, null, 2)}
          </Box>
        );
      }
    } catch (e) {
      // Not JSON or invalid JSON, continue to other formats
    }
    
    // Check if content looks like markdown
    if (content.includes('#') || content.includes('*') || content.includes('```') || 
        content.includes('---') || content.includes('| ---') || content.includes('[')) {
      return (
        <Box sx={{ px: 2 }}>
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {content}
          </ReactMarkdown>
        </Box>
      );
    }
    
    // Default to plain text
    return (
      <Typography sx={{ whiteSpace: 'pre-wrap', p: 2 }}>
        {content}
      </Typography>
    );
  };

  // Get iteration details if available
  const getIterationDetails = (artifact: Artifact) => {
    if (!artifact.iterations || artifact.iterations.length === 0) {
      return null;
    }
    
    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle1">Iterations</Typography>
        <List dense>
          {artifact.iterations.map((iteration: any, index: number) => (
            <ListItem key={index} disablePadding>
              <ListItemButton onClick={() => {
                const content = typeof iteration.content === 'string' 
                  ? iteration.content 
                  : JSON.stringify(iteration.content, null, 2);
                setArtifactContent(content);
                setContentSource(`iteration-${index}`);
              }}>
                <ListItemText 
                  primary={`Version ${iteration.version || index + 1}`} 
                  secondary={`${formatDate(iteration.timestamp)} by ${iteration.agent || 'Unknown'}`} 
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>
    );
  };

  // Get reasoning details if available
  const getReasoningDetails = (artifact: Artifact) => {
    const reasoning = artifact.reasoning || 
                     (artifact.metadata && artifact.metadata.reasoning) ||
                     (artifact.iterations && artifact.iterations.length > 0 && artifact.iterations[0].reasoning);
                     
    if (!reasoning) {
      return null;
    }
    
    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle1">Reasoning</Typography>
        <Paper variant="outlined" sx={{ p: 2, mt: 1 }}>
          {reasoning.process && (
            <Typography variant="subtitle2" gutterBottom>
              Process: {reasoning.process}
            </Typography>
          )}
          
          {reasoning.confidence && (
            <Typography variant="body2" gutterBottom>
              Confidence: {(reasoning.confidence * 100).toFixed(0)}%
            </Typography>
          )}
          
          {reasoning.steps && reasoning.steps.length > 0 && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="subtitle2" gutterBottom>Steps:</Typography>
              <List dense>
                {reasoning.steps.map((step: string, index: number) => (
                  <ListItem key={index}>
                    <ListItemText primary={`${index + 1}. ${step}`} />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
          
          {reasoning.conclusion && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="subtitle2" gutterBottom>Conclusion:</Typography>
              <Typography variant="body2">{reasoning.conclusion}</Typography>
            </Box>
          )}
        </Paper>
      </Box>
    );
  };

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab label="By Agent" />
          <Tab label="By Type" />
        </Tabs>
      </Box>
      
      {/* Loading indicator */}
      {(loading || loadingDirectArtifacts) && (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      )}
      
      {/* Error message */}
      {directArtifactsError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {directArtifactsError}
        </Alert>
      )}
      
      {/* Empty state */}
      {!loading && !loadingDirectArtifacts && directArtifacts.length === 0 && (
        <Box sx={{ textAlign: 'center', p: 4 }}>
          <Typography variant="body1" color="text.secondary">
            No artifacts available
          </Typography>
          <Button onClick={refreshContent} sx={{ mt: 2 }}>
            Refresh
          </Button>
        </Box>
      )}
      
      {/* Content panel */}
      <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
        {/* Artifact list panel */}
        <Box sx={{ width: { xs: '100%', md: '35%' }, height: selectedArtifact ? '700px' : 'auto', overflow: 'auto' }}>
          {/* Tab 1: Grouped by Agent */}
          <Box sx={{ display: activeTab === 0 ? 'block' : 'none' }}>
            {Object.entries(artifactsByAgent).map(([agent, agentArtifacts]) => (
              <Accordion 
                key={agent} 
                expanded={expandedAgent === agent} 
                onChange={handleAccordionChange(agent)}
                sx={{ mb: 1 }}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography>
                    {agent} ({agentArtifacts.length})
                  </Typography>
                </AccordionSummary>
                <AccordionDetails sx={{ p: 0 }}>
                  <List dense disablePadding>
                    {agentArtifacts.map((artifact) => (
                      <ListItem 
                        key={artifact.id} 
                        disablePadding
                        secondaryAction={
                          <Chip 
                            size="small" 
                            label={artifact.status || 'draft'} 
                            color={
                              artifact.status === 'completed' ? 'success' : 
                              artifact.status === 'in-progress' ? 'info' : 
                              'default'
                            }
                          />
                        }
                      >
                        <ListItemButton 
                          selected={selectedArtifact?.id === artifact.id}
                          onClick={() => handleArtifactSelect(artifact)}
                        >
                          <ListItemText 
                            primary={artifact.name || artifact.title || 'Unnamed'}
                            secondary={`${artifact.type || 'Unknown Type'} - ${formatDate(artifact.createdAt)}`}
                          />
                        </ListItemButton>
                      </ListItem>
                    ))}
                  </List>
                </AccordionDetails>
              </Accordion>
            ))}
          </Box>
          
          {/* Tab 2: Grouped by Type */}
          <Box sx={{ display: activeTab === 1 ? 'block' : 'none' }}>
            {Object.entries(artifactsByType).map(([type, typeArtifacts]) => (
              <Accordion 
                key={type} 
                defaultExpanded={true}
                sx={{ mb: 1 }}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography>
                    {type} ({typeArtifacts.length})
                  </Typography>
                </AccordionSummary>
                <AccordionDetails sx={{ p: 0 }}>
                  <List dense disablePadding>
                    {typeArtifacts.map((artifact) => (
                      <ListItem 
                        key={artifact.id} 
                        disablePadding
                        secondaryAction={
                          <Chip 
                            size="small" 
                            label={artifact.status || 'draft'} 
                            color={
                              artifact.status === 'completed' ? 'success' : 
                              artifact.status === 'in-progress' ? 'info' : 
                              'default'
                            }
                          />
                        }
                      >
                        <ListItemButton 
                          selected={selectedArtifact?.id === artifact.id}
                          onClick={() => handleArtifactSelect(artifact)}
                        >
                          <ListItemText 
                            primary={artifact.name || artifact.title || 'Unnamed'}
                            secondary={`${artifact.createdBy || artifact.agent || 'Unknown'} - ${formatDate(artifact.createdAt)}`}
                          />
                        </ListItemButton>
                      </ListItem>
                    ))}
                  </List>
                </AccordionDetails>
              </Accordion>
            ))}
          </Box>
        </Box>
        
        {/* Artifact detail panel */}
        <Box sx={{ 
          width: { xs: '100%', md: '65%' }, 
          display: selectedArtifact ? 'block' : 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'background.paper',
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 1,
          p: 0,
          height: '700px',
          overflow: 'auto'
        }}>
          {selectedArtifact ? (
            <Box>
              <Box sx={{ 
                p: 2, 
                bgcolor: 'background.default', 
                borderBottom: '1px solid', 
                borderColor: 'divider',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <Box>
                  <Typography variant="h6">{selectedArtifact.name || selectedArtifact.title || 'Unnamed Artifact'}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {selectedArtifact.type || 'Unknown Type'} • Created by {selectedArtifact.createdBy || selectedArtifact.creator || selectedArtifact.agent || 'Unknown'} • {formatDate(selectedArtifact.createdAt)}
                  </Typography>
                </Box>
                <Button size="small" onClick={handleClearSelection}>
                  Close
                </Button>
              </Box>
              
              <Box sx={{ p: 0 }}>
                {/* Metadata section */}
                <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
                  <Typography variant="subtitle2" gutterBottom>Metadata</Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {selectedArtifact.status && (
                      <Chip 
                        size="small" 
                        label={`Status: ${selectedArtifact.status}`} 
                        color={
                          selectedArtifact.status === 'completed' ? 'success' : 
                          selectedArtifact.status === 'in-progress' ? 'info' : 
                          'default'
                        }
                      />
                    )}
                    {selectedArtifact.currentVersion && (
                      <Chip size="small" label={`Version: ${selectedArtifact.currentVersion}`} />
                    )}
                    {selectedArtifact.qualityScore !== undefined && (
                      <Chip 
                        size="small" 
                        label={`Quality: ${typeof selectedArtifact.qualityScore === 'number' ? 
                          `${(selectedArtifact.qualityScore * 100).toFixed(0)}%` : 
                          selectedArtifact.qualityScore}`} 
                      />
                    )}
                    {selectedArtifact.iterations && (
                      <Chip size="small" label={`Iterations: ${selectedArtifact.iterations.length}`} />
                    )}
                  </Box>
                </Box>
                
                {/* Content section */}
                <Box sx={{ p: 0, borderBottom: '1px solid', borderColor: 'divider' }}>
                  <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
                    <Typography variant="subtitle1">Content</Typography>
                    <Typography variant="caption" color="text.secondary">
                      Source: {contentSource}
                    </Typography>
                  </Box>
                  {renderContent(artifactContent)}
                </Box>
                
                {/* Iterations section */}
                {getIterationDetails(selectedArtifact)}
                
                {/* Reasoning section */}
                {getReasoningDetails(selectedArtifact)}
              </Box>
            </Box>
          ) : (
            <Typography variant="body1" color="text.secondary" sx={{ p: 4 }}>
              Select an artifact to view its details
            </Typography>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default ArtifactPanel;
