import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSearchParams } from 'next/navigation';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Button,
  CircularProgress,
  Alert,
  TextField,
  MenuItem,
  Select,
  FormControl,
  FormHelperText,
  InputLabel,
  Tab,
  Tabs,
  Snackbar
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import ArticleIcon from '@mui/icons-material/Article';
import ChatIcon from '@mui/icons-material/Chat';
import PsychologyIcon from '@mui/icons-material/Psychology';
import ManageSearchIcon from '@mui/icons-material/ManageSearch';

// Import the necessary components
import AgentDiscussionPanel from './AgentDiscussionPanel';
import ChainOfThoughtVisualizer from './ChainOfThoughtVisualizer';
import ArticleViewer from './ArticleViewer';
import ArtifactPanel from './ArtifactPanel';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// Tab panel component
function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      style={{ width: '100%', padding: '20px 0' }}
      {...other}
    >
      {value === index && (
        <Box>
          {children}
        </Box>
      )}
    </div>
  );
}

// Main Dashboard Component
const CollaborativeAgentDashboard: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('id');
  
  // State for the entire system
  const [state, setState] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [notification, setNotification] = useState<{open: boolean, message: string, severity: 'success' | 'error' | 'info'}>({
    open: false,
    message: '',
    severity: 'info'
  });
  
  // Form state for new collaboration
  const [topic, setTopic] = useState<string>('');
  const [contentType, setContentType] = useState<string>('blog-article');
  const [targetAudience, setTargetAudience] = useState<string>('');
  const [tone, setTone] = useState<string>('professional');
  const [keywords, setKeywords] = useState<string>('');
  const [formError, setFormError] = useState<string | null>(null);
  
  // State for article feedback
  const [feedback, setFeedback] = useState<string>('');
  
  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  // Fetch the session state
  const fetchState = async () => {
    if (!sessionId) return;
    
    try {
      setLoading(true);
      const response = await fetch(`/api/collaborative-agents?sessionId=${sessionId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch session state: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.state) {
        console.log('Session state fetched:', data.state);
        setState(data.state);
      } else {
        throw new Error('Invalid session state returned from server');
      }
    } catch (err) {
      console.error('Error fetching session state:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch session state');
    } finally {
      setLoading(false);
    }
  };
  
  // Start a new collaboration session
  const startCollaboration = async () => {
    try {
      setLoading(true);
      setFormError(null);
      
      // Validate form
      if (!topic.trim()) {
        setFormError('Topic is required');
        return;
      }
      
      if (!targetAudience.trim()) {
        setFormError('Target audience is required');
        return;
      }
      
      if (!keywords.trim()) {
        setFormError('At least one keyword is required');
        return;
      }
      
      // Prepare the request payload
      const keywordsArray = keywords.split(',').map(k => k.trim()).filter(Boolean);
      
      const payload = {
        action: 'createArticle',
        topic: topic.trim(),
        contentType,
        targetAudience: targetAudience.trim(),
        tone,
        keywords: keywordsArray,
        useCollaborativeDiscussion: true
      };
      
      console.log('Creating new collaboration session with payload:', payload);
      
      // Call the API
      const response = await fetch('/api/collaborative-agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create session: ${response.status} - ${errorText}`);
      }
      
      const data = await response.json();
      
      if (data.sessionId) {
        setNotification({
          open: true,
          message: 'Collaboration session created successfully!',
          severity: 'success'
        });
        
        // Navigate to the new session
        router.push(`/admin/enhanced-collaboration?id=${data.sessionId}`);
      } else {
        throw new Error('No session ID returned from server');
      }
    } catch (err) {
      console.error('Error creating collaboration session:', err);
      setFormError(err instanceof Error ? err.message : 'Failed to create session');
      setNotification({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to create session',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Send feedback on the article
  const sendArticleFeedback = async () => {
    if (!sessionId || !feedback.trim()) return;
    
    try {
      setLoading(true);
      
      const response = await fetch('/api/collaborative-agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          action: 'sendFeedback',
          feedback: feedback.trim()
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to send feedback: ${response.status}`);
      }
      
      setNotification({
        open: true,
        message: 'Feedback sent successfully!',
        severity: 'success'
      });
      
      // Clear feedback field and refresh state
      setFeedback('');
      fetchState();
    } catch (err) {
      console.error('Error sending feedback:', err);
      setNotification({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to send feedback',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Convert collaboration state to article format for ArticleViewer
  const getArticleData = () => {
    if (!state) return { title: 'Loading...', content: '' };
    
    // Find content in artifacts or final output
    let title = state.topic || 'Generated Article';
    let content = '';
    let seoScore = 0;
    let generatedAt = '';
    let contributors = [];
    let metadata = { };
    
    // Look for content artifacts
    if (state.artifacts && Array.isArray(state.artifacts)) {
      // Find the content artifact (usually created by content-generation agent)
      const contentArtifact = state.artifacts.find(
        (a: any) => a.type === 'content' || a.type === 'content-draft' || a.type === 'article-draft'
      );
      
      if (contentArtifact) {
        // Extract content and metadata from the artifact
        if (contentArtifact.content?.title) {
          title = contentArtifact.content.title;
        }
        
        if (typeof contentArtifact.content?.body === 'string') {
          content = contentArtifact.content.body;
        } else if (typeof contentArtifact.content === 'string') {
          content = contentArtifact.content;
        } else if (typeof contentArtifact.data?.content === 'string') {
          content = contentArtifact.data.content;
        }
        
        generatedAt = contentArtifact.createdAt || contentArtifact.timestamp || '';
      }
      
      // Find SEO artifact for SEO score
      const seoArtifact = state.artifacts.find(
        (a: any) => a.type === 'seo-optimization' || a.type === 'seo-review'
      );
      
      if (seoArtifact) {
        seoScore = seoArtifact.score || seoArtifact.content?.score || 70;
      }
      
      // Find keyword artifact for metadata
      const keywordArtifact = state.artifacts.find(
        (a: any) => a.type === 'seo-keywords' || a.type === 'keywords'
      );
      
      if (keywordArtifact) {
        metadata = {
          ...metadata,
          keywords: keywordArtifact.content?.keywords || keywordArtifact.data?.keywords || []
        };
      }
    }
    
    // If no content found in artifacts, check the state's finalOutput field
    if (!content && state.finalOutput) {
      if (state.finalOutput.title) {
        title = state.finalOutput.title;
      }
      
      if (typeof state.finalOutput.content === 'string') {
        content = state.finalOutput.content;
      }
      
      if (state.finalOutput.seoScore) {
        seoScore = state.finalOutput.seoScore;
      }
    }
    
    // Add all agents as contributors
    let contributors: string[] = [];
    if (state.messages && Array.isArray(state.messages)) {
      const agentSet = new Set<string>();
      state.messages.forEach((msg: any) => {
        if (msg.from && msg.from !== 'system' && msg.from !== 'user') {
          agentSet.add(msg.from);
        }
      });
      contributors = Array.from(agentSet);
    }
    
    // Add remaining metadata
    metadata = {
      ...metadata,
      contentType: state.contentType || 'blog-article',
      targetAudience: state.targetAudience || 'general',
      tone: state.tone || 'professional',
      wordCount: content.split(/\s+/).length,
      readingTime: Math.ceil(content.split(/\s+/).length / 200) // Approximate reading time in minutes
    };
    
    return {
      title,
      content,
      seoScore,
      generatedAt,
      contributors,
      metadata
    };
  };
  
  // Initial load
  useEffect(() => {
    if (sessionId) {
      fetchState();
      
      // Set up polling for active sessions
      const intervalId = setInterval(() => {
        if (state?.status === 'active' || state?.status === 'processing') {
          console.log('Auto-refreshing active session');
          fetchState();
        }
      }, 10000); // Poll every 10 seconds
      
      return () => clearInterval(intervalId);
    } else {
      setLoading(false);
    }
  }, [sessionId]);
  
  // If no session ID, show the creation form
  if (!sessionId) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ mt: 4, mb: 4 }}>
          <Typography variant="h4" gutterBottom>
                    onClick={startCollaboration}
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <ChatIcon />}
                  >
                    {loading ? 'Creating...' : 'Start Collaborative Creation'}
                  </Button>
                </Box>
                
                {formError && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    {formError}
                  </Alert>
                )}
              </Grid>
            </Grid>
          </Paper>
        </Box>
        
        <Snackbar
          open={notification.open}
          autoHideDuration={6000}
          onClose={() => setNotification({ ...notification, open: false })}
        >
          <Alert severity={notification.severity} onClose={() => setNotification({ ...notification, open: false })}>
            {notification.message}
          </Alert>
        </Snackbar>
      </Container>
    );
  }
  
  // Show loading state
  if (loading && !state) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Loading collaboration session...
        </Typography>
      </Box>
    );
  }
  
  // Show error state
  if (error && !state) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchState}
        >
          Retry
        </Button>
      </Box>
    );
  }
  
  const articleData = getArticleData();
  
  return (
    <Container maxWidth="xl">
      <Box sx={{ mt: 2, mb: 4 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs>
            <Typography variant="h4">
              {state?.topic || 'Collaborative Content Creation'}
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Session ID: {sessionId}
            </Typography>
          </Grid>
          <Grid item component="div">
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={fetchState}
                disabled={loading}
              >
                Refresh
              </Button>
            </Box>
          </Grid>
        </Grid>
        
        <Paper sx={{ mt: 3, mb: 3, p: 2 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4} component="div">
              <Typography variant="subtitle2" color="text.secondary">
                Content Type
              </Typography>
              <Typography variant="body1">
                {state?.contentType?.replace('-', ' ') || 'Not specified'}
              </Typography>
            </Grid>
            <Grid item xs={12} md={4} component="div">
              <Typography variant="subtitle2" color="text.secondary">
                Target Audience
              </Typography>
              <Typography variant="body1">
                {state?.targetAudience || 'Not specified'}
              </Typography>
            </Grid>
            <Grid item xs={12} md={4} component="div">
              <Typography variant="subtitle2" color="text.secondary">
                Status
              </Typography>
              <Typography variant="body1" sx={{ 
                color: state?.status === 'completed' ? 'success.main' : 
                       state?.status === 'active' ? 'info.main' : 'text.primary'
              }}>
                {state?.status || 'Unknown'}
              </Typography>
            </Grid>
          </Grid>
        </Paper>
        
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            variant="scrollable"
            scrollButtons="auto"
            aria-label="dashboard tabs"
          >
            <Tab label="Generated Article" icon={<ArticleIcon />} iconPosition="start" />
            <Tab label="Agent Discussions" icon={<ChatIcon />} iconPosition="start" />
            <Tab label="Reasoning Process" icon={<PsychologyIcon />} iconPosition="start" />
            <Tab label="Artifacts" icon={<ManageSearchIcon />} iconPosition="start" />
          </Tabs>
        </Box>
        
        <TabPanel value={tabValue} index={0}>
          <ArticleViewer 
            article={articleData}
            sessionId={sessionId}
            onSave={(updatedArticle) => {
              console.log('Article updated:', updatedArticle);
              // In a real implementation, we would save the updated article to the backend
            }}
          />
          
          <Box sx={{ mt: 3, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
            <Typography variant="h6" gutterBottom>
              Provide Feedback
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={3}
              placeholder="Enter your feedback on the generated content..."
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              variant="outlined"
              sx={{ mb: 2 }}
            />
            <Button
              variant="contained"
              disabled={!feedback.trim() || loading}
              onClick={sendArticleFeedback}
            >
              Send Feedback
            </Button>
          </Box>
        </TabPanel>
        
        <TabPanel value={tabValue} index={1}>
          <AgentDiscussionPanel 
            messages={state?.messages || []}
            sessionId={sessionId}
            showInput={false}
          />
        </TabPanel>
        
        <TabPanel value={tabValue} index={2}>
          <ChainOfThoughtVisualizer 
            messages={state?.messages || []}
            artifacts={state?.artifacts || []}
            consultations={state?.consultations || []}
          />
        </TabPanel>
        
        <TabPanel value={tabValue} index={3}>
          <ArtifactPanel 
            artifacts={state?.artifacts || []}
            refreshContent={fetchState}
            loading={loading}
          />
        </TabPanel>
      </Box>
      
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, open: false })}
      >
        <Alert 
          severity={notification.severity} 
          onClose={() => setNotification({ ...notification, open: false })}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default CollaborativeAgentDashboard;
