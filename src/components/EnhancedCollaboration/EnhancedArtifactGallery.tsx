// src/components/EnhancedCollaboration/EnhancedArtifactGalleryV2.tsx

import React, { useState, useEffect, useMemo } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Card, 
  CardContent, 
  CardActions,
  Button, 
  Divider, 
  Chip,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tabs,
  Tab,
  CircularProgress,
  Alert
} from '@mui/material';
import StarIcon from '@mui/icons-material/Star';
import HistoryIcon from '@mui/icons-material/History';
import DownloadIcon from '@mui/icons-material/Download';
import FeedbackIcon from '@mui/icons-material/Feedback';
import CloseIcon from '@mui/icons-material/Close';
import RefreshIcon from '@mui/icons-material/Refresh';
// Define feedback structure
interface ArtifactFeedback {
  id?: string;
  from: string;
  timestamp: string;
  content: string;
  rating?: number;
}

// Define reasoning structure
interface Reasoning {
  process?: string;
  steps?: string[];
  thoughts?: string[];
  considerations?: string[];
  decision?: string;
  confidence?: number;
  agentId?: string;
  timestamp?: string;
  conclusion?: string;
  supportingEvidence?: string[];
  insights?: string[];
}

// Define the iteration structure based on the Redis data
interface Iteration {
  version: number;
  timestamp: string;
  agent: string;
  content: string | Record<string, unknown>;
  feedback?: ArtifactFeedback[];
  incorporatedConsultations?: string[];
  reasoning?: Reasoning;
  changes?: string;
}

// Define the artifact structure based on the Redis data
interface Artifact {
  id: string;
  name: string;
  type: string;
  createdBy: string;
  createdAt: string;
  updatedAt?: string;
  currentVersion: number;
  iterations: Iteration[];
  status: string;
  qualityScore: number;
  content?: string | Record<string, unknown>;
  metadata?: Record<string, unknown>;
  data?: unknown;
  agent?: string;
  title?: string;
  creator?: string;
  timestamp?: string;
  created?: string;
  date?: string;
  text?: string;
  reasoningSteps?: unknown[];
}

interface EnhancedArtifactGalleryProps {
  artifacts: unknown; // Accept unknown type as input to handle different structures
  onSendFeedback?: (artifactId: string, feedback: string) => void;
  refreshContent?: () => void;
  loading?: boolean;
}
const EnhancedArtifactGalleryV2: React.FC<EnhancedArtifactGalleryProps> = ({ 
  artifacts,
  onSendFeedback,
  refreshContent,
  loading = false
}): React.ReactElement => {
  const [selectedArtifact, setSelectedArtifact] = useState<Artifact | null>(null);
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState<boolean>(false);
  const [feedbackContent, setFeedbackContent] = useState<string>('');
  const [historyDialogOpen, setHistoryDialogOpen] = useState<boolean>(false);
  const [activeHistoryTab, setActiveHistoryTab] = useState<number>(0);
  const [normalizedArtifacts, setNormalizedArtifacts] = useState<Artifact[]>([]);

  // Functions and useEffect to be added
  // Define normalization function using useMemo to avoid recreation on each render
const normalizeArtifacts = useMemo(() => {  
    return (artifactsInput: unknown): Artifact[] => {
      if (!artifactsInput) {
        return [];
      }
      
      console.log('Normalizing artifacts:', typeof artifactsInput);
      let extractedArtifacts: Artifact[] = [];
      
      try {
        // PRIORITY CASE: Handle the Redis structure (object with artifact IDs as keys)
        if (artifactsInput && typeof artifactsInput === 'object' && !Array.isArray(artifactsInput)) {
          const artifactsObj = artifactsInput as Record<string, unknown>;
          
          // Check if it's the direct artifacts object structure from Redis
          const hasValidArtifacts = Object.values(artifactsObj).some(v => 
            v !== null && typeof v === 'object' && 
            ((v as Record<string, unknown>).type !== undefined || 
             (v as Record<string, unknown>).createdBy !== undefined || 
             (v as Record<string, unknown>).iterations !== undefined)
          );
          
          if (hasValidArtifacts) {
            console.log('Processing Redis-style artifacts object with ID keys');
            const artifacts = Object.entries(artifactsObj).map(([id, data]) => {
              if (!data || typeof data !== 'object') return null;
              
              const artifactData = data as Record<string, unknown>;
              
              // Create artifact from the Redis data structure
              return {
                id: (artifactData.id as string) || id,
                name: (artifactData.name as string) || (artifactData.title as string) || 'Unnamed Artifact',
                type: (artifactData.type as string) || 'unknown',
                createdBy: (artifactData.createdBy as string) || (artifactData.creator as string) || 'Unknown',
                createdAt: (artifactData.createdAt as string) || (artifactData.timestamp as string) || new Date().toISOString(),
                status: (artifactData.status as string) || 'draft',
                content: artifactData.content || '',
                currentVersion: typeof artifactData.currentVersion === 'number' ? artifactData.currentVersion : 
                              (Array.isArray(artifactData.iterations) ? artifactData.iterations.length : 1),
                qualityScore: typeof artifactData.qualityScore === 'number' ? artifactData.qualityScore : 
                            (typeof artifactData.qualityScore === 'string' ? 
                              parseFloat(artifactData.qualityScore) : 0),
                iterations: Array.isArray(artifactData.iterations) ? 
                            artifactData.iterations as Iteration[] : [],
                metadata: (artifactData.metadata as Record<string, unknown>) || {}
              } as Artifact;
            }).filter(Boolean) as Artifact[];
            
            if (artifacts.length > 0) {
              return artifacts.sort((a, b) => {
                const dateA = new Date(a.createdAt).getTime();
                const dateB = new Date(b.createdAt).getTime();
                return dateB - dateA; // newest first
              });
            }
          }
        }
        
        // Handle other cases (arrays, nested objects, etc.)
        // Add the remaining normalization logic
  
      } catch (error) {
        console.error('Error normalizing artifacts:', error);
        return [];
      }
      
      return extractedArtifacts;
    };
  }, []);
  
  // Effect to normalize artifacts when the input changes
  useEffect(() => {
    setNormalizedArtifacts(normalizeArtifacts(artifacts));
  }, [artifacts, normalizeArtifacts]);
  // Helper functions for the component
  const handleOpenArtifact = (artifact: Artifact): void => {
    setSelectedArtifact(artifact);
  };
  
  const handleCloseArtifact = (): void => {
    setSelectedArtifact(null);
  };
  
  const handleOpenFeedbackDialog = (): void => {
    setFeedbackDialogOpen(true);
  };
  
  const handleCloseFeedbackDialog = (): void => {
    setFeedbackDialogOpen(false);
    setFeedbackContent('');
  };
  
  const handleSubmitFeedback = (): void => {
    if (selectedArtifact && feedbackContent.trim() && onSendFeedback) {
      onSendFeedback(selectedArtifact.id, feedbackContent);
      handleCloseFeedbackDialog();
    }
  };
  
  const handleOpenHistoryDialog = (): void => {
    setHistoryDialogOpen(true);
  };
  
  const handleCloseHistoryDialog = (): void => {
    setHistoryDialogOpen(false);
    setActiveHistoryTab(0);
  };
  
  const handleHistoryTabChange = (event: React.SyntheticEvent, newValue: number): void => {
    setActiveHistoryTab(newValue);
  };
  
  // Download artifact as JSON
  const downloadArtifact = (artifact: Artifact): void => {
    const data = JSON.stringify(artifact, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${artifact.name.replace(/\s+/g, '-').toLowerCase()}-v${artifact.currentVersion}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  // Helper function to capitalize first letter
  const capitalizeFirstLetter = (string: string): string => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };
  
  // Format timestamp for display
  const formatTimestamp = (timestamp: string): string => {
    try {
      return new Date(timestamp).toLocaleString();
    } catch (e) {
      return timestamp;
    }
  };
  
  // Render the history dialog
  const renderHistoryDialog = (): React.ReactNode => {
    if (!selectedArtifact) return null;
    
    return (
      <Dialog 
        open={historyDialogOpen} 
        onClose={handleCloseHistoryDialog} 
        maxWidth="md" 
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Version History: {selectedArtifact.name}</Typography>
            <IconButton onClick={handleCloseHistoryDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        
        <DialogContent>
          {/* Implement the history dialog content */}
        </DialogContent>
      </Dialog>
    );
  };
  
  // Render the feedback dialog
  const renderFeedbackDialog = (): React.ReactNode => {
    if (!selectedArtifact) return null;
    
    return (
      <Dialog
        open={feedbackDialogOpen}
        onClose={handleCloseFeedbackDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Provide Feedback</Typography>
            <IconButton onClick={handleCloseFeedbackDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Your Feedback"
            fullWidth
            multiline
            rows={4}
            value={feedbackContent}
            onChange={(e) => setFeedbackContent(e.target.value)}
            variant="outlined"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseFeedbackDialog}>Cancel</Button>
          <Button 
            onClick={handleSubmitFeedback} 
            variant="contained" 
            color="primary"
            disabled={!feedbackContent.trim()}
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>
    );
  };
  
  // Render the artifact detail dialog
  const renderArtifactDetail = (): React.ReactNode => {
    if (!selectedArtifact) return null;
    
    return (
      <Dialog 
        open={selectedArtifact !== null} 
        onClose={handleCloseArtifact}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">{selectedArtifact.name}</Typography>
            <IconButton onClick={handleCloseArtifact}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          {/* Implement the artifact detail content */}
        </DialogContent>
      </Dialog>
    );
  };
  
  return (
    <Box sx={{ p: 2 }}>
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
          <CircularProgress />
          <Typography variant="body1" color="text.secondary" sx={{ ml: 2 }}>
            Loading artifacts...
          </Typography>
        </Box>
      ) : normalizedArtifacts.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="body1" color="text.secondary">
            No artifacts available
          </Typography>
          {refreshContent && (
            <Button 
              startIcon={<RefreshIcon />} 
              onClick={refreshContent} 
              sx={{ mt: 2 }}
            >
              Refresh
            </Button>
          )}
        </Box>
      ) : (
        <>
          {/* Implement the artifact cards display */}
          
          {/* Render dialogs */}
          {renderArtifactDetail()}
          {renderHistoryDialog()}
          {renderFeedbackDialog()}
        </>
      )}
    </Box>
  );
};

export default EnhancedArtifactGalleryV2;