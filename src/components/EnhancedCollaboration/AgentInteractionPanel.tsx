// src/components/EnhancedCollaboration/AgentInteractionPanel.tsx

import React, { useState, useRef, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  TextField, 
  Button, 
  Divider, 
  Avatar, 
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import { v4 as uuidv4 } from 'uuid';
import SendIcon from '@mui/icons-material/Send';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

interface Message {
  id: string;
  timestamp: string;
  from: string;
  to: string;
  role: string;
  parts: any[];
  conversationId: string;
  reasoning?: any;
  intentions?: string[];
  artifactReferences?: string[];
}

interface AgentInteractionPanelProps {
  messages: Message[];
  onSendMessage: (message: any) => void;
}

// Map of agent names to colors
const agentColors: Record<string, string> = {
  'market-research': '#7986CB', // indigo
  'seo-keyword': '#4DB6AC', // teal
  'content-strategy': '#FFB74D', // orange
  'content-generation': '#4FC3F7', // light blue
  'seo-optimization': '#AED581', // light green
  'user': '#9575CD', // purple
  'system': '#78909C' // blue grey
};

const AgentInteractionPanel: React.FC<AgentInteractionPanelProps> = ({ messages, onSendMessage }) => {
  const [newMessage, setNewMessage] = useState('');
  const [expandedReasoningMap, setExpandedReasoningMap] = useState<Record<string, boolean>>({});
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Scroll to bottom whenever messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  const handleSendMessage = () => {
    if (!newMessage.trim()) return;
    
    const messageObject = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'user',
      to: 'system',
      role: 'user',
      parts: [
        {
          type: 'text',
          text: newMessage
        }
      ],
      conversationId: messages.length > 0 ? messages[0].conversationId : uuidv4(),
      intentions: ['request']
    };
    
    onSendMessage(messageObject);
    setNewMessage('');
  };
  
  const toggleReasoning = (messageId: string) => {
    setExpandedReasoningMap(prev => ({
      ...prev,
      [messageId]: !prev[messageId]
    }));
  };
  
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  const getAgentInitials = (agent: string) => {
    return agent
      .split('-')
      .map(word => word[0]?.toUpperCase())
      .join('');
  };
  
  const renderMessageContent = (message: Message) => {
    return message.parts.map((part, index) => {
      if (part.type === 'text') {
        return (
          <Typography key={index} variant="body1" sx={{ whiteSpace: 'pre-wrap', mb: 1 }}>
            {part.text}
          </Typography>
        );
      } else if (part.type === 'data') {
        return (
          <Box key={index} sx={{ mb: 1 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
              Data:
            </Typography>
            <Paper sx={{ p: 1, bgcolor: 'rgba(0,0,0,0.03)' }}>
              <pre style={{ margin: 0, overflow: 'auto', maxHeight: '200px' }}>
                {JSON.stringify(part.data, null, 2)}
              </pre>
            </Paper>
          </Box>
        );
      }
      return null;
    });
  };
  
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '70vh' }}>
      <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
        {messages.length === 0 ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <Typography variant="body1" color="text.secondary">
              No messages yet. Start the conversation!
            </Typography>
          </Box>
        ) : (
          messages.map((message, index) => (
            <Box key={`agent-interaction-${message.id}-${index}-${Math.random().toString(36).substring(2, 9)}`} sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                <Avatar 
                  sx={{ 
                    bgcolor: agentColors[message.from] || '#ccc',
                    width: 32,
                    height: 32,
                    mr: 1,
                    fontSize: '0.875rem'
                  }}
                >
                  {getAgentInitials(message.from)}
                </Avatar>
                
                <Box sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                    <Typography variant="subtitle2">
                      {message.from}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {formatTimestamp(message.timestamp)}
                    </Typography>
                  </Box>
                  
                  <Paper 
                    sx={{ 
                      p: 2,
                      bgcolor: message.from === 'user' ? 'rgba(0,0,0,0.03)' : 'white',
                      borderRadius: 2
                    }}
                  >
                    {renderMessageContent(message)}
                    
                    {message.intentions && message.intentions.length > 0 && (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                        {message.intentions.map(intention => (
                          <Chip 
                            key={intention} 
                            label={intention} 
                            size="small" 
                            variant="outlined"
                            sx={{ fontSize: '0.7rem' }}
                          />
                        ))}
                      </Box>
                    )}
                    
                    {message.artifactReferences && message.artifactReferences.length > 0 && (
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="caption" color="text.secondary">
                          References artifacts: {message.artifactReferences.join(', ')}
                        </Typography>
                      </Box>
                    )}
                  </Paper>
                  
                  {message.reasoning && (
                    <Box sx={{ mt: 1 }}>
                      <Button 
                        size="small" 
                        onClick={() => toggleReasoning(message.id)}
                        endIcon={<InfoOutlinedIcon fontSize="small" />}
                        sx={{ textTransform: 'none' }}
                      >
                        {expandedReasoningMap[message.id] ? 'Hide' : 'Show'} Reasoning
                      </Button>
                      
                      {expandedReasoningMap[message.id] && (
                        <Paper sx={{ p: 2, mt: 1, bgcolor: 'rgba(0,0,0,0.02)', borderRadius: 2 }}>
                          {message.reasoning.thoughts && (
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="subtitle2" gutterBottom>
                                Thoughts:
                              </Typography>
                              <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>
                                {message.reasoning.thoughts.map((thought: string, i: number) => (
                                  <li key={i}>
                                    <Typography variant="body2">{thought}</Typography>
                                  </li>
                                ))}
                              </ul>
                            </Box>
                          )}
                          
                          {message.reasoning.considerations && (
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="subtitle2" gutterBottom>
                                Considerations:
                              </Typography>
                              <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>
                                {message.reasoning.considerations.map((consideration: string, i: number) => (
                                  <li key={i}>
                                    <Typography variant="body2">{consideration}</Typography>
                                  </li>
                                ))}
                              </ul>
                            </Box>
                          )}
                          
                          {message.reasoning.alternatives && (
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="subtitle2" gutterBottom>
                                Alternatives Considered:
                              </Typography>
                              <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>
                                {message.reasoning.alternatives.map((alternative: string, i: number) => (
                                  <li key={i}>
                                    <Typography variant="body2">{alternative}</Typography>
                                  </li>
                                ))}
                              </ul>
                            </Box>
                          )}
                          
                          {message.reasoning.decision && (
                            <Box sx={{ mb: 1 }}>
                              <Typography variant="subtitle2" gutterBottom>
                                Decision:
                              </Typography>
                              <Typography variant="body2">{message.reasoning.decision}</Typography>
                            </Box>
                          )}
                          
                          {message.reasoning.confidence !== undefined && (
                            <Box sx={{ mt: 1 }}>
                              <Typography variant="subtitle2" component="span">
                                Confidence:
                              </Typography>{' '}
                              <Typography variant="body2" component="span">
                                {Math.round(message.reasoning.confidence * 100)}%
                              </Typography>
                            </Box>
                          )}
                        </Paper>
                      )}
                    </Box>
                  )}
                </Box>
              </Box>
              
              {index < messages.length - 1 && <Divider sx={{ my: 2 }} />}
            </Box>
          ))
        )}
        <div ref={messagesEndRef} />
      </Box>
      
      <Divider />
      
      <Box sx={{ p: 2, display: 'flex', alignItems: 'center' }}>
        <TextField
          fullWidth
          multiline
          maxRows={4}
          placeholder="Send a message..."
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          onKeyPress={(e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              handleSendMessage();
            }
          }}
          sx={{ mr: 1 }}
        />
        <IconButton 
          color="primary" 
          onClick={handleSendMessage}
          disabled={!newMessage.trim()}
          sx={{ p: 1 }}
        >
          <SendIcon />
        </IconButton>
      </Box>
    </Box>
  );
};

export default AgentInteractionPanel;
