import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Paper,
  Button,
  Divider,
  Card,
  CardContent,
  TextField,
  IconButton,
  Chip,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Tab,
  CircularProgress
} from '@mui/material';
import Grid from '@mui/material/Grid';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import VisibilityIcon from '@mui/icons-material/Visibility';
import GetAppIcon from '@mui/icons-material/GetApp';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import AssessmentIcon from '@mui/icons-material/Assessment';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface ArticleViewerProps {
  article: {
    title: string;
    content: string;
    seoScore?: number;
    generatedAt?: string;
    contributors?: string[];
    metadata?: {
      wordCount?: number;
      readingTime?: number;
      keywords?: string[];
      targetAudience?: string;
      [key: string]: any;
    };
  };
  onSave?: (updatedArticle: any) => void;
  onEdit?: () => void;
  readOnly?: boolean;
  sessionId?: string;
}

/**
 * Component to display and edit the final article generated by the collaborative agent system
 */
const ArticleViewer: React.FC<ArticleViewerProps> = ({
  article,
  onSave,
  onEdit,
  readOnly = false,
  sessionId
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedTitle, setEditedTitle] = useState(article?.title || '');
  const [editedContent, setEditedContent] = useState(article?.content || '');
  const [activeTab, setActiveTab] = useState(0);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Toggle editing mode
  const toggleEditing = () => {
    setIsEditing(!isEditing);
    if (!isEditing && article) {
      // When entering edit mode, update the state with current values
      setEditedTitle(article.title || '');
      setEditedContent(article.content || '');
    }
  };

  // Save the edited article
  const saveArticle = () => {
    if (onSave && article) {
      onSave({
        ...article,
        title: editedTitle,
        content: editedContent
      });
    }
    setIsEditing(false);
  };

  // Calculate SEO score color based on the value
  const getSeoScoreColor = (score: number) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'primary';
    if (score >= 40) return 'warning';
    return 'error';
  };

  // Function to export article as markdown
  const exportAsMarkdown = () => {
    if (!article.content) return;

    const mdContent = `# ${article.title}\n\n${article.content}`;
    const blob = new Blob([mdContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${article.title.replace(/\s+/g, '-').toLowerCase()}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Function to copy content to clipboard
  const copyToClipboard = () => {
    const fullContent = `# ${article.title}\n\n${article.content}`;
    navigator.clipboard.writeText(fullContent).then(
      () => {
        alert('Content copied to clipboard!');
      },
      (err) => {
        console.error('Could not copy text: ', err);
      }
    );
  };

  return (
    <Box sx={{ mt: 2 }}>
      <Paper elevation={3} sx={{ p: 3, mb: 3, borderRadius: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h5" sx={{ fontWeight: 'bold', color: 'primary.main' }}>Generated Article</Typography>
          <Box>
            {!readOnly && (
              <>
                <Button
                  variant={isEditing ? "outlined" : "contained"}
                  color={isEditing ? "secondary" : "primary"}
                  startIcon={isEditing ? <VisibilityIcon /> : <EditIcon />}
                  onClick={toggleEditing}
                  sx={{ mr: 1 }}
                >
                  {isEditing ? 'Preview' : 'Edit'}
                </Button>
                {isEditing && (
                  <Button
                    variant="contained"
                    color="success"
                    startIcon={<SaveIcon />}
                    onClick={saveArticle}
                    sx={{ mr: 1 }}
                  >
                    Save
                  </Button>
                )}
              </>
            )}
            <Tooltip title="Export as Markdown">
              <IconButton color="primary" onClick={exportAsMarkdown} sx={{ ml: 1 }}>
                <GetAppIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Copy to clipboard">
              <IconButton color="primary" onClick={copyToClipboard} sx={{ ml: 1 }}>
                <ContentCopyIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {article.seoScore && (
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <AssessmentIcon color={getSeoScoreColor(article.seoScore) as any} sx={{ mr: 1 }} />
            <Typography variant="body1" fontWeight="medium">
              SEO Score:
              <Typography component="span" fontWeight="bold" color={`${getSeoScoreColor(article.seoScore)}.main`}>
                {article.seoScore}/100
              </Typography>
            </Typography>
          </Box>
        )}

        {article.metadata?.keywords && article.metadata.keywords.length > 0 && (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
            {article.metadata.keywords.map((keyword, index) => (
              <Chip
                key={index}
                label={keyword}
                size="small"
                color="primary"
                variant="outlined"
              />
            ))}
          </Box>
        )}

        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
        >
          <Tab label={isEditing ? "Edit Article" : "View Article"} />
          <Tab label="Metadata & Info" />
        </Tabs>

        {activeTab === 0 && isEditing ? (
          <Box>
            <TextField
              fullWidth
              label="Title"
              variant="outlined"
              value={editedTitle}
              onChange={(e) => setEditedTitle(e.target.value)}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Content"
              variant="outlined"
              multiline
              rows={20}
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              sx={{ mb: 2, fontFamily: 'monospace' }}
            />
            <Typography variant="caption" color="text.secondary">
              * Content uses Markdown formatting
            </Typography>
          </Box>
        ) : activeTab === 0 ? (
          <Paper
            elevation={2}
            sx={{
              p: 3,
              borderRadius: 2,
              bgcolor: 'background.paper',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
            }}
          >
            <Typography variant="h4" gutterBottom fontWeight="bold">{article.title}</Typography>
            <Divider sx={{ mb: 2 }} />
            {article.content ? (
              <Box sx={{
                typography: 'body1',
                '& img': { maxWidth: '100%', height: 'auto' },
                '& a': { color: 'primary.main' },
                '& h1, & h2, & h3, & h4': { mt: 2, mb: 1, color: 'text.primary', fontWeight: 'medium' },
                '& ul, & ol': { pl: 3 },
                '& blockquote': { borderLeft: '4px solid', borderColor: 'grey.300', pl: 2, fontStyle: 'italic' }
              }}>
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {article.content}
                </ReactMarkdown>
              </Box>
            ) : (
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 4 }}>
                <CircularProgress size={40} sx={{ mb: 2 }} />
                <Typography variant="body1" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                  Content is being generated...
                </Typography>
              </Box>
            )}
          </Paper>
        ) : (
          <Card variant="outlined" sx={{ mt: 2, borderRadius: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom fontWeight="medium" color="primary.main">
                Article Metadata & Information
              </Typography>
              <Divider sx={{ mb: 3 }} />
              <Box display="grid" gridTemplateColumns={{ xs: "1fr", sm: "repeat(2, 1fr)", md: "repeat(3, 1fr)" }} gap={3}>
                {article.seoScore && (
                  <Box>
                    <Paper elevation={1} sx={{ p: 2, height: '100%', borderRadius: 2, bgcolor: `${getSeoScoreColor(article.seoScore)}.50`, position: 'relative' }}>
                      <Box sx={{ position: 'absolute', top: 10, right: 10 }}>
                        <AssessmentIcon color={getSeoScoreColor(article.seoScore) as any} />
                      </Box>
                      <Typography variant="subtitle2" color="text.secondary">SEO Score</Typography>
                      <Typography variant="h4" fontWeight="bold" color={`${getSeoScoreColor(article.seoScore)}.main`}>
                        {article.seoScore}<Typography component="span" variant="body2">/100</Typography>
                      </Typography>
                    </Paper>
                  </Box>
                )}
                {article.metadata?.wordCount && (
                  <Box>
                    <Paper elevation={1} sx={{ p: 2, height: '100%', borderRadius: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary">Word Count</Typography>
                      <Typography variant="h5" fontWeight="medium">{article.metadata.wordCount} <Typography component="span" variant="body2">words</Typography></Typography>
                    </Paper>
                  </Box>
                )}
                {article.metadata?.readingTime && (
                  <Box>
                    <Paper elevation={1} sx={{ p: 2, height: '100%', borderRadius: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary">Reading Time</Typography>
                      <Typography variant="h5" fontWeight="medium">{article.metadata.readingTime} <Typography component="span" variant="body2">min</Typography></Typography>
                    </Paper>
                  </Box>
                )}
                {article.metadata?.targetAudience && (
                  <Box>
                    <Paper elevation={1} sx={{ p: 2, height: '100%', borderRadius: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary">Target Audience</Typography>
                      <Typography variant="body1" fontWeight="medium">{article.metadata.targetAudience}</Typography>
                    </Paper>
                  </Box>
                )}
                {article.metadata?.contentType && (
                  <Box>
                    <Paper elevation={1} sx={{ p: 2, height: '100%', borderRadius: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary">Content Type</Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {article.metadata.contentType.replace(/-/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                      </Typography>
                    </Paper>
                  </Box>
                )}
                {article.generatedAt && (
                  <Box>
                    <Paper elevation={1} sx={{ p: 2, height: '100%', borderRadius: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary">Generated On</Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {new Date(article.generatedAt).toLocaleDateString(undefined, {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </Typography>
                    </Paper>
                  </Box>
                )}
              </Box>

              {article.metadata?.keywords && article.metadata.keywords.length > 0 && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="subtitle1" color="text.secondary" gutterBottom>Keywords</Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                    {article.metadata.keywords.map((keyword, index) => (
                      <Chip
                        key={index}
                        label={keyword}
                        size="medium"
                        color="primary"
                        sx={{ fontWeight: 'medium', px: 1 }}
                      />
                    ))}
                  </Box>
                </Box>
              )}

              {article.contributors && article.contributors.length > 0 && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="subtitle1" color="text.secondary" gutterBottom>Contributors</Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                    {article.contributors.map((contributor, index) => (
                      <Chip
                        key={index}
                        label={contributor.replace(/-/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                        size="medium"
                        variant="outlined"
                        color="secondary"
                        sx={{ fontWeight: 'medium' }}
                      />
                    ))}
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        )}
      </Paper>
    </Box>
  );
};

export default ArticleViewer;
