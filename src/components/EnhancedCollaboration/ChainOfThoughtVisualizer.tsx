import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  Divider,
  Chip,
  Avatar,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Grid,
  CircularProgress
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

// Agent color mapping
const agentColors: Record<string, string> = {
  'market-research': '#ff9800',
  'seo-keyword': '#00bcd4',
  'content-strategy': '#9c27b0',
  'content-generation': '#4caf50',
  'seo-optimization': '#2196f3',
  'system': '#757575',
  'user': '#f44336'
};

// Agent display names
const agentNames: Record<string, string> = {
  'market-research': 'Market Research',
  'seo-keyword': 'SEO Keyword',
  'content-strategy': 'Content Strategy',
  'content-generation': 'Content Writer',
  'seo-optimization': 'SEO Optimizer',
  'system': 'System',
  'user': 'User'
};

interface Decision {
  id: string;
  agent: string;
  agentId?: string;
  timestamp: string | number;
  context?: string;
  reasoning?: string;
  outcome?: string;
  confidence?: number;
  originalReasoning?: any;
}

interface ChainOfThoughtVisualizerProps {
  iterations?: any[];
  messages?: any[];
  consultations?: any[];
  artifacts?: any[] | Record<string, any>;
  decisions?: Decision[] | Record<string, Decision>;
  steps?: any[];
  loading?: boolean;
}

/**
 * Enhanced component to visualize agent reasoning in a chain-of-thought format
 * Focuses on making the reasoning process transparent and understandable
 */
const ChainOfThoughtVisualizer: React.FC<ChainOfThoughtVisualizerProps> = ({
  iterations = [],
  messages = [],
  consultations = [],
  artifacts = [],
  decisions: rawDecisions = [],
  loading = false
}) => {
  // Normalize artifacts data to handle both array and object with IDs as keys
  const normalizedArtifacts = React.useMemo(() => {
    if (Array.isArray(artifacts)) {
      return artifacts;
    } else if (artifacts && typeof artifacts === 'object') {
      // Convert object with IDs as keys to array
      return Object.entries(artifacts).map(([id, artifact]) => {
        if (artifact && typeof artifact === 'object') {
          return {
            id: artifact.id || id,
            ...artifact
          };
        }
        return { id };
      });
    }
    return [];
  }, [artifacts]);
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);
  const [selectedStepId, setSelectedStepId] = useState<string | null>(null);

  // Normalize decisions data
  const decisions = Array.isArray(rawDecisions) ? rawDecisions :
    rawDecisions ? Object.values(rawDecisions) : [];

  // Group decisions by agent
  const decisionsByAgent = decisions.reduce<Record<string, Decision[]>>((acc, decision) => {
    // Use agent or agentId, fallback to 'unknown'
    const agentId = decision.agent || decision.agentId || 'unknown';
    if (!acc[agentId]) {
      acc[agentId] = [];
    }
    acc[agentId].push(decision);
    return acc;
  }, {});

  // Filter by selected agent if one is selected
  const filteredMessages = selectedAgentId
    ? messages.filter(msg => msg.from === selectedAgentId || msg.to === selectedAgentId)
    : messages;

  // Combine all agents involved in the process
  const allAgents = Array.from(new Set([
    ...messages.map(m => m.from),
    ...messages.map(m => Array.isArray(m.to) ? m.to : [m.to]).flat()
  ])).filter(Boolean);

  // Sort messages in chronological order
  const sortedMessages = [...filteredMessages].sort(
    (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );

  // Group messages by conversation thread
  const messageThreads: Record<string, any[]> = {};
  sortedMessages.forEach(message => {
    const threadId = message.inReplyTo || message.id;
    if (!messageThreads[threadId]) {
      messageThreads[threadId] = [];
    }
    messageThreads[threadId].push(message);
  });

  // Extract reasoning data from all messages with improved grouping by agent
  const reasoningData = sortedMessages
    .filter(m => m.reasoning)
    .map(m => ({
      agentId: m.from,
      messageId: m.id,
      timestamp: m.timestamp,
      reasoning: m.reasoning,
      content: m.content,
      type: m.type
    }));

  // Group reasoning data by agent for better organization
  const reasoningByAgent = reasoningData.reduce<Record<string, any[]>>((acc, item) => {
    const agentId = item.agentId || 'unknown';
    if (!acc[agentId]) {
      acc[agentId] = [];
    }
    acc[agentId].push(item);
    return acc;
  }, {});

  // We're already using the grouped decisionsByAgent from earlier
  // Just ensure we handle both agent and agentId fields

  // Get all unique agent IDs from decisions
  const decisionAgents = Object.keys(decisionsByAgent);

  // Combine all agents
  const allAgentIds = Array.from(new Set([
    ...allAgents,
    ...decisionAgents
  ])).filter(Boolean);

  return (
    <Box>
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Agent Selection */}
      <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
        <Typography variant="subtitle1" sx={{ mr: 2, alignSelf: 'center' }}>
          Filter by Agent:
        </Typography>
        {allAgentIds.map(agentId => (
          <Chip
            key={agentId}
            avatar={<Avatar sx={{ bgcolor: agentColors[agentId] || '#757575' }}>
              {(agentNames[agentId] || agentId).charAt(0)}
            </Avatar>}
            label={agentNames[agentId] || agentId}
            onClick={() => setSelectedAgentId(selectedAgentId === agentId ? null : agentId)}
            color={selectedAgentId === agentId ? "primary" : "default"}
            variant={selectedAgentId === agentId ? "filled" : "outlined"}
            sx={{ m: 0.5 }}
          />
        ))}
      </Box>

      <Divider sx={{ my: 2 }} />

      {/* Decisions Section */}
      {decisions.length > 0 && (
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            Agent Decisions & Reasoning
          </Typography>

          {Object.entries(decisionsByAgent)
            .filter(([agentId]) => !selectedAgentId || selectedAgentId === agentId)
            .map(([agentId, agentDecisions]) => (
              <Box key={agentId} sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Avatar sx={{ bgcolor: agentColors[agentId] || '#757575', mr: 1 }}>
                    {(agentNames[agentId] || agentId).charAt(0)}
                  </Avatar>
                  <Typography variant="subtitle1" fontWeight="medium">
                    {agentNames[agentId] || agentId}
                  </Typography>
                </Box>

                {agentDecisions.map((decision) => (
                  <Card key={decision.id} sx={{ mb: 2, borderLeft: `4px solid ${agentColors[agentId] || '#757575'}` }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {decision.context || 'Decision'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {new Date(decision.timestamp).toLocaleString()}
                        </Typography>
                      </Box>

                      <Divider sx={{ mb: 2 }} />

                      <Box display="grid" gridTemplateColumns="repeat(12, 1fr)" gap={2}>
                        <Box gridColumn={{ xs: "span 12", md: "span 6" }}>
                          <Typography variant="body2" fontWeight="medium" gutterBottom>
                            Reasoning:
                          </Typography>
                          <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                            <ReactMarkdown remarkPlugins={[remarkGfm]}>
                              {decision.reasoning || 'No reasoning provided'}
                            </ReactMarkdown>
                          </Paper>
                        </Box>

                        <Box gridColumn={{ xs: "span 12", md: "span 6" }}>
                          <Typography variant="body2" fontWeight="medium" gutterBottom>
                            Outcome:
                          </Typography>
                          <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                            <ReactMarkdown remarkPlugins={[remarkGfm]}>
                              {decision.outcome || 'No outcome provided'}
                            </ReactMarkdown>
                          </Paper>

                          {decision.confidence && (
                            <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
                              <Typography variant="body2" sx={{ mr: 1 }}>
                                Confidence:
                              </Typography>
                              <Chip
                                size="small"
                                label={`${Math.round(decision.confidence * 100)}%`}
                                color={
                                  decision.confidence > 0.8 ? "success" :
                                  decision.confidence > 0.5 ? "primary" : "warning"
                                }
                              />
                            </Box>
                          )}
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            ))}
        </Box>
      )}

      {/* Reasoning Timeline */}
      {reasoningData.length > 0 ? (
        <Box>
          <Typography variant="h6" gutterBottom>
            Chain of Thought Reasoning
          </Typography>

          {/* Display reasoning grouped by agent */}
          {Object.entries(reasoningByAgent)
            .filter(([agentId]) => !selectedAgentId || selectedAgentId === agentId)
            .map(([agentId, agentReasoningData]) => (
              <Box key={agentId} sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Avatar sx={{ bgcolor: agentColors[agentId] || '#757575', mr: 1 }}>
                    {(agentNames[agentId] || agentId).charAt(0)}
                  </Avatar>
                  <Typography variant="subtitle1" fontWeight="medium">
                    {agentNames[agentId] || agentId} Reasoning ({agentReasoningData.length})
                  </Typography>
                </Box>

                {agentReasoningData.map((item, index) => (
                  <Card
                    key={`reasoning-${agentId}-${index}`}
                    sx={{
                      mb: 2,
                      borderLeft: `4px solid ${agentColors[item.agentId] || '#757575'}`,
                      bgcolor: 'background.default'
                    }}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          Reasoning #{index + 1}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {new Date(item.timestamp).toLocaleString()}
                        </Typography>
                      </Box>

                      <Divider sx={{ mb: 2 }} />

                <Box display="grid" gridTemplateColumns="repeat(12, 1fr)" gap={2}>
                  <Box gridColumn={{ xs: "span 12", md: "span 4" }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Reasoning Process
                    </Typography>
                    {item.reasoning.process && (
                      <Paper
                        variant="outlined"
                        sx={{ p: 1, mb: 2, bgcolor: 'background.paper' }}
                      >
                        <Typography variant="body2">
                          {item.reasoning.process}
                        </Typography>
                      </Paper>
                    )}

                    {item.reasoning.steps && (
                      <List dense>
                        {item.reasoning.steps.map((step: string, stepIdx: number) => (
                          <ListItem key={`step-${stepIdx}`} disablePadding>
                            <ListItemText
                              primary={`${stepIdx + 1}. ${step}`}
                              primaryTypographyProps={{ variant: 'body2' }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    )}

                    {item.reasoning.confidence && (
                      <Box sx={{ mt: 1, display: 'flex', alignItems: 'center' }}>
                        <Typography variant="body2" sx={{ mr: 1 }}>
                          Confidence:
                        </Typography>
                        <Chip
                          size="small"
                          label={`${Math.round(item.reasoning.confidence * 100)}%`}
                          color={
                            item.reasoning.confidence > 0.8 ? "success" :
                            item.reasoning.confidence > 0.5 ? "primary" : "warning"
                          }
                        />
                      </Box>
                    )}
                  </Box>

                  <Box gridColumn={{ xs: "span 12", md: "span 8" }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Thinking Process
                    </Typography>

                    {item.reasoning.thoughts && (
                      <Accordion defaultExpanded>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography>Thoughts</Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <List dense>
                            {item.reasoning.thoughts.map((thought: string, thoughtIdx: number) => (
                              <ListItem key={`thought-${thoughtIdx}`} disablePadding>
                                <ListItemText
                                  primary={`• ${thought}`}
                                  primaryTypographyProps={{ variant: 'body2' }}
                                />
                              </ListItem>
                            ))}
                          </List>
                        </AccordionDetails>
                      </Accordion>
                    )}

                    {item.reasoning.considerations && (
                      <Accordion defaultExpanded={false}>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography>Considerations</Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <List dense>
                            {item.reasoning.considerations.map((consideration: string, considerationIdx: number) => (
                              <ListItem key={`consideration-${considerationIdx}`} disablePadding>
                                <ListItemText
                                  primary={`• ${consideration}`}
                                  primaryTypographyProps={{ variant: 'body2' }}
                                />
                              </ListItem>
                            ))}
                          </List>
                        </AccordionDetails>
                      </Accordion>
                    )}

                    {item.reasoning.decision && (
                      <Accordion defaultExpanded>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography>Decision</Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                            {item.reasoning.decision}
                          </Typography>
                        </AccordionDetails>
                      </Accordion>
                    )}

                    {item.reasoning.alternatives && (
                      <Accordion defaultExpanded={false}>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography>Alternative Approaches</Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <List dense>
                            {item.reasoning.alternatives.map((alternative: string, altIdx: number) => (
                              <ListItem key={`alternative-${altIdx}`} disablePadding>
                                <ListItemText
                                  primary={`${altIdx + 1}. ${alternative}`}
                                  primaryTypographyProps={{ variant: 'body2' }}
                                />
                              </ListItem>
                            ))}
                          </List>
                        </AccordionDetails>
                      </Accordion>
                    )}
                  </Box>
                </Box>

                <Box sx={{ mt: 2 }}>
                  <Accordion defaultExpanded={false}>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography>Result</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Paper
                        variant="outlined"
                        sx={{ p: 2, bgcolor: 'background.paper' }}
                      >
                        {typeof item.content === 'string' ? (
                          <ReactMarkdown remarkPlugins={[remarkGfm]}>
                            {item.content}
                          </ReactMarkdown>
                        ) : (
                          <ReactMarkdown remarkPlugins={[remarkGfm]}>
                            {item.content?.text || JSON.stringify(item.content, null, 2)}
                          </ReactMarkdown>
                        )}
                      </Paper>
                    </AccordionDetails>
                  </Accordion>
                </Box>
              </CardContent>
            </Card>
          ))}
        </Box>
      ))}
        </Box>
      ) : (
        <Typography variant="body1" sx={{ textAlign: 'center', py: 4 }}>
          No reasoning data available for the selected agent.
        </Typography>
      )}
    </Box>
  );
};

export default ChainOfThoughtVisualizer;
