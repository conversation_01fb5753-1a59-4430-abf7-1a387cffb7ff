'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Divider,
  Chip,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tabs,
  Tab,
  CircularProgress
} from '@mui/material';
import HistoryIcon from '@mui/icons-material/History';
import DownloadIcon from '@mui/icons-material/Download';
import FeedbackIcon from '@mui/icons-material/Feedback';
import CloseIcon from '@mui/icons-material/Close';
import VisibilityIcon from '@mui/icons-material/Visibility';
import RefreshIcon from '@mui/icons-material/Refresh';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

// Define the iteration structure based on the Redis data
interface Iteration {
  version: number;
  timestamp: string;
  agent: string;
  content: string | Record<string, any>;
  feedback?: Array<{
    id?: string;
    from: string;
    timestamp: string;
    content: string;
    rating?: number;
  }>;
  incorporatedConsultations?: string[];
  reasoning?: {
    process?: string;
    steps?: string[];
    thoughts?: string[];
    considerations?: string[];
    decision?: string;
    confidence?: number;
    agentId?: string;
    timestamp?: string;
    conclusion?: string;
    supportingEvidence?: string[];
    insights?: string[];
  };
  changes?: string;
}

// Define feedback structure with evaluation support
interface ArtifactFeedback {
  id?: string;
  from: string;
  timestamp: string;
  content: string;
  rating?: number;
  evaluation?: {
    score?: number;
    metrics?: Record<string, number | string>;
    criteriaResults?: Array<{
      criterion: string;
      score: number;
      feedback: string;
      suggestions?: string[];
    }>;
  };
}

// Define the evaluation feedback interface
export interface EvaluationFeedback {
  overallScore: number;
  feedback: string;
  criteriaResults: Array<{
    criterion: string;
    score: number;
    feedback: string;
    suggestions?: string[];
  }>;
  suggestions: string[];
  metrics?: Record<string, number | string>;
}

// Define the artifact structure based on the Redis data
interface Artifact {
  id: string;
  name: string;
  type: string;
  createdBy: string;
  createdAt: string;
  updatedAt?: string;
  currentVersion: number;
  iterations: Iteration[];
  status: string;
  qualityScore: number;
  content?: string | Record<string, any>;
  metadata?: Record<string, any>;
  data?: any;
}

interface ArtifactGalleryProps {
  artifacts: any[];
  onSendFeedback?: (artifactId: string, feedback: string) => void;
  onSendEvaluation?: (artifactId: string, evaluation: EvaluationFeedback) => void;
  refreshContent?: () => void;
  loading?: boolean;
}

// Import the FeedbackEvaluationForm component
import FeedbackEvaluationForm, { EvaluationFeedback } from './FeedbackEvaluationForm';

const ArtifactGallery: React.FC<ArtifactGalleryProps> = ({
  artifacts = [],
  onSendFeedback,
  onSendEvaluation,
  refreshContent,
  loading = false
}) => {
  const [selectedArtifact, setSelectedArtifact] = useState<Artifact | null>(null);
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);
  const [feedbackContent, setFeedbackContent] = useState('');
  const [historyDialogOpen, setHistoryDialogOpen] = useState(false);
  const [activeHistoryTab, setActiveHistoryTab] = useState(0);
  const [evaluationDialogOpen, setEvaluationDialogOpen] = useState(false);

  const handleOpenArtifact = (artifact: Artifact) => {
    setSelectedArtifact(artifact);
  };

  const handleCloseArtifact = () => {
    setSelectedArtifact(null);
  };

  const handleOpenFeedbackDialog = () => {
    setFeedbackDialogOpen(true);
  };

  const handleCloseFeedbackDialog = () => {
    setFeedbackDialogOpen(false);
    setFeedbackContent('');
  };

  const handleSubmitFeedback = () => {
    if (selectedArtifact && feedbackContent.trim() && onSendFeedback) {
      onSendFeedback(selectedArtifact.id, feedbackContent);
      handleCloseFeedbackDialog();
    }
  };

  // Evaluation dialog handlers
  const handleOpenEvaluationDialog = () => {
    setEvaluationDialogOpen(true);
  };

  const handleCloseEvaluationDialog = () => {
    setEvaluationDialogOpen(false);
  };

  const handleSubmitEvaluation = (artifactId: string, evaluation: EvaluationFeedback) => {
    if (onSendEvaluation) {
      onSendEvaluation(artifactId, evaluation);
      handleCloseEvaluationDialog();
    }
  };

  const handleOpenHistoryDialog = () => {
    setHistoryDialogOpen(true);
  };

  const handleCloseHistoryDialog = () => {
    setHistoryDialogOpen(false);
    setActiveHistoryTab(0);
  };

  const handleHistoryTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveHistoryTab(newValue);
  };

  const formatTimestamp = (timestamp: string) => {
    try {
      return new Date(timestamp).toLocaleString();
    } catch (e) {
      return timestamp;
    }
  };

  const downloadArtifact = (artifact: Artifact) => {
    const data = JSON.stringify(artifact, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${artifact.name.replace(/\s+/g, '-').toLowerCase()}-v${artifact.currentVersion}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const renderArtifactDetail = () => {
    if (!selectedArtifact) return null;

    return (
      <Dialog
        open={selectedArtifact !== null}
        onClose={handleCloseArtifact}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">{selectedArtifact.name}</Typography>
            <IconButton onClick={handleCloseArtifact}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(12, 1fr)', gap: 2 }}>
            <Box sx={{ gridColumn: 'span 12' }}>
              <Typography variant="subtitle1" gutterBottom>Description</Typography>
              <Typography variant="body1" paragraph>
                {typeof selectedArtifact.content === 'string'
                  ? selectedArtifact.content
                  : selectedArtifact.metadata?.description || `${selectedArtifact.type} artifact`}
              </Typography>
            </Box>

            <Box sx={{ gridColumn: { xs: 'span 12', sm: 'span 6' } }}>
              <Typography variant="subtitle1" gutterBottom>Metadata</Typography>
              <Typography variant="body2">
                <strong>Type:</strong> {selectedArtifact.type}
              </Typography>
              <Typography variant="body2">
                <strong>Status:</strong> {selectedArtifact.status}
              </Typography>
              <Typography variant="body2">
                <strong>Version:</strong> {selectedArtifact.currentVersion}
              </Typography>
              <Typography variant="body2">
                <strong>Quality Score:</strong> {Math.round(selectedArtifact.qualityScore * 100)}%
              </Typography>
              <Typography variant="body2">
                <strong>Created by:</strong> {selectedArtifact.createdBy}
              </Typography>
              <Typography variant="body2">
                <strong>Created at:</strong> {new Date(selectedArtifact.createdAt).toLocaleString()}
              </Typography>
              {selectedArtifact.updatedAt && (
                <Typography variant="body2">
                  <strong>Updated at:</strong> {new Date(selectedArtifact.updatedAt).toLocaleString()}
                </Typography>
              )}
            </Box>

            <Box sx={{ gridColumn: { xs: 'span 12', sm: 'span 6' } }}>
              <Typography variant="subtitle1" gutterBottom>Contributors</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                <Chip
                  label={selectedArtifact.createdBy}
                  size="small"
                  color="info"
                />
                {selectedArtifact.iterations
                  .map(iteration => iteration.agent)
                  .filter((agent, index, self) => self.indexOf(agent) === index && agent !== selectedArtifact.createdBy)
                  .map((contributor, index) => (
                    <Chip
                      key={index}
                      label={contributor}
                      size="small"
                      color="info"
                    />
                  ))}
              </Box>
            </Box>

            <Box sx={{ gridColumn: 'span 12' }}>
              <Typography variant="subtitle1" gutterBottom>Content</Typography>
              {selectedArtifact.iterations.length > 0 && selectedArtifact.iterations[selectedArtifact.currentVersion - 1] && (
                <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                  <Typography variant="subtitle2">Latest Content (Version {selectedArtifact.currentVersion})</Typography>
                  <Box component="pre" sx={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace', m: 0, fontSize: '0.875rem' }}>
                    {typeof selectedArtifact.iterations[selectedArtifact.currentVersion - 1].content === 'string'
                      ? selectedArtifact.iterations[selectedArtifact.currentVersion - 1].content
                      : typeof selectedArtifact.iterations[selectedArtifact.currentVersion - 1].content === 'object' &&
                        selectedArtifact.iterations[selectedArtifact.currentVersion - 1].content !== null
                          ? JSON.stringify(selectedArtifact.iterations[selectedArtifact.currentVersion - 1].content, null, 2)
                          : 'No content available'}
                  </Box>
                </Paper>
              )}
            </Box>

            <Box sx={{ gridColumn: 'span 12' }}>
              <Typography variant="subtitle1" gutterBottom>Feedback</Typography>
              {selectedArtifact.iterations.some(iter => iter.feedback && iter.feedback.length > 0) ? (
                selectedArtifact.iterations
                  .flatMap(iter => iter.feedback || [])
                  .map((feedback, index) => (
                    <Paper key={index} variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Typography variant="subtitle2">
                        From: {feedback.from}
                        <Typography component="span" variant="caption" sx={{ ml: 1 }}>
                          ({new Date(feedback.timestamp).toLocaleString()})
                        </Typography>
                        {feedback.evaluation?.score !== undefined && (
                          <Chip
                            size="small"
                            color={
                              feedback.evaluation.score >= 0.7 ? 'success' :
                              feedback.evaluation.score >= 0.4 ? 'warning' : 'error'
                            }
                            label={`Score: ${Math.round(feedback.evaluation.score * 100)}%`}
                            sx={{ ml: 1 }}
                          />
                        )}
                      </Typography>

                      <Typography variant="body1" sx={{ mt: 1 }}>
                        {feedback.content}
                      </Typography>

                      {feedback.rating !== undefined && (
                        <Typography variant="body2">
                          Rating: {feedback.rating}/5
                        </Typography>
                      )}

                      {feedback.evaluation?.criteriaResults && feedback.evaluation.criteriaResults.length > 0 && (
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Evaluation Criteria
                          </Typography>

                          {feedback.evaluation.criteriaResults.map((criterion, criterionIndex) => (
                            <Box key={criterionIndex} sx={{ mb: 1 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Typography variant="body2" sx={{ fontWeight: 'bold', mr: 1 }}>
                                  {criterion.criterion}:
                                </Typography>
                                <Chip
                                  size="small"
                                  color={
                                    criterion.score >= 0.7 ? 'success' :
                                    criterion.score >= 0.4 ? 'warning' : 'error'
                                  }
                                  label={`${Math.round(criterion.score * 100)}%`}
                                />
                              </Box>

                              <Typography variant="body2" sx={{ ml: 2 }}>
                                {criterion.feedback}
                              </Typography>

                              {criterion.suggestions && criterion.suggestions.length > 0 && (
                                <Box sx={{ ml: 2, mt: 0.5 }}>
                                  <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
                                    Suggestions:
                                  </Typography>
                                  <ul style={{ margin: '4px 0', paddingLeft: '20px' }}>
                                    {criterion.suggestions.map((suggestion, suggestionIndex) => (
                                      <li key={suggestionIndex}>
                                        <Typography variant="body2">
                                          {suggestion}
                                        </Typography>
                                      </li>
                                    ))}
                                  </ul>
                                </Box>
                              )}
                            </Box>
                          ))}
                        </Box>
                      )}
                    </Paper>
                  ))
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No feedback yet.
                </Typography>
              )}
            </Box>

            {selectedArtifact.iterations.some(iter => iter.reasoning) && (
              <Box sx={{ gridColumn: 'span 12' }}>
                <Typography variant="subtitle1" gutterBottom>Reasoning</Typography>
                {selectedArtifact.iterations
                  .filter(iter => iter.reasoning)
                  .map((iteration, index) => (
                    <Paper key={index} variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Typography variant="subtitle2">
                        Version {iteration.version} Reasoning
                      </Typography>
                      <Typography variant="body2" gutterBottom>
                        <strong>Process:</strong> {iteration.reasoning?.process}
                      </Typography>
                      {iteration.reasoning?.steps && iteration.reasoning.steps.length > 0 && (
                        <>
                          <Typography variant="body2" gutterBottom>
                            <strong>Steps:</strong>
                          </Typography>
                          <ul>
                            {iteration.reasoning.steps.map((step, stepIndex) => (
                              <li key={stepIndex}>{step}</li>
                            ))}
                          </ul>
                        </>
                      )}
                      {iteration.reasoning?.decision && (
                        <Typography variant="body2">
                          <strong>Decision:</strong> {iteration.reasoning.decision}
                          {iteration.reasoning.confidence !== undefined && ` (Confidence: ${Math.round(iteration.reasoning.confidence * 100)}%)`}
                        </Typography>
                      )}
                    </Paper>
                  ))}
              </Box>
            )}
          </Box>
        </DialogContent>

        <DialogActions>
          <Button
            startIcon={<HistoryIcon />}
            onClick={handleOpenHistoryDialog}
          >
            History
          </Button>
          <Button
            startIcon={<FeedbackIcon />}
            onClick={handleOpenFeedbackDialog}
          >
            Add Feedback
          </Button>
          {onSendEvaluation && (
            <Button
              startIcon={<StarIcon />}
              onClick={handleOpenEvaluationDialog}
              color="secondary"
            >
              Evaluate
            </Button>
          )}
          <Button
            startIcon={<DownloadIcon />}
            onClick={() => downloadArtifact(selectedArtifact)}
          >
            Download
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  const renderHistoryDialog = () => {
    if (!selectedArtifact) return null;

    return (
      <Dialog
        open={historyDialogOpen}
        onClose={handleCloseHistoryDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Version History: {selectedArtifact.name}</Typography>
            <IconButton onClick={handleCloseHistoryDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent>
          {selectedArtifact.iterations.length === 0 ? (
            <Typography variant="body2" color="text.secondary">
              No history available for this artifact.
            </Typography>
          ) : (
            <>
              <Tabs
                value={activeHistoryTab}
                onChange={handleHistoryTabChange}
                variant="scrollable"
                scrollButtons="auto"
              >
                {selectedArtifact.iterations.map((iteration, index) => (
                  <Tab
                    key={index}
                    label={`v${iteration.version}`}
                  />
                ))}
              </Tabs>

              <Box sx={{ mt: 2 }}>
                {selectedArtifact.iterations.map((iteration, index) => (
                  <Box
                    key={index}
                    sx={{
                      display: activeHistoryTab === index ? 'block' : 'none',
                      mt: 2
                    }}
                  >
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2">
                        Version {iteration.version}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" display="block">
                        {formatTimestamp(iteration.timestamp)} by {iteration.agent}
                      </Typography>
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        {iteration.changes || 'Content updated'}
                      </Typography>
                    </Box>

                    <Divider sx={{ my: 2 }} />

                    <Typography variant="subtitle2" gutterBottom>
                      Content at v{iteration.version}
                    </Typography>

                    <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                      <Box component="pre" sx={{ margin: 0, whiteSpace: 'pre-wrap', fontFamily: 'monospace', fontSize: '0.875rem' }}>
                        {typeof iteration.content === 'string'
                          ? iteration.content
                          : typeof iteration.content === 'object' && iteration.content !== null
                            ? JSON.stringify(iteration.content, null, 2)
                            : 'No content available'}
                      </Box>
                    </Paper>

                    {iteration.reasoning && (
                      <Box sx={{ mt: 3 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Reasoning
                        </Typography>
                        <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                          <Box component="pre" sx={{ margin: 0, whiteSpace: 'pre-wrap', fontFamily: 'monospace', fontSize: '0.875rem' }}>
                            {typeof iteration.reasoning === 'string'
                              ? iteration.reasoning
                              : typeof iteration.reasoning === 'object' && iteration.reasoning !== null
                                ? JSON.stringify(iteration.reasoning, null, 2)
                                : 'No reasoning available'}
                          </Box>
                        </Paper>
                      </Box>
                    )}
                  </Box>
                ))}
              </Box>
            </>
          )}
        </DialogContent>
      </Dialog>
    );
  };

  const renderFeedbackDialog = () => {
    if (!selectedArtifact) return null;

    return (
      <Dialog
        open={feedbackDialogOpen}
        onClose={handleCloseFeedbackDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Add Feedback</Typography>
            <IconButton onClick={handleCloseFeedbackDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            id="feedback"
            label="Your Feedback"
            fullWidth
            multiline
            rows={4}
            value={feedbackContent}
            onChange={(e) => setFeedbackContent(e.target.value)}
          />
        </DialogContent>

        <DialogActions>
          <Button onClick={handleCloseFeedbackDialog}>Cancel</Button>
          <Button
            onClick={handleSubmitFeedback}
            variant="contained"
            disabled={!feedbackContent.trim()}
          >
            Submit Feedback
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Render the evaluation dialog with structured feedback form
  const renderEvaluationDialog = () => {
    if (!selectedArtifact) return null;

    return (
      <Dialog
        open={evaluationDialogOpen}
        onClose={handleCloseEvaluationDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Evaluate Artifact</Typography>
            <IconButton onClick={handleCloseEvaluationDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <FeedbackEvaluationForm
            artifactId={selectedArtifact.id}
            artifactType={selectedArtifact.type}
            onSubmit={handleSubmitEvaluation}
            onCancel={handleCloseEvaluationDialog}
          />
        </DialogContent>
      </Dialog>
    );
  };

  // Group artifacts by type
  const groupedArtifacts: Record<string, Artifact[]> = {};
  artifacts.forEach(artifact => {
    if (!groupedArtifacts[artifact.type]) {
      groupedArtifacts[artifact.type] = [];
    }
    groupedArtifacts[artifact.type].push(artifact);
  });

  // Add console log for debugging
  console.log('ArtifactGallery received artifacts:', artifacts);

  return (
    <Box sx={{ p: 2 }}>
      {artifacts.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
          <Typography variant="body1" color="text.secondary">
            No artifacts available yet. They will appear here as agents create them.
          </Typography>
        </Box>
      ) : (
        Object.entries(groupedArtifacts).map(([artifactType, typeArtifacts]) => (
          <Box key={artifactType} sx={{ mb: 4 }}>
            <Typography variant="h5" sx={{ mb: 2 }}>
              {artifactType.charAt(0).toUpperCase() + artifactType.slice(1).replace(/-/g, ' ')}
            </Typography>

            <Box sx={{ display: 'grid', gridTemplateColumns: { xs: 'repeat(1, 1fr)', sm: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)' }, gap: 2 }}>
              {typeArtifacts.map((artifact, index) => (
                <Card key={artifact.id || `artifact-${index}`} variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" noWrap>
                      {artifact.name}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1, color: 'text.secondary' }}>
                      {Array.isArray(artifact.iterations) && artifact.iterations.length > 0 ?
                       `Version ${artifact.currentVersion || 1}/${artifact.iterations.length}` :
                       `Version ${artifact.currentVersion || 1}`}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                    }}>
                      {artifact.type}
                    </Typography>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
                      <Chip
                        size="small"
                        label={artifact.status}
                        color={
                          artifact.status === 'completed' ? 'success' :
                          artifact.status === 'in-progress' ? 'info' :
                          artifact.status === 'draft' ? 'default' : 'warning'
                        }
                      />
                      <Typography variant="body2" color="text.secondary" noWrap>
                        {artifact.createdAt ? formatTimestamp(artifact.createdAt) : 'Unknown date'}
                      </Typography>
                    </Box>
                  </CardContent>
                  <CardActions>
                    <Button
                      size="small"
                      onClick={() => handleOpenArtifact(artifact)}
                    >
                      View Details
                    </Button>
                    <Button
                      size="small"
                      startIcon={<DownloadIcon />}
                      onClick={(e) => {
                        e.stopPropagation();
                        downloadArtifact(artifact);
                      }}
                    >
                      Download
                    </Button>
                  </CardActions>
                </Card>
              ))}
            </Box>
          </Box>
        ))
      )}

      {renderArtifactDetail()}
      {renderHistoryDialog()}
      {renderFeedbackDialog()}
      {renderEvaluationDialog()}
    </Box>
  );
};

export default ArtifactGallery;
