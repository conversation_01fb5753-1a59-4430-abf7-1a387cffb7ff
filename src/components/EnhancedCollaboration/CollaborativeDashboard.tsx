// src/components/EnhancedCollaboration/CollaborativeDashboard.tsx

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Button,
  CircularProgress,
  <PERSON>ert,
  <PERSON><PERSON>kbar,
  Tabs,
  Tab
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import CollaborativeWorkflowVisualizer from './CollaborativeWorkflowVisualizer';
import AgentReasoningVisualizer from './AgentReasoningVisualizer';
import ArtifactGallery from './ArtifactGallery';

// Import the EvaluationFeedback interface
import { EvaluationFeedback } from './FeedbackEvaluationForm';

interface CollaborativeDashboardProps {
  sessionId: string;
  initialState?: any;
  onStartCollaboration?: (params: any) => Promise<any>;
  onRefreshState?: (sessionId: string) => Promise<any>;
  onSendFeedback?: (sessionId: string, artifactId: string, feedback: string) => Promise<any>;
  onSendEvaluation?: (sessionId: string, artifactId: string, evaluation: EvaluationFeedback) => Promise<any>;
}

/**
 * Dashboard component for monitoring and interacting with the collaborative agent system
 */
const CollaborativeDashboard: React.FC<CollaborativeDashboardProps> = ({
  sessionId,
  initialState,
  onStartCollaboration,
  onRefreshState,
  onSendFeedback,
  onSendEvaluation
}) => {
  // State
  const [state, setState] = useState<any>(initialState || {});
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState(0);
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' | 'info' | 'warning' }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Effect to initialize state
  useEffect(() => {
    if (initialState) {
      setState(initialState);
    } else if (sessionId) {
      refreshState();
    }
  }, [sessionId, initialState]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
  };

  // Refresh state
  const refreshState = async () => {
    if (!onRefreshState || !sessionId) return;

    setLoading(true);
    try {
      const updatedState = await onRefreshState(sessionId);
      setState(updatedState);
      setSnackbar({
        open: true,
        message: 'Dashboard refreshed successfully',
        severity: 'success'
      });
    } catch (err) {
      console.error('Error refreshing state:', err);
      setError('Failed to refresh dashboard state');
      setSnackbar({
        open: true,
        message: 'Failed to refresh dashboard state',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Start collaboration
  const startCollaboration = async (params: any) => {
    if (!onStartCollaboration) return;

    setLoading(true);
    try {
      const result = await onStartCollaboration(params);
      setState(result);
      setSnackbar({
        open: true,
        message: 'Collaboration started successfully',
        severity: 'success'
      });
    } catch (err) {
      console.error('Error starting collaboration:', err);
      setError('Failed to start collaboration');
      setSnackbar({
        open: true,
        message: 'Failed to start collaboration',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle sending feedback
  const handleSendFeedback = async (artifactId: string, feedback: string) => {
    if (!onSendFeedback || !sessionId) return;

    setLoading(true);
    try {
      const result = await onSendFeedback(sessionId, artifactId, feedback);
      setState(result);
      setSnackbar({
        open: true,
        message: 'Feedback sent successfully',
        severity: 'success'
      });
    } catch (err) {
      console.error('Error sending feedback:', err);
      setSnackbar({
        open: true,
        message: 'Failed to send feedback',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Extract artifacts from state
  const artifacts = state?.artifacts ? Object.values(state.artifacts) : [];

  // Extract iterations for agent reasoning visualization
  const allIterations = artifacts.flatMap((artifact: any) =>
    artifact.iterations.map((iteration: any) => ({
      ...iteration,
      artifactId: artifact.id,
      artifactType: artifact.type,
      artifactName: artifact.name,
      agent: artifact.createdBy
    }))
  );

  return (
    <Box sx={{ p: 2 }}>
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h4">
            Collaborative Content Creation
          </Typography>
          <Box>
            <Button
              variant="contained"
              color="primary"
              startIcon={state?.status === 'active' ? <PauseIcon /> : <PlayArrowIcon />}
              onClick={() => startCollaboration({ topic: 'SEO Optimization', contentType: 'blog' })}
              disabled={loading || state?.status === 'active'}
              sx={{ mr: 1 }}
            >
              {state?.status === 'active' ? 'Pause' : 'Start'} Collaboration
            </Button>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={refreshState}
              disabled={loading}
            >
              Refresh
            </Button>
          </Box>
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Session Info */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>Session ID</Typography>
                <Typography variant="body1">{sessionId || 'No active session'}</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>Status</Typography>
                <Typography variant="body1">
                  {loading ? (
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CircularProgress size={20} sx={{ mr: 1 }} />
                      Loading...
                    </Box>
                  ) : (
                    state?.status || 'Not started'
                  )}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>Current Phase</Typography>
                <Typography variant="body1">{state?.currentPhase || 'Not started'}</Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Main Content Tabs */}
        <Tabs value={selectedTab} onChange={handleTabChange} aria-label="dashboard tabs">
          <Tab label="Workflow Visualization" />
          <Tab label="Artifacts" />
          <Tab label="Agent Reasoning" />
        </Tabs>

        {/* Workflow Visualization Tab */}
        <Box hidden={selectedTab !== 0} sx={{ mt: 2 }}>
          <CollaborativeWorkflowVisualizer
            sessionId={sessionId}
            state={state}
            onRefresh={refreshState}
          />
        </Box>

        {/* Artifacts Tab */}
        <Box hidden={selectedTab !== 1} sx={{ mt: 2 }}>
          <ArtifactGallery
            artifacts={artifacts}
            onSendFeedback={handleSendFeedback}
          />
        </Box>

        {/* Agent Reasoning Tab */}
        <Box hidden={selectedTab !== 2} sx={{ mt: 2 }}>
          <AgentReasoningVisualizer iterations={allIterations} />
        </Box>
      </Paper>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CollaborativeDashboard;
