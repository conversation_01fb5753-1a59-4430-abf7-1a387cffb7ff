'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  LinearProgress,
  CircularProgress,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Tooltip,
  Alert,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import { styled } from '@mui/material/styles';
import RefreshIcon from '@mui/icons-material/Refresh';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import SpeedIcon from '@mui/icons-material/Speed';
import MemoryIcon from '@mui/icons-material/Memory';
import StorageIcon from '@mui/icons-material/Storage';
import TimelineIcon from '@mui/icons-material/Timeline';
import AssessmentIcon from '@mui/icons-material/Assessment';
import { IterativeCollaborationState } from '../../app/(payload)/api/agents/collaborative-iteration/types';

// Styled components
const ProgressLabel = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(0.5),
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
}));

const MetricCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
}));

const MetricCardContent = styled(CardContent)({
  flexGrow: 1,
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'center',
  textAlign: 'center',
});

interface WorkflowMonitoringDashboardProps {
  sessionId: string;
  state: IterativeCollaborationState;
  onRefresh?: () => void;
  loading?: boolean;
}

/**
 * Comprehensive monitoring dashboard for workflow progress
 */
const WorkflowMonitoringDashboard: React.FC<WorkflowMonitoringDashboardProps> = ({
  sessionId,
  state,
  onRefresh,
  loading = false
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [refreshInterval, setRefreshInterval] = useState<number>(10000); // 10 seconds
  const [lastRefreshTime, setLastRefreshTime] = useState<Date>(new Date());
  const [phaseMetrics, setPhaseMetrics] = useState<any>({});
  const [agentMetrics, setAgentMetrics] = useState<any>({});
  const [systemMetrics, setSystemMetrics] = useState<any>({});

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Calculate phase metrics
  useEffect(() => {
    if (!state) return;

    // Calculate phase durations and status
    const phases = ['initialization', 'research', 'content-generation', 'review', 'finalization'];
    const currentPhase = state.currentPhase || 'initialization';
    const currentPhaseIndex = phases.indexOf(currentPhase);
    
    const phaseData: Record<string, any> = {};
    
    // Phase display names
    const phaseDisplayNames: Record<string, string> = {
      'initialization': 'Initialization',
      'research': 'Research Phase',
      'content-generation': 'Content Generation',
      'review': 'Review & Optimization',
      'finalization': 'Finalization'
    };
    
    // Calculate phase metrics
    phases.forEach((phase, index) => {
      // Determine phase status
      let status: 'completed' | 'active' | 'pending' = 'pending';
      
      if (index < currentPhaseIndex) {
        status = 'completed';
      } else if (index === currentPhaseIndex) {
        status = 'active';
      }
      
      // Calculate phase progress
      let progress = 0;
      
      if (status === 'completed') {
        progress = 100;
      } else if (status === 'active') {
        // Calculate based on workflowProgress
        if (state.workflowProgress) {
          switch (phase) {
            case 'research':
              if (state.workflowProgress.marketResearchComplete && 
                  state.workflowProgress.keywordResearchComplete && 
                  state.workflowProgress.contentStrategyComplete) {
                progress = 100;
              } else if (state.workflowProgress.marketResearchComplete && 
                         state.workflowProgress.keywordResearchComplete) {
                progress = 75;
              } else if (state.workflowProgress.marketResearchComplete) {
                progress = 50;
              } else {
                progress = 25;
              }
              break;
            case 'content-generation':
              progress = state.workflowProgress.contentGenerationComplete ? 100 : 50;
              break;
            case 'review':
              progress = state.workflowProgress.seoOptimizationComplete ? 100 : 50;
              break;
            default:
              progress = 50;
          }
        }
      }
      
      // Calculate artifacts for this phase
      const phaseArtifacts = getPhaseArtifacts(phase);
      
      // Store phase data
      phaseData[phase] = {
        name: phaseDisplayNames[phase] || phase,
        status,
        progress,
        artifacts: phaseArtifacts,
        startTime: getPhaseStartTime(phase),
        endTime: status === 'completed' ? getPhaseEndTime(phase) : null,
        duration: status === 'completed' ? getPhaseDuration(phase) : null
      };
    });
    
    setPhaseMetrics(phaseData);
    
    // Calculate agent metrics
    calculateAgentMetrics();
    
    // Calculate system metrics
    calculateSystemMetrics();
    
  }, [state]);

  // Get artifacts for a specific phase
  const getPhaseArtifacts = (phase: string): any[] => {
    if (!state || !state.artifacts || !state.generatedArtifacts) return [];
    
    const phaseArtifactTypes: Record<string, string[]> = {
      'research': ['market-research-report', 'keyword-analysis', 'content-strategy'],
      'content-generation': ['content-draft', 'blog-content', 'generated-content'],
      'review': ['seo-optimized-content'],
      'finalization': ['final-content']
    };
    
    const artifactTypes = phaseArtifactTypes[phase] || [];
    
    return state.generatedArtifacts
      .map(id => state.artifacts[id])
      .filter(artifact => artifactTypes.includes(artifact?.type));
  };

  // Get phase start time (estimated)
  const getPhaseStartTime = (phase: string): Date | null => {
    // This is a placeholder - in a real implementation, you would track actual phase start times
    return new Date();
  };

  // Get phase end time (estimated)
  const getPhaseEndTime = (phase: string): Date | null => {
    // This is a placeholder - in a real implementation, you would track actual phase end times
    return new Date();
  };

  // Get phase duration (estimated)
  const getPhaseDuration = (phase: string): number | null => {
    // This is a placeholder - in a real implementation, you would calculate actual phase durations
    const phaseDurations: Record<string, number> = {
      'initialization': 30, // seconds
      'research': 240, // seconds
      'content-generation': 300, // seconds
      'review': 180, // seconds
      'finalization': 60 // seconds
    };
    
    return phaseDurations[phase] || null;
  };

  // Calculate agent metrics
  const calculateAgentMetrics = () => {
    if (!state || !state.messages) return;
    
    const agents: Record<string, any> = {};
    
    // Count messages by agent
    state.messages.forEach(message => {
      const agentId = message.from;
      
      if (!agentId) return;
      
      if (!agents[agentId]) {
        agents[agentId] = {
          id: agentId,
          messageCount: 0,
          artifactCount: 0,
          lastActive: null
        };
      }
      
      agents[agentId].messageCount++;
      agents[agentId].lastActive = message.timestamp;
    });
    
    // Count artifacts by agent
    if (state.artifacts && state.generatedArtifacts) {
      state.generatedArtifacts.forEach(id => {
        const artifact = state.artifacts[id];
        
        if (!artifact || !artifact.createdBy) return;
        
        const agentId = artifact.createdBy;
        
        if (!agents[agentId]) {
          agents[agentId] = {
            id: agentId,
            messageCount: 0,
            artifactCount: 0,
            lastActive: null
          };
        }
        
        agents[agentId].artifactCount++;
      });
    }
    
    setAgentMetrics(agents);
  };

  // Calculate system metrics
  const calculateSystemMetrics = () => {
    if (!state) return;
    
    // These would be real metrics in a production system
    const metrics = {
      totalMessages: state.messages?.length || 0,
      totalArtifacts: state.generatedArtifacts?.length || 0,
      averageQualityScore: calculateAverageQualityScore(),
      startTime: state.startTime ? new Date(state.startTime) : new Date(),
      totalDuration: calculateTotalDuration(),
      estimatedTimeRemaining: calculateEstimatedTimeRemaining()
    };
    
    setSystemMetrics(metrics);
  };

  // Calculate average quality score
  const calculateAverageQualityScore = (): number => {
    if (!state || !state.artifacts || !state.generatedArtifacts) return 0;
    
    const artifacts = state.generatedArtifacts
      .map(id => state.artifacts[id])
      .filter(artifact => artifact?.qualityScore !== undefined);
    
    if (artifacts.length === 0) return 0;
    
    const totalScore = artifacts.reduce((sum, artifact) => sum + (artifact.qualityScore || 0), 0);
    return totalScore / artifacts.length;
  };

  // Calculate total workflow duration
  const calculateTotalDuration = (): number => {
    if (!state || !state.startTime) return 0;
    
    const startTime = new Date(state.startTime).getTime();
    const endTime = state.endTime ? new Date(state.endTime).getTime() : Date.now();
    
    return Math.floor((endTime - startTime) / 1000); // Duration in seconds
  };

  // Calculate estimated time remaining
  const calculateEstimatedTimeRemaining = (): number => {
    if (!state) return 0;
    
    // This is a placeholder - in a real implementation, you would calculate based on phase progress
    const phases = ['initialization', 'research', 'content-generation', 'review', 'finalization'];
    const currentPhase = state.currentPhase || 'initialization';
    const currentPhaseIndex = phases.indexOf(currentPhase);
    
    // Phase time estimates in seconds
    const phaseTimeEstimates: Record<string, number> = {
      'initialization': 30,
      'research': 240,
      'content-generation': 300,
      'review': 180,
      'finalization': 60
    };
    
    // Calculate remaining time
    let remainingTime = 0;
    
    // Add time for current phase (proportional to remaining progress)
    const currentPhaseProgress = phaseMetrics[currentPhase]?.progress || 0;
    const currentPhaseRemaining = (100 - currentPhaseProgress) / 100;
    remainingTime += (phaseTimeEstimates[currentPhase] || 0) * currentPhaseRemaining;
    
    // Add time for remaining phases
    for (let i = currentPhaseIndex + 1; i < phases.length; i++) {
      remainingTime += phaseTimeEstimates[phases[i]] || 0;
    }
    
    return Math.floor(remainingTime);
  };

  // Format duration in seconds to human-readable format
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds} sec`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes} min ${remainingSeconds} sec`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours} hr ${minutes} min`;
    }
  };

  // Handle manual refresh
  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
      setLastRefreshTime(new Date());
    }
  };

  if (loading) {
    return (
      <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
          <CircularProgress />
          <Typography variant="body1" sx={{ ml: 2 }}>
            Loading monitoring data...
          </Typography>
        </Box>
      </Paper>
    );
  }

  return (
    <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" gutterBottom>
          Workflow Monitoring Dashboard
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="caption" color="text.secondary" sx={{ mr: 2 }}>
            Last updated: {lastRefreshTime.toLocaleTimeString()}
          </Typography>
          
          <Tooltip title="Refresh Data">
            <IconButton onClick={handleRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
      
      <Typography variant="body2" color="text.secondary" paragraph>
        Real-time monitoring of workflow progress, agent activity, and system performance.
      </Typography>
      
      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard>
            <MetricCardContent>
              <Avatar sx={{ bgcolor: 'primary.main', width: 56, height: 56, mb: 2 }}>
                <TimelineIcon fontSize="large" />
              </Avatar>
              <Typography variant="h4" gutterBottom>
                {systemMetrics.totalMessages || 0}
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Total Messages
              </Typography>
            </MetricCardContent>
          </MetricCard>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard>
            <MetricCardContent>
              <Avatar sx={{ bgcolor: 'secondary.main', width: 56, height: 56, mb: 2 }}>
                <StorageIcon fontSize="large" />
              </Avatar>
              <Typography variant="h4" gutterBottom>
                {systemMetrics.totalArtifacts || 0}
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Generated Artifacts
              </Typography>
            </MetricCardContent>
          </MetricCard>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard>
            <MetricCardContent>
              <Avatar sx={{ bgcolor: 'success.main', width: 56, height: 56, mb: 2 }}>
                <AccessTimeIcon fontSize="large" />
              </Avatar>
              <Typography variant="h4" gutterBottom>
                {formatDuration(systemMetrics.totalDuration || 0)}
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Total Duration
              </Typography>
            </MetricCardContent>
          </MetricCard>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard>
            <MetricCardContent>
              <Avatar sx={{ bgcolor: 'warning.main', width: 56, height: 56, mb: 2 }}>
                <SpeedIcon fontSize="large" />
              </Avatar>
              <Typography variant="h4" gutterBottom>
                {Math.round((systemMetrics.averageQualityScore || 0) * 100)}%
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Avg. Quality Score
              </Typography>
            </MetricCardContent>
          </MetricCard>
        </Grid>
      </Grid>
      
      <Divider sx={{ mb: 3 }} />
      
      {/* Tabs */}
      <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 3 }}>
        <Tab
          label="Phase Progress"
          icon={<AssessmentIcon />}
          iconPosition="start"
        />
        <Tab
          label="Agent Activity"
          icon={<MemoryIcon />}
          iconPosition="start"
        />
        <Tab
          label="System Metrics"
          icon={<SpeedIcon />}
          iconPosition="start"
        />
      </Tabs>
      
      {/* Phase Progress Tab */}
      <Box hidden={activeTab !== 0}>
        <Typography variant="subtitle1" gutterBottom>
          Phase Progress Tracking
        </Typography>
        
        <TableContainer component={Paper} variant="outlined" sx={{ mb: 3 }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Phase</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Progress</TableCell>
                <TableCell>Artifacts</TableCell>
                <TableCell>Duration</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Object.entries(phaseMetrics).map(([phaseId, phase]: [string, any]) => (
                <TableRow key={phaseId}>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {phase.name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={phase.status === 'completed' ? 'Completed' : 
                             phase.status === 'active' ? 'In Progress' : 'Pending'}
                      color={phase.status === 'completed' ? 'success' : 
                             phase.status === 'active' ? 'primary' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ width: '100%', mr: 1 }}>
                      <LinearProgress 
                        variant="determinate" 
                        value={phase.progress} 
                        color={phase.status === 'completed' ? 'success' : 'primary'}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                        {phase.progress}%
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    {phase.artifacts.length} / {phaseId === 'research' ? 3 : 
                                               phaseId === 'content-generation' ? 1 : 
                                               phaseId === 'review' ? 1 : 
                                               phaseId === 'finalization' ? 1 : 0}
                  </TableCell>
                  <TableCell>
                    {phase.duration ? formatDuration(phase.duration) : '-'}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        <Alert severity="info">
          <Typography variant="body2">
            Estimated time remaining: {formatDuration(systemMetrics.estimatedTimeRemaining || 0)}
          </Typography>
        </Alert>
      </Box>
      
      {/* Agent Activity Tab */}
      <Box hidden={activeTab !== 1}>
        <Typography variant="subtitle1" gutterBottom>
          Agent Activity Monitoring
        </Typography>
        
        <TableContainer component={Paper} variant="outlined">
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Agent</TableCell>
                <TableCell>Messages</TableCell>
                <TableCell>Artifacts</TableCell>
                <TableCell>Last Active</TableCell>
                <TableCell>Status</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Object.entries(agentMetrics).map(([agentId, agent]: [string, any]) => (
                <TableRow key={agentId}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar 
                        sx={{ 
                          width: 32, 
                          height: 32, 
                          mr: 1,
                          bgcolor: agentId === 'market-research' ? '#2196f3' :
                                   agentId === 'seo-keyword' ? '#ff9800' :
                                   agentId === 'content-strategy' ? '#3f51b5' :
                                   agentId === 'content-generation' ? '#009688' :
                                   agentId === 'seo-optimization' ? '#f44336' :
                                   agentId === 'system' ? '#9e9e9e' :
                                   agentId === 'user' ? '#4caf50' : '#9e9e9e'
                        }}
                      >
                        {agentId.charAt(0).toUpperCase()}
                      </Avatar>
                      <Typography variant="body2">
                        {agentId}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>{agent.messageCount}</TableCell>
                  <TableCell>{agent.artifactCount}</TableCell>
                  <TableCell>
                    {agent.lastActive ? new Date(agent.lastActive).toLocaleTimeString() : '-'}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={agent.lastActive && (Date.now() - new Date(agent.lastActive).getTime() < 60000) ? 'Active' : 'Idle'}
                      color={agent.lastActive && (Date.now() - new Date(agent.lastActive).getTime() < 60000) ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
      
      {/* System Metrics Tab */}
      <Box hidden={activeTab !== 2}>
        <Typography variant="subtitle1" gutterBottom>
          System Performance Metrics
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardHeader title="Workflow Timing" />
              <Divider />
              <CardContent>
                <List dense>
                  <ListItem>
                    <ListItemIcon>
                      <AccessTimeIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Start Time"
                      secondary={systemMetrics.startTime?.toLocaleString() || '-'}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <AccessTimeIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Total Duration"
                      secondary={formatDuration(systemMetrics.totalDuration || 0)}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <AccessTimeIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Estimated Time Remaining"
                      secondary={formatDuration(systemMetrics.estimatedTimeRemaining || 0)}
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardHeader title="Quality Metrics" />
              <Divider />
              <CardContent>
                <List dense>
                  <ListItem>
                    <ListItemIcon>
                      <SpeedIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Average Quality Score"
                      secondary={`${Math.round((systemMetrics.averageQualityScore || 0) * 100)}%`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <StorageIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Total Artifacts"
                      secondary={systemMetrics.totalArtifacts || 0}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <TimelineIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Total Messages"
                      secondary={systemMetrics.totalMessages || 0}
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Paper>
  );
};

export default WorkflowMonitoringDashboard;
