// src/components/EnhancedCollaboration/AgentReasoningPanel.tsx

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Card,
  CardContent,
  Button,
  Tooltip
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import LightbulbOutlinedIcon from '@mui/icons-material/LightbulbOutlined';
import PsychologyIcon from '@mui/icons-material/Psychology';
import FactCheckIcon from '@mui/icons-material/FactCheck';
import BalanceIcon from '@mui/icons-material/Balance';
import InfoIcon from '@mui/icons-material/Info';

interface AgentReasoning {
  process: string;
  steps: string[];
  thoughts?: string[];
  considerations?: string[];
  decision: string;
  confidence?: number;
}

interface AgentReasoningPanelProps {
  agentId: string;
  agentName: string;
  reasoning: AgentReasoning;
  expanded?: boolean;
  onShowMore?: () => void;
}

// Map agent IDs to colors and icons
const agentConfig: Record<string, { color: string; icon: React.ReactNode }> = {
  'content-generation': {
    color: '#4caf50',
    icon: <LightbulbOutlinedIcon />
  },
  'seo-optimization': {
    color: '#2196f3',
    icon: <PsychologyIcon />
  },
  'market-research': {
    color: '#ff9800',
    icon: <FactCheckIcon />
  },
  'content-strategy': {
    color: '#9c27b0',
    icon: <LightbulbOutlinedIcon />
  },
  'seo-keyword': {
    color: '#00bcd4',
    icon: <LightbulbOutlinedIcon />
  }
};

// Helper function to get agent config with fallback
const getAgentConfig = (agentId: string) => {
  return agentConfig[agentId] || {
    color: '#757575',
    icon: <LightbulbOutlinedIcon />
  };
};

// Helper function to format confidence as percentage
const formatConfidence = (confidence: number): string => {
  return `${Math.round(confidence * 100)}%`;
};

/**
 * Component to display agent reasoning in a collapsible panel
 */
const AgentReasoningPanel: React.FC<AgentReasoningPanelProps> = ({
  agentId,
  agentName,
  reasoning,
  expanded = false,
  onShowMore
}) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(expanded);
  const agentConf = getAgentConfig(agentId);

  return (
    <Paper elevation={1} sx={{ mb: 2, overflow: 'hidden' }}>
      <Accordion 
        expanded={isExpanded} 
        onChange={() => setIsExpanded(!isExpanded)}
        sx={{ 
          '&:before': { display: 'none' },
          boxShadow: 'none'
        }}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          sx={{ 
            bgcolor: `${agentConf.color}15`,
            borderLeft: `4px solid ${agentConf.color}`
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar sx={{ bgcolor: agentConf.color, width: 32, height: 32, mr: 1.5 }}>
              {agentConf.icon}
            </Avatar>
            <Box>
              <Typography variant="subtitle1">{agentName} Reasoning</Typography>
              {reasoning.confidence !== undefined && (
                <Chip 
                  label={`Confidence: ${formatConfidence(reasoning.confidence)}`} 
                  size="small"
                  color={reasoning.confidence > 0.7 ? "success" : 
                         reasoning.confidence > 0.4 ? "warning" : "error"}
                  sx={{ ml: 1 }}
                />
              )}
            </Box>
          </Box>
        </AccordionSummary>
        <AccordionDetails sx={{ p: 0 }}>
          <Box sx={{ p: 2 }}>
            <Typography variant="body2" paragraph>
              {reasoning.process}
            </Typography>
            
            <Divider sx={{ my: 1 }} />
            
            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
              <Card variant="outlined" sx={{ flex: 1 }}>
                <CardContent>
                  <Typography variant="subtitle2" gutterBottom>
                    Steps Taken
                  </Typography>
                  <List dense disablePadding>
                    {reasoning.steps.map((step, index) => (
                      <ListItem key={`step-${index}`} disablePadding sx={{ py: 0.5 }}>
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <Chip 
                            label={index + 1} 
                            size="small" 
                            sx={{ 
                              minWidth: 24, 
                              height: 24, 
                              bgcolor: agentConf.color,
                              color: 'white'
                            }} 
                          />
                        </ListItemIcon>
                        <ListItemText primary={step} />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
              
              {reasoning.thoughts && reasoning.thoughts.length > 0 && (
                <Card variant="outlined" sx={{ flex: 1 }}>
                  <CardContent>
                    <Typography variant="subtitle2" gutterBottom>
                      Thought Process
                    </Typography>
                    <List dense disablePadding>
                      {reasoning.thoughts.map((thought, index) => (
                        <ListItem key={`thought-${index}`} disablePadding sx={{ py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 36 }}>
                            <LightbulbOutlinedIcon fontSize="small" color="primary" />
                          </ListItemIcon>
                          <ListItemText primary={thought} />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              )}
              
              {reasoning.considerations && reasoning.considerations.length > 0 && (
                <Card variant="outlined" sx={{ flex: 1 }}>
                  <CardContent>
                    <Typography variant="subtitle2" gutterBottom>
                      Considerations
                    </Typography>
                    <List dense disablePadding>
                      {reasoning.considerations.map((consideration, index) => (
                        <ListItem key={`consideration-${index}`} disablePadding sx={{ py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 36 }}>
                            <BalanceIcon fontSize="small" color="secondary" />
                          </ListItemIcon>
                          <ListItemText primary={consideration} />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              )}
            </Box>
            
            <Card 
              variant="outlined" 
              sx={{ 
                mt: 2, 
                bgcolor: `${agentConf.color}10`,
                borderColor: agentConf.color
              }}
            >
              <CardContent>
                <Typography variant="subtitle2" gutterBottom>
                  Decision
                </Typography>
                <Typography variant="body2">
                  {reasoning.decision}
                </Typography>
              </CardContent>
            </Card>
            
            {onShowMore && (
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Button 
                  variant="outlined" 
                  size="small" 
                  onClick={onShowMore}
                  endIcon={<InfoIcon />}
                >
                  View Full Reasoning History
                </Button>
              </Box>
            )}
          </Box>
        </AccordionDetails>
      </Accordion>
    </Paper>
  );
};

export default AgentReasoningPanel;
