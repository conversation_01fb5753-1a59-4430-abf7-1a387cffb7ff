import { postgresAdapter } from '@payloadcms/db-postgres'
import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import path from 'path'
import { buildConfig } from 'payload'
import { fileURLToPath } from 'url'
import sharp from 'sharp'
import { gcsStorage } from '@payloadcms/storage-gcs'

// Import collections
import { Users } from './collections/Users'
import { Media } from './collections/Media'
import { Products } from './collections/Products'
import { Sellers } from './collections/Sellers'
import { ProductFeatures } from './collections/ProductFeatures'
import { PricingPlans } from './collections/PricingPlans'
import { FAQs } from './collections/FAQs'
import { FeatureComparisons } from './collections/FeatureComparisons'
import { MigrationConsiderations } from './collections/MigrationConsiderations'
import { Authors } from './collections/Authors'
import { UseCaseRecommendations } from './collections/UseCaseRecommendations'
import { Reviews } from './collections/Reviews'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export default buildConfig({
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(dirname),
    },
    // Customize admin UI
    meta: {
      titleSuffix: '- Product CMS',
      favicon: '/favicon.ico',
    },
    components: {
      // Custom components
      beforeNavLinks: [
        // Add custom navigation component
        '@/components/Admin/CustomNavGroup',
      ],
    },
    // Group collections in the admin UI
    nav: {
      'Product Content': [
        'products',
        'product-features',
        'pricing-plans',
        'faqs',
        'reviews',
        'sellers',
      ],
      'Comparison Content': [
        'feature-comparisons',
        'migration-considerations',
        'use-case-recommendations',
      ],
      'People': ['authors', 'users'],
      'Media': ['media'],
    },
  },
  // Register all collections
  collections: [
    Users, 
    Media, 
    Products, 
    Sellers,
    ProductFeatures,
    PricingPlans,
    FAQs,
    FeatureComparisons,
    MigrationConsiderations,
    Authors,
    UseCaseRecommendations,
    Reviews
  ],
  // Use the Lexical rich text editor
  editor: lexicalEditor(),
  // Your secret key
  secret: process.env.PAYLOAD_SECRET || '',
  // TypeScript configuration
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  // Database configuration using PostgreSQL
  db: postgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URI || '',
    },
  }),
  // Image processing library
  sharp,
  // Plugins
  plugins: [
    payloadCloudPlugin(),
    // Storage adapter for GCS
    gcsStorage({
      collections: {
        media: true,
        'media-with-prefix': {
          prefix: 'logo-',
        },
      },
      bucket: process.env.GCS_BUCKET,
      options: {
        apiEndpoint: process.env.GCS_ENDPOINT,
        projectId: process.env.GCS_PROJECT_ID,
      },
    }),
  ],
})