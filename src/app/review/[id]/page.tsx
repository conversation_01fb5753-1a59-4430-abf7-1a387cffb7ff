/**
 * Simple Review Interface
 * Basic UI for human review and approval
 */

'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';

interface ReviewData {
  id: string;
  content: {
    id: string;
    type: string;
    title: string;
    data: any;
    context?: {
      workflowName?: string;
      stepName?: string;
    };
  };
  instructions: string;
  type: string;
  status: string;
  deadline?: string;
  createdAt: string;
}

export default function ReviewPage() {
  const params = useParams();
  const reviewId = params.id as string;
  
  const [reviewData, setReviewData] = useState<ReviewData | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [edits, setEdits] = useState('');
  const [decision, setDecision] = useState<'approve' | 'reject' | null>(null);

  useEffect(() => {
    loadReview();
  }, [reviewId]);

  const loadReview = async () => {
    try {
      const response = await fetch(`/api/review/${reviewId}`);
      const result = await response.json();

      if (result.success) {
        setReviewData(result.data);
        // Pre-populate edits with current content for editing
        if (result.data.type === 'editing') {
          setEdits(typeof result.data.content.data === 'string' 
            ? result.data.content.data 
            : JSON.stringify(result.data.content.data, null, 2)
          );
        }
      } else {
        setError(result.error || 'Failed to load review');
      }
    } catch (err) {
      setError('Failed to load review');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const submitReview = async () => {
    if (!decision) return;

    setSubmitting(true);
    setError('');

    try {
      const response = await fetch(`/api/review/${reviewId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          decision,
          edits: edits || undefined,
          reviewer: 'anonymous' // In production, get from auth
        })
      });

      const result = await response.json();

      if (result.success) {
        // Show success message
        setReviewData(prev => prev ? { ...prev, status: 'completed' } : null);
        alert('Review submitted successfully!');
      } else {
        setError(result.error || 'Failed to submit review');
      }
    } catch (err) {
      setError('Failed to submit review');
      console.error(err);
    } finally {
      setSubmitting(false);
    }
  };

  const formatContent = (data: any): string => {
    if (typeof data === 'string') {
      return data;
    }
    return JSON.stringify(data, null, 2);
  };

  const isExpired = reviewData?.deadline && new Date() > new Date(reviewData.deadline);
  const isCompleted = reviewData?.status === 'completed';

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading review...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!reviewData) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <p className="text-gray-600">Review not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Content Review</h1>
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <span>Type: {reviewData.type}</span>
          <span>Status: {reviewData.status}</span>
          {reviewData.deadline && (
            <span className={isExpired ? 'text-red-600' : ''}>
              Deadline: {new Date(reviewData.deadline).toLocaleString()}
            </span>
          )}
        </div>
      </div>

      {/* Context */}
      {reviewData.content.context && (
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <h3 className="font-medium mb-2">Context</h3>
          {reviewData.content.context.workflowName && (
            <p className="text-sm text-gray-600">
              Workflow: {reviewData.content.context.workflowName}
            </p>
          )}
          {reviewData.content.context.stepName && (
            <p className="text-sm text-gray-600">
              Step: {reviewData.content.context.stepName}
            </p>
          )}
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h3 className="font-medium mb-2">Instructions</h3>
        <p className="text-gray-700">{reviewData.instructions}</p>
      </div>

      {/* Content to Review */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-3">Content: {reviewData.content.title}</h3>
        <div className="border rounded-lg p-4 bg-white">
          <pre className="whitespace-pre-wrap text-sm">
            {formatContent(reviewData.content.data)}
          </pre>
        </div>
      </div>

      {/* Review Form */}
      {!isCompleted && !isExpired && (
        <div className="space-y-6">
          {/* Edits (for editing type reviews) */}
          {reviewData.type === 'editing' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Edit Content
              </label>
              <textarea
                value={edits}
                onChange={(e) => setEdits(e.target.value)}
                rows={10}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Make your edits here..."
              />
            </div>
          )}

          {/* Feedback (for all review types) */}
          {reviewData.type !== 'editing' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Feedback (optional)
              </label>
              <textarea
                value={edits}
                onChange={(e) => setEdits(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Provide feedback or comments..."
              />
            </div>
          )}

          {/* Decision Buttons */}
          <div className="flex gap-4">
            <button
              onClick={() => setDecision('approve')}
              className={`px-6 py-2 rounded-md font-medium ${
                decision === 'approve'
                  ? 'bg-green-600 text-white'
                  : 'bg-green-100 text-green-700 hover:bg-green-200'
              }`}
            >
              Approve
            </button>
            <button
              onClick={() => setDecision('reject')}
              className={`px-6 py-2 rounded-md font-medium ${
                decision === 'reject'
                  ? 'bg-red-600 text-white'
                  : 'bg-red-100 text-red-700 hover:bg-red-200'
              }`}
            >
              Reject
            </button>
          </div>

          {/* Submit Button */}
          {decision && (
            <button
              onClick={submitReview}
              disabled={submitting}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
            >
              {submitting ? 'Submitting...' : `Submit ${decision === 'approve' ? 'Approval' : 'Rejection'}`}
            </button>
          )}
        </div>
      )}

      {/* Status Messages */}
      {isCompleted && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          This review has been completed.
        </div>
      )}

      {isExpired && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
          This review has expired.
        </div>
      )}
    </div>
  );
}
