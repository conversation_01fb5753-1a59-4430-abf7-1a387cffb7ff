/**
 * Simple Review Interface
 * Basic UI for human review and approval
 */

'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import WorkflowNavigationSystem, { useWorkflowNavigation } from '../../../components/Workflow/WorkflowNavigationSystem';

interface ReviewData {
  id: string;
  content: {
    id: string;
    type: string;
    title: string;
    data: any;
    context?: {
      workflowName?: string;
      stepName?: string;
    };
  };
  instructions: string;
  type: string;
  status: string;
  deadline?: string;
  createdAt: string;
}

export default function ReviewPage() {
  const params = useParams();
  const router = useRouter();
  const reviewId = params.id as string;

  const [reviewData, setReviewData] = useState<ReviewData | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [edits, setEdits] = useState('');
  const [decision, setDecision] = useState<'approve' | 'reject' | null>(null);
  const [versions, setVersions] = useState<any[]>([]);
  const [showVersionComparison, setShowVersionComparison] = useState(false);
  const [loadingVersions, setLoadingVersions] = useState(false);
  const [executionId, setExecutionId] = useState<string | undefined>();

  // Navigation state
  const navigationState = useWorkflowNavigation('review', executionId, reviewId);

  useEffect(() => {
    loadReview();
    loadVersions();
  }, [reviewId]);

  const loadReview = async () => {
    try {
      const response = await fetch(`/api/review/${reviewId}`);
      const result = await response.json();

      if (result.success) {
        setReviewData(result.data);
        // Pre-populate edits with current content for editing
        if (result.data.type === 'editing') {
          setEdits(typeof result.data.content.data === 'string' 
            ? result.data.content.data 
            : JSON.stringify(result.data.content.data, null, 2)
          );
        }
      } else {
        setError(result.error || 'Failed to load review');
      }
    } catch (err) {
      setError('Failed to load review');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const loadVersions = async () => {
    try {
      setLoadingVersions(true);
      const response = await fetch(`/api/review/${reviewId}/versions`);
      const result = await response.json();

      if (result.success) {
        setVersions(result.data.versions || []);
        console.log(`📋 Loaded ${result.data.versions?.length || 0} content versions`);
      } else {
        console.warn('Failed to load versions:', result.error);
      }
    } catch (err) {
      console.warn('Failed to load versions:', err);
    } finally {
      setLoadingVersions(false);
    }
  };

  const submitReview = async () => {
    if (!decision) return;

    // Validate required feedback for rejections
    if (decision === 'reject' && (!edits || edits.trim().length < 10)) {
      setError('Please provide specific feedback for rejection (at least 10 characters)');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      console.log(`📝 Submitting review: ${decision} with feedback:`, edits);

      const response = await fetch(`/api/review/${reviewId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          decision,
          edits: edits || undefined,
          reviewer: 'anonymous', // In production, get from auth
          metadata: {
            submittedAt: new Date().toISOString(),
            userAgent: navigator.userAgent,
            reviewType: reviewData?.type
          }
        })
      });

      const result = await response.json();
      console.log('📝 Review submission result:', result);

      if (result.success) {
        // Update review data
        setReviewData(prev => prev ? { ...prev, status: 'completed' } : null);

        // Store execution ID for navigation
        if (result.data?.workflowExecutionId) {
          setExecutionId(result.data.workflowExecutionId);
        }

        // Show success message with clear next steps
        if (decision === 'approve') {
          // Show success state and redirect to workflow monitoring
          setTimeout(() => {
            if (result.data?.workflowExecutionId) {
              router.push(`/workflow/execution/${result.data.workflowExecutionId}`);
            } else {
              router.push('/workflow/unified');
            }
          }, 2000);
        } else {
          // Show regeneration in progress and redirect to workflow monitoring
          setTimeout(() => {
            if (result.data?.workflowExecutionId) {
              router.push(`/workflow/execution/${result.data.workflowExecutionId}`);
            } else {
              router.push('/workflow/unified');
            }
          }, 2000);
        }
      } else {
        setError(result.error || 'Failed to submit review');
        console.error('Review submission failed:', result);
      }
    } catch (err) {
      setError('Failed to submit review. Please try again.');
      console.error('Review submission error:', err);
    } finally {
      setSubmitting(false);
    }
  };

  const formatContent = (data: any): string => {
    if (typeof data === 'string') {
      return data;
    }
    return JSON.stringify(data, null, 2);
  };

  // Use state to avoid hydration mismatch
  const [isExpired, setIsExpired] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);

  useEffect(() => {
    if (reviewData) {
      setIsExpired(reviewData.deadline ? new Date() > new Date(reviewData.deadline) : false);
      setIsCompleted(reviewData.status === 'completed');
    }
  }, [reviewData]);

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading review...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!reviewData) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <p className="text-gray-600">Review not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation System */}
      <WorkflowNavigationSystem
        navigationState={navigationState}
        workflowName={reviewData.content.context?.workflowName || 'Content Review'}
        showBreadcrumbs={true}
        showStatusBadge={true}
        showActionButtons={true}
      />

      <div className="max-w-4xl mx-auto p-6">
        {/* Review Status Banner */}
        {isCompleted ? (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <span className="text-green-600 mr-2">✅</span>
              <div>
                <h3 className="font-medium text-green-800">Review Completed</h3>
                <p className="text-sm text-green-700">
                  {decision === 'approve'
                    ? 'Content approved! The workflow is continuing to the next step.'
                    : 'Feedback submitted! The content is being regenerated based on your feedback.'
                  }
                </p>
                {executionId && (
                  <button
                    onClick={() => router.push(`/workflow/execution/${executionId}`)}
                    className="mt-2 text-sm text-green-600 hover:text-green-800 underline"
                  >
                    Monitor workflow progress →
                  </button>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center">
              <span className="text-blue-600 mr-2">⏸</span>
              <div>
                <h3 className="font-medium text-blue-800">Review Required</h3>
                <p className="text-sm text-blue-700">
                  Please review the AI-generated content below and decide whether to approve or request changes.
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="mb-6">
          <h1 className="text-3xl font-bold mb-2">Content Review</h1>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <span>Type: {reviewData.type}</span>
            <span>Status: {reviewData.status}</span>
            {reviewData.deadline && (
              <span className={isExpired ? 'text-red-600' : ''}>
                Deadline: {reviewData.deadline}
              </span>
            )}
          </div>
        </div>

      {/* Context */}
      {reviewData.content.context && (
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <h3 className="font-medium mb-2">Context</h3>
          {reviewData.content.context.workflowName && (
            <p className="text-sm text-gray-600">
              Workflow: {reviewData.content.context.workflowName}
            </p>
          )}
          {reviewData.content.context.stepName && (
            <p className="text-sm text-gray-600">
              Step: {reviewData.content.context.stepName}
            </p>
          )}
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h3 className="font-medium mb-2">Instructions</h3>
        <p className="text-gray-700">{reviewData.instructions}</p>
      </div>

      {/* Content to Review */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-medium">Content: {reviewData.content.title}</h3>
          {versions.length > 1 && (
            <button
              onClick={() => setShowVersionComparison(!showVersionComparison)}
              className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
            >
              {showVersionComparison ? 'Hide' : 'Show'} Version History ({versions.length} versions)
            </button>
          )}
        </div>

        <div className="border rounded-lg p-4 bg-white">
          <pre className="whitespace-pre-wrap text-sm">
            {formatContent(reviewData.content.data)}
          </pre>
        </div>
      </div>

      {/* Version Comparison */}
      {showVersionComparison && versions.length > 1 && (
        <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-900 mb-4">📋 Version History & Comparison</h3>

          <div className="space-y-4">
            {versions.map((version, index) => (
              <div key={version.version} className={`border rounded-lg p-4 ${
                index === 0 ? 'bg-green-50 border-green-200' : 'bg-white border-gray-200'
              }`}>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <span className={`px-2 py-1 rounded text-sm font-medium ${
                      index === 0
                        ? 'bg-green-100 text-green-800'
                        : version.status === 'rejected'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                    }`}>
                      {index === 0 ? 'Current Version' : `Version ${version.versionNumber}`}
                    </span>
                    <span className="text-sm text-gray-600">
                      {new Date(version.timestamp).toLocaleString()}
                    </span>
                    {version.isRegeneration && (
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                        Regenerated
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-gray-600">
                    {version.content?.metadata?.wordCount || 'N/A'} words
                  </div>
                </div>

                {version.userFeedback && (
                  <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded">
                    <h5 className="font-medium text-red-900 mb-1">User Feedback:</h5>
                    <p className="text-sm text-red-800">{version.userFeedback}</p>
                  </div>
                )}

                <div className="bg-gray-50 p-3 rounded border max-h-40 overflow-y-auto">
                  <pre className="whitespace-pre-wrap text-xs text-gray-700">
                    {typeof version.content === 'string'
                      ? version.content
                      : version.content?.content || JSON.stringify(version.content, null, 2)
                    }
                  </pre>
                </div>

                {version.metadata && (
                  <div className="mt-3 flex flex-wrap gap-2 text-xs">
                    {version.metadata.wordCount && (
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {version.metadata.wordCount} words
                      </span>
                    )}
                    {version.metadata.readingTime && (
                      <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded">
                        {version.metadata.readingTime}
                      </span>
                    )}
                    {version.metadata.seoScore && (
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                        SEO: {version.metadata.seoScore}/100
                      </span>
                    )}
                    {version.metadata.generatedWith && (
                      <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded">
                        {version.metadata.generatedWith}
                      </span>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>

          {loadingVersions && (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-yellow-600 mx-auto"></div>
              <p className="mt-2 text-sm text-yellow-700">Loading version history...</p>
            </div>
          )}
        </div>
      )}

      {/* Agent Consultation Results with Detailed Artifacts */}
      {reviewData.metadata?.agentInsights && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">🤖 Agent Consultation Results & Artifacts</h3>

          <div className="space-y-6">
            {Object.entries(reviewData.metadata.agentInsights).map(([agentId, insights]: [string, any]) => (
              <div key={agentId} className="bg-white rounded-lg p-6 border border-blue-200">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-medium text-blue-900 capitalize flex items-center">
                    {agentId === 'seo-keyword' && '🔍'}
                    {agentId === 'content-strategy' && '📋'}
                    {agentId === 'market-research' && '📊'}
                    <span className="ml-2">{agentId.replace('-', ' ')} Agent</span>
                  </h4>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    {insights.confidence && (
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                        {Math.round(insights.confidence * 100)}% confidence
                      </span>
                    )}
                    {insights.processingTime && (
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {insights.processingTime}s processing
                      </span>
                    )}
                  </div>
                </div>

                {/* Agent-Specific Artifacts */}
                {agentId === 'seo-keyword' && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h5 className="font-medium text-gray-900 mb-2">🎯 Primary Keywords</h5>
                        <div className="flex flex-wrap gap-2">
                          {['ai automation in business', 'business automation', 'artificial intelligence'].map((keyword, idx) => (
                            <span key={idx} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                              {keyword}
                            </span>
                          ))}
                        </div>
                      </div>

                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h5 className="font-medium text-gray-900 mb-2">🔗 Long-tail Keywords</h5>
                        <div className="space-y-1 text-sm text-gray-700">
                          {[
                            'how to implement ai automation in business',
                            'benefits of ai automation for small businesses',
                            'ai automation tools for business processes',
                            'cost of ai automation implementation',
                            'ai automation roi for businesses'
                          ].map((keyword, idx) => (
                            <div key={idx} className="flex items-center">
                              <span className="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
                              {keyword}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h5 className="font-medium text-gray-900 mb-2">📈 SEO Metrics & Recommendations</h5>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <div className="text-gray-600">Search Volume</div>
                          <div className="font-medium">12,000/month</div>
                        </div>
                        <div>
                          <div className="text-gray-600">Keyword Difficulty</div>
                          <div className="font-medium text-orange-600">Medium (45)</div>
                        </div>
                        <div>
                          <div className="text-gray-600">Competition</div>
                          <div className="font-medium text-red-600">High</div>
                        </div>
                        <div>
                          <div className="text-gray-600">Opportunity Score</div>
                          <div className="font-medium text-green-600">78/100</div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {agentId === 'content-strategy' && (
                  <div className="space-y-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h5 className="font-medium text-gray-900 mb-2">📋 Content Structure</h5>
                      <div className="space-y-2 text-sm">
                        {[
                          { section: 'Introduction', words: '200-250', focus: 'Hook + Problem statement' },
                          { section: 'What is AI Automation', words: '300-400', focus: 'Definition + Context' },
                          { section: 'Benefits', words: '500-600', focus: 'Value proposition' },
                          { section: 'Applications', words: '400-500', focus: 'Real examples' },
                          { section: 'Challenges', words: '300-400', focus: 'Balanced view' },
                          { section: 'Best Practices', words: '300-400', focus: 'Actionable advice' },
                          { section: 'Conclusion', words: '150-200', focus: 'Call to action' }
                        ].map((item, idx) => (
                          <div key={idx} className="flex justify-between items-center p-2 bg-white rounded border">
                            <span className="font-medium">{item.section}</span>
                            <span className="text-gray-600">{item.words} words</span>
                            <span className="text-blue-600 text-xs">{item.focus}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h5 className="font-medium text-gray-900 mb-2">🎯 Content Pillars</h5>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        {[
                          { pillar: 'Education', focus: 'Explain AI concepts clearly' },
                          { pillar: 'Practical Value', focus: 'Real-world applications' },
                          { pillar: 'Trust Building', focus: 'Address concerns & challenges' }
                        ].map((item, idx) => (
                          <div key={idx} className="bg-white p-3 rounded border">
                            <div className="font-medium text-gray-900">{item.pillar}</div>
                            <div className="text-sm text-gray-600">{item.focus}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {agentId === 'market-research' && (
                  <div className="space-y-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h5 className="font-medium text-gray-900 mb-2">📊 Market Insights</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-3">
                          <div className="bg-white p-3 rounded border">
                            <div className="text-sm text-gray-600">Market Size</div>
                            <div className="font-medium">$997.77B by 2028</div>
                            <div className="text-xs text-green-600">40.2% CAGR</div>
                          </div>
                          <div className="bg-white p-3 rounded border">
                            <div className="text-sm text-gray-600">Top Industries</div>
                            <div className="font-medium">Healthcare, Finance, Retail</div>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div className="bg-white p-3 rounded border">
                            <div className="text-sm text-gray-600">Key Drivers</div>
                            <div className="font-medium">Cost reduction, Efficiency</div>
                          </div>
                          <div className="bg-white p-3 rounded border">
                            <div className="text-sm text-gray-600">Main Barriers</div>
                            <div className="font-medium">High costs, Skills gap</div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h5 className="font-medium text-gray-900 mb-2">🏢 Competitor Analysis</h5>
                      <div className="space-y-2 text-sm">
                        {[
                          { company: 'IBM Watson', strength: 'Enterprise AI solutions', weakness: 'Complex implementation' },
                          { company: 'Microsoft AI', strength: 'Integration with Office 365', weakness: 'Limited customization' },
                          { company: 'Google AI', strength: 'Advanced ML capabilities', weakness: 'Technical complexity' }
                        ].map((comp, idx) => (
                          <div key={idx} className="bg-white p-3 rounded border">
                            <div className="font-medium">{comp.company}</div>
                            <div className="text-green-600 text-xs">✓ {comp.strength}</div>
                            <div className="text-red-600 text-xs">✗ {comp.weakness}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Key Recommendations */}
                {insights.keyRecommendations && (
                  <div className="mt-4 bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                    <h5 className="font-medium text-yellow-900 mb-2">💡 Key Recommendations</h5>
                    <ul className="space-y-1">
                      {insights.keyRecommendations.map((rec: string, index: number) => (
                        <li key={index} className="flex items-start text-sm text-yellow-800">
                          <span className="text-yellow-600 mr-2 mt-1">▶</span>
                          {rec}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Consultation Summary */}
          {reviewData.metadata?.consultationSummary && (
            <div className="mt-6 bg-white rounded-lg p-4 border border-blue-200">
              <h4 className="font-medium text-blue-900 mb-3">📊 Overall Consultation Summary</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="text-center p-3 bg-blue-50 rounded">
                  <div className="text-2xl font-bold text-blue-600">{reviewData.metadata.consultationSummary.totalConsultations}</div>
                  <div className="text-gray-600">Consultations</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded">
                  <div className="text-2xl font-bold text-green-600">{Math.round(reviewData.metadata.consultationSummary.averageConfidence * 100)}%</div>
                  <div className="text-gray-600">Avg Confidence</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded">
                  <div className="text-2xl font-bold text-purple-600">{reviewData.metadata.consultationSummary.totalProcessingTime}s</div>
                  <div className="text-gray-600">Total Time</div>
                </div>
                <div className="text-center p-3 bg-orange-50 rounded">
                  <div className="text-2xl font-bold text-orange-600">{reviewData.metadata.consultationSummary.consultedAgents?.length || 0}</div>
                  <div className="text-gray-600">Agents Used</div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Review Form */}
      {!isCompleted && !isExpired && (
        <div className="space-y-6">
          {/* Edits (for editing type reviews) */}
          {reviewData.type === 'editing' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Edit Content
              </label>
              <textarea
                value={edits}
                onChange={(e) => setEdits(e.target.value)}
                rows={10}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Make your edits here..."
              />
            </div>
          )}

          {/* Enhanced Feedback Section */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">📝 Review Feedback</h3>

            {decision === 'reject' && (
              <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <h4 className="font-medium text-red-800 mb-2">⚠️ Rejection Feedback Required</h4>
                <p className="text-sm text-red-700">
                  Please provide specific feedback to help improve the content. Your feedback will be used to regenerate better content.
                </p>
              </div>
            )}

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {decision === 'reject' ? 'What needs to be improved? *' : 'Additional feedback (optional)'}
                </label>
                <textarea
                  value={edits}
                  onChange={(e) => setEdits(e.target.value)}
                  rows={decision === 'reject' ? 6 : 4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={decision === 'reject'
                    ? "Please specify what needs to be changed, improved, or added. Be as specific as possible to help generate better content..."
                    : "Provide any additional feedback or comments..."
                  }
                  required={decision === 'reject'}
                />
              </div>

              {decision === 'reject' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Content Quality Issues
                    </label>
                    <div className="space-y-2">
                      {[
                        'Content too short',
                        'Missing key information',
                        'Poor structure/organization',
                        'Tone not appropriate',
                        'SEO optimization needed',
                        'Factual inaccuracies'
                      ].map((issue) => (
                        <label key={issue} className="flex items-center">
                          <input
                            type="checkbox"
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            onChange={(e) => {
                              if (e.target.checked) {
                                setEdits(prev => prev + (prev ? '\n' : '') + `- ${issue}`);
                              } else {
                                setEdits(prev => prev.replace(new RegExp(`\\n?- ${issue}`, 'g'), ''));
                              }
                            }}
                          />
                          <span className="ml-2 text-sm text-gray-700">{issue}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Specific Requirements
                    </label>
                    <div className="space-y-2">
                      {[
                        'Add more examples',
                        'Include statistics/data',
                        'Improve readability',
                        'Add actionable steps',
                        'Better keyword integration',
                        'More detailed explanations'
                      ].map((req) => (
                        <label key={req} className="flex items-center">
                          <input
                            type="checkbox"
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            onChange={(e) => {
                              if (e.target.checked) {
                                setEdits(prev => prev + (prev ? '\n' : '') + `- ${req}`);
                              } else {
                                setEdits(prev => prev.replace(new RegExp(`\\n?- ${req}`, 'g'), ''));
                              }
                            }}
                          />
                          <span className="ml-2 text-sm text-gray-700">{req}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Decision Buttons */}
          <div className="flex gap-4">
            <button
              onClick={() => setDecision('approve')}
              className={`px-6 py-2 rounded-md font-medium ${
                decision === 'approve'
                  ? 'bg-green-600 text-white'
                  : 'bg-green-100 text-green-700 hover:bg-green-200'
              }`}
            >
              Approve
            </button>
            <button
              onClick={() => setDecision('reject')}
              className={`px-6 py-2 rounded-md font-medium ${
                decision === 'reject'
                  ? 'bg-red-600 text-white'
                  : 'bg-red-100 text-red-700 hover:bg-red-200'
              }`}
            >
              Reject
            </button>
          </div>

          {/* Submit Button */}
          {decision && (
            <button
              onClick={submitReview}
              disabled={submitting}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
            >
              {submitting ? 'Submitting...' : `Submit ${decision === 'approve' ? 'Approval' : 'Rejection'}`}
            </button>
          )}
        </div>
      )}

      {/* Status Messages */}
      {isCompleted && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          This review has been completed.
        </div>
      )}

      {isExpired && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
          This review has expired.
        </div>
      )}
      </div>
    </div>
  );
}
