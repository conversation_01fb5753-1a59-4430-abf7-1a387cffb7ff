/**
 * Enhanced Dashboard Page
 * Unified dashboard/workflow experience for single article creation
 */

'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import WorkflowInterface from '@/components/Workflow/WorkflowInterface';

interface DashboardStats {
  totalTemplates: number;
  recentWorkflows: RecentWorkflow[];
  templateStats: TemplateStats[];
  systemStatus: {
    activeWorkflows: number;
    pendingReviews: number;
    completedToday: number;
  };
}

interface RecentWorkflow {
  id: string;
  name: string;
  status: string;
  progress: number;
  createdAt: string;
  templateId: string;
}

interface TemplateStats {
  id: string;
  name: string;
  usageCount: number;
  category: string;
  description: string;
}

export default function DashboardPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeView, setActiveView] = useState<'dashboard' | 'workflow'>('dashboard');
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if we should start with workflow view
    const template = searchParams.get('template');
    const startWorkflow = searchParams.get('start');

    if (template || startWorkflow) {
      setSelectedTemplate(template);
      setActiveView('workflow');
    }

    loadDashboardStats();
  }, [searchParams]);

  const loadDashboardStats = async () => {
    try {
      // In real implementation, this would call actual APIs
      // For now, using mock data that represents the enhanced template system
      setDashboardStats({
        totalTemplates: 10,
        recentWorkflows: [
          {
            id: 'wf-1',
            name: 'SEO Blog Post: AI in Healthcare',
            status: 'completed',
            progress: 100,
            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            templateId: 'blog-post-seo'
          },
          {
            id: 'wf-2',
            name: 'How-To Guide: Setting up CI/CD',
            status: 'in_review',
            progress: 80,
            createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
            templateId: 'how-to-guide'
          },
          {
            id: 'wf-3',
            name: 'Case Study: E-commerce Migration',
            status: 'running',
            progress: 45,
            createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
            templateId: 'case-study'
          }
        ],
        templateStats: [
          { id: 'blog-post-seo', name: 'SEO Blog Post', usageCount: 12, category: 'Blog', description: 'SEO-optimized blog posts' },
          { id: 'how-to-guide', name: 'How-To Guide', usageCount: 8, category: 'Educational', description: 'Step-by-step instructions' },
          { id: 'case-study', name: 'Case Study', usageCount: 5, category: 'Business', description: 'Success stories & results' },
          { id: 'listicle', name: 'Listicle', usageCount: 7, category: 'Blog', description: 'List-based articles' },
          { id: 'opinion-piece', name: 'Opinion Piece', usageCount: 3, category: 'Editorial', description: 'Thought leadership content' },
          { id: 'technical-tutorial', name: 'Technical Tutorial', usageCount: 4, category: 'Technical', description: 'Code tutorials & guides' }
        ],
        systemStatus: {
          activeWorkflows: 3,
          pendingReviews: 2,
          completedToday: 8
        }
      });
    } catch (error) {
      console.error('Failed to load dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const startWorkflow = (templateId?: string) => {
    setSelectedTemplate(templateId || null);
    setActiveView('workflow');

    // Update URL without page reload
    const url = new URL(window.location.href);
    if (templateId) {
      url.searchParams.set('template', templateId);
    } else {
      url.searchParams.delete('template');
    }
    url.searchParams.set('start', 'true');
    window.history.pushState({}, '', url.toString());
  };

  const backToDashboard = () => {
    setActiveView('dashboard');
    setSelectedTemplate(null);

    // Clean URL
    const url = new URL(window.location.href);
    url.searchParams.delete('template');
    url.searchParams.delete('start');
    window.history.pushState({}, '', url.toString());
  };

  if (activeView === 'workflow') {
    return (
      <WorkflowInterface
        selectedTemplate={selectedTemplate}
        onBack={backToDashboard}
        onWorkflowComplete={(workflowId) => {
          // Handle workflow completion
          console.log('Workflow completed:', workflowId);
          // Could show success message or redirect to results
        }}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Content Generation Dashboard</h1>
              <p className="mt-1 text-gray-600">
                Create high-quality content with AI-powered workflows
              </p>
            </div>
            <button
              onClick={() => startWorkflow()}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Create Content
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading ? (
          <div className="space-y-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                {[1, 2, 3, 4].map(i => (
                  <div key={i} className="h-24 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* Quick Start Templates */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Start</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {dashboardStats?.templateStats.slice(0, 6).map(template => (
                  <button
                    key={template.id}
                    onClick={() => startWorkflow(template.id)}
                    className="p-4 bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors text-left group"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium text-gray-900 group-hover:text-blue-900">{template.name}</h3>
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        {template.usageCount} uses
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 group-hover:text-blue-700">{template.description}</p>
                    <div className="mt-2">
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        {template.category}
                      </span>
                    </div>
                  </button>
                ))}

                <button
                  onClick={() => startWorkflow()}
                  className="p-4 bg-white border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors text-center group"
                >
                  <div className="text-gray-400 group-hover:text-blue-500 mb-2">
                    <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                  </div>
                  <h3 className="font-medium text-gray-700 group-hover:text-blue-900">Browse All Templates</h3>
                  <p className="text-sm text-gray-500 group-hover:text-blue-700">See all {dashboardStats?.totalTemplates} templates</p>
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Recent Workflows */}
              <div className="lg:col-span-2">
                <div className="bg-white rounded-lg shadow">
                  <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h2 className="text-lg font-semibold text-gray-900">Recent Workflows</h2>
                    <button
                      onClick={() => router.push('/workflow/history')}
                      className="text-sm text-blue-600 hover:text-blue-800"
                    >
                      View All
                    </button>
                  </div>
                  <div className="p-6">
                    {dashboardStats?.recentWorkflows.length ? (
                      <div className="space-y-4">
                        {dashboardStats.recentWorkflows.map(workflow => (
                          <WorkflowCard key={workflow.id} workflow={workflow} />
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-gray-500 mb-4">No workflows yet</p>
                        <button
                          onClick={() => startWorkflow()}
                          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                        >
                          Create Your First Workflow
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* System Overview */}
              <div className="space-y-6">
                {/* System Stats */}
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">System Overview</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Active Workflows</span>
                      <span className="text-lg font-semibold text-blue-600">
                        {dashboardStats?.systemStatus.activeWorkflows}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Pending Reviews</span>
                      <span className="text-lg font-semibold text-yellow-600">
                        {dashboardStats?.systemStatus.pendingReviews}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Completed Today</span>
                      <span className="text-lg font-semibold text-green-600">
                        {dashboardStats?.systemStatus.completedToday}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Popular Templates */}
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Popular Templates</h3>
                  <div className="space-y-3">
                    {dashboardStats?.templateStats.slice(0, 5).map(template => (
                      <div key={template.id} className="flex items-center justify-between">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{template.name}</div>
                          <div className="text-xs text-gray-500">{template.category}</div>
                        </div>
                        <div className="text-sm font-medium text-gray-600">
                          {template.usageCount}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

// Workflow Card Component
function WorkflowCard({ workflow }: { workflow: RecentWorkflow }) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_review': return 'bg-yellow-100 text-yellow-800';
      case 'running': return 'bg-blue-100 text-blue-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  return (
    <div className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors">
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-medium text-gray-900">{workflow.name}</h3>
        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(workflow.status)}`}>
          {workflow.status.replace('_', ' ')}
        </span>
      </div>
      <div className="flex items-center justify-between text-sm text-gray-500">
        <span>{formatTimeAgo(workflow.createdAt)}</span>
        <div className="flex items-center space-x-2">
          <div className="w-24 bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${workflow.progress}%` }}
            ></div>
          </div>
          <span>{workflow.progress}%</span>
        </div>
      </div>
      <div className="mt-2">
        <a
          href={`/workflow/results/${workflow.id}`}
          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
        >
          View Details →
        </a>
      </div>
    </div>
  );
}
