/**
 * API endpoint to store workflow artifacts in the same Redis system
 * as the working executions
 */

import { NextRequest, NextResponse } from 'next/server';
import { getWorkflowEngine } from '../../../../../core/workflow/singleton';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { executionId, artifacts, status, completedAt } = body;

    console.log(`🔄 Storing artifacts for execution: ${executionId}`);

    // Validate required fields
    if (!executionId || !artifacts) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: executionId, artifacts'
      }, { status: 400 });
    }

    // Use the same workflow engine as the working executions
    const workflowEngine = getWorkflowEngine();

    // Get existing execution
    const execution = await workflowEngine.stateStore.getExecution(executionId);
    if (!execution) {
      return NextResponse.json({
        success: false,
        error: 'Execution not found'
      }, { status: 404 });
    }

    // Update execution with artifacts and completion status
    const updatedExecution = {
      ...execution,
      artifacts,
      status: status || 'completed',
      completedAt: completedAt || new Date().toISOString(),
      progress: 100,
      updatedAt: new Date().toISOString()
    };

    // Store updated execution in the workflow engine's storage system
    await workflowEngine.stateStore.setExecution(updatedExecution);

    console.log(`✅ Stored ${artifacts.length} artifacts for execution: ${executionId}`);

    return NextResponse.json({
      success: true,
      data: {
        executionId,
        artifactCount: artifacts.length,
        status: 'stored',
        message: 'Artifacts stored successfully'
      }
    });

  } catch (error) {
    console.error('❌ Failed to store workflow artifacts:', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create workflow execution'
    }, { status: 500 });
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json({
    success: false,
    error: 'Method not allowed. Use POST to store artifacts.'
  }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({
    success: false,
    error: 'Method not allowed. Use POST to store artifacts.'
  }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({
    success: false,
    error: 'Method not allowed. Use POST to store artifacts.'
  }, { status: 405 });
}
