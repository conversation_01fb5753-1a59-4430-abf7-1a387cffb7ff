/**
 * Human Review Submission API
 * 
 * Handles human review submissions for workflow steps
 */

import { NextRequest, NextResponse } from 'next/server';
import { getWorkflowEngine } from '../../../../../core/workflow/singleton';

/**
 * POST /api/workflow/execution/review
 * Submit a human review decision for a workflow step
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { executionId, stepId, decision, feedback, reviewer } = body;

    console.log(`📝 Submitting review for step ${stepId} in execution ${executionId}`);

    // Validate required fields
    if (!executionId || !stepId || decision === undefined) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: executionId, stepId, decision'
      }, { status: 400 });
    }

    const workflowEngine = getWorkflowEngine();

    // Get execution to verify it exists and step is waiting for review
    const execution = await workflowEngine.getExecution(executionId);
    if (!execution) {
      return NextResponse.json({
        success: false,
        error: 'Execution not found'
      }, { status: 404 });
    }

    const stepResult = execution.stepResults[stepId];
    if (!stepResult) {
      return NextResponse.json({
        success: false,
        error: 'Step not found'
      }, { status: 404 });
    }

    if (stepResult.status !== 'waiting_review') {
      return NextResponse.json({
        success: false,
        error: `Step is not waiting for review. Current status: ${stepResult.status}`
      }, { status: 400 });
    }

    // Submit review through workflow engine
    await workflowEngine.submitReview(executionId, stepId, {
      approved: decision === 'approved',
      feedback: feedback || '',
      reviewer: reviewer || 'anonymous'
    });

    console.log(`✅ Review submitted for step ${stepId}: ${decision}`);

    return NextResponse.json({
      success: true,
      data: {
        executionId,
        stepId,
        decision,
        message: 'Review submitted successfully'
      }
    });

  } catch (error) {
    console.error('❌ Failed to submit review:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to submit review'
    }, { status: 500 });
  }
}

/**
 * GET /api/workflow/execution/review
 * Get pending reviews for a user or execution
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const executionId = searchParams.get('executionId');
    const userId = searchParams.get('userId');

    const workflowEngine = getWorkflowEngine();

    if (executionId) {
      // Get pending reviews for specific execution
      const execution = await workflowEngine.getExecution(executionId);
      if (!execution) {
        return NextResponse.json({
          success: false,
          error: 'Execution not found'
        }, { status: 404 });
      }

      const pendingReviews = Object.values(execution.stepResults)
        .filter(step => step.status === 'waiting_review')
        .map(step => ({
          executionId,
          stepId: step.stepId,
          status: step.status,
          artifactId: step.artifactId,
          createdAt: step.startedAt
        }));

      return NextResponse.json({
        success: true,
        data: {
          pendingReviews,
          count: pendingReviews.length
        }
      });
    }

    // TODO: Implement user-specific pending reviews
    return NextResponse.json({
      success: true,
      data: {
        pendingReviews: [],
        count: 0
      }
    });

  } catch (error) {
    console.error('❌ Failed to get pending reviews:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get pending reviews'
    }, { status: 500 });
  }
}
