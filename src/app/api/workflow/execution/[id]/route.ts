/**
 * Workflow Execution API
 * Get execution details and step results for workflow visualization
 */

import { NextRequest, NextResponse } from 'next/server';
import { getWorkflowEngine } from '../../../../../core/workflow/singleton';

/**
 * GET /api/workflow/execution/[id]
 * Get execution details and step results
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const executionId = id;
    console.log(`🔍 Fetching execution data for: ${executionId}`);

    const workflowEngine = getWorkflowEngine();

    // Debug: Check state store directly
    const stateStore = (workflowEngine as any).stateStore;
    const state = await stateStore.get();
    console.log(`🔍 Debug - State executions:`, {
      hasState: !!state,
      hasExecutions: !!(state?.executions),
      executionIds: state?.executions ? Object.keys(state.executions) : [],
      targetExecutionId: executionId
    });

    // Get execution details
    const execution = await workflowEngine.getExecution(executionId);

    if (!execution) {
      console.log(`❌ Execution ${executionId} not found`);
      return NextResponse.json(
        { error: 'Execution not found' },
        { status: 404 }
      );
    }

    // Get step results with detailed information
    const stepResults = Object.values(execution.stepResults || {}).map((stepResult: any) => {
      console.log(`📋 Step ${stepResult.stepId}:`, {
        status: stepResult.status,
        artifactId: stepResult.artifactId,
        outputs: stepResult.outputs
      });

      return {
        stepId: stepResult.stepId,
        status: stepResult.status,
        artifactId: stepResult.artifactId,
        outputs: stepResult.outputs,
        approvalRequired: stepResult.stepId.includes('approval'),
        completedAt: stepResult.completedAt,
        startedAt: stepResult.startedAt,
        error: stepResult.error,
        rejectionReason: stepResult.rejectionReason,
        approvedBy: stepResult.approvedBy,
        approvedAt: stepResult.approvedAt
      };
    });

    console.log(`✅ Found execution ${executionId} with ${stepResults.length} steps`);

    return NextResponse.json({
      success: true,
      data: {
        execution: {
          id: execution.id,
          workflowId: execution.workflowId,
          status: execution.status,
          progress: execution.progress,
          startedAt: execution.startedAt,
          completedAt: execution.completedAt,
          error: execution.error
        },
        steps: stepResults
      }
    });

  } catch (error) {
    console.error(`❌ Error fetching execution ${params.id}:`, error);
    
    return NextResponse.json(
      {
        error: 'Failed to fetch execution data',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
