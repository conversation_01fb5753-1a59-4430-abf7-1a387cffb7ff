/**
 * Workflow Execution API
 * Get execution details and step results for workflow visualization
 */

import { NextRequest, NextResponse } from 'next/server';
import { getWorkflowEngine } from '../../../../../core/workflow/singleton';

/**
 * GET /api/workflow/execution/[id]
 * Get execution details and step results
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const executionId = id;
    console.log(`🔍 Fetching execution data for: ${executionId}`);

    const workflowEngine = getWorkflowEngine();

    // Debug: Check state store directly
    const stateStore = (workflowEngine as any).stateStore;
    const state = await stateStore.get();
    console.log(`🔍 Debug - State executions:`, {
      hasState: !!state,
      hasExecutions: !!(state?.executions),
      executionIds: state?.executions ? Object.keys(state.executions) : [],
      targetExecutionId: executionId
    });

    // Get execution details
    const execution = await workflowEngine.getExecution(executionId);

    if (!execution) {
      console.log(`❌ Execution ${executionId} not found`);
      return NextResponse.json(
        { error: 'Execution not found' },
        { status: 404 }
      );
    }

    // Get workflow details to get step names
    const workflow = await workflowEngine.getWorkflow(execution.workflowId);
    const workflowSteps = workflow?.steps || [];

    // Get step results with detailed information
    const stepResults = Object.values(execution.stepResults || {}).map((stepResult: any) => {
      console.log(`📋 Step ${stepResult.stepId}:`, {
        status: stepResult.status,
        artifactId: stepResult.artifactId,
        outputs: stepResult.outputs
      });

      // Find step definition to get name
      const stepDef = workflowSteps.find(s => s.id === stepResult.stepId);

      return {
        id: stepResult.stepId,
        stepId: stepResult.stepId,
        name: stepDef?.name || stepResult.stepId.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()),
        status: stepResult.status,
        artifactId: stepResult.artifactId,
        outputs: stepResult.outputs,
        approvalRequired: stepResult.stepId.includes('approval'),
        completedAt: stepResult.completedAt,
        startedAt: stepResult.startedAt,
        error: stepResult.error,
        rejectionReason: stepResult.rejectionReason,
        approvedBy: stepResult.approvedBy,
        approvedAt: stepResult.approvedAt
      };
    });

    console.log(`✅ Found execution ${executionId} with ${stepResults.length} steps`);

    // Calculate current step based on status
    const runningStep = stepResults.find(s => s.status === 'running');
    const currentStep = runningStep?.stepId ||
                      (execution.status === 'waiting_review' ? 'human-review' :
                       execution.status === 'completed' ? 'completed' : 'unknown');

    // Return data in the format expected by frontend
    return NextResponse.json({
      success: true,
      data: {
        id: execution.id,
        workflowId: execution.workflowId,
        status: execution.status,
        progress: execution.progress,
        currentStep: currentStep,
        startedAt: execution.startedAt,
        completedAt: execution.completedAt,
        error: execution.error,
        steps: stepResults
      }
    });

  } catch (error) {
    console.error(`❌ Error fetching execution ${params.id}:`, error);
    
    return NextResponse.json(
      {
        error: 'Failed to fetch execution data',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
