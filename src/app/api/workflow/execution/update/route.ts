/**
 * API endpoint to update workflow executions with stepResults and completion status
 */

import { NextRequest, NextResponse } from 'next/server';
import { getWorkflowEngine } from '../../../../../core/workflow/singleton';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { executionId, status, stepResults, completedAt, progress } = body;

    console.log(`🔄 Updating execution: ${executionId} to status: ${status}`);

    // Validate required fields
    if (!executionId || !status) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: executionId, status'
      }, { status: 400 });
    }

    // Use the same workflow engine as the working executions
    const workflowEngine = getWorkflowEngine();

    // Get existing execution
    const execution = await workflowEngine.stateStore.getExecution(executionId);
    if (!execution) {
      return NextResponse.json({
        success: false,
        error: 'Execution not found'
      }, { status: 404 });
    }

    // Update execution with new status and stepResults
    const updatedExecution = {
      ...execution,
      status,
      stepResults: stepResults || execution.stepResults || {},
      completedAt: completedAt || execution.completedAt,
      progress: progress !== undefined ? progress : execution.progress,
      updatedAt: new Date().toISOString()
    };

    // Store updated execution
    await workflowEngine.stateStore.setExecution(updatedExecution);

    console.log(`✅ Updated execution: ${executionId} with ${Object.keys(stepResults || {}).length} step results`);

    return NextResponse.json({
      success: true,
      data: {
        executionId,
        status,
        stepCount: Object.keys(stepResults || {}).length,
        message: 'Execution updated successfully'
      }
    });

  } catch (error) {
    console.error('❌ Failed to update workflow execution:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update workflow execution'
    }, { status: 500 });
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json({
    success: false,
    error: 'Method not allowed. Use POST to update executions.'
  }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({
    success: false,
    error: 'Method not allowed. Use POST to update executions.'
  }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({
    success: false,
    error: 'Method not allowed. Use POST to update executions.'
  }, { status: 405 });
}
