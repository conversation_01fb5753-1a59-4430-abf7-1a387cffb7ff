/**
 * Workflow Creation API
 * Simple API to create and execute workflows
 */

import { NextRequest, NextResponse } from 'next/server';
import {
  getWorkflowEngine,
  getTemplateRegistry,
  getEnhancedTemplateRegistry
} from '../../../../core/workflow/singleton';
import { ResponseFormatter } from '../../../../core/api/response-formatter';
import { ErrorType } from '../../../../core/utils/error-handler';

export async function POST(request: NextRequest) {
  const requestId = ResponseFormatter.getRequestId(request);

  try {
    const body = await request.json();

    // Check if this is a template-based workflow creation (legacy API)
    if (body.templateId && body.inputs) {
      const { templateId, inputs, userApiKey, userId } = body;

      // Get singletons - use enhanced template registry for proper processing
      const enhancedTemplateRegistry = getEnhancedTemplateRegistry();
      const workflowEngine = getWorkflowEngine();

      // Process template to handle human review steps and approval gates properly
      const processed = enhancedTemplateRegistry.processTemplate(templateId, userId || 'anonymous');
      if (!processed) {
        return ResponseFormatter.notFound('Template', templateId, requestId);
      }

      console.log(`🔄 Creating workflow from template ${templateId} with ${processed.workflow.steps.length} steps`);
      console.log(`📋 Steps: ${processed.workflow.steps.map(s => `${s.id}(${s.type})`).join(', ')}`);

      // Create workflow from processed template (includes all steps)
      const workflowId = await workflowEngine.createWorkflow(processed.workflow);

      // Register approval gates if any
      for (const gate of processed.approvalGates) {
        await workflowEngine.registerApprovalGate(gate);
        console.log(`✅ Registered approval gate: ${gate.id}`);
      }

      // Prepare inputs with user API key if provided
      const executionInputs = {
        ...inputs,
        userApiKey // Pass user's API key for BYOK
      };

      // Execute workflow
      const executionId = await workflowEngine.executeWorkflow(
        workflowId,
        executionInputs,
        {
          source: 'api',
          priority: 'normal',
          userId: userId || 'anonymous'
        }
      );

      return ResponseFormatter.success({
        workflowId,
        executionId,
        templateId,
        status: 'started',
        stepsCount: processed.workflow.steps.length,
        approvalGatesCount: processed.approvalGates.length
      }, requestId);
    }

    // Handle direct workflow creation (from WorkflowBuilder)
    const workflow = body;

    // Validate required fields for direct workflow creation
    if (!workflow.name) {
      return ResponseFormatter.validationError(
        'Workflow name is required',
        [{ field: 'name', message: 'Workflow name is required', code: 'REQUIRED' }],
        requestId
      );
    }

    if (!workflow.steps || !Array.isArray(workflow.steps)) {
      return ResponseFormatter.validationError(
        'Workflow steps are required',
        [{ field: 'steps', message: 'Workflow steps are required', code: 'REQUIRED' }],
        requestId
      );
    }

    // Get workflow engine
    const workflowEngine = getWorkflowEngine();

    // Create workflow directly
    const workflowId = await workflowEngine.createWorkflow({
      id: workflow.id,
      name: workflow.name,
      description: workflow.description,
      steps: workflow.steps,
      metadata: {
        category: workflow.metadata?.category || 'blog',
        difficulty: workflow.metadata?.difficulty || 'easy',
        estimatedTime: workflow.metadata?.estimatedTime || 30,
        tags: workflow.metadata?.tags || [],
        featured: workflow.metadata?.featured || false,
        ...workflow.metadata
      },
      agentConsultationEnabled: workflow.agentConsultationEnabled || false
    });

    return ResponseFormatter.success({
      workflowId,
      workflow: {
        id: workflow.id,
        name: workflow.name,
        description: workflow.description,
        steps: workflow.steps.length,
        agentConsultationEnabled: workflow.agentConsultationEnabled
      },
      status: 'created'
    }, requestId);

  } catch (error) {
    console.error('Workflow creation error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const executionId = searchParams.get('executionId');

    // Get singletons
    const templateRegistry = getTemplateRegistry();
    const workflowEngine = getWorkflowEngine();

    if (!executionId) {
      // Return available templates
      const templates = templateRegistry.getAllTemplates();

      return NextResponse.json({
        success: true,
        data: {
          templates: templates.map(template => ({
            id: template.id,
            name: template.name,
            description: template.description,
            instructions: template.instructions || template.description || '',
            featured: template.featured,
            sampleInputs: template.sampleInputs,
            workflow: {
              metadata: {
                category: template.workflow.metadata.category,
                difficulty: template.workflow.metadata.difficulty,
                estimatedTime: template.workflow.metadata.estimatedTime
              }
            }
          }))
        }
      });
    }

    // Get execution status
    const execution = await workflowEngine.getExecution(executionId);
    if (!execution) {
      return NextResponse.json(
        { error: 'Execution not found' },
        { status: 404 }
      );
    }

    // Get workflow details
    const workflow = await workflowEngine.getWorkflow(execution.workflowId);
    const workflowSteps = workflow?.steps || [];

    // Map steps with names and detailed information
    const stepsWithDetails = Object.values(execution.stepResults).map((step: any) => {
      const stepDef = workflowSteps.find(s => s.id === step.stepId);
      return {
        id: step.stepId,
        stepId: step.stepId,
        name: stepDef?.name || step.stepId.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()),
        status: step.status,
        startedAt: step.startedAt,
        completedAt: step.completedAt,
        duration: step.duration,
        error: step.error,
        artifactId: step.artifactId,
        outputs: step.outputs,
        approvalRequired: step.approvalRequired,
        approvedBy: step.approvedBy,
        approvedAt: step.approvedAt,
        rejectionReason: step.rejectionReason,
        metadata: step.metadata
      };
    });

    // Calculate current step
    const runningStep = stepsWithDetails.find(s => s.status === 'running');
    const currentStep = runningStep?.stepId ||
                      (execution.status === 'waiting_review' ? 'human-review' :
                       execution.status === 'completed' ? 'completed' : 'unknown');

    return NextResponse.json({
      success: true,
      data: {
        id: execution.id,
        workflowId: execution.workflowId,
        status: execution.status,
        progress: execution.progress,
        currentStep: currentStep,
        startedAt: execution.startedAt,
        completedAt: execution.completedAt,
        error: execution.error,
        workflow: workflow ? {
          id: workflow.id,
          name: workflow.name,
          description: workflow.description
        } : null,
        steps: stepsWithDetails
      }
    });

  } catch (error) {
    console.error('Workflow status error:', error);

    return NextResponse.json(
      {
        error: 'Failed to get workflow status',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
