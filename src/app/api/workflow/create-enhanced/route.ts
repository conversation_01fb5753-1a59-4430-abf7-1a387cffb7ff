/**
 * Enhanced Workflow Creation API with Agent Consultation
 * 
 * Handles workflow creation with agent consultation configuration
 */

import { NextRequest, NextResponse } from 'next/server';
import { Redis } from '@upstash/redis';

// Initialize Redis client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

interface WorkflowStep {
  id: string;
  name: string;
  type: string;
  config: any;
  inputs: string[];
  outputs: string[];
  dependencies: string[];
  consultationConfig?: {
    enabled: boolean;
    triggers: Array<{
      type: string;
      agents: string[];
      priority: string;
      condition?: any;
    }>;
    maxConsultations: number;
    timeoutMs: number;
    fallbackBehavior: string;
  };
}

interface Workflow {
  id: string;
  name: string;
  description: string;
  steps: WorkflowStep[];
  template?: any;
  agentConsultationEnabled: boolean;
  createdAt: string;
  version: string;
  metadata: {
    totalSteps: number;
    agentEnabledSteps: number;
    estimatedTime: number;
  };
}

/**
 * POST /api/workflow/create-enhanced
 * Create a new workflow with agent consultation configuration
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      description,
      steps,
      template,
      agentConsultationEnabled,
      version,
      metadata
    } = body;

    // Validate required fields
    if (!name || !name.trim()) {
      return NextResponse.json({
        success: false,
        error: 'Workflow name is required'
      }, { status: 400 });
    }

    if (!steps || !Array.isArray(steps) || steps.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'At least one workflow step is required'
      }, { status: 400 });
    }

    // Validate workflow steps
    const validationErrors = validateWorkflowSteps(steps);
    if (validationErrors.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'Workflow validation failed',
        details: validationErrors
      }, { status: 400 });
    }

    // Create workflow object
    const workflowId = `workflow-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const workflow: Workflow = {
      id: workflowId,
      name: name.trim(),
      description: description?.trim() || '',
      steps,
      template,
      agentConsultationEnabled: agentConsultationEnabled || false,
      createdAt: new Date().toISOString(),
      version: version || '1.0.0',
      metadata: {
        totalSteps: steps.length,
        agentEnabledSteps: steps.filter(step => step.consultationConfig?.enabled).length,
        estimatedTime: metadata?.estimatedTime || calculateEstimatedTime(steps),
        ...metadata
      }
    };

    // Save workflow to Redis
    await redis.hset('enhanced-workflows', workflowId, JSON.stringify(workflow));

    // Update workflow index
    const workflowIndex = await redis.get('enhanced-workflow-index') || '[]';
    const index = JSON.parse(workflowIndex as string);
    index.push({
      id: workflowId,
      name: workflow.name,
      description: workflow.description,
      agentConsultationEnabled: workflow.agentConsultationEnabled,
      createdAt: workflow.createdAt,
      totalSteps: workflow.metadata.totalSteps,
      agentEnabledSteps: workflow.metadata.agentEnabledSteps
    });
    await redis.set('enhanced-workflow-index', JSON.stringify(index));

    // Log workflow creation
    await logWorkflowEvent(workflowId, 'workflow_created', {
      name: workflow.name,
      totalSteps: workflow.metadata.totalSteps,
      agentEnabledSteps: workflow.metadata.agentEnabledSteps
    });

    return NextResponse.json({
      success: true,
      data: {
        workflow,
        message: 'Enhanced workflow created successfully'
      }
    });

  } catch (error) {
    console.error('Enhanced workflow creation error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create enhanced workflow'
    }, { status: 500 });
  }
}

/**
 * GET /api/workflow/create-enhanced
 * Get enhanced workflow creation templates and configuration options
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'templates';

    switch (type) {
      case 'templates':
        const templates = await getEnhancedWorkflowTemplates();
        return NextResponse.json({
          success: true,
          data: {
            templates,
            totalTemplates: templates.length
          }
        });

      case 'workflows':
        const workflows = await getEnhancedWorkflows();
        return NextResponse.json({
          success: true,
          data: {
            workflows,
            totalWorkflows: workflows.length
          }
        });

      case 'step-types':
        const stepTypes = getAvailableStepTypes();
        return NextResponse.json({
          success: true,
          data: {
            stepTypes
          }
        });

      case 'agent-config':
        const agentConfig = await getAgentConfiguration();
        return NextResponse.json({
          success: true,
          data: {
            agentConfig
          }
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid type parameter. Use: templates, workflows, step-types, or agent-config'
        }, { status: 400 });
    }

  } catch (error) {
    console.error('Enhanced workflow creation API error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get enhanced workflow creation data'
    }, { status: 500 });
  }
}

// Helper functions
function validateWorkflowSteps(steps: WorkflowStep[]): string[] {
  const errors: string[] = [];
  const stepIds = new Set<string>();

  steps.forEach((step, index) => {
    // Validate step ID uniqueness
    if (stepIds.has(step.id)) {
      errors.push(`Duplicate step ID: ${step.id}`);
    }
    stepIds.add(step.id);

    // Validate required fields
    if (!step.name || !step.name.trim()) {
      errors.push(`Step ${index + 1}: Name is required`);
    }

    if (!step.type || !step.type.trim()) {
      errors.push(`Step ${index + 1}: Type is required`);
    }

    // Validate dependencies
    if (step.dependencies) {
      step.dependencies.forEach(depId => {
        if (!stepIds.has(depId) && !steps.some(s => s.id === depId)) {
          errors.push(`Step ${index + 1}: Invalid dependency ${depId}`);
        }
      });
    }

    // Validate agent consultation config
    if (step.consultationConfig?.enabled) {
      if (!step.consultationConfig.triggers || step.consultationConfig.triggers.length === 0) {
        errors.push(`Step ${index + 1}: Agent consultation enabled but no triggers configured`);
      }

      step.consultationConfig.triggers?.forEach((trigger, triggerIndex) => {
        if (!trigger.agents || trigger.agents.length === 0) {
          errors.push(`Step ${index + 1}, Trigger ${triggerIndex + 1}: No agents selected`);
        }
      });
    }
  });

  return errors;
}

function calculateEstimatedTime(steps: WorkflowStep[]): number {
  const stepTimes = {
    'TEXT_INPUT': 2,
    'AI_GENERATION': 15,
    'HUMAN_REVIEW': 10,
    'CSV_IMPORT': 3,
    'CSV_EXPORT': 2,
    'URL_FETCH': 5,
    'LOOP': 20
  };

  let totalTime = 0;
  steps.forEach(step => {
    const baseTime = stepTimes[step.type as keyof typeof stepTimes] || 10;
    let stepTime = baseTime;

    // Add time for agent consultation
    if (step.consultationConfig?.enabled) {
      const agentTime = step.consultationConfig.triggers.reduce((sum, trigger) => {
        return sum + (trigger.agents.length * 3); // 3 minutes per agent
      }, 0);
      stepTime += agentTime;
    }

    totalTime += stepTime;
  });

  return totalTime;
}

async function getEnhancedWorkflowTemplates() {
  return [
    {
      id: 'blog-post-seo-enhanced',
      name: 'Enhanced SEO Blog Post',
      description: 'Complete SEO-optimized blog post generation with intelligent agent consultation',
      category: 'blog',
      consultationEnabled: true,
      agentCount: 3,
      steps: [
        {
          id: 'topic-input',
          name: 'Topic Input',
          type: 'TEXT_INPUT',
          config: {},
          inputs: [],
          outputs: ['topic', 'target_audience'],
          dependencies: []
        },
        {
          id: 'keyword-research',
          name: 'Keyword Research',
          type: 'AI_GENERATION',
          config: {},
          inputs: ['topic', 'target_audience'],
          outputs: ['keywords', 'search_volume'],
          dependencies: ['topic-input'],
          consultationConfig: {
            enabled: true,
            triggers: [{
              type: 'always',
              agents: ['seo-keyword', 'market-research'],
              priority: 'high'
            }],
            maxConsultations: 3,
            timeoutMs: 30000,
            fallbackBehavior: 'continue'
          }
        },
        {
          id: 'content-creation',
          name: 'Content Creation',
          type: 'AI_GENERATION',
          config: {},
          inputs: ['topic', 'keywords', 'target_audience'],
          outputs: ['content', 'meta_description'],
          dependencies: ['keyword-research'],
          consultationConfig: {
            enabled: true,
            triggers: [{
              type: 'always',
              agents: ['seo-keyword', 'content-strategy'],
              priority: 'high'
            }],
            maxConsultations: 3,
            timeoutMs: 30000,
            fallbackBehavior: 'continue'
          }
        },
        {
          id: 'human-review',
          name: 'Human Review',
          type: 'HUMAN_REVIEW',
          config: {},
          inputs: ['content', 'meta_description'],
          outputs: ['approved_content'],
          dependencies: ['content-creation']
        }
      ]
    }
  ];
}

async function getEnhancedWorkflows() {
  try {
    const workflowIndex = await redis.get('enhanced-workflow-index') || '[]';
    return JSON.parse(workflowIndex as string);
  } catch (error) {
    console.error('Failed to get enhanced workflows:', error);
    return [];
  }
}

function getAvailableStepTypes() {
  return [
    {
      type: 'TEXT_INPUT',
      name: 'Text Input',
      description: 'Collect text input from user',
      icon: '📝',
      estimatedTime: 2,
      supportsAgentConsultation: false
    },
    {
      type: 'AI_GENERATION',
      name: 'AI Generation',
      description: 'Generate content using AI with optional agent consultation',
      icon: '🤖',
      estimatedTime: 15,
      supportsAgentConsultation: true
    },
    {
      type: 'HUMAN_REVIEW',
      name: 'Human Review',
      description: 'Human review and approval',
      icon: '👤',
      estimatedTime: 10,
      supportsAgentConsultation: false
    },
    {
      type: 'CSV_IMPORT',
      name: 'CSV Import',
      description: 'Import data from CSV file',
      icon: '📊',
      estimatedTime: 3,
      supportsAgentConsultation: false
    }
  ];
}

async function getAgentConfiguration() {
  return {
    availableAgents: [
      {
        agentId: 'seo-keyword',
        name: 'SEO Keyword Agent',
        capabilities: ['keyword-research', 'seo-optimization'],
        icon: '🔍',
        isAvailable: true
      },
      {
        agentId: 'market-research',
        name: 'Market Research Agent',
        capabilities: ['market-analysis', 'competitor-research'],
        icon: '📊',
        isAvailable: true
      },
      {
        agentId: 'content-strategy',
        name: 'Content Strategy Agent',
        capabilities: ['content-planning', 'structure-optimization'],
        icon: '📝',
        isAvailable: true
      }
    ],
    triggerTypes: [
      {
        type: 'always',
        name: 'Always',
        description: 'Consult agents for every execution'
      },
      {
        type: 'quality_threshold',
        name: 'Quality Threshold',
        description: 'Consult when quality falls below threshold'
      },
      {
        type: 'feedback_keywords',
        name: 'Feedback Keywords',
        description: 'Consult when specific keywords appear in feedback'
      },
      {
        type: 'content_complexity',
        name: 'Content Complexity',
        description: 'Consult when content complexity exceeds threshold'
      }
    ]
  };
}

async function logWorkflowEvent(workflowId: string, event: string, data: any) {
  try {
    const logEntry = {
      timestamp: new Date().toISOString(),
      workflowId,
      event,
      data
    };

    const logs = await redis.get('enhanced-workflow-logs') || '[]';
    const logArray = JSON.parse(logs as string);
    logArray.unshift(logEntry);

    // Keep only last 1000 log entries
    if (logArray.length > 1000) {
      logArray.splice(1000);
    }

    await redis.set('enhanced-workflow-logs', JSON.stringify(logArray));
  } catch (error) {
    console.error('Failed to log enhanced workflow event:', error);
  }
}
