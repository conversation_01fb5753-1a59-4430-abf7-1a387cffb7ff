/**
 * Visual Workflow API
 * Endpoints for visual workflow data and updates
 */

import { NextRequest, NextResponse } from 'next/server';
import { getStateStore } from '../../../../../core/workflow/singleton';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const workflowId = id;

    const stateStore = getStateStore();
    
    // Get workflow execution data
    const execution = await stateStore.getExecution(workflowId);
    if (!execution) {
      return NextResponse.json(
        { error: 'Workflow not found' },
        { status: 404 }
      );
    }

    // Convert execution data to visual format
    const visualData = convertExecutionToVisualFormat(execution);

    return NextResponse.json({
      success: true,
      data: visualData
    });

  } catch (error) {
    console.error('Visual workflow fetch error:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch visual workflow data',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const workflowId = id;
    const body = await request.json();
    const { nodes, edges, action } = body;

    const stateStore = getStateStore();

    switch (action) {
      case 'update_layout':
        // Update node positions and connections
        await stateStore.updateWorkflowLayout(workflowId, { nodes, edges });
        break;

      case 'update_node_status':
        const { nodeId, status, progress } = body;
        await stateStore.updateStepStatus(workflowId, nodeId, status, progress);
        break;

      case 'trigger_step':
        const { stepId } = body;
        // Trigger a specific workflow step
        await triggerWorkflowStep(workflowId, stepId);
        break;

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      data: { message: 'Workflow updated successfully' }
    });

  } catch (error) {
    console.error('Visual workflow update error:', error);
    return NextResponse.json(
      {
        error: 'Failed to update visual workflow',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// Helper function to convert execution data to visual format
function convertExecutionToVisualFormat(execution: any) {
  const nodes = [];
  const edges = [];

  // Template node
  nodes.push({
    id: 'template',
    type: 'template',
    position: { x: 100, y: 50 },
    data: {
      label: execution.template?.name || 'Template',
      status: 'completed',
      description: execution.template?.description || 'Workflow template',
      category: execution.template?.workflow?.metadata?.category,
      difficulty: execution.template?.workflow?.metadata?.difficulty,
      icon: '📋'
    }
  });

  // Input configuration node
  nodes.push({
    id: 'input',
    type: 'workflowStep',
    position: { x: 100, y: 200 },
    data: {
      label: 'Input Configuration',
      status: execution.inputs ? 'completed' : 'pending',
      description: 'Configure workflow inputs',
      stepType: 'input',
      icon: '⚙️'
    }
  });

  // Add workflow steps
  if (execution.steps && Array.isArray(execution.steps)) {
    execution.steps.forEach((step: any, index: number) => {
      const yPosition = 350 + (index * 150);
      
      nodes.push({
        id: step.id,
        type: step.type === 'review' ? 'review' : 'workflowStep',
        position: { x: 100, y: yPosition },
        data: {
          label: step.name || `Step ${index + 1}`,
          status: step.status || 'pending',
          description: step.description || `Workflow step ${index + 1}`,
          stepType: step.type,
          icon: getStepIcon(step.type),
          progress: step.progress,
          error: step.error,
          // Review-specific data
          reviewers: step.reviewers,
          reviewType: step.reviewType,
          deadline: step.deadline,
          priority: step.priority,
          completedReviews: step.completedReviews,
          totalReviews: step.totalReviews
        }
      });

      // Add edge from previous step
      const sourceId = index === 0 ? 'input' : execution.steps[index - 1].id;
      edges.push({
        id: `${sourceId}-${step.id}`,
        source: sourceId,
        target: step.id,
        type: 'smoothstep',
        animated: step.status === 'running'
      });
    });
  }

  // Add template to input edge
  edges.push({
    id: 'template-input',
    source: 'template',
    target: 'input',
    type: 'smoothstep',
    animated: false
  });

  // Output node
  const lastStepId = execution.steps && execution.steps.length > 0 
    ? execution.steps[execution.steps.length - 1].id 
    : 'input';
  
  nodes.push({
    id: 'output',
    type: 'workflowStep',
    position: { x: 100, y: 350 + (execution.steps?.length || 0) * 150 },
    data: {
      label: 'Output Generation',
      status: execution.status === 'completed' ? 'completed' : 'pending',
      description: 'Generate final output',
      stepType: 'output',
      icon: '📄'
    }
  });

  edges.push({
    id: `${lastStepId}-output`,
    source: lastStepId,
    target: 'output',
    type: 'smoothstep',
    animated: false
  });

  return {
    nodes,
    edges,
    status: execution.status || 'idle',
    workflowId: execution.id,
    templateId: execution.templateId
  };
}

// Helper function to get step icons
function getStepIcon(stepType: string): string {
  switch (stepType) {
    case 'ai_generation': return '🤖';
    case 'review': return '👥';
    case 'processing': return '⚙️';
    case 'validation': return '✅';
    case 'transformation': return '🔄';
    case 'output': return '📄';
    case 'input': return '📝';
    default: return '⚡';
  }
}

// Helper function to trigger workflow steps
async function triggerWorkflowStep(workflowId: string, stepId: string) {
  try {
    // This would integrate with the actual workflow engine
    // For now, we'll just update the step status
    const stateStore = getStateStore();
    await stateStore.updateStepStatus(workflowId, stepId, 'running', 0);
    
    // Simulate step execution
    setTimeout(async () => {
      try {
        await stateStore.updateStepStatus(workflowId, stepId, 'completed', 100);
      } catch (error) {
        console.error('Failed to complete step:', error);
        await stateStore.updateStepStatus(workflowId, stepId, 'failed', 0);
      }
    }, 5000); // 5 second simulation

  } catch (error) {
    console.error('Failed to trigger workflow step:', error);
    throw error;
  }
}
