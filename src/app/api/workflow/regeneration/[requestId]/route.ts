/**
 * Regeneration Status API
 * 
 * Provides endpoints to check the status of artifact regeneration requests
 */

import { NextRequest, NextResponse } from 'next/server';

// In a real implementation, this would be stored in Redis or a database
const regenerationRequests = new Map();

export async function GET(
  request: NextRequest,
  { params }: { params: { requestId: string } }
) {
  try {
    const { requestId } = params;

    if (!requestId) {
      return NextResponse.json({
        success: false,
        error: 'Request ID is required'
      }, { status: 400 });
    }

    // Get regeneration request status
    const regenerationRequest = regenerationRequests.get(requestId);

    if (!regenerationRequest) {
      return NextResponse.json({
        success: false,
        error: 'Regeneration request not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: regenerationRequest
    });

  } catch (error) {
    console.error('Error fetching regeneration status:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch regeneration status'
    }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { requestId: string } }
) {
  try {
    const { requestId } = params;
    const updates = await request.json();

    if (!requestId) {
      return NextResponse.json({
        success: false,
        error: 'Request ID is required'
      }, { status: 400 });
    }

    // Get existing request
    const existingRequest = regenerationRequests.get(requestId);
    if (!existingRequest) {
      return NextResponse.json({
        success: false,
        error: 'Regeneration request not found'
      }, { status: 404 });
    }

    // Update the request
    const updatedRequest = {
      ...existingRequest,
      ...updates,
      updatedAt: new Date().toISOString()
    };

    regenerationRequests.set(requestId, updatedRequest);

    return NextResponse.json({
      success: true,
      data: updatedRequest
    });

  } catch (error) {
    console.error('Error updating regeneration status:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update regeneration status'
    }, { status: 500 });
  }
}

// Helper function to store regeneration requests (would be called from the processor)
export function storeRegenerationRequest(request: any) {
  regenerationRequests.set(request.id, request);
}

// Helper function to get all requests for an artifact
export function getRegenerationRequestsForArtifact(artifactId: string) {
  const requests = [];
  for (const [id, request] of regenerationRequests.entries()) {
    if (request.originalArtifactId === artifactId) {
      requests.push(request);
    }
  }
  return requests;
}
