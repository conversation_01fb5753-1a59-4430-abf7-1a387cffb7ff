/**
 * Workflow Control API
 * 
 * Handles workflow pause, resume, stop operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { Redis } from '@upstash/redis';

// Initialize Redis client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

interface WorkflowExecution {
  executionId: string;
  workflowId: string;
  status: 'pending' | 'running' | 'paused' | 'completed' | 'failed' | 'cancelled';
  currentStepIndex: number;
  startedAt: string;
  pausedAt?: string;
  resumedAt?: string;
  completedAt?: string;
  pauseReason?: string;
  controlHistory: Array<{
    action: string;
    timestamp: string;
    userId?: string;
    reason?: string;
  }>;
}

/**
 * POST /api/workflow/control
 * Control workflow execution (pause, resume, stop)
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, executionId, reason, userId } = body;

    if (!action || !executionId) {
      return NextResponse.json({
        success: false,
        error: 'Action and executionId are required'
      }, { status: 400 });
    }

    // Get current execution status
    const executionData = await redis.hget('workflow-executions', executionId);
    if (!executionData) {
      return NextResponse.json({
        success: false,
        error: 'Workflow execution not found'
      }, { status: 404 });
    }

    const execution: WorkflowExecution = JSON.parse(executionData as string);
    const timestamp = new Date().toISOString();

    switch (action) {
      case 'pause':
        if (execution.status !== 'running') {
          return NextResponse.json({
            success: false,
            error: 'Can only pause running workflows'
          }, { status: 400 });
        }

        execution.status = 'paused';
        execution.pausedAt = timestamp;
        execution.pauseReason = reason || 'Manual pause';
        execution.controlHistory.push({
          action: 'pause',
          timestamp,
          userId,
          reason: execution.pauseReason
        });

        await redis.hset('workflow-executions', executionId, JSON.stringify(execution));
        await logWorkflowEvent(executionId, 'workflow_paused', { reason: execution.pauseReason });

        return NextResponse.json({
          success: true,
          data: {
            execution,
            message: 'Workflow paused successfully'
          }
        });

      case 'resume':
        if (execution.status !== 'paused') {
          return NextResponse.json({
            success: false,
            error: 'Can only resume paused workflows'
          }, { status: 400 });
        }

        execution.status = 'running';
        execution.resumedAt = timestamp;
        execution.controlHistory.push({
          action: 'resume',
          timestamp,
          userId,
          reason: reason || 'Manual resume'
        });

        await redis.hset('workflow-executions', executionId, JSON.stringify(execution));
        await logWorkflowEvent(executionId, 'workflow_resumed', { reason: reason || 'Manual resume' });

        return NextResponse.json({
          success: true,
          data: {
            execution,
            message: 'Workflow resumed successfully'
          }
        });

      case 'stop':
        if (!['running', 'paused'].includes(execution.status)) {
          return NextResponse.json({
            success: false,
            error: 'Can only stop running or paused workflows'
          }, { status: 400 });
        }

        execution.status = 'cancelled';
        execution.completedAt = timestamp;
        execution.controlHistory.push({
          action: 'stop',
          timestamp,
          userId,
          reason: reason || 'Manual stop'
        });

        await redis.hset('workflow-executions', executionId, JSON.stringify(execution));
        await logWorkflowEvent(executionId, 'workflow_stopped', { reason: reason || 'Manual stop' });

        return NextResponse.json({
          success: true,
          data: {
            execution,
            message: 'Workflow stopped successfully'
          }
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Use: pause, resume, or stop'
        }, { status: 400 });
    }

  } catch (error) {
    console.error('Workflow control error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to control workflow'
    }, { status: 500 });
  }
}

/**
 * GET /api/workflow/control
 * Get workflow execution status and control history
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const executionId = searchParams.get('executionId');

    if (!executionId) {
      return NextResponse.json({
        success: false,
        error: 'executionId parameter is required'
      }, { status: 400 });
    }

    // Get execution data
    const executionData = await redis.hget('workflow-executions', executionId);
    if (!executionData) {
      return NextResponse.json({
        success: false,
        error: 'Workflow execution not found'
      }, { status: 404 });
    }

    const execution: WorkflowExecution = JSON.parse(executionData as string);

    // Calculate execution metrics
    const metrics = calculateExecutionMetrics(execution);

    return NextResponse.json({
      success: true,
      data: {
        execution,
        metrics,
        canPause: execution.status === 'running',
        canResume: execution.status === 'paused',
        canStop: ['running', 'paused'].includes(execution.status)
      }
    });

  } catch (error) {
    console.error('Workflow control status error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get workflow status'
    }, { status: 500 });
  }
}

// Helper functions
function calculateExecutionMetrics(execution: WorkflowExecution) {
  const now = new Date();
  const startTime = new Date(execution.startedAt);
  
  let totalRuntime = 0;
  let pausedTime = 0;
  
  if (execution.status === 'completed' || execution.status === 'failed' || execution.status === 'cancelled') {
    const endTime = new Date(execution.completedAt!);
    totalRuntime = endTime.getTime() - startTime.getTime();
  } else {
    totalRuntime = now.getTime() - startTime.getTime();
  }

  // Calculate paused time
  if (execution.pausedAt && execution.status === 'paused') {
    pausedTime = now.getTime() - new Date(execution.pausedAt).getTime();
  }

  const activeRuntime = totalRuntime - pausedTime;

  return {
    totalRuntime,
    activeRuntime,
    pausedTime,
    pauseCount: execution.controlHistory.filter(h => h.action === 'pause').length,
    resumeCount: execution.controlHistory.filter(h => h.action === 'resume').length,
    isCurrentlyPaused: execution.status === 'paused',
    lastAction: execution.controlHistory[execution.controlHistory.length - 1]
  };
}

async function logWorkflowEvent(executionId: string, event: string, data: any) {
  try {
    const logEntry = {
      timestamp: new Date().toISOString(),
      executionId,
      event,
      data
    };

    const logs = await redis.get('workflow-control-logs') || '[]';
    const logArray = JSON.parse(logs as string);
    logArray.unshift(logEntry);

    // Keep only last 1000 log entries
    if (logArray.length > 1000) {
      logArray.splice(1000);
    }

    await redis.set('workflow-control-logs', JSON.stringify(logArray));
  } catch (error) {
    console.error('Failed to log workflow control event:', error);
  }
}
