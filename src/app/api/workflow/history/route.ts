/**
 * Workflow History API
 * Endpoints for retrieving workflow execution history
 */

import { NextRequest, NextResponse } from 'next/server';
import { getStateStore } from '../../../../core/workflow/singleton';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const status = searchParams.get('status');
    const templateId = searchParams.get('templateId');

    const stateStore = getStateStore();
    
    // Get all executions (in a real implementation, this would be paginated)
    const allExecutions = await stateStore.getAllExecutions();

    console.log('📊 History API Debug:', {
      totalExecutions: allExecutions.length,
      executionIds: allExecutions.map(e => e?.id || 'undefined'),
      executionSample: allExecutions.length > 0 ? {
        id: allExecutions[0]?.id || 'undefined',
        workflowId: allExecutions[0]?.workflowId || 'undefined',
        status: allExecutions[0]?.status || 'undefined',
        progress: allExecutions[0]?.progress || 'undefined',
        startedAt: allExecutions[0]?.startedAt || 'undefined',
        hasWorkflowName: 'workflowName' in (allExecutions[0] || {}),
        fullExecution: allExecutions[0]
      } : null
    });

    // Filter out invalid executions (those without IDs or workflowIds)
    const validExecutions = allExecutions.filter(execution => {
      const isValid = execution && execution.id && execution.workflowId;
      if (!isValid) {
        console.warn('⚠️ Filtering out invalid execution:', execution);
        // TODO: Clean up invalid executions from Redis in the future
      }
      return isValid;
    });

    console.log(`✅ Filtered executions: ${allExecutions.length} total, ${validExecutions.length} valid`);

    // If no valid executions, return empty result
    if (validExecutions.length === 0) {
      console.log('📋 No valid executions found, returning empty result');
      return NextResponse.json({
        success: true,
        data: {
          executions: [],
          pagination: {
            total: 0,
            limit,
            offset,
            hasMore: false
          },
          statistics: {
            total: 0,
            completed: 0,
            failed: 0,
            running: 0,
            successRate: 0,
            averageExecutionTime: 0
          },
          templateStats: {}
        }
      });
    }

    // Filter executions based on query parameters
    let filteredExecutions = validExecutions;
    
    if (status) {
      filteredExecutions = filteredExecutions.filter(exec => exec.status === status);
    }
    
    if (templateId) {
      filteredExecutions = filteredExecutions.filter(exec => exec.templateId === templateId);
    }

    // Sort by creation date (newest first)
    filteredExecutions.sort((a, b) => 
      new Date(b.startedAt).getTime() - new Date(a.startedAt).getTime()
    );

    // Apply pagination
    const paginatedExecutions = filteredExecutions.slice(offset, offset + limit);

    // Enrich executions with workflow names (with fallback for missing workflows)
    const enrichedExecutions = await Promise.all(
      paginatedExecutions.map(async (execution) => {
        try {
          // Ensure execution has required fields
          if (!execution.workflowId) {
            console.warn('⚠️ Execution missing workflowId:', execution);
            return {
              ...execution,
              workflowName: 'Unknown Workflow'
            };
          }

          const workflow = await stateStore.getWorkflow(execution.workflowId);
          return {
            ...execution,
            workflowName: workflow?.name || `SEO Blog Post (${execution.workflowId.slice(-8)})`
          };
        } catch (error) {
          console.warn('⚠️ Error enriching execution:', error);
          // Fallback for missing workflows - assume they were SEO blog posts
          const workflowId = execution.workflowId || 'unknown';
          return {
            ...execution,
            workflowName: workflowId.length >= 8 ? `SEO Blog Post (${workflowId.slice(-8)})` : 'Unknown Workflow'
          };
        }
      })
    );

    // Calculate summary statistics
    const totalExecutions = filteredExecutions.length;
    const completedExecutions = filteredExecutions.filter(exec => exec.status === 'completed').length;
    const failedExecutions = filteredExecutions.filter(exec => exec.status === 'failed').length;
    const runningExecutions = filteredExecutions.filter(exec => exec.status === 'running').length;

    const averageExecutionTime = calculateAverageExecutionTime(
      filteredExecutions.filter(exec => exec.status === 'completed')
    );

    // Get template usage statistics
    const templateStats = getTemplateUsageStats(filteredExecutions);

    return NextResponse.json({
      success: true,
      data: {
        executions: enrichedExecutions,
        pagination: {
          total: totalExecutions,
          limit,
          offset,
          hasMore: offset + limit < totalExecutions
        },
        statistics: {
          total: totalExecutions,
          completed: completedExecutions,
          failed: failedExecutions,
          running: runningExecutions,
          successRate: totalExecutions > 0 ? (completedExecutions / totalExecutions) * 100 : 0,
          averageExecutionTime
        },
        templateStats
      }
    });

  } catch (error) {
    console.error('Workflow history fetch error:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch workflow history',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const executionId = searchParams.get('executionId');
    const olderThan = searchParams.get('olderThan'); // ISO date string
    const status = searchParams.get('status');

    if (!executionId && !olderThan && !status) {
      return NextResponse.json(
        { error: 'Must specify executionId, olderThan date, or status for deletion' },
        { status: 400 }
      );
    }

    const stateStore = getStateStore();

    let deletedCount = 0;

    if (executionId) {
      // Delete specific execution
      const success = await stateStore.deleteExecution(executionId);
      deletedCount = success ? 1 : 0;
    } else {
      // Bulk delete based on criteria
      const allExecutions = await stateStore.getAllExecutions();
      let toDelete = allExecutions;

      if (olderThan) {
        const cutoffDate = new Date(olderThan);
        toDelete = toDelete.filter(exec => new Date(exec.startedAt) < cutoffDate);
      }

      if (status) {
        toDelete = toDelete.filter(exec => exec.status === status);
      }

      // Delete executions
      for (const execution of toDelete) {
        const success = await stateStore.deleteExecution(execution.id);
        if (success) deletedCount++;
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        deletedCount,
        message: `Successfully deleted ${deletedCount} workflow execution(s)`
      }
    });

  } catch (error) {
    console.error('Workflow history deletion error:', error);
    return NextResponse.json(
      {
        error: 'Failed to delete workflow history',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// Helper function to calculate average execution time
function calculateAverageExecutionTime(completedExecutions: any[]): number {
  if (completedExecutions.length === 0) return 0;

  const totalTime = completedExecutions.reduce((sum, exec) => {
    if (exec.startedAt && exec.completedAt) {
      const startTime = new Date(exec.startedAt).getTime();
      const endTime = new Date(exec.completedAt).getTime();
      return sum + (endTime - startTime);
    }
    return sum;
  }, 0);

  return Math.round(totalTime / completedExecutions.length / 1000); // Return in seconds
}

// Helper function to get template usage statistics
function getTemplateUsageStats(executions: any[]): Record<string, any> {
  const templateCounts: Record<string, number> = {};
  const templateSuccessRates: Record<string, { total: number; completed: number }> = {};

  executions.forEach(exec => {
    const templateId = exec.templateId || 'unknown';
    
    // Count usage
    templateCounts[templateId] = (templateCounts[templateId] || 0) + 1;
    
    // Track success rates
    if (!templateSuccessRates[templateId]) {
      templateSuccessRates[templateId] = { total: 0, completed: 0 };
    }
    templateSuccessRates[templateId].total++;
    if (exec.status === 'completed') {
      templateSuccessRates[templateId].completed++;
    }
  });

  // Convert to final format
  const stats: Record<string, any> = {};
  Object.keys(templateCounts).forEach(templateId => {
    const usage = templateCounts[templateId];
    const successData = templateSuccessRates[templateId];
    const successRate = successData.total > 0 ? (successData.completed / successData.total) * 100 : 0;

    stats[templateId] = {
      usage,
      successRate: Math.round(successRate * 100) / 100,
      totalExecutions: successData.total,
      completedExecutions: successData.completed
    };
  });

  return stats;
}
