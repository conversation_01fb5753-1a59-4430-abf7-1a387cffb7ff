/**
 * Template Processing API
 * Handles template-based workflow creation with approval gates
 */

import { NextRequest, NextResponse } from 'next/server';
import { getEnhancedTemplateRegistry, getWorkflowEngine } from '@/core/workflow/singleton';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('id');
    const category = searchParams.get('category');
    const featured = searchParams.get('featured');

    const registry = getEnhancedTemplateRegistry();

    if (templateId) {
      // Get specific template
      const template = registry.getTemplate(templateId);
      if (!template) {
        return NextResponse.json(
          { error: 'Template not found' },
          { status: 404 }
        );
      }
      return NextResponse.json({ template });
    }

    if (category) {
      // Get templates by category
      const templates = registry.getTemplatesByCategory(category);
      return NextResponse.json({ templates });
    }

    if (featured === 'true') {
      // Get featured templates
      const templates = registry.getFeaturedTemplates();
      return NextResponse.json({ templates });
    }

    // Get all templates
    const templates = registry.getAllTemplates();
    return NextResponse.json({ templates });

  } catch (error) {
    console.error('Template API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch templates' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { templateId, inputs, userId } = body;

    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    const registry = getEnhancedTemplateRegistry();
    const workflowEngine = getWorkflowEngine();

    // Process template into workflow with approval gates
    const processed = registry.processTemplate(templateId, userId);
    if (!processed) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Create workflow execution
    const execution = await workflowEngine.createExecution(
      processed.workflow,
      inputs || {},
      {
        userId,
        source: 'template',
        templateId
      }
    );

    // Register approval gates
    for (const gate of processed.approvalGates) {
      await workflowEngine.registerApprovalGate(gate);
    }

    // Start workflow execution
    await workflowEngine.executeWorkflow(execution.id);

    return NextResponse.json({
      success: true,
      execution: {
        id: execution.id,
        workflowId: processed.workflow.id,
        status: execution.status,
        approvalGates: processed.approvalGates.length
      }
    });

  } catch (error) {
    console.error('Template processing error:', error);
    return NextResponse.json(
      { error: 'Failed to process template' },
      { status: 500 }
    );
  }
}

// Validate template endpoint
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { template } = body;

    if (!template) {
      return NextResponse.json(
        { error: 'Template is required' },
        { status: 400 }
      );
    }

    const { TemplateProcessor } = await import('@/core/workflow/template-processor');
    const validation = TemplateProcessor.validateTemplate(template);

    return NextResponse.json({
      validation,
      executionOrder: validation.valid ? TemplateProcessor.getExecutionOrder(template) : null
    });

  } catch (error) {
    console.error('Template validation error:', error);
    return NextResponse.json(
      { error: 'Failed to validate template' },
      { status: 500 }
    );
  }
}
