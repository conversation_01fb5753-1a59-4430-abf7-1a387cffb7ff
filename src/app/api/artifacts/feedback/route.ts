/**
 * Artifact Feedback API
 * 
 * Handles human feedback collection and processing for artifacts
 */

import { NextRequest, NextResponse } from 'next/server';
import { Redis } from '@upstash/redis';

// Initialize Redis client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

interface Feedback {
  id: string;
  artifactId: string;
  workflowExecutionId: string;
  stepId: string;
  content: string;
  timestamp: string;
  userId: string;
  processed: boolean;
  agentConsultations?: Array<{
    agentId: string;
    suggestions: any[];
    confidence: number;
    processingTime: number;
  }>;
  sentiment?: 'positive' | 'negative' | 'neutral';
  categories?: string[];
  priority?: 'low' | 'medium' | 'high';
}

interface Artifact {
  id: string;
  type: string;
  content: any;
  version: string;
  previousVersionId?: string;
  metadata: {
    title: string;
    description: string;
    createdAt: string;
    updatedAt: string;
    qualityScore?: number;
    wordCount?: number;
    readabilityScore?: number;
  };
  feedback?: Feedback[];
  status: 'draft' | 'pending_review' | 'approved' | 'rejected' | 'regenerating';
}

/**
 * POST /api/artifacts/feedback
 * Submit feedback for an artifact
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      artifactId,
      workflowExecutionId,
      stepId,
      feedback,
      userId
    } = body;

    if (!artifactId || !feedback || !userId) {
      return NextResponse.json({
        success: false,
        error: 'artifactId, feedback, and userId are required'
      }, { status: 400 });
    }

    // Get artifact
    const artifactData = await redis.hget('artifacts', artifactId);
    if (!artifactData) {
      return NextResponse.json({
        success: false,
        error: 'Artifact not found'
      }, { status: 404 });
    }

    const artifact: Artifact = JSON.parse(artifactData as string);

    // Create feedback object
    const feedbackId = `feedback-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newFeedback: Feedback = {
      id: feedbackId,
      artifactId,
      workflowExecutionId: workflowExecutionId || '',
      stepId: stepId || '',
      content: feedback.trim(),
      timestamp: new Date().toISOString(),
      userId,
      processed: false,
      sentiment: analyzeFeedbackSentiment(feedback),
      categories: categorizeFeedback(feedback),
      priority: determineFeedbackPriority(feedback)
    };

    // Add feedback to artifact
    if (!artifact.feedback) {
      artifact.feedback = [];
    }
    artifact.feedback.push(newFeedback);
    artifact.metadata.updatedAt = new Date().toISOString();

    // Update artifact status if needed
    if (artifact.status === 'pending_review') {
      artifact.status = 'draft'; // Move back to draft for revision
    }

    // Save updated artifact
    await redis.hset('artifacts', artifactId, JSON.stringify(artifact));

    // Store feedback separately for analytics
    await redis.hset('feedback', feedbackId, JSON.stringify(newFeedback));

    // Update feedback index
    const feedbackIndex = await redis.get('feedback-index') || '[]';
    const index = JSON.parse(feedbackIndex as string);
    index.push({
      id: feedbackId,
      artifactId,
      userId,
      timestamp: newFeedback.timestamp,
      sentiment: newFeedback.sentiment,
      priority: newFeedback.priority
    });
    await redis.set('feedback-index', JSON.stringify(index));

    // Log feedback event
    await logFeedbackEvent(feedbackId, 'feedback_submitted', {
      artifactId,
      userId,
      sentiment: newFeedback.sentiment,
      priority: newFeedback.priority
    });

    return NextResponse.json({
      success: true,
      data: {
        feedback: newFeedback,
        artifact,
        message: 'Feedback submitted successfully'
      }
    });

  } catch (error) {
    console.error('Feedback submission error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to submit feedback'
    }, { status: 500 });
  }
}

/**
 * GET /api/artifacts/feedback
 * Get feedback for an artifact or all feedback
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const artifactId = searchParams.get('artifactId');
    const userId = searchParams.get('userId');
    const limit = parseInt(searchParams.get('limit') || '50');

    if (artifactId) {
      // Get feedback for specific artifact
      const artifactData = await redis.hget('artifacts', artifactId);
      if (!artifactData) {
        return NextResponse.json({
          success: false,
          error: 'Artifact not found'
        }, { status: 404 });
      }

      const artifact: Artifact = JSON.parse(artifactData as string);
      return NextResponse.json({
        success: true,
        data: {
          feedback: artifact.feedback || [],
          totalFeedback: artifact.feedback?.length || 0
        }
      });
    }

    // Get all feedback with optional filtering
    const feedbackIndex = await redis.get('feedback-index') || '[]';
    let feedbackList = JSON.parse(feedbackIndex as string);

    // Filter by user if specified
    if (userId) {
      feedbackList = feedbackList.filter((f: any) => f.userId === userId);
    }

    // Sort by timestamp (newest first) and limit
    feedbackList.sort((a: any, b: any) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    feedbackList = feedbackList.slice(0, limit);

    // Get full feedback details
    const detailedFeedback = await Promise.all(
      feedbackList.map(async (f: any) => {
        const feedbackData = await redis.hget('feedback', f.id);
        return feedbackData ? JSON.parse(feedbackData as string) : null;
      })
    );

    const validFeedback = detailedFeedback.filter(f => f !== null);

    return NextResponse.json({
      success: true,
      data: {
        feedback: validFeedback,
        totalFeedback: validFeedback.length,
        hasMore: feedbackList.length === limit
      }
    });

  } catch (error) {
    console.error('Feedback retrieval error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get feedback'
    }, { status: 500 });
  }
}

/**
 * PUT /api/artifacts/feedback
 * Update feedback processing status
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { feedbackId, processed, agentConsultations } = body;

    if (!feedbackId) {
      return NextResponse.json({
        success: false,
        error: 'feedbackId is required'
      }, { status: 400 });
    }

    // Get feedback
    const feedbackData = await redis.hget('feedback', feedbackId);
    if (!feedbackData) {
      return NextResponse.json({
        success: false,
        error: 'Feedback not found'
      }, { status: 404 });
    }

    const feedback: Feedback = JSON.parse(feedbackData as string);

    // Update feedback
    if (processed !== undefined) {
      feedback.processed = processed;
    }
    if (agentConsultations) {
      feedback.agentConsultations = agentConsultations;
    }

    // Save updated feedback
    await redis.hset('feedback', feedbackId, JSON.stringify(feedback));

    // Update artifact feedback as well
    const artifactData = await redis.hget('artifacts', feedback.artifactId);
    if (artifactData) {
      const artifact: Artifact = JSON.parse(artifactData as string);
      if (artifact.feedback) {
        const feedbackIndex = artifact.feedback.findIndex(f => f.id === feedbackId);
        if (feedbackIndex !== -1) {
          artifact.feedback[feedbackIndex] = feedback;
          await redis.hset('artifacts', feedback.artifactId, JSON.stringify(artifact));
        }
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        feedback,
        message: 'Feedback updated successfully'
      }
    });

  } catch (error) {
    console.error('Feedback update error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update feedback'
    }, { status: 500 });
  }
}

// Helper functions
function analyzeFeedbackSentiment(feedback: string): 'positive' | 'negative' | 'neutral' {
  const positiveWords = ['good', 'great', 'excellent', 'perfect', 'amazing', 'love', 'like', 'awesome'];
  const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'wrong', 'error', 'issue', 'problem'];
  
  const lowerFeedback = feedback.toLowerCase();
  const positiveCount = positiveWords.filter(word => lowerFeedback.includes(word)).length;
  const negativeCount = negativeWords.filter(word => lowerFeedback.includes(word)).length;
  
  if (positiveCount > negativeCount) return 'positive';
  if (negativeCount > positiveCount) return 'negative';
  return 'neutral';
}

function categorizeFeedback(feedback: string): string[] {
  const categories: string[] = [];
  const lowerFeedback = feedback.toLowerCase();
  
  if (lowerFeedback.includes('content') || lowerFeedback.includes('text') || lowerFeedback.includes('writing')) {
    categories.push('content');
  }
  if (lowerFeedback.includes('structure') || lowerFeedback.includes('organization') || lowerFeedback.includes('flow')) {
    categories.push('structure');
  }
  if (lowerFeedback.includes('seo') || lowerFeedback.includes('keyword') || lowerFeedback.includes('search')) {
    categories.push('seo');
  }
  if (lowerFeedback.includes('style') || lowerFeedback.includes('tone') || lowerFeedback.includes('voice')) {
    categories.push('style');
  }
  if (lowerFeedback.includes('accuracy') || lowerFeedback.includes('fact') || lowerFeedback.includes('correct')) {
    categories.push('accuracy');
  }
  
  return categories.length > 0 ? categories : ['general'];
}

function determineFeedbackPriority(feedback: string): 'low' | 'medium' | 'high' {
  const highPriorityWords = ['urgent', 'critical', 'important', 'must', 'required', 'essential'];
  const lowPriorityWords = ['minor', 'small', 'suggestion', 'consider', 'maybe', 'optional'];
  
  const lowerFeedback = feedback.toLowerCase();
  
  if (highPriorityWords.some(word => lowerFeedback.includes(word))) {
    return 'high';
  }
  if (lowPriorityWords.some(word => lowerFeedback.includes(word))) {
    return 'low';
  }
  
  return 'medium';
}

async function logFeedbackEvent(feedbackId: string, event: string, data: any) {
  try {
    const logEntry = {
      timestamp: new Date().toISOString(),
      feedbackId,
      event,
      data
    };

    const logs = await redis.get('feedback-logs') || '[]';
    const logArray = JSON.parse(logs as string);
    logArray.unshift(logEntry);

    // Keep only last 1000 log entries
    if (logArray.length > 1000) {
      logArray.splice(1000);
    }

    await redis.set('feedback-logs', JSON.stringify(logArray));
  } catch (error) {
    console.error('Failed to log feedback event:', error);
  }
}
