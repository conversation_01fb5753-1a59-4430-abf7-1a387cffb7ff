/**
 * Dynamic Collaboration V3 Specific Artifact API Route
 *
 * This file implements the API route for retrieving a specific artifact from the dynamic collaboration system.
 */

import { NextRequest, NextResponse } from 'next/server';
import logger from '../../../../../../(payload)/api/agents/collaborative-iteration/utils/logger';
import { WorkflowOrchestrator } from '../../../../../../(payload)/api/agents/dynamic-collaboration-v3';

/**
 * GET handler for retrieving a specific artifact from a dynamic collaboration session
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ artifactId: string }> }
): Promise<NextResponse> {
  try {
    const sessionId = req.nextUrl.searchParams.get('sessionId');
    const { artifactId } = await params;

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing sessionId parameter' },
        { status: 400 }
      );
    }

    // Create orchestrator
    const orchestrator = new WorkflowOrchestrator(sessionId);

    // Get state
    const state = await orchestrator.getState();

    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Get artifact
    const artifact = state.artifacts[artifactId];

    if (!artifact) {
      return NextResponse.json(
        { error: 'Artifact not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      sessionId,
      artifact
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error retrieving artifact for dynamic collaboration session`, {
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: err.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}
