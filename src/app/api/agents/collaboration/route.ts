/**
 * Agent Collaboration API
 * 
 * Handles agent collaboration for feedback processing and artifact improvement
 */

import { NextRequest, NextResponse } from 'next/server';
import { Redis } from '@upstash/redis';

// Initialize Redis client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

interface AgentCollaboration {
  id: string;
  type: 'feedback_analysis' | 'artifact_improvement' | 'quality_assessment';
  artifactId: string;
  feedback?: string;
  workflowExecutionId: string;
  stepId: string;
  requestedAt: string;
  completedAt?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  consultations: Array<{
    consultationId: string;
    agentId: string;
    startedAt: string;
    completedAt?: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    confidence: number;
    processingTime: number;
    suggestions: Array<{
      area: string;
      suggestion: string;
      priority: 'low' | 'medium' | 'high';
      confidence: number;
      reasoning: string;
    }>;
    metadata?: any;
  }>;
}

/**
 * POST /api/agents/collaboration
 * Trigger agent collaboration for feedback processing
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      artifactId,
      feedback,
      workflowExecutionId,
      stepId,
      collaborationType = 'feedback_analysis',
      specificAgents
    } = body;

    if (!artifactId || !workflowExecutionId) {
      return NextResponse.json({
        success: false,
        error: 'artifactId and workflowExecutionId are required'
      }, { status: 400 });
    }

    // Get artifact for context
    const artifactData = await redis.hget('artifacts', artifactId);
    if (!artifactData) {
      return NextResponse.json({
        success: false,
        error: 'Artifact not found'
      }, { status: 404 });
    }

    const artifact = JSON.parse(artifactData as string);

    // Create collaboration session
    const collaborationId = `collab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const collaboration: AgentCollaboration = {
      id: collaborationId,
      type: collaborationType,
      artifactId,
      feedback,
      workflowExecutionId,
      stepId,
      requestedAt: new Date().toISOString(),
      status: 'processing',
      consultations: []
    };

    // Determine which agents to consult
    const agentsToConsult = specificAgents || await selectAgentsForCollaboration(
      collaborationType,
      artifact,
      feedback
    );

    // Start agent consultations
    const consultationPromises = agentsToConsult.map(async (agentId: string) => {
      return await consultAgent(agentId, artifact, feedback, collaborationType);
    });

    const consultationResults = await Promise.allSettled(consultationPromises);
    
    // Process consultation results
    consultationResults.forEach((result, index) => {
      const agentId = agentsToConsult[index];
      
      if (result.status === 'fulfilled') {
        collaboration.consultations.push(result.value);
      } else {
        // Add failed consultation
        collaboration.consultations.push({
          consultationId: `consultation-${Date.now()}-${index}`,
          agentId,
          startedAt: new Date().toISOString(),
          completedAt: new Date().toISOString(),
          status: 'failed',
          confidence: 0,
          processingTime: 0,
          suggestions: [],
          metadata: { error: result.reason }
        });
      }
    });

    collaboration.status = 'completed';
    collaboration.completedAt = new Date().toISOString();

    // Save collaboration session
    await redis.hset('agent-collaborations', collaborationId, JSON.stringify(collaboration));

    // Update collaboration index
    const collaborationIndex = await redis.get('collaboration-index') || '[]';
    const index = JSON.parse(collaborationIndex as string);
    index.push({
      id: collaborationId,
      type: collaborationType,
      artifactId,
      timestamp: collaboration.requestedAt,
      agentCount: collaboration.consultations.length,
      successfulConsultations: collaboration.consultations.filter(c => c.status === 'completed').length
    });
    await redis.set('collaboration-index', JSON.stringify(index));

    // Log collaboration event
    await logCollaborationEvent(collaborationId, 'collaboration_completed', {
      type: collaborationType,
      artifactId,
      agentCount: collaboration.consultations.length,
      successfulConsultations: collaboration.consultations.filter(c => c.status === 'completed').length
    });

    return NextResponse.json({
      success: true,
      data: {
        collaboration,
        consultations: collaboration.consultations,
        summary: {
          totalConsultations: collaboration.consultations.length,
          successfulConsultations: collaboration.consultations.filter(c => c.status === 'completed').length,
          averageConfidence: collaboration.consultations.reduce((sum, c) => sum + c.confidence, 0) / collaboration.consultations.length,
          totalSuggestions: collaboration.consultations.reduce((sum, c) => sum + c.suggestions.length, 0)
        }
      }
    });

  } catch (error) {
    console.error('Agent collaboration error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to process agent collaboration'
    }, { status: 500 });
  }
}

/**
 * GET /api/agents/collaboration
 * Get collaboration history and results
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const collaborationId = searchParams.get('collaborationId');
    const artifactId = searchParams.get('artifactId');
    const type = searchParams.get('type');
    const limit = parseInt(searchParams.get('limit') || '20');

    if (collaborationId) {
      // Get specific collaboration
      const collaborationData = await redis.hget('agent-collaborations', collaborationId);
      if (!collaborationData) {
        return NextResponse.json({
          success: false,
          error: 'Collaboration not found'
        }, { status: 404 });
      }

      return NextResponse.json({
        success: true,
        data: {
          collaboration: JSON.parse(collaborationData as string)
        }
      });
    }

    // Get collaboration index with filtering
    const collaborationIndex = await redis.get('collaboration-index') || '[]';
    let collaborations = JSON.parse(collaborationIndex as string);

    // Apply filters
    if (artifactId) {
      collaborations = collaborations.filter((c: any) => c.artifactId === artifactId);
    }
    if (type) {
      collaborations = collaborations.filter((c: any) => c.type === type);
    }

    // Sort by timestamp (newest first) and limit
    collaborations.sort((a: any, b: any) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    collaborations = collaborations.slice(0, limit);

    return NextResponse.json({
      success: true,
      data: {
        collaborations,
        totalCollaborations: collaborations.length,
        hasMore: collaborations.length === limit
      }
    });

  } catch (error) {
    console.error('Collaboration retrieval error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get collaborations'
    }, { status: 500 });
  }
}

// Helper functions
async function selectAgentsForCollaboration(
  type: string,
  artifact: any,
  feedback?: string
): Promise<string[]> {
  const availableAgents = ['seo-keyword', 'market-research', 'content-strategy'];
  
  switch (type) {
    case 'feedback_analysis':
      // Select agents based on feedback content
      if (feedback) {
        const agents: string[] = [];
        const lowerFeedback = feedback.toLowerCase();
        
        if (lowerFeedback.includes('seo') || lowerFeedback.includes('keyword') || lowerFeedback.includes('search')) {
          agents.push('seo-keyword');
        }
        if (lowerFeedback.includes('market') || lowerFeedback.includes('audience') || lowerFeedback.includes('competitor')) {
          agents.push('market-research');
        }
        if (lowerFeedback.includes('content') || lowerFeedback.includes('structure') || lowerFeedback.includes('strategy')) {
          agents.push('content-strategy');
        }
        
        return agents.length > 0 ? agents : ['content-strategy']; // Default to content strategy
      }
      return ['content-strategy'];
      
    case 'artifact_improvement':
      // Select all relevant agents for comprehensive improvement
      return availableAgents;
      
    case 'quality_assessment':
      // Select agents based on artifact type
      if (artifact.type === 'blog-post' || artifact.type === 'article') {
        return ['seo-keyword', 'content-strategy'];
      }
      return ['content-strategy'];
      
    default:
      return ['content-strategy'];
  }
}

async function consultAgent(
  agentId: string,
  artifact: any,
  feedback: string | undefined,
  collaborationType: string
): Promise<any> {
  const startTime = Date.now();
  const consultationId = `consultation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  // Simulate agent consultation with realistic processing time
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 3000));
  
  const processingTime = Date.now() - startTime;
  const confidence = 0.7 + Math.random() * 0.3; // 0.7 to 1.0
  
  // Generate agent-specific suggestions based on feedback and artifact
  const suggestions = generateAgentSuggestions(agentId, artifact, feedback, collaborationType);
  
  return {
    consultationId,
    agentId,
    startedAt: new Date(startTime).toISOString(),
    completedAt: new Date().toISOString(),
    status: 'completed',
    confidence,
    processingTime,
    suggestions,
    metadata: {
      artifactType: artifact.type,
      feedbackLength: feedback?.length || 0,
      collaborationType
    }
  };
}

function generateAgentSuggestions(
  agentId: string,
  artifact: any,
  feedback: string | undefined,
  collaborationType: string
): any[] {
  const suggestions: any[] = [];
  
  switch (agentId) {
    case 'seo-keyword':
      suggestions.push({
        area: 'SEO Optimization',
        suggestion: 'Improve keyword density and add semantic keywords for better search ranking',
        priority: 'high',
        confidence: 0.85,
        reasoning: 'Current content lacks target keyword optimization'
      });
      
      if (feedback?.toLowerCase().includes('seo')) {
        suggestions.push({
          area: 'Meta Description',
          suggestion: 'Optimize meta description length and include primary keyword',
          priority: 'medium',
          confidence: 0.9,
          reasoning: 'Feedback specifically mentions SEO concerns'
        });
      }
      break;
      
    case 'market-research':
      suggestions.push({
        area: 'Audience Targeting',
        suggestion: 'Adjust content tone to better match target audience preferences',
        priority: 'medium',
        confidence: 0.8,
        reasoning: 'Market analysis suggests different audience expectations'
      });
      
      suggestions.push({
        area: 'Competitive Analysis',
        suggestion: 'Include unique value propositions to differentiate from competitors',
        priority: 'high',
        confidence: 0.75,
        reasoning: 'Competitor content analysis reveals gaps in positioning'
      });
      break;
      
    case 'content-strategy':
      suggestions.push({
        area: 'Content Structure',
        suggestion: 'Reorganize content flow for better readability and engagement',
        priority: 'high',
        confidence: 0.9,
        reasoning: 'Current structure may not optimize user engagement'
      });
      
      if (feedback?.toLowerCase().includes('structure') || feedback?.toLowerCase().includes('flow')) {
        suggestions.push({
          area: 'Information Architecture',
          suggestion: 'Implement clearer section headers and logical content progression',
          priority: 'high',
          confidence: 0.95,
          reasoning: 'Feedback directly addresses structural concerns'
        });
      }
      break;
  }
  
  return suggestions;
}

async function logCollaborationEvent(collaborationId: string, event: string, data: any) {
  try {
    const logEntry = {
      timestamp: new Date().toISOString(),
      collaborationId,
      event,
      data
    };

    const logs = await redis.get('collaboration-logs') || '[]';
    const logArray = JSON.parse(logs as string);
    logArray.unshift(logEntry);

    // Keep only last 1000 log entries
    if (logArray.length > 1000) {
      logArray.splice(1000);
    }

    await redis.set('collaboration-logs', JSON.stringify(logArray));
  } catch (error) {
    console.error('Failed to log collaboration event:', error);
  }
}
