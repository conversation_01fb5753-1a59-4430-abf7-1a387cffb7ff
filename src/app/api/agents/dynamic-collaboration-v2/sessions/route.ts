import { NextRequest, NextResponse } from 'next/server';
import { DynamicWorkflowOrchestrator } from '../../../../(payload)/api/agents/dynamic-collaboration-v2/dynamic-workflow-orchestrator';
import { v4 as uuidv4 } from 'uuid';
import { ContentGenerationParams } from '../../../../(payload)/api/agents/dynamic-collaboration-v2/types';
import { stateStore } from '../../../../(payload)/api/agents/collaborative-iteration/utils/stateStore';

/**
 * API route handler for dynamic collaboration sessions
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Parse the request body
    const body = await req.json();
    const {
      topic,
      contentType = 'blog-article',
      targetAudience = 'general',
      tone = 'professional',
      keywords = [],
      additionalInstructions = ''
    } = body;

    // Validate required parameters
    if (!topic) {
      return NextResponse.json(
        { error: 'Missing required parameter: topic' },
        { status: 400 }
      );
    }

    // Generate a new session ID
    const sessionId = uuidv4();

    // Create parameters object
    const params: ContentGenerationParams = {
      topic,
      contentType,
      targetAudience,
      tone,
      keywords,
      additionalParams: {
        additionalInstructions
      }
    };

    // Initialize the collaboration
    const success = await DynamicWorkflowOrchestrator.initiate(sessionId, params);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to initialize collaboration session' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      sessionId,
      message: 'Dynamic collaboration session created successfully'
    });
  } catch (error) {
    console.error('Error creating dynamic collaboration session:', error);
    return NextResponse.json(
      { error: 'An error occurred while processing your request' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Get all sessions
    // In a real implementation, you would fetch sessions from a database
    // For now, we'll return a mock response
    const sessions = await stateStore.getAllStates();

    // Filter for dynamic collaboration sessions only
    const dynamicSessions = Object.entries(sessions)
      .filter(([_, state]) => state.currentPhase !== undefined)
      .map(([id, state]) => ({
        id,
        topic: state.topic,
        status: state.status,
        startTime: state.startTime,
        endTime: state.endTime,
        currentPhase: state.currentPhase
      }));

    return NextResponse.json({
      success: true,
      sessions: dynamicSessions
    });
  } catch (error) {
    console.error('Error fetching dynamic collaboration sessions:', error);
    return NextResponse.json(
      { error: 'An error occurred while processing your request' },
      { status: 500 }
    );
  }
}
