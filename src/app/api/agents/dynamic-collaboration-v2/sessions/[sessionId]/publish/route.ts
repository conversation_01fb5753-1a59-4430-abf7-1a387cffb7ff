import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { DynamicWorkflowOrchestrator } from '../../../../../../(payload)/api/agents/dynamic-collaboration-v2/dynamic-workflow-orchestrator';
import { stateStore } from '../../../../../../(payload)/api/agents/collaborative-iteration/utils/stateStore';
import { DynamicMessageType, DynamicWorkflowPhase } from '../../../../../../(payload)/api/agents/dynamic-collaboration-v2/types';
import { ArtifactStatus } from '../../../../../../(payload)/api/collaborative-iteration/types';

/**
 * API route handler for publishing content from dynamic collaboration sessions
 */
export async function POST(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
): Promise<NextResponse> {
  try {
    const { sessionId } = params;

    // Validate session ID
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Parse the request body
    const body = await req.json();
    const {
      title,
      content,
      status = 'published'
    } = body;

    // Validate required parameters
    if (!title) {
      return NextResponse.json(
        { error: 'Missing required parameter: title' },
        { status: 400 }
      );
    }

    if (!content) {
      return NextResponse.json(
        { error: 'Missing required parameter: content' },
        { status: 400 }
      );
    }

    // Create the artifact
    const artifactId = uuidv4();
    const timestamp = new Date().toISOString();

    // Add the artifact to the state
    await stateStore.updateState(sessionId, (currentState) => {
      if (!currentState) return currentState;

      const newArtifact = {
        id: artifactId,
        type: 'published-article',
        name: title,
        content,
        creator: 'user',
        status: ArtifactStatus.COMPLETED,
        timestamp,
        data: {
          publishStatus: status,
          publishedAt: timestamp
        }
      };

      return {
        ...currentState,
        artifacts: {
          ...currentState.artifacts,
          [artifactId]: newArtifact
        },
        generatedArtifacts: [...(currentState.generatedArtifacts || []), artifactId],
        status: 'completed',
        endTime: timestamp,
        currentPhase: DynamicWorkflowPhase.COMPLETED
      };
    });

    // Create a notification message
    const messageId = uuidv4();
    const conversationId = uuidv4();

    await stateStore.updateState(sessionId, (currentState) => {
      if (!currentState) return currentState;

      const notificationMessage = {
        id: messageId,
        timestamp,
        from: 'system',
        to: 'all',
        type: DynamicMessageType.SYSTEM_NOTIFICATION,
        content: {
          message: `Article "${title}" has been published`,
          artifactId,
          status
        },
        conversationId
      };

      const updatedDynamicMessages = {
        ...currentState.dynamicMessages,
        [messageId]: notificationMessage
      };

      const updatedConversations = { ...currentState.conversations };
      if (!updatedConversations[conversationId]) {
        updatedConversations[conversationId] = [];
      }
      updatedConversations[conversationId].push(messageId);

      return {
        ...currentState,
        dynamicMessages: updatedDynamicMessages,
        conversations: updatedConversations
      };
    });

    return NextResponse.json({
      success: true,
      artifactId,
      message: 'Content published successfully',
      publishStatus: status
    });
  } catch (error) {
    console.error('Error publishing content from dynamic collaboration session:', error);
    return NextResponse.json(
      { error: 'An error occurred while processing your request' },
      { status: 500 }
    );
  }
}
