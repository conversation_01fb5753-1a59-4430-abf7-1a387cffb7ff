import { NextRequest, NextResponse } from 'next/server';
import { DynamicWorkflowOrchestrator } from '../../../../../(payload)/api/agents/dynamic-collaboration-v2/dynamic-workflow-orchestrator';
import { stateStore } from '../../../../../(payload)/api/agents/collaborative-iteration/utils/stateStore';
import { DynamicWorkflowPhase } from '../../../../../(payload)/api/agents/dynamic-collaboration-v2/types';

/**
 * API route handler for specific dynamic collaboration sessions
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
): Promise<NextResponse> {
  try {
    const { sessionId } = await params;

    // Validate session ID
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Get the session state from the state store
    const sessionState = await stateStore.getState(sessionId);

    if (!sessionState) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(sessionState);
  } catch (error) {
    console.error('Error retrieving dynamic collaboration session:', error);
    return NextResponse.json(
      { error: 'An error occurred while processing your request' },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
): Promise<NextResponse> {
  try {
    const { sessionId } = params;

    // Validate session ID
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Parse the request body
    const body = await req.json();
    const { action, phase } = body;

    // Create a new orchestrator instance
    const orchestrator = new DynamicWorkflowOrchestrator(sessionId);

    // Perform the requested action
    let success = false;

    switch (action) {
      case 'transition-phase':
        if (!phase) {
          return NextResponse.json(
            { error: 'Missing required parameter: phase' },
            { status: 400 }
          );
        }
        success = await orchestrator.transitionToPhase(phase as DynamicWorkflowPhase);
        break;

      case 'pause':
        // Implement pause functionality
        await stateStore.updateState(sessionId, (currentState) => {
          if (!currentState) return currentState;
          return {
            ...currentState,
            status: 'paused'
          };
        });
        success = true;
        break;

      case 'resume':
        // Implement resume functionality
        await stateStore.updateState(sessionId, (currentState) => {
          if (!currentState) return currentState;
          return {
            ...currentState,
            status: 'active'
          };
        });
        success = true;
        break;

      case 'cancel':
        // Implement cancel functionality
        await stateStore.updateState(sessionId, (currentState) => {
          if (!currentState) return currentState;
          return {
            ...currentState,
            status: 'failed',
            endTime: new Date().toISOString()
          };
        });
        success = true;
        break;

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

    if (!success) {
      return NextResponse.json(
        { error: `Failed to perform action: ${action}` },
        { status: 500 }
      );
    }

    // Get the updated session state
    const updatedState = await stateStore.getState(sessionId);

    return NextResponse.json({
      success: true,
      message: `Action ${action} performed successfully`,
      state: updatedState
    });
  } catch (error) {
    console.error('Error updating dynamic collaboration session:', error);
    return NextResponse.json(
      { error: 'An error occurred while processing your request' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
): Promise<NextResponse> {
  try {
    const { sessionId } = params;

    // Validate session ID
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Delete the session state from the state store
    await stateStore.deleteState(sessionId);

    return NextResponse.json({
      success: true,
      message: 'Session deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting dynamic collaboration session:', error);
    return NextResponse.json(
      { error: 'An error occurred while processing your request' },
      { status: 500 }
    );
  }
}
