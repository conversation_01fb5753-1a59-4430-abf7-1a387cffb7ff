import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { DynamicWorkflowOrchestrator } from '../../../../../../(payload)/api/agents/dynamic-collaboration-v2/dynamic-workflow-orchestrator';
import { stateStore } from '../../../../../../(payload)/api/agents/collaborative-iteration/utils/stateStore';
import { DynamicMessageType } from '../../../../../../(payload)/api/agents/dynamic-collaboration-v2/types';
import { ArtifactStatus } from '../../../../../../(payload)/api/collaborative-iteration/types';

/**
 * API route handler for artifacts in dynamic collaboration sessions
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
): Promise<NextResponse> {
  try {
    const { sessionId } = params;

    // Validate session ID
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Get the session state from the state store
    const sessionState = await stateStore.getState(sessionId);

    if (!sessionState) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Extract artifacts
    const artifacts = sessionState.artifacts || {};
    const generatedArtifacts = sessionState.generatedArtifacts || [];

    return NextResponse.json({
      success: true,
      artifacts: Object.values(artifacts).filter(artifact =>
        generatedArtifacts.includes(artifact.id)
      )
    });
  } catch (error) {
    console.error('Error retrieving artifacts from dynamic collaboration session:', error);
    return NextResponse.json(
      { error: 'An error occurred while processing your request' },
      { status: 500 }
    );
  }
}

export async function POST(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
): Promise<NextResponse> {
  try {
    const { sessionId } = params;

    // Validate session ID
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Parse the request body
    const body = await req.json();
    const {
      type,
      name,
      content,
      creator = 'user',
      status = ArtifactStatus.COMPLETED,
      data = {}
    } = body;

    // Validate required parameters
    if (!type) {
      return NextResponse.json(
        { error: 'Missing required parameter: type' },
        { status: 400 }
      );
    }

    if (!name) {
      return NextResponse.json(
        { error: 'Missing required parameter: name' },
        { status: 400 }
      );
    }

    // Create the artifact
    const artifactId = uuidv4();
    const timestamp = new Date().toISOString();

    // Add the artifact to the state
    await stateStore.updateState(sessionId, (currentState) => {
      if (!currentState) return currentState;

      const newArtifact = {
        id: artifactId,
        type,
        name,
        content,
        creator,
        status,
        timestamp,
        data
      };

      return {
        ...currentState,
        artifacts: {
          ...currentState.artifacts,
          [artifactId]: newArtifact
        },
        generatedArtifacts: [...(currentState.generatedArtifacts || []), artifactId]
      };
    });

    // Create a notification message
    const messageId = uuidv4();
    const conversationId = uuidv4();

    await stateStore.updateState(sessionId, (currentState) => {
      if (!currentState) return currentState;

      const notificationMessage = {
        id: messageId,
        timestamp,
        from: 'system',
        to: 'all',
        type: DynamicMessageType.ARTIFACT_UPDATE,
        content: {
          action: 'CREATED',
          artifactId,
          name,
          type
        },
        conversationId
      };

      const updatedDynamicMessages = {
        ...currentState.dynamicMessages,
        [messageId]: notificationMessage
      };

      const updatedConversations = { ...currentState.conversations };
      if (!updatedConversations[conversationId]) {
        updatedConversations[conversationId] = [];
      }
      updatedConversations[conversationId].push(messageId);

      return {
        ...currentState,
        dynamicMessages: updatedDynamicMessages,
        conversations: updatedConversations
      };
    });

    return NextResponse.json({
      success: true,
      artifactId,
      message: 'Artifact created successfully'
    });
  } catch (error) {
    console.error('Error creating artifact in dynamic collaboration session:', error);
    return NextResponse.json(
      { error: 'An error occurred while processing your request' },
      { status: 500 }
    );
  }
}
