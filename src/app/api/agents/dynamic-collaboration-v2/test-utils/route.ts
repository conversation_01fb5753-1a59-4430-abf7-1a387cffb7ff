import { NextRequest, NextResponse } from 'next/server';
import {
  dynamicStateStore,
  DynamicCollaborationState,
  DynamicWorkflowPhase,
  GoalStatus
} from '../../../../(payload)/api/agents/dynamic-collaboration-v2/state/index';

/**
 * Direct state update endpoint for testing purposes
 * This endpoint bypasses the orchestration logic and directly updates the state
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { action, sessionId, data } = body;

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    if (!action) {
      return NextResponse.json({ error: 'Action is required' }, { status: 400 });
    }

    // Get the current state
    const currentState = await dynamicStateStore.getState(sessionId);
    if (!currentState) {
      return NextResponse.json({ error: `Session ${sessionId} not found` }, { status: 404 });
    }

    let result;

    switch (action) {
      case 'updatePhase':
        result = await updatePhase(sessionId, data.phase);
        break;

      case 'completeGoal':
        result = await completeGoal(sessionId, data.goalType, data.artifactId);
        break;

      case 'addArtifact':
        result = await addArtifact(sessionId, data.artifact);
        break;

      case 'getState':
        result = { state: currentState };
        break;

      default:
        return NextResponse.json({ error: `Unknown action: ${action}` }, { status: 400 });
    }

    return NextResponse.json({ success: true, result });
  } catch (error) {
    console.error('Error in test-utils endpoint:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * Update the phase directly in the state
 */
async function updatePhase(sessionId: string, phase: DynamicWorkflowPhase): Promise<any> {
  // Update the phase directly in the state
  await dynamicStateStore.updateState(sessionId, (state: DynamicCollaborationState | null) => {
    if (!state) return state;

    // Create a message about the phase transition
    const messageId = crypto.randomUUID();
    const conversationId = crypto.randomUUID();
    const timestamp = new Date().toISOString();

    const message = {
      id: messageId,
      timestamp,
      from: 'system',
      to: 'all',
      type: 'workflow_transition',
      content: {
        message: `Transitioned to phase: ${phase}`,
        phase
      },
      conversationId
    };

    // Add to messages
    const messages = [...state.messages, message];

    // Add to dynamic messages
    const dynamicMessages = {
      ...state.dynamicMessages,
      [messageId]: message
    };

    // Add to conversations
    const conversations = { ...state.conversations };
    if (!conversations[conversationId]) {
      conversations[conversationId] = [];
    }
    conversations[conversationId].push(messageId);

    return {
      ...state,
      currentPhase: phase,
      messages,
      dynamicMessages,
      conversations
    };
  });

  // Get the updated state
  const updatedState = await dynamicStateStore.getState(sessionId);

  return {
    success: true,
    phase,
    currentPhase: updatedState?.currentPhase
  };
}

/**
 * Complete a goal directly in the state
 */
async function completeGoal(sessionId: string, goalType: string, artifactId: string): Promise<any> {
  // Get the current state
  const state = await dynamicStateStore.getState(sessionId);
  if (!state) {
    throw new Error(`Session ${sessionId} not found`);
  }

  // Find the goal with the specified type
  const goalsOfType = Object.entries(state.goals).filter(([_, g]) => g.type === goalType);

  if (goalsOfType.length === 0) {
    throw new Error(`No goals of type ${goalType} found`);
  }

  // Get the first goal of this type
  const [goalId, goal] = goalsOfType[0];

  // Update the state directly
  await dynamicStateStore.updateState(sessionId, (currentState: DynamicCollaborationState | null) => {
    if (!currentState) return currentState;

    // Update the goal status
    const updatedGoals = { ...currentState.goals };
    updatedGoals[goalId] = {
      ...updatedGoals[goalId],
      status: GoalStatus.COMPLETED,
      progress: 100,
      completedAt: new Date().toISOString(),
      artifactId
    };

    // Add to completed goals if not already there
    const completedGoals = [...(currentState.completedGoals || [])];
    if (!completedGoals.includes(goalType)) {
      completedGoals.push(goalType);
    }

    // Remove from active goals
    const activeGoals = (currentState.activeGoals || []).filter(id => id !== goalId);

    return {
      ...currentState,
      goals: updatedGoals,
      activeGoals,
      completedGoals
    };
  });

  // Get the updated state
  const updatedState = await dynamicStateStore.getState(sessionId);

  return {
    success: true,
    goalId,
    goalType,
    artifactId,
    completedGoals: updatedState?.completedGoals || []
  };
}

/**
 * Add an artifact directly to the state
 */
async function addArtifact(sessionId: string, artifact: any): Promise<any> {
  // Update the state directly
  await dynamicStateStore.updateState(sessionId, (currentState: DynamicCollaborationState | null) => {
    if (!currentState) return currentState;

    // Add the artifact to the artifacts object
    const artifacts = {
      ...currentState.artifacts,
      [artifact.id]: artifact
    };

    // Add to generatedArtifacts if not already there
    const generatedArtifacts = [...(currentState.generatedArtifacts || [])];
    if (!generatedArtifacts.includes(artifact.id)) {
      generatedArtifacts.push(artifact.id);
    }

    return {
      ...currentState,
      artifacts,
      generatedArtifacts
    };
  });

  // Get the updated state
  const updatedState = await dynamicStateStore.getState(sessionId);

  return {
    success: true,
    artifactId: artifact.id,
    artifactsCount: Object.keys(updatedState?.artifacts || {}).length,
    generatedArtifacts: updatedState?.generatedArtifacts || []
  };
}
