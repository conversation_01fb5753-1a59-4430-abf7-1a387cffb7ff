import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import {
  dynamicStateStore,
  DynamicCollaborationState,
  DynamicArtifact,
  ArtifactStatus
} from '../../../../(payload)/api/agents/dynamic-collaboration-v2/state/index';

/**
 * Debug endpoint for testing state operations
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { action, sessionId } = body;

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    // Get the current state
    const currentState = await dynamicStateStore.getState(sessionId);
    if (!currentState) {
      return NextResponse.json({ error: `Session ${sessionId} not found` }, { status: 404 });
    }

    let result;

    switch (action) {
      case 'testArtifactStorage':
        result = await testArtifactStorage(sessionId);
        break;

      case 'testGoalCompletion':
        result = await testGoalCompletion(sessionId, body.goalType, body.artifactId);
        break;

      case 'getState':
        result = { state: currentState };
        break;

      default:
        return NextResponse.json({ error: `Unknown action: ${action}` }, { status: 400 });
    }

    return NextResponse.json({ success: true, result });
  } catch (error) {
    console.error('Error in debug endpoint:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * Test artifact storage
 */
async function testArtifactStorage(sessionId: string): Promise<any> {
  // Create a test artifact
  const artifactId = uuidv4();
  const testArtifact: DynamicArtifact = {
    id: artifactId,
    type: 'test_artifact',
    title: 'Test Artifact',
    content: {
      message: 'This is a test artifact',
      timestamp: new Date().toISOString()
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'debug_system',
    status: ArtifactStatus.DRAFT,
    version: 1
  };

  // Get the state before adding the artifact
  const stateBefore = await dynamicStateStore.getState(sessionId);
  const artifactCountBefore = Object.keys(stateBefore?.artifacts || {}).length;
  const generatedArtifactsCountBefore = stateBefore?.generatedArtifacts?.length || 0;

  // Add the artifact directly to the state
  await dynamicStateStore.updateState(sessionId, (state: DynamicCollaborationState | null) => {
    if (!state) return state;

    // Add to artifacts
    const artifacts = {
      ...state.artifacts,
      [artifactId]: testArtifact
    };

    // Add to generated artifacts
    const generatedArtifacts = [...(state.generatedArtifacts || [])];
    if (!generatedArtifacts.includes(artifactId)) {
      generatedArtifacts.push(artifactId);
    }

    return {
      ...state,
      artifacts,
      generatedArtifacts
    };
  });

  // Get the state after adding the artifact
  const stateAfter = await dynamicStateStore.getState(sessionId);
  const artifactCountAfter = Object.keys(stateAfter?.artifacts || {}).length;
  const generatedArtifactsCountAfter = stateAfter?.generatedArtifacts?.length || 0;
  const artifactExists = stateAfter?.artifacts?.[artifactId] !== undefined;
  const artifactInGeneratedList = stateAfter?.generatedArtifacts?.includes(artifactId) || false;

  return {
    artifactId,
    artifactCountBefore,
    artifactCountAfter,
    generatedArtifactsCountBefore,
    generatedArtifactsCountAfter,
    artifactExists,
    artifactInGeneratedList,
    artifact: stateAfter?.artifacts?.[artifactId]
  };
}

/**
 * Test goal completion
 */
async function testGoalCompletion(sessionId: string, specificGoalType?: string, specificArtifactId?: string): Promise<any> {
  // Get the current state
  const state = await dynamicStateStore.getState(sessionId);
  if (!state) {
    throw new Error(`Session ${sessionId} not found`);
  }

  let goalId: string;
  let goal: any;
  let artifactId: string;

  if (specificGoalType) {
    // Find the goal with the specified type
    const goalsOfType = Object.entries(state.goals).filter(([_, g]) => g.type === specificGoalType);

    if (goalsOfType.length === 0) {
      return { message: `No goals of type ${specificGoalType} found` };
    }

    // Get the first goal of this type
    [goalId, goal] = goalsOfType[0];

    // Use the specified artifact ID or create a new one
    artifactId = specificArtifactId || uuidv4();
  } else {
    // Find a goal that's not completed
    const pendingGoals = Object.entries(state.goals).filter(([_, goal]) => goal.status !== 'completed');
    if (pendingGoals.length === 0) {
      return { message: 'No pending goals found' };
    }

    // Get the first pending goal
    [goalId, goal] = pendingGoals[0];

    // Create a new artifact ID
    artifactId = uuidv4();
  }

  // Create a test artifact for this goal if no specific artifact ID was provided
  let testArtifact: DynamicArtifact;

  if (specificArtifactId && state.artifacts && state.artifacts[specificArtifactId]) {
    // Use the existing artifact
    testArtifact = state.artifacts[specificArtifactId];
  } else {
    // Create a new test artifact
    testArtifact = {
      id: artifactId,
      type: goal.type,
      title: `${goal.type} Artifact`,
      content: {
        message: `This is a test artifact for goal ${goal.type}`,
        timestamp: new Date().toISOString()
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'debug_system',
      status: ArtifactStatus.DRAFT,
      version: 1
    };
  }

  // Add the artifact to the state
  await dynamicStateStore.updateState(sessionId, (state: DynamicCollaborationState | null) => {
    if (!state) return state;

    // Add to artifacts
    const artifacts = {
      ...state.artifacts,
      [artifactId]: testArtifact
    };

    // Add to generated artifacts
    const generatedArtifacts = [...(state.generatedArtifacts || [])];
    if (!generatedArtifacts.includes(artifactId)) {
      generatedArtifacts.push(artifactId);
    }

    return {
      ...state,
      artifacts,
      generatedArtifacts
    };
  });

  // Complete the goal
  await dynamicStateStore.updateState(sessionId, (state: DynamicCollaborationState | null) => {
    if (!state) return state;

    // Update the goal
    const updatedGoals = { ...state.goals };
    updatedGoals[goalId] = {
      ...updatedGoals[goalId],
      status: 'completed',
      progress: 100,
      completedAt: new Date().toISOString(),
      artifactId
    };

    // Add to completed goals
    const completedGoals = [...(state.completedGoals || [])];
    if (!completedGoals.includes(goal.type)) {
      completedGoals.push(goal.type);
    }

    // Remove from active goals
    const activeGoals = (state.activeGoals || []).filter(id => id !== goalId);

    return {
      ...state,
      goals: updatedGoals,
      activeGoals,
      completedGoals
    };
  });

  // Get the updated state
  const updatedState = await dynamicStateStore.getState(sessionId);
  const updatedGoal = updatedState?.goals[goalId];
  const artifactExists = updatedState?.artifacts?.[artifactId] !== undefined;
  const goalCompleted = updatedGoal?.status === 'completed';
  const goalInCompletedList = updatedState?.completedGoals?.includes(goal.type) || false;

  return {
    goalId,
    goalType: goal.type,
    artifactId,
    artifactExists,
    goalCompleted,
    goalInCompletedList,
    goal: updatedGoal,
    artifact: updatedState?.artifacts?.[artifactId]
  };
}
