import { NextRequest, NextResponse } from 'next/server';
import {
  processJsonRpcRequest,
  processJsonRpcBatch
} from '../../../../(payload)/api/agents/dynamic-collaboration-v2/routes/jsonrpc';

/**
 * A2A JSONRPC API endpoint for dynamic collaboration
 * This single endpoint replaces multiple route.ts files with a unified interface
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const jsonrpcRequest = await req.json();

    // Handle both single requests and batches
    const isBatch = Array.isArray(jsonrpcRequest);

    if (isBatch) {
      const responses = await processJsonRpcBatch(jsonrpcRequest);
      return NextResponse.json(responses);
    } else {
      const response = await processJsonRpcRequest(jsonrpcRequest);
      return NextResponse.json(response);
    }
  } catch (error) {
    return NextResponse.json({
      jsonrpc: '2.0',
      error: { code: -32700, message: 'Parse error' },
      id: null
    }, { status: 400 });
  }
}
