import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import {
  DynamicWorkflowPhase,
  DynamicMessageType,
  createStateManager,
  dynamicStateStore,
  DynamicCollaborationState,
  GoalStatus
} from '../../../../(payload)/api/agents/dynamic-collaboration-v2/state/index'
import { DynamicWorkflowOrchestratorV2 } from '../../../../(payload)/api/agents/dynamic-collaboration-v2/dynamic-workflow-orchestrator-v2';

// Create a simple logger for this file
const logger = {
  info: (message: string, data?: any) => console.info(`[INFO] ${message}`, data || ''),
  error: (message: string, data?: any) => console.error(`[ERROR] ${message}`, data || ''),
  warn: (message: string, data?: any) => console.warn(`[WARN] ${message}`, data || ''),
  debug: (message: string, data?: any) => console.debug(`[DEBUG] ${message}`, data || '')
};

// We're using the real implementations, so we don't need to define enums here

// Using real implementations imported from the modules

// Map to track active sessions and their last check time
const activeSessionsLastCheck: Record<string, number> = {};

/**
 * JSON-RPC V2 API endpoint for dynamic collaboration
 * This endpoint uses the new state management system
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { method, params, id } = body;

    // Validate request
    if (!method || !params) {
      return NextResponse.json({
        jsonrpc: '2.0',
        error: {
          code: -32600,
          message: 'Invalid Request'
        },
        id: id || null
      }, { status: 400 });
    }

    // Process method
    let result;

    switch (method) {
      case 'initiate':
        result = await handleInitiate(params);
        break;

      case 'getState':
        result = await handleGetState(params);
        break;

      case 'transitionPhase':
        result = await handleTransitionPhase(params);
        break;

      case 'processUserMessage':
        result = await handleProcessUserMessage(params);
        break;

      case 'addUserMessage':
        result = await handleAddUserMessage(params);
        break;

      case 'completeGoal':
        result = await handleCompleteGoal(params);
        break;

      case 'handleFeedback':
        result = await handleFeedback(params);
        break;

      case 'addArtifact':
        result = await handleAddArtifact(params);
        break;

      case 'checkPhaseTransition':
        result = await handleCheckPhaseTransition(params);
        break;

      default:
        return NextResponse.json({
          jsonrpc: '2.0',
          error: {
            code: -32601,
            message: 'Method not found'
          },
          id: id || null
        }, { status: 404 });
    }

    // Return result
    return NextResponse.json({
      jsonrpc: '2.0',
      result,
      id: id || null
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error processing JSON-RPC request`, {
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json({
      jsonrpc: '2.0',
      error: {
        code: -32603,
        message: 'Internal error',
        data: err.message || String(error)
      },
      id: null
    }, { status: 500 });
  }
}

/**
 * Handle initiate method
 */
async function handleInitiate(params: any) {
  const { topic, contentType, targetAudience, tone, keywords, additionalInstructions, referenceUrls } = params;

  // Validate required parameters
  if (!topic) {
    throw new Error('Missing required parameter: topic');
  }

  // Generate session ID
  const sessionId = params.sessionId || uuidv4();

  // Initialize session
  const success = await DynamicWorkflowOrchestratorV2.initiate(sessionId, {
    topic,
    contentType: contentType || 'blog-article',
    targetAudience: targetAudience || 'general audience',
    tone: tone || 'informative',
    keywords: keywords || [],
    additionalInstructions,
    referenceUrls
  });

  if (!success) {
    throw new Error('Failed to initialize session');
  }

  return { sessionId, success };
}

/**
 * Handle getState method
 */
async function handleGetState(params: any) {
  const { sessionId } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }

  // Get state manager
  const stateManager = createStateManager(sessionId);

  // Get state
  const state = await stateManager.getState();
  if (!state) {
    throw new Error(`Session ${sessionId} not found`);
  }

  // Track this as an active session
  activeSessionsLastCheck[sessionId] = Date.now();

  // Check for goal completion and phase transitions if it's been more than 10 seconds
  // since the last check for this session
  const lastCheckTime = activeSessionsLastCheck[sessionId] || 0;
  const currentTime = Date.now();

  if (currentTime - lastCheckTime > 10000) {
    // Update the last check time
    activeSessionsLastCheck[sessionId] = currentTime;

    // Check for goal completion and phase transitions in the background
    checkGoalCompletionAndPhaseTransition(sessionId).catch(error => {
      const err = error as Error;
      logger.error(`Error checking goal completion and phase transition`, {
        sessionId,
        error: err.message || String(error)
      });
    });
  }

  return { state };
}

/**
 * Check for goal completion and phase transitions
 */
async function checkGoalCompletionAndPhaseTransition(sessionId: string): Promise<void> {
  try {
    logger.info(`Checking goal completion and phase transition for session ${sessionId}`);

    // Get the current state to log
    const state = await dynamicStateStore.getState(sessionId);
    if (!state) {
      logger.error(`Session not found when checking goal completion`, {
        sessionId
      });
      return;
    }

    logger.info(`Current state before phase transition check`, {
      sessionId,
      currentPhase: state.currentPhase,
      completedGoals: JSON.stringify(state.completedGoals || []),
      activeGoals: JSON.stringify(state.activeGoals || []),
      artifactsCount: Object.keys(state.artifacts || {}).length,
      generatedArtifactsCount: (state.generatedArtifacts || []).length
    });

    // Check if we need to manually transition based on completed goals
    const { currentPhase, completedGoals } = state;

    // Determine if we should transition based on completed goals
    let shouldTransition = false;
    let nextPhase = currentPhase;

    if (currentPhase === DynamicWorkflowPhase.RESEARCH) {
      // If both market_research and keyword_analysis are completed, transition to CREATION
      const hasMarketResearch = completedGoals.includes('market_research');
      const hasKeywordAnalysis = completedGoals.includes('keyword_analysis');

      logger.info(`Research phase check: market_research=${hasMarketResearch}, keyword_analysis=${hasKeywordAnalysis}`, {
        sessionId,
        completedGoals: JSON.stringify(completedGoals)
      });

      if (hasMarketResearch && hasKeywordAnalysis) {
        shouldTransition = true;
        nextPhase = DynamicWorkflowPhase.CREATION;
      }
    } else if (currentPhase === DynamicWorkflowPhase.CREATION) {
      // If content_strategy and content_creation are completed, transition to REVIEW
      const hasContentStrategy = completedGoals.includes('content_strategy');
      const hasContentCreation = completedGoals.includes('content_creation');

      logger.info(`Creation phase check: content_strategy=${hasContentStrategy}, content_creation=${hasContentCreation}`, {
        sessionId,
        completedGoals: JSON.stringify(completedGoals)
      });

      if (hasContentStrategy && hasContentCreation) {
        shouldTransition = true;
        nextPhase = DynamicWorkflowPhase.REVIEW;
      }
    } else if (currentPhase === DynamicWorkflowPhase.REVIEW) {
      // If seo_optimization and quality_assessment are completed, transition to FINALIZATION
      const hasSeoOptimization = completedGoals.includes('seo_optimization');
      const hasQualityAssessment = completedGoals.includes('quality_assessment');

      logger.info(`Review phase check: seo_optimization=${hasSeoOptimization}, quality_assessment=${hasQualityAssessment}`, {
        sessionId,
        completedGoals: JSON.stringify(completedGoals)
      });

      if (hasSeoOptimization && hasQualityAssessment) {
        shouldTransition = true;
        nextPhase = DynamicWorkflowPhase.FINALIZATION;
      }
    }

    // If we should transition, do it directly
    if (shouldTransition && nextPhase !== currentPhase) {
      logger.info(`Manually transitioning from ${currentPhase} to ${nextPhase}`, {
        sessionId,
        currentPhase,
        nextPhase,
        completedGoals: JSON.stringify(completedGoals)
      });

      // Create state manager
      const stateManager = createStateManager(sessionId);

      // Update the phase
      await stateManager.updatePhase(nextPhase);
    }

    // Create orchestrator instance
    const orchestrator = new DynamicWorkflowOrchestratorV2(sessionId);

    // Check if we need to transition to the next phase
    const transitioned = await orchestrator.checkGoalCompletionAndTransition();

    // Get the updated state to log
    const updatedState = await dynamicStateStore.getState(sessionId);
    if (updatedState) {
      logger.info(`State after phase transition check (transitioned: ${transitioned})`, {
        sessionId,
        currentPhase: updatedState.currentPhase,
        completedGoals: JSON.stringify(updatedState.completedGoals || []),
        activeGoals: JSON.stringify(updatedState.activeGoals || []),
        artifactsCount: Object.keys(updatedState.artifacts || {}).length,
        generatedArtifactsCount: (updatedState.generatedArtifacts || []).length
      });
    }
  } catch (error) {
    const err = error as Error;
    logger.error(`Error checking goal completion and phase transition`, {
      sessionId,
      error: err.message || String(error)
    });
  }
}

/**
 * Handle transitionPhase method
 */
async function handleTransitionPhase(params: any) {
  const { sessionId, phase } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!phase) {
    throw new Error('Missing required parameter: phase');
  }

  try {
    // Get state manager
    const stateManager = createStateManager(sessionId);

    // Update the phase directly
    const success = await stateManager.updatePhase(phase as DynamicWorkflowPhase);

    if (!success) {
      // Try using the orchestrator as a fallback
      const orchestrator = new DynamicWorkflowOrchestratorV2(sessionId);
      await orchestrator.transitionToPhase(phase as DynamicWorkflowPhase);
    }

    logger.info(`Transitioned to phase: ${phase}`, {
      sessionId,
      phase
    });

    return {
      success: true,
      phase
    };
  } catch (error) {
    const err = error as Error;
    logger.error(`Error transitioning to phase: ${phase}`, {
      sessionId,
      phase,
      error: err.message || String(error)
    });

    throw error;
  }
}

/**
 * Handle processUserMessage method
 */
async function handleProcessUserMessage(params: any) {
  const { sessionId, messageId } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!messageId) {
    throw new Error('Missing required parameter: messageId');
  }

  // Create orchestrator instance
  const orchestrator = new DynamicWorkflowOrchestratorV2(sessionId);

  // Process the user message
  const success = await orchestrator.processUserMessage(messageId);

  return { success };
}

/**
 * Handle addUserMessage method
 */
async function handleAddUserMessage(params: any) {
  const { sessionId, content } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!content) {
    throw new Error('Missing required parameter: content');
  }

  // Get state manager
  const stateManager = createStateManager(sessionId);

  // Create message
  const messageId = uuidv4();
  const conversationId = uuidv4();

  // Add message
  await stateManager.addMessage({
    id: messageId,
    timestamp: new Date().toISOString(),
    from: 'user',
    to: 'system',
    type: DynamicMessageType.USER_MESSAGE,
    content,
    conversationId
  });

  // Create orchestrator instance
  const orchestrator = new DynamicWorkflowOrchestratorV2(sessionId);

  // Process the user message
  await orchestrator.processUserMessage(messageId);

  return { messageId, conversationId };
}

/**
 * Handle completeGoal method (for testing purposes)
 */
async function handleCompleteGoal(params: any) {
  const { sessionId, goalType, artifactId, goalId: providedGoalId } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!goalType) {
    throw new Error('Missing required parameter: goalType');
  }

  try {
    // Get state manager
    const stateManager = createStateManager(sessionId);

    // Get current state
    const state = await stateManager.getState();
    if (!state) {
      throw new Error(`Session ${sessionId} not found`);
    }

    let goalId = providedGoalId;

    // If no goalId was provided, find it by type
    if (!goalId) {
      // Find the goal with the specified type
      const goalsOfType = Object.entries(state.goals).filter(([_, g]) => g.type === goalType);

      if (goalsOfType.length === 0) {
        throw new Error(`No goals of type ${goalType} found`);
      }

      // Get the first goal of this type
      [goalId] = goalsOfType[0];
    }

    // Verify the goal exists
    if (!state.goals[goalId]) {
      throw new Error(`Goal with ID ${goalId} not found`);
    }

    // Create orchestrator instance
    const orchestrator = new DynamicWorkflowOrchestratorV2(sessionId);

    // Complete the goal using the orchestrator
    const success = await orchestrator.completeGoalByType(goalType, artifactId);

    if (!success) {
      logger.error(`Failed to complete goal`, {
        sessionId,
        goalId,
        goalType,
        artifactId
      });
      throw new Error(`Failed to complete goal ${goalType}`);
    }

    // Verify the goal was completed
    const stateAfterCompletion = await dynamicStateStore.getState(sessionId);
    const goalCompleted = stateAfterCompletion?.completedGoals?.includes(goalType) || false;

    if (!goalCompleted) {
      logger.error(`Goal not marked as completed in state`, {
        sessionId,
        goalId,
        goalType,
        completedGoals: JSON.stringify(stateAfterCompletion?.completedGoals || [])
      });

      // Force update the completedGoals array
      await dynamicStateStore.updateState(sessionId, (currentState: DynamicCollaborationState | null) => {
        if (!currentState) return currentState;

        const completedGoals = [...(currentState.completedGoals || [])];
        if (!completedGoals.includes(goalType)) {
          completedGoals.push(goalType);
          logger.info(`Forced addition of ${goalType} to completedGoals`, {
            sessionId,
            goalType,
            completedGoals: JSON.stringify(completedGoals)
          });
        }

        return {
          ...currentState,
          completedGoals
        };
      });
    }

    // Check for phase transition
    await checkGoalCompletionAndPhaseTransition(sessionId);

    // Update the state directly to ensure completedGoals is updated
    logger.info(`Directly updating state for goal completion`, {
      sessionId,
      goalId,
      goalType,
      artifactId
    });

    await dynamicStateStore.updateState(sessionId, (currentState: DynamicCollaborationState | null) => {
      if (!currentState) {
        logger.error(`Current state is null when updating goal`, {
          sessionId,
          goalId,
          goalType
        });
        return currentState;
      }

      // Get the goal
      const goal = currentState.goals[goalId];
      if (!goal) {
        logger.error(`Goal not found in state`, {
          sessionId,
          goalId,
          goalType,
          availableGoals: Object.keys(currentState.goals || {})
        });
        return currentState;
      }

      // Log the current state
      logger.info(`Current state before update`, {
        sessionId,
        currentPhase: currentState.currentPhase,
        completedGoals: currentState.completedGoals,
        activeGoals: currentState.activeGoals,
        artifactsCount: Object.keys(currentState.artifacts || {}).length,
        generatedArtifactsCount: (currentState.generatedArtifacts || []).length
      });

      // Update the goal status
      const updatedGoals = { ...currentState.goals };
      updatedGoals[goalId] = {
        ...goal,
        status: GoalStatus.COMPLETED,
        progress: 100,
        completedAt: new Date().toISOString(),
        artifactId
      };

      // Add to completed goals if not already there
      const completedGoals = [...(currentState.completedGoals || [])];
      if (!completedGoals.includes(goalType)) {
        completedGoals.push(goalType);
        logger.info(`Added ${goalType} to completedGoals`, {
          sessionId,
          goalType,
          completedGoals
        });
      } else {
        logger.info(`${goalType} already in completedGoals`, {
          sessionId,
          goalType,
          completedGoals
        });
      }

      // Remove from active goals
      const activeGoals = (currentState.activeGoals || []).filter(id => id !== goalId);

      // Create updated state
      const updatedState = {
        ...currentState,
        goals: updatedGoals,
        activeGoals,
        completedGoals
      };

      logger.info(`Updated state after goal completion`, {
        sessionId,
        goalId,
        goalType,
        completedGoals,
        activeGoals
      });

      return updatedState;
    });

    logger.info(`Completed goal ${goalId} of type ${goalType}`, {
      sessionId,
      goalId,
      goalType,
      artifactId
    });

    // Get the updated state to return
    const updatedState = await dynamicStateStore.getState(sessionId);

    return {
      success: true,
      goalId,
      message: `Goal ${goalType} completed successfully`,
      completedGoals: updatedState?.completedGoals || [],
      artifacts: Object.keys(updatedState?.artifacts || {}).length,
      state: {
        currentPhase: updatedState?.currentPhase,
        completedGoals: updatedState?.completedGoals,
        generatedArtifacts: updatedState?.generatedArtifacts
      }
    };
  } catch (error) {
    const err = error as Error;
    logger.error(`Error completing goal`, {
      sessionId,
      goalType,
      error: err.message || String(error)
    });

    throw error;
  }
}

/**
 * Handle handleFeedback method
 */
async function handleFeedback(params: any) {
  const { sessionId, messageId } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!messageId) {
    throw new Error('Missing required parameter: messageId');
  }

  // Create orchestrator instance
  const orchestrator = new DynamicWorkflowOrchestratorV2(sessionId);

  // Handle feedback and improve artifact
  const success = await orchestrator.handleFeedbackAndImproveArtifact(messageId);

  return { success };
}

/**
 * Handle addArtifact method
 */
async function handleAddArtifact(params: any) {
  const { sessionId, artifact } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!artifact) {
    throw new Error('Missing required parameter: artifact');
  }

  try {
    // Get state manager
    const stateManager = createStateManager(sessionId);

    // Ensure the artifact has an ID
    if (!artifact.id) {
      artifact.id = uuidv4();
    }

    // Update the state directly to ensure artifacts are properly added
    logger.info(`Directly updating state to add artifact`, {
      sessionId,
      artifactId: artifact.id,
      artifactType: artifact.type
    });

    await dynamicStateStore.updateState(sessionId, (currentState: DynamicCollaborationState | null) => {
      if (!currentState) {
        logger.error(`Current state is null when adding artifact`, {
          sessionId,
          artifactId: artifact.id
        });
        return currentState;
      }

      // Log the current state
      logger.info(`Current state before adding artifact`, {
        sessionId,
        currentPhase: currentState.currentPhase,
        artifactsCount: Object.keys(currentState.artifacts || {}).length,
        generatedArtifactsCount: (currentState.generatedArtifacts || []).length
      });

      // Add the artifact to the artifacts object
      const artifacts = {
        ...currentState.artifacts,
        [artifact.id]: artifact
      };

      // Add to generatedArtifacts if not already there
      const generatedArtifacts = [...(currentState.generatedArtifacts || [])];
      if (!generatedArtifacts.includes(artifact.id)) {
        generatedArtifacts.push(artifact.id);
        logger.info(`Added ${artifact.id} to generatedArtifacts`, {
          sessionId,
          artifactId: artifact.id,
          generatedArtifactsCount: generatedArtifacts.length
        });
      } else {
        logger.info(`${artifact.id} already in generatedArtifacts`, {
          sessionId,
          artifactId: artifact.id
        });
      }

      // Create updated state
      const updatedState = {
        ...currentState,
        artifacts,
        generatedArtifacts
      };

      logger.info(`Updated state after adding artifact`, {
        sessionId,
        artifactId: artifact.id,
        artifactsCount: Object.keys(artifacts).length,
        generatedArtifactsCount: generatedArtifacts.length
      });

      return updatedState;
    });

    // Verify the artifact was added
    const updatedState = await dynamicStateStore.getState(sessionId);
    if (!updatedState?.artifacts?.[artifact.id]) {
      logger.error(`Failed to add artifact to state`, {
        sessionId,
        artifactId: artifact.id
      });
      throw new Error(`Failed to add artifact ${artifact.id} to state`);
    }

    logger.info(`Verified artifact was added to state`, {
      sessionId,
      artifactId: artifact.id,
      artifactsCount: Object.keys(updatedState?.artifacts || {}).length,
      generatedArtifactsCount: updatedState?.generatedArtifacts?.length || 0
    });

    // Also try using the state manager method as a backup
    try {
      await stateManager.addArtifact(artifact);
    } catch (error) {
      logger.warn(`Error using stateManager.addArtifact, but direct state update succeeded`, {
        sessionId,
        artifactId: artifact.id,
        error: error instanceof Error ? error.message : String(error)
      });
    }

    const artifactId = artifact.id;

    logger.info(`Added artifact ${artifactId} to session ${sessionId}`, {
      sessionId,
      artifactId,
      artifactType: artifact.type
    });

    return {
      success: true,
      artifactId
    };
  } catch (error) {
    const err = error as Error;
    logger.error(`Error adding artifact`, {
      sessionId,
      artifactId: artifact.id,
      error: err.message || String(error)
    });

    throw error;
  }
}

/**
 * Handle checkPhaseTransition method
 */
async function handleCheckPhaseTransition(params: any) {
  const { sessionId, forcePhase } = params;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }

  try {
    // Get the current state
    const state = await dynamicStateStore.getState(sessionId);
    if (!state) {
      throw new Error(`Session ${sessionId} not found`);
    }

    // If forcePhase is provided, transition to that phase
    if (forcePhase) {
      const stateManager = createStateManager(sessionId);
      await stateManager.updatePhase(forcePhase);

      logger.info(`Forced phase transition to ${forcePhase}`, {
        sessionId,
        previousPhase: state.currentPhase,
        newPhase: forcePhase
      });

      return {
        success: true,
        previousPhase: state.currentPhase,
        newPhase: forcePhase
      };
    }

    // Otherwise, check for automatic phase transitions
    const { currentPhase, completedGoals } = state;

    logger.info(`Checking for phase transition`, {
      sessionId,
      currentPhase,
      completedGoals: JSON.stringify(completedGoals),
      artifactsCount: Object.keys(state.artifacts || {}).length,
      generatedArtifactsCount: (state.generatedArtifacts || []).length
    });

    // Determine if we should transition based on completed goals
    let shouldTransition = false;
    let nextPhase = currentPhase;

    if (currentPhase === DynamicWorkflowPhase.RESEARCH) {
      // If both market_research and keyword_analysis are completed, transition to CREATION
      const hasMarketResearch = completedGoals.includes('market_research');
      const hasKeywordAnalysis = completedGoals.includes('keyword_analysis');

      logger.info(`Research phase check: market_research=${hasMarketResearch}, keyword_analysis=${hasKeywordAnalysis}`, {
        sessionId,
        completedGoals: JSON.stringify(completedGoals)
      });

      if (hasMarketResearch && hasKeywordAnalysis) {
        shouldTransition = true;
        nextPhase = DynamicWorkflowPhase.CREATION;
      }
    } else if (currentPhase === DynamicWorkflowPhase.CREATION) {
      // If content_strategy and content_creation are completed, transition to REVIEW
      const hasContentStrategy = completedGoals.includes('content_strategy');
      const hasContentCreation = completedGoals.includes('content_creation');

      logger.info(`Creation phase check: content_strategy=${hasContentStrategy}, content_creation=${hasContentCreation}`, {
        sessionId,
        completedGoals: JSON.stringify(completedGoals)
      });

      if (hasContentStrategy && hasContentCreation) {
        shouldTransition = true;
        nextPhase = DynamicWorkflowPhase.REVIEW;
      }
    } else if (currentPhase === DynamicWorkflowPhase.REVIEW) {
      // If seo_optimization and quality_assessment are completed, transition to FINALIZATION
      const hasSeoOptimization = completedGoals.includes('seo_optimization');
      const hasQualityAssessment = completedGoals.includes('quality_assessment');

      logger.info(`Review phase check: seo_optimization=${hasSeoOptimization}, quality_assessment=${hasQualityAssessment}`, {
        sessionId,
        completedGoals: JSON.stringify(completedGoals)
      });

      if (hasSeoOptimization && hasQualityAssessment) {
        shouldTransition = true;
        nextPhase = DynamicWorkflowPhase.FINALIZATION;
      }
    }

    logger.info(`Phase transition decision: shouldTransition=${shouldTransition}, nextPhase=${nextPhase}`, {
      sessionId,
      currentPhase,
      nextPhase,
      shouldTransition
    });

    if (shouldTransition && nextPhase !== currentPhase) {
      const stateManager = createStateManager(sessionId);
      await stateManager.updatePhase(nextPhase);

      logger.info(`Automatic phase transition from ${currentPhase} to ${nextPhase}`, {
        sessionId,
        previousPhase: currentPhase,
        newPhase: nextPhase,
        completedGoals
      });

      return {
        success: true,
        previousPhase: currentPhase,
        newPhase: nextPhase,
        automatic: true
      };
    }

    // If no transition occurred, just return success
    return {
      success: true,
      phase: currentPhase,
      noTransition: true
    };
  } catch (error) {
    const err = error as Error;
    logger.error(`Error checking phase transition`, {
      sessionId,
      error: err.message || String(error)
    });

    throw error;
  }
}