/**
 * Agent Selection API
 * 
 * Provides intelligent agent selection recommendations based on context
 */

import { NextRequest, NextResponse } from 'next/server';
import { DynamicAgentConsultationService } from '../../../../core/workflow/dynamic-agent-consultation-service';
import { WorkflowAgentBridge } from '../../../../core/workflow/workflow-agent-bridge';
import { SeoKeywordAgent } from '../../../../core/agents/seo-keyword-agent';
import { MarketResearchAgent } from '../../../../core/agents/market-research-agent';
import { ContentStrategyAgent } from '../../../../core/agents/content-strategy-agent';
import { AgentConsultationConfig } from '../../../../core/workflow/types';

// Shared service instance
let consultationService: DynamicAgentConsultationService | null = null;

function getConsultationService(): DynamicAgentConsultationService {
  if (!consultationService) {
    const seoAgent = new SeoKeywordAgent();
    const marketAgent = new MarketResearchAgent();
    const strategyAgent = new ContentStrategyAgent();

    const agentBridge = new WorkflowAgentBridge({
      'seo-keyword': seoAgent,
      'market-research': marketAgent,
      'content-strategy': strategyAgent
    });

    consultationService = new DynamicAgentConsultationService(agentBridge);
  }
  
  return consultationService;
}

/**
 * POST /api/agents/selection
 * Get agent selection recommendations based on context
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { context, consultationConfig } = body;

    if (!context) {
      return NextResponse.json({
        success: false,
        error: 'Context is required for agent selection'
      }, { status: 400 });
    }

    const service = getConsultationService();

    // Get recommended agents based on context
    const recommendedAgents = await service.selectAgentsForContext(context);

    // Check if consultation should be triggered
    let shouldTrigger = false;
    let triggerReasons: string[] = [];

    if (consultationConfig) {
      shouldTrigger = await service.shouldTriggerConsultation(consultationConfig, context);
      
      // Analyze which triggers would activate
      for (const trigger of consultationConfig.triggers) {
        switch (trigger.type) {
          case 'always':
            triggerReasons.push('Always trigger is enabled');
            break;
          case 'quality_threshold':
            if (trigger.condition?.threshold && context.qualityScore !== undefined) {
              if (context.qualityScore < trigger.condition.threshold) {
                triggerReasons.push(`Quality score (${context.qualityScore}) below threshold (${trigger.condition.threshold})`);
              }
            }
            break;
          case 'feedback_keywords':
            if (trigger.condition?.keywords && context.feedback) {
              const keywords = trigger.condition.keywords;
              const feedbackLower = context.feedback.toLowerCase();
              const foundKeywords = keywords.filter(keyword => 
                feedbackLower.includes(keyword.toLowerCase())
              );
              if (foundKeywords.length > 0) {
                triggerReasons.push(`Feedback contains keywords: ${foundKeywords.join(', ')}`);
              }
            }
            break;
          case 'content_complexity':
            if (trigger.condition?.threshold) {
              const complexity = context.complexity || calculateComplexity(context);
              if (complexity > trigger.condition.threshold) {
                triggerReasons.push(`Content complexity (${complexity.toFixed(2)}) above threshold (${trigger.condition.threshold})`);
              }
            }
            break;
        }
      }
    }

    // Get agent capabilities and status
    const agentBridge = (service as any).agentBridge;
    const agentDetails = await Promise.all(
      recommendedAgents.map(async (agentId) => {
        const agent = agentBridge.getAgent(agentId);
        const isAvailable = await agentBridge.isAgentAvailable(agentId);
        
        return {
          agentId,
          capabilities: agent?.getCapabilities() || [],
          isAvailable,
          status: agent?.getStatus ? agent.getStatus() : null
        };
      })
    );

    // Generate selection reasoning
    const selectionReasoning = generateSelectionReasoning(context, recommendedAgents);

    return NextResponse.json({
      success: true,
      data: {
        recommendedAgents,
        agentDetails,
        shouldTriggerConsultation: shouldTrigger,
        triggerReasons,
        selectionReasoning,
        contextAnalysis: {
          topic: context.topic,
          contentType: context.contentType,
          targetAudience: context.targetAudience,
          complexity: context.complexity || calculateComplexity(context),
          hasFeedback: !!context.feedback,
          qualityScore: context.qualityScore
        },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Agent selection API error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Agent selection failed'
    }, { status: 500 });
  }
}

/**
 * GET /api/agents/selection/capabilities
 * Get all available agents and their capabilities
 */
export async function GET(request: NextRequest) {
  try {
    const service = getConsultationService();
    const agentBridge = (service as any).agentBridge;
    
    const registeredAgents = agentBridge.getRegisteredAgents();
    const agentCapabilities = registeredAgents.map(agentId => {
      const agent = agentBridge.getAgent(agentId);
      return {
        agentId,
        capabilities: agent?.getCapabilities() || [],
        configuration: agent?.getConfiguration() || null
      };
    });

    return NextResponse.json({
      success: true,
      data: {
        agents: agentCapabilities,
        totalAgents: registeredAgents.length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Agent capabilities API error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get agent capabilities'
    }, { status: 500 });
  }
}

// Helper functions
function calculateComplexity(context: any): number {
  let complexity = 0;

  // Content length factor
  if (context.content) {
    const contentLength = context.content.length;
    if (contentLength > 2000) complexity += 0.3;
    else if (contentLength > 1000) complexity += 0.2;
    else if (contentLength > 500) complexity += 0.1;
  }

  // Topic complexity factor
  if (context.topic) {
    const technicalKeywords = ['algorithm', 'api', 'framework', 'architecture', 'implementation'];
    const hasTechnicalTerms = technicalKeywords.some(keyword => 
      context.topic.toLowerCase().includes(keyword)
    );
    if (hasTechnicalTerms) complexity += 0.2;
  }

  // Target audience factor
  if (context.targetAudience) {
    const expertAudiences = ['technical experts', 'developers', 'engineers', 'professionals'];
    const isExpertAudience = expertAudiences.some(audience => 
      context.targetAudience.toLowerCase().includes(audience)
    );
    if (isExpertAudience) complexity += 0.2;
  }

  // Multiple goals factor
  if (context.goals && Array.isArray(context.goals) && context.goals.length > 3) {
    complexity += 0.1;
  }

  return Math.min(complexity, 1.0);
}

function generateSelectionReasoning(context: any, selectedAgents: string[]): string[] {
  const reasoning: string[] = [];

  // Content type reasoning
  if (context.contentType === 'blog-post' || context.contentType === 'article') {
    reasoning.push('Blog post content type requires SEO optimization and strategic planning');
  }

  // Topic-based reasoning
  if (context.topic) {
    const topicLower = context.topic.toLowerCase();
    
    if (topicLower.includes('market') || topicLower.includes('business')) {
      reasoning.push('Topic suggests need for market research insights');
    }
    
    if (topicLower.includes('seo') || topicLower.includes('keyword')) {
      reasoning.push('Topic indicates SEO focus requiring keyword expertise');
    }
  }

  // Feedback-based reasoning
  if (context.feedback) {
    const feedbackLower = context.feedback.toLowerCase();
    
    if (feedbackLower.includes('seo') || feedbackLower.includes('keyword')) {
      reasoning.push('Feedback mentions SEO concerns requiring keyword agent consultation');
    }
    
    if (feedbackLower.includes('strategy') || feedbackLower.includes('structure')) {
      reasoning.push('Feedback indicates need for content strategy improvements');
    }
  }

  // Complexity reasoning
  const complexity = context.complexity || calculateComplexity(context);
  if (complexity > 0.6) {
    reasoning.push('High content complexity requires strategic content planning');
  }

  // Default reasoning if no specific triggers
  if (reasoning.length === 0) {
    reasoning.push('Standard content creation benefits from multi-agent consultation');
  }

  return reasoning;
}
