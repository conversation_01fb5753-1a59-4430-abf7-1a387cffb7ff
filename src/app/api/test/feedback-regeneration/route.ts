/**
 * Test API for Feedback & Regeneration System
 * Demonstrates the complete feedback processing and artifact regeneration flow
 */

import { NextRequest, NextResponse } from 'next/server';
import { getWorkflowEngine } from '../../../../core/workflow/singleton';
import { ArtifactStatus, ArtifactType } from '../../../../core/workflow/types';

/**
 * POST /api/test/feedback-regeneration
 * Test the complete feedback and regeneration flow
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Starting feedback & regeneration test...');

    const workflowEngine = getWorkflowEngine();

    // Step 1: Create a test workflow execution
    const executionId = await workflowEngine.createExecution('test-workflow', {
      topic: 'AI and Machine Learning',
      targetAudience: 'developers',
      contentType: 'blog_post'
    });

    console.log(`✅ Created test execution: ${executionId}`);

    // Step 2: Create a test artifact that needs approval
    const artifactId = await workflowEngine.createArtifact(executionId, 'test-step', {
      stepId: 'test-step',
      executionId,
      type: ArtifactType.BLOG_POST,
      title: 'Test Blog Post for Feedback',
      content: {
        title: 'Introduction to Machine Learning',
        content: 'Machine learning is a subset of artificial intelligence. It is very important.',
        keywords: ['machine learning', 'AI'],
        meta_description: 'Learn about machine learning basics'
      },
      status: ArtifactStatus.PENDING_APPROVAL,
      version: 1,
      createdBy: 'test-system'
    });

    console.log(`✅ Created test artifact: ${artifactId}`);

    // Step 3: Simulate rejection with feedback
    const feedback = 'The content is too basic and lacks depth. Please add more technical details, examples, and explain different types of machine learning algorithms. The writing style should be more engaging and professional.';

    console.log(`📝 Simulating rejection with feedback: ${feedback}`);

    // Submit rejection with feedback
    await workflowEngine.submitApproval(artifactId, {
      approved: false,
      approver: 'test-reviewer',
      feedback,
      reason: feedback,
      timestamp: new Date().toISOString()
    });

    console.log(`✅ Submitted rejection with feedback`);

    // Step 4: Wait a moment for regeneration to complete
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Step 5: Check the results
    const execution = await workflowEngine.getExecution(executionId);
    const stepResult = execution?.stepResults['test-step'];

    console.log(`📊 Test Results:`, {
      executionId,
      originalArtifactId: artifactId,
      stepStatus: stepResult?.status,
      newArtifactId: stepResult?.artifactId,
      regenerated: stepResult?.artifactId !== artifactId
    });

    return NextResponse.json({
      success: true,
      message: 'Feedback & regeneration test completed',
      data: {
        executionId,
        originalArtifactId: artifactId,
        feedback,
        stepResult: {
          status: stepResult?.status,
          newArtifactId: stepResult?.artifactId,
          regenerated: stepResult?.artifactId !== artifactId,
          outputs: stepResult?.outputs
        }
      }
    });

  } catch (error) {
    console.error('❌ Feedback & regeneration test failed:', error);

    return NextResponse.json(
      {
        error: 'Test failed',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/test/feedback-regeneration
 * Get information about the feedback & regeneration system
 */
export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Feedback & Regeneration System Test API',
    description: 'This API tests the complete feedback processing and artifact regeneration flow',
    endpoints: {
      'POST /api/test/feedback-regeneration': 'Run a complete test of the feedback & regeneration system',
      'GET /api/test/feedback-regeneration': 'Get information about this test API'
    },
    testFlow: [
      '1. Create a test workflow execution',
      '2. Create a test artifact with basic content',
      '3. Submit rejection with detailed feedback',
      '4. System processes feedback and regenerates improved content',
      '5. New artifact is created and submitted for approval',
      '6. Return test results showing the regeneration process'
    ]
  });
}
