/**
 * Review API Endpoints
 * Handles review listing, filtering, and management
 */

import { NextRequest, NextResponse } from 'next/server';
import { ResponseFormatter } from '../../../core/api/response-formatter';
import { ErrorType } from '../../../core/utils/error-handler';
import { getReviewSystem } from '../../../core/workflow/singleton';

// GET /api/review - List reviews with filtering
export async function GET(request: NextRequest) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { searchParams } = new URL(request.url);
    
    // Extract query parameters
    const status = searchParams.get('status');
    const reviewer = searchParams.get('reviewer');
    const priority = searchParams.get('priority');
    const contentType = searchParams.get('contentType');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return ResponseFormatter.validationError(
        'Invalid pagination parameters',
        [
          { field: 'page', message: 'Page must be >= 1', code: 'INVALID_VALUE' },
          { field: 'limit', message: 'Limit must be between 1 and 100', code: 'INVALID_RANGE' }
        ],
        requestId
      );
    }

    const reviewSystem = getReviewSystem();
    
    // Get all reviews (in a real implementation, this would be from database)
    // For now, we'll simulate with the review system
    const allReviews = await reviewSystem.getAllReviews();
    
    // Apply filters
    let filteredReviews = allReviews;
    
    if (status) {
      filteredReviews = filteredReviews.filter(review => review.status === status);
    }
    
    if (reviewer) {
      filteredReviews = filteredReviews.filter(review => 
        review.assignedTo === reviewer || 
        (review.reviewerAssignments && review.reviewerAssignments.includes(reviewer))
      );
    }
    
    if (priority) {
      filteredReviews = filteredReviews.filter(review => review.priority === priority);
    }
    
    if (contentType) {
      filteredReviews = filteredReviews.filter(review => 
        review.metadata?.contentType === contentType
      );
    }

    // Apply sorting
    filteredReviews.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'createdAt':
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
          break;
        case 'deadline':
          aValue = a.deadline ? new Date(a.deadline) : new Date('9999-12-31');
          bValue = b.deadline ? new Date(b.deadline) : new Date('9999-12-31');
          break;
        case 'priority':
          const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
          aValue = priorityOrder[a.priority as keyof typeof priorityOrder] || 0;
          bValue = priorityOrder[b.priority as keyof typeof priorityOrder] || 0;
          break;
        default:
          aValue = a[sortBy as keyof typeof a];
          bValue = b[sortBy as keyof typeof b];
      }
      
      if (sortOrder === 'desc') {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      } else {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      }
    });

    // Apply pagination
    const total = filteredReviews.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedReviews = filteredReviews.slice(startIndex, endIndex);

    // Add computed fields for UI
    const enrichedReviews = paginatedReviews.map(review => ({
      ...review,
      timeRemaining: review.deadline ? 
        Math.max(0, new Date(review.deadline).getTime() - Date.now()) / (1000 * 60) : null,
      isOverdue: review.deadline ? 
        new Date(review.deadline).getTime() < Date.now() && review.status !== 'completed' : false,
      assignedReviewerNames: review.reviewerAssignments?.map(id => 
        // In real implementation, would fetch reviewer names
        `Reviewer ${id}`
      ) || []
    }));

    return ResponseFormatter.paginated(
      enrichedReviews,
      page,
      limit,
      total,
      requestId
    );

  } catch (error) {
    console.error('Review listing error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// POST /api/review - Create a new review
export async function POST(request: NextRequest) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const body = await request.json();
    const {
      contentId,
      executionId,
      stepId,
      type = 'approval',
      instructions,
      reviewers = [],
      priority = 'medium',
      deadline,
      estimatedTime = 30,
      tags = [],
      metadata = {}
    } = body;

    // Validate required fields
    const validationErrors = [];
    if (!contentId) {
      validationErrors.push({ field: 'contentId', message: 'Content ID is required', code: 'REQUIRED' });
    }
    if (!executionId) {
      validationErrors.push({ field: 'executionId', message: 'Execution ID is required', code: 'REQUIRED' });
    }
    if (!stepId) {
      validationErrors.push({ field: 'stepId', message: 'Step ID is required', code: 'REQUIRED' });
    }

    if (validationErrors.length > 0) {
      return ResponseFormatter.validationError(
        'Missing required fields',
        validationErrors,
        requestId
      );
    }

    const reviewSystem = getReviewSystem();
    
    // Create review options
    const reviewOptions = {
      contentId,
      executionId,
      stepId,
      type,
      instructions: instructions || 'Please review this content',
      reviewers,
      priority,
      deadline,
      metadata: {
        ...metadata,
        estimatedTime,
        tags,
        createdVia: 'api'
      }
    };

    // Create the review
    const reviewLink = await reviewSystem.createReview(
      contentId, // content parameter
      reviewOptions
    );

    // Get the created review details
    const reviewData = await reviewSystem.getReview(reviewLink.reviewId);

    return ResponseFormatter.success({
      review: reviewData,
      reviewLink,
      message: 'Review created successfully'
    }, requestId);

  } catch (error) {
    console.error('Review creation error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// PUT /api/review - Bulk update reviews
export async function PUT(request: NextRequest) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const body = await request.json();
    const { reviewIds, updates, operation } = body;

    if (!reviewIds || !Array.isArray(reviewIds) || reviewIds.length === 0) {
      return ResponseFormatter.validationError(
        'Review IDs are required',
        [{ field: 'reviewIds', message: 'Must be a non-empty array', code: 'REQUIRED' }],
        requestId
      );
    }

    const reviewSystem = getReviewSystem();
    const results = [];
    const errors = [];

    for (const reviewId of reviewIds) {
      try {
        switch (operation) {
          case 'updatePriority':
            if (updates.priority) {
              await reviewSystem.updateReviewPriority(reviewId, updates.priority);
              results.push({ reviewId, status: 'updated', field: 'priority' });
            }
            break;
          case 'assignReviewer':
            if (updates.reviewerId) {
              await reviewSystem.assignReviewer(reviewId, updates.reviewerId);
              results.push({ reviewId, status: 'updated', field: 'reviewer' });
            }
            break;
          case 'extendDeadline':
            if (updates.deadline) {
              await reviewSystem.updateDeadline(reviewId, updates.deadline);
              results.push({ reviewId, status: 'updated', field: 'deadline' });
            }
            break;
          default:
            errors.push({ reviewId, error: 'Unknown operation' });
        }
      } catch (error) {
        errors.push({ 
          reviewId, 
          error: error instanceof Error ? error.message : String(error) 
        });
      }
    }

    return ResponseFormatter.success({
      results,
      errors,
      summary: {
        total: reviewIds.length,
        successful: results.length,
        failed: errors.length
      }
    }, requestId);

  } catch (error) {
    console.error('Bulk review update error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// DELETE /api/review - Bulk delete reviews
export async function DELETE(request: NextRequest) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { searchParams } = new URL(request.url);
    const reviewIds = searchParams.get('ids')?.split(',') || [];

    if (reviewIds.length === 0) {
      return ResponseFormatter.validationError(
        'Review IDs are required',
        [{ field: 'ids', message: 'Must provide review IDs as comma-separated values', code: 'REQUIRED' }],
        requestId
      );
    }

    const reviewSystem = getReviewSystem();
    const results = [];
    const errors = [];

    for (const reviewId of reviewIds) {
      try {
        await reviewSystem.deleteReview(reviewId);
        results.push({ reviewId, status: 'deleted' });
      } catch (error) {
        errors.push({ 
          reviewId, 
          error: error instanceof Error ? error.message : String(error) 
        });
      }
    }

    return ResponseFormatter.success({
      results,
      errors,
      summary: {
        total: reviewIds.length,
        successful: results.length,
        failed: errors.length
      }
    }, requestId);

  } catch (error) {
    console.error('Bulk review deletion error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}
