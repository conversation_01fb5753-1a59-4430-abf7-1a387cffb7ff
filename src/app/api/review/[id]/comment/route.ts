/**
 * Review Comment API Endpoint
 * Handles comments and feedback on reviews
 */

import { NextRequest, NextResponse } from 'next/server';
import { ResponseFormatter } from '../../../../../core/api/response-formatter';
import { ErrorType } from '../../../../../core/utils/error-handler';
import { getReviewSystem } from '../../../../../core/workflow/singleton';
import { v4 as uuidv4 } from 'uuid';

// POST /api/review/[id]/comment - Add comment to review
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const reviewId = id;
    const body = await request.json();
    
    const { 
      text, 
      author, 
      position,
      type = 'general',
      isPrivate = false,
      parentCommentId 
    } = body;

    // Validate required fields
    const validationErrors = [];
    if (!text || text.trim().length === 0) {
      validationErrors.push({ field: 'text', message: 'Comment text is required', code: 'REQUIRED' });
    }
    if (!author) {
      validationErrors.push({ field: 'author', message: 'Author is required', code: 'REQUIRED' });
    }

    if (validationErrors.length > 0) {
      return ResponseFormatter.validationError(
        'Missing required fields',
        validationErrors,
        requestId
      );
    }

    const reviewSystem = getReviewSystem();
    
    // Check if review exists
    const review = await reviewSystem.getReview(reviewId);
    if (!review) {
      return ResponseFormatter.notFound('Review', reviewId, requestId);
    }

    // Create comment
    const comment = {
      id: uuidv4(),
      reviewId,
      text: text.trim(),
      author,
      position,
      type,
      isPrivate,
      parentCommentId,
      timestamp: new Date().toISOString(),
      isResolved: false,
      reactions: [],
      metadata: {}
    };

    // Add comment to review
    await reviewSystem.addComment(reviewId, comment);

    return ResponseFormatter.success({
      comment,
      message: 'Comment added successfully'
    }, requestId);

  } catch (error) {
    console.error('Comment creation error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// GET /api/review/[id]/comment - Get all comments for a review
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const reviewId = id;
    const { searchParams } = new URL(request.url);
    
    const includePrivate = searchParams.get('includePrivate') === 'true';
    const type = searchParams.get('type');
    const author = searchParams.get('author');
    const resolved = searchParams.get('resolved');

    const reviewSystem = getReviewSystem();
    
    // Get all comments for the review
    let comments = await reviewSystem.getComments(reviewId);

    // Apply filters
    if (!includePrivate) {
      comments = comments.filter(comment => !comment.isPrivate);
    }

    if (type) {
      comments = comments.filter(comment => comment.type === type);
    }

    if (author) {
      comments = comments.filter(comment => comment.author === author);
    }

    if (resolved !== null) {
      const isResolved = resolved === 'true';
      comments = comments.filter(comment => comment.isResolved === isResolved);
    }

    // Sort by timestamp (newest first)
    comments.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Group comments by thread (parent-child relationships)
    const commentThreads = this.organizeCommentThreads(comments);

    return ResponseFormatter.success({
      reviewId,
      comments,
      commentThreads,
      summary: {
        total: comments.length,
        unresolved: comments.filter(c => !c.isResolved).length,
        byType: this.groupCommentsByType(comments),
        byAuthor: this.groupCommentsByAuthor(comments)
      }
    }, requestId);

  } catch (error) {
    console.error('Comment fetch error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// PUT /api/review/[id]/comment - Update comment (resolve, edit, etc.)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const reviewId = id;
    const body = await request.json();
    
    const { 
      commentId, 
      text, 
      isResolved, 
      reactions,
      metadata 
    } = body;

    if (!commentId) {
      return ResponseFormatter.validationError(
        'Comment ID is required',
        [{ field: 'commentId', message: 'Comment ID is required', code: 'REQUIRED' }],
        requestId
      );
    }

    const reviewSystem = getReviewSystem();
    
    // Update comment
    const updates: any = {};
    if (text !== undefined) updates.text = text;
    if (isResolved !== undefined) updates.isResolved = isResolved;
    if (reactions !== undefined) updates.reactions = reactions;
    if (metadata !== undefined) updates.metadata = metadata;

    await reviewSystem.updateComment(reviewId, commentId, updates);

    // Get updated comment
    const updatedComment = await reviewSystem.getComment(reviewId, commentId);

    return ResponseFormatter.success({
      comment: updatedComment,
      message: 'Comment updated successfully'
    }, requestId);

  } catch (error) {
    console.error('Comment update error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// DELETE /api/review/[id]/comment - Delete comment
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const reviewId = id;
    const { searchParams } = new URL(request.url);
    const commentId = searchParams.get('commentId');

    if (!commentId) {
      return ResponseFormatter.validationError(
        'Comment ID is required',
        [{ field: 'commentId', message: 'Comment ID is required in query params', code: 'REQUIRED' }],
        requestId
      );
    }

    const reviewSystem = getReviewSystem();
    
    // Delete comment
    await reviewSystem.deleteComment(reviewId, commentId);

    return ResponseFormatter.success({
      message: 'Comment deleted successfully',
      commentId
    }, requestId);

  } catch (error) {
    console.error('Comment deletion error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// Helper methods (would be moved to a utility class in real implementation)
function organizeCommentThreads(comments: any[]): any[] {
  const commentMap = new Map();
  const threads: any[] = [];

  // First pass: create map of all comments
  comments.forEach(comment => {
    commentMap.set(comment.id, { ...comment, replies: [] });
  });

  // Second pass: organize into threads
  comments.forEach(comment => {
    const commentWithReplies = commentMap.get(comment.id);
    
    if (comment.parentCommentId) {
      // This is a reply
      const parent = commentMap.get(comment.parentCommentId);
      if (parent) {
        parent.replies.push(commentWithReplies);
      }
    } else {
      // This is a top-level comment
      threads.push(commentWithReplies);
    }
  });

  return threads;
}

function groupCommentsByType(comments: any[]): Record<string, number> {
  const groups: Record<string, number> = {};
  comments.forEach(comment => {
    groups[comment.type] = (groups[comment.type] || 0) + 1;
  });
  return groups;
}

function groupCommentsByAuthor(comments: any[]): Record<string, number> {
  const groups: Record<string, number> = {};
  comments.forEach(comment => {
    groups[comment.author] = (groups[comment.author] || 0) + 1;
  });
  return groups;
}
