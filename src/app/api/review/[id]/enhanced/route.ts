/**
 * Enhanced Review API
 * Multi-reviewer support with advanced features
 */

import { NextRequest, NextResponse } from 'next/server';
import { getStateStore } from '../../../../../core/workflow/singleton';
import { EnhancedReviewSystem } from '../../../../../core/review/enhanced-system';

// Initialize enhanced review system
let enhancedReviewSystem: EnhancedReviewSystem | null = null;

function getEnhancedReviewSystem(): EnhancedReviewSystem {
  if (!enhancedReviewSystem) {
    const stateStore = getStateStore();
    enhancedReviewSystem = new EnhancedReviewSystem(stateStore);
  }
  return enhancedReviewSystem;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const reviewId = id;

    const reviewSystem = getEnhancedReviewSystem();

    // Get basic review data
    const reviewData = await reviewSystem.getReview(reviewId);
    if (!reviewData) {
      return NextResponse.json(
        { error: 'Review not found' },
        { status: 404 }
      );
    }

    // Get enhanced data
    const assignments = await reviewSystem.getReviewAssignments(reviewId);
    const reviewers = await Promise.all(
      assignments.map(a => reviewSystem.getReviewer(a.reviewerId))
    );

    // Calculate time remaining
    let timeRemaining: number | undefined;
    if (reviewData.deadline) {
      const deadlineTime = new Date(reviewData.deadline).getTime();
      const currentTime = new Date().getTime();
      const remainingMs = deadlineTime - currentTime;
      timeRemaining = remainingMs > 0 ? Math.floor(remainingMs / (1000 * 60)) : 0;
    }

    // Mock escalation history (in real implementation, this would come from database)
    const escalationHistory = [
      // {
      //   id: 'escalation-1',
      //   timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      //   reason: 'Review deadline exceeded',
      //   escalatedBy: 'system',
      //   escalatedTo: ['senior-reviewer']
      // }
    ];

    const enhancedData = {
      ...reviewData,
      assignments: assignments.filter(a => a !== null),
      reviewers: reviewers.filter(r => r !== null),
      timeRemaining,
      escalationHistory
    };

    return NextResponse.json({
      success: true,
      data: enhancedData
    });

  } catch (error) {
    console.error('Enhanced review fetch error:', error);

    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: 'Review not found' },
        { status: 404 }
      );
    }

    if (error instanceof Error && error.message.includes('expired')) {
      return NextResponse.json(
        { error: 'Review has expired' },
        { status: 410 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to fetch enhanced review data',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const reviewId = id;
    const body = await request.json();
    const { action, ...actionData } = body;

    const reviewSystem = getEnhancedReviewSystem();

    switch (action) {
      case 'assign_reviewer':
        const { reviewerId, role, priority, deadline } = actionData;
        
        if (!reviewerId) {
          return NextResponse.json(
            { error: 'Reviewer ID is required' },
            { status: 400 }
          );
        }

        const assignment = {
          id: `assignment-${Date.now()}`,
          reviewId,
          reviewerId,
          assignedAt: new Date().toISOString(),
          assignedBy: 'current-user', // In production, get from auth
          role: role || 'secondary',
          status: 'pending' as const,
          deadline: deadline || new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString(),
          priority: priority || 'medium',
          estimatedTime: 30
        };

        await reviewSystem.assignReviewers(reviewId, [assignment]);

        return NextResponse.json({
          success: true,
          data: { assignmentId: assignment.id }
        });

      case 'send_reminder':
        const { targetReviewerId } = actionData;
        await reviewSystem.sendReminder(reviewId, targetReviewerId);

        return NextResponse.json({
          success: true,
          data: { message: 'Reminder sent successfully' }
        });

      case 'escalate':
        const { reason } = actionData;
        
        if (!reason) {
          return NextResponse.json(
            { error: 'Escalation reason is required' },
            { status: 400 }
          );
        }

        await reviewSystem.escalateReview(reviewId, reason);

        return NextResponse.json({
          success: true,
          data: { message: 'Review escalated successfully' }
        });

      case 'update_assignment':
        const { assignmentId, updates } = actionData;
        
        if (!assignmentId) {
          return NextResponse.json(
            { error: 'Assignment ID is required' },
            { status: 400 }
          );
        }

        await reviewSystem.updateAssignment(assignmentId, updates);

        return NextResponse.json({
          success: true,
          data: { message: 'Assignment updated successfully' }
        });

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Enhanced review action error:', error);

    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: 'Review or assignment not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to perform review action',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// Additional endpoint for reviewer management
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const reviewId = id;
    const body = await request.json();
    const { reviewers, priority, deadline, escalationRules } = body;

    const reviewSystem = getEnhancedReviewSystem();

    // Update review with new reviewers
    if (reviewers && Array.isArray(reviewers)) {
      const assignments = reviewers.map((reviewerId: string, index: number) => ({
        id: `assignment-${Date.now()}-${index}`,
        reviewId,
        reviewerId,
        assignedAt: new Date().toISOString(),
        assignedBy: 'current-user',
        role: index === 0 ? 'primary' : 'secondary',
        status: 'pending' as const,
        deadline: deadline || new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString(),
        priority: priority || 'medium',
        estimatedTime: 30
      }));

      await reviewSystem.assignReviewers(reviewId, assignments);
    }

    return NextResponse.json({
      success: true,
      data: { 
        message: 'Review updated successfully',
        reviewId 
      }
    });

  } catch (error) {
    console.error('Review update error:', error);

    return NextResponse.json(
      {
        error: 'Failed to update review',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
