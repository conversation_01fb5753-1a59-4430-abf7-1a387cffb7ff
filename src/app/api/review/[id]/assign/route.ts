/**
 * Review Assignment API Endpoint
 * Handles reviewer assignment to reviews
 */

import { NextRequest, NextResponse } from 'next/server';
import { ResponseFormatter } from '../../../../../core/api/response-formatter';
import { ErrorType } from '../../../../../core/utils/error-handler';
import { getReviewSystem } from '../../../../../core/workflow/singleton';

// POST /api/review/[id]/assign - Assign reviewer to review
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const reviewId = id;
    const body = await request.json();
    
    const { 
      reviewerId, 
      assignedBy = 'system', 
      priority, 
      deadline, 
      role = 'primary',
      estimatedTime,
      instructions 
    } = body;

    // Validate required fields
    if (!reviewerId) {
      return ResponseFormatter.validationError(
        'Reviewer ID is required',
        [{ field: 'reviewerId', message: 'Reviewer ID is required', code: 'REQUIRED' }],
        requestId
      );
    }

    const reviewSystem = getReviewSystem();
    
    // Check if review exists
    const review = await reviewSystem.getReview(reviewId);
    if (!review) {
      return ResponseFormatter.notFound('Review', reviewId, requestId);
    }

    // Check if review is still assignable
    if (review.status === 'completed' || review.status === 'expired') {
      return ResponseFormatter.error(
        ErrorType.VALIDATION,
        'REVIEW_NOT_ASSIGNABLE',
        `Cannot assign reviewer to ${review.status} review`,
        { reviewId, status: review.status },
        requestId
      );
    }

    // Assign reviewer
    const assignmentResult = await reviewSystem.assignReviewer(
      reviewId,
      reviewerId,
      assignedBy,
      {
        priority,
        deadline,
        role,
        estimatedTime,
        instructions
      }
    );

    // Get updated review data
    const updatedReview = await reviewSystem.getReview(reviewId);

    return ResponseFormatter.success({
      assignment: assignmentResult,
      review: updatedReview,
      message: 'Reviewer assigned successfully'
    }, requestId);

  } catch (error) {
    console.error('Review assignment error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// DELETE /api/review/[id]/assign - Remove reviewer assignment
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const reviewId = id;
    const { searchParams } = new URL(request.url);
    const reviewerId = searchParams.get('reviewerId');

    if (!reviewerId) {
      return ResponseFormatter.validationError(
        'Reviewer ID is required',
        [{ field: 'reviewerId', message: 'Reviewer ID is required in query params', code: 'REQUIRED' }],
        requestId
      );
    }

    const reviewSystem = getReviewSystem();
    
    // Check if review exists
    const review = await reviewSystem.getReview(reviewId);
    if (!review) {
      return ResponseFormatter.notFound('Review', reviewId, requestId);
    }

    // Remove reviewer assignment
    await reviewSystem.removeReviewerAssignment(reviewId, reviewerId);

    // Get updated review data
    const updatedReview = await reviewSystem.getReview(reviewId);

    return ResponseFormatter.success({
      review: updatedReview,
      message: 'Reviewer assignment removed successfully'
    }, requestId);

  } catch (error) {
    console.error('Review assignment removal error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// GET /api/review/[id]/assign - Get assignment details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const reviewId = id;

    const reviewSystem = getReviewSystem();
    
    // Get review assignments
    const assignments = await reviewSystem.getReviewAssignments(reviewId);

    return ResponseFormatter.success({
      reviewId,
      assignments,
      summary: {
        totalAssignments: assignments.length,
        pendingAssignments: assignments.filter(a => a.status === 'pending').length,
        completedAssignments: assignments.filter(a => a.status === 'completed').length
      }
    }, requestId);

  } catch (error) {
    console.error('Review assignment fetch error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// PUT /api/review/[id]/assign - Update assignment details
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const reviewId = id;
    const body = await request.json();
    
    const { 
      reviewerId, 
      priority, 
      deadline, 
      role,
      estimatedTime,
      instructions 
    } = body;

    if (!reviewerId) {
      return ResponseFormatter.validationError(
        'Reviewer ID is required',
        [{ field: 'reviewerId', message: 'Reviewer ID is required', code: 'REQUIRED' }],
        requestId
      );
    }

    const reviewSystem = getReviewSystem();
    
    // Update assignment
    await reviewSystem.updateReviewerAssignment(reviewId, reviewerId, {
      priority,
      deadline,
      role,
      estimatedTime,
      instructions
    });

    // Get updated assignment
    const assignments = await reviewSystem.getReviewAssignments(reviewId);
    const updatedAssignment = assignments.find(a => a.reviewerId === reviewerId);

    return ResponseFormatter.success({
      assignment: updatedAssignment,
      message: 'Assignment updated successfully'
    }, requestId);

  } catch (error) {
    console.error('Review assignment update error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}
