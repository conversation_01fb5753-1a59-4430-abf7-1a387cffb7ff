import { NextRequest, NextResponse } from 'next/server';
import { getReviewSystem, getStateStore } from '../../../../../core/workflow/singleton';
import { ResponseFormatter } from '../../../../../core/api/response-formatter';
import { ErrorType } from '../../../../../core/utils/error-handler';

/**
 * GET /api/review/[id]/versions
 * Get all versions of content for comparison
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = crypto.randomUUID();
  
  try {
    const { id } = await params;
    const reviewId = id;

    console.log(`🔍 Fetching content versions for review: ${reviewId}`);

    // Get review system and state store
    const reviewSystem = getReviewSystem();
    const stateStore = getStateStore();

    // Get review data
    const reviewData = await reviewSystem.getReview(reviewId);
    if (!reviewData) {
      return ResponseFormatter.error(
        'Review not found',
        ErrorType.NOT_FOUND,
        requestId
      );
    }

    // Get execution data to find all versions
    const execution = await stateStore.getExecution(reviewData.executionId);
    if (!execution) {
      return ResponseFormatter.error(
        'Execution not found',
        ErrorType.NOT_FOUND,
        requestId
      );
    }

    // Find all content versions from the execution history
    const versions = [];
    
    // Current version (from review data)
    if (reviewData.content) {
      versions.push({
        version: 'current',
        versionNumber: execution.metadata?.regenerationAttempts || 1,
        timestamp: reviewData.createdAt || new Date().toISOString(),
        content: reviewData.content,
        metadata: reviewData.metadata,
        isRegeneration: (execution.metadata?.regenerationAttempts || 0) > 0,
        userFeedback: execution.metadata?.lastRejectionFeedback,
        status: 'pending_review'
      });
    }

    // Previous versions (if any regenerations occurred)
    if (execution.metadata?.regenerationAttempts > 0) {
      // Try to find previous versions in execution history
      const stepHistory = execution.steps.find(step => step.id === reviewData.stepId);
      if (stepHistory?.metadata?.previousVersions) {
        stepHistory.metadata.previousVersions.forEach((prevVersion: any, index: number) => {
          versions.push({
            version: `v${index + 1}`,
            versionNumber: index + 1,
            timestamp: prevVersion.timestamp,
            content: prevVersion.content,
            metadata: prevVersion.metadata,
            isRegeneration: index > 0,
            userFeedback: prevVersion.userFeedback,
            status: 'rejected'
          });
        });
      }
    }

    // Sort versions by version number (newest first)
    versions.sort((a, b) => b.versionNumber - a.versionNumber);

    console.log(`✅ Found ${versions.length} content versions for review ${reviewId}`);

    return ResponseFormatter.success({
      reviewId,
      executionId: reviewData.executionId,
      stepId: reviewData.stepId,
      totalVersions: versions.length,
      currentVersion: versions[0]?.versionNumber || 1,
      regenerationAttempts: execution.metadata?.regenerationAttempts || 0,
      versions
    }, requestId);

  } catch (error) {
    console.error('❌ Error fetching content versions:', error);
    return ResponseFormatter.error(
      'Failed to fetch content versions',
      ErrorType.INTERNAL_ERROR,
      requestId
    );
  }
}
