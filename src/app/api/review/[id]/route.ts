/**
 * Review API
 * Simple review interface for human approval/rejection
 */

import { NextRequest, NextResponse } from 'next/server';
import { getReviewSystem } from '../../../../core/workflow/singleton';
import { ResponseFormatter } from '../../../../core/api/response-formatter';
import { ErrorType } from '../../../../core/utils/error-handler';

/**
 * Trigger feedback-driven regeneration
 */
async function triggerFeedbackDrivenRegeneration(
  executionId: string,
  stepId: string,
  feedback: string,
  originalContentId: string
): Promise<any> {
  try {
    // Import workflow engine and state store
    const { getWorkflowEngine, getStateStore } = await import('../../../../core/workflow/singleton');
    const workflowEngine = getWorkflowEngine();
    const stateStore = getStateStore();

    console.log(`🔄 Processing feedback for regeneration:`, { executionId, stepId, feedback });

    // Get current execution state
    const execution = await stateStore.getExecution(executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    // Store the feedback for agent consultation
    const feedbackData = {
      originalContentId,
      userFeedback: feedback,
      rejectionReason: 'user_feedback',
      timestamp: new Date().toISOString(),
      regenerationAttempt: (execution.metadata?.regenerationAttempts || 0) + 1
    };

    // Update execution metadata with feedback
    execution.metadata = {
      ...execution.metadata,
      feedbackData,
      regenerationAttempts: feedbackData.regenerationAttempt,
      lastRejectionFeedback: feedback
    };

    // Save updated execution
    await stateStore.saveExecution(execution);

    // Find the step that needs regeneration
    const stepToRegenerate = execution.steps.find(step => step.id === stepId);
    if (!stepToRegenerate) {
      throw new Error(`Step ${stepId} not found in execution ${executionId}`);
    }

    // Reset the step status to trigger regeneration
    stepToRegenerate.status = 'pending';
    stepToRegenerate.outputs = {};
    stepToRegenerate.metadata = {
      ...stepToRegenerate.metadata,
      regenerating: true,
      feedbackDriven: true,
      userFeedback: feedback,
      previousVersion: originalContentId
    };

    // Mark execution as running again
    execution.status = 'running';
    execution.currentStepIndex = execution.steps.findIndex(step => step.id === stepId);

    // Save the updated execution
    await stateStore.saveExecution(execution);

    console.log(`🔄 Triggering workflow regeneration from step ${stepId}`);

    // Resume execution from the regeneration step
    await workflowEngine.resumeExecution(executionId);

    return {
      success: true,
      executionId,
      stepId,
      regenerationAttempt: feedbackData.regenerationAttempt,
      message: 'Feedback-driven regeneration triggered successfully'
    };

  } catch (error) {
    console.error('❌ Error in feedback-driven regeneration:', error);
    throw error;
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);

  try {
    const { id } = await params;
    const reviewId = id;

    // Get review system singleton
    const reviewSystem = getReviewSystem();

    // Get review data
    const reviewData = await reviewSystem.getReview(reviewId);

    if (!reviewData) {
      return ResponseFormatter.notFound('Review', reviewId, requestId);
    }

    // Enrich with additional data
    const enrichedReview = {
      ...reviewData,
      timeRemaining: reviewData.deadline ?
        Math.max(0, new Date(reviewData.deadline).getTime() - Date.now()) / (1000 * 60) : null,
      isOverdue: reviewData.deadline ?
        new Date(reviewData.deadline).getTime() < Date.now() && reviewData.status !== 'completed' : false,
      canEdit: reviewData.status === 'pending' || reviewData.status === 'in_progress',
      canSubmit: reviewData.status === 'pending' || reviewData.status === 'in_progress'
    };

    return ResponseFormatter.success(enrichedReview, requestId);

  } catch (error) {
    console.error('Review fetch error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// POST /api/review/[id] - Submit review decision
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);

  try {
    const { id } = await params;
    const reviewId = id;
    const body = await request.json();
    const { decision, edits, reviewer, feedback, timeSpent } = body;

    // Debug logging
    console.log(`📝 Review submission for ${reviewId}:`, { decision, edits, reviewer });

    // Validate required fields
    if (!decision || !['approve', 'reject', 'needs_changes'].includes(decision)) {
      return ResponseFormatter.validationError(
        'Valid decision is required',
        [{ field: 'decision', message: 'Must be approve, reject, or needs_changes', code: 'INVALID_VALUE' }],
        requestId
      );
    }

    if (!reviewer) {
      return ResponseFormatter.validationError(
        'Reviewer is required',
        [{ field: 'reviewer', message: 'Reviewer ID is required', code: 'REQUIRED' }],
        requestId
      );
    }

    // Get review system singleton
    const reviewSystem = getReviewSystem();

    // Submit review
    await reviewSystem.submitReview(reviewId, decision, edits);

    // Get the review to extract workflow information
    const review = await reviewSystem.getReview(reviewId);
    let workflowExecutionId = null;
    let regenerationResult = null;

    if (review && review.executionId) {
      workflowExecutionId = review.executionId;

      // If approved, trigger workflow continuation
      if (decision === 'approve') {
        try {
          // Import workflow engine
          const { getWorkflowEngine } = await import('../../../../core/workflow/singleton');
          const workflowEngine = getWorkflowEngine();

          console.log(`🔄 Resuming workflow execution ${review.executionId} after approval`);
          await workflowEngine.resumeExecution(review.executionId);
          console.log(`✅ Workflow execution ${review.executionId} resumed successfully`);
        } catch (error) {
          console.error(`❌ Failed to resume workflow execution ${review.executionId}:`, error);
          // Don't fail the review submission if workflow resumption fails
        }
      } else {
        // Handle rejection with feedback-driven regeneration
        console.log(`📝 Review rejected with feedback: ${edits}`);

        if (edits && edits.trim().length > 0) {
          try {
            console.log(`🔄 Starting feedback-driven regeneration for ${review.executionId}`);
            regenerationResult = await triggerFeedbackDrivenRegeneration(
              review.executionId,
              review.stepId,
              edits,
              review.contentId
            );
            console.log(`✅ Regeneration triggered successfully:`, regenerationResult);
          } catch (error) {
            console.error(`❌ Failed to trigger regeneration:`, error);
            // Continue with normal rejection flow
          }
        }
      }
    }

    return ResponseFormatter.success({
      reviewId,
      decision,
      reviewer,
      feedback,
      timeSpent,
      submittedAt: new Date().toISOString(),
      message: decision === 'approve'
        ? 'Review approved! Workflow continuing to next step.'
        : regenerationResult
          ? 'Feedback received! Content is being regenerated based on your input.'
          : 'Review submitted successfully',
      workflowExecutionId,
      regeneration: regenerationResult,
      nextAction: decision === 'approve'
        ? 'workflow_continued'
        : regenerationResult
          ? 'content_regenerating'
          : 'feedback_provided'
    }, requestId);

  } catch (error) {
    console.error('Review submission error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}
