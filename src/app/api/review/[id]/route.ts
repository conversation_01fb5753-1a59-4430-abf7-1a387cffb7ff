/**
 * Review API
 * Simple review interface for human approval/rejection
 */

import { NextRequest, NextResponse } from 'next/server';
import { getReviewSystem } from '../../../../core/workflow/singleton';
import { ResponseFormatter } from '../../../../core/api/response-formatter';
import { ErrorType } from '../../../../core/utils/error-handler';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);

  try {
    const { id } = await params;
    const reviewId = id;

    // Get review system singleton
    const reviewSystem = getReviewSystem();

    // Get review data
    const reviewData = await reviewSystem.getReview(reviewId);

    if (!reviewData) {
      return ResponseFormatter.notFound('Review', reviewId, requestId);
    }

    // Enrich with additional data
    const enrichedReview = {
      ...reviewData,
      timeRemaining: reviewData.deadline ?
        Math.max(0, new Date(reviewData.deadline).getTime() - Date.now()) / (1000 * 60) : null,
      isOverdue: reviewData.deadline ?
        new Date(reviewData.deadline).getTime() < Date.now() && reviewData.status !== 'completed' : false,
      canEdit: reviewData.status === 'pending' || reviewData.status === 'in_progress',
      canSubmit: reviewData.status === 'pending' || reviewData.status === 'in_progress'
    };

    return ResponseFormatter.success(enrichedReview, requestId);

  } catch (error) {
    console.error('Review fetch error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// POST /api/review/[id] - Submit review decision
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);

  try {
    const { id } = await params;
    const reviewId = id;
    const body = await request.json();
    const { decision, edits, reviewer, feedback, timeSpent } = body;

    // Debug logging
    console.log(`📝 Review submission for ${reviewId}:`, { decision, edits, reviewer });

    // Validate required fields
    if (!decision || !['approve', 'reject', 'needs_changes'].includes(decision)) {
      return ResponseFormatter.validationError(
        'Valid decision is required',
        [{ field: 'decision', message: 'Must be approve, reject, or needs_changes', code: 'INVALID_VALUE' }],
        requestId
      );
    }

    if (!reviewer) {
      return ResponseFormatter.validationError(
        'Reviewer is required',
        [{ field: 'reviewer', message: 'Reviewer ID is required', code: 'REQUIRED' }],
        requestId
      );
    }

    // Get review system singleton
    const reviewSystem = getReviewSystem();

    // Submit review
    await reviewSystem.submitReview(reviewId, decision, edits);

    return ResponseFormatter.success({
      reviewId,
      decision,
      reviewer,
      feedback,
      timeSpent,
      submittedAt: new Date().toISOString(),
      message: 'Review submitted successfully'
    }, requestId);

  } catch (error) {
    console.error('Review submission error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}
