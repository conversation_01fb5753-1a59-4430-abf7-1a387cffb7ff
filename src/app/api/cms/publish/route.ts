/**
 * CMS Publishing API
 * Publishes approved workflow artifacts to Payload CMS
 */

import { NextRequest, NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@payload-config';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      artifactId, 
      title, 
      content, 
      type, 
      executionId, 
      stepId, 
      metadata 
    } = body;

    // Validate required fields
    if (!artifactId || !title || !content) {
      return NextResponse.json(
        { error: 'Missing required fields: artifactId, title, content' },
        { status: 400 }
      );
    }

    // Get Payload instance
    const payload = await getPayload({ config });

    // Determine collection based on artifact type
    let collection = 'posts'; // Default to posts
    let publishData: any = {
      title,
      content,
      status: 'published',
      meta: {
        description: `Generated content from workflow execution ${executionId}`,
      },
      // Add workflow metadata
      workflowMetadata: {
        artifactId,
        executionId,
        stepId,
        generatedAt: new Date().toISOString(),
        ...metadata
      }
    };

    // Customize based on artifact type
    switch (type) {
      case 'blog_post':
      case 'content_draft':
        collection = 'posts';
        // Parse JSON content if it's structured
        if (typeof content === 'string' && content.startsWith('{')) {
          try {
            const parsed = JSON.parse(content);
            publishData = {
              ...publishData,
              title: parsed.title || title,
              content: parsed.content || content,
              meta: {
                description: parsed.meta_description || publishData.meta.description,
              }
            };
          } catch (e) {
            // Keep original content if parsing fails
          }
        }
        break;
      
      case 'keyword_research':
        collection = 'research';
        publishData = {
          ...publishData,
          type: 'keyword_research',
          data: content
        };
        break;
      
      default:
        // Use posts collection for unknown types
        break;
    }

    // Create the document in Payload CMS
    const result = await payload.create({
      collection,
      data: publishData,
    });

    // Return success with the created document info
    return NextResponse.json({
      success: true,
      data: {
        id: result.id,
        url: `/admin/collections/${collection}/${result.id}`,
        collection,
        title: result.title || title,
        status: result.status
      },
      message: `Successfully published to ${collection}`
    });

  } catch (error) {
    console.error('CMS publish error:', error);

    // Handle specific Payload errors
    if (error instanceof Error) {
      if (error.message.includes('Collection')) {
        return NextResponse.json(
          { error: 'Invalid collection or configuration error', details: error.message },
          { status: 400 }
        );
      }
      
      if (error.message.includes('validation')) {
        return NextResponse.json(
          { error: 'Validation error', details: error.message },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      {
        error: 'Failed to publish to CMS',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check publishing status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const artifactId = searchParams.get('artifactId');

    if (!artifactId) {
      return NextResponse.json(
        { error: 'artifactId parameter required' },
        { status: 400 }
      );
    }

    // Get Payload instance
    const payload = await getPayload({ config });

    // Search for documents with this artifact ID
    const collections = ['posts', 'research']; // Add more collections as needed
    const results = [];

    for (const collection of collections) {
      try {
        const docs = await payload.find({
          collection,
          where: {
            'workflowMetadata.artifactId': {
              equals: artifactId
            }
          }
        });
        
        results.push(...docs.docs.map(doc => ({
          ...doc,
          collection
        })));
      } catch (e) {
        // Collection might not exist, continue
        console.warn(`Collection ${collection} not found or accessible`);
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        artifactId,
        published: results.length > 0,
        documents: results.map(doc => ({
          id: doc.id,
          collection: doc.collection,
          title: doc.title,
          status: doc.status,
          url: `/admin/collections/${doc.collection}/${doc.id}`,
          createdAt: doc.createdAt
        }))
      }
    });

  } catch (error) {
    console.error('CMS status check error:', error);

    return NextResponse.json(
      {
        error: 'Failed to check publishing status',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
