/**
 * Tests for AI Generation API
 * 
 * Testing the missing /api/ai/generate endpoint that was causing 404 errors
 */

import { NextRequest } from 'next/server';
import { POST, GET } from './route';

// Mock OpenAI
jest.mock('openai', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: jest.fn().mockResolvedValue({
            choices: [{
              message: {
                content: 'Generated AI content for testing'
              }
            }],
            usage: {
              total_tokens: 150
            }
          })
        }
      }
    }))
  };
});

// Mock environment variables
const originalEnv = process.env;

beforeEach(() => {
  jest.resetModules();
  process.env = {
    ...originalEnv,
    OPENAI_API_KEY: 'test-api-key'
  };
});

afterEach(() => {
  process.env = originalEnv;
});

describe('/api/ai/generate', () => {
  describe('POST', () => {
    it('should generate AI content successfully', async () => {
      const request = new NextRequest('http://localhost:3000/api/ai/generate', {
        method: 'POST',
        body: JSON.stringify({
          prompt: 'Write a blog post about AI',
          context: {
            topic: 'Artificial Intelligence',
            targetAudience: 'Developers',
            contentType: 'blog-post',
            keywords: ['AI', 'machine learning'],
            tone: 'professional',
            stepId: 'content-creation',
            workflowExecutionId: 'test-execution-123'
          },
          options: {
            maxTokens: 500,
            temperature: 0.7,
            model: 'gpt-3.5-turbo'
          }
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      expect(data.data.content).toBe('Generated AI content for testing');
      expect(data.data.metadata).toBeDefined();
      expect(data.data.metadata.model).toBe('gpt-3.5-turbo');
      expect(data.data.metadata.tokensUsed).toBe(150);
      expect(data.data.metadata.stepId).toBe('content-creation');
      expect(data.data.metadata.workflowExecutionId).toBe('test-execution-123');
    });

    it('should return 400 when prompt is missing', async () => {
      const request = new NextRequest('http://localhost:3000/api/ai/generate', {
        method: 'POST',
        body: JSON.stringify({
          context: {
            topic: 'Test Topic'
          }
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Prompt is required');
    });

    it('should return 500 when OpenAI API key is not configured', async () => {
      // Remove API key
      delete process.env.OPENAI_API_KEY;

      const request = new NextRequest('http://localhost:3000/api/ai/generate', {
        method: 'POST',
        body: JSON.stringify({
          prompt: 'Test prompt'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('AI service not configured');
    });

    it('should use default options when not provided', async () => {
      const request = new NextRequest('http://localhost:3000/api/ai/generate', {
        method: 'POST',
        body: JSON.stringify({
          prompt: 'Simple test prompt'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.metadata.model).toBe('gpt-3.5-turbo');
    });

    it('should build enhanced prompt with context', async () => {
      const request = new NextRequest('http://localhost:3000/api/ai/generate', {
        method: 'POST',
        body: JSON.stringify({
          prompt: 'Write content',
          context: {
            topic: 'AI Technology',
            targetAudience: 'Business Leaders',
            contentType: 'article',
            keywords: ['AI', 'business'],
            tone: 'professional'
          }
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
    });
  });

  describe('GET', () => {
    it('should return service status', async () => {
      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.status).toBe('active');
      expect(data.data.service).toBe('AI Content Generation');
      expect(data.data.models).toContain('gpt-3.5-turbo');
      expect(data.data.models).toContain('gpt-4');
      expect(data.data.capabilities).toContain('content-generation');
      expect(data.data.version).toBe('1.0.0');
    });
  });
});
