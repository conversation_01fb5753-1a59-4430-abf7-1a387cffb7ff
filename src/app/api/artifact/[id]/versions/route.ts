/**
 * Artifact Version Management API
 * Handles version history and operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { ResponseFormatter } from '../../../../../core/api/response-formatter';
import { ErrorType } from '../../../../../core/utils/error-handler';
import { artifactVersionManager } from '../../../../../core/review/version-manager';

// GET /api/artifact/[id]/versions - Get version history
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const artifactId = id;
    const { searchParams } = new URL(request.url);
    
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const includeContent = searchParams.get('includeContent') === 'true';

    // Validate pagination
    if (page < 1 || limit < 1 || limit > 100) {
      return ResponseFormatter.validationError(
        'Invalid pagination parameters',
        [
          { field: 'page', message: 'Page must be >= 1', code: 'INVALID_VALUE' },
          { field: 'limit', message: 'Limit must be between 1 and 100', code: 'INVALID_RANGE' }
        ],
        requestId
      );
    }

    // Get version history
    const allVersions = await artifactVersionManager.getVersionHistory(artifactId);
    
    if (allVersions.length === 0) {
      return ResponseFormatter.success({
        artifactId,
        versions: [],
        pagination: {
          page,
          limit,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false
        },
        metrics: {
          totalVersions: 0,
          activeVersion: 0,
          averageChangeSize: 0,
          mostActiveContributor: '',
          changeFrequency: 0
        }
      }, requestId);
    }

    // Apply pagination
    const total = allVersions.length;
    const totalPages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedVersions = allVersions.slice(startIndex, endIndex);

    // Format versions for response
    const formattedVersions = paginatedVersions.map(version => {
      const formatted: any = {
        id: version.id,
        version: version.version,
        createdAt: version.createdAt,
        createdBy: version.createdBy,
        changeDescription: version.changeDescription,
        isActive: version.isActive,
        contentHash: version.contentHash,
        metadata: version.metadata
      };

      if (includeContent) {
        formatted.content = version.content;
      }

      return formatted;
    });

    // Get metrics
    const metrics = artifactVersionManager.getVersionMetrics(artifactId);

    return ResponseFormatter.success({
      artifactId,
      versions: formattedVersions,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      metrics
    }, requestId);

  } catch (error) {
    console.error('Version history fetch error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// POST /api/artifact/[id]/versions - Create new version
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const artifactId = id;
    const body = await request.json();
    
    const { 
      content, 
      changeDescription = 'Manual version creation',
      createdBy = 'system',
      metadata = {}
    } = body;

    // Validate required fields
    if (!content) {
      return ResponseFormatter.validationError(
        'Content is required',
        [{ field: 'content', message: 'Content is required for new version', code: 'REQUIRED' }],
        requestId
      );
    }

    // Create new version
    const versionId = await artifactVersionManager.createVersion(
      artifactId,
      content,
      changeDescription,
      createdBy,
      metadata
    );

    // Get created version
    const newVersion = artifactVersionManager.getVersion(versionId);

    return ResponseFormatter.success({
      version: newVersion,
      message: 'Version created successfully'
    }, requestId);

  } catch (error) {
    console.error('Version creation error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// DELETE /api/artifact/[id]/versions - Cleanup old versions
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const artifactId = id;
    const { searchParams } = new URL(request.url);
    
    const keepCount = parseInt(searchParams.get('keep') || '10');

    if (keepCount < 1) {
      return ResponseFormatter.validationError(
        'Invalid keep count',
        [{ field: 'keep', message: 'Keep count must be >= 1', code: 'INVALID_VALUE' }],
        requestId
      );
    }

    // Cleanup old versions
    const deletedCount = await artifactVersionManager.cleanupOldVersions(artifactId, keepCount);

    return ResponseFormatter.success({
      message: `Cleaned up ${deletedCount} old versions`,
      deletedCount,
      keepCount
    }, requestId);

  } catch (error) {
    console.error('Version cleanup error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}
