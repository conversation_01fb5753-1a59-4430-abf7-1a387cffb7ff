/**
 * Artifact Revert API
 * Handles reverting artifacts to previous versions
 */

import { NextRequest, NextResponse } from 'next/server';
import { ResponseFormatter } from '../../../../../core/api/response-formatter';
import { ErrorType } from '../../../../../core/utils/error-handler';
import { artifactVersionManager } from '../../../../../core/review/version-manager';
import { getStateStore } from '../../../../../core/workflow/singleton';

// POST /api/artifact/[id]/revert - Revert to specific version
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const artifactId = id;
    const body = await request.json();
    
    const { 
      versionId, 
      revertedBy = 'system',
      reason = 'Manual revert'
    } = body;

    // Validate required fields
    if (!versionId) {
      return ResponseFormatter.validationError(
        'Version ID is required',
        [{ field: 'versionId', message: 'Version ID is required for revert', code: 'REQUIRED' }],
        requestId
      );
    }

    // Check if target version exists
    const targetVersion = artifactVersionManager.getVersion(versionId);
    if (!targetVersion || targetVersion.artifactId !== artifactId) {
      return ResponseFormatter.notFound('Version', versionId, requestId);
    }

    // Get current version for comparison
    const currentVersion = artifactVersionManager.getActiveVersion(artifactId);
    
    // Revert to target version
    const newVersionId = await artifactVersionManager.revertToVersion(
      artifactId, 
      versionId, 
      revertedBy
    );

    // Update artifact in state store
    const stateStore = getStateStore();
    await stateStore.update(state => {
      if (!state || !state.content[artifactId]) return state;
      
      const artifact = state.content[artifactId];
      artifact.content = targetVersion.content;
      artifact.updatedAt = new Date().toISOString();
      artifact.metadata = {
        ...artifact.metadata,
        revertedFrom: versionId,
        revertedAt: new Date().toISOString(),
        revertedBy,
        revertReason: reason,
        version: (artifact.metadata.version || 1) + 1
      };

      return state;
    });

    // Get the new version details
    const newVersion = artifactVersionManager.getVersion(newVersionId);

    // Create comparison with previous version
    let comparison = null;
    if (currentVersion) {
      try {
        comparison = await artifactVersionManager.compareVersions(currentVersion.id, newVersionId);
      } catch (error) {
        console.warn('Failed to create version comparison:', error);
      }
    }

    return ResponseFormatter.success({
      revert: {
        artifactId,
        fromVersionId: currentVersion?.id,
        fromVersion: currentVersion?.version,
        toVersionId: versionId,
        toVersion: targetVersion.version,
        newVersionId,
        newVersion: newVersion?.version,
        revertedBy,
        revertedAt: new Date().toISOString(),
        reason
      },
      comparison,
      message: `Successfully reverted to version ${targetVersion.version}`
    }, requestId);

  } catch (error) {
    console.error('Artifact revert error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// GET /api/artifact/[id]/revert - Get revert history
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const artifactId = id;

    // Get version history
    const versions = await artifactVersionManager.getVersionHistory(artifactId);
    
    // Filter for reverted versions (versions that have revert metadata)
    const revertHistory = versions
      .filter(version => version.metadata?.revertedFrom)
      .map(version => ({
        id: version.id,
        version: version.version,
        revertedFrom: version.metadata.revertedFrom,
        revertedFromVersion: version.metadata.revertedFromVersion,
        revertedAt: version.createdAt,
        revertedBy: version.createdBy,
        reason: version.metadata.revertReason || version.changeDescription,
        metadata: version.metadata
      }))
      .sort((a, b) => new Date(b.revertedAt).getTime() - new Date(a.revertedAt).getTime());

    return ResponseFormatter.success({
      artifactId,
      revertHistory,
      summary: {
        totalReverts: revertHistory.length,
        lastRevert: revertHistory[0]?.revertedAt || null,
        mostRevertedVersion: this.getMostRevertedVersion(revertHistory)
      }
    }, requestId);

  } catch (error) {
    console.error('Revert history fetch error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// Helper function to find most reverted version
function getMostRevertedVersion(revertHistory: any[]): { version: number; count: number } | null {
  if (revertHistory.length === 0) return null;

  const versionCounts = new Map<number, number>();
  
  revertHistory.forEach(revert => {
    const version = revert.revertedFromVersion;
    versionCounts.set(version, (versionCounts.get(version) || 0) + 1);
  });

  let mostReverted = { version: 0, count: 0 };
  for (const [version, count] of versionCounts.entries()) {
    if (count > mostReverted.count) {
      mostReverted = { version, count };
    }
  }

  return mostReverted.count > 0 ? mostReverted : null;
}
