/**
 * Individual Artifact API Endpoints
 * Handles single artifact operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { ResponseFormatter } from '../../../../core/api/response-formatter';
import { ErrorType } from '../../../../core/utils/error-handler';
import { getStateStore } from '../../../../core/workflow/singleton';
import { artifactVersionManager } from '../../../../core/review/version-manager';

// GET /api/artifact/[id] - Get artifact with current content
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const artifactId = id;
    const { searchParams } = new URL(request.url);
    const includeVersions = searchParams.get('includeVersions') === 'true';
    const includeHistory = searchParams.get('includeHistory') === 'true';

    const stateStore = getStateStore();
    const state = await stateStore.get();
    
    if (!state || !state.content[artifactId]) {
      return ResponseFormatter.notFound('Artifact', artifactId, requestId);
    }

    const artifact = state.content[artifactId];
    
    // Enrich with version information
    let enrichedArtifact = { ...artifact };

    if (includeVersions || includeHistory) {
      try {
        const versions = await artifactVersionManager.getVersionHistory(artifactId);
        const activeVersion = artifactVersionManager.getActiveVersion(artifactId);
        const metrics = artifactVersionManager.getVersionMetrics(artifactId);

        enrichedArtifact = {
          ...artifact,
          versionInfo: {
            hasVersions: versions.length > 0,
            versionCount: versions.length,
            activeVersion: activeVersion?.version || 1,
            latestVersion: versions[0]?.version || 1,
            metrics
          }
        };

        if (includeVersions) {
          enrichedArtifact.versions = versions.map(v => ({
            id: v.id,
            version: v.version,
            createdAt: v.createdAt,
            createdBy: v.createdBy,
            changeDescription: v.changeDescription,
            isActive: v.isActive,
            contentHash: v.contentHash
          }));
        }

        if (includeHistory) {
          enrichedArtifact.versionHistory = versions;
        }
      } catch (error) {
        console.warn(`Failed to get version info for artifact ${artifactId}:`, error);
      }
    }

    return ResponseFormatter.success(enrichedArtifact, requestId);

  } catch (error) {
    console.error('Artifact fetch error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// PUT /api/artifact/[id] - Update artifact content (creates new version)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const artifactId = id;
    const body = await request.json();
    
    const { 
      content, 
      title, 
      status, 
      changeDescription = 'Updated content',
      updatedBy = 'system',
      metadata = {}
    } = body;

    const stateStore = getStateStore();
    
    // Get current artifact
    const state = await stateStore.get();
    if (!state || !state.content[artifactId]) {
      return ResponseFormatter.notFound('Artifact', artifactId, requestId);
    }

    const currentArtifact = state.content[artifactId];
    
    // Update artifact
    await stateStore.update(state => {
      if (!state) return null;
      
      const artifact = state.content[artifactId];
      if (!artifact) return state;

      // Update fields
      if (content !== undefined) artifact.content = content;
      if (title !== undefined) artifact.title = title;
      if (status !== undefined) artifact.status = status;
      
      artifact.updatedAt = new Date().toISOString();
      artifact.metadata = { ...artifact.metadata, ...metadata };
      
      // Increment version number
      artifact.metadata.version = (artifact.metadata.version || 1) + 1;

      return state;
    });

    // Create new version if content changed
    if (content !== undefined && content !== currentArtifact.content) {
      try {
        await artifactVersionManager.createVersion(
          artifactId,
          content,
          changeDescription,
          updatedBy,
          metadata
        );
      } catch (error) {
        console.warn('Failed to create version:', error);
      }
    }

    // Get updated artifact
    const updatedState = await stateStore.get();
    const updatedArtifact = updatedState?.content[artifactId];

    return ResponseFormatter.success({
      artifact: updatedArtifact,
      message: 'Artifact updated successfully'
    }, requestId);

  } catch (error) {
    console.error('Artifact update error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// DELETE /api/artifact/[id] - Delete artifact
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const artifactId = id;
    const { searchParams } = new URL(request.url);
    const force = searchParams.get('force') === 'true';

    const stateStore = getStateStore();
    
    // Check if artifact exists
    const state = await stateStore.get();
    if (!state || !state.content[artifactId]) {
      return ResponseFormatter.notFound('Artifact', artifactId, requestId);
    }

    const artifact = state.content[artifactId];

    // Check if artifact can be deleted
    if (!force && (artifact.status === 'published' || artifact.status === 'approved')) {
      return ResponseFormatter.error(
        ErrorType.VALIDATION,
        'ARTIFACT_CANNOT_DELETE',
        `Cannot delete ${artifact.status} artifact without force flag`,
        { artifactId, status: artifact.status },
        requestId
      );
    }

    // Delete artifact
    await stateStore.update(state => {
      if (!state) return null;
      delete state.content[artifactId];
      return state;
    });

    // Clean up versions
    try {
      await artifactVersionManager.cleanupOldVersions(artifactId, 0); // Delete all versions
    } catch (error) {
      console.warn('Failed to cleanup versions:', error);
    }

    return ResponseFormatter.success({
      message: 'Artifact deleted successfully',
      artifactId
    }, requestId);

  } catch (error) {
    console.error('Artifact deletion error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}
