/**
 * Artifact Diff API
 * Handles version comparison and differences
 */

import { NextRequest, NextResponse } from 'next/server';
import { ResponseFormatter } from '../../../../../core/api/response-formatter';
import { ErrorType } from '../../../../../core/utils/error-handler';
import { artifactVersionManager } from '../../../../../core/review/version-manager';

// GET /api/artifact/[id]/diff - Compare versions
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const artifactId = id;
    const { searchParams } = new URL(request.url);
    
    const version1Id = searchParams.get('version1');
    const version2Id = searchParams.get('version2');
    const format = searchParams.get('format') || 'structured'; // structured, unified, side-by-side
    const includeMetadata = searchParams.get('includeMetadata') === 'true';

    // Validate required parameters
    if (!version1Id || !version2Id) {
      return ResponseFormatter.validationError(
        'Both version IDs are required',
        [
          { field: 'version1', message: 'Version 1 ID is required', code: 'REQUIRED' },
          { field: 'version2', message: 'Version 2 ID is required', code: 'REQUIRED' }
        ],
        requestId
      );
    }

    if (version1Id === version2Id) {
      return ResponseFormatter.validationError(
        'Cannot compare version with itself',
        [{ field: 'versions', message: 'Version IDs must be different', code: 'INVALID_VALUE' }],
        requestId
      );
    }

    // Get versions
    const version1 = artifactVersionManager.getVersion(version1Id);
    const version2 = artifactVersionManager.getVersion(version2Id);

    if (!version1 || version1.artifactId !== artifactId) {
      return ResponseFormatter.notFound('Version 1', version1Id, requestId);
    }

    if (!version2 || version2.artifactId !== artifactId) {
      return ResponseFormatter.notFound('Version 2', version2Id, requestId);
    }

    // Compare versions
    const comparison = await artifactVersionManager.compareVersions(version1Id, version2Id);

    // Format response based on requested format
    let formattedDiff;
    switch (format) {
      case 'unified':
        formattedDiff = this.formatUnifiedDiff(comparison);
        break;
      case 'side-by-side':
        formattedDiff = this.formatSideBySideDiff(comparison);
        break;
      case 'structured':
      default:
        formattedDiff = this.formatStructuredDiff(comparison);
        break;
    }

    const response: any = {
      artifactId,
      comparison: {
        version1: {
          id: version1.id,
          version: version1.version,
          createdAt: version1.createdAt,
          createdBy: version1.createdBy,
          changeDescription: version1.changeDescription
        },
        version2: {
          id: version2.id,
          version: version2.version,
          createdAt: version2.createdAt,
          createdBy: version2.createdBy,
          changeDescription: version2.changeDescription
        },
        summary: comparison.summary,
        similarity: comparison.similarity,
        diff: formattedDiff
      }
    };

    if (includeMetadata) {
      response.comparison.version1.metadata = version1.metadata;
      response.comparison.version2.metadata = version2.metadata;
      response.comparison.metadataDiff = this.compareMetadata(version1.metadata, version2.metadata);
    }

    return ResponseFormatter.success(response, requestId);

  } catch (error) {
    console.error('Version diff error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// POST /api/artifact/[id]/diff - Compare with custom content
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { id } = await params;
    const artifactId = id;
    const body = await request.json();
    
    const { 
      versionId, 
      content, 
      format = 'structured',
      label = 'Custom Content'
    } = body;

    // Validate required fields
    if (!versionId) {
      return ResponseFormatter.validationError(
        'Version ID is required',
        [{ field: 'versionId', message: 'Version ID is required', code: 'REQUIRED' }],
        requestId
      );
    }

    if (!content) {
      return ResponseFormatter.validationError(
        'Content is required',
        [{ field: 'content', message: 'Content is required for comparison', code: 'REQUIRED' }],
        requestId
      );
    }

    // Get version
    const version = artifactVersionManager.getVersion(versionId);
    if (!version || version.artifactId !== artifactId) {
      return ResponseFormatter.notFound('Version', versionId, requestId);
    }

    // Create temporary version for comparison
    const tempVersionId = `temp_${Date.now()}`;
    const tempVersion = {
      id: tempVersionId,
      artifactId,
      version: -1, // Indicate this is temporary
      content,
      contentHash: 'temp',
      createdAt: new Date().toISOString(),
      createdBy: 'comparison',
      changeDescription: label,
      metadata: {},
      isActive: false
    };

    // Manually calculate differences
    const differences = this.calculateDifferences(version.content, content);
    const summary = this.summarizeDifferences(differences);
    const similarity = this.calculateSimilarity(version.content, content);

    const comparison = {
      versionId1: versionId,
      versionId2: tempVersionId,
      differences,
      summary,
      similarity
    };

    // Format diff
    let formattedDiff;
    switch (format) {
      case 'unified':
        formattedDiff = this.formatUnifiedDiff(comparison);
        break;
      case 'side-by-side':
        formattedDiff = this.formatSideBySideDiff(comparison);
        break;
      case 'structured':
      default:
        formattedDiff = this.formatStructuredDiff(comparison);
        break;
    }

    return ResponseFormatter.success({
      artifactId,
      comparison: {
        version: {
          id: version.id,
          version: version.version,
          createdAt: version.createdAt,
          createdBy: version.createdBy,
          changeDescription: version.changeDescription
        },
        customContent: {
          label,
          timestamp: new Date().toISOString()
        },
        summary: comparison.summary,
        similarity: comparison.similarity,
        diff: formattedDiff
      }
    }, requestId);

  } catch (error) {
    console.error('Custom content diff error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// Helper methods for formatting diffs
function formatStructuredDiff(comparison: any): any {
  return {
    type: 'structured',
    changes: comparison.differences.map((diff: any) => ({
      type: diff.type,
      path: diff.path,
      description: diff.description,
      oldValue: diff.oldValue,
      newValue: diff.newValue
    }))
  };
}

function formatUnifiedDiff(comparison: any): any {
  const lines: string[] = [];
  
  comparison.differences.forEach((diff: any) => {
    switch (diff.type) {
      case 'added':
        lines.push(`+ ${diff.path}: ${JSON.stringify(diff.newValue)}`);
        break;
      case 'removed':
        lines.push(`- ${diff.path}: ${JSON.stringify(diff.oldValue)}`);
        break;
      case 'modified':
        lines.push(`- ${diff.path}: ${JSON.stringify(diff.oldValue)}`);
        lines.push(`+ ${diff.path}: ${JSON.stringify(diff.newValue)}`);
        break;
    }
  });

  return {
    type: 'unified',
    content: lines.join('\n')
  };
}

function formatSideBySideDiff(comparison: any): any {
  return {
    type: 'side-by-side',
    changes: comparison.differences.map((diff: any) => ({
      path: diff.path,
      left: diff.type === 'added' ? null : diff.oldValue,
      right: diff.type === 'removed' ? null : diff.newValue,
      changeType: diff.type
    }))
  };
}

function compareMetadata(metadata1: any, metadata2: any): any {
  // Simple metadata comparison
  const keys1 = Object.keys(metadata1 || {});
  const keys2 = Object.keys(metadata2 || {});
  const allKeys = new Set([...keys1, ...keys2]);
  
  const changes: any[] = [];
  
  for (const key of allKeys) {
    const value1 = metadata1?.[key];
    const value2 = metadata2?.[key];
    
    if (value1 !== value2) {
      changes.push({
        key,
        oldValue: value1,
        newValue: value2,
        type: value1 === undefined ? 'added' : value2 === undefined ? 'removed' : 'modified'
      });
    }
  }
  
  return changes;
}

// Simplified diff calculation methods (would use proper diff library in production)
function calculateDifferences(content1: any, content2: any, path: string = ''): any[] {
  // This is a simplified version - in production, use a proper diff library
  const differences: any[] = [];
  
  if (JSON.stringify(content1) !== JSON.stringify(content2)) {
    differences.push({
      type: 'modified',
      path: path || 'root',
      oldValue: content1,
      newValue: content2,
      description: `Content changed at ${path || 'root'}`
    });
  }
  
  return differences;
}

function summarizeDifferences(differences: any[]): any {
  return {
    totalChanges: differences.length,
    addedFields: differences.filter(d => d.type === 'added').length,
    removedFields: differences.filter(d => d.type === 'removed').length,
    modifiedFields: differences.filter(d => d.type === 'modified').length
  };
}

function calculateSimilarity(content1: any, content2: any): number {
  const str1 = JSON.stringify(content1);
  const str2 = JSON.stringify(content2);
  
  if (str1 === str2) return 1;
  if (str1.length === 0 && str2.length === 0) return 1;
  if (str1.length === 0 || str2.length === 0) return 0;
  
  // Simple character-based similarity
  const maxLength = Math.max(str1.length, str2.length);
  const minLength = Math.min(str1.length, str2.length);
  
  let commonChars = 0;
  for (let i = 0; i < minLength; i++) {
    if (str1[i] === str2[i]) {
      commonChars++;
    }
  }
  
  return commonChars / maxLength;
}
