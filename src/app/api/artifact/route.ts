/**
 * Artifact Management API
 * Handles artifact listing and bulk operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { ResponseFormatter } from '../../../core/api/response-formatter';
import { ErrorType } from '../../../core/utils/error-handler';
import { getStateStore } from '../../../core/workflow/singleton';
import { artifactVersionManager } from '../../../core/review/version-manager';

// GET /api/artifact - List artifacts with filtering
export async function GET(request: NextRequest) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const { searchParams } = new URL(request.url);
    
    // Extract query parameters
    const status = searchParams.get('status');
    const type = searchParams.get('type');
    const executionId = searchParams.get('executionId');
    const stepId = searchParams.get('stepId');
    const createdBy = searchParams.get('createdBy');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const includeVersions = searchParams.get('includeVersions') === 'true';

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return ResponseFormatter.validationError(
        'Invalid pagination parameters',
        [
          { field: 'page', message: 'Page must be >= 1', code: 'INVALID_VALUE' },
          { field: 'limit', message: 'Limit must be between 1 and 100', code: 'INVALID_RANGE' }
        ],
        requestId
      );
    }

    const stateStore = getStateStore();
    
    // Get all content items (artifacts)
    const state = await stateStore.get();
    let artifacts = state ? Object.values(state.content) : [];
    
    // Apply filters
    if (status) {
      artifacts = artifacts.filter(artifact => artifact.status === status);
    }
    
    if (type) {
      artifacts = artifacts.filter(artifact => artifact.type === type);
    }
    
    if (executionId) {
      artifacts = artifacts.filter(artifact => artifact.executionId === executionId);
    }
    
    if (stepId) {
      artifacts = artifacts.filter(artifact => artifact.stepId === stepId);
    }
    
    if (createdBy) {
      artifacts = artifacts.filter(artifact => artifact.metadata.createdBy === createdBy);
    }

    // Apply sorting
    artifacts.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'createdAt':
        case 'updatedAt':
          aValue = new Date(a[sortBy]);
          bValue = new Date(b[sortBy]);
          break;
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'status':
        case 'type':
          aValue = a[sortBy];
          bValue = b[sortBy];
          break;
        default:
          aValue = a[sortBy as keyof typeof a];
          bValue = b[sortBy as keyof typeof b];
      }
      
      if (sortOrder === 'desc') {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      } else {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      }
    });

    // Apply pagination
    const total = artifacts.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedArtifacts = artifacts.slice(startIndex, endIndex);

    // Enrich with version information if requested
    const enrichedArtifacts = await Promise.all(
      paginatedArtifacts.map(async (artifact) => {
        const enriched = {
          ...artifact,
          hasVersions: false,
          versionCount: 0,
          latestVersion: 1,
          versions: [] as any[]
        };

        if (includeVersions) {
          try {
            const versions = await artifactVersionManager.getVersionHistory(artifact.id);
            enriched.hasVersions = versions.length > 0;
            enriched.versionCount = versions.length;
            enriched.latestVersion = versions[0]?.version || 1;
            enriched.versions = versions.map(v => ({
              id: v.id,
              version: v.version,
              createdAt: v.createdAt,
              createdBy: v.createdBy,
              changeDescription: v.changeDescription,
              isActive: v.isActive
            }));
          } catch (error) {
            console.warn(`Failed to get versions for artifact ${artifact.id}:`, error);
          }
        }

        return enriched;
      })
    );

    return ResponseFormatter.paginated(
      enrichedArtifacts,
      page,
      limit,
      total,
      requestId
    );

  } catch (error) {
    console.error('Artifact listing error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// POST /api/artifact - Create a new artifact
export async function POST(request: NextRequest) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const body = await request.json();
    const {
      type,
      title,
      content,
      executionId,
      stepId,
      status = 'draft',
      metadata = {}
    } = body;

    // Validate required fields
    const validationErrors = [];
    if (!type) {
      validationErrors.push({ field: 'type', message: 'Type is required', code: 'REQUIRED' });
    }
    if (!title) {
      validationErrors.push({ field: 'title', message: 'Title is required', code: 'REQUIRED' });
    }
    if (!content) {
      validationErrors.push({ field: 'content', message: 'Content is required', code: 'REQUIRED' });
    }
    if (!executionId) {
      validationErrors.push({ field: 'executionId', message: 'Execution ID is required', code: 'REQUIRED' });
    }
    if (!stepId) {
      validationErrors.push({ field: 'stepId', message: 'Step ID is required', code: 'REQUIRED' });
    }

    if (validationErrors.length > 0) {
      return ResponseFormatter.validationError(
        'Missing required fields',
        validationErrors,
        requestId
      );
    }

    const stateStore = getStateStore();
    
    // Create artifact
    const artifactId = `artifact_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    const now = new Date().toISOString();
    
    const artifact = {
      id: artifactId,
      type,
      title,
      content,
      status,
      executionId,
      stepId,
      createdAt: now,
      updatedAt: now,
      metadata: {
        ...metadata,
        createdBy: metadata.createdBy || 'system',
        version: 1
      }
    };

    // Store artifact
    await stateStore.update(state => {
      if (!state) {
        state = {
          workflows: {},
          executions: {},
          content: {},
          reviews: {},
          lastUpdated: now,
          version: 1
        };
      }
      
      state.content[artifactId] = artifact;
      return state;
    });

    // Create initial version
    try {
      await artifactVersionManager.createVersion(
        artifactId,
        content,
        'Initial version',
        metadata.createdBy || 'system'
      );
    } catch (error) {
      console.warn('Failed to create initial version:', error);
    }

    return ResponseFormatter.success({
      artifact,
      message: 'Artifact created successfully'
    }, requestId);

  } catch (error) {
    console.error('Artifact creation error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

// PUT /api/artifact - Bulk update artifacts
export async function PUT(request: NextRequest) {
  const requestId = ResponseFormatter.getRequestId(request);
  
  try {
    const body = await request.json();
    const { artifactIds, updates, operation } = body;

    if (!artifactIds || !Array.isArray(artifactIds) || artifactIds.length === 0) {
      return ResponseFormatter.validationError(
        'Artifact IDs are required',
        [{ field: 'artifactIds', message: 'Must be a non-empty array', code: 'REQUIRED' }],
        requestId
      );
    }

    const stateStore = getStateStore();
    const results = [];
    const errors = [];

    await stateStore.update(state => {
      if (!state) return null;

      for (const artifactId of artifactIds) {
        try {
          const artifact = state.content[artifactId];
          if (!artifact) {
            errors.push({ artifactId, error: 'Artifact not found' });
            continue;
          }

          switch (operation) {
            case 'updateStatus':
              if (updates.status) {
                artifact.status = updates.status;
                artifact.updatedAt = new Date().toISOString();
                results.push({ artifactId, status: 'updated', field: 'status' });
              }
              break;
            case 'updateMetadata':
              if (updates.metadata) {
                artifact.metadata = { ...artifact.metadata, ...updates.metadata };
                artifact.updatedAt = new Date().toISOString();
                results.push({ artifactId, status: 'updated', field: 'metadata' });
              }
              break;
            case 'addTags':
              if (updates.tags) {
                const currentTags = artifact.metadata.tags || [];
                artifact.metadata.tags = [...new Set([...currentTags, ...updates.tags])];
                artifact.updatedAt = new Date().toISOString();
                results.push({ artifactId, status: 'updated', field: 'tags' });
              }
              break;
            default:
              errors.push({ artifactId, error: 'Unknown operation' });
          }
        } catch (error) {
          errors.push({ 
            artifactId, 
            error: error instanceof Error ? error.message : String(error) 
          });
        }
      }

      return state;
    });

    return ResponseFormatter.success({
      results,
      errors,
      summary: {
        total: artifactIds.length,
        successful: results.length,
        failed: errors.length
      }
    }, requestId);

  } catch (error) {
    console.error('Bulk artifact update error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}
