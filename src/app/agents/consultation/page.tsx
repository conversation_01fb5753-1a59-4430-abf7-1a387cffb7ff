/**
 * Agent Consultation Dashboard
 * 
 * Main UI for monitoring and managing the dynamic agent consultation system
 */

'use client';

import { useState, useEffect } from 'react';
import AgentStatusDashboard from '../../../components/Agents/AgentStatusDashboard';
import ConsultationMetrics from '../../../components/Agents/ConsultationMetrics';
import AgentSelectionTester from '../../../components/Agents/AgentSelectionTester';
import ConsultationHistory from '../../../components/Agents/ConsultationHistory';
import RealTimeConsultationMonitor from '../../../components/Agents/RealTimeConsultationMonitor';

type TabType = 'dashboard' | 'metrics' | 'tester' | 'history' | 'monitor';

interface AgentMetrics {
  totalConsultations: number;
  successfulConsultations: number;
  failedConsultations: number;
  averageResponseTime: number;
  averageConfidence: number;
  successRate: number;
  agentUtilization: Record<string, number>;
  lastUpdated: string;
}

interface AgentStatus {
  agentId: string;
  isRegistered: boolean;
  isAvailable: boolean;
  capabilities: string[];
  status: any;
}

interface HealthCheck {
  overallHealth: 'healthy' | 'degraded' | 'unhealthy';
  agentHealth: Record<string, 'healthy' | 'unhealthy'>;
  issues: string[];
}

export default function AgentConsultationPage() {
  const [activeTab, setActiveTab] = useState<TabType>('dashboard');
  const [metrics, setMetrics] = useState<AgentMetrics | null>(null);
  const [agentStatus, setAgentStatus] = useState<AgentStatus[]>([]);
  const [healthCheck, setHealthCheck] = useState<HealthCheck | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [notifications, setNotifications] = useState<string[]>([]);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    loadDashboardData();
    
    if (autoRefresh) {
      const interval = setInterval(loadDashboardData, 10000); // Refresh every 10 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);

      // Load metrics
      const metricsResponse = await fetch('/api/agents/consultation?type=metrics');
      const metricsResult = await metricsResponse.json();
      
      if (metricsResult.success) {
        setMetrics(metricsResult.data.metrics);
      }

      // Load agent status
      const statusResponse = await fetch('/api/agents/consultation?type=status');
      const statusResult = await statusResponse.json();
      
      if (statusResult.success) {
        setAgentStatus(statusResult.data.agents);
        setHealthCheck(statusResult.data.health);
      }

    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      addNotification('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const addNotification = (message: string) => {
    setNotifications(prev => [message, ...prev.slice(0, 4)]);
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n !== message));
    }, 5000);
  };

  const clearMetrics = async () => {
    try {
      const response = await fetch('/api/agents/consultation', {
        method: 'DELETE'
      });
      
      const result = await response.json();
      
      if (result.success) {
        addNotification('Metrics and history cleared successfully');
        loadDashboardData();
      } else {
        addNotification('Failed to clear metrics');
      }
    } catch (error) {
      console.error('Failed to clear metrics:', error);
      addNotification('Failed to clear metrics');
    }
  };

  const getTabIcon = (tab: TabType) => {
    switch (tab) {
      case 'dashboard': return '🎛️';
      case 'metrics': return '📊';
      case 'tester': return '🧪';
      case 'history': return '📋';
      case 'monitor': return '📡';
      default: return '📋';
    }
  };

  const getHealthStatusColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'degraded': return 'text-yellow-600 bg-yellow-100';
      case 'unhealthy': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Agent Consultation System</h1>
              <p className="text-sm text-gray-600">
                Monitor and manage dynamic agent consultations for enhanced content generation
              </p>
            </div>

            <div className="flex items-center space-x-4">
              {/* System Health Indicator */}
              {healthCheck && (
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${getHealthStatusColor(healthCheck.overallHealth)}`}>
                  {healthCheck.overallHealth === 'healthy' && '✅ System Healthy'}
                  {healthCheck.overallHealth === 'degraded' && '⚠️ System Degraded'}
                  {healthCheck.overallHealth === 'unhealthy' && '❌ System Unhealthy'}
                </div>
              )}

              {/* Auto Refresh Toggle */}
              <label className="flex items-center space-x-2 text-sm">
                <input
                  type="checkbox"
                  checked={autoRefresh}
                  onChange={(e) => setAutoRefresh(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-gray-700">Auto Refresh</span>
              </label>

              {/* Refresh Button */}
              <button
                onClick={loadDashboardData}
                disabled={isLoading}
                className="flex items-center px-3 py-2 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50"
              >
                <svg className={`w-4 h-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                {isLoading ? 'Loading...' : 'Refresh'}
              </button>

              {/* Clear Metrics Button */}
              <button
                onClick={clearMetrics}
                className="flex items-center px-3 py-2 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Clear Data
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Notifications */}
      {notifications.length > 0 && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
          <div className="space-y-2">
            {notifications.map((notification, index) => (
              <div
                key={index}
                className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-800 animate-fade-in"
              >
                🔔 {notification}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {(['dashboard', 'metrics', 'tester', 'history', 'monitor'] as TabType[]).map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {getTabIcon(tab)} {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {activeTab === 'dashboard' && (
          <AgentStatusDashboard
            agentStatus={agentStatus}
            healthCheck={healthCheck}
            metrics={metrics}
            onRefresh={loadDashboardData}
          />
        )}

        {activeTab === 'metrics' && (
          <ConsultationMetrics
            metrics={metrics}
            agentStatus={agentStatus}
            onRefresh={loadDashboardData}
          />
        )}

        {activeTab === 'tester' && (
          <AgentSelectionTester
            onNotification={addNotification}
          />
        )}

        {activeTab === 'history' && (
          <ConsultationHistory
            onNotification={addNotification}
          />
        )}

        {activeTab === 'monitor' && (
          <RealTimeConsultationMonitor
            metrics={metrics}
            agentStatus={agentStatus}
            onNotification={addNotification}
          />
        )}
      </div>
    </div>
  );
}
