/**
 * Test Approval Page
 * Simple page to test the approval flow with a real artifact
 */

'use client';

import { useState } from 'react';

export default function TestApprovalPage() {
  const [artifactId, setArtifactId] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState('');

  const testApproval = async (approved: boolean) => {
    if (!artifactId.trim()) {
      setResult('Please enter an artifact ID');
      return;
    }

    setLoading(true);
    setResult('');

    try {
      const response = await fetch('/api/workflow/approval', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          artifactId: artifactId.trim(),
          approved,
          approver: 'test-user',
          feedback: approved ? 'Approved via test page' : 'Rejected via test page',
          reason: approved ? '' : 'Testing rejection flow'
        })
      });

      const data = await response.json();
      
      if (data.success) {
        setResult(`✅ ${approved ? 'Approved' : 'Rejected'} successfully! Workflow should ${approved ? 'continue' : 'stop'}.`);
      } else {
        setResult(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      setResult(`❌ Network error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const getLatestArtifacts = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/debug/artifacts');
      const data = await response.json();
      
      if (data.success && data.artifacts.length > 0) {
        const latest = data.artifacts[0];
        setArtifactId(latest.id);
        setResult(`Found latest artifact: ${latest.id} (${latest.type})`);
      } else {
        setResult('No artifacts found');
      }
    } catch (error) {
      setResult(`Error fetching artifacts: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">🧪 Test Approval Flow</h1>
          
          <div className="space-y-6">
            {/* Get Latest Artifacts */}
            <div>
              <button
                onClick={getLatestArtifacts}
                disabled={loading}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Loading...' : '🔍 Get Latest Artifact ID'}
              </button>
            </div>

            {/* Artifact ID Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Artifact ID to Test:
              </label>
              <input
                type="text"
                value={artifactId}
                onChange={(e) => setArtifactId(e.target.value)}
                placeholder="Enter artifact ID (e.g., 0079aaa6-a990-4d87-9b1c-aa9c9a027337)"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Test Buttons */}
            <div className="grid grid-cols-2 gap-4">
              <button
                onClick={() => testApproval(true)}
                disabled={loading || !artifactId.trim()}
                className="px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 font-medium"
              >
                {loading ? 'Testing...' : '✅ Test Approve'}
              </button>
              
              <button
                onClick={() => testApproval(false)}
                disabled={loading || !artifactId.trim()}
                className="px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 font-medium"
              >
                {loading ? 'Testing...' : '❌ Test Reject'}
              </button>
            </div>

            {/* Result */}
            {result && (
              <div className={`p-4 rounded-lg ${
                result.includes('✅') ? 'bg-green-50 text-green-800' :
                result.includes('❌') ? 'bg-red-50 text-red-800' :
                'bg-blue-50 text-blue-800'
              }`}>
                <pre className="whitespace-pre-wrap text-sm">{result}</pre>
              </div>
            )}

            {/* Instructions */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h3 className="font-medium text-yellow-800 mb-2">📋 How to Test:</h3>
              <ol className="text-sm text-yellow-700 space-y-1">
                <li>1. Click "Get Latest Artifact ID" to find a pending artifact</li>
                <li>2. Or copy an artifact ID from the terminal logs</li>
                <li>3. Click "Test Approve" or "Test Reject"</li>
                <li>4. Check the terminal to see if the workflow continues/stops</li>
                <li>5. Check the enhanced workflow page to see visual updates</li>
              </ol>
            </div>

            {/* Links */}
            <div className="flex space-x-4">
              <a
                href="/workflow/enhanced"
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                🔄 Enhanced Workflow
              </a>
              <a
                href={artifactId ? `/workflow/approval/${artifactId}` : '#'}
                className={`px-4 py-2 rounded-lg ${
                  artifactId 
                    ? 'bg-purple-600 text-white hover:bg-purple-700' 
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                👀 View Approval Page
              </a>
            </div>
          </div>
        </div>

        {/* Recent Artifact IDs from Terminal */}
        <div className="mt-6 bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">🔍 Recent Artifact IDs from Terminal:</h2>
          <div className="bg-gray-50 rounded p-4">
            <div className="text-sm text-gray-600 space-y-1">
              <div>• 0079aaa6-a990-4d87-9b1c-aa9c9a027337</div>
              <div>• 2e5901c8-aa7b-492a-b83e-b2cbbcdcc6fb</div>
              <div>• 14253308-0e98-4806-b011-1e59a27b8c14</div>
              <div>• 9ca164b1-b5e4-4a27-8f70-b9eb47102cec</div>
              <div>• 2d1542c6-13dc-4531-bc35-0abfb277d905</div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Copy any of these IDs to test the approval flow
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
