/**
 * Unified Workflow Experience Page
 * 
 * Single-page workflow experience with integrated agent collaboration
 * and seamless user flow from template selection to final results
 */

'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import UnifiedWorkflowExperience from '../../../components/Workflow/UnifiedWorkflowExperience';

export default function UnifiedWorkflowPage() {
  const router = useRouter();
  const [showDashboard, setShowDashboard] = useState(false);

  const handleWorkflowComplete = (executionId: string) => {
    console.log('Workflow completed:', executionId);
    // Could redirect to results page or show completion message
  };

  const handleBackToDashboard = () => {
    setShowDashboard(true);
  };

  if (showDashboard) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Dashboard Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">AI Workflow Dashboard</h1>
                <p className="text-sm text-gray-600">
                  Manage your intelligent content workflows
                </p>
              </div>
              <button
                onClick={() => setShowDashboard(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                🚀 Create New Workflow
              </button>
            </div>
          </div>
        </div>

        {/* Dashboard Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Quick Start Card */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-blue-600 text-xl">🚀</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Quick Start</h3>
                  <p className="text-sm text-gray-600">Create a new workflow</p>
                </div>
              </div>
              <p className="text-gray-600 text-sm mb-4">
                Get started with our intelligent workflow templates featuring agent collaboration.
              </p>
              <button
                onClick={() => setShowDashboard(false)}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Create Workflow
              </button>
            </div>

            {/* Agent Status Card */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <span className="text-green-600 text-xl">🤖</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Agent Status</h3>
                  <p className="text-sm text-gray-600">AI agents are ready</p>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">SEO Specialist</span>
                  <span className="text-green-600">● Online</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Market Research</span>
                  <span className="text-green-600">● Online</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Content Strategy</span>
                  <span className="text-green-600">● Online</span>
                </div>
              </div>
            </div>

            {/* Recent Activity Card */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <span className="text-purple-600 text-xl">📊</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
                  <p className="text-sm text-gray-600">Latest workflow runs</p>
                </div>
              </div>
              <div className="text-center py-4">
                <div className="text-gray-400 mb-2">
                  <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <p className="text-sm text-gray-600">No recent workflows</p>
                <p className="text-xs text-gray-500">Create your first workflow to get started</p>
              </div>
            </div>
          </div>

          {/* Features Overview */}
          <div className="mt-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">What You Can Do</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-blue-600 text-2xl">📝</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">SEO Blog Posts</h3>
                <p className="text-sm text-gray-600">
                  Create optimized blog content with keyword research and agent collaboration
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-green-600 text-2xl">🛒</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Product Descriptions</h3>
                <p className="text-sm text-gray-600">
                  Generate compelling product descriptions with market insights
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-purple-600 text-2xl">🤖</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Agent Collaboration</h3>
                <p className="text-sm text-gray-600">
                  Leverage AI agents for enhanced content quality and insights
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-yellow-600 text-2xl">👥</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Human Review</h3>
                <p className="text-sm text-gray-600">
                  Built-in review workflows for quality control and approval
                </p>
              </div>
            </div>
          </div>

          {/* Getting Started Guide */}
          <div className="mt-12 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-8 border border-blue-200">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">🎯 How It Works</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-600 text-white rounded-lg flex items-center justify-center mx-auto mb-3 font-bold">
                  1
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Choose Template</h3>
                <p className="text-sm text-gray-600">
                  Select from our collection of intelligent workflow templates
                </p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-blue-600 text-white rounded-lg flex items-center justify-center mx-auto mb-3 font-bold">
                  2
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Configure & Execute</h3>
                <p className="text-sm text-gray-600">
                  Provide your inputs and watch as agents collaborate to create content
                </p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-blue-600 text-white rounded-lg flex items-center justify-center mx-auto mb-3 font-bold">
                  3
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Review & Export</h3>
                <p className="text-sm text-gray-600">
                  Review the generated content and export your final results
                </p>
              </div>
            </div>

            <div className="text-center mt-6">
              <button
                onClick={() => setShowDashboard(false)}
                className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                🚀 Get Started Now
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <UnifiedWorkflowExperience
      onComplete={handleWorkflowComplete}
      onBack={handleBackToDashboard}
    />
  );
}
