/**
 * Artifact Approval Page
 * Standalone page for approving workflow artifacts
 */

'use client';

import { useParams, useRouter } from 'next/navigation';
import ArtifactApproval from '../../../../components/Workflow/ArtifactApproval';

export default function ArtifactApprovalPage() {
  const params = useParams();
  const router = useRouter();
  const artifactId = params.artifactId as string;

  const handleApprovalComplete = (artifactId: string, approved: boolean) => {
    // Show success message and optionally redirect
    const message = approved ? 'Artifact approved successfully!' : 'Artifact rejected.';
    alert(message);
    
    // Could redirect to workflow status page
    // router.push(`/workflow/status/${executionId}`);
  };

  if (!artifactId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Invalid Artifact ID</h1>
          <p className="text-gray-600">Please check the URL and try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <ArtifactApproval
        artifactId={artifactId}
        onApprovalComplete={handleApprovalComplete}
        currentUser="demo-user" // In a real app, this would come from authentication
      />
    </div>
  );
}
