/**
 * Unified Enhanced Workflow System
 * Single page combining workflow creation, visualization, review, and management
 */

'use client';

import { useState, useEffect } from 'react';
import WorkflowInterface from '../../../components/Workflow/WorkflowInterface';
import SimpleVisualWorkflow from '../../../components/Workflow/SimpleVisualWorkflow';
import ApprovalFlowSimpleExplanation from '../../../components/Workflow/ApprovalFlowSimpleExplanation';

type TabType = 'builder' | 'visual' | 'review' | 'history';

interface ExecutionStatus {
  id: string;
  workflowId: string;
  workflowName?: string;
  status: string;
  progress: number;
  currentStep?: string;
  startedAt: string;
  completedAt?: string;
  error?: any;
}

export default function EnhancedWorkflowPage() {
  const [activeTab, setActiveTab] = useState<TabType>('builder');
  const [currentExecutionId, setCurrentExecutionId] = useState<string | null>(null);
  const [workflowHistory, setWorkflowHistory] = useState<ExecutionStatus[]>([]);
  const [showApprovalHelp, setShowApprovalHelp] = useState(false);
  const [notifications, setNotifications] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadWorkflowHistory();
  }, []);

  const loadWorkflowHistory = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/workflow/history');
      const result = await response.json();

      if (result.success) {
        setWorkflowHistory(result.data.executions || []);
      } else {
        console.error('Failed to load workflow history:', result.error);
      }
    } catch (error) {
      console.error('Failed to load workflow history:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addNotification = (message: string) => {
    setNotifications(prev => [message, ...prev.slice(0, 4)]);
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n !== message));
    }, 5000);
  };

  const handleWorkflowCreated = (executionId: string) => {
    console.log(`🚀 Workflow created with execution ID: ${executionId}`);
    setCurrentExecutionId(executionId);
    setActiveTab('visual');
    addNotification(`Workflow execution started: ${executionId.slice(-8)}`);
    loadWorkflowHistory();
  };

  const handleWorkflowComplete = (executionId: string) => {
    console.log(`✅ Workflow completed: ${executionId}`);
    addNotification(`Workflow completed successfully: ${executionId.slice(-8)}`);
    loadWorkflowHistory();
  };

  const handleReviewCreated = (reviewId: string) => {
    console.log(`📝 Review created: ${reviewId}`);
    addNotification(`Review created: ${reviewId.slice(-8)}`);
  };

  const handleStepClick = (stepId: string, stepType?: string) => {
    console.log(`Step clicked: ${stepId}, type: ${stepType}`);
    if (stepType === 'approval') {
      addNotification(`Approval step clicked: ${stepId}`);
    }
  };

  const getTabIcon = (tab: TabType) => {
    switch (tab) {
      case 'builder': return '🔧';
      case 'visual': return '🎨';
      case 'review': return '👥';
      case 'history': return '📊';
      default: return '📋';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'running': return 'bg-blue-100 text-blue-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Enhanced Workflow System</h1>
              <p className="text-sm text-gray-600">
                Create, visualize, and manage content workflows with integrated review capabilities
              </p>
            </div>

            <div className="flex items-center space-x-4">
              {currentExecutionId && (
                <div className="text-sm text-blue-600">
                  Active: <span className="font-medium">{currentExecutionId.slice(-8)}</span>
                </div>
              )}

              <button
                onClick={() => setShowApprovalHelp(true)}
                className="flex items-center px-3 py-2 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Help
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Notifications */}
      {notifications.length > 0 && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
          <div className="space-y-2">
            {notifications.map((notification, index) => (
              <div
                key={index}
                className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-800 animate-fade-in"
              >
                🔔 {notification}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {(['builder', 'visual', 'review', 'history'] as TabType[]).map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {getTabIcon(tab)} {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {activeTab === 'builder' && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <WorkflowInterface
              onWorkflowCreated={handleWorkflowCreated}
              onWorkflowComplete={handleWorkflowComplete}
              onReviewCreated={handleReviewCreated}
            />
          </div>
        )}

        {activeTab === 'visual' && (
          <div className="bg-white rounded-lg shadow-lg" style={{ height: '70vh' }}>
            <SimpleVisualWorkflow
              workflowId={currentExecutionId}
              onStepClick={handleStepClick}
              onWorkflowUpdate={(steps) => console.log('Workflow updated:', steps.length, 'steps')}
            />
          </div>
        )}

        {activeTab === 'review' && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            {currentExecutionId ? (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Active Workflow Review</h3>
                    <p className="text-sm text-gray-600">Execution ID: {currentExecutionId}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium text-blue-700">Monitoring for Review Requests</span>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="font-semibold text-blue-900 mb-2">🔍 Review Status</h4>
                    <p className="text-sm text-blue-800 mb-3">
                      This workflow is being monitored for approval gates and review requests.
                      When the workflow reaches an approval step, you'll see the review interface here.
                    </p>
                    <div className="text-xs text-blue-700 bg-blue-100 px-3 py-2 rounded">
                      💡 Tip: Switch to the Visual tab to see real-time workflow progress and approval gates
                    </div>
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h4 className="font-semibold text-yellow-900 mb-2">⏳ Waiting for Approval Gates</h4>
                    <p className="text-sm text-yellow-800 mb-2">
                      No approval gates are currently active for this workflow.
                    </p>
                    <ul className="text-xs text-yellow-700 space-y-1">
                      <li>• Approval gates will appear here when the workflow reaches them</li>
                      <li>• You'll be able to review artifacts and approve/reject them</li>
                      <li>• The workflow will automatically continue after approval</li>
                    </ul>
                  </div>

                  <div className="flex space-x-3">
                    <button
                      onClick={() => setActiveTab('visual')}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      View Workflow Progress
                    </button>
                    <button
                      onClick={() => setActiveTab('history')}
                      className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                    >
                      View History
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center">
                <div className="text-gray-400 mb-4">
                  <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Active Workflow</h3>
                <p className="text-gray-600 mb-4">
                  Create a workflow to see review and approval options here.
                </p>
                <div className="space-y-4">
                  <div className="text-left bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-blue-900 mb-2">How Approval Gates Work:</h4>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• Create a workflow with approval steps (e.g., "SEO Blog Post with Approval")</li>
                      <li>• Workflow will pause at approval gates and create artifacts for review</li>
                      <li>• You'll receive an approval URL to review and approve/reject the artifact</li>
                      <li>• Once approved, the workflow continues automatically</li>
                    </ul>
                  </div>
                  <button
                    onClick={() => setActiveTab('builder')}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Create Workflow with Approval Gates
                  </button>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'history' && (
          <div className="bg-white rounded-lg shadow-lg">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">Workflow History</h2>
                  <p className="text-gray-600 mt-1">View and manage your workflow executions</p>
                </div>
                <button
                  onClick={loadWorkflowHistory}
                  disabled={isLoading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                >
                  {isLoading ? 'Loading...' : 'Refresh'}
                </button>
              </div>
            </div>

            <div className="p-6">
              {workflowHistory.length > 0 ? (
                <div className="space-y-4">
                  {workflowHistory.map((execution) => (
                    <div key={execution.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-3">
                          <div className="font-medium text-gray-900">
                            {execution.workflowName || `Workflow ${execution.workflowId}`}
                          </div>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(execution.status)}`}>
                            {execution.status}
                          </span>
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(execution.startedAt).toLocaleDateString()}
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                        <div>
                          <span className="font-medium">Progress:</span> {execution.progress}%
                        </div>
                        {execution.currentStep && (
                          <div>
                            <span className="font-medium">Current Step:</span> {execution.currentStep}
                          </div>
                        )}
                        {execution.completedAt && (
                          <div>
                            <span className="font-medium">Completed:</span> {new Date(execution.completedAt).toLocaleString()}
                          </div>
                        )}
                      </div>

                      {execution.error && (
                        <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded">
                          <div className="text-sm font-medium text-red-800">Error:</div>
                          <div className="text-sm text-red-600 mt-1">{execution.error.message}</div>
                        </div>
                      )}

                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            setCurrentExecutionId(execution.id);
                            setActiveTab('visual');
                          }}
                          className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                        >
                          View Visual
                        </button>
                        {execution.status === 'completed' && (
                          <a
                            href={`/workflow/results/${execution.id}`}
                            className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors"
                          >
                            View Results
                          </a>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="text-gray-400 mb-4">
                    <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Workflows Yet</h3>
                  <p className="text-gray-600 mb-4">
                    Create your first workflow to see execution history here.
                  </p>
                  <button
                    onClick={() => setActiveTab('builder')}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Create First Workflow
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Help Modal */}
      <ApprovalFlowSimpleExplanation
        isOpen={showApprovalHelp}
        onClose={() => setShowApprovalHelp(false)}
      />
    </div>
  );
}
