// src/app/(payload)/api/automated-research/route.ts
import { NextResponse } from 'next/server';
import configPromise from '@payload-config';
import { getPayload } from 'payload';
import { executeResearch } from '../agents/research';
import type { Product, Seller, ProductFeature, PricingPlan, Faq } from '../../../../payload-types';

// Request interfaces
interface AutomatedResearchRequest {
  productName: string;
  category: string;
  generateSeller: boolean;
  sellerName: string;
  existingSeller: string | null;
  researchDepth: 'basic' | 'standard' | 'comprehensive';
  generateFeatures: boolean;
  generatePricing: boolean;
  generateFAQs: boolean;
  preview: boolean; // Whether to only preview or actually save to database
}

// Main API handler
export async function POST(request: Request) {
  try {
    // Parse request
    const data: AutomatedResearchRequest = await request.json();
    const { 
      productName, 
      category, 
      generateSeller, 
      sellerName, 
      existingSeller,
      researchDepth,
      generateFeatures,
      generatePricing,
      generateFAQs,
      preview 
    } = data;

    // Validate request
    if (!productName) {
      return NextResponse.json({ success: false, error: 'Product name is required' }, { status: 400 });
    }

    if (generateSeller && !existingSeller && !sellerName) {
      return NextResponse.json(
        { success: false, error: 'Seller name is required when generating a new seller' }, 
        { status: 400 }
      );
    }

    // Get Payload CMS instance
    const payload = await getPayload({
      config: configPromise,
    });

    // First check if the product already exists
    const existingProducts = await payload.find({
      collection: 'products',
      where: {
        name: {
          equals: productName
        }
      }
    });

    if (existingProducts.docs.length > 0) {
      return NextResponse.json(
        { success: false, error: `A product named "${productName}" already exists` },
        { status: 400 }
      );
    }

    // Execute research using LangGraph agent
    const researchResults = await executeResearch({
      productName,
      category,
      generateSeller,
      sellerName,
      existingSeller,
      researchDepth,
      generateFeatures,
      generatePricing,
      generateFAQs,
      preview
    });

    // Handle any errors from the research process
    if (!researchResults.success) {
      return NextResponse.json({ 
        success: false, 
        error: researchResults.error || 'Research process failed'
      }, { status: 500 });
    }

    // If this is just a preview request, return the generated content without saving
    if (preview) {
      return NextResponse.json({
        success: true,
        message: 'Content generated successfully',
        preview: researchResults.preview
      });
    }

    // If not preview, save all content to the database
    let productId = null;
    let sellerId = null;
    
    // Step 1: Create seller first if needed
    if (generateSeller && !existingSeller && researchResults.preview.seller) {
      try {
        const sellerResponse = await payload.create({
          collection: 'sellers',
          data: researchResults.preview.seller as any
        });
        
        sellerId = sellerResponse.id;
      } catch (err) {
        console.error('Error creating seller:', err);
        return NextResponse.json({ 
          success: false, 
          error: 'Failed to create seller' 
        }, { status: 500 });
      }
    } else if (existingSeller) {
      sellerId = existingSeller;
    }

    // Step 2: Create the product
    try {
      const productData = {
        ...researchResults.preview.product,
        seller: sellerId
      };
      
      // Create a placeholder logo (in real implementation, this would need to be handled)
      // For now, use the first media item as a placeholder
      const mediaItems = await payload.find({ 
        collection: 'media',
        limit: 1
      });
      
      if (mediaItems.docs.length > 0) {
        productData.logo = mediaItems.docs[0].id;
      } else {
        // If no media items, return error
        return NextResponse.json({ 
          success: false, 
          error: 'No media items found. Please upload at least one logo image to the media collection.' 
        }, { status: 400 });
      }
      
      const productResponse = await payload.create({
        collection: 'products',
        data: productData as any
      });
      
      productId = productResponse.id;
    } catch (err) {
      console.error('Error creating product:', err);
      return NextResponse.json({ 
        success: false, 
        error: 'Failed to create product' 
      }, { status: 500 });
    }

    // Step 3: Create features if any
    if (generateFeatures && researchResults.preview.features?.length > 0 && productId) {
      for (const feature of researchResults.preview.features) {
        try {
          await payload.create({
            collection: 'product-features',
            data: {
              ...feature,
              product: productId
            } as any
          });
        } catch (err) {
          console.error('Error creating feature:', err);
          // Continue with other features even if one fails
        }
      }
    }

    // Step 4: Create pricing plans if any
    if (generatePricing && researchResults.preview.pricingPlans?.length > 0 && productId) {
      for (const plan of researchResults.preview.pricingPlans) {
        try {
          await payload.create({
            collection: 'pricing-plans',
            data: {
              ...plan,
              product: productId
            } as any
          });
        } catch (err) {
          console.error('Error creating pricing plan:', err);
          // Continue with other plans even if one fails
        }
      }
    }

    // Step 5: Create FAQs if any
    if (generateFAQs && researchResults.preview.faqs?.length > 0 && productId) {
      for (const faq of researchResults.preview.faqs) {
        try {
          await payload.create({
            collection: 'faqs',
            data: {
              ...faq,
              product: productId
            } as any
          });
        } catch (err) {
          console.error('Error creating FAQ:', err);
          // Continue with other FAQs even if one fails
        }
      }
    }

    // Return success response with IDs
    return NextResponse.json({
      success: true,
      message: 'Content generated and saved successfully',
      data: {
        productId,
        sellerId
      }
    });

  } catch (error) {
    console.error('Error in automated research:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      }, 
      { status: 500 }
    );
  }
}