// src/app/(payload)/api/utils/logger.ts

/**
 * Advanced logging utility for the API system
 * Provides structured, level-based logging with timestamps and context
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogContext {
  [key: string]: any;
}

// Enable different log levels based on environment
const LOG_LEVEL = process.env.NODE_ENV === 'production' ? 'info' : 'debug';
const LOG_LEVELS: Record<LogLevel, number> = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3
};

/**
 * Formats a log message with contextual information
 */
function formatLogMessage(level: LogLevel, message: string, context: LogContext = {}): string {
  const timestamp = new Date().toISOString();
  
  // Stringify any additional context
  const contextStr = Object.keys(context).length > 0 
    ? ` | Context: ${JSON.stringify(context)}` 
    : '';

  return `${timestamp} [${level.toUpperCase()}] ${message}${contextStr}`;
}

/**
 * Checks if the message should be logged based on current log level
 */
function shouldLog(level: LogLevel): boolean {
  return LOG_LEVELS[level] >= LOG_LEVELS[LOG_LEVEL as LogLevel];
}

/**
 * Core logging function
 */
function log(level: LogLevel, message: string, context: LogContext = {}): void {
  if (!shouldLog(level)) return;

  const formattedMessage = formatLogMessage(level, message, context);
  
  switch (level) {
    case 'debug':
      console.debug(formattedMessage);
      break;
    case 'info':
      console.info(formattedMessage);
      break;
    case 'warn':
      console.warn(formattedMessage);
      break;
    case 'error':
      console.error(formattedMessage);
      break;
  }
}

/**
 * Debug level logging
 */
function debug(message: string, context: LogContext = {}): void {
  log('debug', message, context);
}

/**
 * Info level logging
 */
function info(message: string, context: LogContext = {}): void {
  log('info', message, context);
}

/**
 * Warning level logging
 */
function warn(message: string, context: LogContext = {}): void {
  log('warn', message, context);
}

/**
 * Error level logging
 */
function error(message: string, context: LogContext = {}): void {
  log('error', message, context);
}

export const logger = {
  debug,
  info,
  warn,
  error
};

export default logger;
