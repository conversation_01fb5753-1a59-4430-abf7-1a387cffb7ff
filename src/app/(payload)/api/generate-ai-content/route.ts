// src/app/api/generate-ai-content/route.ts
import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request: Request) {
  try {
    // Parse request body
    const { fieldName, fieldType, currentValue, userPrompt, collectionSlug } = await request.json();
    
    if (!fieldName) {
      return NextResponse.json({ error: 'Field name is required' }, { status: 400 });
    }
    
    // Use provided prompt or create a default one
    let promptToUse = userPrompt;
    
    if (!promptToUse) {
      // Build a context-aware default prompt
      let defaultPrompt = `Generate high-quality content for the "${fieldName}" field`;
      
      if (collectionSlug) {
        defaultPrompt += ` in the ${collectionSlug} collection`;
      }
      
      defaultPrompt += '.\n\n';
      
      if (fieldType === 'textarea' || fieldType === 'richText') {
        defaultPrompt += 'Provide detailed, well-structured content with paragraphs and proper formatting. ';
      } else if (fieldType === 'text') {
        defaultPrompt += 'Provide concise, compelling text that fits in a single field. ';
      }
      
      if (currentValue) {
        defaultPrompt += `\n\nCurrent content for reference:\n${currentValue}`;
      }
      
      promptToUse = defaultPrompt;
    }

    console.log('Using prompt:', promptToUse);

    // Call OpenAI API to generate content
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        { 
          role: "system", 
          content: "You are a helpful assistant that generates high-quality content for a CMS."
        },
        { 
          role: "user", 
          content: promptToUse
        }
      ],
      temperature: 0.7,
      max_tokens: fieldType === 'text' ? 100 : 800,
    });

    const generatedContent = completion.choices[0].message.content;
    
    // For debugging
    console.log("Generated content length:", generatedContent?.length);
    
    // Handle richText fields (for future support)
    if (fieldType === 'richText') {
      // Convert the plain text to a basic Lexical format
      const lexicalFormat = {
        root: {
          children: generatedContent?.split('\n\n').map(paragraph => ({
            type: 'paragraph',
            children: [{ text: paragraph }],
          })) || [],
          direction: null,
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        }
      };
      
      return NextResponse.json({ 
        generatedContent: lexicalFormat,
        originalText: generatedContent
      });
    }
    
    // Return the generated content
    return NextResponse.json({ generatedContent });
    
  } catch (error) {
    console.error('Error in AI content generation:', error);
    return NextResponse.json(
      { error: 'Error generating content', details: error instanceof Error ? error.message : 'Unknown error' }, 
      { status: 500 }
    );
  }
}