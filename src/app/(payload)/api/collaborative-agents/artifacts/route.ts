// src/app/(payload)/api/collaborative-agents/artifacts/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { stateStore } from '../../agents/collaborative-iteration/server-based';
import logger from '../../agents/collaborative-iteration/utils/logger';

/**
 * GET handler - Get all artifacts for a session or a specific artifact by ID
 * 
 * This endpoint is specifically designed to retrieve artifacts from sessions
 * to ensure reliable access to artifact data regardless of state structure.
 * 
 * Query parameters:
 * - sessionId: The ID of the session (required)
 * - artifactId: Optional ID of a specific artifact to retrieve
 */
export async function GET(request: NextRequest) {
  // Get parameters from the URL
  const searchParams = request.nextUrl.searchParams;
  const sessionId = searchParams.get('sessionId');
  const artifactId = searchParams.get('artifactId');
  
  if (!sessionId) {
    return NextResponse.json(
      { error: 'Session ID is required' },
      { status: 400 }
    );
  }
  
  // Get the session state
  const state = await stateStore.getState(sessionId);
  
  if (!state) {
    return NextResponse.json(
      { error: 'Session not found' },
      { status: 404 }
    );
  }
  
  // Initialize artifacts data structure
  const artifactsResult: {
    artifacts: any[];
    count: number;
    message: string;
  } = {
    artifacts: [],
    count: 0,
    message: ''
  };
  
  try {
    // Check if artifacts property exists
    if (!state.artifacts) {
      logger.info(`No artifacts found in session ${sessionId} state`);
      artifactsResult.message = 'No artifacts found in session state';
      return NextResponse.json(artifactsResult);
    }
    
    logger.info(`Found artifacts property in session ${sessionId}`);
    
    // Handle specific artifact request
    if (artifactId) {
      if (typeof state.artifacts === 'object' && !Array.isArray(state.artifacts)) {
        // Object/map structure (Record<string, IterativeArtifact>)
        const artifact = state.artifacts[artifactId];
        
        if (artifact) {
          artifactsResult.artifacts = [artifact];
          artifactsResult.count = 1;
          artifactsResult.message = 'Artifact retrieved successfully';
          
          logger.info(`Retrieved specific artifact ${artifactId} from session ${sessionId}`);
        } else {
          artifactsResult.message = `Artifact with ID ${artifactId} not found`;
          
          logger.info(`Artifact ${artifactId} not found in session ${sessionId}`);
        }
      } else if (Array.isArray(state.artifacts)) {
        // Array structure
        const artifact = state.artifacts.find(a => a.id === artifactId);
        
        if (artifact) {
          artifactsResult.artifacts = [artifact];
          artifactsResult.count = 1;
          artifactsResult.message = 'Artifact retrieved successfully';
          
          logger.info(`Retrieved specific artifact ${artifactId} from session ${sessionId} array`);
        } else {
          artifactsResult.message = `Artifact with ID ${artifactId} not found in array`;
          
          logger.info(`Artifact ${artifactId} not found in session ${sessionId} array`);
        }
      }
    } else {
      // Return all artifacts
      if (typeof state.artifacts === 'object' && !Array.isArray(state.artifacts)) {
        // Object/map structure (Record<string, IterativeArtifact>)
        artifactsResult.artifacts = Object.values(state.artifacts);
        artifactsResult.count = artifactsResult.artifacts.length;
        artifactsResult.message = `Retrieved ${artifactsResult.count} artifacts from session`;
        
        logger.info(`Retrieved ${artifactsResult.count} artifacts from session ${sessionId} map`);
      } else if (Array.isArray(state.artifacts)) {
        // Array structure
        artifactsResult.artifacts = state.artifacts;
        artifactsResult.count = artifactsResult.artifacts.length;
        artifactsResult.message = `Retrieved ${artifactsResult.count} artifacts from session array`;
        
        logger.info(`Retrieved ${artifactsResult.count} artifacts from session ${sessionId} array`);
      }
    }
    
    // If we still didn't find artifacts, check agent states for references
    if (artifactsResult.count === 0 && state.agentStates) {
      logger.info(`No artifacts found directly, checking agent states for ${sessionId}`);
      
      const allArtifactReferences: string[] = [];
      
      // Gather all artifact references from agent states
      Object.values(state.agentStates).forEach(agentState => {
        if (agentState && agentState.generatedArtifacts && Array.isArray(agentState.generatedArtifacts)) {
          allArtifactReferences.push(...agentState.generatedArtifacts);
        }
      });
      
      if (allArtifactReferences.length > 0) {
        const uniqueReferences = [...new Set(allArtifactReferences)];
        logger.info(`Found ${uniqueReferences.length} unique artifact references in agent states`);
        
        artifactsResult.message = `Found ${uniqueReferences.length} artifact references, but artifacts data is not properly stored`;
      }
    }
    
    // Log artifact retrieval details
    logger.debug('Artifacts response payload', {
      sessionId,
      count: artifactsResult.count,
      message: artifactsResult.message
    });
    
    return NextResponse.json(artifactsResult);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error(`Error retrieving artifacts: ${errorMessage}`, { sessionId, artifactId });
    
    return NextResponse.json(
      { error: `Failed to retrieve artifacts: ${errorMessage}` },
      { status: 500 }
    );
  }
}
