/**
 * Collaborative Agents Feedback API Route
 *
 * This file redirects to the new workflow-orchestrator endpoint.
 * It exists only for backward compatibility and will be removed in a future release.
 */

import { NextRequest, NextResponse } from 'next/server';

/**
 * POST handler - Redirects to the new workflow-orchestrator endpoint
 */
export async function POST(request: NextRequest) {
  console.warn('The /api/collaborative-agents/feedback endpoint is deprecated. Please use /api/workflow-orchestrator instead.');

  // Get the request body
  const body = await request.json();

  // Validate required fields
  const { sessionId, feedback, from = 'user', artifactId } = body;

  if (!sessionId || !feedback) {
    return NextResponse.json({ error: 'Session ID and feedback are required' }, { status: 400 });
  }

  // Map the old format to the new format
  const newBody = {
    action: 'sendFeedback',
    sessionId,
    feedback,
    from,
    artifactId
  };

  // Redirect to the new endpoint
  const response = await fetch(new URL('/api/workflow-orchestrator', request.url), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(newBody)
  });

  // Return the response
  const data = await response.json();
  return NextResponse.json(data, { status: response.status });
}
