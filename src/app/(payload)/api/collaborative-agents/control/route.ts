/**
 * Collaborative Agents Control API Route
 *
 * This file redirects to the new workflow-orchestrator endpoint.
 * It exists only for backward compatibility and will be removed in a future release.
 */

import { NextRequest, NextResponse } from 'next/server';

/**
 * PUT handler - Redirects to the new workflow-orchestrator endpoint
 */
export async function PUT(request: NextRequest) {
  console.warn('The /api/collaborative-agents/control endpoint is deprecated. Please use /api/workflow-orchestrator instead.');

  // Get the URL parameters
  const url = new URL(request.url);
  const searchParams = url.searchParams;

  // Extract the action and sessionId
  const action = searchParams.get('action');
  const sessionId = searchParams.get('sessionId');

  if (!action || !sessionId) {
    return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
  }

  // Map the old action to the new format
  const body = {
    action: 'transitionPhase',
    sessionId,
    phase: action
  };

  // Redirect to the new endpoint
  const response = await fetch(new URL('/api/workflow-orchestrator', request.url), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(body)
  });

  // Return the response
  const data = await response.json();
  return NextResponse.json(data, { status: response.status });
}
