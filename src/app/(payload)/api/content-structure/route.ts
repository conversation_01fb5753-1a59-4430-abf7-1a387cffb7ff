// src/app/(payload)/api/content-structure/route.ts
import { NextResponse } from 'next/server';
import configPromise from '@payload-config';
import { getPayload } from 'payload';
import { 
  generateContentStructure, 
  ContentStructure, 
  ContentStructureProposal, 
  ContentStructureFeedback,
  ContentStructureNegotiation,
  initializeContentStructureNegotiation,
  addProposalToNegotiation,
  addFeedbackToNegotiation,
  completeNegotiation,
  negotiateFinalContentStructure
} from '../agents/dynamicContentStructure';
import { createOrUpdateTask, getTask, updateTaskStatus, addArtifactToTask, createTextMessage, createDataMessage, createTextArtifact } from '../agents/a2aimplementation';
import { A2ATask, TaskState } from '../agents/a2atypes';
import { ContentGenerationState } from '../agents/contentGeneration';

// Request interfaces
interface ContentStructureGenerationRequest {
  contentType: 'product-page' | 'blog-article' | 'buying-guide';
  topicFocus: string;
  targetAudience: string;
  primaryKeywords: string[];
  tonePreference: string;
  marketResearchInsights?: any;
  seoKeywordInsights?: any;
}

interface ContentStructureProposalRequest {
  taskId: string;
  agentId: string;
  agentName: string;
  agentSpecialty: string;
  contentType: 'product-page' | 'blog-article' | 'buying-guide';
  topicFocus: string;
  targetAudience: string;
  primaryKeywords: string[];
  tonePreference: string;
  marketResearchInsights?: any;
  seoKeywordInsights?: any;
}

interface ContentStructureFeedbackRequest {
  taskId: string;
  fromAgentId: string;
  fromAgentName: string;
  toAgentId: string;
  proposalId: string;
  agentSpecialty: string;
}

interface ContentStructureNegotiationRequest {
  taskId: string;
}

// Main API handler for POST requests
export async function POST(request: Request) {
  try {
    // Parse request
    const data = await request.json();
    const action = request.headers.get('x-action');
    
    // Get Payload CMS instance
    const payload = await getPayload({
      config: configPromise,
    });
    
    // Handle different actions based on the x-action header
    switch (action) {
      case 'generate':
        return handleGenerateContentStructure(data);
      case 'propose':
        return handleContentStructureProposal(data);
      case 'feedback':
        return handleContentStructureFeedback(data);
      case 'negotiate':
        return handleContentStructureNegotiation(data);
      case 'get-task':
        return handleGetTask(data);
      default:
        return NextResponse.json({ success: false, error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in content structure API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      }, 
      { status: 500 }
    );
  }
}

// Handle GET requests to retrieve content structure tasks
export async function GET(request: Request) {
  try {
    // Get the task ID from the URL query parameters
    const { searchParams } = new URL(request.url);
    const taskId = searchParams.get('taskId');
    
    if (!taskId) {
      return NextResponse.json({ success: false, error: 'Task ID is required' }, { status: 400 });
    }
    
    // Create a dummy state to use with getTask
    const dummyState: ContentGenerationState = {
      contentType: 'blog-article',
      topicFocus: '',
      a2aTasks: {}
    };
    
    // Get the task
    const task = getTask(dummyState, { id: taskId });
    
    if (!task) {
      return NextResponse.json({ success: false, error: 'Task not found' }, { status: 404 });
    }
    
    return NextResponse.json({
      success: true,
      task
    });
  } catch (error) {
    console.error('Error getting content structure task:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      }, 
      { status: 500 }
    );
  }
}

// Handler for generating a content structure
async function handleGenerateContentStructure(data: ContentStructureGenerationRequest) {
  try {
    // Validate request
    if (!data.topicFocus) {
      return NextResponse.json({ success: false, error: 'Topic focus is required' }, { status: 400 });
    }
    
    // Generate the content structure
    const contentStructure = await generateContentStructure(
      data.contentType,
      data.topicFocus,
      data.targetAudience,
      data.primaryKeywords,
      data.tonePreference,
      data.marketResearchInsights,
      data.seoKeywordInsights
    );
    
    // Create a dummy state to use with createOrUpdateTask
    const dummyState: ContentGenerationState = {
      contentType: data.contentType,
      topicFocus: data.topicFocus,
      a2aTasks: {}
    };
    
    // Create a new task for this content structure generation
    const { state, task } = createOrUpdateTask(dummyState, {
      id: '',
      sessionId: '',
      message: createDataMessage('user', {
        action: 'generate-content-structure',
        contentType: data.contentType,
        topicFocus: data.topicFocus
      })
    });
    
    // Add the content structure as an artifact to the task
    const artifact = createTextArtifact(
      JSON.stringify(contentStructure, null, 2),
      'Content Structure'
    );
    
    const { task: updatedTask } = addArtifactToTask(state, task.id, artifact);
    
    // Update the task status to completed
    const { task: completedTask } = updateTaskStatus(
      state,
      task.id,
      'completed',
      createTextMessage('agent', 'Content structure generated successfully')
    );
    
    return NextResponse.json({
      success: true,
      message: 'Content structure generated successfully',
      contentStructure,
      taskId: completedTask?.id
    });
  } catch (error) {
    console.error('Error generating content structure:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      }, 
      { status: 500 }
    );
  }
}

// Handler for content structure proposals
async function handleContentStructureProposal(data: ContentStructureProposalRequest) {
  try {
    // Validate request
    if (!data.taskId) {
      return NextResponse.json({ success: false, error: 'Task ID is required' }, { status: 400 });
    }
    
    if (!data.agentId || !data.agentName || !data.agentSpecialty) {
      return NextResponse.json({ success: false, error: 'Agent information is required' }, { status: 400 });
    }
    
    // Create a dummy state to use with getTask
    const dummyState: ContentGenerationState = {
      contentType: data.contentType,
      topicFocus: data.topicFocus,
      a2aTasks: {}
    };
    
    // Get the task
    const task = getTask(dummyState, { id: data.taskId });
    
    if (!task) {
      return NextResponse.json({ success: false, error: 'Task not found' }, { status: 404 });
    }
    
    // Generate the content structure proposal
    const proposal = await generateContentStructureProposal(
      data.agentId,
      data.agentName,
      data.contentType,
      data.topicFocus,
      data.targetAudience,
      data.primaryKeywords,
      data.tonePreference,
      data.agentSpecialty,
      data.marketResearchInsights,
      data.seoKeywordInsights
    );
    
    // Add the proposal to the task
    const artifact = createTextArtifact(
      JSON.stringify(proposal, null, 2),
      `Content Structure Proposal from ${data.agentName}`
    );
    
    const { task: updatedTask } = addArtifactToTask(dummyState, task.id, artifact);
    
    return NextResponse.json({
      success: true,
      message: 'Content structure proposal submitted successfully',
      proposal,
      taskId: updatedTask?.id
    });
  } catch (error) {
    console.error('Error submitting content structure proposal:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      }, 
      { status: 500 }
    );
  }
}

// Handler for content structure feedback
async function handleContentStructureFeedback(data: ContentStructureFeedbackRequest) {
  try {
    // Validate request
    if (!data.taskId) {
      return NextResponse.json({ success: false, error: 'Task ID is required' }, { status: 400 });
    }
    
    if (!data.fromAgentId || !data.fromAgentName || !data.toAgentId || !data.proposalId) {
      return NextResponse.json({ success: false, error: 'Agent and proposal information is required' }, { status: 400 });
    }
    
    // Create a dummy state to use with getTask
    const dummyState: ContentGenerationState = {
      contentType: 'blog-article',
      topicFocus: '',
      a2aTasks: {}
    };
    
    // Get the task
    const task = getTask(dummyState, { id: data.taskId });
    
    if (!task) {
      return NextResponse.json({ success: false, error: 'Task not found' }, { status: 404 });
    }
    
    // Find the proposal in the task artifacts
    const proposalArtifact = task.artifacts?.find(artifact => 
      artifact.metadata?.proposalId === data.proposalId || 
      artifact.name?.includes(data.toAgentId)
    );
    
    if (!proposalArtifact) {
      return NextResponse.json({ success: false, error: 'Proposal not found' }, { status: 404 });
    }
    
    // Parse the proposal from the artifact
    const proposalText = proposalArtifact.parts.find(part => part.type === 'text')?.text;
    if (!proposalText) {
      return NextResponse.json({ success: false, error: 'Invalid proposal format' }, { status: 400 });
    }
    
    const proposal = JSON.parse(proposalText) as ContentStructureProposal;
    
    // Generate feedback on the proposal
    const feedback = await generateContentStructureFeedback(
      data.fromAgentId,
      data.fromAgentName,
      data.toAgentId,
      data.proposalId,
      proposal,
      data.agentSpecialty
    );
    
    // Add the feedback to the task
    const artifact = createTextArtifact(
      JSON.stringify(feedback, null, 2),
      `Feedback from ${data.fromAgentName} to ${data.toAgentId}`
    );
    
    const { task: updatedTask } = addArtifactToTask(dummyState, task.id, artifact);
    
    return NextResponse.json({
      success: true,
      message: 'Content structure feedback submitted successfully',
      feedback,
      taskId: updatedTask?.id
    });
  } catch (error) {
    console.error('Error submitting content structure feedback:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      }, 
      { status: 500 }
    );
  }
}

// Handler for content structure negotiation
async function handleContentStructureNegotiation(data: ContentStructureNegotiationRequest) {
  try {
    // Validate request
    if (!data.taskId) {
      return NextResponse.json({ success: false, error: 'Task ID is required' }, { status: 400 });
    }
    
    // Create a dummy state to use with getTask
    const dummyState: ContentGenerationState = {
      contentType: 'blog-article',
      topicFocus: '',
      a2aTasks: {}
    };
    
    // Get the task
    const task = getTask(dummyState, { id: data.taskId });
    
    if (!task) {
      return NextResponse.json({ success: false, error: 'Task not found' }, { status: 404 });
    }
    
    // Extract proposals and feedback from task artifacts
    const proposals: ContentStructureProposal[] = [];
    const feedbacks: ContentStructureFeedback[] = [];
    
    task.artifacts?.forEach(artifact => {
      const textPart = artifact.parts.find(part => part.type === 'text');
      if (textPart && textPart.text) {
        try {
          const content = JSON.parse(textPart.text);
          
          if (content.structure && content.agentId && content.reasoning) {
            // This is a proposal
            proposals.push(content as ContentStructureProposal);
          } else if (content.fromAgentId && content.toAgentId && content.feedback) {
            // This is feedback
            feedbacks.push(content as ContentStructureFeedback);
          }
        } catch (error) {
          console.warn('Could not parse artifact content:', error);
        }
      }
    });
    
    // Initialize negotiation
    const negotiation: ContentStructureNegotiation = initializeContentStructureNegotiation();
    
    // Add proposals and feedback to negotiation
    proposals.forEach(proposal => {
      addProposalToNegotiation(negotiation, proposal);
    });
    
    feedbacks.forEach(feedback => {
      addFeedbackToNegotiation(negotiation, feedback);
    });
    
    // Negotiate final content structure
    const finalStructure = await negotiateFinalContentStructure(negotiation);
    
    // Complete the negotiation
    const completedNegotiation = completeNegotiation(negotiation, finalStructure);
    
    // Add the final structure to the task
    const artifact = createTextArtifact(
      JSON.stringify(finalStructure, null, 2),
      'Final Content Structure'
    );
    
    const { task: updatedTask } = addArtifactToTask(dummyState, task.id, artifact);
    
    // Update the task status to completed
    const { task: completedTask } = updateTaskStatus(
      dummyState,
      task.id,
      'completed',
      createTextMessage('agent', 'Content structure negotiation completed successfully')
    );
    
    return NextResponse.json({
      success: true,
      message: 'Content structure negotiation completed successfully',
      negotiation: completedNegotiation,
      finalStructure,
      taskId: completedTask?.id
    });
  } catch (error) {
    console.error('Error in content structure negotiation:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      }, 
      { status: 500 }
    );
  }
}

// Handler for getting a task
async function handleGetTask(data: { taskId: string }) {
  try {
    // Validate request
    if (!data.taskId) {
      return NextResponse.json({ success: false, error: 'Task ID is required' }, { status: 400 });
    }
    
    // Create a dummy state to use with getTask
    const dummyState: ContentGenerationState = {
      contentType: 'blog-article',
      topicFocus: '',
      a2aTasks: {}
    };
    
    // Get the task
    const task = getTask(dummyState, { id: data.taskId });
    
    if (!task) {
      return NextResponse.json({ success: false, error: 'Task not found' }, { status: 404 });
    }
    
    return NextResponse.json({
      success: true,
      task
    });
  } catch (error) {
    console.error('Error getting task:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      }, 
      { status: 500 }
    );
  }
}