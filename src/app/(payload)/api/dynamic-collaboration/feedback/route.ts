/**
 * Dynamic Collaboration Feedback API Route
 * 
 * This file handles API requests for feedback operations in the dynamic collaboration system.
 */

import { NextRequest, NextResponse } from 'next/server';
import { FeedbackLoopSystem } from '../../agents/dynamic-collaboration-v2/feedback-loop-system';
import logger from '../../agents/collaborative-iteration/utils/logger';

/**
 * POST handler for feedback operations
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const body = await req.json();
    const { 
      sessionId, 
      action, 
      fromAgent, 
      toAgent, 
      artifactId, 
      feedback, 
      requestId,
      feedbackIds,
      changes,
      specificAreas
    } = body;
    
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing sessionId parameter' },
        { status: 400 }
      );
    }
    
    if (!action) {
      return NextResponse.json(
        { error: 'Missing action parameter (request, provide, incorporate)' },
        { status: 400 }
      );
    }
    
    // Get the feedback loop system
    const feedbackSystem = new FeedbackLoopSystem(sessionId);
    
    // Handle different actions
    switch (action) {
      case 'request':
        // Validate required parameters for requesting feedback
        if (!fromAgent || !toAgent || !artifactId) {
          return NextResponse.json(
            { 
              error: 'Missing required parameters for feedback request',
              requiredParams: ['fromAgent', 'toAgent', 'artifactId']
            },
            { status: 400 }
          );
        }
        
        // Request feedback
        const requestMessageId = await feedbackSystem.requestFeedback(
          fromAgent,
          toAgent,
          artifactId,
          specificAreas
        );
        
        return NextResponse.json({ 
          success: true,
          sessionId,
          messageId: requestMessageId,
          action: 'request'
        });
        
      case 'provide':
        // Validate required parameters for providing feedback
        if (!fromAgent || !toAgent || !artifactId || !feedback || !requestId) {
          return NextResponse.json(
            { 
              error: 'Missing required parameters for providing feedback',
              requiredParams: ['fromAgent', 'toAgent', 'artifactId', 'feedback', 'requestId']
            },
            { status: 400 }
          );
        }
        
        // Provide feedback
        const feedbackMessageId = await feedbackSystem.provideFeedback(
          fromAgent,
          toAgent,
          artifactId,
          feedback,
          requestId
        );
        
        return NextResponse.json({ 
          success: true,
          sessionId,
          messageId: feedbackMessageId,
          action: 'provide'
        });
        
      case 'incorporate':
        // Validate required parameters for incorporating feedback
        if (!fromAgent || !artifactId || !feedbackIds || !changes) {
          return NextResponse.json(
            { 
              error: 'Missing required parameters for incorporating feedback',
              requiredParams: ['fromAgent', 'artifactId', 'feedbackIds', 'changes']
            },
            { status: 400 }
          );
        }
        
        // Incorporate feedback
        const incorporateSuccess = await feedbackSystem.incorporateFeedback(
          fromAgent,
          artifactId,
          feedbackIds,
          changes
        );
        
        return NextResponse.json({ 
          success: incorporateSuccess,
          sessionId,
          artifactId,
          action: 'incorporate'
        });
        
      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}. Supported actions: request, provide, incorporate` },
          { status: 400 }
        );
    }
  } catch (error) {
    const err = error as Error;
    logger.error(`Error handling feedback operation in dynamic collaboration`, {
      error: err.message || String(error),
      stack: err.stack
    });
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: err.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * GET handler for retrieving feedback information
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    const sessionId = req.nextUrl.searchParams.get('sessionId');
    const artifactId = req.nextUrl.searchParams.get('artifactId');
    
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing sessionId parameter' },
        { status: 400 }
      );
    }
    
    if (!artifactId) {
      return NextResponse.json(
        { error: 'Missing artifactId parameter' },
        { status: 400 }
      );
    }
    
    // Get the feedback loop system
    const feedbackSystem = new FeedbackLoopSystem(sessionId);
    
    // Get feedback stats for this artifact
    const feedbackStats = await feedbackSystem.trackFeedbackCycle(artifactId);
    
    // Get the state store
    const { stateStore } = await import('../../agents/collaborative-iteration/utils/stateStore');
    
    // Get the session state
    const state = await stateStore.getState(sessionId);
    
    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }
    
    // Get feedback cycles for this artifact
    const feedbackCycle = state.feedbackCycles?.[artifactId];
    
    return NextResponse.json({ 
      success: true,
      sessionId,
      artifactId,
      feedbackStats,
      feedbackCycle
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error retrieving feedback information from dynamic collaboration`, {
      error: err.message || String(error),
      stack: err.stack
    });
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: err.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}
