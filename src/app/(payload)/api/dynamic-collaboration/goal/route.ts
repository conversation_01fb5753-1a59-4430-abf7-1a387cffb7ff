/**
 * Dynamic Collaboration Goal API Route
 * 
 * This file handles API requests for goal operations in the dynamic collaboration system.
 */

import { NextRequest, NextResponse } from 'next/server';
import { GoalManager } from '../../agents/dynamic-collaboration-v2/goal-manager';
import logger from '../../agents/collaborative-iteration/utils/logger';

/**
 * POST handler for goal operations
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const body = await req.json();
    const { 
      sessionId, 
      action, 
      goalId, 
      agentId, 
      progress, 
      artifactId
    } = body;
    
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing sessionId parameter' },
        { status: 400 }
      );
    }
    
    if (!action) {
      return NextResponse.json(
        { error: 'Missing action parameter (assign, track, complete)' },
        { status: 400 }
      );
    }
    
    if (!goalId) {
      return NextResponse.json(
        { error: 'Missing goalId parameter' },
        { status: 400 }
      );
    }
    
    // Get the goal manager
    const goalManager = new GoalManager(sessionId);
    
    // Handle different actions
    switch (action) {
      case 'assign':
        // Validate required parameters for assigning a goal
        if (!agentId) {
          return NextResponse.json(
            { 
              error: 'Missing required parameters for goal assignment',
              requiredParams: ['agentId']
            },
            { status: 400 }
          );
        }
        
        // Assign the goal
        const assignSuccess = await goalManager.assignGoal(goalId, agentId);
        
        return NextResponse.json({ 
          success: assignSuccess,
          sessionId,
          goalId,
          agentId,
          action: 'assign'
        });
        
      case 'track':
        // Validate required parameters for tracking goal progress
        if (progress === undefined) {
          return NextResponse.json(
            { 
              error: 'Missing required parameters for tracking goal progress',
              requiredParams: ['progress']
            },
            { status: 400 }
          );
        }
        
        // Track goal progress
        await goalManager.trackGoalProgress(goalId, progress);
        
        return NextResponse.json({ 
          success: true,
          sessionId,
          goalId,
          progress,
          action: 'track'
        });
        
      case 'complete':
        // Complete the goal
        const completeSuccess = await goalManager.completeGoal(goalId, artifactId);
        
        return NextResponse.json({ 
          success: completeSuccess,
          sessionId,
          goalId,
          artifactId,
          action: 'complete'
        });
        
      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}. Supported actions: assign, track, complete` },
          { status: 400 }
        );
    }
  } catch (error) {
    const err = error as Error;
    logger.error(`Error handling goal operation in dynamic collaboration`, {
      error: err.message || String(error),
      stack: err.stack
    });
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: err.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * GET handler for retrieving goal information
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    const sessionId = req.nextUrl.searchParams.get('sessionId');
    const goalId = req.nextUrl.searchParams.get('goalId');
    
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing sessionId parameter' },
        { status: 400 }
      );
    }
    
    // Get the state store
    const { stateStore } = await import('../../agents/collaborative-iteration/utils/stateStore');
    
    // Get the session state
    const state = await stateStore.getState(sessionId);
    
    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }
    
    if (goalId) {
      // Get a specific goal
      const goal = state.goals?.[goalId];
      
      if (!goal) {
        return NextResponse.json(
          { error: 'Goal not found' },
          { status: 404 }
        );
      }
      
      return NextResponse.json({ 
        success: true,
        sessionId,
        goal
      });
    } else {
      // Get all goals
      const goals = state.goals || {};
      const activeGoals = state.activeGoals || [];
      const completedGoals = state.completedGoals || [];
      
      return NextResponse.json({ 
        success: true,
        sessionId,
        goals,
        activeGoals,
        completedGoals,
        goalCount: Object.keys(goals).length
      });
    }
  } catch (error) {
    const err = error as Error;
    logger.error(`Error retrieving goal information from dynamic collaboration`, {
      error: err.message || String(error),
      stack: err.stack
    });
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: err.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}
