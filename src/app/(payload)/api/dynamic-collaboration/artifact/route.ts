/**
 * Dynamic Collaboration Artifact API Route
 * 
 * This file handles API requests for artifact operations in the dynamic collaboration system.
 */

import { NextRequest, NextResponse } from 'next/server';
import { ArtifactDecisionFramework } from '../../agents/dynamic-collaboration-v2/artifact-decision-framework';
import { FeedbackLoopSystem } from '../../agents/dynamic-collaboration-v2/feedback-loop-system';
import logger from '../../agents/collaborative-iteration/utils/logger';

/**
 * POST handler for creating or updating artifacts
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const body = await req.json();
    const { sessionId, action, agentId, artifactId, artifactType, content, metadata, goalId, reasoning } = body;
    
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing sessionId parameter' },
        { status: 400 }
      );
    }
    
    if (!action) {
      return NextResponse.json(
        { error: 'Missing action parameter (create, update, decide)' },
        { status: 400 }
      );
    }
    
    if (!agentId) {
      return NextResponse.json(
        { error: 'Missing agentId parameter' },
        { status: 400 }
      );
    }
    
    // Get the artifact decision framework
    const artifactFramework = new ArtifactDecisionFramework(sessionId);
    
    // Handle different actions
    switch (action) {
      case 'create':
        // Validate required parameters for creation
        if (!artifactType || !content) {
          return NextResponse.json(
            { 
              error: 'Missing required parameters for artifact creation',
              requiredParams: ['artifactType', 'content']
            },
            { status: 400 }
          );
        }
        
        // Create the artifact
        const newArtifactId = await artifactFramework.createArtifact(
          agentId,
          artifactType,
          content,
          metadata || {},
          goalId
        );
        
        return NextResponse.json({ 
          success: true,
          sessionId,
          artifactId: newArtifactId,
          action: 'create'
        });
        
      case 'update':
        // Validate required parameters for update
        if (!artifactId || !content || !reasoning) {
          return NextResponse.json(
            { 
              error: 'Missing required parameters for artifact update',
              requiredParams: ['artifactId', 'content', 'reasoning']
            },
            { status: 400 }
          );
        }
        
        // Update the artifact
        const updateSuccess = await artifactFramework.updateArtifact(
          artifactId,
          agentId,
          content,
          reasoning
        );
        
        return NextResponse.json({ 
          success: updateSuccess,
          sessionId,
          artifactId,
          action: 'update'
        });
        
      case 'decide':
        // Validate required parameters for decision
        if (!goalId) {
          return NextResponse.json(
            { 
              error: 'Missing required parameters for artifact decision',
              requiredParams: ['goalId']
            },
            { status: 400 }
          );
        }
        
        // Make a decision about artifact creation
        const decision = await artifactFramework.decideArtifactCreation(
          agentId,
          goalId
        );
        
        return NextResponse.json({ 
          success: true,
          sessionId,
          decision,
          action: 'decide'
        });
        
      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}. Supported actions: create, update, decide` },
          { status: 400 }
        );
    }
  } catch (error) {
    const err = error as Error;
    logger.error(`Error handling artifact operation in dynamic collaboration`, {
      error: err.message || String(error),
      stack: err.stack
    });
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: err.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * GET handler for retrieving artifacts
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    const sessionId = req.nextUrl.searchParams.get('sessionId');
    const artifactId = req.nextUrl.searchParams.get('artifactId');
    
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing sessionId parameter' },
        { status: 400 }
      );
    }
    
    // Get the state store
    const { stateStore } = await import('../../agents/collaborative-iteration/utils/stateStore');
    
    // Get the session state
    const state = await stateStore.getState(sessionId);
    
    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }
    
    // Get artifacts
    if (artifactId) {
      // Get a specific artifact
      const artifact = state.artifacts?.[artifactId];
      
      if (!artifact) {
        return NextResponse.json(
          { error: 'Artifact not found' },
          { status: 404 }
        );
      }
      
      // Get feedback stats for this artifact
      const feedbackSystem = new FeedbackLoopSystem(sessionId);
      const feedbackStats = await feedbackSystem.trackFeedbackCycle(artifactId);
      
      return NextResponse.json({ 
        success: true,
        sessionId,
        artifact,
        feedbackStats
      });
    } else {
      // Get all artifacts
      const artifacts = state.artifacts || {};
      const generatedArtifacts = state.generatedArtifacts || [];
      
      return NextResponse.json({ 
        success: true,
        sessionId,
        artifacts,
        generatedArtifacts,
        artifactCount: Object.keys(artifacts).length
      });
    }
  } catch (error) {
    const err = error as Error;
    logger.error(`Error retrieving artifacts from dynamic collaboration`, {
      error: err.message || String(error),
      stack: err.stack
    });
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: err.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}
