/**
 * Dynamic Collaboration API Route
 * 
 * This file handles API requests for the dynamic collaboration system.
 */

import { NextRequest, NextResponse } from 'next/server';
import { handleDynamicCollaborationRequest } from '../agents/dynamic-collaboration-v2';
import { stateStore } from '../agents/collaborative-iteration/utils/stateStore';
import logger from '../agents/collaborative-iteration/utils/logger';

/**
 * POST handler for initiating a dynamic collaboration session
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const body = await req.json();
    
    // Create a response object that mimics Express
    const res = {
      status: (code: number) => ({
        json: (data: any) => NextResponse.json(data, { status: code })
      })
    };
    
    // Use the handler from the dynamic collaboration system
    await handleDynamicCollaborationRequest(
      { method: 'POST', body },
      res
    );
    
    // The handler will have called res.status().json() which returns a NextResponse
    // This is a bit of a hack, but it allows us to reuse the handler
    return res.status(200).json({ error: 'Response not set by handler' });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error in dynamic collaboration API route`, {
      error: err.message || String(error),
      stack: err.stack
    });
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: err.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * GET handler for retrieving a dynamic collaboration session
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    const sessionId = req.nextUrl.searchParams.get('sessionId');
    
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing sessionId parameter' },
        { status: 400 }
      );
    }
    
    // Get the session state
    const state = await stateStore.getState(sessionId);
    
    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }
    
    // Return the session state
    return NextResponse.json({ 
      success: true,
      sessionId,
      state
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error retrieving dynamic collaboration session`, {
      error: err.message || String(error),
      stack: err.stack
    });
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: err.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}
