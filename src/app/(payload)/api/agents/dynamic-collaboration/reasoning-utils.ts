/**
 * Utility functions for generating chain-of-thought reasoning
 * for agent responses in the dynamic collaboration system.
 * 
 * This implementation uses a LangGraph-inspired approach where reasoning
 * is dynamically generated using direct LLM calls.
 */

import { CollaborationMessage } from './types';
import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || '',
});

interface ReasoningOutput {
  thoughts: string[];
  considerations: string[];
  alternatives?: string[];
  decision: string;
}

/**
 * Generate chain-of-thought reasoning using LLM directly
 * 
 * @param agentType The type of agent generating the reasoning
 * @param context Context information for the reasoning
 * @param focusArea The specific area to focus on
 * @returns A reasoning object with thoughts, considerations, alternatives, and decision
 */
export async function generateChainOfThoughtReasoning(
  agentType: string, 
  context: any, 
  focusArea: string
): Promise<ReasoningOutput> {
  // Define agent-specific system prompts for different agent types
  const systemPrompts: Record<string, string> = {
    'market-research': `You are an expert market research analyst. Generate in-depth reasoning about "${context.topic}" with focus on ${focusArea} for ${context.targetAudience}.`,
    'seo-keywords': `You are an expert SEO strategist. Generate in-depth reasoning about keyword strategy for "${context.topic}" with focus on ${focusArea}.`,
    'content-strategy': `You are an expert content strategist. Generate in-depth reasoning about content strategy for "${context.topic}" with focus on ${focusArea} for ${context.targetAudience}.`,
    'seo-optimization': `You are an expert SEO optimizer. Generate in-depth reasoning about optimizing content for "${context.topic}" with focus on ${focusArea}.`,
    'default': `You are an expert analyst. Generate in-depth reasoning about "${context.topic || 'the topic'}" with focus on ${focusArea}.`
  };

  // Get the system prompt for the agent type or use the default
  const systemPrompt = systemPrompts[agentType] || systemPrompts['default'];

  // Prepare the user prompt that requests the specific reasoning structure
  const userPrompt = `
  Please provide detailed chain-of-thought reasoning for ${context.topic || 'this task'} with focus on ${focusArea}.
  
  Structure your response in the following JSON format:
  {
    "thoughts": [
      "First key thought about the topic",
      "Second key thought about the topic",
      "Third key thought about the topic",
      "Fourth key thought about the topic",
      "Fifth key thought about the topic"
    ],
    "considerations": [
      "First important consideration or question to address",
      "Second important consideration or question to address",
      "Third important consideration or question to address",
      "Fourth important consideration or question to address",
      "Fifth important consideration or question to address"
    ],
    "alternatives": [
      "First alternative approach that could be considered",
      "Second alternative approach that could be considered",
      "Third alternative approach that could be considered",
      "Fourth alternative approach that could be considered"
    ],
    "decision": "A comprehensive decision paragraph that synthesizes the analysis and provides a clear recommendation with supporting rationale."
  }

  Ensure your reasoning is specific, data-informed, and actionable. Include industry best practices and strategic insights relevant to ${context.topic || 'this task'}.
  `;

  try {
    // Make the API call to OpenAI
    const response = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-4-turbo',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.7,
      max_tokens: 1500,
      response_format: { type: 'json_object' }
    });

    // Parse the response
    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No content returned from OpenAI');
    }

    // Parse the JSON response
    const parsedResponse = JSON.parse(content) as ReasoningOutput;
    
    // Validate the response structure
    if (!parsedResponse.thoughts || !parsedResponse.considerations || !parsedResponse.decision) {
      throw new Error('Invalid response structure from OpenAI');
    }

    return parsedResponse;
  } catch (error) {
    console.error('Error generating reasoning with OpenAI:', error);
    
    // Fallback to default reasoning if API call fails
    return {
      thoughts: [
        `Analyzing "${context.topic || 'the topic'}" with specific focus on ${focusArea}`,
        `Examining the current state and identifying key areas for improvement`,
        `Researching industry best practices and standards relevant to ${focusArea}`,
        `Evaluating multiple approaches based on effectiveness, efficiency, and alignment with goals`,
        `Considering potential implementation challenges and mitigation strategies`
      ],
      considerations: [
        `What specific metrics or outcomes would define success for this ${focusArea} initiative?`,
        `How does this approach balance short-term improvements with long-term strategic goals?`,
        `What potential risks or unintended consequences should be considered with each approach?`,
        `How will this solution scale and adapt to changing requirements over time?`,
        `What resources, dependencies, or constraints might impact implementation?`
      ],
      alternatives: [
        `Could implement an iterative approach with regular feedback cycles to allow for continuous refinement`,
        `Could pursue a more comprehensive solution that addresses multiple related challenges simultaneously`,
        `Could focus on a targeted, high-impact intervention in the most critical area first`,
        `Could adopt an innovative approach that challenges conventional wisdom in this domain`
      ],
      decision: `After thorough analysis of ${context.topic || 'this task'} with particular focus on ${focusArea}, I recommend the following approach: (1) Prioritize addressing the most critical aspects first, specifically [key aspect]; (2) Implement a solution that balances immediate impact with strategic alignment; (3) Incorporate specific measurable success criteria to evaluate effectiveness; and (4) Build in flexibility to adapt based on feedback and changing requirements. This recommendation is based on careful consideration of the specific context, industry best practices, and the unique requirements of this situation.`
    };
  }
}

/**
 * Apply chain-of-thought reasoning to a message
 * 
 * @param message The message to enhance with reasoning
 * @param agentType The type of agent generating the reasoning
 * @param context Context information for the reasoning
 * @param focusArea The specific area to focus on
 * @param customReasoning Optional custom reasoning to use instead of generating new reasoning
 * @returns The message with reasoning added
 */
export async function applyReasoningToMessage(
  message: CollaborationMessage,
  agentType: string,
  context: any,
  focusArea: string,
  customReasoning?: ReasoningOutput
): Promise<CollaborationMessage> {
  // Use custom reasoning if provided, otherwise generate new reasoning
  const reasoning = customReasoning || await generateChainOfThoughtReasoning(agentType, context, focusArea);
  
  // Return a new message with reasoning added
  return {
    ...message,
    reasoning
  };
}
