// src/app/(payload)/api/agents/dynamic-collaboration/types.ts

import { v4 as uuidv4 } from 'uuid';

/**
 * Message types for dynamic agent collaboration
 */
export type MessageType = 
  | 'REQUEST_INFO'       // Request information from another agent
  | 'PROVIDE_INFO'       // Provide information to another agent
  | 'REQUEST_FEEDBACK'   // Request feedback on content or ideas
  | 'PROVIDE_FEEDBACK'   // Provide feedback on content or ideas
  | 'REQUEST_CONTENT'    // Request content generation
  | 'PROVIDE_CONTENT'    // Provide generated content
  | 'ASK_FOLLOWUP'       // Ask a follow-up question
  | 'ANSWER_FOLLOWUP'    // Answer a follow-up question
  | 'SUGGEST_REVISION'   // Suggest a revision to content
  | 'ACCEPT_REVISION'    // Accept a suggested revision
  | 'REJECT_REVISION'    // Reject a suggested revision
  | 'SYSTEM_MESSAGE';    // System message (phase changes, etc.)

/**
 * Collaboration phases
 */
export type CollaborationPhase = 
  | 'planning'    // Initial planning phase
  | 'discussion'  // Discussion and information gathering
  | 'execution'   // Content creation
  | 'review'      // Review and feedback
  | 'refinement'  // Refinement based on feedback
  | 'completed';  // Collaboration completed

/**
 * Message structure for dynamic collaboration
 */
export interface CollaborationMessage {
  id: string;
  timestamp: string;
  from: string;
  to: string;
  type: MessageType;
  content: any;
  replyTo?: string;
  conversationId: string;
  reasoning?: {
    thoughts: string[];
    considerations: string[];
    alternatives?: string[];
    decision: string;
  };
  metadata?: {
    phase: CollaborationPhase;
    priority?: 'low' | 'medium' | 'high';
    tags?: string[];
    [key: string]: any;
  };
}

/**
 * Shared state for collaboration
 */
export interface SharedCollaborationState {
  id: string;
  topic: string;
  contentType: 'blog-article' | 'product-page' | 'buying-guide';
  targetAudience: string;
  tone: string;
  keywords: string[];
  phase: CollaborationPhase;
  startTime: string;
  endTime?: string;
  marketResearch?: any;
  seoKeywords?: any;
  contentStrategy?: any;
  content?: any;
  seoOptimization?: any;
  conversations: Record<string, CollaborationMessage[]>;
  decisions: {
    planning: string[];
    discussion: string[];
    execution: string[];
    review: string[];
    refinement: string[];
  };
  iterations: {
    version: number;
    timestamp: string;
    content: any;
    feedback: any[];
    reasoning?: {
      thoughts: string[];
      considerations: string[];
      alternatives?: string[];
      decision: string;
    };
  }[];
}

/**
 * Content generation request interface
 */
export interface ContentGenerationRequest {
  topic: string;
  contentType: 'blog-article' | 'product-page' | 'buying-guide';
  targetAudience: string;
  keywords: string[];
  tone: string;
}

/**
 * Agent information
 */
export interface AgentInfo {
  id: string;
  name: string;
  role: string;
  capabilities: string[];
  endpoint: string;
}

/**
 * Available agents in the system
 */
export const AVAILABLE_AGENTS: Record<string, AgentInfo> = {
  'content-generation': {
    id: 'content-generation',
    name: 'Content Generation Agent',
    role: 'Coordinator',
    capabilities: ['planning', 'coordination', 'content-generation'],
    endpoint: '/api/agents/content-generation'
  },
  'market-research': {
    id: 'market-research',
    name: 'Market Research Agent',
    role: 'Researcher',
    capabilities: ['market-analysis', 'audience-analysis', 'trend-analysis'],
    endpoint: '/api/agents/market-research'
  },
  'seo-keyword': {
    id: 'seo-keyword',
    name: 'SEO Keyword Agent',
    role: 'SEO Specialist',
    capabilities: ['keyword-research', 'search-intent-analysis', 'content-optimization'],
    endpoint: '/api/agents/seo-keyword'
  },
  'content-strategy': {
    id: 'content-strategy',
    name: 'Content Strategy Agent',
    role: 'Strategist',
    capabilities: ['content-planning', 'structure-development', 'tone-adaptation'],
    endpoint: '/api/agents/content-strategy'
  },
  'seo-optimization': {
    id: 'seo-optimization',
    name: 'SEO Optimization Agent',
    role: 'Optimizer',
    capabilities: ['seo-analysis', 'content-optimization', 'metadata-optimization'],
    endpoint: '/api/agents/seo-optimization'
  }
};

/**
 * Create a new collaboration message
 */
export function createMessage(
  from: string,
  to: string,
  type: MessageType,
  content: any,
  options: {
    replyTo?: string;
    conversationId?: string;
    phase?: CollaborationPhase;
    priority?: 'low' | 'medium' | 'high';
    tags?: string[];
    reasoning?: {
      thoughts: string[];
      considerations: string[];
      alternatives?: string[];
      decision: string;
    };
  } = {}
): CollaborationMessage {
  // Strict validation for required fields
  if (!from || !to || !type || content === undefined || content === null) {
    console.error('[createMessage] Invalid message fields:', { from, to, type, content, options, stack: new Error().stack });
    throw new Error('Invalid message: missing required fields (from, to, type, content)');
  }
  return {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    from,
    to,
    type,
    content,
    replyTo: options.replyTo,
    conversationId: options.conversationId || uuidv4(),
    ...(options.reasoning && { reasoning: options.reasoning }),
    metadata: {
      phase: options.phase || 'discussion',
      priority: options.priority || 'medium',
      tags: options.tags || []
    }
  };
}

/**
 * Create a new collaboration state
 */
export function createCollaborationState(request: ContentGenerationRequest): SharedCollaborationState {
  return {
    id: uuidv4(),
    topic: request.topic,
    contentType: request.contentType,
    targetAudience: request.targetAudience,
    tone: request.tone,
    keywords: request.keywords,
    phase: 'planning',
    startTime: new Date().toISOString(),
    conversations: {},
    decisions: {
      planning: [],
      discussion: [],
      execution: [],
      review: [],
      refinement: []
    },
    iterations: []
  };
}
