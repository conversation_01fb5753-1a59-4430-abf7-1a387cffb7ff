// src/app/(payload)/api/agents/seo-optimization/with-reasoning/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { 
  EnhancedA2AMessage, 
  Reasoning, 
  Part, 
  TextPart, 
  DataPart,
  CollaborationState,
  ArtifactType
} from '../../a2atypes';

/**
 * Handle enhanced A2A messages with explicit reasoning
 */
async function handleEnhancedMessage(message: EnhancedA2AMessage, state: CollaborationState): Promise<{
  message: EnhancedA2AMessage;
  stateUpdates?: any;
}> {
  console.log("SEO Optimization Agent received enhanced message:", JSON.stringify(message));
  
  // Extract request type from message parts
  let requestType = '';
  for (const part of message.parts) {
    if (part.type === 'text') {
      const text = part.text;
      
      if (text.includes('optimize content') || text.includes('seo review')) {
        requestType = 'seo-review';
      } else if (text.includes('meta tags') || text.includes('metadata')) {
        requestType = 'meta-tags';
      }
    }
  }
  
  // Default to seo-review if no specific request type
  if (!requestType) {
    requestType = 'seo-review';
  }
  
  // Create parts for response
  const responseParts: Part[] = [];
  const stateUpdates: any = {};
  
  // Extract content draft from state
  let contentDraft = null;
  let contentArtifactId = '';
  if (state.artifactIndex) {
    const draftArtifact = Object.values(state.artifactIndex).find(
      artifact => artifact.type === 'draft-content'
    );
    
    if (draftArtifact) {
      contentArtifactId = draftArtifact.id;
      const dataPart = draftArtifact.parts.find(part => part.type === 'data');
      if (dataPart && dataPart.type === 'data') {
        contentDraft = dataPart.data;
      }
    }
  }
  
  // Extract keywords from state
  let keywords = state.keywords;
  if (state.artifactIndex) {
    const keywordArtifact = Object.values(state.artifactIndex).find(
      artifact => artifact.type === 'keyword-set'
    );
    
    if (keywordArtifact && keywordArtifact.parts) {
      const dataPart = keywordArtifact.parts.find(part => part.type === 'data');
      if (dataPart && dataPart.type === 'data' && dataPart.data.recommendedKeywords) {
        keywords = dataPart.data.recommendedKeywords.map(k => k.keyword);
      }
    }
  }
  
  // Generate SEO analysis
  const seoAnalysis = {
    overallScore: 82,
    keywordAnalysis: {
      primaryKeywordUsage: {
        title: true,
        headings: true,
        firstParagraph: true,
        contentBody: true,
        density: '1.7%',
        recommendation: 'Good primary keyword usage, maintain current implementation'
      },
      secondaryKeywordUsage: {
        headings: true,
        contentBody: true,
        density: '0.9%',
        recommendation: 'Consider increasing secondary keyword usage slightly'
      },
      missingKeywords: keywords ? keywords.filter(k => 
        !contentDraft || !contentDraft.fullContent || 
        !contentDraft.fullContent.toLowerCase().includes(k.toLowerCase())
      ) : [],
      keywordSuggestions: [
        'Add more long-tail variations of primary keywords',
        'Include semantic keywords related to the main topic',
        'Distribute keywords more evenly across all sections'
      ]
    },
    contentAnalysis: {
      readability: {
        score: contentDraft?.metadata?.readabilityScore || 75,
        issuesFound: [
          'Some paragraphs are too long for optimal readability',
          'Consider breaking down complex sentences in section 3'
        ],
        recommendations: [
          'Keep paragraphs under 3-4 sentences for better readability',
          'Use more bullet points to break up dense information'
        ]
      },
      structure: {
        headingHierarchy: 'Good H1 > H2 > H3 structure',
        subheadingDistribution: 'Well-distributed, content is properly segmented',
        recommendations: [
          'Add more descriptive H2 and H3 headings that include secondary keywords'
        ]
      },
      contentQuality: {
        comprehensiveness: 'Good coverage of the topic',
        uniqueness: 'Content appears original and valuable',
        recommendations: [
          'Add more specific examples and case studies',
          'Include more data points and statistics to support claims'
        ]
      }
    },
    technicalSEO: {
      metaTags: {
        title: {
          current: contentDraft?.metadata?.title || `${state.topic}: A Comprehensive Guide`,
          recommended: `${state.topic}: Expert Guide with ${keywords && keywords.length > 0 ? keywords[0] : 'Key Insights'} [${new Date().getFullYear()}]`,
          issues: 'Title could be more compelling and include year for freshness'
        },
        description: {
          current: contentDraft?.metadata?.description || 'Description not provided',
          recommended: `Learn everything about ${state.topic} in this comprehensive guide. Discover expert strategies, best practices, and actionable insights for ${state.targetAudience}.`,
          issues: 'Description could better highlight the value proposition'
        }
      },
      urlStructure: {
        recommendation: `${state.topic.toLowerCase().replace(/\s+/g, '-')}`,
        issues: 'Ensure URL is descriptive and includes primary keyword'
      },
      internalLinking: {
        opportunities: [
          'Link to related content about ' + (keywords && keywords.length > 1 ? keywords[1] : 'related topics'),
          'Create pillar-cluster structure with related content',
          'Add table of contents with anchor links'
        ]
      }
    },
    actionItems: [
      'Optimize meta title and description with recommended versions',
      'Increase keyword density for secondary keywords',
      'Break up longer paragraphs for better readability',
      'Add more descriptive H2 and H3 headings with keywords',
      'Include internal linking opportunities'
    ]
  };
  
  // Create reasoning
  const reasoning: Reasoning = {
    thoughts: [
      `SEO analysis for "${state.topic}" content focused on keyword optimization and content structure`,
      `Evaluated keyword usage, readability, and technical SEO elements`,
      `Current content has good foundation but needs optimization in specific areas`,
      `Meta tags and keyword distribution require the most attention`
    ],
    considerations: [
      'Balance between keyword optimization and natural readability',
      'User experience factors that impact SEO performance',
      'Technical SEO elements that search engines prioritize',
      'Content comprehensiveness relative to competitor content'
    ],
    alternatives: [
      'Could recommend complete content restructuring (too disruptive)',
      'Could focus only on keyword density (too narrow)',
      'Could suggest minimal changes (insufficient for optimal performance)'
    ],
    decision: 'Provide targeted optimization recommendations focusing on keyword usage, readability improvements, and technical SEO elements',
    confidence: 0.89
  };
  
  // Create text part with main response
  const textPart: TextPart = {
    type: 'text',
    text: `I've completed an SEO analysis of the content for "${state.topic}" and found several optimization opportunities.

Overall SEO Score: ${seoAnalysis.overallScore}/100

Key findings:
1. Keyword usage: Primary keyword implementation is good (${seoAnalysis.keywordAnalysis.primaryKeywordUsage.density} density), but secondary keywords could be increased slightly.

2. Content structure: Good heading hierarchy, but consider adding more descriptive H2 and H3 headings that include target keywords.

3. Readability: Score of ${seoAnalysis.contentAnalysis.readability.score}/100. Some paragraphs are too long - breaking these up would improve readability.

4. Meta tags: Current title and description could be more compelling. I've provided optimized versions.

5. Internal linking: Adding internal links to related content would strengthen the SEO value.

Priority action items:
${seoAnalysis.actionItems.map((item, index) => `${index + 1}. ${item}`).join('\n')}

These targeted optimizations will significantly improve search visibility while maintaining content quality and user experience.`
  };
  
  // Create data part with structured analysis
  const dataPart: DataPart = {
    type: 'data',
    data: seoAnalysis
  };
  
  responseParts.push(textPart, dataPart);
  
  // Create artifact
  const artifactId = uuidv4();
  const artifact = {
    id: artifactId,
    name: `SEO Analysis for ${state.topic}`,
    description: `Comprehensive SEO optimization recommendations for ${state.topic} content`,
    type: 'seo-analysis' as ArtifactType,
    parts: [textPart, dataPart],
    metadata: {
      createdAt: new Date().toISOString(),
      createdBy: 'seo-optimization'
    },
    index: 0,
    version: 1,
    contributors: ['seo-optimization'],
    feedback: [],
    history: [
      {
        version: 1,
        timestamp: new Date().toISOString(),
        agent: 'seo-optimization',
        changes: 'Initial SEO analysis',
        parts: [textPart, dataPart],
        reasoning
      }
    ],
    status: 'draft',
    qualityScore: 88
  };
  
  // Add to state updates
  stateUpdates.seoOptimization = seoAnalysis;
  stateUpdates.artifactIndex = {
    [artifactId]: artifact
  };
  
  // Create response message
  const responseMessage: EnhancedA2AMessage = {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    from: 'seo-optimization',
    to: message.from,
    role: 'agent',
    parts: responseParts,
    conversationId: message.conversationId,
    reasoning,
    intentions: ['inform', 'suggest'],
    replyTo: message.id,
    artifactReferences: [artifactId],
    requestedActions: [
      {
        agent: 'content-generation',
        action: 'ApplySEOFeedback',
        priority: 8,
        rationale: 'SEO improvements will significantly enhance content visibility',
        parameters: {
          contentArtifactId,
          seoArtifactId: artifactId,
          priorityChanges: seoAnalysis.actionItems.slice(0, 3)
        }
      }
    ]
  };
  
  return { message: responseMessage, stateUpdates };
}

/**
 * API route handler for enhanced A2A communication
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("SEO Optimization Agent received enhanced POST request:", JSON.stringify(body));
    
    // Extract the message and state from request body
    const { message, state } = body;
    
    // Handle the enhanced message
    const response = await handleEnhancedMessage(message, state);
    
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error in SEO Optimization Agent enhanced POST handler:", error);
    
    // Create error response
    const errorResponse: EnhancedA2AMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'seo-optimization',
      to: 'system',
      role: 'agent',
      parts: [
        {
          type: 'text',
          text: `Error processing message: ${(error as Error).message}`
        }
      ],
      conversationId: uuidv4(),
      reasoning: {
        thoughts: ['An error occurred while processing the message'],
        considerations: ['The error might be due to invalid input or internal processing'],
        decision: 'Return error message with details',
        confidence: 1.0
      },
      intentions: ['inform']
    };
    
    return NextResponse.json(
      { message: errorResponse, error: (error as Error).message },
      { status: 500 }
    );
  }
}

/**
 * API route handler for agent capabilities
 */
export async function GET(request: NextRequest) {
  return NextResponse.json({ 
    agent: "SEO Optimization Agent",
    status: "active",
    capabilities: [
      "content-optimization", 
      "seo-analysis", 
      "meta-tag-optimization"
    ],
    enhancedProtocol: true,
    reasoningEnabled: true,
    artifactCreation: true
  });
}
