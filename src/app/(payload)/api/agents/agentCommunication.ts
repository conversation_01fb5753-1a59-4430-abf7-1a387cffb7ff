// src/app/(payload)/api/agents/agentCommunication.ts

import { ContentGenerationState } from './contentGeneration';
import fetch from 'node-fetch';

// Define message types for agent communication
export type AgentMessageType = 
  | 'request_information'
  | 'provide_information'
  | 'request_feedback'
  | 'provide_feedback'
  | 'request_collaboration'
  | 'collaboration_response'
  | 'notify_completion'
  | 'error';

// Define the structure of agent messages
export interface AgentMessage {
  id: string;
  timestamp: string;
  from: string;
  to: string;
  type: AgentMessageType;
  content: any;
  replyTo?: string;
  metadata?: Record<string, any>;
}

// Define the message bus for agent communication
export class AgentMessageBus {
  private static instance: AgentMessageBus;
  private messages: AgentMessage[] = [];
  private subscribers: Record<string, ((message: AgentMessage) => Promise<void>)[]> = {};
  private agentEndpoints: Record<string, string> = {
    'marketResearch': 'market-research',
    'seoKeyword': 'seo-keyword',
    'contentStrategy': 'content-strategy',
    'contentGeneration': 'content-generation',
    'editorial': 'editorial',
    'seoOptimization': 'seo-optimization',
    'contentScoring': 'content-scoring'
  };

  private constructor() {
    // Initialize agent-specific subscribers
    Object.keys(this.agentEndpoints).forEach(agent => {
      this.subscribers[agent] = [];
    });
  }

  // Get the singleton instance
  public static getInstance(): AgentMessageBus {
    if (!AgentMessageBus.instance) {
      AgentMessageBus.instance = new AgentMessageBus();
    }
    return AgentMessageBus.instance;
  }

  // Publish a message to the bus
  public async publishMessage(message: AgentMessage): Promise<void> {
    // Add timestamp if not provided
    if (!message.timestamp) {
      message.timestamp = new Date().toISOString();
    }
    
    // Add message to the history
    this.messages.push(message);
    
    // Notify subscribers
    if (this.subscribers[message.to]) {
      for (const subscriber of this.subscribers[message.to]) {
        try {
          await subscriber(message);
        } catch (error) {
          console.error(`Error notifying subscriber for agent ${message.to}:`, error);
        }
      }
    }
    
    // If this is a direct agent-to-agent communication, also send via A2A protocol
    if (message.to !== 'all' && this.agentEndpoints[message.to]) {
      await this.sendMessageViaA2A(message);
    }
  }

  // Subscribe to messages for a specific agent
  public subscribeToMessages(agent: string, callback: (message: AgentMessage) => Promise<void>): void {
    if (!this.subscribers[agent]) {
      this.subscribers[agent] = [];
    }
    this.subscribers[agent].push(callback);
  }

  // Get all messages for a specific agent
  public getMessagesForAgent(agent: string): AgentMessage[] {
    return this.messages.filter(message => message.to === agent || message.to === 'all');
  }

  // Get conversation thread based on a message ID
  public getConversationThread(messageId: string): AgentMessage[] {
    // Find the original message
    const originalMessage = this.messages.find(message => message.id === messageId);
    if (!originalMessage) return [];
    
    // Get all replies to this message
    const directReplies = this.messages.filter(message => message.replyTo === messageId);
    
    // Recursively get replies to the replies
    const allReplies = [...directReplies];
    for (const reply of directReplies) {
      const subReplies = this.getConversationThread(reply.id);
      allReplies.push(...subReplies);
    }
    
    // Return the original message and all replies in chronological order
    return [originalMessage, ...allReplies].sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
  }

  // Send a message via the A2A protocol
  private async sendMessageViaA2A(message: AgentMessage): Promise<void> {
    try {
      const agentEndpoint = this.agentEndpoints[message.to];
      if (!agentEndpoint) {
        throw new Error(`Unknown agent endpoint for ${message.to}`);
      }
      
      // Format the message for A2A protocol
      const response = await fetch(`/api/agents/${agentEndpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'tasks/send',
          params: {
            task: {
              history: [
                {
                  role: 'user',
                  parts: [
                    {
                      type: 'text',
                      text: this.formatMessageForA2A(message)
                    }
                  ]
                }
              ],
              metadata: {
                agentMessage: message,
                ...message.metadata
              },
              id: `task-${message.id}`
            }
          },
          id: Math.floor(Math.random() * 10000)
        })
      });
      
      const result = await response.json();
      
      if (result.error) {
        throw new Error(`A2A error: ${result.error.message}`);
      }
    } catch (error) {
      console.error(`Error sending message via A2A protocol:`, error);
      
      // Publish an error message back to the sender
      await this.publishMessage({
        id: `error-${Date.now()}-${Math.floor(Math.random() * 10000)}`,
        timestamp: new Date().toISOString(),
        from: message.to,
        to: message.from,
        type: 'error',
        content: {
          error: `Failed to deliver message: ${error instanceof Error ? error.message : 'Unknown error'}`,
          originalMessage: message
        },
        replyTo: message.id
      });
    }
  }

  // Format a message for A2A protocol
  private formatMessageForA2A(message: AgentMessage): string {
    // Create a human-readable format for the message
    const messageType = message.type.replace(/_/g, ' ');
    
    let formattedMessage = `[Agent Communication] ${message.from} ${messageType}:\n\n`;
    
    if (typeof message.content === 'string') {
      formattedMessage += message.content;
    } else {
      formattedMessage += JSON.stringify(message.content, null, 2);
    }
    
    if (message.replyTo) {
      formattedMessage += `\n\n(In response to message ${message.replyTo})`;
    }
    
    return formattedMessage;
  }
}

// Helper functions for agent communication

// Create a new message
export function createAgentMessage(
  from: string,
  to: string,
  type: AgentMessageType,
  content: any,
  replyTo?: string,
  metadata?: Record<string, any>
): AgentMessage {
  return {
    id: `msg-${Date.now()}-${Math.floor(Math.random() * 10000)}`,
    timestamp: new Date().toISOString(),
    from,
    to,
    type,
    content,
    replyTo,
    metadata
  };
}

// Request information from another agent
export async function requestInformationFromAgent(
  fromAgent: string,
  toAgent: string,
  query: string,
  context?: any,
  replyToMessageId?: string
): Promise<string> {
  const messageBus = AgentMessageBus.getInstance();
  
  const messageId = `msg-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  
  // Create and publish the message
  await messageBus.publishMessage({
    id: messageId,
    timestamp: new Date().toISOString(),
    from: fromAgent,
    to: toAgent,
    type: 'request_information',
    content: {
      query,
      context
    },
    replyTo: replyToMessageId,
    metadata: {
      priority: 'high',
      requiresResponse: true
    }
  });
  
  return messageId;
}

// Provide information to another agent
export async function provideInformationToAgent(
  fromAgent: string,
  toAgent: string,
  information: any,
  replyToMessageId: string
): Promise<string> {
  const messageBus = AgentMessageBus.getInstance();
  
  const messageId = `msg-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  
  // Create and publish the message
  await messageBus.publishMessage({
    id: messageId,
    timestamp: new Date().toISOString(),
    from: fromAgent,
    to: toAgent,
    type: 'provide_information',
    content: information,
    replyTo: replyToMessageId
  });
  
  return messageId;
}

// Request feedback from another agent
export async function requestFeedbackFromAgent(
  fromAgent: string,
  toAgent: string,
  content: any,
  feedbackInstructions: string,
  replyToMessageId?: string
): Promise<string> {
  const messageBus = AgentMessageBus.getInstance();
  
  const messageId = `msg-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  
  // Create and publish the message
  await messageBus.publishMessage({
    id: messageId,
    timestamp: new Date().toISOString(),
    from: fromAgent,
    to: toAgent,
    type: 'request_feedback',
    content: {
      content,
      feedbackInstructions
    },
    replyTo: replyToMessageId,
    metadata: {
      priority: 'medium',
      requiresResponse: true
    }
  });
  
  return messageId;
}

// Provide feedback to another agent
export async function provideFeedbackToAgent(
  fromAgent: string,
  toAgent: string,
  feedback: any,
  replyToMessageId: string
): Promise<string> {
  const messageBus = AgentMessageBus.getInstance();
  
  const messageId = `msg-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  
  // Create and publish the message
  await messageBus.publishMessage({
    id: messageId,
    timestamp: new Date().toISOString(),
    from: fromAgent,
    to: toAgent,
    type: 'provide_feedback',
    content: feedback,
    replyTo: replyToMessageId
  });
  
  return messageId;
}

// Request collaboration from another agent
export async function requestCollaborationFromAgent(
  fromAgent: string,
  toAgent: string,
  task: string,
  context: any,
  replyToMessageId?: string
): Promise<string> {
  const messageBus = AgentMessageBus.getInstance();
  
  const messageId = `msg-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  
  // Create and publish the message
  await messageBus.publishMessage({
    id: messageId,
    timestamp: new Date().toISOString(),
    from: fromAgent,
    to: toAgent,
    type: 'request_collaboration',
    content: {
      task,
      context
    },
    replyTo: replyToMessageId,
    metadata: {
      priority: 'high',
      requiresResponse: true
    }
  });
  
  return messageId;
}

// Respond to a collaboration request
export async function respondToCollaborationRequest(
  fromAgent: string,
  toAgent: string,
  collaborationResult: any,
  replyToMessageId: string
): Promise<string> {
  const messageBus = AgentMessageBus.getInstance();
  
  const messageId = `msg-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  
  // Create and publish the message
  await messageBus.publishMessage({
    id: messageId,
    timestamp: new Date().toISOString(),
    from: fromAgent,
    to: toAgent,
    type: 'collaboration_response',
    content: collaborationResult,
    replyTo: replyToMessageId
  });
  
  return messageId;
}

// Notify completion of a task
export async function notifyTaskCompletion(
  fromAgent: string,
  toAgent: string,
  taskResult: any,
  replyToMessageId?: string
): Promise<string> {
  const messageBus = AgentMessageBus.getInstance();
  
  const messageId = `msg-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  
  // Create and publish the message
  await messageBus.publishMessage({
    id: messageId,
    timestamp: new Date().toISOString(),
    from: fromAgent,
    to: toAgent,
    type: 'notify_completion',
    content: taskResult,
    replyTo: replyToMessageId
  });
  
  return messageId;
}

// Enhanced content generation agent with dynamic collaboration
export async function enhancedContentGenerationAgent(state: {
  agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
  try {
    console.log("Starting content generation with dynamic agent collaboration...");
    const { agentState } = state;
    
    // Initialize collaboration context if it doesn't exist
    if (!agentState.collaborationContext) {
      agentState.collaborationContext = {
        topic: agentState.topicFocus,
        collaborationHistory: [],
        agentContributions: {},
        currentPhase: 'planning'
      };
    }
    
    // Set up message bus and subscribe to messages
    const messageBus = AgentMessageBus.getInstance();
    
    // Subscribe to messages for content generation agent
    messageBus.subscribeToMessages('contentGeneration', async (message) => {
      console.log(`Content Generation Agent received message from ${message.from}:`, message.type);
      
      // Process the message based on its type
      if (message.type === 'provide_information') {
        // Update state with the provided information
        updateStateWithAgentContribution(agentState, message.from, message.content);
        
        // Record in collaboration history
        agentState.collaborationContext.collaborationHistory.push({
          phase: agentState.collaborationContext.currentPhase,
          action: `received_information_from_${message.from}`,
          messageId: message.id,
          timestamp: message.timestamp
        });
      } else if (message.type === 'provide_feedback') {
        // Update state with the feedback
        if (message.from === 'editorial') {
          agentState.editorialResult = message.content;
        } else if (message.from === 'seoOptimization') {
          agentState.seoOptimizationResult = message.content;
        }
        
        // Record in collaboration history
        agentState.collaborationContext.collaborationHistory.push({
          phase: agentState.collaborationContext.currentPhase,
          action: `received_feedback_from_${message.from}`,
          messageId: message.id,
          timestamp: message.timestamp
        });
      }
    });
    
    // Start with market research
    const marketResearchMessageId = await requestInformationFromAgent(
      'contentGeneration',
      'marketResearch',
      `What market insights should we gather for "${agentState.topicFocus}"?`,
      {
        contentType: agentState.contentType,
        targetAudience: agentState.targetAudience
      }
    );
    
    // Record in collaboration history
    agentState.collaborationContext.collaborationHistory.push({
      phase: 'planning',
      action: 'requested_market_research',
      messageId: marketResearchMessageId,
      timestamp: new Date().toISOString()
    });
    
    // Wait for market research results (in a real implementation, this would be asynchronous)
    // For now, we'll simulate waiting with a timeout
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Request SEO keywords based on market research
    const seoKeywordMessageId = await requestInformationFromAgent(
      'contentGeneration',
      'seoKeyword',
      `What keywords should we target for "${agentState.topicFocus}"?`,
      {
        marketResearchResult: agentState.marketResearchResult,
        contentType: agentState.contentType,
        targetAudience: agentState.targetAudience
      }
    );
    
    // Record in collaboration history
    agentState.collaborationContext.collaborationHistory.push({
      phase: 'planning',
      action: 'requested_seo_keywords',
      messageId: seoKeywordMessageId,
      timestamp: new Date().toISOString()
    });
    
    // Wait for SEO keyword results
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Request content strategy based on market research and SEO keywords
    const contentStrategyMessageId = await requestCollaborationFromAgent(
      'contentGeneration',
      'contentStrategy',
      `Develop a content strategy for "${agentState.topicFocus}"`,
      {
        marketResearchResult: agentState.marketResearchResult,
        seoKeywordResult: agentState.seoKeywordResult,
        contentType: agentState.contentType,
        targetAudience: agentState.targetAudience
      }
    );
    
    // Record in collaboration history
    agentState.collaborationContext.collaborationHistory.push({
      phase: 'planning',
      action: 'requested_content_strategy',
      messageId: contentStrategyMessageId,
      timestamp: new Date().toISOString()
    });
    
    // Wait for content strategy results
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Update phase to creation
    agentState.collaborationContext.currentPhase = 'creation';
    
    // Generate content based on all inputs
    // This would be the actual content generation logic
    agentState.contentResult = {
      title: `${agentState.topicFocus}: A Comprehensive Guide`,
      metaDescription: `Learn everything about ${agentState.topicFocus} in this comprehensive guide for ${agentState.targetAudience}.`,
      sections: [
        {
          title: "Introduction",
          content: `This is an introduction to ${agentState.topicFocus}.`
        },
        {
          title: "Key Insights",
          content: `These are the key insights about ${agentState.topicFocus}.`
        },
        {
          title: "Conclusion",
          content: `This is the conclusion about ${agentState.topicFocus}.`
        }
      ]
    };
    
    // Record in collaboration history
    agentState.collaborationContext.collaborationHistory.push({
      phase: 'creation',
      action: 'generated_content',
      timestamp: new Date().toISOString()
    });
    
    // Update phase to review
    agentState.collaborationContext.currentPhase = 'review';
    
    // Request editorial review
    const editorialMessageId = await requestFeedbackFromAgent(
      'contentGeneration',
      'editorial',
      agentState.contentResult,
      `Review this content about "${agentState.topicFocus}" for clarity, coherence, and grammar.`
    );
    
    // Record in collaboration history
    agentState.collaborationContext.collaborationHistory.push({
      phase: 'review',
      action: 'requested_editorial_review',
      messageId: editorialMessageId,
      timestamp: new Date().toISOString()
    });
    
    // Wait for editorial feedback
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Request SEO optimization
    const seoOptimizationMessageId = await requestFeedbackFromAgent(
      'contentGeneration',
      'seoOptimization',
      agentState.contentResult,
      `Optimize this content about "${agentState.topicFocus}" for search engines.`,
      {
        seoKeywordResult: agentState.seoKeywordResult
      }
    );
    
    // Record in collaboration history
    agentState.collaborationContext.collaborationHistory.push({
      phase: 'review',
      action: 'requested_seo_optimization',
      messageId: seoOptimizationMessageId,
      timestamp: new Date().toISOString()
    });
    
    // Wait for SEO optimization feedback
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Update phase to completed
    agentState.collaborationContext.currentPhase = 'completed';
    
    // Finalize content based on all feedback
    const finalContent = agentState.seoOptimizationResult?.optimizedContent || 
                         agentState.editorialResult?.editedContent ||
                         agentState.contentResult;
    
    // Update the content result with the final version
    agentState.contentResult = finalContent;
    
    // Record in collaboration history
    agentState.collaborationContext.collaborationHistory.push({
      phase: 'completed',
      action: 'finalized_content',
      timestamp: new Date().toISOString()
    });
    
    return { agentState };
  } catch (error) {
    console.error("Error in enhanced content generation agent:", error);
    throw error;
  }
}

// Update the ContentGenerationState interface in contentGeneration.ts to include:
/*
collaborationContext?: {
  topic: string;
  collaborationHistory: Array<{
    phase: string;
    action: string;
    timestamp: string;
    [key: string]: any;
  }>;
  agentContributions: Record<string, {
    contribution?: any;
    timestamp: string;
    [key: string]: any;
  }>;
  currentPhase: 'planning' | 'creation' | 'review' | 'completed';
};
*/