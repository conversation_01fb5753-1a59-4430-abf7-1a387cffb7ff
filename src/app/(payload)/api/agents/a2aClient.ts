// src/app/(payload)/api/agents/a2aClient.ts

/**
 * A2A Client - Agent-to-Agent Communication Protocol
 * 
 * This client enables communication between agents using the A2A protocol.
 * It supports sending tasks to agents and receiving responses.
 */

export interface A2AClientOptions {
  baseUrl?: string;
  apiKey?: string;
  timeout?: number;
}

export interface AgentCard {
  name: string;
  description: string;
  version: string;
  capabilities: string[];
  authentication?: {
    schemes: Array<{
      type: string;
      [key: string]: any;
    }>;
  };
}

export interface A2AMessage {
  id: string;
  timestamp: string;
  from: string;
  to: string;
  type: string;
  content: any;
  replyTo?: string;
}

export interface A2ATask {
  id: string;
  history: Array<{
    role: 'user' | 'agent';
    parts: Array<{
      type: string;
      [key: string]: any;
    }>;
  }>;
  status?: {
    state: 'pending' | 'running' | 'completed' | 'failed';
    timestamp: string;
    message?: {
      role: 'agent';
      parts: Array<{
        type: string;
        [key: string]: any;
      }>;
    };
  };
  metadata?: Record<string, any>;
}

/**
 * A2A Client for agent-to-agent communication
 */
export class A2AClient {
  private baseUrl: string;
  private apiKey?: string;
  private timeout: number;

  constructor(options: A2AClientOptions = {}) {
    this.baseUrl = options.baseUrl || '';
    this.apiKey = options.apiKey;
    this.timeout = options.timeout || 60000; // Default 60s timeout
  }

  /**
   * Discover an agent by its URL
   */
  async discoverAgent(agentUrl: string): Promise<AgentCard> {
    const url = new URL(agentUrl);
    url.pathname = `${url.pathname.replace(/\/$/, '')}/.well-known/agent.json`;
    
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: this.getHeaders(),
      signal: AbortSignal.timeout(this.timeout),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to discover agent: ${response.statusText}`);
    }
    
    return await response.json();
  }

  /**
   * Send a task to an agent
   */
  async sendTask(agentUrl: string, task: Partial<A2ATask>): Promise<A2ATask> {
    const response = await fetch(agentUrl, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'tasks/send',
        params: { task },
        id: Math.floor(Math.random() * 10000)
      }),
      signal: AbortSignal.timeout(this.timeout),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to send task: ${response.statusText}`);
    }
    
    const result = await response.json();
    
    if (result.error) {
      throw new Error(`Agent error: ${result.error.message}`);
    }
    
    return result.result.task;
  }

  /**
   * Get a task by ID
   */
  async getTask(agentUrl: string, taskId: string): Promise<A2ATask> {
    const response = await fetch(agentUrl, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'tasks/get',
        params: { id: taskId },
        id: Math.floor(Math.random() * 10000)
      }),
      signal: AbortSignal.timeout(this.timeout),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to get task: ${response.statusText}`);
    }
    
    const result = await response.json();
    
    if (result.error) {
      throw new Error(`Agent error: ${result.error.message}`);
    }
    
    return result.result.task;
  }

  /**
   * Cancel a task
   */
  async cancelTask(agentUrl: string, taskId: string): Promise<boolean> {
    const response = await fetch(agentUrl, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'tasks/cancel',
        params: { id: taskId },
        id: Math.floor(Math.random() * 10000)
      }),
      signal: AbortSignal.timeout(this.timeout),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to cancel task: ${response.statusText}`);
    }
    
    const result = await response.json();
    
    if (result.error) {
      throw new Error(`Agent error: ${result.error.message}`);
    }
    
    return result.result.success;
  }

  /**
   * Send a message to another agent
   */
  async sendMessage(targetAgentUrl: string, message: Omit<A2AMessage, 'id' | 'timestamp'>): Promise<A2AMessage> {
    const fullMessage: A2AMessage = {
      ...message,
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      timestamp: new Date().toISOString(),
    };
    
    const task: Partial<A2ATask> = {
      id: `task_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      history: [
        {
          role: 'user',
          parts: [
            {
              type: 'agent_message',
              message: fullMessage,
            }
          ]
        }
      ],
      metadata: {
        messageId: fullMessage.id,
        messageType: fullMessage.type,
      }
    };
    
    const result = await this.sendTask(targetAgentUrl, task);
    
    // Extract response message from the task result
    if (result.status?.state === 'completed' && result.status.message) {
      const responsePart = result.status.message.parts.find(part => part.type === 'agent_message');
      if (responsePart && responsePart.message) {
        return responsePart.message as A2AMessage;
      }
    }
    
    throw new Error('No valid response message received from agent');
  }

  /**
   * Get headers for API requests
   */
  private getHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
    }
    
    return headers;
  }
}

/**
 * Message Bus for agent communication
 */
export class AgentMessageBus {
  private subscribers: Map<string, Array<(message: A2AMessage) => Promise<void>>> = new Map();
  private client: A2AClient;
  
  constructor(clientOptions: A2AClientOptions = {}) {
    this.client = new A2AClient(clientOptions);
  }
  
  /**
   * Subscribe to messages for a specific agent
   */
  subscribe(agentId: string, callback: (message: A2AMessage) => Promise<void>) {
    if (!this.subscribers.has(agentId)) {
      this.subscribers.set(agentId, []);
    }
    
    this.subscribers.get(agentId)!.push(callback);
  }
  
  /**
   * Publish a message to an agent
   */
  async publish(message: Omit<A2AMessage, 'id' | 'timestamp'>, targetAgentUrl: string): Promise<A2AMessage> {
    try {
      return await this.client.sendMessage(targetAgentUrl, message);
    } catch (error) {
      console.error(`Failed to publish message to ${message.to}:`, error);
      throw error;
    }
  }
  
  /**
   * Process an incoming message
   */
  async processMessage(message: A2AMessage): Promise<void> {
    const targetAgentId = message.to;
    
    if (!this.subscribers.has(targetAgentId)) {
      console.warn(`No subscribers found for agent ${targetAgentId}`);
      return;
    }
    
    const callbacks = this.subscribers.get(targetAgentId)!;
    
    // Process the message with all subscribers
    await Promise.all(
      callbacks.map(callback => 
        callback(message).catch(error => 
          console.error(`Error processing message in subscriber:`, error)
        )
      )
    );
  }
  
  /**
   * Request information from another agent
   */
  async requestInformation(
    fromAgentId: string, 
    toAgentId: string, 
    targetAgentUrl: string, 
    query: string, 
    replyToId?: string
  ): Promise<any> {
    const message: Omit<A2AMessage, 'id' | 'timestamp'> = {
      from: fromAgentId,
      to: toAgentId,
      type: 'request_information',
      content: { query },
      replyTo: replyToId
    };
    
    const response = await this.publish(message, targetAgentUrl);
    
    if (response.type === 'provide_information') {
      return response.content;
    }
    
    throw new Error(`Unexpected response type: ${response.type}`);
  }
  
  /**
   * Request feedback from another agent
   */
  async requestFeedback(
    fromAgentId: string, 
    toAgentId: string, 
    targetAgentUrl: string, 
    content: any, 
    replyToId?: string
  ): Promise<any> {
    const message: Omit<A2AMessage, 'id' | 'timestamp'> = {
      from: fromAgentId,
      to: toAgentId,
      type: 'request_feedback',
      content,
      replyTo: replyToId
    };
    
    const response = await this.publish(message, targetAgentUrl);
    
    if (response.type === 'provide_feedback') {
      return response.content;
    }
    
    throw new Error(`Unexpected response type: ${response.type}`);
  }
  
  /**
   * Request collaboration from another agent
   */
  async requestCollaboration(
    fromAgentId: string, 
    toAgentId: string, 
    targetAgentUrl: string, 
    task: any, 
    replyToId?: string
  ): Promise<any> {
    const message: Omit<A2AMessage, 'id' | 'timestamp'> = {
      from: fromAgentId,
      to: toAgentId,
      type: 'request_collaboration',
      content: { task },
      replyTo: replyToId
    };
    
    const response = await this.publish(message, targetAgentUrl);
    
    if (response.type === 'collaboration_response') {
      return response.content;
    }
    
    throw new Error(`Unexpected response type: ${response.type}`);
  }
}

// Create a singleton instance of the message bus
export const messageBus = new AgentMessageBus();

// Export helper functions for agent communication
export async function generateInformation(query: string, agentType: string, agentState?: any): Promise<any> {
  // This function will be implemented in each agent to handle information requests
  console.log(`Generating information for query: ${query} in agent: ${agentType}`);
  
  // Each agent will implement its own version of this function
  return {
    message: `Information generated for query: ${query}`,
    data: {}
  };
}

export async function provideFeedback(content: any, agentType: string, agentState?: any): Promise<any> {
  // This function will be implemented in each agent to handle feedback requests
  console.log(`Providing feedback for content in agent: ${agentType}`);
  
  // Each agent will implement its own version of this function
  return {
    message: `Feedback provided for content`,
    suggestions: [],
    score: 0
  };
}

export async function collaborateOnTask(task: any, agentType: string, agentState?: any): Promise<any> {
  // This function will be implemented in each agent to handle collaboration requests
  console.log(`Collaborating on task in agent: ${agentType}`);
  
  // Each agent will implement its own version of this function
  return {
    message: `Collaboration completed for task`,
    result: {}
  };
}