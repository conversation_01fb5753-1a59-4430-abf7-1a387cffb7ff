/**
 * Feedback Loop System
 * 
 * This class enables agents to evaluate each other's work,
 * request revisions, and track feedback cycles.
 */

import { v4 as uuidv4 } from 'uuid';
import { stateStore } from '../collaborative-iteration/utils/stateStore';
import logger from '../collaborative-iteration/utils/logger';
import { 
  DynamicAgentMessage, 
  DynamicMessageType,
  FeedbackData,
  FeedbackCycle
} from './types';

export class FeedbackLoopSystem {
  private sessionId: string;
  
  constructor(sessionId: string) {
    this.sessionId = sessionId;
  }
  
  /**
   * Request feedback on an artifact
   * @param fromAgent The agent requesting feedback
   * @param toAgent The agent to provide feedback
   * @param artifactId The artifact to get feedback on
   * @param specificAreas Optional specific areas to focus feedback on
   * @returns The message ID of the feedback request
   */
  public async requestFeedback(
    fromAgent: string,
    toAgent: string,
    artifactId: string,
    specificAreas?: string[]
  ): Promise<string> {
    try {
      const state = await stateStore.getState(this.sessionId);
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }
      
      const artifact = state.artifacts?.[artifactId];
      if (!artifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }
      
      // Create feedback request message
      const requestMessage: DynamicAgentMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: fromAgent,
        to: toAgent,
        type: DynamicMessageType.REQUEST_FEEDBACK,
        content: {
          artifactId,
          artifactType: artifact.type,
          artifactName: artifact.name,
          specificAreas,
          currentVersion: artifact.currentVersion
        },
        conversationId: uuidv4(),
        reasoning: {
          thoughts: [
            `Need feedback on ${artifact.type} artifact`,
            `${toAgent} has expertise relevant to this artifact`
          ],
          considerations: [
            'Feedback will improve artifact quality',
            'Specific areas of focus will get targeted feedback'
          ],
          decision: `Request feedback from ${toAgent} on ${artifact.type} artifact`,
          confidence: 0.9
        }
      };
      
      // Store the message
      await stateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;
        
        const dynamicMessages = { ...currentState.dynamicMessages };
        dynamicMessages[requestMessage.id] = requestMessage;
        
        // Add to conversations
        const conversations = { ...currentState.conversations };
        if (!conversations[requestMessage.conversationId]) {
          conversations[requestMessage.conversationId] = [];
        }
        conversations[requestMessage.conversationId].push(requestMessage.id);
        
        return {
          ...currentState,
          dynamicMessages,
          conversations
        };
      });
      
      // Create or update feedback cycle
      await this.createFeedbackCycleRequest(artifactId, requestMessage.id, fromAgent, toAgent);
      
      logger.info(`Feedback requested for artifact ${artifactId}`, {
        sessionId: this.sessionId,
        artifactId,
        fromAgent,
        toAgent,
        messageId: requestMessage.id
      });
      
      return requestMessage.id;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error requesting feedback`, {
        sessionId: this.sessionId,
        artifactId,
        fromAgent,
        toAgent,
        error: err.message || String(error),
        stack: err.stack
      });
      throw error;
    }
  }
  
  /**
   * Provide feedback on an artifact
   * @param fromAgent The agent providing feedback
   * @param toAgent The agent who requested feedback
   * @param artifactId The artifact being evaluated
   * @param feedback The feedback data
   * @param requestId The ID of the feedback request message
   * @returns The message ID of the feedback response
   */
  public async provideFeedback(
    fromAgent: string,
    toAgent: string,
    artifactId: string,
    feedback: FeedbackData,
    requestId: string
  ): Promise<string> {
    try {
      const state = await stateStore.getState(this.sessionId);
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }
      
      const artifact = state.artifacts?.[artifactId];
      if (!artifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }
      
      // Create feedback response message
      const feedbackMessage: DynamicAgentMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: fromAgent,
        to: toAgent,
        type: DynamicMessageType.PROVIDE_FEEDBACK,
        content: {
          artifactId,
          artifactType: artifact.type,
          artifactName: artifact.name,
          feedback
        },
        replyTo: requestId,
        conversationId: await this.getConversationId(requestId),
        reasoning: feedback.reasoning
      };
      
      // Store the message
      await stateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;
        
        const dynamicMessages = { ...currentState.dynamicMessages };
        dynamicMessages[feedbackMessage.id] = feedbackMessage;
        
        // Add to conversations
        const conversations = { ...currentState.conversations };
        const conversationId = feedbackMessage.conversationId;
        if (!conversations[conversationId]) {
          conversations[conversationId] = [];
        }
        conversations[conversationId].push(feedbackMessage.id);
        
        return {
          ...currentState,
          dynamicMessages,
          conversations
        };
      });
      
      // Update feedback cycle
      await this.updateFeedbackCycleWithResponse(
        artifactId, 
        requestId, 
        feedbackMessage.id, 
        feedback.overallRating
      );
      
      logger.info(`Feedback provided for artifact ${artifactId}`, {
        sessionId: this.sessionId,
        artifactId,
        fromAgent,
        toAgent,
        messageId: feedbackMessage.id,
        rating: feedback.overallRating
      });
      
      return feedbackMessage.id;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error providing feedback`, {
        sessionId: this.sessionId,
        artifactId,
        fromAgent,
        toAgent,
        requestId,
        error: err.message || String(error),
        stack: err.stack
      });
      throw error;
    }
  }
  
  /**
   * Incorporate feedback into an artifact
   * @param agentId The agent incorporating the feedback
   * @param artifactId The artifact to update
   * @param feedbackIds The IDs of the feedback messages to incorporate
   * @param changes Description of the changes made
   * @returns True if the feedback was incorporated successfully
   */
  public async incorporateFeedback(
    agentId: string,
    artifactId: string,
    feedbackIds: string[],
    changes: string
  ): Promise<boolean> {
    try {
      const state = await stateStore.getState(this.sessionId);
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }
      
      // Update feedback cycles to mark feedback as incorporated
      await stateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;
        
        const feedbackCycles = { ...currentState.feedbackCycles };
        const cycle = feedbackCycles[artifactId];
        
        if (cycle) {
          const updatedCycle = { ...cycle };
          updatedCycle.cycles = updatedCycle.cycles.map(c => {
            if (feedbackIds.includes(c.feedbackId || '')) {
              return {
                ...c,
                incorporated: true,
                incorporatedTimestamp: new Date().toISOString()
              };
            }
            return c;
          });
          
          feedbackCycles[artifactId] = updatedCycle;
        }
        
        return {
          ...currentState,
          feedbackCycles
        };
      });
      
      // Create feedback incorporation message
      const incorporationMessage: DynamicAgentMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: agentId,
        to: 'all',
        type: DynamicMessageType.SYSTEM_MESSAGE,
        content: {
          event: 'FEEDBACK_INCORPORATED',
          artifactId,
          feedbackIds,
          changes
        },
        conversationId: uuidv4(),
        reasoning: {
          thoughts: [
            'Feedback has been reviewed and incorporated',
            'Changes improve the artifact quality'
          ],
          considerations: [
            'Prioritized most important feedback points',
            'Balanced different feedback suggestions'
          ],
          decision: 'Incorporate feedback to improve artifact quality',
          confidence: 0.9
        }
      };
      
      // Store the message
      await stateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;
        
        const dynamicMessages = { ...currentState.dynamicMessages };
        dynamicMessages[incorporationMessage.id] = incorporationMessage;
        
        return {
          ...currentState,
          dynamicMessages
        };
      });
      
      logger.info(`Feedback incorporated for artifact ${artifactId}`, {
        sessionId: this.sessionId,
        artifactId,
        agentId,
        feedbackCount: feedbackIds.length
      });
      
      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error incorporating feedback`, {
        sessionId: this.sessionId,
        artifactId,
        agentId,
        feedbackIds,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }
  
  /**
   * Get statistics about feedback cycles for an artifact
   * @param artifactId The artifact to get stats for
   * @returns Statistics about the feedback cycles
   */
  public async trackFeedbackCycle(artifactId: string): Promise<{
    cyclesCompleted: number;
    averageRating: number;
    improvementTrend: number;
    timeToIncorporate: number;
  }> {
    try {
      const state = await stateStore.getState(this.sessionId);
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }
      
      const feedbackCycle = state.feedbackCycles?.[artifactId];
      if (!feedbackCycle) {
        return {
          cyclesCompleted: 0,
          averageRating: 0,
          improvementTrend: 0,
          timeToIncorporate: 0
        };
      }
      
      // Count completed cycles
      const completedCycles = feedbackCycle.cycles.filter(
        c => c.feedbackId && c.incorporated
      );
      
      // Calculate average rating
      const ratings = completedCycles
        .map(c => c.rating || 0)
        .filter(r => r > 0);
      
      const averageRating = ratings.length > 0
        ? ratings.reduce((sum, r) => sum + r, 0) / ratings.length
        : 0;
      
      // Calculate improvement trend
      let improvementTrend = 0;
      if (ratings.length >= 2) {
        const firstHalf = ratings.slice(0, Math.floor(ratings.length / 2));
        const secondHalf = ratings.slice(Math.floor(ratings.length / 2));
        
        const firstHalfAvg = firstHalf.reduce((sum, r) => sum + r, 0) / firstHalf.length;
        const secondHalfAvg = secondHalf.reduce((sum, r) => sum + r, 0) / secondHalf.length;
        
        improvementTrend = secondHalfAvg - firstHalfAvg;
      }
      
      // Calculate average time to incorporate feedback
      const incorporationTimes = completedCycles
        .filter(c => c.feedbackTimestamp && c.incorporatedTimestamp)
        .map(c => {
          const feedbackTime = new Date(c.feedbackTimestamp!).getTime();
          const incorporatedTime = new Date(c.incorporatedTimestamp!).getTime();
          return (incorporatedTime - feedbackTime) / 1000; // in seconds
        });
      
      const timeToIncorporate = incorporationTimes.length > 0
        ? incorporationTimes.reduce((sum, t) => sum + t, 0) / incorporationTimes.length
        : 0;
      
      return {
        cyclesCompleted: completedCycles.length,
        averageRating,
        improvementTrend,
        timeToIncorporate
      };
    } catch (error) {
      const err = error as Error;
      logger.error(`Error tracking feedback cycle`, {
        sessionId: this.sessionId,
        artifactId,
        error: err.message || String(error),
        stack: err.stack
      });
      
      return {
        cyclesCompleted: 0,
        averageRating: 0,
        improvementTrend: 0,
        timeToIncorporate: 0
      };
    }
  }
  
  /**
   * Create a new feedback cycle request entry
   * @param artifactId The artifact ID
   * @param requestId The feedback request message ID
   * @param fromAgent The agent requesting feedback
   * @param toAgent The agent to provide feedback
   */
  private async createFeedbackCycleRequest(
    artifactId: string,
    requestId: string,
    fromAgent: string,
    toAgent: string
  ): Promise<void> {
    await stateStore.updateState(this.sessionId, (currentState) => {
      if (!currentState) return currentState;
      
      const feedbackCycles = { ...currentState.feedbackCycles };
      
      // Create or update the feedback cycle for this artifact
      if (!feedbackCycles[artifactId]) {
        feedbackCycles[artifactId] = {
          artifactId,
          cycles: []
        };
      }
      
      // Add the new cycle entry
      feedbackCycles[artifactId].cycles.push({
        requestId,
        requestTimestamp: new Date().toISOString(),
        fromAgent,
        toAgent,
        incorporated: false
      });
      
      return {
        ...currentState,
        feedbackCycles
      };
    });
  }
  
  /**
   * Update a feedback cycle with response information
   * @param artifactId The artifact ID
   * @param requestId The feedback request message ID
   * @param feedbackId The feedback response message ID
   * @param rating The feedback rating
   */
  private async updateFeedbackCycleWithResponse(
    artifactId: string,
    requestId: string,
    feedbackId: string,
    rating?: number
  ): Promise<void> {
    await stateStore.updateState(this.sessionId, (currentState) => {
      if (!currentState) return currentState;
      
      const feedbackCycles = { ...currentState.feedbackCycles };
      const cycle = feedbackCycles[artifactId];
      
      if (cycle) {
        // Find the cycle entry for this request
        const updatedCycle = { ...cycle };
        updatedCycle.cycles = updatedCycle.cycles.map(c => {
          if (c.requestId === requestId) {
            return {
              ...c,
              feedbackId,
              feedbackTimestamp: new Date().toISOString(),
              rating
            };
          }
          return c;
        });
        
        feedbackCycles[artifactId] = updatedCycle;
      }
      
      return {
        ...currentState,
        feedbackCycles
      };
    });
  }
  
  /**
   * Get the conversation ID for a message
   * @param messageId The message ID to look up
   * @returns The conversation ID or a new UUID if not found
   */
  private async getConversationId(messageId: string): Promise<string> {
    const state = await stateStore.getState(this.sessionId);
    if (!state) {
      return uuidv4();
    }
    
    const dynamicMessages = state.dynamicMessages || {};
    const message = dynamicMessages[messageId];
    
    return message?.conversationId || uuidv4();
  }
}
