import { NextRequest, NextResponse } from 'next/server';
import { logger } from '@/lib/logger';
import { getStateStore } from '@/lib/state-store';
import { IterativeMessageType } from '@/app/(payload)/api/agents/collaborative-iteration/types';
import { dynamicCollaborationClientV2 } from '@/lib/dynamic-collaboration-client-v2';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { sessionId, artifactId, feedback, from, timestamp } = body;

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    if (!artifactId) {
      return NextResponse.json(
        { error: 'Artifact ID is required' },
        { status: 400 }
      );
    }

    if (!feedback) {
      return NextResponse.json(
        { error: 'Feedback content is required' },
        { status: 400 }
      );
    }

    // Get the current state
    const stateStore = getStateStore();
    const state = await stateStore.getState(sessionId);

    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Get the artifact
    const artifact = state.artifacts?.[artifactId];
    if (!artifact) {
      return NextResponse.json(
        { error: 'Artifact not found' },
        { status: 404 }
      );
    }

    // Create feedback object
    const feedbackObj = {
      id: `feedback-${Date.now()}`,
      from: from || 'User',
      timestamp: timestamp || new Date().toISOString(),
      content: feedback
    };

    // Add feedback to the artifact
    if (!artifact.feedback) {
      artifact.feedback = [];
    }
    artifact.feedback.push(feedbackObj);

    // If the artifact has iterations, add feedback to the latest iteration as well
    if (artifact.iterations && artifact.iterations.length > 0) {
      const latestIteration = artifact.iterations[artifact.iterations.length - 1];
      if (!latestIteration.feedback) {
        latestIteration.feedback = [];
      }
      latestIteration.feedback.push(feedbackObj);
    }

    // Update the artifact in the state
    state.artifacts[artifactId] = artifact;

    // Save the updated state
    await stateStore.setState(sessionId, state);

    // Send feedback message to the agent that created the artifact
    if (artifact.creator) {
      try {
        await dynamicCollaborationClientV2.sendMessage(sessionId, {
          type: IterativeMessageType.FEEDBACK,
          from: from || 'User',
          to: artifact.creator,
          content: feedback,
          artifactId: artifactId,
          timestamp: timestamp || new Date().toISOString()
        });
      } catch (error) {
        logger.error('Error sending feedback message to agent', {
          error,
          sessionId,
          artifactId,
          agentId: artifact.creator
        });
        // Continue even if sending the message fails
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Feedback added successfully',
      feedback: feedbackObj
    });
  } catch (error) {
    logger.error('Error adding feedback', { error });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
