/**
 * Dynamic Collaboration System (V2)
 * 
 * This file exports all components of the dynamic collaboration system.
 */

// Export types
export * from './types';

// Export main components
export { DynamicWorkflowOrchestrator } from './dynamic-workflow-orchestrator';
export { GoalManager } from './goal-manager';
export { AgentCommunicationHub } from './agent-communication-hub';
export { ArtifactDecisionFramework } from './artifact-decision-framework';
export { FeedbackLoopSystem } from './feedback-loop-system';

// Main entry point for the dynamic collaboration system
import { DynamicWorkflowOrchestrator } from './dynamic-workflow-orchestrator';
import { ContentGenerationParams } from './types';
import { v4 as uuidv4 } from 'uuid';
import logger from '../collaborative-iteration/utils/logger';

/**
 * Initialize a new dynamic collaboration session
 * @param params Content generation parameters
 * @returns The session ID
 */
export async function initiateDynamicCollaboration(
  params: ContentGenerationParams
): Promise<string> {
  try {
    // Generate a new session ID
    const sessionId = uuidv4();
    
    // Initialize the workflow
    const success = await DynamicWorkflowOrchestrator.initiate(sessionId, params);
    
    if (!success) {
      throw new Error('Failed to initialize dynamic collaboration workflow');
    }
    
    logger.info(`Dynamic collaboration session initiated`, {
      sessionId,
      topic: params.topic,
      contentType: params.contentType
    });
    
    return sessionId;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error initiating dynamic collaboration`, {
      error: err.message || String(error),
      stack: err.stack
    });
    throw error;
  }
}

/**
 * Get a workflow orchestrator instance for an existing session
 * @param sessionId The session ID
 * @returns A new DynamicWorkflowOrchestrator instance
 */
export function getWorkflowOrchestrator(sessionId: string): DynamicWorkflowOrchestrator {
  return new DynamicWorkflowOrchestrator(sessionId);
}

/**
 * API route handler for initiating a dynamic collaboration session
 * @param req The request object
 * @param res The response object
 */
export async function handleDynamicCollaborationRequest(req: any, res: any): Promise<void> {
  try {
    if (req.method !== 'POST') {
      res.status(405).json({ error: 'Method not allowed' });
      return;
    }
    
    const params = req.body as ContentGenerationParams;
    
    // Validate required parameters
    if (!params.topic || !params.contentType || !params.targetAudience) {
      res.status(400).json({ 
        error: 'Missing required parameters',
        requiredParams: ['topic', 'contentType', 'targetAudience']
      });
      return;
    }
    
    // Initiate the collaboration
    const sessionId = await initiateDynamicCollaboration(params);
    
    // Return the session ID
    res.status(200).json({ 
      success: true,
      sessionId,
      message: 'Dynamic collaboration session initiated successfully'
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error handling dynamic collaboration request`, {
      error: err.message || String(error),
      stack: err.stack
    });
    
    res.status(500).json({ 
      error: 'Internal server error',
      message: err.message || 'An unexpected error occurred'
    });
  }
}
