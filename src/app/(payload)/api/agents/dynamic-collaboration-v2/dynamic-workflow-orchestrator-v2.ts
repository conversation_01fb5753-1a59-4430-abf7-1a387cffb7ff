/**
 * Dynamic Workflow Orchestrator V2
 *
 * This class orchestrates the dynamic collaboration between agents,
 * managing the overall workflow and coordinating agent activities.
 * It uses the dedicated state manager for dynamic collaboration.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../collaborative-iteration/utils/logger';
import {
  DynamicCollaborationState,
  ContentGenerationParams,
  DynamicWorkflowPhase,
  DynamicMessageType,
  GoalType,
  GoalStatus,
  createStateManager,
  DynamicStateManager,
  dynamicStateStore
} from './state';
import { IterativeArtifact } from '../collaborative-iteration/types';
import {
  marketResearchAgent,
  seoKeywordAgent,
  contentStrategyAgent,
  contentGenerationAgent,
  seoOptimizationAgent
} from '../collaborative-iteration/agents';
import { GoalManager } from './goal-manager';

export class DynamicWorkflowOrchestratorV2 {
  private sessionId: string;
  private stateManager: DynamicStateManager;
  private goalManager: GoalManager;

  constructor(sessionId: string) {
    this.sessionId = sessionId;
    this.stateManager = createStateManager(sessionId);
    this.goalManager = new GoalManager(sessionId);
  }

  /**
   * Initialize a new collaboration session
   * @param sessionId The session ID
   * @param params The content generation parameters
   * @returns Promise<boolean> indicating success or failure
   */
  public static async initiate(sessionId: string, params: ContentGenerationParams): Promise<boolean> {
    try {
      logger.info(`Initializing dynamic collaboration session`, {
        sessionId,
        topic: params.topic
      });

      // Create state manager
      const stateManager = createStateManager(sessionId);

      // Initialize session
      await stateManager.initializeSession(params);

      // Create a new orchestrator instance
      const orchestrator = new DynamicWorkflowOrchestratorV2(sessionId);

      // Initialize the research phase
      await orchestrator.transitionToPhase(DynamicWorkflowPhase.RESEARCH);

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error initializing dynamic collaboration session`, {
        sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Get the current state of the session
   * @returns Promise<DynamicCollaborationState | null>
   */
  public async getState(): Promise<DynamicCollaborationState | null> {
    return await this.stateManager.getState();
  }

  /**
   * Transition to a new phase
   * @param phase The phase to transition to
   * @returns Promise<boolean> indicating success or failure
   */
  public async transitionToPhase(phase: DynamicWorkflowPhase): Promise<boolean> {
    try {
      logger.info(`Transitioning to phase: ${phase}`, {
        sessionId: this.sessionId,
        phase
      });

      // Update the phase in the state
      await this.stateManager.updatePhase(phase);

      // Trigger phase-specific activities
      await this.initiatePhaseActivities(phase);

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error transitioning to phase: ${phase}`, {
        sessionId: this.sessionId,
        phase,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Initiate activities for a specific phase
   * @param phase The phase to initiate activities for
   * @returns Promise<boolean> indicating success or failure
   */
  private async initiatePhaseActivities(phase: DynamicWorkflowPhase): Promise<boolean> {
    try {
      logger.info(`Initiating activities for phase: ${phase}`, {
        sessionId: this.sessionId,
        phase
      });

      const state = await this.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      switch (phase) {
        case DynamicWorkflowPhase.RESEARCH:
          // Define goals for research phase
          await this.goalManager.defineGoals();

          // Assign market research goal to market research agent
          const marketResearchGoals = Object.values(state.goals).filter(
            goal => goal.type === GoalType.MARKET_RESEARCH
          );

          if (marketResearchGoals.length > 0) {
            const marketResearchGoal = marketResearchGoals[0];
            await this.stateManager.assignGoal(marketResearchGoal.id, 'market-research');

            // Trigger market research agent
            await marketResearchAgent.act(this.sessionId);

            logger.info(`Triggered market research agent for session ${this.sessionId}`, {
              sessionId: this.sessionId,
              goalId: marketResearchGoal.id
            });
          }

          // Assign keyword analysis goal to SEO keyword agent
          const keywordGoals = Object.values(state.goals).filter(
            goal => goal.type === GoalType.KEYWORD_ANALYSIS
          );

          if (keywordGoals.length > 0) {
            const keywordGoal = keywordGoals[0];
            await this.stateManager.assignGoal(keywordGoal.id, 'seo-keyword');

            // Trigger SEO keyword agent
            await seoKeywordAgent.act(this.sessionId);

            logger.info(`Triggered SEO keyword agent for session ${this.sessionId}`, {
              sessionId: this.sessionId,
              goalId: keywordGoal.id
            });
          }

          break;

        case DynamicWorkflowPhase.CREATION:
          // Assign content strategy goal to content strategy agent
          const contentStrategyGoals = Object.values(state.goals).filter(
            goal => goal.type === GoalType.CONTENT_STRATEGY
          );

          if (contentStrategyGoals.length > 0) {
            const contentStrategyGoal = contentStrategyGoals[0];
            await this.stateManager.assignGoal(contentStrategyGoal.id, 'content-strategy');

            // Trigger content strategy agent
            await contentStrategyAgent.act(this.sessionId);

            logger.info(`Triggered content strategy agent for session ${this.sessionId}`, {
              sessionId: this.sessionId,
              goalId: contentStrategyGoal.id
            });
          }

          break;

        case DynamicWorkflowPhase.REVIEW:
          // Assign content creation goal to content generation agent
          const contentCreationGoals = Object.values(state.goals).filter(
            goal => goal.type === GoalType.CONTENT_CREATION
          );

          if (contentCreationGoals.length > 0) {
            const contentCreationGoal = contentCreationGoals[0];
            await this.stateManager.assignGoal(contentCreationGoal.id, 'content-generation');

            // Trigger content generation agent
            await contentGenerationAgent.act(this.sessionId);

            logger.info(`Triggered content generation agent for session ${this.sessionId}`, {
              sessionId: this.sessionId,
              goalId: contentCreationGoal.id
            });
          }

          break;

        case DynamicWorkflowPhase.FINALIZATION:
          // Assign SEO optimization goal to SEO optimization agent
          const seoOptimizationGoals = Object.values(state.goals).filter(
            goal => goal.type === GoalType.SEO_OPTIMIZATION
          );

          if (seoOptimizationGoals.length > 0) {
            const seoOptimizationGoal = seoOptimizationGoals[0];
            await this.stateManager.assignGoal(seoOptimizationGoal.id, 'seo-optimization');

            // Trigger SEO optimization agent
            await seoOptimizationAgent.act(this.sessionId);

            logger.info(`Triggered SEO optimization agent for session ${this.sessionId}`, {
              sessionId: this.sessionId,
              goalId: seoOptimizationGoal.id
            });
          }

          break;

        default:
          logger.info(`No specific activities for phase: ${phase}`, {
            sessionId: this.sessionId,
            phase
          });
      }

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error initiating activities for phase: ${phase}`, {
        sessionId: this.sessionId,
        phase,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Manually complete a goal (for testing purposes)
   * @param goalType The type of goal to complete
   * @param artifactId Optional artifact ID to associate with the goal
   * @returns Promise<boolean> indicating success or failure
   */
  public async completeGoalByType(goalType: GoalType, artifactId?: string): Promise<boolean> {
    try {
      logger.info(`Manually completing goal of type ${goalType}`, {
        sessionId: this.sessionId,
        goalType,
        artifactId
      });

      const state = await this.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      // Find a goal of the specified type
      const goalsOfType = Object.values(state.goals).filter(g => g.type === goalType);

      if (goalsOfType.length === 0) {
        logger.error(`No goals of type ${goalType} found`, {
          sessionId: this.sessionId
        });
        return false;
      }

      // Get the first goal of this type
      const goal = goalsOfType[0];
      const goalId = goal.id;

      // If no artifact ID is provided, check if there's an existing artifact for this goal type
      let finalArtifactId = artifactId;
      if (!finalArtifactId) {
        // Look for an artifact of the same type as the goal
        const matchingArtifacts = Object.values(state.artifacts || {}).filter(
          a => a.type === goalType
        );

        if (matchingArtifacts.length > 0) {
          finalArtifactId = matchingArtifacts[0].id;
          logger.info(`Found existing artifact for goal type ${goalType}`, {
            sessionId: this.sessionId,
            goalId,
            artifactId: finalArtifactId
          });
        }
      }

      // Directly update the state to complete the goal
      await dynamicStateStore.updateState(this.sessionId, (currentState: DynamicCollaborationState | null) => {
        if (!currentState) return currentState;

        // Update the goal status
        const updatedGoals = { ...currentState.goals };
        updatedGoals[goalId] = {
          ...updatedGoals[goalId],
          status: GoalStatus.COMPLETED,
          progress: 100,
          completedAt: new Date().toISOString(),
          artifactId: finalArtifactId
        };

        // Move from active to completed goals
        const activeGoals = (currentState.activeGoals || []).filter(id => id !== goalId);

        // Add to completed goals array
        const completedGoals = [...(currentState.completedGoals || [])];
        if (!completedGoals.includes(goalType)) {
          completedGoals.push(goalType);
        }

        logger.info(`Directly updating state to complete goal ${goalId} of type ${goalType}`, {
          sessionId: this.sessionId,
          goalId,
          goalType,
          artifactId: finalArtifactId,
          completedGoals: JSON.stringify(completedGoals)
        });

        return {
          ...currentState,
          goals: updatedGoals,
          activeGoals,
          completedGoals
        };
      });

      // Create goal completion message
      const messageId = uuidv4();
      const conversationId = uuidv4();
      const timestamp = new Date().toISOString();

      await this.stateManager.addSystemMessage({
        id: messageId,
        timestamp,
        from: 'system',
        to: 'all',
        type: DynamicMessageType.GOAL_UPDATE,
        content: {
          action: 'GOAL_COMPLETED',
          goalId,
          description: goal.description,
          artifactId: finalArtifactId
        },
        conversationId
      });

      logger.info(`Successfully completed goal ${goalId} of type ${goalType}`, {
        sessionId: this.sessionId,
        goalId,
        goalType,
        artifactId: finalArtifactId
      });

      // Check for phase transitions
      await this.checkGoalCompletionAndTransition();

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error completing goal by type`, {
        sessionId: this.sessionId,
        goalType,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Get the appropriate agent ID for a goal type
   * @param goalType The goal type
   * @returns The agent ID
   */
  private getAgentForGoalType(goalType: GoalType): string {
    switch (goalType) {
      case GoalType.MARKET_RESEARCH:
        return 'market-research';
      case GoalType.KEYWORD_ANALYSIS:
        return 'seo-keyword';
      case GoalType.CONTENT_STRATEGY:
        return 'content-strategy';
      case GoalType.CONTENT_CREATION:
        return 'content-generation';
      case GoalType.SEO_OPTIMIZATION:
        return 'seo-optimization';
      case GoalType.QUALITY_ASSESSMENT:
        return 'quality-assessment';
      default:
        return 'system';
    }
  }

  /**
   * Handle feedback and improve artifact
   * @param messageId The ID of the feedback message
   * @returns Promise<boolean> indicating success or failure
   */
  public async handleFeedbackAndImproveArtifact(messageId: string): Promise<boolean> {
    try {
      logger.info(`Handling feedback and improving artifact`, {
        sessionId: this.sessionId,
        messageId
      });

      const state = await this.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      const message = state.dynamicMessages?.[messageId];
      if (!message) {
        throw new Error(`Message ${messageId} not found`);
      }

      // Check if it's a feedback message
      if (message.type !== DynamicMessageType.FEEDBACK_RESPONSE &&
          message.type !== DynamicMessageType.PROVIDE_FEEDBACK) {
        throw new Error(`Message ${messageId} is not a feedback message`);
      }

      const { artifactId, goalId, evaluation } = message.content;

      // Get the artifact and goal
      const artifact = state.artifacts[artifactId];
      const goal = state.goals[goalId];

      if (!artifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }

      if (!goal) {
        throw new Error(`Goal ${goalId} not found`);
      }

      // Determine which agent should handle the improvement
      const agentId = goal.assignedTo || this.getAgentForGoalType(goal.type);

      // Create an improvement plan based on the evaluation
      const improvementPlan = this.generateImprovementPlan(artifact as unknown as IterativeArtifact, evaluation);

      // Create a message to the agent with the improvement plan
      const improvementRequestId = uuidv4();
      await this.stateManager.addSystemMessage({
        id: improvementRequestId,
        timestamp: new Date().toISOString(),
        from: 'system',
        to: agentId,
        type: DynamicMessageType.REQUEST_COLLABORATION,
        content: {
          action: 'IMPROVE_ARTIFACT',
          artifactId,
          goalId,
          evaluation,
          improvementPlan
        },
        conversationId: uuidv4()
      });

      logger.info(`Sent improvement request to agent`, {
        sessionId: this.sessionId,
        agentId,
        artifactId,
        goalId,
        messageId: improvementRequestId
      });

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling feedback and improving artifact`, {
        sessionId: this.sessionId,
        messageId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Generate an improvement plan based on evaluation
   */
  private generateImprovementPlan(artifact: IterativeArtifact, evaluation: any): string {
    // Extract the weakest criteria from the evaluation
    const weakCriteria = evaluation.criteriaResults
      .filter((result: any) => result.score < 0.7)
      .sort((a: any, b: any) => a.score - b.score);

    // Generate improvement plan
    let plan = `# Improvement Plan for ${artifact.name}\n\n`;

    plan += `## Overall Assessment\n`;
    plan += `${evaluation.feedback}\n\n`;

    plan += `## Areas Needing Improvement\n`;

    if (weakCriteria.length > 0) {
      weakCriteria.forEach((criterion: any, index: number) => {
        plan += `### ${index + 1}. ${criterion.criterion}\n`;
        plan += `- Score: ${Math.round(criterion.score * 100)}%\n`;
        plan += `- Feedback: ${criterion.feedback}\n`;

        if (criterion.suggestions && criterion.suggestions.length > 0) {
          plan += `- Suggestions:\n`;
          criterion.suggestions.forEach((suggestion: string) => {
            plan += `  - ${suggestion}\n`;
          });
        }

        plan += `\n`;
      });
    } else {
      plan += `No specific areas were identified as needing significant improvement, but the overall quality could still be enhanced.\n\n`;
    }

    plan += `## Improvement Strategy\n`;
    plan += `1. Address the weakest areas first (listed above)\n`;
    plan += `2. Ensure all criteria are fully met\n`;
    plan += `3. Enhance the overall quality and coherence\n`;

    return plan;
  }

  /**
   * Process a user message
   * @param messageId The ID of the message to process
   * @returns Promise<boolean> indicating success or failure
   */
  public async processUserMessage(messageId: string): Promise<boolean> {
    try {
      logger.info(`Processing user message`, {
        sessionId: this.sessionId,
        messageId
      });

      const state = await this.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      const message = state.dynamicMessages?.[messageId];
      if (!message) {
        throw new Error(`Message ${messageId} not found`);
      }

      // Create a response message
      await this.stateManager.addSystemMessage({
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: 'system',
        to: 'user',
        type: DynamicMessageType.AGENT_MESSAGE,
        content: `Thank you for your message. The agents are processing your request.`,
        conversationId: message.conversationId,
        replyTo: messageId
      });

      // Check if we need to transition to the next phase
      await this.checkGoalCompletionAndTransition();

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error processing user message`, {
        sessionId: this.sessionId,
        messageId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Check if all goals for the current phase are complete and transition to the next phase if needed
   * @returns Promise<boolean> indicating whether a transition occurred
   */
  public async checkGoalCompletionAndTransition(): Promise<boolean> {
    try {
      const state = await this.getState();
      if (!state) {
        logger.error(`Session not found when checking goal completion`, {
          sessionId: this.sessionId
        });
        return false;
      }

      // Get the current phase
      const currentPhase = state.currentPhase;

      // Check if all goals for the current phase are complete
      const phaseComplete = await this.isPhaseComplete(currentPhase);

      if (phaseComplete) {
        logger.info(`All goals for phase ${currentPhase} are complete, transitioning to next phase`, {
          sessionId: this.sessionId,
          currentPhase
        });

        // Determine the next phase
        let nextPhase: DynamicWorkflowPhase;

        switch (currentPhase) {
          case DynamicWorkflowPhase.PLANNING:
            nextPhase = DynamicWorkflowPhase.RESEARCH;
            break;
          case DynamicWorkflowPhase.RESEARCH:
            nextPhase = DynamicWorkflowPhase.CREATION;
            break;
          case DynamicWorkflowPhase.CREATION:
            nextPhase = DynamicWorkflowPhase.REVIEW;
            break;
          case DynamicWorkflowPhase.REVIEW:
            nextPhase = DynamicWorkflowPhase.FINALIZATION;
            break;
          case DynamicWorkflowPhase.FINALIZATION:
            // Already in the final phase
            return false;
          default:
            logger.error(`Unknown phase ${currentPhase}`, {
              sessionId: this.sessionId
            });
            return false;
        }

        // Transition to the next phase
        return await this.transitionToPhase(nextPhase);
      }

      return false;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error checking goal completion and transitioning`, {
        sessionId: this.sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Check if all goals for a specific phase are complete
   * @param phase The phase to check
   * @returns Promise<boolean> indicating whether all goals for the phase are complete
   */
  private async isPhaseComplete(phase: DynamicWorkflowPhase): Promise<boolean> {
    try {
      const state = await this.getState();
      if (!state) return false;

      // Define which goals should be complete for each phase
      let requiredGoalTypes: GoalType[] = [];

      switch (phase) {
        case DynamicWorkflowPhase.PLANNING:
          // No goals required for planning phase
          return true;
        case DynamicWorkflowPhase.RESEARCH:
          requiredGoalTypes = [GoalType.MARKET_RESEARCH, GoalType.KEYWORD_ANALYSIS];
          break;
        case DynamicWorkflowPhase.CREATION:
          requiredGoalTypes = [GoalType.CONTENT_STRATEGY];
          break;
        case DynamicWorkflowPhase.REVIEW:
          requiredGoalTypes = [GoalType.CONTENT_CREATION];
          break;
        case DynamicWorkflowPhase.FINALIZATION:
          requiredGoalTypes = [GoalType.SEO_OPTIMIZATION, GoalType.QUALITY_ASSESSMENT];
          break;
        default:
          logger.error(`Unknown phase ${phase}`, {
            sessionId: this.sessionId
          });
          return false;
      }

      // Check if all required goals are complete
      for (const goalType of requiredGoalTypes) {
        const goalsOfType = Object.values(state.goals).filter(g => g.type === goalType);

        // If there are no goals of this type, consider it incomplete
        if (goalsOfType.length === 0) {
          logger.debug(`No goals of type ${goalType} found for phase ${phase}`, {
            sessionId: this.sessionId
          });
          return false;
        }

        // Check if any goal of this type is not complete
        const allComplete = goalsOfType.every(g => g.status === GoalStatus.COMPLETED);

        if (!allComplete) {
          logger.debug(`Not all goals of type ${goalType} are complete for phase ${phase}`, {
            sessionId: this.sessionId
          });
          return false;
        }
      }

      logger.info(`All goals for phase ${phase} are complete`, {
        sessionId: this.sessionId,
        phase,
        requiredGoalTypes
      });

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error checking if phase is complete`, {
        sessionId: this.sessionId,
        phase,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }
}
