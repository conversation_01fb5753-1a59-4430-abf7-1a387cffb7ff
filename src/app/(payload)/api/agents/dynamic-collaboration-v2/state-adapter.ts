/**
 * State Adapter
 * 
 * This module provides adapter functions to convert between different state formats
 * to ensure compatibility with the stateStore and other components.
 */

import { IterativeCollaborationState } from '../collaborative-iteration/types';
import { DynamicCollaborationState, GoalType, GoalStatus } from './types';

/**
 * Convert a DynamicCollaborationState to an IterativeCollaborationState
 * for compatibility with the stateStore
 */
export function adaptDynamicToIterativeState(
  dynamicState: DynamicCollaborationState
): IterativeCollaborationState {
  // Create a compatible state object with required properties
  return {
    ...dynamicState,
    // Add required properties for IterativeCollaborationState
    consultations: {},
    agentStates: {},
    iterations: 0,
    maxIterations: 5,
    // Convert goals from Record to array
    goals: Object.values(dynamicState.goals || {}).map(goal => ({
      id: goal.id,
      name: goal.description,
      description: goal.description,
      status: goal.status as any,
      assignedTo: Array.isArray(goal.assignedTo) ? goal.assignedTo[0] : goal.assignedTo,
      createdAt: goal.createdAt,
      type: goal.type as any,
      metadata: {
        criteria: goal.criteria,
        progress: goal.progress,
        dependencies: goal.dependencies
      }
    }))
  } as unknown as IterativeCollaborationState;
}

/**
 * Convert an IterativeCollaborationState to a DynamicCollaborationState
 * for use in the dynamic collaboration system
 */
export function adaptIterativeToDynamicState(
  iterativeState: IterativeCollaborationState
): DynamicCollaborationState {
  // Extract dynamic-specific properties
  const {
    dynamicMessages = {},
    conversations = {},
    activeGoals = [],
    completedGoals = [],
    ...rest
  } = iterativeState as any;

  // Convert goals from array to Record
  const goalsRecord: Record<string, any> = {};
  
  if (Array.isArray(iterativeState.goals)) {
    iterativeState.goals.forEach(goal => {
      goalsRecord[goal.id] = {
        id: goal.id,
        description: goal.name || goal.description,
        type: goal.type as GoalType,
        status: goal.status as GoalStatus,
        assignedTo: goal.assignedTo,
        createdAt: goal.createdAt,
        progress: goal.metadata?.progress || 0,
        dependencies: goal.metadata?.dependencies || [],
        criteria: goal.metadata?.criteria || []
      };
    });
  }

  return {
    ...rest,
    dynamicMessages,
    conversations,
    activeGoals,
    completedGoals,
    goals: goalsRecord
  } as unknown as DynamicCollaborationState;
}

/**
 * Get a dynamic state from the stateStore
 * This handles the conversion from IterativeCollaborationState to DynamicCollaborationState
 */
export async function getDynamicState(
  stateStore: any,
  sessionId: string
): Promise<DynamicCollaborationState | null> {
  const iterativeState = await stateStore.getState(sessionId);
  if (!iterativeState) return null;
  
  return adaptIterativeToDynamicState(iterativeState);
}

/**
 * Update a dynamic state in the stateStore
 * This handles the conversion from DynamicCollaborationState to IterativeCollaborationState
 */
export async function updateDynamicState(
  stateStore: any,
  sessionId: string,
  updateFn: (state: DynamicCollaborationState) => DynamicCollaborationState | null
): Promise<void> {
  await stateStore.updateState(sessionId, (currentState: IterativeCollaborationState) => {
    if (!currentState) return currentState;
    
    // Convert to dynamic state
    const dynamicState = adaptIterativeToDynamicState(currentState);
    
    // Apply update function
    const updatedDynamicState = updateFn(dynamicState);
    if (!updatedDynamicState) return currentState;
    
    // Convert back to iterative state
    return adaptDynamicToIterativeState(updatedDynamicState);
  });
}
