/**
 * Dynamic Workflow Orchestrator
 *
 * This class orchestrates the dynamic collaboration between agents,
 * managing the overall workflow and coordinating agent activities.
 */

import { v4 as uuidv4 } from 'uuid';
import { stateStore } from '../collaborative-iteration/utils/stateStore';
import logger from '../collaborative-iteration/utils/logger';
import {
  DynamicCollaborationState,
  ContentGenerationParams,
  DynamicWorkflowPhase,
  DynamicMessageType,
  DynamicAgentMessage,
  GoalType,
  GoalStatus,
  ContentGoal,
  createDynamicCollaborationState
} from './types';
import { IterativeMessage, IterativeMessageType, ArtifactStatus } from '../collaborative-iteration/types';
import { GoalManager } from './goal-manager';
import {
  marketResearchAgent,
  seoKeywordAgent,
  contentStrategyAgent,
  contentGenerationAgent,
  seoOptimizationAgent
} from '../collaborative-iteration/agents';
import {
  adaptDynamicToIterativeState,
  adaptIterativeToDynamicState,
  getDynamicState,
  updateDynamicState
} from './state-adapter';

export class DynamicWorkflowOrchestrator {
  private sessionId: string;
  private goalManager: GoalManager;

  constructor(sessionId: string) {
    this.sessionId = sessionId;
    this.goalManager = new GoalManager(sessionId);
  }

  /**
   * Initialize a new collaboration session
   * @param sessionId The session ID
   * @param params The content generation parameters
   * @returns Promise<boolean> indicating success or failure
   */
  public static async initiate(sessionId: string, params: ContentGenerationParams): Promise<boolean> {
    try {
      logger.info(`Initializing dynamic collaboration session`, {
        sessionId,
        topic: params.topic
      });

      // Create initial state
      const initialState = createDynamicCollaborationState(sessionId, params);

      // Convert to iterative state for storage
      const iterativeState = adaptDynamicToIterativeState(initialState);

      // Store the state
      await stateStore.setState(sessionId, iterativeState);

      // Create a new orchestrator instance
      const orchestrator = new DynamicWorkflowOrchestrator(sessionId);

      // Initialize the research phase
      await orchestrator.transitionToPhase(DynamicWorkflowPhase.RESEARCH);

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error initializing dynamic collaboration session`, {
        sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Get the current state of the session
   * @returns Promise<DynamicCollaborationState | null>
   */
  public async getState(): Promise<DynamicCollaborationState | null> {
    return await getDynamicState(stateStore, this.sessionId);
  }

  /**
   * Transition to a new phase
   * @param phase The phase to transition to
   * @returns Promise<boolean> indicating success or failure
   */
  public async transitionToPhase(phase: DynamicWorkflowPhase): Promise<boolean> {
    try {
      logger.info(`Transitioning to phase: ${phase}`, {
        sessionId: this.sessionId,
        phase
      });

      // Update the state with the new phase
      await updateDynamicState(stateStore, this.sessionId, (currentState) => {
        if (!currentState) return currentState;

        return {
          ...currentState,
          currentPhase: phase
        };
      });

      // Create a system message about the phase transition
      const messageId = uuidv4();
      const conversationId = uuidv4();
      const timestamp = new Date().toISOString();

      await updateDynamicState(stateStore, this.sessionId, (currentState) => {
        if (!currentState) return currentState;

        const phaseTransitionMessage = {
          id: messageId,
          timestamp,
          from: 'system',
          to: 'all',
          type: DynamicMessageType.SYSTEM_NOTIFICATION,
          content: {
            message: `Transitioned to phase: ${phase}`,
            phase
          },
          conversationId
        };

        // Handle dynamic messages and conversations safely
        const updatedDynamicMessages = {
          ...(currentState.dynamicMessages || {}),
          [messageId]: phaseTransitionMessage
        };

        const updatedConversations = { ...(currentState.conversations || {}) };
        if (!updatedConversations[conversationId]) {
          updatedConversations[conversationId] = [];
        }
        updatedConversations[conversationId].push(messageId);

        return {
          ...currentState,
          dynamicMessages: updatedDynamicMessages,
          conversations: updatedConversations
        };
      });

      // Trigger phase-specific activities
      await this.initiatePhaseActivities(phase);

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error transitioning to phase: ${phase}`, {
        sessionId: this.sessionId,
        phase,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Initiate activities for a specific phase
   * @param phase The phase to initiate activities for
   * @returns Promise<boolean> indicating success or failure
   */
  private async initiatePhaseActivities(phase: DynamicWorkflowPhase): Promise<boolean> {
    try {
      logger.info(`Initiating activities for phase: ${phase}`, {
        sessionId: this.sessionId,
        phase
      });

      const state = await this.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      switch (phase) {
        case DynamicWorkflowPhase.RESEARCH:
          // Define goals for research phase
          await this.goalManager.defineGoals();

          // Assign market research goal to market research agent
          const marketResearchGoals = Object.values(state.goals).filter(
            goal => goal.type === GoalType.MARKET_RESEARCH
          );

          if (marketResearchGoals.length > 0) {
            const marketResearchGoal = marketResearchGoals[0];
            await this.goalManager.assignGoal(marketResearchGoal.id, 'market-research');

            // Trigger market research agent
            await marketResearchAgent.act(this.sessionId);

            logger.info(`Triggered market research agent for session ${this.sessionId}`, {
              sessionId: this.sessionId,
              goalId: marketResearchGoal.id
            });
          }

          // Assign keyword analysis goal to SEO keyword agent
          const keywordGoals = Object.values(state.goals).filter(
            goal => goal.type === GoalType.KEYWORD_ANALYSIS
          );

          if (keywordGoals.length > 0) {
            const keywordGoal = keywordGoals[0];
            await this.goalManager.assignGoal(keywordGoal.id, 'seo-keyword');

            // Trigger SEO keyword agent
            await seoKeywordAgent.act(this.sessionId);

            logger.info(`Triggered SEO keyword agent for session ${this.sessionId}`, {
              sessionId: this.sessionId,
              goalId: keywordGoal.id
            });
          }

          break;

        case DynamicWorkflowPhase.CREATION:
          // Assign content strategy goal to content strategy agent
          const contentStrategyGoals = Object.values(state.goals).filter(
            goal => goal.type === GoalType.CONTENT_STRATEGY
          );

          if (contentStrategyGoals.length > 0) {
            const contentStrategyGoal = contentStrategyGoals[0];
            await this.goalManager.assignGoal(contentStrategyGoal.id, 'content-strategy');

            // Trigger content strategy agent
            await contentStrategyAgent.act(this.sessionId);

            logger.info(`Triggered content strategy agent for session ${this.sessionId}`, {
              sessionId: this.sessionId,
              goalId: contentStrategyGoal.id
            });
          }

          break;

        case DynamicWorkflowPhase.REVIEW:
          // Assign content creation goal to content generation agent
          const contentCreationGoals = Object.values(state.goals).filter(
            goal => goal.type === GoalType.CONTENT_CREATION
          );

          if (contentCreationGoals.length > 0) {
            const contentCreationGoal = contentCreationGoals[0];
            await this.goalManager.assignGoal(contentCreationGoal.id, 'content-generation');

            // Trigger content generation agent
            await contentGenerationAgent.act(this.sessionId);

            logger.info(`Triggered content generation agent for session ${this.sessionId}`, {
              sessionId: this.sessionId,
              goalId: contentCreationGoal.id
            });
          }

          break;

        case DynamicWorkflowPhase.FINALIZATION:
          // Assign SEO optimization goal to SEO optimization agent
          const seoOptimizationGoals = Object.values(state.goals).filter(
            goal => goal.type === GoalType.SEO_OPTIMIZATION
          );

          if (seoOptimizationGoals.length > 0) {
            const seoOptimizationGoal = seoOptimizationGoals[0];
            await this.goalManager.assignGoal(seoOptimizationGoal.id, 'seo-optimization');

            // Trigger SEO optimization agent
            await seoOptimizationAgent.act(this.sessionId);

            logger.info(`Triggered SEO optimization agent for session ${this.sessionId}`, {
              sessionId: this.sessionId,
              goalId: seoOptimizationGoal.id
            });
          }

          break;

        default:
          logger.info(`No specific activities for phase: ${phase}`, {
            sessionId: this.sessionId,
            phase
          });
      }

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error initiating activities for phase: ${phase}`, {
        sessionId: this.sessionId,
        phase,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Process a user message
   * @param messageId The ID of the message to process
   * @returns Promise<boolean> indicating success or failure
   */
  public async processUserMessage(messageId: string): Promise<boolean> {
    try {
      logger.info(`Processing user message`, {
        sessionId: this.sessionId,
        messageId
      });

      const state = await this.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      const message = state.dynamicMessages?.[messageId];
      if (!message) {
        throw new Error(`Message ${messageId} not found`);
      }

      // Create a response message
      const responseId = uuidv4();
      const timestamp = new Date().toISOString();

      await updateDynamicState(stateStore, this.sessionId, (currentState) => {
        if (!currentState) return currentState;

        const responseMessage = {
          id: responseId,
          timestamp,
          from: 'system',
          to: 'user',
          type: DynamicMessageType.AGENT_MESSAGE,
          content: `Thank you for your message. The agents are processing your request.`,
          conversationId: message.conversationId,
          replyTo: messageId
        };

        // Handle dynamic messages and conversations safely
        const updatedDynamicMessages = {
          ...(currentState.dynamicMessages || {}),
          [responseId]: responseMessage
        };

        const updatedConversations = { ...(currentState.conversations || {}) };
        if (!updatedConversations[message.conversationId]) {
          updatedConversations[message.conversationId] = [];
        }
        updatedConversations[message.conversationId].push(responseId);

        return {
          ...currentState,
          dynamicMessages: updatedDynamicMessages,
          conversations: updatedConversations
        };
      });

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error processing user message`, {
        sessionId: this.sessionId,
        messageId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }
}
