/**
 * Artifact Evaluation Service
 *
 * This service evaluates artifacts against goal criteria to determine if they meet
 * the requirements for goal completion.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../collaborative-iteration/utils/logger';
import { ContentGoal, GoalType, IterativeArtifact } from './types';
import { assessArtifactQuality } from '../collaborative-iteration/utils/quality-assessment';
import { calculateContentQualityMetrics } from '../collaborative-iteration/utils/content-quality-metrics';

/**
 * Evaluation result for a criterion
 */
export interface CriterionEvaluation {
  criterion: string;
  score: number; // 0-1 scale
  feedback: string;
  suggestions: string[];
}

/**
 * Overall artifact evaluation result
 */
export interface ArtifactEvaluation {
  id: string;
  goalId: string;
  artifactId: string;
  overallScore: number; // 0-1 scale
  criteriaResults: CriterionEvaluation[];
  goalMet: boolean;
  feedback: string;
  timestamp: string;
}

/**
 * Service for evaluating artifacts against goal criteria
 */
export class ArtifactEvaluationService {
  /**
   * Evaluate an artifact against goal criteria
   * @param artifact The artifact to evaluate
   * @param goal The goal to evaluate against
   * @returns Evaluation result with scores and feedback
   */
  public static evaluateArtifact(artifact: IterativeArtifact, goal: ContentGoal): ArtifactEvaluation {
    try {
      logger.info(`Evaluating artifact against goal criteria`, {
        artifactId: artifact.id,
        goalId: goal.id,
        goalType: goal.type,
        criteriaCount: goal.criteria.length
      });

      // Extract content as string
      const content = this.extractContentAsString(artifact);

      // Get target keywords if available
      const targetKeywords = this.extractTargetKeywords(artifact);

      // Evaluate each criterion
      const evaluationResults = goal.criteria.map(criterion => {
        return this.evaluateCriterion(artifact, content, criterion, goal.type);
      });

      // Calculate overall score
      const overallScore = evaluationResults.reduce((sum, result) => sum + result.score, 0) / evaluationResults.length;

      // Determine if the goal is met based on a threshold
      const goalMet = overallScore >= 0.7; // 70% threshold

      // Generate feedback
      const feedback = this.generateFeedback(evaluationResults, goalMet);

      return {
        id: uuidv4(),
        goalId: goal.id,
        artifactId: artifact.id,
        overallScore,
        criteriaResults: evaluationResults,
        goalMet,
        feedback,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      const err = error as Error;
      logger.error(`Error evaluating artifact`, {
        artifactId: artifact.id,
        goalId: goal.id,
        error: err.message || String(error),
        stack: err.stack
      });

      // Return a default evaluation with failure
      return {
        id: uuidv4(),
        goalId: goal.id,
        artifactId: artifact.id,
        overallScore: 0.5,
        criteriaResults: goal.criteria.map(criterion => ({
          criterion,
          score: 0.5,
          feedback: 'Evaluation failed due to an error',
          suggestions: ['Retry evaluation']
        })),
        goalMet: false,
        feedback: `Error evaluating artifact: ${err.message || 'Unknown error'}`,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Evaluate a single criterion
   */
  private static evaluateCriterion(
    artifact: IterativeArtifact,
    content: string,
    criterion: string,
    goalType: GoalType
  ): CriterionEvaluation {
    // Use different evaluation strategies based on goal type and criterion
    switch (goalType) {
      case GoalType.MARKET_RESEARCH:
        return this.evaluateMarketResearchCriterion(artifact, content, criterion);
      case GoalType.KEYWORD_ANALYSIS:
        return this.evaluateKeywordAnalysisCriterion(artifact, content, criterion);
      case GoalType.CONTENT_STRATEGY:
        return this.evaluateContentStrategyCriterion(artifact, content, criterion);
      case GoalType.CONTENT_CREATION:
        return this.evaluateContentCreationCriterion(artifact, content, criterion);
      case GoalType.SEO_OPTIMIZATION:
        return this.evaluateSeoOptimizationCriterion(artifact, content, criterion);
      case GoalType.QUALITY_ASSESSMENT:
        return this.evaluateQualityAssessmentCriterion(artifact, content, criterion);
      default:
        return this.evaluateGenericCriterion(artifact, content, criterion);
    }
  }

  /**
   * Generate feedback based on evaluation results
   */
  private static generateFeedback(evaluationResults: CriterionEvaluation[], goalMet: boolean): string {
    if (goalMet) {
      return `The artifact meets the goal criteria with an overall score of ${Math.round(evaluationResults.reduce((sum, result) => sum + result.score, 0) / evaluationResults.length * 100)}%. ${evaluationResults.filter(r => r.score > 0.8).length} out of ${evaluationResults.length} criteria were strongly met.`;
    } else {
      const weakCriteria = evaluationResults.filter(r => r.score < 0.7);
      return `The artifact does not fully meet the goal criteria. Areas needing improvement: ${weakCriteria.map(c => c.criterion).join(', ')}. Please address the following suggestions: ${weakCriteria.flatMap(c => c.suggestions).join('; ')}`;
    }
  }

  /**
   * Extract content as string from artifact
   */
  private static extractContentAsString(artifact: IterativeArtifact): string {
    if (typeof artifact.content === 'string') {
      return artifact.content;
    }

    if (artifact.iterations && artifact.iterations.length > 0) {
      const latestIteration = artifact.iterations[artifact.iterations.length - 1];
      if (typeof latestIteration.content === 'string') {
        return latestIteration.content;
      }

      if (typeof latestIteration.content === 'object') {
        return JSON.stringify(latestIteration.content);
      }
    }

    // Try to extract from data field
    if (artifact.data) {
      if (typeof artifact.data === 'string') {
        return artifact.data;
      }

      if (typeof artifact.data === 'object') {
        return JSON.stringify(artifact.data);
      }
    }

    return '';
  }

  /**
   * Extract target keywords from artifact
   */
  private static extractTargetKeywords(artifact: IterativeArtifact): string[] {
    const keywords: string[] = [];

    // Try to extract from metadata
    if (artifact.metadata && artifact.metadata.keywords) {
      if (Array.isArray(artifact.metadata.keywords)) {
        keywords.push(...artifact.metadata.keywords);
      } else if (typeof artifact.metadata.keywords === 'string') {
        keywords.push(...artifact.metadata.keywords.split(',').map(k => k.trim()));
      }
    }

    // Try to extract from data
    if (artifact.data && artifact.data.keywords) {
      if (Array.isArray(artifact.data.keywords)) {
        keywords.push(...artifact.data.keywords);
      } else if (typeof artifact.data.keywords === 'string') {
        keywords.push(...artifact.data.keywords.split(',').map(k => k.trim()));
      }
    }

    return keywords;
  }

  /**
   * Evaluate a keyword analysis criterion
   */
  private static evaluateKeywordAnalysisCriterion(
    artifact: IterativeArtifact,
    content: string,
    criterion: string
  ): CriterionEvaluation {
    let score = 0.5; // Default score
    let feedback = '';
    const suggestions: string[] = [];

    // Check for primary and secondary keywords
    if (criterion.includes('primary and secondary keywords')) {
      // Look for keywords in the content
      const hasKeywords = content.toLowerCase().includes('keyword') ||
                         content.toLowerCase().includes('search term');

      // Look for primary/secondary distinction
      const hasPrimary = content.toLowerCase().includes('primary') ||
                        content.toLowerCase().includes('main');
      const hasSecondary = content.toLowerCase().includes('secondary') ||
                          content.toLowerCase().includes('additional');

      if (hasKeywords && hasPrimary && hasSecondary) {
        score = 0.9;
        feedback = 'Both primary and secondary keywords identified';
      } else if (hasKeywords) {
        score = 0.6;
        feedback = 'Keywords identified but not clearly categorized as primary/secondary';
        suggestions.push('Clearly distinguish between primary and secondary keywords');
      } else {
        score = 0.3;
        feedback = 'Keywords not clearly identified';
        suggestions.push('Include a section listing primary and secondary keywords');
      }
    }

    // Check for search intent
    else if (criterion.includes('search intent')) {
      const hasIntent = content.toLowerCase().includes('intent') ||
                       content.toLowerCase().includes('search purpose') ||
                       content.toLowerCase().includes('user intent');

      // Look for intent types
      const hasInformational = content.toLowerCase().includes('informational');
      const hasTransactional = content.toLowerCase().includes('transactional');
      const hasNavigational = content.toLowerCase().includes('navigational');

      if (hasIntent && (hasInformational || hasTransactional || hasNavigational)) {
        score = 0.9;
        feedback = 'Search intent thoroughly analyzed with intent types';
      } else if (hasIntent) {
        score = 0.7;
        feedback = 'Search intent mentioned but not categorized';
        suggestions.push('Categorize search intent (informational, transactional, navigational)');
      } else {
        score = 0.3;
        feedback = 'Search intent not clearly analyzed';
        suggestions.push('Include analysis of search intent for each keyword');
      }
    }

    // Generic evaluation for other criteria
    else {
      score = content.toLowerCase().includes(criterion.toLowerCase()) ? 0.7 : 0.4;
      feedback = score > 0.5 ?
        `Criterion "${criterion}" appears to be addressed` :
        `Criterion "${criterion}" may not be adequately addressed`;

      if (score <= 0.5) {
        suggestions.push(`Explicitly address the criterion: ${criterion}`);
      }
    }

    return {
      criterion,
      score,
      feedback,
      suggestions
    };
  }

  /**
   * Evaluate a content strategy criterion
   */
  private static evaluateContentStrategyCriterion(
    artifact: IterativeArtifact,
    content: string,
    criterion: string
  ): CriterionEvaluation {
    let score = 0.5; // Default score
    let feedback = '';
    const suggestions: string[] = [];

    // Check for content structure
    if (criterion.includes('content structure')) {
      const hasStructure = content.toLowerCase().includes('structure') ||
                          content.toLowerCase().includes('outline') ||
                          content.toLowerCase().includes('section');

      // Check for section headings
      const sectionMatches = content.match(/#+\s+|<h[1-6]>|section|heading/gi);
      const hasSections = sectionMatches && sectionMatches.length > 3;

      if (hasStructure && hasSections) {
        score = 0.9;
        feedback = 'Clear content structure with multiple sections defined';
      } else if (hasStructure) {
        score = 0.6;
        feedback = 'Content structure mentioned but sections not clearly defined';
        suggestions.push('Define clear sections with headings in the content structure');
      } else {
        score = 0.3;
        feedback = 'Content structure not clearly defined';
        suggestions.push('Include a clear content structure with defined sections');
      }
    }

    // Check for tone and style
    else if (criterion.includes('tone and style')) {
      const hasTone = content.toLowerCase().includes('tone') ||
                     content.toLowerCase().includes('style') ||
                     content.toLowerCase().includes('voice');

      if (hasTone) {
        score = 0.8;
        feedback = 'Tone and style defined for the content';
      } else {
        score = 0.4;
        feedback = 'Tone and style not clearly defined';
        suggestions.push('Define the tone and style appropriate for the target audience');
      }
    }

    // Check for keyword incorporation
    else if (criterion.includes('incorporate keywords')) {
      const hasKeywordPlan = content.toLowerCase().includes('keyword') &&
                            (content.toLowerCase().includes('incorporate') ||
                             content.toLowerCase().includes('include') ||
                             content.toLowerCase().includes('use'));

      if (hasKeywordPlan) {
        score = 0.8;
        feedback = 'Plan for keyword incorporation included';
      } else {
        score = 0.4;
        feedback = 'Keyword incorporation strategy not clearly defined';
        suggestions.push('Include a plan for strategically incorporating keywords');
      }
    }

    // Generic evaluation for other criteria
    else {
      score = content.toLowerCase().includes(criterion.toLowerCase()) ? 0.7 : 0.4;
      feedback = score > 0.5 ?
        `Criterion "${criterion}" appears to be addressed` :
        `Criterion "${criterion}" may not be adequately addressed`;

      if (score <= 0.5) {
        suggestions.push(`Explicitly address the criterion: ${criterion}`);
      }
    }

    return {
      criterion,
      score,
      feedback,
      suggestions
    };
  }

  /**
   * Evaluate a content creation criterion
   */
  private static evaluateContentCreationCriterion(
    artifact: IterativeArtifact,
    content: string,
    criterion: string
  ): CriterionEvaluation {
    let score = 0.5; // Default score
    let feedback = '';
    const suggestions: string[] = [];

    // Check for compelling introduction and conclusion
    if (criterion.includes('introduction') || criterion.includes('conclusion')) {
      const hasIntro = content.toLowerCase().includes('introduction') ||
                      content.match(/^.{0,500}(welcome|hello|introduction|begin|start)/i) !== null;

      const hasConclusion = content.toLowerCase().includes('conclusion') ||
                           content.match(/(conclusion|summary|in summary|to summarize|finally).{0,500}$/i) !== null;

      if (criterion.includes('introduction') && hasIntro) {
        score = 0.8;
        feedback = 'Content includes a compelling introduction';
      } else if (criterion.includes('conclusion') && hasConclusion) {
        score = 0.8;
        feedback = 'Content includes a proper conclusion';
      } else if (criterion.includes('introduction') && criterion.includes('conclusion') && hasIntro && hasConclusion) {
        score = 0.9;
        feedback = 'Content includes both compelling introduction and conclusion';
      } else {
        score = 0.4;
        feedback = criterion.includes('introduction') ?
          'Introduction not clearly identified' :
          'Conclusion not clearly identified';

        suggestions.push(criterion.includes('introduction') ?
          'Add a compelling introduction that hooks the reader' :
          'Add a proper conclusion that summarizes key points');
      }
    }

    // Check for section content
    else if (criterion.includes('section content')) {
      // Look for multiple sections
      const sectionMatches = content.match(/#+\s+|<h[1-6]>|section|heading/gi);
      const hasSections = sectionMatches && sectionMatches.length > 3;

      if (hasSections) {
        score = 0.8;
        feedback = 'Content includes comprehensive section content';
      } else {
        score = 0.5;
        feedback = 'Section content could be more comprehensive';
        suggestions.push('Develop more comprehensive content for each section');
      }
    }

    // Check for keyword incorporation
    else if (criterion.includes('incorporate keywords')) {
      // This would ideally use NLP to check for natural keyword usage
      // For now, we'll use a simple heuristic
      const hasKeywords = content.toLowerCase().includes('keyword') ||
                         this.extractTargetKeywords(artifact).some(kw =>
                           content.toLowerCase().includes(kw.toLowerCase()));

      if (hasKeywords) {
        score = 0.7;
        feedback = 'Keywords appear to be incorporated';
      } else {
        score = 0.4;
        feedback = 'Keywords may not be properly incorporated';
        suggestions.push('Incorporate target keywords more naturally throughout the content');
      }
    }

    // Check for formatting and readability
    else if (criterion.includes('formatting') || criterion.includes('readability')) {
      // Check for formatting elements
      const hasFormatting = content.match(/#+\s+|<h[1-6]>|\*\*|__|\*|_|`|```|>|---|---|>/g) !== null;

      // Check for paragraph breaks
      const paragraphs = content.split(/\n\s*\n/);
      const hasParagraphBreaks = paragraphs.length > 3;

      if (hasFormatting && hasParagraphBreaks) {
        score = 0.9;
        feedback = 'Content is well-formatted with good readability';
      } else if (hasFormatting || hasParagraphBreaks) {
        score = 0.6;
        feedback = 'Content has some formatting but could be improved';
        suggestions.push(hasFormatting ?
          'Improve paragraph breaks for better readability' :
          'Add more formatting elements (headings, bold, lists) for better structure');
      } else {
        score = 0.3;
        feedback = 'Content lacks proper formatting and readability';
        suggestions.push('Improve formatting with headings, lists, and proper paragraph breaks');
      }
    }

    // Generic evaluation for other criteria
    else {
      score = content.toLowerCase().includes(criterion.toLowerCase()) ? 0.7 : 0.4;
      feedback = score > 0.5 ?
        `Criterion "${criterion}" appears to be addressed` :
        `Criterion "${criterion}" may not be adequately addressed`;

      if (score <= 0.5) {
        suggestions.push(`Explicitly address the criterion: ${criterion}`);
      }
    }

    return {
      criterion,
      score,
      feedback,
      suggestions
    };
  }

  /**
   * Evaluate an SEO optimization criterion
   */
  private static evaluateSeoOptimizationCriterion(
    artifact: IterativeArtifact,
    content: string,
    criterion: string
  ): CriterionEvaluation {
    let score = 0.5; // Default score
    let feedback = '';
    const suggestions: string[] = [];

    // Check for keyword distribution
    if (criterion.includes('keyword distribution') || criterion.includes('keyword placement')) {
      const targetKeywords = this.extractTargetKeywords(artifact);

      if (targetKeywords.length === 0) {
        score = 0.3;
        feedback = 'No target keywords found for evaluation';
        suggestions.push('Define target keywords for proper SEO evaluation');
        return { criterion, score, feedback, suggestions };
      }

      // Check for keywords in different parts of the content
      const contentLower = content.toLowerCase();
      const firstQuarter = contentLower.substring(0, Math.floor(contentLower.length / 4));
      const lastQuarter = contentLower.substring(Math.floor(contentLower.length * 3 / 4));

      const keywordsInFirstQuarter = targetKeywords.filter(kw =>
        firstQuarter.includes(kw.toLowerCase()));

      const keywordsInLastQuarter = targetKeywords.filter(kw =>
        lastQuarter.includes(kw.toLowerCase()));

      const keywordsInContent = targetKeywords.filter(kw =>
        contentLower.includes(kw.toLowerCase()));

      if (keywordsInContent.length === 0) {
        score = 0.2;
        feedback = 'Target keywords not found in content';
        suggestions.push('Include target keywords in the content');
      } else if (keywordsInFirstQuarter.length > 0 && keywordsInLastQuarter.length > 0) {
        score = 0.9;
        feedback = 'Keywords well-distributed throughout the content';
      } else if (keywordsInFirstQuarter.length > 0 || keywordsInLastQuarter.length > 0) {
        score = 0.7;
        feedback = 'Keywords present but distribution could be improved';
        suggestions.push('Distribute keywords more evenly throughout the content');
      } else {
        score = 0.5;
        feedback = 'Keywords present but not optimally distributed';
        suggestions.push('Include keywords in both the beginning and end of the content');
      }
    }

    // Check for metadata optimization
    else if (criterion.includes('metadata')) {
      const hasMetadata = artifact.metadata && Object.keys(artifact.metadata).length > 0;

      if (hasMetadata) {
        const metadataKeys = Object.keys(artifact.metadata);
        const hasSeoMetadata = metadataKeys.some(key =>
          ['title', 'description', 'keywords', 'metaTitle', 'metaDescription'].includes(key));

        if (hasSeoMetadata) {
          score = 0.9;
          feedback = 'SEO metadata properly optimized';
        } else {
          score = 0.6;
          feedback = 'Metadata present but missing key SEO elements';
          suggestions.push('Add SEO-specific metadata (title, description, keywords)');
        }
      } else {
        score = 0.3;
        feedback = 'Metadata not found or not optimized';
        suggestions.push('Add proper metadata for SEO optimization');
      }
    }

    // Check for content length
    else if (criterion.includes('content length')) {
      const wordCount = content.split(/\s+/).filter(Boolean).length;

      if (wordCount >= 1500) {
        score = 0.9;
        feedback = 'Content length is excellent for SEO (1500+ words)';
      } else if (wordCount >= 800) {
        score = 0.7;
        feedback = 'Content length is good for SEO (800+ words)';
      } else if (wordCount >= 500) {
        score = 0.5;
        feedback = 'Content length is acceptable but could be improved';
        suggestions.push('Expand content to at least 800 words for better SEO');
      } else {
        score = 0.3;
        feedback = 'Content length is insufficient for good SEO';
        suggestions.push('Significantly expand content to at least 800 words');
      }
    }

    // Generic evaluation for other criteria
    else {
      score = content.toLowerCase().includes(criterion.toLowerCase()) ? 0.7 : 0.4;
      feedback = score > 0.5 ?
        `SEO criterion "${criterion}" appears to be addressed` :
        `SEO criterion "${criterion}" may not be adequately addressed`;

      if (score <= 0.5) {
        suggestions.push(`Explicitly address the SEO criterion: ${criterion}`);
      }
    }

    return {
      criterion,
      score,
      feedback,
      suggestions
    };
  }

  /**
   * Evaluate a quality assessment criterion
   */
  private static evaluateQualityAssessmentCriterion(
    artifact: IterativeArtifact,
    content: string,
    criterion: string
  ): CriterionEvaluation {
    let score = 0.5; // Default score
    let feedback = '';
    const suggestions: string[] = [];

    // Check for content accuracy and relevance
    if (criterion.includes('accuracy') || criterion.includes('relevance')) {
      // This would ideally use more sophisticated NLP
      // For now, we'll use a simple heuristic based on content structure

      // Check for citations or references
      const hasCitations = content.match(/\[\d+\]|\(.*?\d+.*?\)/) !== null;

      // Check for specific details and facts
      const hasSpecificDetails = content.match(/\d+%|\d+\s+[a-zA-Z]+|according to|research shows/) !== null;

      if (hasCitations && hasSpecificDetails) {
        score = 0.9;
        feedback = 'Content appears accurate with citations and specific details';
      } else if (hasCitations || hasSpecificDetails) {
        score = 0.7;
        feedback = 'Content includes some elements of accuracy and relevance';
        suggestions.push(hasCitations ?
          'Include more specific details and facts' :
          'Add citations or references to support claims');
      } else {
        score = 0.5;
        feedback = 'Content accuracy and relevance could be improved';
        suggestions.push('Add specific details, facts, and citations to improve accuracy');
      }
    }

    // Check for grammar and spelling
    else if (criterion.includes('grammar') || criterion.includes('spelling')) {
      // This would ideally use a grammar checking API
      // For now, we'll use a simple heuristic

      // Check for common grammar issues
      const commonErrors = [
        'their is', 'there is', 'they\'re is',
        'your welcome', 'you\'re welcome',
        'its a', 'it\'s a',
        'alot', 'a lot',
        'could of', 'should of', 'would of'
      ];

      const foundErrors = commonErrors.filter(error =>
        content.toLowerCase().includes(error));

      if (foundErrors.length === 0) {
        score = 0.8;
        feedback = 'No common grammar or spelling issues detected';
      } else {
        score = 0.5;
        feedback = 'Some grammar or spelling issues detected';
        suggestions.push('Proofread content for grammar and spelling errors');
        suggestions.push(`Check for issues like: ${foundErrors.join(', ')}`);
      }
    }

    // Generic evaluation for other criteria
    else {
      score = content.toLowerCase().includes(criterion.toLowerCase()) ? 0.7 : 0.4;
      feedback = score > 0.5 ?
        `Quality criterion "${criterion}" appears to be addressed` :
        `Quality criterion "${criterion}" may not be adequately addressed`;

      if (score <= 0.5) {
        suggestions.push(`Explicitly address the quality criterion: ${criterion}`);
      }
    }

    return {
      criterion,
      score,
      feedback,
      suggestions
    };
  }

  /**
   * Evaluate a market research criterion
   */
  private static evaluateMarketResearchCriterion(
    artifact: IterativeArtifact,
    content: string,
    criterion: string
  ): CriterionEvaluation {
    let score = 0.5; // Default score
    let feedback = '';
    const suggestions: string[] = [];

    // Check for audience demographics
    if (criterion.includes('audience demographics')) {
      const hasAudience = content.toLowerCase().includes('audience') ||
                         content.toLowerCase().includes('demographic') ||
                         content.toLowerCase().includes('target market');

      if (hasAudience) {
        // Check for specific demographic details
        const demographicTerms = ['age', 'gender', 'income', 'education', 'location', 'occupation'];
        const foundTerms = demographicTerms.filter(term => content.toLowerCase().includes(term));

        if (foundTerms.length >= 3) {
          score = 0.9;
          feedback = 'Comprehensive audience demographics identified';
        } else if (foundTerms.length >= 1) {
          score = 0.7;
          feedback = 'Basic audience demographics identified';
          suggestions.push('Include more demographic details such as ' +
                          demographicTerms.filter(t => !foundTerms.includes(t)).join(', '));
        } else {
          score = 0.5;
          feedback = 'Audience mentioned but demographics not detailed';
          suggestions.push('Add specific demographic information about the target audience');
        }
      } else {
        score = 0.3;
        feedback = 'Audience demographics not clearly identified';
        suggestions.push('Include a section on target audience demographics');
      }
    }

    // Check for market trends
    else if (criterion.includes('market trends')) {
      const hasTrends = content.toLowerCase().includes('trend') ||
                       content.toLowerCase().includes('market') ||
                       content.toLowerCase().includes('industry');

      if (hasTrends) {
        // Check for specific trend details
        const trendTerms = ['growth', 'decline', 'increase', 'decrease', 'emerging', 'forecast'];
        const foundTerms = trendTerms.filter(term => content.toLowerCase().includes(term));

        if (foundTerms.length >= 2) {
          score = 0.9;
          feedback = 'Comprehensive market trends analyzed';
        } else if (foundTerms.length >= 1) {
          score = 0.7;
          feedback = 'Basic market trends identified';
          suggestions.push('Include more trend analysis with specific metrics or forecasts');
        } else {
          score = 0.5;
          feedback = 'Market mentioned but trends not detailed';
          suggestions.push('Add specific trend information with data points');
        }
      } else {
        score = 0.3;
        feedback = 'Market trends not clearly identified';
        suggestions.push('Include a section on relevant market trends');
      }
    }

    // Generic evaluation for other criteria
    else {
      score = content.toLowerCase().includes(criterion.toLowerCase()) ? 0.7 : 0.4;
      feedback = score > 0.5 ?
        `Criterion "${criterion}" appears to be addressed` :
        `Criterion "${criterion}" may not be adequately addressed`;

      if (score <= 0.5) {
        suggestions.push(`Explicitly address the criterion: ${criterion}`);
      }
    }

    return {
      criterion,
      score,
      feedback,
      suggestions
    };
  }

  /**
   * Evaluate a generic criterion
   */
  private static evaluateGenericCriterion(
    artifact: IterativeArtifact,
    content: string,
    criterion: string
  ): CriterionEvaluation {
    // Basic evaluation based on keyword presence
    const criterionKeywords = criterion.toLowerCase().split(/\s+/).filter(word => word.length > 3);
    const matchingKeywords = criterionKeywords.filter(keyword => content.toLowerCase().includes(keyword));

    const matchRatio = criterionKeywords.length > 0 ?
      matchingKeywords.length / criterionKeywords.length : 0;

    let score = 0;
    let feedback = '';
    const suggestions: string[] = [];

    if (matchRatio >= 0.8) {
      score = 0.9;
      feedback = `Criterion "${criterion}" is well-addressed`;
    } else if (matchRatio >= 0.5) {
      score = 0.7;
      feedback = `Criterion "${criterion}" is partially addressed`;
      suggestions.push(`Enhance content to fully address: ${criterion}`);
    } else if (matchRatio > 0) {
      score = 0.5;
      feedback = `Criterion "${criterion}" is minimally addressed`;
      suggestions.push(`Significantly improve content to address: ${criterion}`);
    } else {
      score = 0.3;
      feedback = `Criterion "${criterion}" does not appear to be addressed`;
      suggestions.push(`Add content specifically addressing: ${criterion}`);
    }

    return {
      criterion,
      score,
      feedback,
      suggestions
    };
  }
}
