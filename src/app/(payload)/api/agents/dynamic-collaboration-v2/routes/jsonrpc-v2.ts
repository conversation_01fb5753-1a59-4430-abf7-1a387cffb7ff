/**
 * Dynamic Collaboration JSON-RPC API V2
 * 
 * This file implements the JSON-RPC API for the dynamic collaboration system
 * using the new state manager and orchestrator.
 */

import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import logger from '../../../collaborative-iteration/utils/logger';
import { DynamicWorkflowOrchestratorV2 } from '../dynamic-workflow-orchestrator-v2';
import { DynamicWorkflowPhase, DynamicMessageType } from '../state';
import { createStateManager } from '../state';

/**
 * Handle JSON-RPC requests for the dynamic collaboration system
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { method, params, id } = body;
    
    // Validate request
    if (!method || !params) {
      return NextResponse.json({
        jsonrpc: '2.0',
        error: {
          code: -32600,
          message: 'Invalid Request'
        },
        id: id || null
      }, { status: 400 });
    }
    
    // Process method
    let result;
    
    switch (method) {
      case 'initiate':
        result = await handleInitiate(params);
        break;
        
      case 'getState':
        result = await handleGetState(params);
        break;
        
      case 'transitionPhase':
        result = await handleTransitionPhase(params);
        break;
        
      case 'processUserMessage':
        result = await handleProcessUserMessage(params);
        break;
        
      case 'addUserMessage':
        result = await handleAddUserMessage(params);
        break;
        
      default:
        return NextResponse.json({
          jsonrpc: '2.0',
          error: {
            code: -32601,
            message: 'Method not found'
          },
          id: id || null
        }, { status: 404 });
    }
    
    // Return result
    return NextResponse.json({
      jsonrpc: '2.0',
      result,
      id: id || null
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error processing JSON-RPC request`, {
      error: err.message || String(error),
      stack: err.stack
    });
    
    return NextResponse.json({
      jsonrpc: '2.0',
      error: {
        code: -32603,
        message: 'Internal error',
        data: err.message || String(error)
      },
      id: null
    }, { status: 500 });
  }
}

/**
 * Handle initiate method
 */
async function handleInitiate(params: any) {
  const { topic, contentType, targetAudience, tone, keywords, additionalInstructions, referenceUrls } = params;
  
  // Validate required parameters
  if (!topic) {
    throw new Error('Missing required parameter: topic');
  }
  
  // Generate session ID
  const sessionId = params.sessionId || uuidv4();
  
  // Initialize session
  const success = await DynamicWorkflowOrchestratorV2.initiate(sessionId, {
    topic,
    contentType: contentType || 'blog-article',
    targetAudience: targetAudience || 'general audience',
    tone: tone || 'informative',
    keywords: keywords || [],
    additionalInstructions,
    referenceUrls
  });
  
  if (!success) {
    throw new Error('Failed to initialize session');
  }
  
  return { sessionId, success };
}

/**
 * Handle getState method
 */
async function handleGetState(params: any) {
  const { sessionId } = params;
  
  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  
  // Get state manager
  const stateManager = createStateManager(sessionId);
  
  // Get state
  const state = await stateManager.getState();
  if (!state) {
    throw new Error(`Session ${sessionId} not found`);
  }
  
  return { state };
}

/**
 * Handle transitionPhase method
 */
async function handleTransitionPhase(params: any) {
  const { sessionId, phase } = params;
  
  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!phase) {
    throw new Error('Missing required parameter: phase');
  }
  
  // Create orchestrator
  const orchestrator = new DynamicWorkflowOrchestratorV2(sessionId);
  
  // Transition phase
  const success = await orchestrator.transitionToPhase(phase as DynamicWorkflowPhase);
  
  return { success };
}

/**
 * Handle processUserMessage method
 */
async function handleProcessUserMessage(params: any) {
  const { sessionId, messageId } = params;
  
  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!messageId) {
    throw new Error('Missing required parameter: messageId');
  }
  
  // Create orchestrator
  const orchestrator = new DynamicWorkflowOrchestratorV2(sessionId);
  
  // Process message
  const success = await orchestrator.processUserMessage(messageId);
  
  return { success };
}

/**
 * Handle addUserMessage method
 */
async function handleAddUserMessage(params: any) {
  const { sessionId, content } = params;
  
  // Validate required parameters
  if (!sessionId) {
    throw new Error('Missing required parameter: sessionId');
  }
  if (!content) {
    throw new Error('Missing required parameter: content');
  }
  
  // Get state manager
  const stateManager = createStateManager(sessionId);
  
  // Create message
  const messageId = uuidv4();
  const conversationId = uuidv4();
  
  // Add message
  await stateManager.addMessage({
    id: messageId,
    timestamp: new Date().toISOString(),
    from: 'user',
    to: 'system',
    type: DynamicMessageType.USER_MESSAGE,
    content,
    conversationId
  });
  
  // Create orchestrator
  const orchestrator = new DynamicWorkflowOrchestratorV2(sessionId);
  
  // Process message
  await orchestrator.processUserMessage(messageId);
  
  return { messageId, conversationId };
}
