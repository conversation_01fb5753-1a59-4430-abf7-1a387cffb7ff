import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { DynamicWorkflowOrchestrator } from '../../../dynamic-workflow-orchestrator';
import { DynamicMessageType } from '../../../types';
import logger from '../../../../../../api/agents/collaborative-iteration/utils/logger';

/**
 * API handler for sending a message in a dynamic collaboration session
 *
 * @param req The Next.js request object
 * @param params The route parameters containing the session ID
 * @returns NextResponse with the result of the message sending
 */
export async function POST(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
): Promise<NextResponse> {
  try {
    const { sessionId } = params;

    // Validate session ID
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Parse the request body
    const body = await req.json();
    const {
      content,
      type = DynamicMessageType.USER_MESSAGE,
      to = 'system'
    } = body;

    // Validate content
    if (!content) {
      return NextResponse.json(
        { error: 'Missing required parameter: content' },
        { status: 400 }
      );
    }

    // Create a new orchestrator instance
    const orchestrator = new DynamicWorkflowOrchestrator(sessionId);

    // Get the communication hub
    const communicationHub = orchestrator.getCommunicationHub();

    // Create the message
    const message = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'user',
      to,
      type,
      content,
      conversationId: uuidv4()
    };

    // Send the message
    const messageId = await communicationHub.sendMessage(message);

    // Process the user message
    await orchestrator.processUserMessage(message);

    logger.info(`Sent message in dynamic collaboration session`, {
      sessionId,
      messageId,
      type
    });

    return NextResponse.json({
      success: true,
      messageId,
      message: 'Message sent successfully'
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error sending message in dynamic collaboration session`, {
      sessionId: params.sessionId,
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: err.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * API handler for getting messages from a dynamic collaboration session
 *
 * @param req The Next.js request object
 * @param params The route parameters containing the session ID
 * @returns NextResponse with the messages
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
): Promise<NextResponse> {
  try {
    const { sessionId } = params;

    // Validate session ID
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Create a new orchestrator instance
    const orchestrator = new DynamicWorkflowOrchestrator(sessionId);

    // Get the session state
    const state = await orchestrator.getState();

    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Extract messages
    const messages = state.dynamicMessages || {};

    logger.info(`Retrieved messages from dynamic collaboration session`, {
      sessionId,
      messageCount: Object.keys(messages).length
    });

    return NextResponse.json({
      success: true,
      messages: Object.values(messages)
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error retrieving messages from dynamic collaboration session`, {
      sessionId: params.sessionId,
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: err.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
