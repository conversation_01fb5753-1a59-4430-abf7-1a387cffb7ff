import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { DynamicWorkflowOrchestrator } from '../../../dynamic-workflow-orchestrator';
import { ArtifactStatus } from '../../../../../../api/agents/collaborative-iteration/types';
import logger from '../../../../../../api/agents/collaborative-iteration/utils/logger';

/**
 * API handler for creating a new artifact in a dynamic collaboration session
 *
 * @param req The Next.js request object
 * @param params The route parameters containing the session ID
 * @returns NextResponse with the result of the artifact creation
 */
export async function POST(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
): Promise<NextResponse> {
  try {
    const { sessionId } = params;

    // Validate session ID
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Parse the request body
    const body = await req.json();
    const {
      type,
      name,
      content,
      creator = 'user',
      status = ArtifactStatus.COMPLETED,
      data = {}
    } = body;

    // Validate required parameters
    if (!type) {
      return NextResponse.json(
        { error: 'Missing required parameter: type' },
        { status: 400 }
      );
    }

    if (!name) {
      return NextResponse.json(
        { error: 'Missing required parameter: name' },
        { status: 400 }
      );
    }

    // Create a new orchestrator instance
    const orchestrator = new DynamicWorkflowOrchestrator(sessionId);

    // Get the artifact decision framework
    const artifactFramework = orchestrator.getArtifactDecisionFramework();

    // Create the artifact
    const artifactId = await artifactFramework.createArtifact({
      id: uuidv4(),
      type,
      name,
      content,
      creator,
      status,
      timestamp: new Date().toISOString(),
      data
    });

    logger.info(`Created artifact in dynamic collaboration session`, {
      sessionId,
      artifactId,
      type,
      name
    });

    return NextResponse.json({
      success: true,
      artifactId,
      message: 'Artifact created successfully'
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error creating artifact in dynamic collaboration session`, {
      sessionId: params.sessionId,
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: err.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * API handler for getting artifacts from a dynamic collaboration session
 *
 * @param req The Next.js request object
 * @param params The route parameters containing the session ID
 * @returns NextResponse with the artifacts
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
): Promise<NextResponse> {
  try {
    const { sessionId } = params;

    // Validate session ID
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Create a new orchestrator instance
    const orchestrator = new DynamicWorkflowOrchestrator(sessionId);

    // Get the session state
    const state = await orchestrator.getState();

    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Extract artifacts
    const artifacts = state.artifacts || {};
    const generatedArtifacts = state.generatedArtifacts || [];

    logger.info(`Retrieved artifacts from dynamic collaboration session`, {
      sessionId,
      artifactCount: generatedArtifacts.length
    });

    return NextResponse.json({
      success: true,
      artifacts: Object.values(artifacts).filter(artifact =>
        generatedArtifacts.includes(artifact.id)
      )
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error retrieving artifacts from dynamic collaboration session`, {
      sessionId: params.sessionId,
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: err.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
