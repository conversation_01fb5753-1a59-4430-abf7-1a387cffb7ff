// src/app/(payload)/api/agents/contentGeneration.ts
import { ChatOpenAI } from "@langchain/openai";
import { StateGraph, END, START } from "@langchain/langgraph";
import { RunnableLambda } from "@langchain/core/runnables";
import { SystemMessage } from "@langchain/core/messages";
import { JsonOutputParser } from "@langchain/core/output_parsers";
import { v4 as uuidv4 } from 'uuid';
import { 
    A2ATask, 
    AgentCard,
    A2AMessage, 
    TaskState,
    Part,
    Artifact
} from './a2atypes';
import {
    createOrUpdateTask,
    getTask,
    cancelTask,
    updateTaskStatus,
    addArtifactToTask,
    createTextMessage,
    createDataMessage,
    createTextArtifact,
    initializeAgentCards
} from './a2aimplementation';
import {
    ContentStructure,
    ContentStructureProposal,
    ContentStructureFeedback,
    ContentStructureNegotiation,
    initializeContentStructureNegotiation
} from './dynamicContentStructure';

// Define the state interface for our content generation agent
export interface ContentGenerationState {
    contentType: 'product-page' | 'blog-article' | 'buying-guide';
    topicFocus: string;
    category: string;
    targetAudience: string;
    primaryKeywords: string[];
    tonePreference: string;
    competitorUrls?: string[];
    internalData?: any;
    generalInstructions?: string;
    feedback?: string;
    isRetry?: boolean;

    // Agent discussion and planning
    planningPhase?: {
        contentLeadPlan?: string;
        marketResearchPlan?: string;
        seoKeywordPlan?: string;
        contentStrategyPlan?: string;
        contentWritingPlan?: string;
        editorialPlan?: string;
        // Agent plans and guidelines
        coordinationPlan?: string;
        agentGuidelines?: Record<string, string>;
        researchPlan?: string;
        keywordResearchPlan?: string;
    };

    // Enhanced discussion phase with multi-turn exchanges
    discussionPhase?: {
        // Discussion coordinator outputs
        coordinatorSummary?: string;
        marketResearchPrompt?: string;
        seoKeywordPrompt?: string;
        contentStrategyPrompt?: string;
        editorialPrompt?: string;
        
        // First round discussions
        marketResearchDiscussion?: string;
        seoKeywordDiscussion?: string;
        contentStrategyDiscussion?: string;
        
        // Second round discussions with feedback
        marketResearchFeedback?: string;
        seoKeywordFeedback?: string;
        contentStrategyFeedback?: string;
        
        // Final round with resolution
        marketResearchResolution?: string;
        seoKeywordResolution?: string;
        contentStrategyResolution?: string;
        
        // Content lead summary and final plan
        contentLeadSummary?: string;
        finalContentPlan?: any;
        
        // Chain of thought reasoning for key decisions
        decisionReasoning?: {
            keywordStrategy?: string;
            contentStructure?: string;
            targetAudienceApproach?: string;
            toneAndStyle?: string;
        };
        
        // Additional properties for discussion phase
        discussionSummary?: string;
        discussionPrompts?: Record<string, string>;
        feedbackPrompts?: Record<string, string>;
        marketResearchFeedbackPrompt?: string;
        seoKeywordFeedbackPrompt?: string;
        contentStrategyFeedbackPrompt?: string;
        resolutionPrompts?: Record<string, string>;
        keyDecisionsNeeded?: string[];
        collaborationFramework?: any;
        marketResearchReasoning?: string;
        marketResearchAgreements?: string[];
        marketResearchResolutionPrompt?: string;
        marketResearchDiscussionSummary?: string;
        keyDecisions?: any[];
        
        // Additional properties found in code
        marketResearchApproach?: string;
        marketResearchConflicts?: string[];
        marketResearchFinalApproach?: string;
        conflictResolutions?: any;
    };
    contentStructureNegotiation?: ContentStructureNegotiation;
    finalContentStructure?: ContentStructure;
    // Execution phase with feedback loops
    executionPhase?: {
        // Feedback between agents
        marketResearchToSEO?: string;
        seoToContentStrategy?: string;
        contentStrategyToWriter?: string;
        
        // Iteration tracking
        currentIteration?: number;
        maxIterations?: number;
        
        // Improvement suggestions between iterations
        improvementSuggestions?: {
            marketResearch?: string[];
            seoKeywords?: string[];
            contentStrategy?: string[];
            content?: string[];
        };
    };

    // Review phase with comparison
    reviewPhase?: {
        // Editorial review
        editorialFeedback?: string;
        contentBeforeEdits?: any;
        contentAfterEdits?: any;
        
        // SEO review
        seoFeedback?: string;
        contentBeforeSEO?: any;
        contentAfterSEO?: any;
        
        // Comparison of versions
        versionComparison?: {
            original?: any;
            edited?: any;
            seoOptimized?: any;
            final?: any;
            comparisonNotes?: string;
        };
    };

    // Agent results
    marketResearchResult?: any;
    seoKeywordResult?: any;
    contentStrategyResult?: any;
    contentResult?: any;
    editorialResult?: any;
    seoOptimizationResult?: any;
    interlinkingResult?: any;
    scoringResult?: any;

    // Refinement phase results
    refinementResults?: {
        iteration?: number;
        improvements?: string[];
        refinedContent?: any;
        comparisonToOriginal?: string;
    };
 
    error?: string;
    a2aTasks?: Record<string, A2ATask>;
    currentTaskId?: string;
    agentCards?: Record<string, AgentCard>;
    // Add to ContentGenerationState interface
    collaborationContext?: {
        topic: string;
        collaborationHistory: Array<{
            phase: string;
            action: string;
            timestamp: string;
            [key: string]: any;
        }>;
        agentContributions: Record<string, {
            contribution?: any;
            timestamp: string;
            [key: string]: any;
        }>;
        currentPhase: 'planning' | 'creation' | 'review' | 'completed';
    };
}

// Helper to create OpenAI model instances
function createModel(temp: number = 0) {
    return new ChatOpenAI({
        temperature: temp,
        modelName: "gpt-3.5-turbo-1106",
    });
}

// Create the base model with output parsing for consistent JSON results
const baseModel = createModel(0.7).pipe(new JsonOutputParser());

// Helper function to safely parse JSON responses
async function safeJsonParse(response: any) {
    try {
        // If the response is already an object, return it
        if (typeof response === 'object' && response !== null) {
            return response;
        }
        
        // If it's a string, try to parse it
        if (typeof response === 'string') {
            return JSON.parse(response);
        }
        
        // If it's neither, return a default object
        return {};
    } catch (error) {
        console.error("Error parsing JSON response:", error);
        console.error("Original response:", response);
        return {};
    }
}

// Configure the content generation settings based on type
const GENERATION_CONFIG = {
    'product-page': {
        sections: ['overview', 'features', 'pricing', 'comparison', 'faq'],
        detailLevel: "detailed",
    },
    'blog-article': {
        sections: ['introduction', 'body', 'conclusion'],
        detailLevel: "moderate",
    },
    'buying-guide': {
        sections: ['introduction', 'considerations', 'top-picks', 'conclusion'],
        detailLevel: "comprehensive",
    }
};

// Define the state channel for our graph
const contentGenerationState = {
    agentState: {
        value: (x: ContentGenerationState, y: ContentGenerationState) => (y ? Object.assign({}, x, y) : x),
        default: () => ({
            contentType: "blog-article",
            topicFocus: "",
            category: "",
            targetAudience: "",
            primaryKeywords: [],
            tonePreference: "expert"
        }),
    },
};

// Content Lead Agent - Coordinates the entire process
async function contentLeadAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        // If this is a retry with feedback, add special instructions
        let retryInstructions = '';
        if (state.agentState.isRetry && state.agentState.feedback) {
            retryInstructions = `
          This is a content regeneration request based on user feedback.
          User feedback: "${state.agentState.feedback}"
          
          Please incorporate this feedback in your planning and coordination.
          `;
        }

        const contentLeadPrompt = `
          You are the Content Lead Agent responsible for coordinating the content generation process.
          Your role is to create a high-level plan for generating ${state.agentState.contentType} content about "${state.agentState.topicFocus}".
          
          ${retryInstructions}
          
          Create a coordination plan that outlines:
          1. The overall approach to creating this content
          2. Key considerations for each specialized agent
          3. How the different agents should collaborate
          
          The response should have this JSON structure:
          {
            "coordinationPlan": "...",
            "agentGuidelines": {
              "marketResearch": "...",
              "seoKeyword": "...",
              "contentStrategy": "...",
              "contentWriting": "...",
              "editorial": "..."
            },
            "expectedOutcome": "..."
          }
          `;

        const response = await baseModel.invoke([
            new SystemMessage(
                "You are a Content Lead Agent responsible for coordinating the content generation process."
            ),
            new SystemMessage(contentLeadPrompt)
        ]);
        //console.log("Result in contentLeadAgent", response);

        return {
            agentState: {
                ...state.agentState,
                planningPhase: {
                    contentLeadPlan: response.coordinationPlan,
                    marketResearchPlan: response.agentGuidelines.marketResearch,
                    seoKeywordPlan: response.agentGuidelines.seoKeyword,
                    contentStrategyPlan: response.agentGuidelines.contentStrategy,
                    contentWritingPlan: response.agentGuidelines.contentWriting,
                    editorialPlan: response.agentGuidelines.editorial
                },
            },
        };
    } catch (error) {
        return {
            agentState: {
                ...state.agentState,
                error: `Error in content lead planning: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

// Planning Phase Agents

// 1. Market Research Planning Agent
async function marketResearchPlanningAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        const config = GENERATION_CONFIG[state.agentState.contentType];

        const planningPrompt = `
          You are an expert market researcher planning research for a ${state.agentState.contentType} about "${state.agentState.topicFocus}".
          
          Content Lead's guidelines for you: "${state.agentState.planningPhase?.marketResearchPlan}"
          
          Create a detailed research plan that outlines:
          1. Key market data you need to gather
          2. Competitor analysis approach
          3. Target audience insights needed
          4. Industry trends to research
          
          The response should have this JSON structure:
          {
            "researchPlan": "...",
            "keyQuestions": ["...", "..."],
            "dataSourcesNeeded": ["...", "..."],
            "competitorAnalysisApproach": "..."
          }
          `;

        const response = await baseModel.invoke([
            new SystemMessage(
                "You are a market research specialist creating a research plan."
            ),
            new SystemMessage(planningPrompt)
        ]);
        //console.log("Result in marketResearchPlanningAgent", response);

        return {
            agentState: {
                ...state.agentState,
                planningPhase: {
                    ...state.agentState.planningPhase,
                    marketResearchPlan: response.researchPlan,
                },
            },
        };
    } catch (error) {
        return {
            agentState: {
                ...state.agentState,
                error: `Error in market research planning: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

// 2. SEO Keyword Planning Agent
async function seoKeywordPlanningAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        const config = GENERATION_CONFIG[state.agentState.contentType];

        const planningPrompt = `
          You are an SEO specialist planning keyword research for a ${state.agentState.contentType} about "${state.agentState.topicFocus}".
          
          Content Lead's guidelines for you: "${state.agentState.planningPhase?.seoKeywordPlan}"
          
          The user has provided these primary keywords: ${state.agentState.primaryKeywords.join(', ')}.
          
          Create a detailed keyword research plan that outlines:
          1. How you'll approach keyword research
          2. Types of keywords needed (primary, secondary, long-tail)
          3. Keyword mapping strategy for the content
          
          The response should have this JSON structure:
          {
            "keywordResearchPlan": "...",
            "keywordTypes": {
              "primary": "...",
              "secondary": "...",
              "longTail": "..."
            },
            "keywordMappingStrategy": "..."
          }
          `;

        const response = await baseModel.invoke([
            new SystemMessage(
                "You are an SEO specialist creating a keyword research plan."
            ),
            new SystemMessage(planningPrompt)
        ]);
        //console.log("Result in seoKeywordPlanningAgent", response);

        return {
            agentState: {
                ...state.agentState,
                planningPhase: {
                    ...state.agentState.planningPhase,
                    seoKeywordPlan: response.keywordResearchPlan,
                },
            },
        };
    } catch (error) {
        return {
            agentState: {
                ...state.agentState,
                error: `Error in SEO keyword planning: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

// 3. Content Strategy Planning Agent
async function contentStrategyPlanningAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        const config = GENERATION_CONFIG[state.agentState.contentType];

        const planningPrompt = `
          You are a content strategist planning content for a ${state.agentState.contentType} about "${state.agentState.topicFocus}".
          
          Content Lead's guidelines for you: "${state.agentState.planningPhase?.contentStrategyPlan}"
          
          Create a detailed content strategy plan that outlines:
          1. Content structure and sections
          2. Key messaging points
          3. Content format and style
          4. Target audience considerations
          
          The response should have this JSON structure:
          {
            "contentStrategyPlan": "...",
            "contentStructure": ["...", "..."],
            "keyMessaging": ["...", "..."],
            "formatAndStyle": "..."
          }
          `;

        const response = await baseModel.invoke([
            new SystemMessage(
                "You are a content strategist creating a content plan."
            ),
            new SystemMessage(planningPrompt)
        ]);
        //console.log("Result in contentStrategyPlanningAgent", response);

        return {
            agentState: {
                ...state.agentState,
                planningPhase: {
                    ...state.agentState.planningPhase,
                    contentStrategyPlan: response.contentStrategyPlan,
                },
            },
        };
    } catch (error) {
        return {
            agentState: {
                ...state.agentState,
                error: `Error in content strategy planning: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

// Discussion Phase Agents

// 1. Discussion Coordinator Agent
async function discussionCoordinatorAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        const discussionPrompt = `
          You are the Discussion Coordinator Agent responsible for facilitating collaborative discussion between specialized agents.
          
          Review the planning phase outputs:
          - Market Research Plan: "${state.agentState.planningPhase?.marketResearchPlan}"
          - SEO Keyword Plan: "${state.agentState.planningPhase?.seoKeywordPlan}"
          - Content Strategy Plan: "${state.agentState.planningPhase?.contentStrategyPlan}"
          
          Your task is to:
          1. Identify areas where agents need to collaborate and potential conflicts to resolve
          2. Create discussion prompts for each agent that encourage detailed responses with reasoning
          3. Design a multi-turn discussion flow where agents can provide feedback on each other's ideas
          4. Establish key decision points that need to be resolved through discussion
          
          The agents will engage in multiple rounds of discussion:
          - Round 1: Initial perspectives and approaches
          - Round 2: Feedback on other agents' perspectives
          - Round 3: Resolution of conflicts and final positions
          
          For each agent, create prompts that encourage them to:
          - Explain their reasoning (chain-of-thought)
          - Identify potential conflicts with other agents' approaches
          - Suggest collaborative solutions
          - Reference specific points from other agents' outputs
          
          The response should have this JSON structure:
          {
            "discussionSummary": "Detailed overview of the discussion objectives and process",
            "discussionPrompts": {
              "marketResearch": "Prompt for market research agent with specific questions",
              "seoKeyword": "Prompt for SEO keyword agent with specific questions",
              "contentStrategy": "Prompt for content strategy agent with specific questions"
            },
            "feedbackPrompts": {
              "marketResearch": "Prompt for market research agent to provide feedback on others",
              "seoKeyword": "Prompt for SEO keyword agent to provide feedback on others",
              "contentStrategy": "Prompt for content strategy agent to provide feedback on others"
            },
            "resolutionPrompts": {
              "marketResearch": "Prompt for market research agent to resolve conflicts",
              "seoKeyword": "Prompt for SEO keyword agent to resolve conflicts",
              "contentStrategy": "Prompt for content strategy agent to resolve conflicts"
            },
            "keyDecisionsNeeded": [
              "Specific decision 1 with options and considerations",
              "Specific decision 2 with options and considerations",
              "Specific decision 3 with options and considerations"
            ],
            "collaborationFramework": "Structure for how agents should collaborate"
          }
          `;

        const response = await baseModel.invoke([
            new SystemMessage(
                "You are a Discussion Coordinator Agent facilitating collaborative discussion between specialized agents. Your goal is to create a structured discussion that leads to high-quality content decisions through reasoned debate and feedback."
            ),
            new SystemMessage(discussionPrompt)
        ]);
        //console.log("Result in discussionCoordinatorAgent", response);

        return {
            agentState: {
                ...state.agentState,
                discussionPhase: {
                    coordinatorSummary: response.discussionSummary,
                    marketResearchPrompt: response.discussionPrompts.marketResearch,
                    seoKeywordPrompt: response.discussionPrompts.seoKeyword,
                    contentStrategyPrompt: response.discussionPrompts.contentStrategy,
                    // Store feedback prompts for second round
                    marketResearchFeedbackPrompt: response.feedbackPrompts.marketResearch,
                    seoKeywordFeedbackPrompt: response.feedbackPrompts.seoKeyword,
                    contentStrategyFeedbackPrompt: response.feedbackPrompts.contentStrategy,
                    // Store resolution prompts for third round
                    marketResearchResolutionPrompt: response.resolutionPrompts.marketResearch,
                    seoKeywordResolutionPrompt: response.resolutionPrompts.seoKeyword,
                    contentStrategyResolutionPrompt: response.resolutionPrompts.contentStrategy,
                    // Store key decisions and collaboration framework
                    keyDecisionsNeeded: response.keyDecisionsNeeded,
                    collaborationFramework: response.collaborationFramework
                },
            },
        };
    } catch (error) {
        return {
            agentState: {
                ...state.agentState,
                error: `Error in discussion coordination: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

// 2. Market Research Discussion Agent
async function marketResearchDiscussionAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        const discussionPrompt = `
          You are a Market Research Agent participating in a collaborative discussion.
          
          Discussion prompt: "${state.agentState.discussionPhase?.marketResearchPrompt}"
          
          Respond to this prompt with your insights and considerations as a market researcher.
          
          IMPORTANT: In your response, explicitly show your reasoning process (chain-of-thought):
          1. First, analyze what information would be most valuable for this ${state.agentState.contentType} about "${state.agentState.topicFocus}"
          2. Then, explain how you would gather this information
          3. Consider potential challenges in researching this topic
          4. Explain how your research approach will benefit the content creation process
          5. Identify any specific areas where you'll need input from SEO and Content Strategy
          
          Your response MUST be valid JSON with the following structure:
          {
            "discussionResponse": "Your detailed response showing chain-of-thought reasoning",
            "questionsForOtherAgents": {
              "seoKeyword": "Specific questions for the SEO agent",
              "contentStrategy": "Specific questions for the Content Strategy agent"
            },
            "researchApproach": {
              "primaryResearch": "How you would approach primary research",
              "secondaryResearch": "How you would approach secondary research",
              "competitorAnalysis": "How you would analyze competitors",
              "audienceInsights": "How you would gather audience insights"
            },
            "potentialChallenges": ["Challenge 1", "Challenge 2"],
            "reasoningProcess": "Explicit explanation of your thought process and how you arrived at your conclusions"
          }
          `;

        // Use a try-catch block specifically for the model invocation
        let modelResponse;
        try {
            modelResponse = await baseModel.invoke([
                new SystemMessage(
                    "You are a Market Research Agent participating in a collaborative discussion. Always respond with valid JSON. Show your reasoning process explicitly."
                ),
                new SystemMessage(discussionPrompt)
            ]);
        } catch (modelError) {
            console.error("Error in model invocation:", modelError);
            // Provide a fallback response if the model fails
            modelResponse = {
                discussionResponse: "Unable to generate market research discussion due to an error.",
                questionsForOtherAgents: {
                    seoKeyword: "What keywords should we focus on?",
                    contentStrategy: "What content structure would work best?"
                },
                researchApproach: {
                    primaryResearch: "Basic survey methodology",
                    secondaryResearch: "Industry reports and publications",
                    competitorAnalysis: "Review of top competitors",
                    audienceInsights: "Demographic analysis"
                },
                potentialChallenges: ["Limited data availability", "Rapidly changing market"],
                reasoningProcess: "Would analyze market trends and competitor positioning to inform content strategy."
            };
        }

        // Ensure we have a valid response object
        const response = await safeJsonParse(modelResponse);
        console.log("Validated response in marketResearchDiscussionAgent", response);

        return {
            agentState: {
                ...state.agentState,
                discussionPhase: {
                    ...state.agentState.discussionPhase,
                    marketResearchDiscussion: response.discussionResponse || "No valid discussion response generated.",
                    marketResearchReasoning: response.reasoningProcess || "No reasoning process provided.",
                    marketResearchApproach: response.researchApproach || {},
                    marketResearchChallenges: response.potentialChallenges || []
                },
            },
        };
    } catch (error) {
        console.error("Critical error in marketResearchDiscussionAgent:", error);
        return {
            agentState: {
                ...state.agentState,
                error: `Error in market research discussion: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

// Market Research Feedback Agent - Second Round
async function marketResearchFeedbackAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        const feedbackPrompt = `
          You are a Market Research Agent providing feedback on the perspectives shared by other agents.
          
          Feedback prompt: "${state.agentState.discussionPhase?.marketResearchFeedbackPrompt}"
          
          Review what other agents have shared:
          
          SEO Agent said: "${state.agentState.discussionPhase?.seoKeywordDiscussion}"
          
          Content Strategy Agent said: "${state.agentState.discussionPhase?.contentStrategyDiscussion}"
          
          Your previous contribution: "${state.agentState.discussionPhase?.marketResearchDiscussion}"
          
          IMPORTANT: In your feedback, explicitly show your reasoning process (chain-of-thought):
          1. First, identify points of agreement with the other agents
          2. Then, identify potential conflicts or misalignments
          3. Analyze how the SEO and Content Strategy approaches might impact your research approach
          4. Suggest specific adjustments to align your approach with theirs
          5. Explain your reasoning for each suggestion
          
          Your response MUST be valid JSON with the following structure:
          {
            "feedbackResponse": "Your detailed feedback showing chain-of-thought reasoning",
            "pointsOfAgreement": [
              {"agent": "SEO", "point": "Specific point you agree with", "reasoning": "Why you agree"},
              {"agent": "ContentStrategy", "point": "Specific point you agree with", "reasoning": "Why you agree"}
            ],
            "pointsOfConflict": [
              {"agent": "SEO", "conflict": "Specific conflict", "impact": "How it affects research", "suggestion": "How to resolve"},
              {"agent": "ContentStrategy", "conflict": "Specific conflict", "impact": "How it affects research", "suggestion": "How to resolve"}
            ],
            "adjustedApproach": {
              "primaryResearch": "Adjusted primary research approach",
              "secondaryResearch": "Adjusted secondary research approach",
              "competitorAnalysis": "Adjusted competitor analysis approach",
              "audienceInsights": "Adjusted audience insights approach"
            },
            "reasoningProcess": "Explicit explanation of your thought process and how you arrived at your conclusions"
          }
          `;

        // Use a try-catch block specifically for the model invocation
        let modelResponse;
        try {
            modelResponse = await baseModel.invoke([
                new SystemMessage(
                    "You are a Market Research Agent providing feedback in a collaborative discussion. Always respond with valid JSON. Show your reasoning process explicitly."
                ),
                new SystemMessage(feedbackPrompt)
            ]);
        } catch (modelError) {
            console.error("Error in model invocation:", modelError);
            // Provide a fallback response if the model fails
            modelResponse = {
                feedbackResponse: "Unable to generate market research feedback due to an error.",
                pointsOfAgreement: [
                    {agent: "SEO", point: "Keyword focus", reasoning: "Aligns with market trends"},
                    {agent: "ContentStrategy", point: "Audience targeting", reasoning: "Matches research findings"}
                ],
                pointsOfConflict: [
                    {agent: "SEO", conflict: "Keyword priority", impact: "May skew research focus", suggestion: "Balance keyword volume with audience needs"},
                    {agent: "ContentStrategy", conflict: "Content depth", impact: "Research scope", suggestion: "Adjust research depth to match content requirements"}
                ],
                adjustedApproach: {
                    primaryResearch: "Focus on validating keyword relevance with target audience",
                    secondaryResearch: "Prioritize sources that align with SEO strategy",
                    competitorAnalysis: "Analyze how competitors balance keywords and content structure",
                    audienceInsights: "Gather insights on audience response to proposed content structure"
                },
                reasoningProcess: "Analyzed points of alignment and conflict to create a research approach that supports both SEO and content strategy goals."
            };
        }

        // Ensure we have a valid response object
        const response = await safeJsonParse(modelResponse);
        console.log("Validated response in marketResearchFeedbackAgent", response);

        return {
            agentState: {
                ...state.agentState,
                discussionPhase: {
                    ...state.agentState.discussionPhase,
                    marketResearchFeedback: response.feedbackResponse || "No valid feedback response generated.",
                    marketResearchAgreements: response.pointsOfAgreement || [],
                    marketResearchConflicts: response.pointsOfConflict || [],
                    marketResearchAdjustedApproach: response.adjustedApproach || {},
                    marketResearchFeedbackReasoning: response.reasoningProcess || "No reasoning process provided."
                },
            },
        };
    } catch (error) {
        console.error("Critical error in marketResearchFeedbackAgent:", error);
        return {
            agentState: {
                ...state.agentState,
                error: `Error in market research feedback: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

// Market Research Resolution Agent - Third Round
async function marketResearchResolutionAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        const resolutionPrompt = `
          You are a Market Research Agent finalizing your approach after collaborative discussion.
          
          Resolution prompt: "${state.agentState.discussionPhase?.marketResearchResolutionPrompt}"
          
          Review the full discussion history:
          
          Your initial contribution: "${state.agentState.discussionPhase?.marketResearchDiscussion}"
          
          SEO Agent said: "${state.agentState.discussionPhase?.seoKeywordDiscussion}"
          
          Content Strategy Agent said: "${state.agentState.discussionPhase?.contentStrategyDiscussion}"
          
          Your feedback: "${state.agentState.discussionPhase?.marketResearchFeedback}"
          
          SEO Agent feedback: "${state.agentState.discussionPhase?.seoKeywordFeedback}"
          
          Content Strategy Agent feedback: "${state.agentState.discussionPhase?.contentStrategyFeedback}"
          
          IMPORTANT: In your resolution, explicitly show your reasoning process (chain-of-thought):
          1. First, summarize the key points from the discussion
          2. Then, explain how you've integrated feedback from other agents
          3. Describe your final research approach and how it aligns with SEO and content strategy
          4. Justify any decisions where you've chosen to maintain your original approach
          5. Explain how your final approach will benefit the overall content creation process
          
          Your response MUST be valid JSON with the following structure:
          {
            "resolutionResponse": "Your detailed resolution showing chain-of-thought reasoning",
            "discussionSummary": "Summary of key points from the discussion",
            "integratedFeedback": [
              {"source": "SEO", "feedback": "Specific feedback", "integration": "How you integrated it"},
              {"source": "ContentStrategy", "feedback": "Specific feedback", "integration": "How you integrated it"}
            ],
            "finalResearchApproach": {
              "primaryResearch": "Final primary research approach",
              "secondaryResearch": "Final secondary research approach",
              "competitorAnalysis": "Final competitor analysis approach",
              "audienceInsights": "Final audience insights approach",
              "deliverables": ["Specific research deliverable 1", "Specific research deliverable 2"]
            },
            "justifications": [
              {"decision": "Specific decision", "reasoning": "Why you made this decision"}
            ],
            "benefitsToContentCreation": ["Specific benefit 1", "Specific benefit 2"],
            "reasoningProcess": "Explicit explanation of your thought process and how you arrived at your conclusions"
          }
          `;

        // Use a try-catch block specifically for the model invocation
        let modelResponse;
        try {
            modelResponse = await baseModel.invoke([
                new SystemMessage(
                    "You are a Market Research Agent finalizing your approach after collaborative discussion. Always respond with valid JSON. Show your reasoning process explicitly."
                ),
                new SystemMessage(resolutionPrompt)
            ]);
        } catch (modelError) {
            console.error("Error in model invocation:", modelError);
            // Provide a fallback response if the model fails
            modelResponse = {
                resolutionResponse: "Unable to generate market research resolution due to an error.",
                discussionSummary: "Discussion focused on aligning research with SEO and content strategy needs.",
                integratedFeedback: [
                    {source: "SEO", feedback: "Keyword prioritization", integration: "Incorporated keyword validation in research"},
                    {source: "ContentStrategy", feedback: "Content structure", integration: "Adjusted research to inform content structure"}
                ],
                finalResearchApproach: {
                    primaryResearch: "Targeted surveys of audience segments",
                    secondaryResearch: "Analysis of industry reports and competitor content",
                    competitorAnalysis: "Evaluation of top competitors' content and keyword strategies",
                    audienceInsights: "Demographic and psychographic analysis of target audience",
                    deliverables: ["Audience persona profiles", "Competitor content analysis", "Market trend report"]
                },
                justifications: [
                    {decision: "Focus on audience needs over keyword volume", reasoning: "Will result in more engaging content that naturally incorporates relevant keywords"}
                ],
                benefitsToContentCreation: ["Data-driven content decisions", "Better audience targeting", "More effective competitive positioning"],
                reasoningProcess: "Balanced SEO needs with content strategy requirements to create a research approach that supports both while prioritizing audience value."
            };
        }

        // Ensure we have a valid response object
        const response = await safeJsonParse(modelResponse);
        console.log("Validated response in marketResearchResolutionAgent", response);

        return {
            agentState: {
                ...state.agentState,
                discussionPhase: {
                    ...state.agentState.discussionPhase,
                    marketResearchResolution: response.resolutionResponse || "No valid resolution response generated.",
                    marketResearchDiscussionSummary: response.discussionSummary || "No discussion summary provided.",
                    marketResearchFinalApproach: response.finalResearchApproach || {},
                    marketResearchJustifications: response.justifications || [],
                    marketResearchBenefits: response.benefitsToContentCreation || [],
                    marketResearchResolutionReasoning: response.reasoningProcess || "No reasoning process provided."
                },
            },
        };
    } catch (error) {
        console.error("Critical error in marketResearchResolutionAgent:", error);
        return {
            agentState: {
                ...state.agentState,
                error: `Error in market research resolution: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

// 3. SEO Keyword Discussion Agent
async function seoKeywordDiscussionAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        const discussionPrompt = `
          You are an SEO Specialist participating in a content team discussion.
          
          Discussion prompt: "${state.agentState.discussionPhase?.seoKeywordPrompt}"
          
          Market Research Agent said: "${state.agentState.discussionPhase?.marketResearchDiscussion}"
          
          Respond to these points with your insights and considerations as an SEO specialist.
          Consider how your keyword strategy might need to be adjusted based on market research and content strategy needs.
          
          IMPORTANT: Your response MUST be valid JSON with the following structure:
          {
            "discussionResponse": "...",
            "questionsForOtherAgents": {
              "contentStrategy": "...",
              "contentWriting": "..."
            },
            "adjustedKeywordStrategy": "..."
          }
          
          Do not include any text outside of the JSON structure. Ensure all quotes are properly escaped.
          `;

        // Use a try-catch block specifically for the model invocation
        let modelResponse;
        try {
            modelResponse = await baseModel.invoke([
                new SystemMessage(
                    "You are an SEO Specialist participating in a content team discussion. Always respond with valid JSON."
                ),
                new SystemMessage(discussionPrompt)
            ]);
        } catch (modelError) {
            console.error("Error in model invocation:", modelError);
            // Provide a fallback response if the model fails
            modelResponse = {
                discussionResponse: "Unable to generate SEO keyword discussion due to an error.",
                questionsForOtherAgents: {
                    contentStrategy: "How should we structure the content for SEO?",
                    contentWriting: "How can we naturally incorporate these keywords?"
                },
                adjustedKeywordStrategy: "Focus on primary keywords with natural variations."
            };
        }

        // Ensure we have a valid response object
        const response = await safeJsonParse(modelResponse);
        console.log("Validated response in seoKeywordDiscussionAgent", response);

        return {
            agentState: {
                ...state.agentState,
                discussionPhase: {
                    ...state.agentState.discussionPhase,
                    seoKeywordDiscussion: response.discussionResponse || "No valid discussion response generated.",
                },
            },
        };
    } catch (error) {
        console.error("Critical error in seoKeywordDiscussionAgent:", error);
        return {
            agentState: {
                ...state.agentState,
                error: `Error in SEO keyword discussion: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

// 4. Content Strategy Discussion Agent
async function contentStrategyDiscussionAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        const discussionPrompt = `
          You are a Content Strategist participating in a content team discussion.
          
          Discussion prompt: "${state.agentState.discussionPhase?.contentStrategyPrompt}"
          
          Market Research Agent said: "${state.agentState.discussionPhase?.marketResearchDiscussion}"
          SEO Specialist said: "${state.agentState.discussionPhase?.seoKeywordDiscussion}"
          
          Respond to these points with your insights and considerations as a content strategist.
          Consider how your content strategy might need to be adjusted based on market research and SEO needs.
          
          IMPORTANT: Your response MUST be valid JSON with the following structure:
          {
            "discussionResponse": "...",
            "questionsForOtherAgents": {
              "contentWriting": "...",
              "editorial": "..."
            },
            "adjustedContentStrategy": "..."
          }
          
          Do not include any text outside of the JSON structure. Ensure all quotes are properly escaped.
          `;

        // Use a try-catch block specifically for the model invocation
        let modelResponse;
        try {
            modelResponse = await baseModel.invoke([
                new SystemMessage(
                    "You are a Content Strategist participating in a content team discussion. Always respond with valid JSON."
                ),
                new SystemMessage(discussionPrompt)
            ]);
        } catch (modelError) {
            console.error("Error in model invocation:", modelError);
            // Provide a fallback response if the model fails
            modelResponse = {
                discussionResponse: "Unable to generate content strategy discussion due to an error.",
                questionsForOtherAgents: {
                    contentWriting: "What tone and style should we use for this content?",
                    editorial: "What quality standards should we focus on?"
                },
                adjustedContentStrategy: "Focus on a clear structure with SEO optimization."
            };
        }

        // Ensure we have a valid response object
        const response = await safeJsonParse(modelResponse);
        console.log("Validated response in contentStrategyDiscussionAgent", response);

        return {
            agentState: {
                ...state.agentState,
                discussionPhase: {
                    ...state.agentState.discussionPhase,
                    contentStrategyDiscussion: response.discussionResponse || "No valid discussion response generated.",
                },
            },
        };
    } catch (error) {
        console.error("Critical error in contentStrategyDiscussionAgent:", error);
        return {
            agentState: {
                ...state.agentState,
                error: `Error in content strategy discussion: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

// 5. Content Lead Summary Agent
async function contentLeadSummaryAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        const summaryPrompt = `
          You are the Content Lead Agent summarizing the team discussion and finalizing the content plan.
          
          Review the complete multi-turn discussion:
          
          MARKET RESEARCH AGENT:
          - Initial perspective: "${state.agentState.discussionPhase?.marketResearchDiscussion}"
          - Feedback on others: "${state.agentState.discussionPhase?.marketResearchFeedback}"
          - Final resolution: "${state.agentState.discussionPhase?.marketResearchResolution}"
          
          SEO SPECIALIST:
          - Initial perspective: "${state.agentState.discussionPhase?.seoKeywordDiscussion}"
          - Feedback (if available): "${state.agentState.discussionPhase?.seoKeywordFeedback || ''}"
          - Final resolution (if available): "${state.agentState.discussionPhase?.seoKeywordResolution || ''}"
          
          CONTENT STRATEGIST:
          - Initial perspective: "${state.agentState.discussionPhase?.contentStrategyDiscussion}"
          - Feedback (if available): "${state.agentState.discussionPhase?.contentStrategyFeedback || ''}"
          - Final resolution (if available): "${state.agentState.discussionPhase?.contentStrategyResolution || ''}"
          
          KEY DECISIONS NEEDED:
          ${JSON.stringify(state.agentState.discussionPhase?.keyDecisionsNeeded || [])}
          
          Your task is to:
          1. Analyze the complete discussion thread across all rounds
          2. Identify the key insights, agreements, and resolutions
          3. Resolve any remaining conflicts or open questions
          4. Create a comprehensive final content plan that integrates all agent perspectives
          5. Provide explicit reasoning for each major decision in the plan
          
          IMPORTANT: Your response MUST be valid JSON with the following structure:
          {
            "discussionSummary": "Comprehensive summary of the multi-turn discussion, highlighting key insights and how perspectives evolved",
            
            "keyDecisions": [
              {
                "decision": "Specific decision made",
                "reasoning": "Detailed reasoning for this decision, referencing specific agent inputs",
                "impact": "How this decision will impact the content"
              },
              ...
            ],
            
            "conflictResolutions": [
              {
                "conflict": "Description of the conflict between agents",
                "resolution": "How you resolved this conflict",
                "reasoning": "Why you chose this resolution approach"
              },
              ...
            ],
            
            "finalContentPlan": {
              "contentObjective": "Clear statement of the content's primary objective",
              "targetAudience": {
                "primary": "Description of primary audience",
                "secondary": "Description of secondary audience",
                "needs": ["Specific need 1", "Specific need 2"]
              },
              "contentStructure": [
                {
                  "section": "Section name",
                  "purpose": "Purpose of this section",
                  "keyPoints": ["Key point 1", "Key point 2"],
                  "keywordTargeting": ["Primary keyword", "Secondary keyword"],
                  "estimatedLength": "Word count estimate"
                },
                ...
              ],
              "keywordStrategy": {
                "primary": "Primary keyword strategy",
                "secondary": "Secondary keyword strategy",
                "distribution": "How keywords should be distributed"
              },
              "toneAndStyle": "Detailed description of tone and style approach",
              "contentDifferentiators": ["What will make this content stand out 1", "What will make this content stand out 2"]
            },
            
            "executionGuidance": {
              "marketResearch": "Specific guidance for market research execution",
              "seoKeywords": "Specific guidance for SEO keyword execution",
              "contentStrategy": "Specific guidance for content strategy execution",
              "contentCreation": "Specific guidance for content creation",
              "qualityChecks": ["Quality check 1", "Quality check 2"]
            },
            
            "reasoningProcess": "Detailed explanation of your thought process in creating this final plan, including how you weighed different perspectives"
          }
          
          Do not include any text outside of the JSON structure. Ensure all quotes are properly escaped.
          `;

        // Use a try-catch block specifically for the model invocation
        let modelResponse;
        try {
            modelResponse = await baseModel.invoke([
                new SystemMessage(
                    "You are the Content Lead Agent summarizing a collaborative team discussion and finalizing a content plan. Your role is to integrate multiple perspectives into a cohesive strategy. Always respond with valid JSON."
                ),
                new SystemMessage(summaryPrompt)
            ]);
        } catch (modelError) {
            console.error("Error in model invocation:", modelError);
            // Provide a fallback response if the model fails
            modelResponse = {
                discussionSummary: "The team engaged in a multi-turn discussion to align market research, SEO, and content strategy approaches.",
                keyDecisions: [
                    {
                        decision: "Focus on audience needs balanced with SEO requirements",
                        reasoning: "Market research emphasized audience value while SEO highlighted keyword importance",
                        impact: "Content will be optimized for both engagement and search visibility"
                    },
                    {
                        decision: "Structured content approach with clear sections",
                        reasoning: "Content strategist recommended clear structure for readability and SEO",
                        impact: "Improved user experience and better keyword targeting"
                    }
                ],
                conflictResolutions: [
                    {
                        conflict: "Depth of keyword research vs. breadth of audience research",
                        resolution: "Balanced approach with targeted keyword validation through audience research",
                        reasoning: "This satisfies both SEO requirements and ensures content relevance to audience"
                    }
                ],
                finalContentPlan: {
                    contentObjective: `Create a comprehensive ${state.agentState.contentType} about ${state.agentState.topicFocus} that engages the target audience and ranks well in search`,
                    targetAudience: {
                        primary: state.agentState.targetAudience,
                        secondary: "Adjacent audience segments with interest in the topic",
                        needs: ["Clear information", "Actionable insights", "Comprehensive coverage"]
                    },
                    contentStructure: [
                        {
                            section: "Introduction",
                            purpose: "Hook the reader and establish relevance",
                            keyPoints: ["Establish the topic's importance", "Preview key benefits"],
                            keywordTargeting: [state.agentState.primaryKeywords[0] || "Primary keyword"],
                            estimatedLength: "300 words"
                        },
                        {
                            section: "Main Content",
                            purpose: "Deliver core value and information",
                            keyPoints: ["Detailed explanation", "Evidence and examples"],
                            keywordTargeting: ["Secondary keywords", "Long-tail variations"],
                            estimatedLength: "1000-1500 words"
                        },
                        {
                            section: "Conclusion",
                            purpose: "Summarize and drive action",
                            keyPoints: ["Recap key points", "Call to action"],
                            keywordTargeting: ["Primary keyword", "Action-oriented terms"],
                            estimatedLength: "200-300 words"
                        }
                    ],
                    keywordStrategy: {
                        primary: `Focus on ${state.agentState.primaryKeywords[0] || "primary keyword"} in title, headers, and key positions`,
                        secondary: "Distribute secondary keywords naturally throughout content",
                        distribution: "Natural integration prioritizing readability"
                    },
                    toneAndStyle: state.agentState.tonePreference,
                    contentDifferentiators: ["Research-backed insights", "Actionable recommendations", "Comprehensive coverage"]
                },
                executionGuidance: {
                    marketResearch: "Focus on validating audience needs and competitive gaps",
                    seoKeywords: "Prioritize natural keyword integration over density",
                    contentStrategy: "Ensure clear structure with logical flow between sections",
                    contentCreation: "Balance information density with readability and engagement",
                    qualityChecks: ["Keyword optimization without sacrificing readability", "Accuracy of information", "Alignment with audience needs"]
                },
                reasoningProcess: "Analyzed the multi-turn discussion to identify points of agreement and conflict. Prioritized approaches that satisfied multiple objectives while maintaining content quality and audience focus."
            };
        }

        // Ensure we have a valid response object
        const response = await safeJsonParse(modelResponse);
        console.log("Validated response in contentLeadSummaryAgent", response);

        return {
            agentState: {
                ...state.agentState,
                discussionPhase: {
                    ...state.agentState.discussionPhase,
                    contentLeadSummary: response.discussionSummary || "No valid summary generated.",
                    finalContentPlan: response.finalContentPlan || {
                        contentStructure: ["Introduction", "Main sections", "Conclusion"],
                        keywordStrategy: "Basic keyword optimization",
                        targetAudienceFocus: state.agentState.targetAudience || "General audience",
                        contentTone: state.agentState.tonePreference || "Professional"
                    },
                    keyDecisions: response.keyDecisions || [],
                    conflictResolutions: response.conflictResolutions || [],
                    executionGuidance: response.executionGuidance || {},
                    contentLeadReasoning: response.reasoningProcess || "No reasoning process provided."
                },
            },
        };
    } catch (error) {
        console.error("Critical error in contentLeadSummaryAgent:", error);
        return {
            agentState: {
                ...state.agentState,
                error: `Error in content lead summary: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}


// Execution Phase Agents

// 1. Market Research Agent
export async function marketResearchAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        const config = GENERATION_CONFIG[state.agentState.contentType];

        const researchPrompt = `
          You are an expert market researcher specializing in ${state.agentState.category}. 
          I need you to gather key information about the topic: "${state.agentState.topicFocus}".
          
          This content is targeted at ${state.agentState.targetAudience} and should focus on these keywords: ${state.agentState.primaryKeywords.join(', ')}.
          
          Based on the team discussion and final content plan: "${state.agentState.discussionPhase?.contentLeadSummary}"
          
          IMPORTANT: Your response MUST be valid JSON with the following structure:
          {
            "marketTrends": [{"trend": "..."}, ...],
            "painPoints": [{"point": "..."}, ...],
            "competitors": [{"name": "...", "positioning": "..."}, ...],
            "statistics": [{"stat": "...", "source": "..."}, ...]
          }
          
          Do not include any text outside of the JSON structure. Ensure all quotes are properly escaped.
          Detail level should be ${config.detailLevel}.
          `;

        // Use a try-catch block specifically for the model invocation
        let modelResponse;
        try {
            modelResponse = await baseModel.invoke([
                new SystemMessage(
                    "You are a market research specialist with deep knowledge of various industries. Always respond with valid JSON."
                ),
                new SystemMessage(researchPrompt)
            ]);
        } catch (modelError) {
            console.error("Error in model invocation:", modelError);
            // Provide a fallback response if the model fails
            modelResponse = {
                marketTrends: [{trend: "Growing interest in this topic area"}],
                painPoints: [{point: "Lack of clear information"}],
                competitors: [{name: "Major players", positioning: "Market leaders"}],
                statistics: [{stat: "Estimated market growth", source: "Industry analysis"}]
            };
        }

        // Ensure we have a valid response object
        const response = await safeJsonParse(modelResponse);
        console.log("Validated response in marketResearchAgent", response);

        return {
            agentState: {
                ...state.agentState,
                marketResearchResult: response,
            },
        };
    } catch (error) {
        console.error("Critical error in marketResearchAgent:", error);
        return {
            agentState: {
                ...state.agentState,
                error: `Error in market research: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

// 2. SEO Keyword Agent
export async function seoKeywordAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        const config = GENERATION_CONFIG[state.agentState.contentType];

        // Use the research results
        const marketResearch = state.agentState.marketResearchResult || {};

        const seoPrompt = `
          You are an SEO specialist. Based on the topic "${state.agentState.topicFocus}" and target audience "${state.agentState.targetAudience}",
          I need you to develop an optimal keyword strategy.
          
          The user has provided these primary keywords: ${state.agentState.primaryKeywords.join(', ')}.
          
          Based on the team discussion and final content plan: "${state.agentState.discussionPhase?.contentLeadSummary}"
          
          Market research indicates these trends:
          ${JSON.stringify(marketResearch.marketTrends || [])}
          
          IMPORTANT: Your response MUST be valid JSON with the following structure:
          {
            "primaryKeyword": { "keyword": "...", "volume": "high|medium|low" },
            "secondaryKeywords": [{"keyword": "...", "volume": "..."}, ...],
            "longTailKeywords": [{"keyword": "...", "volume": "..."}, ...],
            "semanticKeywords": [{"keyword": "..."}, ...],
            "recommendedDensity": {
              "primary": "X%",
              "secondary": "Y%"
            },
            "metaTitleSuggestion": "...",
            "metaDescriptionSuggestion": "..."
          }
          
          Do not include any text outside of the JSON structure. Ensure all quotes are properly escaped.
          `;

        // Use a try-catch block specifically for the model invocation
        let modelResponse;
        try {
            modelResponse = await baseModel.invoke([
                new SystemMessage(
                    "You are an SEO specialist with expertise in keyword research and content optimization. Always respond with valid JSON."
                ),
                new SystemMessage(seoPrompt)
            ]);
        } catch (modelError) {
            console.error("Error in model invocation:", modelError);
            // Provide a fallback response if the model fails
            modelResponse = {
                primaryKeyword: { keyword: state.agentState.primaryKeywords[0] || "main topic", volume: "medium" },
                secondaryKeywords: [{keyword: "related term 1", volume: "medium"}, {keyword: "related term 2", volume: "low"}],
                longTailKeywords: [{keyword: "how to use main topic", volume: "low"}],
                semanticKeywords: [{keyword: "related concept"}],
                recommendedDensity: {
                    primary: "2-3%",
                    secondary: "1-2%"
                },
                metaTitleSuggestion: `${state.agentState.topicFocus} - Key Information`,
                metaDescriptionSuggestion: `Learn about ${state.agentState.topicFocus} for ${state.agentState.targetAudience}.`
            };
        }

        // Ensure we have a valid response object
        const response = await safeJsonParse(modelResponse);
        console.log("Validated response in seoKeywordAgent", response);

        return {
            agentState: {
                ...state.agentState,
                seoKeywordResult: response,
            },
        };
    } catch (error) {
        console.error("Critical error in seoKeywordAgent:", error);
        return {
            agentState: {
                ...state.agentState,
                error: `Error in SEO keyword research: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

// 3. Content Strategy Agent
export async function contentStrategyAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        const config = GENERATION_CONFIG[state.agentState.contentType];

        // Use the results from previous agents
        const marketResearch = state.agentState.marketResearchResult || {};
        const seoKeywords = state.agentState.seoKeywordResult || {};

        const strategyPrompt = `
          You are a content strategist creating a plan for a ${state.agentState.contentType} about "${state.agentState.topicFocus}".
          
          The content is targeting ${state.agentState.targetAudience} with a ${state.agentState.tonePreference} tone.
          
          Based on the team discussion and final content plan: "${state.agentState.discussionPhase?.contentLeadSummary}"
          
          SEO keywords to incorporate:
          Primary: ${seoKeywords.primaryKeyword?.keyword || state.agentState.primaryKeywords[0] || ''}
          Secondary: ${(seoKeywords.secondaryKeywords || []).map(k => k.keyword).join(', ')}
          
          Create a detailed content plan with:
          1. Content objective (what the content should achieve)
          2. Key talking points
          3. Structure with section breakdown
          4. Content format recommendations
          5. Call-to-action suggestions
          
          IMPORTANT: Your response MUST be valid JSON with the following structure:
          {
            "contentObjective": "...",
            "targetPersona": "...",
            "keyTalkingPoints": [{"point": "..."}, ...],
            "contentStructure": [
              {
                "section": "...",
                "purpose": "...",
                "keyPoints": [{"point": "..."}, ...],
                "estimatedWordCount": 000
              },
              ...
            ],
            "formatRecommendations": [{"recommendation": "..."}, ...],
            "callToAction": {"primary": "...", "secondary": "..."}
          }
          
          Do not include any text outside of the JSON structure. Ensure all quotes are properly escaped.
          For a ${state.agentState.contentType}, include these sections: ${config.sections.join(', ')}.
          Detail level should be ${config.detailLevel}.
          `;

        // Use a try-catch block specifically for the model invocation
        let modelResponse;
        try {
            modelResponse = await baseModel.invoke([
                new SystemMessage(
                    "You are a content strategist specializing in creating effective content plans. Always respond with valid JSON."
                ),
                new SystemMessage(strategyPrompt)
            ]);
        } catch (modelError) {
            console.error("Error in model invocation:", modelError);
            // Provide a fallback response if the model fails
            modelResponse = {
                contentObjective: `Provide valuable information about ${state.agentState.topicFocus} to ${state.agentState.targetAudience}`,
                targetPersona: state.agentState.targetAudience || "General audience",
                keyTalkingPoints: [{point: "Main benefits"}, {point: "Key features"}, {point: "Common use cases"}],
                contentStructure: config.sections.map(section => ({
                    section: section,
                    purpose: `Explain the ${section} aspects`,
                    keyPoints: [{point: `Key information about ${section}`}],
                    estimatedWordCount: 300
                })),
                formatRecommendations: [{recommendation: "Use clear headings and subheadings"}],
                callToAction: {primary: "Learn more", secondary: "Contact for details"}
            };
        }

        // Ensure we have a valid response object
        const response = await safeJsonParse(modelResponse);
        console.log("Validated response in contentStrategyAgent", response);

        return {
            agentState: {
                ...state.agentState,
                contentStrategyResult: response,
            },
        };
    } catch (error) {
        console.error("Critical error in contentStrategyAgent:", error);
        return {
            agentState: {
                ...state.agentState,
                error: `Error in content strategy: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

// 4. Content Generation Agent
//Old Code
// async function contentGenerationAgent(state: {
//     agentState: ContentGenerationState;
// }): Promise<{ agentState: ContentGenerationState }> {
//     try {
//         const config = GENERATION_CONFIG[state.agentState.contentType];

//         // Use the results from previous agents
//         const marketResearch = state.agentState.marketResearchResult || {};
//         const seoKeywords = state.agentState.seoKeywordResult || {};
//         const contentStrategy = state.agentState.contentStrategyResult || {};
        
//         // Get the comprehensive final content plan from the discussion phase
//         const finalContentPlan = state.agentState.discussionPhase?.finalContentPlan || {};
//         const keyDecisions = state.agentState.discussionPhase?.keyDecisions || [];
//         const executionGuidance = state.agentState.discussionPhase?.executionGuidance || {};

//         const generationPrompt = `
//           You are a professional content writer creating a ${state.agentState.contentType} about "${state.agentState.topicFocus}".
          
//           The content is targeting ${state.agentState.targetAudience} with a ${state.agentState.tonePreference} tone.
          
//           COMPREHENSIVE CONTENT PLAN:
//           ${JSON.stringify(finalContentPlan)}
          
//           KEY DECISIONS FROM TEAM DISCUSSION:
//           ${JSON.stringify(keyDecisions)}
          
//           EXECUTION GUIDANCE:
//           ${JSON.stringify(executionGuidance)}
          
//           MARKET RESEARCH INSIGHTS:
//           ${JSON.stringify(marketResearch)}
          
//           SEO KEYWORDS AND STRATEGY:
//           ${JSON.stringify(seoKeywords)}
          
//           CONTENT STRATEGY:
//           ${JSON.stringify(contentStrategy)}
          
//           IMPORTANT: In your content creation process, explicitly show your reasoning process (chain-of-thought):
//           1. First, explain how you're approaching each section of the content
//           2. For each section, describe how you're incorporating market research, SEO keywords, and content strategy
//           3. Explain your decisions about tone, style, and content depth
//           4. Describe how you're addressing the target audience's needs and interests
//           5. Explain how you're optimizing for both engagement and search visibility
          
//           Your response MUST be valid JSON with the following structure:
//           {
//             "title": "Compelling title that includes primary keywords",
//             "metaDescription": "SEO-optimized meta description",
//             "creationProcess": {
//               "overallApproach": "Description of your overall approach to creating this content",
//               "sectionApproaches": [
//                 {
//                   "section": "Section name",
//                   "approach": "How you approached this section",
//                   "researchIntegration": "How you integrated market research",
//                   "keywordStrategy": "How you incorporated keywords",
//                   "audienceConsiderations": "How you addressed audience needs"
//                 },
//                 ...
//               ],
//               "toneAndStyleDecisions": "Explanation of tone and style choices",
//               "challengesAddressed": ["Challenge 1 and how you addressed it", "Challenge 2 and how you addressed it"]
//             },
//             "sections": [
//               {
//                 "id": "section-1",
//                 "type": "heading|text|list|callout|faq|image|overview",
//                 "title": "Section title",
//                 "content": "Detailed content" or ["item1", "item2"] for lists or [{question, answer}] for FAQs
//               },
//               ...
//             ],
//             "keywordUsage": {
//               "primary": ["Locations where primary keyword is used"],
//               "secondary": ["Locations where secondary keywords are used"],
//               "natural": "How you ensured natural keyword integration"
//             },
//             "reasoningProcess": "Detailed explanation of your thought process throughout content creation"
//           }
//           `;

//         // Use a try-catch block specifically for the model invocation
//         let modelResponse;
//         try {
//             modelResponse = await baseModel.invoke([
//                 new SystemMessage(
//                     "You are a professional content writer with expertise in creating engaging, informative content. Show your reasoning process explicitly and create content that balances SEO optimization with audience value. Always respond with valid JSON."
//                 ),
//                 new SystemMessage(generationPrompt)
//             ]);
//         } catch (modelError) {
//             console.error("Error in model invocation:", modelError);
//             // Provide a fallback response if the model fails
//             modelResponse = {
//                 title: `${state.agentState.topicFocus}: A Complete Guide`,
//                 metaDescription: `Learn everything about ${state.agentState.topicFocus} in this comprehensive guide for ${state.agentState.targetAudience}.`,
//                 creationProcess: {
//                     overallApproach: "Created a structured, informative piece that balances SEO needs with audience value",
//                     sectionApproaches: [
//                         {
//                             section: "Introduction",
//                             approach: "Engaging hook that establishes relevance",
//                             researchIntegration: "Incorporated market trends from research",
//                             keywordStrategy: "Included primary keyword naturally",
//                             audienceConsiderations: "Addressed key audience pain points"
//                         },
//                         {
//                             section: "Main Content",
//                             approach: "Comprehensive coverage with evidence and examples",
//                             researchIntegration: "Used competitor analysis to identify gaps",
//                             keywordStrategy: "Distributed secondary keywords throughout",
//                             audienceConsiderations: "Focused on audience needs identified in research"
//                         }
//                     ],
//                     toneAndStyleDecisions: `Used ${state.agentState.tonePreference} tone to match audience expectations`,
//                     challengesAddressed: ["Balancing keyword usage with readability", "Covering complex topics accessibly"]
//                 },
//                 sections: config.sections.map((section, index) => ({
//                     id: `section-${index + 1}`,
//                     type: "heading",
//                     title: section.charAt(0).toUpperCase() + section.slice(1),
//                     content: `This section covers the ${section} aspects of ${state.agentState.topicFocus}.`
//                 })),
//                 keywordUsage: {
//                     primary: ["Title", "Introduction", "Headings", "Conclusion"],
//                     secondary: ["Throughout body content", "In subheadings", "In FAQs"],
//                     natural: "Focused on context and meaning rather than keyword density"
//                 },
//                 reasoningProcess: "Prioritized audience value while strategically incorporating keywords. Structured content to maintain engagement while covering all necessary information."
//             };
//         }

//         // Ensure we have a valid response object
//         const response = await safeJsonParse(modelResponse);
//         console.log("Validated response in contentGenerationAgent", response);

//         // Extract just the content sections for the content result
//         const contentResult = {
//             title: response.title || `${state.agentState.topicFocus}`,
//             metaDescription: response.metaDescription || `Learn about ${state.agentState.topicFocus}`,
//             sections: response.sections || []
//         };

//         return {
//             agentState: {
//                 ...state.agentState,
//                 contentResult: contentResult,
//                 executionPhase: {
//                     ...state.agentState.executionPhase,
//                     contentCreationProcess: response.creationProcess || {},
//                     keywordUsageStrategy: response.keywordUsage || {},
//                     contentCreationReasoning: response.reasoningProcess || "No reasoning process provided."
//                 }
//             },
//         };
//     } catch (error) {
//         console.error("Critical error in contentGenerationAgent:", error);
//         return {
//             agentState: {
//                 ...state.agentState,
//                 error: `Error in content generation: ${error instanceof Error ? error.message : String(error)}`,
//             },
//         };
//     }
// }
// Enhanced Content Generation Agent
// Enhanced Content Generation Agent
// Optimized Content Generation Agent with AbortSignal management and loop protection
// async function contentGenerationAgent(state: {
//     agentState: ContentGenerationState;
// }): Promise<{ agentState: ContentGenerationState }> {
//     try {
//         const config = GENERATION_CONFIG[state.agentState.contentType];

//         // Use the results from previous agents
//         const marketResearch = state.agentState.marketResearchResult || {};
//         const seoKeywords = state.agentState.seoKeywordResult || {};
//         const contentStrategy = state.agentState.contentStrategyResult || {};
        
//         // Get the comprehensive final content plan from the discussion phase
//         const finalContentPlan = state.agentState.discussionPhase?.finalContentPlan || {};
//         const keyDecisions = state.agentState.discussionPhase?.keyDecisions || [];
//         const executionGuidance = state.agentState.discussionPhase?.executionGuidance || {};

//         // ENHANCEMENT 1: Create a detailed section-by-section content mapping with target word counts
//         // This ensures each section gets appropriate depth based on the content type
//         const contentStructure = finalContentPlan.contentStructure || contentStrategy.contentStructure || [];
        
//         // Define minimum word counts based on content type
//         const totalMinWordCount = state.agentState.contentType === 'blog-article' ? 1500 : 
//                                 state.agentState.contentType === 'buying-guide' ? 2500 : 1000;
        
//         // Calculate target word counts for each section
//         const sectionWordCounts = {};
//         let totalPlannedWords = 0;
        
//         // First pass - extract existing word counts
//         contentStructure.forEach(section => {
//             const sectionName = typeof section === 'string' ? section : section.section;
//             const estimatedWordCount = typeof section === 'string' ? 0 : 
//                                       (section.estimatedWordCount || 0);
            
//             sectionWordCounts[sectionName] = estimatedWordCount;
//             totalPlannedWords += estimatedWordCount;
//         });
        
//         // Second pass - adjust word counts if needed
//         if (totalPlannedWords < totalMinWordCount) {
//             // Scale up each section proportionally
//             const scaleFactor = totalMinWordCount / (totalPlannedWords || 1);
//             Object.keys(sectionWordCounts).forEach(section => {
//                 sectionWordCounts[section] = Math.round(sectionWordCounts[section] * scaleFactor);
//             });
//         }

//         // ENHANCEMENT 2: Build a more detailed generator prompt with specific guidance for each section
//         let sectionGuidance = '';
//         const limitedStructure = Array.isArray(contentStructure) ? 
//             contentStructure.slice(0, 5) : // Limit to 5 sections to prevent excessive processing
//             [];
            
//         limitedStructure.forEach((section, index) => {
//             const sectionName = typeof section === 'string' ? section : section.section;
//             const sectionPurpose = typeof section === 'string' ? '' : section.purpose;
//             const keyPoints = typeof section === 'string' ? [] : (section.keyPoints || []);
//             const targetWordCount = sectionWordCounts[sectionName] || 300;
            
//             sectionGuidance += `
//             SECTION ${index + 1}: ${sectionName.toUpperCase()}
//             Purpose: ${sectionPurpose}
//             Target Word Count: ${targetWordCount} words
//             Key Points to Cover: ${Array.isArray(keyPoints) ? 
//                 keyPoints.map(p => typeof p === 'string' ? p : p.point).join(', ') : 
//                 JSON.stringify(keyPoints)}
//             `;
//         });

//         // Create a single abort controller for all API calls
//         const controller = new AbortController();
//         const signal = controller.signal;
        
//         // Set a timeout to prevent infinite processing
//         const timeout = setTimeout(() => {
//             controller.abort();
//             console.log("Content generation aborted due to timeout");
//         }, 120000); // 2 minute timeout
        
//         try {
//             // ENHANCEMENT 3: Generate the content plan with abort signal
//             const contentPlanPrompt = `
//               You are a professional content writer creating a ${state.agentState.contentType} about "${state.agentState.topicFocus}".
              
//               The content is targeting ${state.agentState.targetAudience} with a ${state.agentState.tonePreference} tone.
              
//               COMPREHENSIVE CONTENT PLAN:
//               ${JSON.stringify(finalContentPlan)}
              
//               KEY DECISIONS FROM TEAM DISCUSSION:
//               ${JSON.stringify(keyDecisions)}
              
//               MARKET RESEARCH INSIGHTS:
//               ${JSON.stringify(marketResearch)}
              
//               SEO KEYWORDS AND STRATEGY:
//               ${JSON.stringify(seoKeywords)}
              
//               CONTENT STRUCTURE AND SECTION GUIDANCE:
//               ${sectionGuidance}
              
//               Your task is to create a detailed writing plan with the following components:
//               1. Title - A compelling title that includes primary keywords
//               2. Meta Description - An SEO-optimized meta description
//               3. Content Plan - An array of section details with approach, key points, and target word counts
//               4. Tone and Style Approach - How you'll maintain the requested tone
//               5. Keyword Distribution Plan - How you'll incorporate keywords throughout
              
//               Create a plan that will result in comprehensive, engaging content with adequate depth.
//               The total content should be approximately ${totalMinWordCount} words with appropriate distribution.
//               `;

//             // Create a more conversational tone for the model
//             const contentPlanModel = createModel(0.7);
//             const contentPlanResponse = await contentPlanModel.invoke([
//                 new SystemMessage(
//                     "You are a professional content writer with expertise in creating engaging, informative content plans."
//                 ),
//                 new SystemMessage(contentPlanPrompt)
//             ], { signal });

//             // Parse the content plan
//             const contentPlan = await safeJsonParse(contentPlanResponse);
//             console.log("Content plan created");
            
//             // Validate the content plan structure and limit the number of sections
//             // This prevents processing too many sections and creating too many listeners
//             const validatedContentPlan = {
//                 title: contentPlan.title || `${state.agentState.topicFocus}`,
//                 metaDescription: contentPlan.metaDescription || `Learn about ${state.agentState.topicFocus}`,
//                 contentPlan: Array.isArray(contentPlan.contentPlan) ? 
//                     contentPlan.contentPlan.slice(0, 5) : // Limit to 5 sections max
//                     [{
//                         section: "Main Content",
//                         approach: "Comprehensive coverage",
//                         keyPoints: ["Important aspects", "Key considerations"],
//                         targetWordCount: totalMinWordCount
//                     }],
//                 toneAndStyleApproach: contentPlan.toneAndStyleApproach || state.agentState.tonePreference,
//                 keywordDistributionPlan: contentPlan.keywordDistributionPlan || "Balanced distribution throughout content"
//             };
            
//             // Initialize the sections array
//             const sections = [];
            
//             // OPTIMIZATION: Generate sections in sequence rather than in parallel
//             // This prevents too many concurrent requests and AbortSignal listeners
//             for (let i = 0; i < validatedContentPlan.contentPlan.length; i++) {
//                 const sectionPlan = validatedContentPlan.contentPlan[i];
                
//                 if (signal.aborted) {
//                     throw new Error("Content generation aborted");
//                 }
                
//                 const sectionPrompt = `
//                   You are a professional content writer creating the ${sectionPlan.section} section of a ${state.agentState.contentType} about "${state.agentState.topicFocus}".
                  
//                   This content is targeting ${state.agentState.targetAudience} with a ${state.agentState.tonePreference} tone.
                  
//                   DETAILED PLAN FOR THIS SECTION:
//                   ${JSON.stringify(sectionPlan)}
                  
//                   KEY INSTRUCTIONS:
//                   1. Write approximately ${sectionPlan.targetWordCount} words
//                   2. Cover all the key points listed in the plan
//                   3. Use the specified tone and style
//                   4. Naturally incorporate the keywords
//                   5. Make the content engaging and valuable for the audience
                  
//                   Create ONLY the content for this specific section. Make it high-quality, comprehensive, and engaging.
//                   Write in a natural, flowing style. Do NOT include the section heading - just write the content itself.
//                   `;
                
//                 // Generate the section content sequentially
//                 const sectionModel = createModel(0.7);
//                 const sectionResponse = await sectionModel.invoke([
//                     new SystemMessage(
//                         `You are a professional content writer specializing in creating engaging content.`
//                     ),
//                     new SystemMessage(sectionPrompt)
//                 ], { signal });
                
//                 const sectionContent = typeof sectionResponse === 'string' ? 
//                     sectionResponse : 
//                     JSON.stringify(sectionResponse);
                
//                 // Add the section to the sections array
//                 sections.push({
//                     id: `section-${i + 1}`,
//                     type: "heading",
//                     title: sectionPlan.section,
//                     content: sectionContent
//                 });
//             }
            
//             // OPTIMIZATION: Generate introduction and conclusion sequentially
//             // Introduction
//             const introPrompt = `
//               You are a professional content writer creating the introduction for a ${state.agentState.contentType} about "${state.agentState.topicFocus}".
              
//               This content is targeting ${state.agentState.targetAudience}.
              
//               Write a compelling introduction of approximately 150-200 words that:
//               1. Hooks the reader and establishes relevance
//               2. Clearly states what the content will cover
//               3. Incorporates the primary keyword naturally
//               4. Addresses a key pain point or need of the audience
//               5. Sets the tone for the rest of the content
              
//               Write ONLY the introduction paragraph(s).
//               `;
            
//             if (signal.aborted) {
//                 throw new Error("Content generation aborted");
//             }
            
//             const introModel = createModel(0.7);
//             const introResponse = await introModel.invoke([
//                 new SystemMessage(
//                     "You are a professional content writer specializing in creating engaging introductions."
//                 ),
//                 new SystemMessage(introPrompt)
//             ], { signal });
            
//             const introduction = typeof introResponse === 'string' ? 
//                 introResponse : 
//                 JSON.stringify(introResponse);
            
//             // Conclusion
//             const conclusionPrompt = `
//               You are a professional content writer creating the conclusion for a ${state.agentState.contentType} about "${state.agentState.topicFocus}".
              
//               This content is targeting ${state.agentState.targetAudience}.
              
//               Write a strong conclusion of approximately 150-200 words that:
//               1. Summarizes the key points covered
//               2. Reinforces the main benefit or takeaway
//               3. Incorporates the primary keyword naturally
//               4. Includes a clear call to action appropriate for the content type
//               5. Leaves the reader with a sense of completion
              
//               Write ONLY the conclusion paragraph(s).
//               `;
            
//             if (signal.aborted) {
//                 throw new Error("Content generation aborted");
//             }
            
//             const conclusionModel = createModel(0.7);
//             const conclusionResponse = await conclusionModel.invoke([
//                 new SystemMessage(
//                     "You are a professional content writer specializing in creating effective conclusions."
//                 ),
//                 new SystemMessage(conclusionPrompt)
//             ], { signal });
            
//             const conclusion = typeof conclusionResponse === 'string' ? 
//                 conclusionResponse : 
//                 JSON.stringify(conclusionResponse);
            
//             // Add introduction and conclusion to the sections array
//             sections.unshift({
//                 id: "section-intro",
//                 type: "heading",
//                 title: "Introduction",
//                 content: introduction
//             });
            
//             sections.push({
//                 id: "section-conclusion",
//                 type: "heading",
//                 title: "Conclusion",
//                 content: conclusion
//             });
            
//             // OPTIMIZATION: Skip FAQ generation if abort signal is triggered
//             if (!signal.aborted) {
//                 // Generate FAQs with limited scope
//                 const faqPrompt = `
//                   You are an SEO specialist creating FAQs for a ${state.agentState.contentType} about "${state.agentState.topicFocus}".
                  
//                   This content is targeting ${state.agentState.targetAudience}.
                  
//                   Create 3-5 highly relevant FAQs that:
//                   1. Address common questions about the topic
//                   2. Incorporate important keywords naturally
//                   3. Provide clear, concise answers
                  
//                   The response should have this JSON structure:
//                   [
//                     {
//                       "question": "Clearly worded question with keywords?",
//                       "answer": "Comprehensive answer that directly addresses the question."
//                     },
//                     ...
//                   ]
//                   `;
                
//                 const faqModel = createModel(0.7);
//                 const faqResponse = await faqModel.invoke([
//                     new SystemMessage(
//                         "You are an SEO specialist creating valuable FAQs."
//                     ),
//                     new SystemMessage(faqPrompt)
//                 ], { signal });
                
//                 let faqs = await safeJsonParse(faqResponse);
                
//                 if (!Array.isArray(faqs)) {
//                     faqs = [];
//                 }
                
//                 // Add FAQs to the sections array if any were generated (limit to 5 max)
//                 if (faqs.length > 0) {
//                     sections.push({
//                         id: "section-faq",
//                         type: "faq",
//                         title: "Frequently Asked Questions",
//                         content: Array.isArray(faqs) ? faqs.slice(0, 5) : []
//                     });
//                 }
//             }
            
//             // ENHANCEMENT 7: Track and record keyword usage
//             // This helps ensure proper distribution and optimization
//             const keywordTracking = {
//                 primary: [],
//                 secondary: [],
//                 natural: "Content was designed to incorporate keywords naturally throughout each section."
//             };
            
//             // Check for primary keyword in title and metaDescription
//             const primaryKeyword = (seoKeywords.primaryKeyword?.keyword || state.agentState.primaryKeywords[0] || '').toLowerCase();
            
//             if (validatedContentPlan.title.toLowerCase().includes(primaryKeyword)) {
//                 keywordTracking.primary.push("Title");
//             }
            
//             if (validatedContentPlan.metaDescription.toLowerCase().includes(primaryKeyword)) {
//                 keywordTracking.primary.push("Meta Description");
//             }
            
//             // Track keywords in sections (with safe type handling)
//             sections.forEach(section => {
//                 // Handle different content types safely
//                 const sectionContent = typeof section.content === 'string' 
//                     ? section.content 
//                     : Array.isArray(section.content) 
//                         ? JSON.stringify(section.content) 
//                         : typeof section.content === 'object' && section.content !== null
//                             ? JSON.stringify(section.content)
//                             : '';
                
//                 // Check for primary keyword
//                 if (sectionContent.toLowerCase().includes(primaryKeyword)) {
//                     if (!keywordTracking.primary.includes(section.title)) {
//                         keywordTracking.primary.push(section.title);
//                     }
//                 }
                
//                 // Check for secondary keywords (limit to 5 to prevent excessive processing)
//                 const limitedSecondaryKeywords = (seoKeywords.secondaryKeywords || []).slice(0, 5);
//                 limitedSecondaryKeywords.forEach(keyword => {
//                     const secondaryKeyword = keyword.keyword.toLowerCase();
//                     if (sectionContent.toLowerCase().includes(secondaryKeyword)) {
//                         if (!keywordTracking.secondary.includes(section.title)) {
//                             keywordTracking.secondary.push(section.title);
//                         }
//                     }
//                 });
//             });

//             // Compile the final result
//             const contentResult = {
//                 title: validatedContentPlan.title,
//                 metaDescription: validatedContentPlan.metaDescription,
//                 sections: sections,
//                 keywordUsage: keywordTracking
//             };

//             return {
//                 agentState: {
//                     ...state.agentState,
//                     contentResult: contentResult,
//                     executionPhase: {
//                         ...state.agentState.executionPhase,
//                         contentCreationProcess: {
//                             overallApproach: validatedContentPlan.toneAndStyleApproach,
//                             sectionApproaches: validatedContentPlan.contentPlan.map(plan => ({
//                                 section: plan.section,
//                                 approach: plan.approach,
//                                 keywordStrategy: "Natural keyword integration"
//                             }))
//                         },
//                         keywordUsageStrategy: contentResult.keywordUsage,
//                         contentCreationReasoning: "Content was generated using a section-by-section approach to ensure comprehensive coverage while maintaining cohesion."
//                     }
//                 },
//             };
//         } finally {
//             // Always clear the timeout and abort controller
//             clearTimeout(timeout);
//             controller.abort(); // Ensure all pending operations are cancelled
//         }
//     } catch (error) {
//         console.error("Error in enhancedContentGenerationAgent:", error);
        
//         // Create basic fallback content in case of errors
//         const fallbackSections = [
//             {
//                 id: "section-intro",
//                 type: "heading",
//                 title: "Introduction",
//                 content: `This ${state.agentState.contentType} explores ${state.agentState.topicFocus} with a focus on providing valuable information for ${state.agentState.targetAudience}.`
//             },
//             {
//                 id: "section-1",
//                 type: "heading",
//                 title: "Overview",
//                 content: `${state.agentState.topicFocus} is an important topic that deserves attention. This section provides an overview of the key aspects.`
//             },
//             {
//                 id: "section-2",
//                 type: "heading",
//                 title: "Key Considerations",
//                 content: `When exploring ${state.agentState.topicFocus}, there are several important factors to consider.`
//             },
//             {
//                 id: "section-conclusion",
//                 type: "heading",
//                 title: "Conclusion",
//                 content: `In conclusion, ${state.agentState.topicFocus} is vital for ${state.agentState.targetAudience}. We hope this guide has been helpful.`
//             }
//         ];
        
//         return {
//             agentState: {
//                 ...state.agentState,
//                 contentResult: {
//                     title: `${state.agentState.topicFocus}: Complete Guide`,
//                     metaDescription: `Learn about ${state.agentState.topicFocus} in this comprehensive guide for ${state.agentState.targetAudience}.`,
//                     sections: fallbackSections
//                 },
//                 error: `Warning: Error in content generation: ${error instanceof Error ? error.message : String(error)}. Using fallback content.`,
//             },
//         };
//     }
// }
// Enhanced Content Generation Agent with dynamic content structure support
export async function contentGenerationAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        console.log("Starting content generation with dynamic structure...");
        
        // Get the content strategy and SEO results
        const contentStrategy = state.agentState.contentStrategyResult || {};
        const seoKeywords = state.agentState.seoKeywordResult || {};
        const marketResearch = state.agentState.marketResearchResult || {};
        
        // Get the dynamic content structure if available
        const dynamicStructure = state.agentState.finalContentStructure;
        
        // Create the generation prompt based on dynamic structure if available
        let generationPrompt = "";
        
        if (dynamicStructure) {
            generationPrompt = `
              You are a Content Generation Agent creating a ${state.agentState.contentType} about "${state.agentState.topicFocus}".
              
              Your task is to generate high-quality content following the dynamic structure provided.
              
              Content Objective: ${dynamicStructure.contentObjective}
              
              Target Audience:
              Primary: ${dynamicStructure.targetAudience.primary}
              ${dynamicStructure.targetAudience.secondary ? `Secondary: ${dynamicStructure.targetAudience.secondary}` : ''}
              Audience Needs: ${dynamicStructure.targetAudience.needs.join(', ')}
              
              Tone and Style: ${dynamicStructure.toneAndStyle}
              
              Keyword Strategy:
              Primary: ${dynamicStructure.keywordStrategy.primary}
              Secondary: ${dynamicStructure.keywordStrategy.secondary}
              Distribution: ${dynamicStructure.keywordStrategy.distribution}
              
              Content Structure:
              ${JSON.stringify(dynamicStructure.sections, null, 2)}
              
              Content Differentiators:
              ${dynamicStructure.contentDifferentiators.join('\n')}
              
              IMPORTANT: In your response, explicitly show your reasoning process (chain-of-thought) for key decisions:
              1. How you're implementing the keyword strategy
              2. How you're addressing the audience needs
              3. How you're maintaining the required tone and style
              4. How you're incorporating the content differentiators
              
              Your response MUST be valid JSON with the following structure:
              {
                "title": "Compelling title that includes primary keywords",
                "metaDescription": "SEO-optimized meta description (150-160 characters)",
                "sections": [
                  {
                    "id": "section-id",
                    "type": "section-type",
                    "title": "Section title",
                    "content": "Section content",
                    "subsections": [
                      {
                        "id": "subsection-id",
                        "type": "subsection-type",
                        "title": "Subsection title",
                        "content": "Subsection content"
                      }
                    ]
                  }
                ],
                "keywordUsage": {
                  "primary": "How you used primary keywords",
                  "secondary": "How you used secondary keywords",
                  "distribution": "How you distributed keywords throughout the content"
                },
                "audienceConsiderations": {
                  "primaryAudience": "How you addressed primary audience needs",
                  "secondaryAudience": "How you addressed secondary audience needs (if applicable)"
                },
                "reasoningProcess": "Detailed explanation of your thought process and how you made key decisions"
              }
            `;
        } else {
            // Fallback to a more generic prompt if no dynamic structure is available
            generationPrompt = `
              You are a Content Generation Agent creating a ${state.agentState.contentType} about "${state.agentState.topicFocus}".
              
              Your task is to generate high-quality content based on the following inputs:
              
              Target Audience: ${state.agentState.targetAudience || 'General audience'}
              Primary Keywords: ${state.agentState.primaryKeywords?.join(', ') || 'No specific keywords provided'}
              Tone Preference: ${state.agentState.tonePreference || 'Informative'}
              
              Content Strategy Insights:
              ${JSON.stringify(contentStrategy, null, 2)}
              
              SEO Keyword Insights:
              ${JSON.stringify(seoKeywords, null, 2)}
              
              Market Research Insights:
              ${JSON.stringify(marketResearch, null, 2)}
              
              IMPORTANT: In your response, explicitly show your reasoning process (chain-of-thought) for key decisions:
              1. How you're implementing the keyword strategy
              2. How you're addressing the audience needs
              3. How you're maintaining the required tone and style
              
              Your response MUST be valid JSON with the following structure:
              {
                "title": "Compelling title that includes primary keywords",
                "metaDescription": "SEO-optimized meta description (150-160 characters)",
                "sections": [
                  {
                    "id": "section-id",
                    "type": "section-type",
                    "title": "Section title",
                    "content": "Section content",
                    "subsections": [
                      {
                        "id": "subsection-id",
                        "type": "subsection-type",
                        "title": "Subsection title",
                        "content": "Subsection content"
                      }
                    ]
                  }
                ],
                "keywordUsage": {
                  "primary": "How you used primary keywords",
                  "secondary": "How you used secondary keywords",
                  "distribution": "How you distributed keywords throughout the content"
                },
                "audienceConsiderations": {
                  "primaryAudience": "How you addressed primary audience needs",
                  "secondaryAudience": "How you addressed secondary audience needs (if applicable)"
                },
                "reasoningProcess": "Detailed explanation of your thought process and how you made key decisions"
              }
            `;
        }
        
        // Check if we're using A2A protocol
        if (state.agentState.currentTaskId && state.agentState.a2aTasks) {
            const taskId = state.agentState.currentTaskId;
            const task = state.agentState.a2aTasks[taskId];
            
            if (!task) {
                throw new Error(`Task ${taskId} not found`);
            }
            
            // Update task status to working
            const { state: updatedState } = updateTaskStatus(
                state.agentState,
                taskId,
                "working",
                createTextMessage("agent", "Generating content...")
            );
            
            // Use a try-catch block specifically for the model invocation
            let modelResponse;
            try {
                modelResponse = await baseModel.invoke([
                    new SystemMessage(
                        "You are a Content Generation Agent creating high-quality content. Always respond with valid JSON."
                    ),
                    new SystemMessage(generationPrompt)
                ]);
            } catch (modelError) {
                console.error("Error in model invocation:", modelError);
                // Provide a fallback response if the model fails
                modelResponse = {
                    title: `${state.agentState.topicFocus}: A Complete Guide`,
                    metaDescription: `Learn everything about ${state.agentState.topicFocus} in this comprehensive guide for ${state.agentState.targetAudience}.`,
                    sections: [
                        {
                            id: "introduction",
                            type: "text",
                            title: "Introduction",
                            content: `Introduction to ${state.agentState.topicFocus}.`
                        },
                        {
                            id: "main-content",
                            type: "text",
                            title: "Main Content",
                            content: `Detailed information about ${state.agentState.topicFocus}.`,
                            subsections: [
                                {
                                    id: "subsection-1",
                                    type: "text",
                                    title: "Key Point 1",
                                    content: "Details about key point 1."
                                },
                                {
                                    id: "subsection-2",
                                    type: "text",
                                    title: "Key Point 2",
                                    content: "Details about key point 2."
                                }
                            ]
                        },
                        {
                            id: "conclusion",
                            type: "text",
                            title: "Conclusion",
                            content: `Summary of key points about ${state.agentState.topicFocus}.`
                        }
                    ],
                    keywordUsage: {
                        primary: `Used primary keywords in title, headings, and throughout content.`,
                        secondary: `Incorporated secondary keywords naturally in the content.`,
                        distribution: `Ensured keywords are distributed evenly throughout the content.`
                    },
                    audienceConsiderations: {
                        primaryAudience: `Addressed the needs of ${state.agentState.targetAudience} by providing clear and relevant information.`,
                        secondaryAudience: `Also considered broader audience interests.`
                    },
                    reasoningProcess: `Created a structured approach to cover all key aspects of ${state.agentState.topicFocus} while maintaining readability and SEO optimization.`
                };
            }
            
            // Ensure we have a valid response object
            const response = await safeJsonParse(modelResponse);
            console.log("Validated response in contentGenerationAgent", response);
            
            // Create an artifact with the generated content
            const artifact: Artifact = {
                name: "Generated Content",
                parts: [
                    {
                        type: "data",
                        data: response
                    }
                ],
                index: 0
            };
            
            // Add the artifact to the task
            const { state: stateWithArtifact } = addArtifactToTask(
                updatedState,
                taskId,
                artifact
            );
            
            // Update task status to completed
            const { state: finalState } = updateTaskStatus(
                stateWithArtifact,
                taskId,
                "completed"
            );
            
            // Return updated state with generated content
            return {
                agentState: {
                    ...finalState,
                    contentResult: response
                }
            };
        } else {
            // If not using A2A protocol, use a simpler approach
            
            // Use a try-catch block specifically for the model invocation
            let modelResponse;
            try {
                modelResponse = await baseModel.invoke([
                    new SystemMessage(
                        "You are a Content Generation Agent creating high-quality content. Always respond with valid JSON."
                    ),
                    new SystemMessage(generationPrompt)
                ]);
            } catch (modelError) {
                console.error("Error in model invocation:", modelError);
                // Provide a fallback response if the model fails
                modelResponse = {
                    title: `${state.agentState.topicFocus}: A Complete Guide`,
                    metaDescription: `Learn everything about ${state.agentState.topicFocus} in this comprehensive guide for ${state.agentState.targetAudience}.`,
                    sections: [
                        {
                            id: "introduction",
                            type: "text",
                            title: "Introduction",
                            content: `Introduction to ${state.agentState.topicFocus}.`
                        },
                        {
                            id: "main-content",
                            type: "text",
                            title: "Main Content",
                            content: `Detailed information about ${state.agentState.topicFocus}.`,
                            subsections: [
                                {
                                    id: "subsection-1",
                                    type: "text",
                                    title: "Key Point 1",
                                    content: "Details about key point 1."
                                },
                                {
                                    id: "subsection-2",
                                    type: "text",
                                    title: "Key Point 2",
                                    content: "Details about key point 2."
                                }
                            ]
                        },
                        {
                            id: "conclusion",
                            type: "text",
                            title: "Conclusion",
                            content: `Summary of key points about ${state.agentState.topicFocus}.`
                        }
                    ],
                    keywordUsage: {
                        primary: `Used primary keywords in title, headings, and throughout content.`,
                        secondary: `Incorporated secondary keywords naturally in the content.`,
                        distribution: `Ensured keywords are distributed evenly throughout the content.`
                    },
                    audienceConsiderations: {
                        primaryAudience: `Addressed the needs of ${state.agentState.targetAudience} by providing clear and relevant information.`,
                        secondaryAudience: `Also considered broader audience interests.`
                    },
                    reasoningProcess: `Created a structured approach to cover all key aspects of ${state.agentState.topicFocus} while maintaining readability and SEO optimization.`
                };
            }
            
            // Ensure we have a valid response object
            const response = await safeJsonParse(modelResponse);
            console.log("Validated response in contentGenerationAgent", response);
            
            // Return updated state with generated content
            return {
                agentState: {
                    ...state.agentState,
                    contentResult: response
                }
            };
        }
    } catch (error) {
        console.error("Critical error in contentGenerationAgent:", error);
        return {
            agentState: {
                ...state.agentState,
                error: `Error in content generation: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

// Review Phase Agents

// 1. Editorial Agent
// Fixed Editorial Agent with robust JSON handling
export async function editorialAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        // Use the content from the previous agent
        const content = state.agentState.contentResult || {};

        const editorialPrompt = `
          You are a copy editor reviewing content for a ${state.agentState.contentType} about "${state.agentState.topicFocus}".
          
          The tone should be ${state.agentState.tonePreference} and targeted at ${state.agentState.targetAudience}.
          
          Review and improve the content for:
          1. Grammar, spelling, and punctuation
          2. Clarity and readability
          3. Tone consistency
          4. Logical flow
          5. Engagement
          
          Here's the content to review:
          ${JSON.stringify(content)}
          
          IMPORTANT: Your response MUST be valid JSON with the following structure:
          {
            "editedContent": {
              "title": "Edited title",
              "metaDescription": "Edited meta description",
              "sections": [
                {
                  "id": "section-id",
                  "type": "heading|text|list|callout|faq",
                  "title": "Edited section title",
                  "content": "Edited content"
                },
                // More sections...
              ]
            },
            "editorialNotes": [
              {"category": "grammar|clarity|tone|flow|engagement", "note": "Specific edit note", "severity": "low|medium|high"}
            ],
            "overallAssessment": "Overall assessment of the content quality and changes made"
          }
          
          Do not include any text outside of the JSON structure. Ensure all quotes are properly escaped.
          `;

        // Use a specific temperature for editorial work
        const editorialModel = createModel(0.3); // Lower temperature for precision
        
        // Create an abort controller to prevent hanging
        const controller = new AbortController();
        const timeout = setTimeout(() => {
            controller.abort();
            console.log("Editorial agent aborted due to timeout");
        }, 60000); // 1 minute timeout
        
        try {
            // First attempt with direct JSON parsing
            const rawResponse = await editorialModel.invoke([
                new SystemMessage(
                    "You are a professional copy editor with a keen eye for detail and quality. ALWAYS respond with valid JSON in the exact format specified."
                ),
                new SystemMessage(editorialPrompt)
            ], { signal: controller.signal });
            
            // Try to parse the response as JSON
            let response;
            try {
                // If it's already JSON object, use it directly
                if (typeof rawResponse === 'object' && rawResponse !== null) {
                    response = rawResponse;
                } else {
                    // Try to parse it as JSON string
                    response = JSON.parse(rawResponse);
                }
            } catch (parseError) {
                console.log("Editorial agent produced invalid JSON, falling back to structured response generation");
                
                // Fallback to a more structured approach if JSON parsing fails
                const structuredPrompt = `
                  You are a copy editor reviewing content. 
                  
                  The previous attempt to generate a proper JSON response failed. 
                  
                  I need you to produce a valid JSON response with the following structure:
                  {
                    "editedContent": {
                      "title": "${content.title || "Title"}",
                      "metaDescription": "${content.metaDescription || "Meta description"}",
                      "sections": []
                    },
                    "editorialNotes": [
                      {"category": "grammar", "note": "Fixed grammatical issues", "severity": "medium"}
                    ],
                    "overallAssessment": "Content has been reviewed and edited for clarity and grammar."
                  }
                  
                  Simply take the content provided and make basic improvements to grammar and clarity.
                  
                  IMPORTANT: Your entire response must be valid JSON. Do not include any text before or after the JSON.
                `;
                
                // Try again with a more explicit model call
                const structuredResponse = await editorialModel.invoke([
                    new SystemMessage(
                        "You are a professional copy editor. ONLY respond with valid JSON. Do not include any text outside the JSON structure."
                    ),
                    new SystemMessage(structuredPrompt)
                ], { signal: controller.signal });
                
                try {
                    // Try to parse the structured response
                    if (typeof structuredResponse === 'object' && structuredResponse !== null) {
                        response = structuredResponse;
                    } else {
                        response = JSON.parse(structuredResponse);
                    }
                } catch (secondParseError) {
                    // If all else fails, create a minimal valid response
                    console.error("Still received invalid JSON, creating fallback response");
                    response = {
                        editedContent: content, // Use the original content
                        editorialNotes: [
                            {category: "general", note: "Basic review completed", severity: "low"}
                        ],
                        overallAssessment: "Content has been reviewed for quality."
                    };
                }
            }
            
            // Validate the response structure
            const validatedResponse = {
                editedContent: response.editedContent || content,
                editorialNotes: Array.isArray(response.editorialNotes) ? 
                    response.editorialNotes : 
                    [{category: "general", note: "Basic review completed", severity: "low"}],
                overallAssessment: response.overallAssessment || "Content has been reviewed for quality."
            };
            
            // Ensure the editedContent has the right structure
            if (!validatedResponse.editedContent.title) {
                validatedResponse.editedContent.title = content.title || "Title";
            }
            
            if (!validatedResponse.editedContent.metaDescription) {
                validatedResponse.editedContent.metaDescription = content.metaDescription || "Meta description";
            }
            
            if (!Array.isArray(validatedResponse.editedContent.sections)) {
                validatedResponse.editedContent.sections = content.sections || [];
            }
            
            // Process sections to ensure they have the right structure
            validatedResponse.editedContent.sections = validatedResponse.editedContent.sections.map((section, index) => {
                // Find the corresponding original section if it exists
                const originalSection = Array.isArray(content.sections) && content.sections.length > index ? 
                    content.sections[index] : null;
                
                return {
                    id: section.id || originalSection?.id || `section-${index}`,
                    type: section.type || originalSection?.type || "heading",
                    title: section.title || originalSection?.title || `Section ${index + 1}`,
                    content: section.content || originalSection?.content || ""
                };
            });
            
            return {
                agentState: {
                    ...state.agentState,
                    editorialResult: validatedResponse,
                    reviewPhase: {
                        ...state.agentState.reviewPhase,
                        editorialFeedback: validatedResponse.overallAssessment,
                        contentBeforeEdits: content,
                        contentAfterEdits: validatedResponse.editedContent
                    }
                },
            };
        } finally {
            // Clean up
            clearTimeout(timeout);
            controller.abort(); // Ensure any pending operations are cancelled
        }
    } catch (error) {
        console.error("Error in editorial agent:", error);
        
        // Create a fallback edited content in case of errors
        const fallbackResult = {
            editedContent: state.agentState.contentResult || {},
            editorialNotes: [
                {category: "general", note: "Basic editorial review", severity: "low"}
            ],
            overallAssessment: "Content has been reviewed for basic quality standards."
        };
        
        return {
            agentState: {
                ...state.agentState,
                editorialResult: fallbackResult,
                reviewPhase: {
                    ...state.agentState.reviewPhase,
                    editorialFeedback: fallbackResult.overallAssessment,
                    contentBeforeEdits: state.agentState.contentResult || {},
                    contentAfterEdits: fallbackResult.editedContent
                },
                error: `Warning: Error in editorial review: ${error instanceof Error ? error.message : String(error)}. Using fallback editorial review.`,
            },
        };
    }
}

// 2. SEO Optimization Agent
export async function seoOptimizationAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        // Use the edited content from the previous agent
        const editorialResult = state.agentState.editorialResult || {};
        const content = editorialResult.editedContent || state.agentState.contentResult || {};
        const seoKeywords = state.agentState.seoKeywordResult || {};

        const seoPrompt = `
          You are an SEO specialist optimizing content for a ${state.agentState.contentType} about "${state.agentState.topicFocus}".
          
          The primary keyword is: ${seoKeywords.primaryKeyword?.keyword || state.agentState.primaryKeywords[0] || ''}
          Secondary keywords: ${(seoKeywords.secondaryKeywords || []).map(k => k.keyword).join(', ')}
          
          Review and optimize the content for:
          1. Keyword usage and placement
          2. Heading structure (H1, H2, H3)
          3. Meta title and description
          4. Internal linking opportunities
          5. Image alt text (if applicable)
          
          Here's the content to optimize:
          ${JSON.stringify(content)}
          
          IMPORTANT: Your response MUST be valid JSON with the following structure:
          {
            "optimizedContent": {
              "title": "SEO-optimized title",
              "metaDescription": "SEO-optimized meta description",
              "sections": [
                {
                  "id": "section-id",
                  "type": "heading|text|list|callout|faq",
                  "title": "Optimized section title",
                  "content": "Optimized content"
                },
                // More sections...
              ]
            },
            "seoNotes": [
              {"category": "keywords|headings|meta|links|images", "note": "Specific SEO note", "impact": "low|medium|high"}
            ],
            "seoScore": {
              "overall": 85,
              "keywords": 90,
              "structure": 80,
              "metadata": 85,
              "readability": 90
            },
            "optimizationSuggestions": [
              {"suggestion": "Specific suggestion", "priority": "low|medium|high"}
            ]
          }
          
          Do not include any text outside of the JSON structure. Ensure all quotes are properly escaped.
          `;

        // Use a specific temperature for SEO work
        const seoModel = createModel(0.3); // Lower temperature for precision
        
        // Create an abort controller to prevent hanging
        const controller = new AbortController();
        const timeout = setTimeout(() => {
            controller.abort();
            console.log("SEO optimization agent aborted due to timeout");
        }, 60000); // 1 minute timeout

        try {
            // First attempt with direct JSON parsing
            const rawResponse = await seoModel.invoke([
                new SystemMessage(
                    "You are an SEO specialist with expertise in on-page optimization. ALWAYS respond with valid JSON in the exact format specified."
                ),
                new SystemMessage(seoPrompt)
            ], { signal: controller.signal });
            
            // Try to parse the response as JSON
            let response;
            try {
                // If it's already JSON object, use it directly
                if (typeof rawResponse === 'object' && rawResponse !== null) {
                    response = rawResponse;
                } else {
                    // Try to parse it as JSON string
                    response = JSON.parse(rawResponse);
                }
            } catch (parseError) {
                console.log("SEO agent produced invalid JSON, falling back to structured response generation");
                
                // Fallback to a more structured approach if JSON parsing fails
                const structuredPrompt = `
                  You are an SEO specialist optimizing content. 
                  
                  The previous attempt to generate a proper JSON response failed. 
                  
                  I need you to produce a valid JSON response with the following structure:
                  {
                    "optimizedContent": {
                      "title": "${content.title || "Title"}",
                      "metaDescription": "${content.metaDescription || "Meta description"}",
                      "sections": []
                    },
                    "seoNotes": [
                      {"category": "keywords", "note": "Added keywords to appropriate locations", "impact": "medium"}
                    ],
                    "seoScore": {
                      "overall": 85,
                      "keywords": 80,
                      "structure": 85,
                      "metadata": 90,
                      "readability": 85
                    },
                    "optimizationSuggestions": [
                      {"suggestion": "Add more internal links", "priority": "medium"}
                    ]
                  }
                  
                  Simply take the content provided and make basic SEO improvements.
                  
                  IMPORTANT: Your entire response must be valid JSON. Do not include any text before or after the JSON.
                `;
                
                // Try again with a more explicit model call
                const structuredResponse = await seoModel.invoke([
                    new SystemMessage(
                        "You are an SEO specialist. ONLY respond with valid JSON. Do not include any text outside the JSON structure."
                    ),
                    new SystemMessage(structuredPrompt)
                ], { signal: controller.signal });
                
                try {
                    // Try to parse the structured response
                    if (typeof structuredResponse === 'object' && structuredResponse !== null) {
                        response = structuredResponse;
                    } else {
                        response = JSON.parse(structuredResponse);
                    }
                } catch (secondParseError) {
                    // If all else fails, create a minimal valid response
                    console.error("Still received invalid JSON, creating fallback response");
                    response = {
                        optimizedContent: content, // Use the original content
                        seoNotes: [
                            {category: "general", note: "Basic SEO review completed", impact: "medium"}
                        ],
                        seoScore: {
                            overall: 80,
                            keywords: 75,
                            structure: 80,
                            metadata: 85,
                            readability: 80
                        },
                        optimizationSuggestions: [
                            {suggestion: "Consider adding more keywords", priority: "medium"}
                        ]
                    };
                }
            }
            
            // Validate the response structure
            const validatedResponse = {
                optimizedContent: response.optimizedContent || content,
                seoNotes: Array.isArray(response.seoNotes) ? 
                    response.seoNotes : 
                    [{category: "general", note: "Basic SEO review completed", impact: "medium"}],
                seoScore: response.seoScore || {
                    overall: 80,
                    keywords: 75,
                    structure: 80,
                    metadata: 85,
                    readability: 80
                },
                optimizationSuggestions: Array.isArray(response.optimizationSuggestions) ? 
                    response.optimizationSuggestions : 
                    [{suggestion: "Consider adding more keywords", priority: "medium"}]
            };
            
            // Ensure the optimizedContent has the right structure
            if (!validatedResponse.optimizedContent.title) {
                validatedResponse.optimizedContent.title = content.title || "Title";
            }
            
            if (!validatedResponse.optimizedContent.metaDescription) {
                validatedResponse.optimizedContent.metaDescription = content.metaDescription || "Meta description";
            }
            
            if (!Array.isArray(validatedResponse.optimizedContent.sections)) {
                validatedResponse.optimizedContent.sections = content.sections || [];
            }
            
            // Process sections to ensure they have the right structure
            validatedResponse.optimizedContent.sections = validatedResponse.optimizedContent.sections.map((section, index) => {
                // Find the corresponding original section if it exists
                const originalSection = Array.isArray(content.sections) && content.sections.length > index ? 
                    content.sections[index] : null;
                
                return {
                    id: section.id || originalSection?.id || `section-${index}`,
                    type: section.type || originalSection?.type || "heading",
                    title: section.title || originalSection?.title || `Section ${index + 1}`,
                    content: section.content || originalSection?.content || ""
                };
            });
            
            return {
                agentState: {
                    ...state.agentState,
                    seoOptimizationResult: validatedResponse,
                    reviewPhase: {
                        ...state.agentState.reviewPhase,
                        seoFeedback: validatedResponse.seoNotes.map(note => note.note).join('. '),
                        contentBeforeSEO: content,
                        contentAfterSEO: validatedResponse.optimizedContent
                    }
                },
            };
        } finally {
            // Clean up
            clearTimeout(timeout);
            controller.abort(); // Ensure any pending operations are cancelled
        }
    } catch (error) {
        console.error("Error in SEO optimization agent:", error);
        
        // Create a fallback optimized content in case of errors
        const content = state.agentState.editorialResult?.editedContent || state.agentState.contentResult || {};
        
        const fallbackResult = {
            optimizedContent: content,
            seoNotes: [
                {category: "general", note: "Basic SEO optimization", impact: "medium"}
            ],
            seoScore: {
                overall: 75,
                keywords: 70,
                structure: 75,
                metadata: 80,
                readability: 75
            },
            optimizationSuggestions: [
                {suggestion: "Add more keywords throughout content", priority: "medium"}
            ]
        };
        
        return {
            agentState: {
                ...state.agentState,
                seoOptimizationResult: fallbackResult,
                reviewPhase: {
                    ...state.agentState.reviewPhase,
                    seoFeedback: "Basic SEO optimization applied",
                    contentBeforeSEO: content,
                    contentAfterSEO: fallbackResult.optimizedContent
                },
                error: `Warning: Error in SEO optimization: ${error instanceof Error ? error.message : String(error)}. Using fallback SEO optimization.`,
            },
        };
    }
}

// 3. Internal Linking Agent
export async function internalLinkingAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        // Use the optimized content from the previous agent
        const seoResult = state.agentState.seoOptimizationResult || {};
        const content = seoResult.optimizedContent ||
            state.agentState.editorialResult?.editedContent ||
            state.agentState.contentResult || {};
        
        // Extract relevant data from previous agent results
        const marketResearch = state.agentState.marketResearchResult || {};
        const seoKeywords = state.agentState.seoKeywordResult || {};
        const contentStrategy = state.agentState.contentStrategyResult || {};

        const internalLinkingPrompt = `
          You are a content strategist specializing in internal linking. For a ${state.agentState.contentType} about "${state.agentState.topicFocus}",
          identify opportunities for internal linking based on the actual content and research provided.
          
          CONTENT TO ANALYZE:
          ${JSON.stringify(content)}
          
          MARKET RESEARCH INSIGHTS:
          ${JSON.stringify(marketResearch)}
          
          SEO KEYWORDS:
          ${JSON.stringify(seoKeywords)}
          
          CONTENT STRATEGY:
          ${JSON.stringify(contentStrategy)}
          
          Based on this actual content, the topic "${state.agentState.topicFocus}", and the category "${state.agentState.category}",
          identify 3-5 specific locations in the content where internal links would be valuable. Use the actual phrases and 
          context from the content to suggest anchor text.
          
          The response should have this JSON structure:
          {
            "internalLinks": [
              {
                "anchorText": "...", // Use actual text from the content
                "targetTitle": "...", // Suggest a relevant page title based on the content and keywords
                "targetURL": "/example-path", // Suggest a realistic URL structure
                "context": "...", // The actual sentence or paragraph from the content where this link would fit
                "relevance": 85 // 0-100 based on how well it matches the content and keywords
              },
              ...
            ],
            "linkingStrategy": "..." // Overall strategy based on the actual content analysis
          }
          `;

        const response = await baseModel.invoke([
            new SystemMessage(
                "You are a content strategist specializing in internal linking and content organization."
            ),
            new SystemMessage(internalLinkingPrompt)
        ]);
        //console.log("Result in internalLinkingAgent", response);

        return {
            agentState: {
                ...state.agentState,
                interlinkingResult: response,
            },
        };
    } catch (error) {
        return {
            agentState: {
                ...state.agentState,
                error: `Error in internal linking: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

// 4. Content Scoring Agent
export async function contentScoringAgent(state: {
    agentState: ContentGenerationState;
}): Promise<{ agentState: ContentGenerationState }> {
    try {
        // Use the results from all previous agents
        const marketResearch = state.agentState.marketResearchResult || {};
        const seoKeywords = state.agentState.seoKeywordResult || {};
        const contentStrategy = state.agentState.contentStrategyResult || {};
        const seoResult = state.agentState.seoOptimizationResult || {};
        const internalLinking = state.agentState.interlinkingResult || {};
        const editorialResult = state.agentState.editorialResult || {};

        const content = seoResult.optimizedContent ||
            editorialResult.editedContent ||
            state.agentState.contentResult || {};
        
        const scoringPrompt = `
          You are a content quality analyst evaluating a ${state.agentState.contentType} about "${state.agentState.topicFocus}".
          
          ACTUAL CONTENT TO EVALUATE:
          ${JSON.stringify(content)}
          
          MARKET RESEARCH DATA:
          ${JSON.stringify(marketResearch)}
          
          SEO KEYWORDS AND STRATEGY:
          ${JSON.stringify(seoKeywords)}
          
          CONTENT STRATEGY:
          ${JSON.stringify(contentStrategy)}
          
          SEO OPTIMIZATION RESULTS:
          ${JSON.stringify(seoResult)}
          
          INTERNAL LINKING SUGGESTIONS:
          ${JSON.stringify(internalLinking)}
          
          TARGET AUDIENCE:
          ${state.agentState.targetAudience}
          
          PRIMARY KEYWORDS:
          ${state.agentState.primaryKeywords.join(', ')}
          
          TONE PREFERENCE:
          ${state.agentState.tonePreference}
          
          Based on this actual content and all previous analysis, evaluate the content on:
          1. SEO optimization - How well does it use the identified keywords and follow SEO best practices?
          2. Content quality - Is it informative, engaging, and comprehensive?
          3. Audience alignment - Does it match the needs and interests of the target audience?
          4. Technical quality - Is the grammar, formatting, and readability appropriate?
          
          The response should have this JSON structure:
          {
            "contentScore": {
              "overall": 0, // 0-100, calculated based on the actual content analysis
              "seo": 0, // Score based on keyword usage, meta elements, structure from the actual content
              "contentQuality": 0, // Score based on depth, engagement, and value of the actual content
              "audienceAlignment": 0, // Score based on how well it addresses the target audience needs
              "technicalQuality": 0 // Score based on grammar, formatting, readability of the actual content
            },
            "strengthAreas": [
              {"area": "...", "comment": "..."} // Specific strengths found in the actual content
            ],
            "improvementAreas": [
              {"area": "...", "suggestion": "...", "priority": "low|medium|high"} // Specific areas for improvement
            ],
            "overallAssessment": "..." // Summary assessment based on the actual content analysis
          }
          `;

        const response = await baseModel.invoke([
            new SystemMessage(
                "You are a content quality analyst with expertise in evaluating content effectiveness."
            ),
            new SystemMessage(scoringPrompt)
        ]);
        //console.log("Result in contentScoringAgent", response);

        return {
            agentState: {
                ...state.agentState,
                scoringResult: response,
            },
        };
    } catch (error) {
        return {
            agentState: {
                ...state.agentState,
                error: `Error in content scoring: ${error instanceof Error ? error.message : String(error)}`,
            },
        };
    }
}

// Remove LangGraph-based workflow to avoid infinite loops
// Define a collaboration controller to manage the agent interaction flow
class CollaborationController {
  // Maximum number of iterations to prevent infinite loops
  private static MAX_ITERATIONS = 20;
  private currentIteration = 0;
  
  // Track which agents have completed their primary tasks
  private completedAgents: Record<string, boolean> = {
    marketResearch: false,
    seoKeyword: false,
    contentStrategy: false,
    contentGeneration: false,
    editorial: false,
    seoOptimization: false,
    internalLinking: false,
    contentScoring: false
  };
  
  // Track dependencies between agents
  private dependencies: Record<string, string[]> = {
    marketResearch: [],
    seoKeyword: ['marketResearch'],
    contentStrategy: ['marketResearch', 'seoKeyword'],
    contentGeneration: ['contentStrategy'],
    editorial: ['contentGeneration'],
    seoOptimization: ['editorial'],
    internalLinking: ['seoOptimization'],
    contentScoring: ['internalLinking']
  };
  
  // Check if an agent can be executed based on dependencies
  canExecute(agentType: string, state: ContentGenerationState): boolean {
    // Check if we've exceeded maximum iterations
    if (this.currentIteration >= CollaborationController.MAX_ITERATIONS) {
      console.warn(`Reached maximum iterations (${CollaborationController.MAX_ITERATIONS}). Forcing completion.`);
      return false;
    }
    
    // Check if agent is already completed
    if (this.completedAgents[agentType]) {
      return false;
    }
    
    // Check if dependencies are satisfied
    const deps = this.dependencies[agentType] || [];
    for (const dep of deps) {
      if (!this.completedAgents[dep]) {
        return false;
      }
    }
    
    return true;
  }
  
  // Mark an agent as completed
  markCompleted(agentType: string) {
    this.completedAgents[agentType] = true;
    this.currentIteration++;
    console.log(`Agent ${agentType} completed. Iteration: ${this.currentIteration}`);
  }
  
  // Increment the iteration counter (used when an error occurs but we want to count the attempt)
  incrementIteration() {
    this.currentIteration++;
    console.log(`Iteration incremented due to error. Iteration: ${this.currentIteration}`);
  }
  
  // Check if all agents have completed
  isProcessComplete(): boolean {
    return Object.values(this.completedAgents).every(completed => completed) ||
           this.currentIteration >= CollaborationController.MAX_ITERATIONS;
  }
  
  // Get the next agent to execute
  getNextAgent(state: ContentGenerationState): string | null {
    // Define execution order preference
    const executionOrder = [
      'marketResearch',
      'seoKeyword',
      'contentStrategy',
      'contentGeneration',
      'editorial',
      'seoOptimization',
      'internalLinking',
      'contentScoring'
    ];
    
    for (const agent of executionOrder) {
      if (this.canExecute(agent, state)) {
        return agent;
      }
    }
    
    return null; // No eligible agent found
  }
}

// Main function to execute the content generation process using the custom agent collaboration system
export async function executeContentGeneration(data: {
  contentType: 'product-page' | 'blog-article' | 'buying-guide';
  topicFocus: string;
  category: string;
  targetAudience: string;
  primaryKeywords: string[];
  tonePreference: string;
  competitorUrls?: string[];
  internalData?: any;
  generalInstructions?: string;
  feedback?: string;
  isRetry?: boolean;
}): Promise<any> {
  try {
    console.log("Starting content generation process for:", data.topicFocus);
    
    // Initialize the state with input data
    const state: { agentState: ContentGenerationState } = {
      agentState: {
        contentType: data.contentType,
        topicFocus: data.topicFocus,
        category: data.category,
        targetAudience: data.targetAudience,
        primaryKeywords: data.primaryKeywords,
        tonePreference: data.tonePreference,
        
        // Initialize collaboration context
        collaborationContext: {
          topic: data.topicFocus,
          collaborationHistory: [],
          agentContributions: {},
          currentPhase: 'planning'
        }
      }
    };
    
    // Add optional fields if they exist
    if (data.competitorUrls) state.agentState.competitorUrls = data.competitorUrls;
    if (data.internalData) state.agentState.internalData = data.internalData;
    if (data.generalInstructions) state.agentState.generalInstructions = data.generalInstructions;
    if (data.feedback) state.agentState.feedback = data.feedback;
    if (data.isRetry) state.agentState.isRetry = data.isRetry;
    
    // Create an instance of our collaboration controller
    const controller = new CollaborationController();
    let currentState = state;
    
    // Record the start of the process
    addToCollaborationHistory(currentState.agentState, 'system', 'initialize', `Started content generation process for ${data.topicFocus}`);
    
    // Execute the agent collaboration workflow
    while (!controller.isProcessComplete()) {
      const nextAgent = controller.getNextAgent(currentState.agentState);
      
      if (!nextAgent) {
        // No eligible agent to execute, check if we should exit
        if (controller.isProcessComplete()) {
          console.log('Content generation process completed successfully');
          break;
        } else {
          console.warn('No eligible agent found but process is not complete. Forcing completion.');
          break;
        }
      }
      
      console.log(`Executing agent: ${nextAgent}`);
      try {
        // Execute the corresponding agent
        switch (nextAgent) {
          case 'marketResearch':
            currentState = await executeAgent(marketResearchAgent, currentState);
            break;
          case 'seoKeyword':
            currentState = await executeAgent(seoKeywordAgent, currentState);
            break;
          case 'contentStrategy':
            currentState = await executeAgent(contentStrategyAgent, currentState);
            break;
          case 'contentGeneration':
            currentState = await executeAgent(contentGenerationAgent, currentState);
            break;
          case 'editorial':
            currentState = await executeAgent(editorialAgent, currentState);
            break;
          case 'seoOptimization':
            currentState = await executeAgent(seoOptimizationAgent, currentState);
            break;
          case 'internalLinking':
            currentState = await executeAgent(internalLinkingAgent, currentState);
            break;
          case 'contentScoring':
            currentState = await executeAgent(contentScoringAgent, currentState);
            break;
          default:
            console.warn(`Unknown agent type: ${nextAgent}`);
            break;
        }
        
        // Mark the agent as completed
        controller.markCompleted(nextAgent);
        
        // Add to collaboration history
        addToCollaborationHistory(
          currentState.agentState, 
          nextAgent, 
          'complete', 
          `${getAgentName(nextAgent)} completed its tasks`
        );
        
      } catch (error) {
        console.error(`Error executing agent ${nextAgent}:`, error);
        addToCollaborationHistory(
          currentState.agentState,
          nextAgent,
          'error',
          `Error in ${getAgentName(nextAgent)}: ${error instanceof Error ? error.message : String(error)}`
        );
        
        // Don't mark as completed on error - allows for potential retry
        // But increment the iteration count
        controller.incrementIteration();
      }
    }
    
    // Set the final phase
    if (currentState.agentState.collaborationContext) {
      currentState.agentState.collaborationContext.currentPhase = 'completed';
    }
    
    // Prepare the final result
    const finalContent = currentState.agentState.seoOptimizationResult?.optimizedContent ||
      currentState.agentState.editorialResult?.editedContent ||
      currentState.agentState.contentResult;
    
    // Log completion
    console.log('Content generation process completed. Final content available.');
    
    return {
      success: true,
      content: {
        contentType: data.topicFocus,
        title: finalContent?.title || `${data.topicFocus}`,
        metaDescription: finalContent?.metaDescription || `Comprehensive guide about ${data.topicFocus}`,
        sections: finalContent?.sections || [],
        seoScore: currentState.agentState.seoOptimizationResult?.seoScore ||
                  currentState.agentState.scoringResult?.contentScore || 0,
        internalLinks: currentState.agentState.interlinkingResult?.internalLinks || [],
        improvementSuggestions: [
          ...(currentState.agentState.seoOptimizationResult?.optimizationSuggestions || []),
          ...(currentState.agentState.scoringResult?.improvementAreas || [])
        ],
        collaborationHistory: currentState.agentState.collaborationContext?.collaborationHistory || [],
        agentContributions: currentState.agentState.collaborationContext?.agentContributions || {}
      }
    };
  } catch (error) {
    console.error("Unexpected error in content generation:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unknown error occurred"
    };
  }
}

// Helper function to execute an agent and update the state
async function executeAgent(
  agentFunction: (state: { agentState: ContentGenerationState }) => Promise<{ agentState: ContentGenerationState }>,
  currentState: { agentState: ContentGenerationState }
): Promise<{ agentState: ContentGenerationState }> {
  return await agentFunction(currentState);
}

// Helper function to add an entry to the collaboration history
function addToCollaborationHistory(
  state: ContentGenerationState,
  agent: string,
  action: string,
  description: string,
  details?: any
): void {
  if (!state.collaborationContext) {
    state.collaborationContext = {
      topic: state.topicFocus || '',
      collaborationHistory: [],
      agentContributions: {},
      currentPhase: 'planning'
    };
  }
  
  state.collaborationContext.collaborationHistory.push({
    agent,
    phase: state.collaborationContext.currentPhase,
    action,
    description,
    timestamp: new Date().toISOString(),
    ...(details ? { details } : {})
  });
}

// Helper function to get friendly agent names
function getAgentName(agentType: string): string {
  const agentNames: Record<string, string> = {
    'marketResearch': 'Market Research Agent',
    'seoKeyword': 'SEO Keyword Agent',
    'contentStrategy': 'Content Strategy Agent',
    'contentGeneration': 'Content Generation Agent',
    'editorial': 'Editorial Agent',
    'seoOptimization': 'SEO Optimization Agent',
    'internalLinking': 'Internal Linking Agent',
    'contentScoring': 'Content Scoring Agent'
  };
  
  return agentNames[agentType] || agentType;
}

// Helper function to extract content from LangChain message objects
function extractContentFromLangChainMessage(message: any): string {
    try {
      // Case 1: If it's already a string, return it directly
      if (typeof message === 'string') {
        return message;
      }
      
      // Case 2: If it's a JSON string that contains LangChain message format
      if (typeof message === 'string' && message.includes('"type":"constructor"')) {
        try {
          const parsed = JSON.parse(message);
          if (parsed.kwargs && parsed.kwargs.content) {
            return parsed.kwargs.content;
          }
        } catch (e) {
          // Continue to other cases if JSON parsing fails
        }
      }
      
      // Case 3: If it's an object with the LangChain structure
      if (typeof message === 'object' && message !== null) {
        // Direct LangChain message object
        if (message.kwargs && message.kwargs.content) {
          return message.kwargs.content;
        }
        
        // Nested inside additional properties
        if (message.lc === 1 && message.type === 'constructor' && 
            message.kwargs && message.kwargs.content) {
          return message.kwargs.content;
        }
        
        // Check if it's in 'additional_kwargs'
        if (message.additional_kwargs && message.additional_kwargs.content) {
          return message.additional_kwargs.content;
        }
        
        // Direct content property
        if (message.content && typeof message.content === 'string') {
          return message.content;
        }
      }
      
      // Default fallback: stringify the object if all else fails
      return typeof message === 'object' && message !== null 
        ? JSON.stringify(message) 
        : String(message);
    } catch (error) {
      console.error("Error extracting content from LangChain message:", error);
      return String(message); // Fallback to string conversion
    }
  }
  
  // Function to recursively process content to extract from LangChain messages
  function processContentStructure(content: any): any {
    if (!content) return content;
    
    // If it's a string that might be a LangChain message
    if (typeof content === 'string') {
      return extractContentFromLangChainMessage(content);
    }
    
    // If it's an array, process each item
    if (Array.isArray(content)) {
      return content.map(item => processContentStructure(item));
    }
    
    // If it's an object with sections, process each section
    if (typeof content === 'object' && content !== null) {
      // Special handling for content result structure
      if (content.sections) {
        return {
          ...content,
          sections: content.sections.map((section: any) => {
            if (!section) return section;
            
            return {
              ...section,
              content: processContentStructure(section.content)
            };
          })
        };
      }
      
      // Special handling for FAQ items
      if (Array.isArray(content) && content.length > 0 && 
          typeof content[0] === 'object' && content[0].question && content[0].answer) {
        return content.map(item => ({
          question: processContentStructure(item.question),
          answer: processContentStructure(item.answer)
        }));
      }
      
      // Process all object properties
      const result: any = {};
      for (const key in content) {
        result[key] = processContentStructure(content[key]);
      }
      return result;
    }
    
    // Default case: return unchanged
    return content;
  }
  
  // Add this function at the end of your executeContentGeneration function
  // to process the content before returning it
  function processFinalContent(contentResult: any): any {
    if (!contentResult) return contentResult;
    
    // Process the entire content structure
    const processedContent = processContentStructure(contentResult);
    
    // Ensure any nested LangChain messages are also processed
    return processedContent;
  }
  
  // The old LangGraph-based executeContentGeneration function has been replaced
  // with the new function below that uses the custom agent collaboration system

// Main function to execute the content generation process using the custom agent collaboration system
export async function executeContentGeneration(data: {
  contentType: 'product-page' | 'blog-article' | 'buying-guide';
  topicFocus: string;
  category: string;
  targetAudience: string;
  primaryKeywords: string[];
  tonePreference: string;
  competitorUrls?: string[];
  internalData?: any;
  generalInstructions?: string;
  feedback?: string;
  isRetry?: boolean;
}): Promise<any> {
  try {
    console.log("Starting content generation process for:", data.topicFocus);
    
    // Initialize the state with input data
    const state: { agentState: ContentGenerationState } = {
      agentState: {
        contentType: data.contentType,
        topicFocus: data.topicFocus,
        category: data.category,
        targetAudience: data.targetAudience,
        primaryKeywords: data.primaryKeywords,
        tonePreference: data.tonePreference,
        
        // Initialize collaboration context
        collaborationContext: {
          topic: data.topicFocus,
          collaborationHistory: [],
          agentContributions: {},
          currentPhase: 'planning'
        }
      }
    };
    
    // Add optional fields if they exist
    if (data.competitorUrls) state.agentState.competitorUrls = data.competitorUrls;
    if (data.internalData) state.agentState.internalData = data.internalData;
    if (data.generalInstructions) state.agentState.generalInstructions = data.generalInstructions;
    if (data.feedback) state.agentState.feedback = data.feedback;
    if (data.isRetry) state.agentState.isRetry = data.isRetry;
    
    // Create an instance of our collaboration controller
    const controller = new CollaborationController();
    let currentState = state;
    
    // Record the start of the process
    addToCollaborationHistory(currentState.agentState, 'system', 'initialize', `Started content generation process for ${data.topicFocus}`);
    
    // Execute the agent collaboration workflow
    while (!controller.isProcessComplete()) {
      const nextAgent = controller.getNextAgent(currentState.agentState);
      
      if (!nextAgent) {
        // No eligible agent to execute, check if we should exit
        if (controller.isProcessComplete()) {
          console.log('Content generation process completed successfully');
          break;
        } else {
          console.warn('No eligible agent found but process is not complete. Forcing completion.');
          break;
        }
      }
      
      console.log(`Executing agent: ${nextAgent}`);
      try {
        // Execute the corresponding agent
        switch (nextAgent) {
          case 'marketResearch':
            currentState = await executeAgent(marketResearchAgent, currentState);
            break;
          case 'seoKeyword':
            currentState = await executeAgent(seoKeywordAgent, currentState);
            break;
          case 'contentStrategy':
            currentState = await executeAgent(contentStrategyAgent, currentState);
            break;
          case 'contentGeneration':
            currentState = await executeAgent(contentGenerationAgent, currentState);
            break;
          case 'editorial':
            currentState = await executeAgent(editorialAgent, currentState);
            break;
          case 'seoOptimization':
            currentState = await executeAgent(seoOptimizationAgent, currentState);
            break;
          case 'internalLinking':
            currentState = await executeAgent(internalLinkingAgent, currentState);
            break;
          case 'contentScoring':
            currentState = await executeAgent(contentScoringAgent, currentState);
            break;
          default:
            console.warn(`Unknown agent type: ${nextAgent}`);
            break;
        }
        
        // Mark the agent as completed
        controller.markCompleted(nextAgent);
        
        // Add to collaboration history
        addToCollaborationHistory(
          currentState.agentState, 
          nextAgent, 
          'complete', 
          `${getAgentName(nextAgent)} completed its tasks`
        );
        
      } catch (error) {
        console.error(`Error executing agent ${nextAgent}:`, error);
        addToCollaborationHistory(
          currentState.agentState,
          nextAgent,
          'error',
          `Error in ${getAgentName(nextAgent)}: ${error instanceof Error ? error.message : String(error)}`
        );
        
        // Don't mark as completed on error - allows for potential retry
        // But increment the iteration count
        controller.incrementIteration();
      }
    }
    
    // Set the final phase
    if (currentState.agentState.collaborationContext) {
      currentState.agentState.collaborationContext.currentPhase = 'completed';
    }
    
    // Prepare the final result
    const finalContent = currentState.agentState.seoOptimizationResult?.optimizedContent ||
      currentState.agentState.editorialResult?.editedContent ||
      currentState.agentState.contentResult;
    
    // Process any LangChain message objects in the content
    const processedFinalContent = processFinalContent(finalContent);
    
    // Log completion
    console.log('Content generation process completed. Final content available.');
    
    return {
      success: true,
      content: {
        contentType: data.topicFocus,
        title: processedFinalContent?.title || `${data.topicFocus}`,
        metaDescription: processedFinalContent?.metaDescription || `Comprehensive guide about ${data.topicFocus}`,
        sections: processedFinalContent?.sections || [],
        seoScore: processFinalContent(currentState.agentState.seoOptimizationResult?.seoScore) ||
                 processFinalContent(currentState.agentState.scoringResult?.contentScore) || 0,
        internalLinks: processFinalContent(currentState.agentState.interlinkingResult?.internalLinks) || [],
        improvementSuggestions: [
          ...processFinalContent(currentState.agentState.seoOptimizationResult?.optimizationSuggestions || []),
          ...processFinalContent(currentState.agentState.scoringResult?.improvementAreas || [])
        ],
        collaborationHistory: currentState.agentState.collaborationContext?.collaborationHistory || [],
        agentContributions: currentState.agentState.collaborationContext?.agentContributions || {}
      }
    };
  } catch (error) {
    console.error("Unexpected error in content generation:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unknown error occurred"
    };
  }
}

  // Dynamic Agent Collaboration System
  // These functions enable back-and-forth discussions among specialized agents
  
  // Initiate the collaboration process
  export async function initiateCollaboration(agentState: ContentGenerationState) {
      // Create initial planning questions for each specialized agent
      const planningQuestions = {
          marketResearch: `What market insights should we gather for "${agentState.topicFocus}"?`,
          seoKeyword: `What keywords should we target for "${agentState.topicFocus}"?`,
          contentStrategy: `What content structure would work best for "${agentState.topicFocus}"?`
      };
      
      // Record these questions in collaboration history
      agentState.collaborationContext.collaborationHistory.push({
          phase: 'planning',
          action: 'initial_questions',
          questions: planningQuestions,
          timestamp: new Date().toISOString()
      });
      
      // Start with market research as initial input
      const marketResearchResult = await callAgent('market-research', planningQuestions.marketResearch, agentState);
      
      // Record the contribution
      agentState.collaborationContext.agentContributions.marketResearch = {
          initialContribution: marketResearchResult,
          timestamp: new Date().toISOString()
      };
      
      // Update market research result in state
      agentState.marketResearchResult = marketResearchResult;
  }
  
  // Determine which agent should contribute next based on current state
  export function determineNextAgent(agentState: ContentGenerationState): string {
      const { currentPhase, agentContributions } = agentState.collaborationContext;
      
      // Logic to determine which agent should contribute next
      if (currentPhase === 'planning') {
          if (!agentContributions.seoKeyword) return 'seoKeyword';
          if (!agentContributions.contentStrategy) return 'contentStrategy';
          return 'contentGeneration'; // Move to content generation phase
      }
      
      if (currentPhase === 'creation') {
          if (!agentContributions.contentGeneration) return 'contentGeneration';
          return 'editorial'; // Move to review phase
      }
      
      if (currentPhase === 'review') {
          if (!agentContributions.editorial) return 'editorial';
          if (!agentContributions.seoOptimization) return 'seoOptimization';
          return 'contentScoring'; // Final review
      }
      
      return 'contentGeneration'; // Default
  }
  
  // Request contribution from a specific agent
  export async function requestAgentContribution(agentState: ContentGenerationState, agentType: string) {
      // Formulate the request based on current context and previous contributions
      const request = formulateRequest(agentState, agentType);
      
      // Call the appropriate agent
      const result = await callAgent(agentType, request, agentState);
      
      // Record the contribution
      agentState.collaborationContext.agentContributions[agentType] = {
          contribution: result,
          timestamp: new Date().toISOString()
      };
      
      // Update state with the result
      updateStateWithAgentContribution(agentState, agentType, result);
      
      // Record in collaboration history
      agentState.collaborationContext.collaborationHistory.push({
          phase: agentState.collaborationContext.currentPhase,
          action: `${agentType}_contribution`,
          result: summarizeContribution(result),
          timestamp: new Date().toISOString()
      });
  }
  
  // Evaluate progress and update the current phase
  export function evaluateProgressAndUpdatePhase(agentState: ContentGenerationState) {
      const { currentPhase, agentContributions } = agentState.collaborationContext;
      
      // Update phase based on progress
      if (currentPhase === 'planning' && 
          agentContributions.marketResearch && 
          agentContributions.seoKeyword && 
          agentContributions.contentStrategy) {
          agentState.collaborationContext.currentPhase = 'creation';
          
          // Record phase transition in history
          agentState.collaborationContext.collaborationHistory.push({
              phase: 'transition',
              action: 'phase_change',
              from: 'planning',
              to: 'creation',
              timestamp: new Date().toISOString()
          });
      }
      
      if (currentPhase === 'creation' && 
          agentContributions.contentGeneration) {
          agentState.collaborationContext.currentPhase = 'review';
          
          // Record phase transition
          agentState.collaborationContext.collaborationHistory.push({
              phase: 'transition',
              action: 'phase_change',
              from: 'creation',
              to: 'review',
              timestamp: new Date().toISOString()
          });
      }
  }
  
  // Check if additional input is needed based on current state
  export function needsAdditionalInput(agentState: ContentGenerationState): boolean {
      const { collaborationHistory } = agentState.collaborationContext;
      
      // Check the last few interactions for questions or requests for more information
      const recentInteractions = collaborationHistory.slice(-3);
      
      return recentInteractions.some(interaction => 
          interaction.action === 'request_more_info' || 
          interaction.action === 'clarification_needed'
      );
  }
  
  // Identify which agent can provide supporting information
  export function identifySupportAgent(agentState: ContentGenerationState): string {
      const { collaborationHistory } = agentState.collaborationContext;
      const lastInteraction = collaborationHistory[collaborationHistory.length - 1];
      
      // Determine which agent can address the last question/request
      if (lastInteraction.requestType === 'market_insight') return 'marketResearch';
      if (lastInteraction.requestType === 'keyword_suggestion') return 'seoKeyword';
      if (lastInteraction.requestType === 'content_structure') return 'contentStrategy';
      if (lastInteraction.requestType === 'editorial_feedback') return 'editorial';
      
      // Default to the agent with most relevant expertise
      return determineExpertForQuery(lastInteraction.query);
  }
  
  // Request supporting information from an agent
  export async function requestSupportingInformation(agentState: ContentGenerationState, agentType: string) {
      const { collaborationHistory } = agentState.collaborationContext;
      const lastInteraction = collaborationHistory[collaborationHistory.length - 1];
      
      // Formulate a specific request based on the last interaction
      const request = `Regarding "${lastInteraction.query}": ${lastInteraction.specificQuestion}`;
      
      // Call the agent for supporting information
      const result = await callAgent(agentType, request, agentState);
      
      // Record the supporting information
      agentState.collaborationContext.collaborationHistory.push({
          phase: agentState.collaborationContext.currentPhase,
          action: 'supporting_information',
          from: agentType,
          information: summarizeContribution(result),
          inResponseTo: lastInteraction.id,
          timestamp: new Date().toISOString()
      });
      
      // Update state with the new information
      updateStateWithSupportingInformation(agentState, agentType, result);
  }
  
  // Check if content meets quality criteria
  export function contentMeetsQualityCriteria(agentState: ContentGenerationState): boolean {
      // Check if we have all necessary reviews
      const { agentContributions } = agentState.collaborationContext;
      
      if (!agentContributions.editorial || 
          !agentContributions.seoOptimization || 
          !agentContributions.contentScoring) {
          return false;
      }
      
      // Check if there are any critical issues flagged
      const editorialFeedback = agentContributions.editorial.contribution;
      const seoFeedback = agentContributions.seoOptimization.contribution;
      const scoringFeedback = agentContributions.contentScoring.contribution;
      
      const hasCriticalIssues = 
          editorialFeedback.criticalIssues?.length > 0 ||
          seoFeedback.criticalIssues?.length > 0 ||
          (scoringFeedback.overallScore && scoringFeedback.overallScore < 70);
      
      return !hasCriticalIssues;
  }
  
  // Finalize content based on all agent contributions
  export function finalizeContent(agentState: ContentGenerationState): any {
      // Combine all contributions into final content
      const { agentContributions } = agentState.collaborationContext;
      
      // Get the latest version of content (after all reviews and optimizations)
      const finalContent = agentContributions.seoOptimization?.contribution?.optimizedContent || 
                           agentContributions.editorial?.contribution?.editedContent ||
                           agentContributions.contentGeneration?.contribution;
      
      // Add metadata about the collaboration process
      return {
          content: finalContent,
          collaborationSummary: {
              topic: agentState.topicFocus,
              collaborationProcess: summarizeCollaborationProcess(agentState.collaborationContext),
              contributors: Object.keys(agentContributions),
              marketInsights: summarizeMarketInsights(agentState.marketResearchResult),
              keywordStrategy: summarizeKeywords(agentState.seoKeywordResult),
              contentStructure: summarizeContentStructure(agentState.contentStrategyResult),
              qualityScore: agentContributions.contentScoring?.contribution?.overallScore || "N/A"
          }
      };
  }
  
  // Helper function to call an agent via A2A protocol
  export async function callAgent(agentType: string, request: string, agentState: ContentGenerationState) {
      try {
          // Map agent type to endpoint
          const agentEndpoint = mapAgentTypeToEndpoint(agentType);
          
          // Prepare metadata based on current state
          const metadata = prepareMetadataForAgent(agentType, agentState);
          
          // Make the API call to the agent
          const response = await fetch(`/api/agents/${agentEndpoint}`, {
              method: 'POST',
              headers: {
                  'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                  jsonrpc: '2.0',
                  method: 'tasks/send',
                  params: {
                      task: {
                          history: [
                              {
                                  role: 'user',
                                  parts: [
                                      {
                                          type: 'text',
                                          text: request
                                      }
                                  ]
                              }
                          ],
                          metadata: metadata,
                          id: generateTaskId()
                      }
                  },
                  id: Math.floor(Math.random() * 10000)
              })
          });
          
          const result = await response.json();
          
          if (result.error) {
              throw new Error(`Agent error: ${result.error.message}`);
          }
          
          // Extract the relevant data from the agent's response
          return extractRelevantData(agentType, result.result.task);
      } catch (error) {
          console.error(`Error calling ${agentType} agent:`, error);
          throw error;
      }
  }
  
  // Helper functions for the collaboration system
  export function mapAgentTypeToEndpoint(agentType: string): string {
      const agentEndpoints = {
          marketResearch: 'market-research',
          seoKeyword: 'seo-keyword',
          contentStrategy: 'content-strategy',
          contentGeneration: 'content-generation',
          editorial: 'editorial',
          seoOptimization: 'seo-optimization',
          contentScoring: 'content-scoring'
      };
      
      return agentEndpoints[agentType] || agentType;
  }
  
  export function prepareMetadataForAgent(agentType: string, agentState: ContentGenerationState): any {
      // Prepare relevant metadata based on agent type and current state
      const metadata: any = {
          topic: agentState.topicFocus,
          contentType: agentState.contentType,
          targetAudience: agentState.targetAudience
      };
      
      // Add agent-specific metadata
      if (agentType === 'seoKeyword' && agentState.marketResearchResult) {
          metadata.marketResearchResult = agentState.marketResearchResult;
      }
      
      if (agentType === 'contentStrategy') {
          if (agentState.marketResearchResult) metadata.marketResearchResult = agentState.marketResearchResult;
          if (agentState.seoKeywordResult) metadata.seoKeywordResult = agentState.seoKeywordResult;
      }
      
      if (agentType === 'contentGeneration') {
          if (agentState.marketResearchResult) metadata.marketResearchResult = agentState.marketResearchResult;
          if (agentState.seoKeywordResult) metadata.seoKeywordResult = agentState.seoKeywordResult;
          if (agentState.contentStrategyResult) metadata.contentStrategyResult = agentState.contentStrategyResult;
      }
      
      if (agentType === 'seoOptimization') {
          if (agentState.contentResult) metadata.content = agentState.contentResult;
          if (agentState.seoKeywordResult) metadata.seoKeywordResult = agentState.seoKeywordResult;
      }
      
      return metadata;
  }
  
  export function generateTaskId(): string {
      return `task-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  }
  
  export function extractRelevantData(agentType: string, task: any): any {
      // Extract the relevant data from the agent's response
      if (!task.artifacts || task.artifacts.length === 0) {
          return null;
      }
      
      // Find the relevant artifact based on agent type
      const relevantArtifactName = getRelevantArtifactName(agentType);
      const artifact = task.artifacts.find(a => a.name === relevantArtifactName);
      
      if (!artifact || !artifact.parts || artifact.parts.length === 0) {
          return null;
      }
      
      // Extract the data from the artifact
      return artifact.parts[0].data;
  }
  
  export function getRelevantArtifactName(agentType: string): string {
      const artifactNames = {
          marketResearch: 'Market Research Result',
          seoKeyword: 'SEO Keyword Research Result',
          contentStrategy: 'Content Strategy Result',
          contentGeneration: 'Generated Content',
          editorial: 'Editorial Result',
          seoOptimization: 'SEO Optimization Result',
          contentScoring: 'Content Scoring Result'
      };
      
      return artifactNames[agentType] || 'Result';
  }
  
  export function formulateRequest(agentState: ContentGenerationState, agentType: string): string {
      // Formulate a request based on current context and previous contributions
      const { collaborationContext } = agentState;
      const topic = agentState.topicFocus;
      
      if (agentType === 'marketResearch') {
          return `Perform market research on "${topic}" focusing on target audience needs, competitor analysis, and market trends.`;
      }
      
      if (agentType === 'seoKeyword') {
          return `Generate SEO keywords for "${topic}" based on the market research insights. Focus on primary and secondary keywords that will drive traffic.`;
      }
      
      if (agentType === 'contentStrategy') {
          return `Develop a content strategy for "${topic}" using the market research and SEO keywords. Include content structure, tone, and approach.`;
      }
      
      if (agentType === 'contentGeneration') {
          return `Generate content for "${topic}" following the content strategy and incorporating SEO keywords. Create engaging, valuable content for the target audience.`;
      }
      
      if (agentType === 'editorial') {
          return `Review and edit the content for "${topic}". Focus on clarity, coherence, grammar, and alignment with the content strategy.`;
      }
      
      if (agentType === 'seoOptimization') {
          return `Optimize the content for "${topic}" for search engines. Ensure proper keyword usage, meta description, and SEO best practices.`;
      }
      
      if (agentType === 'contentScoring') {
          return `Score the content for "${topic}" based on quality, engagement potential, SEO optimization, and alignment with the content strategy.`;
      }
      
      return `Contribute to the content creation process for "${topic}" based on your expertise.`;
  }
  
  export function updateStateWithAgentContribution(agentState: ContentGenerationState, agentType: string, result: any) {
      // Update state with the agent's contribution
      if (agentType === 'marketResearch') {
          agentState.marketResearchResult = result;
      } else if (agentType === 'seoKeyword') {
          agentState.seoKeywordResult = result;
      } else if (agentType === 'contentStrategy') {
          agentState.contentStrategyResult = result;
      } else if (agentType === 'contentGeneration') {
          agentState.contentResult = result;
      } else if (agentType === 'editorial') {
          agentState.editorialResult = result;
      } else if (agentType === 'seoOptimization') {
          agentState.seoOptimizationResult = result;
      } else if (agentType === 'contentScoring') {
          agentState.scoringResult = result;
      }
  }
  
  export function updateStateWithSupportingInformation(agentState: ContentGenerationState, agentType: string, result: any) {
      // Update state with supporting information
      const { collaborationContext } = agentState;
      const lastInteraction = collaborationContext.collaborationHistory[collaborationContext.collaborationHistory.length - 1];
      
      // Add the supporting information to the relevant part of the state
      if (lastInteraction.requestType === 'market_insight') {
          agentState.marketResearchResult = {
              ...agentState.marketResearchResult,
              additionalInsights: result
          };
      } else if (lastInteraction.requestType === 'keyword_suggestion') {
          agentState.seoKeywordResult = {
              ...agentState.seoKeywordResult,
              additionalKeywords: result
          };
      } else if (lastInteraction.requestType === 'content_structure') {
          agentState.contentStrategyResult = {
              ...agentState.contentStrategyResult,
              refinedStructure: result
          };
      }
  }
  
  export function summarizeContribution(contribution: any): string {
      // Create a brief summary of the contribution
      if (!contribution) return "No contribution provided";
      
      // Extract key information based on the type of contribution
      if (typeof contribution === 'string') {
          return contribution.length > 100 ? contribution.substring(0, 100) + '...' : contribution;
      }
      
      if (typeof contribution === 'object') {
          // Try to extract the most relevant information
          const summary = [];
          
          if (contribution.title) summary.push(`Title: ${contribution.title}`);
          if (contribution.summary) summary.push(`Summary: ${contribution.summary}`);
          if (contribution.keyPoints) summary.push(`Key Points: ${Array.isArray(contribution.keyPoints) ? contribution.keyPoints.join(', ') : contribution.keyPoints}`);
          if (contribution.primaryKeywords) summary.push(`Primary Keywords: ${Array.isArray(contribution.primaryKeywords) ? contribution.primaryKeywords.join(', ') : contribution.primaryKeywords}`);
          
          return summary.length > 0 ? summary.join(' | ') : JSON.stringify(contribution).substring(0, 100) + '...';
      }
      
      return "Contribution received";
  }
  
  export function summarizeCollaborationProcess(collaborationContext: any): any {
      // Summarize the collaboration process
      return {
          phases: collaborationContext.collaborationHistory
              .filter(item => item.action === 'phase_change')
              .map(item => ({
                  from: item.from,
                  to: item.to,
                  timestamp: item.timestamp
              })),
          interactions: collaborationContext.collaborationHistory.length,
          contributors: Object.keys(collaborationContext.agentContributions),
          duration: calculateDuration(collaborationContext)
      };
  }
  
  export function calculateDuration(collaborationContext: any): string {
      // Calculate the duration of the collaboration process
      if (collaborationContext.collaborationHistory.length < 2) {
          return "N/A";
      }
      
      const firstTimestamp = new Date(collaborationContext.collaborationHistory[0].timestamp).getTime();
      const lastTimestamp = new Date(collaborationContext.collaborationHistory[collaborationContext.collaborationHistory.length - 1].timestamp).getTime();
      
      const durationMs = lastTimestamp - firstTimestamp;
      const durationMinutes = Math.floor(durationMs / 60000);
      const durationSeconds = Math.floor((durationMs % 60000) / 1000);
      
      return `${durationMinutes}m ${durationSeconds}s`;
  }
  
  export function summarizeMarketInsights(marketResearchResult: any): string {
      if (!marketResearchResult) return "No market research available";
      
      return marketResearchResult.marketInsights ? 
          (marketResearchResult.marketInsights.length > 150 ? 
              marketResearchResult.marketInsights.substring(0, 150) + '...' : 
              marketResearchResult.marketInsights) : 
          "Market insights available";
  }
  
  export function summarizeKeywords(seoKeywordResult: any): any {
      if (!seoKeywordResult) return { primary: [], secondary: [] };
      
      return {
          primary: seoKeywordResult.primaryKeywords || [],
          secondary: seoKeywordResult.secondaryKeywords ? 
              (typeof seoKeywordResult.secondaryKeywords === 'string' ? 
                  seoKeywordResult.secondaryKeywords.split(',').map(k => k.trim()) : 
                  seoKeywordResult.secondaryKeywords) : 
              []
      };
  }
  
  export function summarizeContentStructure(contentStrategyResult: any): any {
      if (!contentStrategyResult) return [];
      
      return contentStrategyResult.contentStructure || [];
  }
  
  export function determineExpertForQuery(query: string): string {
      // Determine which agent has the most relevant expertise for a query
      const keywords = {
          marketResearch: ['market', 'audience', 'competitor', 'trend', 'demographic', 'industry'],
          seoKeyword: ['keyword', 'seo', 'search', 'ranking', 'serp', 'query'],
          contentStrategy: ['structure', 'format', 'outline', 'approach', 'strategy', 'plan'],
          contentGeneration: ['write', 'content', 'article', 'blog', 'text', 'paragraph'],
          editorial: ['edit', 'grammar', 'style', 'clarity', 'proofread', 'review'],
          seoOptimization: ['optimize', 'meta', 'title tag', 'description', 'heading', 'h1']
      };
      
      // Count keyword matches for each agent
      const matches = {};
      
      for (const [agent, agentKeywords] of Object.entries(keywords)) {
          matches[agent] = agentKeywords.filter(keyword => 
              query.toLowerCase().includes(keyword.toLowerCase())
          ).length;
      }
      
      // Find the agent with the most matches
      let bestAgent = 'contentGeneration';
      let maxMatches = 0;
      
      for (const [agent, matchCount] of Object.entries(matches)) {
          if (matchCount > maxMatches) {
              maxMatches = matchCount;
              bestAgent = agent;
          }
      }
      
      return bestAgent;
  }