/**
 * Enhanced Agent2Agent (A2A) Protocol Types
 * 
 * This file contains extended type definitions for the A2A protocol, which enables
 * sophisticated collaboration and reasoning between intelligent agents in a content
 * generation system.
 */

// Agent Card - Describes agent capabilities and skills
export interface AgentCard {
  name: string;
  description: string;
  url: string;
  provider?: {
    organization: string;
    url: string;
  };
  version: string;
  documentationUrl?: string;
  capabilities: {
    streaming?: boolean;
    pushNotifications?: boolean;
    stateTransitionHistory?: boolean;
    reasoning?: boolean;
    artifactCreation?: boolean;
    feedback?: boolean;
    deliberation?: boolean;
  };
  authentication: {
    schemes: string[];
    credentials?: string;
  };
  defaultInputModes: string[];
  defaultOutputModes: string[];
  skills: AgentSkill[];
  specializations?: string[];
  knowledgeDomains?: string[];
}

export interface AgentSkill {
  id: string;
  name: string;
  description: string;
  tags: string[];
  examples?: string[];
  inputModes?: string[];
  outputModes?: string[];
  confidenceLevel?: number; // 0-1 scale of confidence in this skill
  requiresCollaboration?: boolean; // Indicates if skill works better with multi-agent input
}

// Task - Core unit of work in A2A
export interface A2ATask {
  id: string;
  sessionId: string;
  status: TaskStatus;
  history?: A2AMessage[];
  artifacts?: EnhancedArtifact[];
  decisions?: Decision[];
  metadata?: Record<string, any>;
  collaborationState?: CollaborationState;
}

export interface TaskStatus {
  state: TaskState;
  message?: A2AMessage;
  timestamp?: string;
  progress?: number; // 0-100 percentage
  currentStage?: string;
  expectedCompletion?: string; // ISO timestamp
}

export type TaskState = 
  | "submitted"
  | "planning"
  | "discussing"
  | "working"
  | "input-required"
  | "reviewing"
  | "refining"
  | "completed"
  | "canceled"
  | "failed"
  | "unknown";

// Event types for streaming
export interface TaskStatusUpdateEvent {
  id: string;
  status: TaskStatus;
  final: boolean;
  metadata?: Record<string, any>;
}

export interface TaskArtifactUpdateEvent {
  id: string;
  artifact: EnhancedArtifact;
  metadata?: Record<string, any>;
  reasoning?: Reasoning;
}

// Enhanced Message - Communication between agents with reasoning
export interface EnhancedA2AMessage extends A2AMessage {
  id: string;
  timestamp: string;
  from: string;
  to: string | string[];
  replyTo?: string;
  conversationId: string;
  reasoning?: Reasoning;
  intentions?: MessageIntention[];
  requestedActions?: RequestedAction[];
  artifactReferences?: string[];
  decisionReferences?: string[];
}

// Standard A2A Message
export interface A2AMessage {
  role: "user" | "agent" | "system";
  parts: Part[];
  metadata?: Record<string, any>;
}

// Enhanced reasoning structure
export interface Reasoning {
  thoughts: string[];
  considerations: string[];
  alternatives?: string[];
  decision: string;
  confidence: number; // 0-1 scale
  sources?: string[];
  constraints?: string[];
}

// Message intentions to clarify purpose
export type MessageIntention = 
  | "inform"
  | "request"
  | "suggest"
  | "critique"
  | "elaborate"
  | "decide"
  | "clarify"
  | "summarize"
  | "brainstorm";

// Actions an agent can request from others
export interface RequestedAction {
  agent: string;
  action: string;
  priority: number; // 1-10 scale
  rationale: string;
  deadline?: string; // ISO timestamp
  parameters?: Record<string, any>;
}

// Part - Content unit within Message or Artifact
export interface TextPart {
  type: "text";
  text: string;
  metadata?: Record<string, any>;
}

export interface FilePart {
  type: "file";
  file: {
    name?: string;
    mimeType?: string;
    bytes?: string;
    uri?: string;
  };
  metadata?: Record<string, any>;
}

export interface DataPart {
  type: "data";
  data: Record<string, any>;
  metadata?: Record<string, any>;
}

export type Part = TextPart | FilePart | DataPart;

// Enhanced Artifact with versioning, attribution and feedback
export interface EnhancedArtifact extends Artifact {
  id: string;
  type: ArtifactType;
  version: number;
  contributors: string[];
  feedback: Feedback[];
  history: ArtifactVersion[];
  dependencies?: string[]; // IDs of artifacts this depends on
  status: "draft" | "review" | "approved" | "published";
  qualityScore?: number; // 0-100 scale
}

// Artifact types for content generation
export type ArtifactType = 
  | "audience-analysis"
  | "market-research"
  | "keyword-set"
  | "content-structure"
  | "content-outline"
  | "draft-content"
  | "seo-analysis"
  | "final-content"
  | "draft-content"
  | "final-content"
  | "seo-analysis"
  | "technical-audit";

// Standard Artifact
export interface Artifact {
  id: string;
  type: ArtifactType;
  name?: string;
  description?: string;
  creator: string;
  timestamp: string;
  data: any;
  messageId?: string;
  parts?: Part[];
  metadata?: Record<string, any>;
  index?: number;
  append?: boolean;
  lastChunk?: boolean;
}

// Artifact version history
export interface ArtifactVersion {
  version: number;
  timestamp: string;
  agent: string;
  changes: string;
  parts: Part[];
  reasoning?: Reasoning;
}

// Structured feedback on artifacts
export interface Feedback {
  id: string;
  agent: string;
  timestamp: string;
  category: "improvement" | "suggestion" | "issue" | "praise";
  content: string;
  priority: number; // 1-10 scale
  location?: string; // Reference to specific part
  reasoning?: Reasoning;
  status: "pending" | "accepted" | "rejected" | "implemented";
  resolution?: string;
}

// Collaborative decision making
export interface Decision {
  id: string;
  topic: string;
  description: string;
  options: DecisionOption[];
  status: "open" | "decided" | "implemented";
  decision?: string;
  decidedBy?: string[];
  decidedAt?: string;
  implementedIn?: string[]; // artifact IDs
  reasoning?: Reasoning;
}

export interface DecisionOption {
  id: string;
  description: string;
  rationale: string;
  proposedBy: string;
  votes: DecisionVote[];
  pros: string[];
  cons: string[];
}

export interface DecisionVote {
  agent: string;
  confidence: number; // 0-1 scale
  reasoning: string;
  timestamp: string;
}

// Shared collaboration state
export interface CollaborationState {
  id: string;
  topic: string;
  contentType: "blog-article" | "product-page" | "buying-guide";
  targetAudience: string;
  tone: string;
  keywords: string[];
  currentGoal: string;
  goals: Goal[];
  progress: number; // 0-100 percentage
  activeAgents: string[];
  conversations: Record<string, string[]>; // conversationId -> messageIds
  messageIndex: Record<string, EnhancedA2AMessage>; // messageId -> message
  artifactIndex: Record<string, EnhancedArtifact>; // artifactId -> artifact
  decisionIndex: Record<string, Decision>; // decisionId -> decision
  startTime: string;
  endTime?: string;
  status: "active" | "paused" | "completed" | "failed";
}

// Goal-oriented workflow
export interface Goal {
  id: string;
  description: string;
  criteria: string[];
  dependencies: string[];
  assignedTo: string[];
  status: "pending" | "in-progress" | "completed" | "blocked";
  artifacts: string[];
  decisions: string[];
  progress: number; // 0-100 percentage
  pendingActions?: PendingAction[];
  completedAt?: string; // ISO timestamp for when the goal was completed
}

// Pending action for a goal
export interface PendingAction {
  id: string;
  agent: string;
  action: string;
  completed: boolean;
}

// Push Notification Configuration
export interface PushNotificationConfig {
  url: string;
  token?: string;
  authentication?: {
    schemes: string[];
    credentials?: string;
  };
}

// Content Generation Request
export interface ContentGenerationRequest {
  id?: string;
  topic: string;
  contentType?: "blog-article" | "product-page" | "buying-guide";
  targetAudience: string;
  tone?: string;
  keywords?: string[];
  additionalContext?: string;
}

// Collaboration Message (Legacy format)
export interface CollaborationMessage {
  id: string;
  timestamp: string;
  from: string;
  to: string | string[];
  type: string;
  content: string | any;
  replyTo?: string;
  conversationId: string;
  reasoning?: {
    thoughts?: string[];
    considerations?: string[];
    alternatives?: string[];
    decision?: string;
  };
}

// JSON-RPC Request/Response Types
export interface JsonRpcRequest {
  jsonrpc: "2.0";
  id: number | string;
  method: string;
  params: any;
}

export interface JsonRpcResponse {
  jsonrpc: "2.0";
  id: number | string;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}

// A2A Method Parameters
export interface TaskSendParams {
  id: string;
  sessionId?: string;
  message: EnhancedA2AMessage;
  historyLength?: number;
  pushNotification?: PushNotificationConfig;
  metadata?: Record<string, any>;
}

export interface TaskGetParams {
  id: string;
  historyLength?: number;
  includeArtifacts?: boolean;
  includeDecisions?: boolean;
  metadata?: Record<string, any>;
}

export interface TaskCancelParams {
  id: string;
  metadata?: Record<string, any>;
}

export interface TaskSubscribeParams {
  id: string;
  metadata?: Record<string, any>;
  artifactTypes?: ArtifactType[];
  decisionTopics?: string[];
}

export interface TaskPushNotificationSetParams {
  id: string;
  pushNotificationConfig: PushNotificationConfig;
}

export interface TaskPushNotificationGetParams {
  id: string;
}