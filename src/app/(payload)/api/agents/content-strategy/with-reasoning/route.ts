// src/app/(payload)/api/agents/content-strategy/with-reasoning/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { 
  EnhancedA2AMessage, 
  Reasoning, 
  Part, 
  TextPart, 
  DataPart,
  CollaborationState,
  ArtifactType
} from '../../a2atypes';

/**
 * Generate content strategy with explicit reasoning
 */
async function generateContentStrategyWithReasoning(
  topic: string,
  targetAudience: string,
  contentType: string,
  keywords: string[],
  tone: string
): Promise<{ contentStrategy: any, reasoning: Reasoning }> {
  // Explicitly show reasoning process
  const thoughts = [
    `The topic "${topic}" needs a comprehensive structure for a ${contentType}`,
    `The target audience "${targetAudience}" will influence section depth and terminology`,
    `The content should incorporate these keywords naturally: ${keywords.join(', ')}`,
    `A ${tone} tone is appropriate for this audience and content type`
  ];
  
  const considerations = [
    'Information should flow logically from basic to advanced concepts',
    'Each section needs clear purpose and key talking points',
    'Structure should support both skimming and deep reading patterns',
    'Content should be comprehensive while avoiding information overload',
    'The structure should be adaptable to different content lengths if needed'
  ];
  
  const alternatives = [
    'Problem-solution structure (could work well but might be limiting)',
    'Chronological approach (not ideal for most informational content)',
    'Comparison-based structure (good for product content but less versatile)',
    'Question-answer format (engaging but may not cover all aspects deeply)'
  ];
  
  // Create a tailored content structure based on topic and content type
  let contentStructure: any[] = [];
  const decisionRationale = `A comprehensive section-based structure will best organize information about ${topic} for ${targetAudience} in a ${contentType} format.`;
  
  // Generate content structure based on content type
  if (contentType === 'blog-article') {
    contentStructure = [
      {
        title: `Introduction to ${topic}`,
        purpose: "Hook the reader and establish relevance",
        keyPoints: [
          "Why this topic matters now",
          "Brief overview of what readers will learn",
          "Establish credibility on the subject"
        ],
        estimatedWordCount: 300,
        keywordTargets: keywords.slice(0, 2),
        contentDirection: "Start with a compelling statistic or question about " + topic
      },
      {
        title: `Understanding ${topic}: Key Concepts`,
        purpose: "Build a foundation of knowledge",
        keyPoints: [
          "Definition and core principles",
          "Historical context or evolution",
          "Current significance in the industry"
        ],
        estimatedWordCount: 500,
        keywordTargets: keywords.slice(1, 3),
        contentDirection: "Define terms clearly and provide concrete examples"
      },
      {
        title: `The Impact of ${topic} on ${targetAudience}`,
        purpose: "Establish relevance to the specific audience",
        keyPoints: [
          "Specific benefits or challenges",
          "Real-world applications",
          "Expected outcomes"
        ],
        estimatedWordCount: 600,
        keywordTargets: keywords.slice(0, 4),
        contentDirection: "Include case studies or testimonials from similar audiences"
      },
      {
        title: `Best Practices for Implementing ${topic}`,
        purpose: "Provide actionable insights",
        keyPoints: [
          "Step-by-step approaches",
          "Common pitfalls to avoid",
          "Tools and resources to leverage"
        ],
        estimatedWordCount: 800,
        keywordTargets: keywords.slice(2, 5),
        contentDirection: "Break down into clear, actionable steps with examples"
      },
      {
        title: `Future Trends in ${topic}`,
        purpose: "Demonstrate forward thinking and ongoing relevance",
        keyPoints: [
          "Emerging developments",
          "Predictions from industry experts",
          "How to prepare for future changes"
        ],
        estimatedWordCount: 400,
        keywordTargets: keywords.slice(3),
        contentDirection: "Balance speculation with evidence-based predictions"
      },
      {
        title: `Conclusion: Taking Action on ${topic}`,
        purpose: "Summarize key points and encourage action",
        keyPoints: [
          "Recap of main insights",
          "Specific next steps for readers",
          "Additional resources for continued learning"
        ],
        estimatedWordCount: 300,
        keywordTargets: keywords.slice(0, 2),
        contentDirection: "End with a clear call to action that ties to the introduction"
      }
    ];
  } else if (contentType === 'product-page') {
    contentStructure = [
      {
        title: `${topic} Overview`,
        purpose: "Introduce the product and its primary benefits",
        keyPoints: [
          "What it is and what it does",
          "Primary value proposition",
          "Who it's designed for"
        ],
        estimatedWordCount: 200,
        keywordTargets: keywords.slice(0, 2),
        contentDirection: "Lead with the most compelling benefit"
      },
      {
        title: `Key Features of ${topic}`,
        purpose: "Highlight the most important capabilities",
        keyPoints: [
          "3-5 standout features",
          "How each feature delivers value",
          "Competitive advantages"
        ],
        estimatedWordCount: 400,
        keywordTargets: keywords.slice(1, 4),
        contentDirection: "Feature-benefit format with visual elements for each feature"
      },
      {
        title: `How ${topic} Works`,
        purpose: "Build trust through transparency",
        keyPoints: [
          "Simple explanation of functionality",
          "Integration with existing systems",
          "Setup and implementation details"
        ],
        estimatedWordCount: 300,
        keywordTargets: keywords.slice(2, 5),
        contentDirection: "Use diagrams or explainer video concepts"
      },
      {
        title: `${topic} Specifications`,
        purpose: "Provide technical details for evaluation",
        keyPoints: [
          "Technical specifications",
          "System requirements",
          "Compatibility information"
        ],
        estimatedWordCount: 200,
        keywordTargets: keywords.slice(3),
        contentDirection: "Organized in easily scannable tables"
      },
      {
        title: `What ${targetAudience} Say About ${topic}`,
        purpose: "Build social proof",
        keyPoints: [
          "Customer testimonials",
          "Case studies",
          "Results and outcomes"
        ],
        estimatedWordCount: 300,
        keywordTargets: keywords.slice(0, 3),
        contentDirection: "Include specific results achieved by customers"
      },
      {
        title: `${topic} Pricing and Options`,
        purpose: "Clear presentation of purchase options",
        keyPoints: [
          "Pricing tiers",
          "Feature comparison across options",
          "Special offers or guarantees"
        ],
        estimatedWordCount: 200,
        keywordTargets: keywords.slice(1, 3),
        contentDirection: "Comparison table with clear call-to-action buttons"
      },
      {
        title: `Frequently Asked Questions About ${topic}`,
        purpose: "Address common concerns",
        keyPoints: [
          "8-10 most common questions",
          "Clear, concise answers",
          "Support options for additional questions"
        ],
        estimatedWordCount: 400,
        keywordTargets: keywords.slice(2, 5),
        contentDirection: "Organize by topic categories for easy navigation"
      }
    ];
  } else if (contentType === 'buying-guide') {
    contentStructure = [
      {
        title: `Introduction: Why ${topic} Matters`,
        purpose: "Establish the importance of making an informed decision",
        keyPoints: [
          "Impact of this purchase decision",
          "Consequences of choosing poorly",
          "What this guide will help readers accomplish"
        ],
        estimatedWordCount: 300,
        keywordTargets: keywords.slice(0, 2),
        contentDirection: "Acknowledge pain points in the decision process"
      },
      {
        title: `${topic} Essentials: What to Look For`,
        purpose: "Educate on key evaluation criteria",
        keyPoints: [
          "Must-have features",
          "Quality indicators",
          "Red flags to watch for"
        ],
        estimatedWordCount: 600,
        keywordTargets: keywords.slice(1, 4),
        contentDirection: "Create a checklist format readers can use while shopping"
      },
      {
        title: `Types of ${topic}: Options Explained`,
        purpose: "Compare different categories or approaches",
        keyPoints: [
          "Major categories or types",
          "Pros and cons of each type",
          "Best use cases for each option"
        ],
        estimatedWordCount: 800,
        keywordTargets: keywords.slice(0, 5),
        contentDirection: "Include comparison table with ratings for different needs"
      },
      {
        title: `Budget Considerations for ${topic}`,
        purpose: "Help set realistic expectations about cost",
        keyPoints: [
          "Price ranges and what they deliver",
          "Long-term costs and considerations",
          "Where to invest vs. where to save"
        ],
        estimatedWordCount: 400,
        keywordTargets: keywords.slice(2, 4),
        contentDirection: "Be transparent about price-quality relationships"
      },
      {
        title: `Top ${topic} Recommendations for ${targetAudience}`,
        purpose: "Provide specific product recommendations",
        keyPoints: [
          "Best overall option",
          "Best budget option",
          "Best premium option",
          "Best for specific needs"
        ],
        estimatedWordCount: 1000,
        keywordTargets: keywords,
        contentDirection: "Include mini-reviews with pros/cons for each recommendation"
      },
      {
        title: `How to Buy ${topic}: Process Guide`,
        purpose: "Walk through the purchase process",
        keyPoints: [
          "Where to buy (online vs. in-store)",
          "When to buy (timing considerations)",
          "Questions to ask sellers",
          "Warranty and return policies"
        ],
        estimatedWordCount: 500,
        keywordTargets: keywords.slice(1, 4),
        contentDirection: "Create a step-by-step timeline or flowchart"
      },
      {
        title: `After the Purchase: Getting the Most from Your ${topic}`,
        purpose: "Extend value beyond the purchase",
        keyPoints: [
          "Setup and initial optimization",
          "Maintenance best practices",
          "Maximizing longevity and performance"
        ],
        estimatedWordCount: 400,
        keywordTargets: keywords.slice(3),
        contentDirection: "Include a maintenance schedule or checklist"
      },
      {
        title: `Conclusion: Making Your Final ${topic} Decision`,
        purpose: "Empower the reader to take action with confidence",
        keyPoints: [
          "Recap of key decision factors",
          "Personalized decision framework",
          "Next steps and resources"
        ],
        estimatedWordCount: 300,
        keywordTargets: keywords.slice(0, 2),
        contentDirection: "Encourage action while acknowledging individual needs"
      }
    ];
  }
  
  // Additional strategic recommendations
  const toneGuidance = `Maintain a ${tone} tone throughout, using language that resonates with ${targetAudience}.`;
  const keywordStrategy = 'Incorporate keywords naturally, with primary keywords in headings and introduction.';
  const mediaRecommendations = 'Include relevant images, diagrams, or infographics to illustrate key concepts.';
  
  // Complete content strategy
  const contentStrategy = {
    contentStructure,
    contentType,
    targetAudience,
    keywords,
    tone,
    toneGuidance,
    keywordStrategy,
    mediaRecommendations,
    totalEstimatedWordCount: contentStructure.reduce((sum, section) => sum + section.estimatedWordCount, 0),
    estimatedReadTime: Math.ceil(contentStructure.reduce((sum, section) => sum + section.estimatedWordCount, 0) / 250) // average reading speed
  };
  
  // Complete reasoning
  const reasoning: Reasoning = {
    thoughts,
    considerations,
    alternatives,
    decision: decisionRationale,
    confidence: 0.9
  };
  
  return { contentStrategy, reasoning };
}

/**
 * Handle enhanced A2A messages with explicit reasoning
 */
async function handleEnhancedMessage(message: EnhancedA2AMessage, state: CollaborationState): Promise<{
  message: EnhancedA2AMessage;
  stateUpdates?: any;
}> {
  console.log("Content Strategy Agent received enhanced message:", JSON.stringify(message));
  
  // Extract topic and context from message parts
  let topic = state.topic;
  let requestType = '';
  let requestContext = '';
  
  // Parse text parts for context
  for (const part of message.parts) {
    if (part.type === 'text') {
      const text = part.text;
      
      // Identify request type
      if (text.includes('develop content structure')) {
        requestType = 'content-structure';
      } else if (text.includes('suggest content strategy')) {
        requestType = 'content-strategy';
      } else if (text.includes('recommend approach')) {
        requestType = 'approach-recommendation';
      }
      
      // Extract additional context
      requestContext += text + ' ';
    }
  }
  
  // If no specific request type identified, default to content structure
  if (!requestType) {
    requestType = 'content-structure';
  }
  
  // Create parts for response
  const responseParts: Part[] = [];
  const stateUpdates: any = {};
  
  // Handle different request types
  if (requestType === 'content-structure') {
    // Generate content strategy with reasoning
    const { contentStrategy, reasoning } = await generateContentStrategyWithReasoning(
      state.topic,
      state.targetAudience,
      state.contentType,
      state.keywords,
      state.tone
    );
    
    // Create text part with main response
    const textPart: TextPart = {
      type: 'text',
      text: `I've developed a comprehensive content structure for "${state.topic}" targeted at ${state.targetAudience}.

The structure includes ${contentStrategy.contentStructure.length} sections with a total estimated word count of ${contentStrategy.totalEstimatedWordCount} words (approximately ${contentStrategy.estimatedReadTime} minutes reading time).

Each section has a clear purpose, key points to cover, and specific keyword targets to ensure SEO optimization.

The structure follows a logical flow that introduces the topic, builds understanding, provides practical application, and concludes with next steps.

I've also included specific content direction for each section to guide the creation process.`
    };
    
    // Create data part with structured content
    const dataPart: DataPart = {
      type: 'data',
      data: contentStrategy
    };
    
    responseParts.push(textPart, dataPart);
    
    // Create artifact for state
    const artifactId = uuidv4();
    const artifact = {
      id: artifactId,
      name: `Content Structure for ${state.topic}`,
      description: `Detailed content structure for ${state.contentType} about ${state.topic}`,
      type: 'content-structure' as ArtifactType,
      parts: [textPart, dataPart],
      metadata: {
        createdAt: new Date().toISOString(),
        createdBy: 'content-strategy'
      },
      index: 0,
      version: 1,
      contributors: ['content-strategy'],
      feedback: [],
      history: [
        {
          version: 1,
          timestamp: new Date().toISOString(),
          agent: 'content-strategy',
          changes: 'Initial creation',
          parts: [textPart, dataPart],
          reasoning
        }
      ],
      status: 'draft',
      qualityScore: 85
    };
    
    // Add to state updates
    stateUpdates.contentStrategy = contentStrategy;
    stateUpdates.artifactIndex = {
      [artifactId]: artifact
    };
  }
  
  // Create response message
  const responseMessage: EnhancedA2AMessage = {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    from: 'content-strategy',
    to: message.from,
    role: 'agent',
    parts: responseParts,
    conversationId: message.conversationId,
    reasoning: {
      thoughts: [
        `The request was for a ${requestType} for "${state.topic}"`,
        `The content is targeted at ${state.targetAudience} with a ${state.tone} tone`,
        `The content type is ${state.contentType} which requires a specific structure`,
        `Keywords need to be naturally incorporated: ${state.keywords.join(', ')}`
      ],
      considerations: [
        'Structure must be comprehensive yet adaptable',
        'Each section needs clear purpose and key points',
        'Content should flow logically from introduction to conclusion',
        'Structure should support both SEO goals and reader engagement'
      ],
      alternatives: [
        'Could have created a simpler structure with fewer sections',
        'Could have focused more on specific subtopics rather than comprehensive coverage',
        'Could have created multiple structure options for comparison'
      ],
      decision: `Created a detailed ${state.contentType} structure with ${stateUpdates.contentStrategy?.contentStructure.length || 'multiple'} sections that balances comprehensiveness with readability`,
      confidence: 0.9
    },
    intentions: ['inform', 'suggest'],
    replyTo: message.id,
    artifactReferences: Object.keys(stateUpdates.artifactIndex || {})
  };
  
  // Add goal progress update
  if (!stateUpdates.goalUpdates) {
    stateUpdates.goalUpdates = {};
  }
  
  // Find the content strategy goal ID
  const contentStrategyGoalId = state.goals.find(g => 
    g.description === 'Develop content strategy and structure' && 
    g.assignedTo.includes('content-strategy')
  )?.id;
  
  if (contentStrategyGoalId && stateUpdates.artifactIndex) {
    // Get the artifact from the artifactIndex
    const artifact = Object.values(stateUpdates.artifactIndex)[0];
    
    // Update the goal progress to 100%
    stateUpdates.goalUpdates[contentStrategyGoalId] = {
      progress: 100,
      artifacts: artifact ? [artifact] : []  
    };
    
    // Create a message to trigger the content generation agent
    const triggerMessage: EnhancedA2AMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'content-strategy',
      to: 'content-generation',
      role: 'agent',
      parts: [
        {
          type: 'text',
          text: `I've completed the content structure for "${state.topic}". Please use this structure to generate the initial content draft.`
        },
        {
          type: 'data',
          data: stateUpdates.contentStrategy
        }
      ],
      conversationId: message.conversationId,
      reasoning: {
        thoughts: [
          'Content structure is complete and ready for content generation',
          'The content generation agent needs this structure to create the draft',
          'Automatic handoff ensures smooth workflow progression'
        ],
        considerations: [
          'Content generation should follow the section structure provided',
          'Each section should incorporate the key points and keywords specified'
        ],
        decision: 'Trigger content generation agent with content structure',
        confidence: 0.95
      },
      intentions: ['inform', 'request'],
      artifactReferences: Object.keys(stateUpdates.artifactIndex || {})
    };
    
    // Add the trigger message to the state updates
    if (!stateUpdates.messages) {
      stateUpdates.messages = [];
    }
    stateUpdates.messages.push(triggerMessage);
  }
  
  return { message: responseMessage, stateUpdates };
}

/**
 * API route handler for enhanced A2A communication
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("Content Strategy Agent received enhanced POST request:", JSON.stringify(body));
    
    // Extract the message and state from request body
    const { message, state } = body;
    
    // Handle the enhanced message
    const response = await handleEnhancedMessage(message, state);
    
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error in Content Strategy Agent enhanced POST handler:", error);
    
    // Create error response
    const errorResponse: EnhancedA2AMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'content-strategy',
      to: 'system',
      role: 'agent',
      parts: [
        {
          type: 'text',
          text: `Error processing message: ${(error as Error).message}`
        }
      ],
      conversationId: uuidv4(),
      reasoning: {
        thoughts: ['An error occurred while processing the message'],
        considerations: ['The error might be due to invalid input or internal processing'],
        decision: 'Return error message with details',
        confidence: 1.0
      },
      intentions: ['inform']
    };
    
    return NextResponse.json(
      { message: errorResponse, error: (error as Error).message },
      { status: 500 }
    );
  }
}

/**
 * API route handler for agent capabilities
 */
export async function GET(request: NextRequest) {
  return NextResponse.json({ 
    agent: "Content Strategy Agent",
    status: "active",
    capabilities: [
      "content-strategy", 
      "content-structure", 
      "audience-targeting"
    ],
    enhancedProtocol: true,
    reasoningEnabled: true,
    artifactCreation: true
  });
}
