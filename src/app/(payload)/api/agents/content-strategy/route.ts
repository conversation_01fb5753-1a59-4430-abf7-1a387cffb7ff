// src/app/(payload)/api/agents/content-strategy/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { A2AMessage } from '../a2atypes';

// Define the Content Strategy state interface
export interface ContentStrategyState {
  topic: string;
  contentStructure?: any[];
  contentRecommendations?: string;
  targetAudience?: string;
  keywords?: string[];
  contentType?: string;
}

// Helper function to generate content strategy information based on a query
async function generateContentStrategyInformation(query: string, state: ContentStrategyState): Promise<any> {
  console.log(`Generating content strategy information for query: ${query}`);
  
  // Extract topic from query if possible
  const topicMatch = query.match(/for "(.*?)"/i);
  const topic = topicMatch ? topicMatch[1] : "the requested topic";
  const targetAudience = state.targetAudience || "the target audience";
  const keywords = state.keywords || [];
  const contentType = state.contentType || "article";
  
  const prompt = `\nYou are a content strategist.\nGiven the topic "${topic}", audience "${targetAudience}", and these keywords: ${keywords && keywords.length > 0 ? keywords.join(', ') : 'N/A'}, build:\n- A detailed outline for a "${contentType}"\n- Section titles and bullet points for each section\n- Suggestions for examples, case studies, or data to include in each section\n- A recommended narrative flow\nBe specific and actionable.\n`;
  
  // Generate content strategy information
  return {
    contentStructure: [
      {
        title: `Introduction to ${topic}`,
        purpose: "Establish relevance and scope",
        keyPoints: ["Definition", "Importance", "Overview"]
      },
      {
        title: `Key Components of ${topic}`,
        purpose: "Provide core information",
        keyPoints: ["Main elements", "Critical factors", "Implementation considerations"]
      },
      {
        title: `Best Practices for ${topic}`,
        purpose: "Deliver actionable insights",
        keyPoints: ["Industry standards", "Expert recommendations", "Common pitfalls"]
      },
      {
        title: `${topic} Case Studies`,
        purpose: "Provide real-world context",
        keyPoints: ["Success stories", "Lessons learned", "Practical applications"]
      },
      {
        title: `Future Trends in ${topic}`,
        purpose: "Establish forward-thinking perspective",
        keyPoints: ["Emerging developments", "Predictions", "Preparation strategies"]
      },
      {
        title: `Conclusion`,
        purpose: "Summarize key points and provide next steps",
        keyPoints: ["Summary", "Action items", "Additional resources"]
      }
    ],
    toneAndStyle: "Professional yet accessible, with a focus on clarity and practical value",
    contentDifferentiators: ["Comprehensive coverage", "Expert insights", "Actionable recommendations"],
    strategyInsights: `This structure ensures comprehensive coverage of ${topic} while maintaining reader engagement through logical progression.`,
    strategyRecommendations: `Include visual elements to break up text and reinforce key concepts about ${topic}.`,
    prompt: prompt
  };
}

// Function to handle agent messages
async function handleAgentMessage(message: A2AMessage): Promise<A2AMessage> {
  console.log("Content Strategy Agent received message:", JSON.stringify(message));
  
  // Process the message based on its task type
  const task = message.task;
  
  if (task.type === "REQUEST_INFORMATION") {
    // Handle information request
    console.log("Handling information request");
    const query = task.query || "content strategy";
    const state: ContentStrategyState = { topic: query };
    const information = await generateContentStrategyInformation(query, state);
    
    // Create response message
    return {
      task: {
        type: "PROVIDE_INFORMATION",
        information
      },
      source: "content-strategy-agent",
      target: message.source
    };
  } else if (task.type === "REQUEST_FEEDBACK") {
    // Handle feedback request
    console.log("Handling feedback request");
    
    // Create response message with feedback
    return {
      task: {
        type: "PROVIDE_FEEDBACK",
        feedback: "The content structure looks good. Consider adding more specific examples to illustrate key points."
      },
      source: "content-strategy-agent",
      target: message.source
    };
  } else if (task.type === "COLLABORATE") {
    // Handle collaboration request
    console.log("Handling collaboration request");
    const query = task.context || "content strategy";
    const state: ContentStrategyState = { topic: query };
    const information = await generateContentStrategyInformation(query, state);
    
    // Create response message with collaboration results
    return {
      task: {
        type: "COLLABORATION_RESULT",
        result: information
      },
      source: "content-strategy-agent",
      target: message.source
    };
  } else {
    // Handle unknown task type
    console.log("Unknown task type:", task.type);
    
    // Create error response message
    return {
      task: {
        type: "ERROR",
        error: `Unsupported task type: ${task.type}`
      },
      source: "content-strategy-agent",
      target: message.source
    };
  }
}

// API route handlers
export async function GET(request: NextRequest) {
  return NextResponse.json({ 
    agent: "Content Strategy Agent", 
    status: "active",
    capabilities: ["content-strategy", "content-structure", "audience-targeting"]
  });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("Content Strategy Agent received POST request:", JSON.stringify(body));
    
    // Extract the message from the request body
    const { message, state } = body;
    console.log("Content Strategy Agent received message:", JSON.stringify(message));
    
    // Adapt the message for the legacy handler
    // The legacy handler expects the message to be directly passed, not nested
    const adaptedMessage = {
      ...message,
      task: message.task || {
        type: "REQUEST_INFORMATION",
        query: typeof message.content === 'object' ? 
          (message.content.query || message.content.context || JSON.stringify(message.content)) : 
          message.content
      }
    };
    
    // Handle the message
    const response = await handleAgentMessage(adaptedMessage);
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error in Content Strategy Agent POST handler:", error);
    return NextResponse.json(
      { error: "Internal server error", details: (error as Error).message },
      { status: 500 }
    );
  }
}
