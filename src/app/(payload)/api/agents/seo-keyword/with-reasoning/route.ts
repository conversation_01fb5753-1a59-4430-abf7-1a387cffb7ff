// src/app/(payload)/api/agents/seo-keyword/with-reasoning/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { 
  EnhancedA2AMessage, 
  Reasoning, 
  Part, 
  TextPart, 
  DataPart,
  CollaborationState,
  ArtifactType
} from '../../a2atypes';

/**
 * Generate keyword analysis with explicit reasoning
 */
async function generateKeywordAnalysis(
  topic: string,
  contentType: string
): Promise<{ keywordAnalysis: any, reasoning: Reasoning }> {
  // Explicitly show reasoning process
  const thoughts = [
    `The topic "${topic}" needs comprehensive keyword research`,
    `${contentType} content requires specific keyword strategies`,
    `Need to identify primary, secondary, and long-tail keywords`,
    `Search intent analysis is crucial for content alignment`
  ];
  
  const considerations = [
    'Keyword difficulty impacts ranking potential',
    'Search volume indicates audience interest level',
    'Query types (informational, commercial, etc.) affect content approach',
    'Keyword competition from established sites requires strategic focus'
  ];
  
  const alternatives = [
    'Could focus on very high volume keywords (too competitive)',
    'Could target only long-tail keywords (might limit reach)',
    'Could ignore keyword difficulty (would hurt ranking potential)'
  ];
  
  // Generate primary keywords based on topic
  const primaryKeywords = [
    topic,
    `best ${topic}`,
    `${topic} guide`,
    `${topic} examples`,
    `how to ${topic.includes(' ') ? topic : 'use ' + topic}`
  ];
  
  // Generate secondary keywords based on content type
  const secondaryKeywords = [
    `${contentType} about ${topic}`,
    `${topic} strategies`,
    `${topic} benefits`,
    `${topic} solutions`,
    `${topic} for beginners`,
    `advanced ${topic}`,
    `${topic} tutorial`
  ];
  
  // Generate long-tail keywords
  const longTailKeywords = [
    `how to implement ${topic} step by step`,
    `what are the benefits of ${topic} for businesses`,
    `top 10 ${topic} strategies for ${new Date().getFullYear()}`,
    `${topic} best practices for beginners`,
    `common mistakes to avoid with ${topic}`,
    `how does ${topic} compare to alternatives`
  ];
  
  // Create search intent categories
  const contentFocus = contentType === 'blog-article' ? 'informational' :
                      contentType === 'product-page' ? 'commercial' :
                      contentType === 'buying-guide' ? 'transactional' : 'mixed';
  
  const searchIntentAnalysis = {
    informational: primaryKeywords.slice(0, 2).concat(secondaryKeywords.slice(1, 3))
                    .concat(longTailKeywords.slice(2, 4)),
    navigational: contentType === 'blog-article' ? primaryKeywords.slice(0, 1) : [],
    commercial: contentType === 'product-page' || contentType === 'buying-guide' ? 
                primaryKeywords.slice(1, 3).concat(secondaryKeywords.slice(0, 2)) : 
                primaryKeywords.slice(1, 2),
    transactional: contentType === 'buying-guide' ? 
                  longTailKeywords.slice(0, 2).concat(secondaryKeywords.slice(3, 5)) : 
                  []
  };
  
  // Calculate keyword metrics (simulated for this implementation)
  const keywordMetrics = {};
  
  // Add metrics for each keyword
  [...primaryKeywords, ...secondaryKeywords, ...longTailKeywords].forEach(keyword => {
    // Simulate metrics based on keyword characteristics
    const isLongTail = keyword.split(' ').length > 3;
    const isPrimary = primaryKeywords.includes(keyword);
    const wordCount = keyword.split(' ').length;
    const containsTopic = keyword.includes(topic);
    
    // Calculate simulated metrics
    const searchVolume = isPrimary ? 
                        Math.floor(Math.random() * 5000) + 1000 : 
                        isLongTail ? 
                        Math.floor(Math.random() * 300) + 50 :
                        Math.floor(Math.random() * 1000) + 300;
                        
    const keywordDifficulty = isPrimary ? 
                             Math.floor(Math.random() * 30) + 70 : 
                             isLongTail ? 
                             Math.floor(Math.random() * 30) + 10 :
                             Math.floor(Math.random() * 40) + 30;
                             
    const cpc = isPrimary ? 
               (Math.random() * 3 + 1).toFixed(2) : 
               isLongTail ? 
               (Math.random() * 0.8 + 0.2).toFixed(2) :
               (Math.random() * 1.5 + 0.5).toFixed(2);
               
    const competition = isPrimary ? 
                       (Math.random() * 0.3 + 0.7).toFixed(2) : 
                       isLongTail ? 
                       (Math.random() * 0.3 + 0.1).toFixed(2) :
                       (Math.random() * 0.4 + 0.3).toFixed(2);
    
    // Add to metrics object
    keywordMetrics[keyword] = {
      searchVolume,
      keywordDifficulty,
      cpc: parseFloat(cpc),
      competition: parseFloat(competition),
      primaryKeyword: isPrimary,
      secondaryKeyword: secondaryKeywords.includes(keyword),
      longTailKeyword: isLongTail
    };
  });
  
  // Create keyword recommendations
  const recommendedKeywords = [];
  const keywordArray = Object.entries(keywordMetrics);
  
  // Sort by search volume and difficulty ratio (higher volume, lower difficulty)
  keywordArray.sort(([keywordA, metricsA], [keywordB, metricsB]) => {
    const ratioA = metricsA.searchVolume / (metricsA.keywordDifficulty || 1);
    const ratioB = metricsB.searchVolume / (metricsB.keywordDifficulty || 1);
    return ratioB - ratioA;
  });
  
  // Take top 10 recommendations
  for (let i = 0; i < Math.min(10, keywordArray.length); i++) {
    recommendedKeywords.push({
      keyword: keywordArray[i][0],
      ...keywordArray[i][1],
      opportunityScore: Math.round(
        (keywordArray[i][1].searchVolume / (keywordArray[i][1].keywordDifficulty || 1)) * 10
      )
    });
  }
  
  // Complete keyword analysis
  const keywordAnalysis = {
    primaryKeywords,
    secondaryKeywords,
    longTailKeywords,
    searchIntentAnalysis,
    keywordMetrics,
    recommendedKeywords,
    topPriorityKeywords: recommendedKeywords.slice(0, 5).map(item => item.keyword),
    contentFocus,
    suggestedKeywordDensity: contentFocus === 'informational' ? '1-2%' : '0.5-1%',
    keywordPlacementRecommendations: [
      'Include primary keywords in title, headings, and first paragraph',
      'Use secondary keywords in subheadings and body content',
      'Place long-tail keywords in paragraphs and FAQ sections',
      'Include keywords naturally in image alt text'
    ]
  };
  
  // Complete reasoning
  const reasoning: Reasoning = {
    thoughts,
    considerations,
    alternatives,
    decision: `A balanced keyword strategy for ${topic} ${contentType} should focus on ${contentFocus} intent keywords with optimal search volume to difficulty ratio.`,
    confidence: 0.87
  };
  
  return { keywordAnalysis, reasoning };
}

/**
 * Handle enhanced A2A messages with explicit reasoning
 */
async function handleEnhancedMessage(message: EnhancedA2AMessage, state: CollaborationState): Promise<{
  message: EnhancedA2AMessage;
  stateUpdates?: any;
}> {
  console.log("SEO Keyword Agent received enhanced message:", JSON.stringify(message));
  
  // Extract request type from message parts
  let requestType = '';
  for (const part of message.parts) {
    if (part.type === 'text') {
      const text = part.text;
      
      if (text.includes('keyword research')) {
        requestType = 'keyword-research';
      } else if (text.includes('search intent')) {
        requestType = 'search-intent';
      } else if (text.includes('keyword optimization')) {
        requestType = 'keyword-optimization';
      }
    }
  }
  
  // Default to keyword research if no specific request type
  if (!requestType) {
    requestType = 'keyword-research';
  }
  
  // Create parts for response
  const responseParts: Part[] = [];
  const stateUpdates: any = {};
  
  // Generate keyword analysis
  const { keywordAnalysis, reasoning } = await generateKeywordAnalysis(
    state.topic,
    state.contentType
  );
  
  // Create text part with main response
  const textPart: TextPart = {
    type: 'text',
    text: `I've completed comprehensive keyword research for "${state.topic}" focused on ${state.contentType} content.

Based on analysis, I recommend prioritizing these top keywords:
${keywordAnalysis.recommendedKeywords.slice(0, 5).map((item, index) => 
  `${index + 1}. "${item.keyword}" (Search Volume: ${item.searchVolume}, Difficulty: ${item.keywordDifficulty}, Opportunity Score: ${item.opportunityScore})`
).join('\n')}

The content should primarily focus on ${keywordAnalysis.contentFocus} search intent, with keyword density around ${keywordAnalysis.suggestedKeywordDensity}.

Key placement recommendations:
${keywordAnalysis.keywordPlacementRecommendations.map(rec => `- ${rec}`).join('\n')}

This strategy balances search volume with competition to maximize organic traffic potential.`
  };
  
  // Create data part with structured analysis
  const dataPart: DataPart = {
    type: 'data',
    data: keywordAnalysis
  };
  
  responseParts.push(textPart, dataPart);
  
  // Create artifact
  const artifactId = uuidv4();
  const artifact = {
    id: artifactId,
    name: `Keyword Analysis for ${state.topic}`,
    description: `Comprehensive keyword research and analysis for ${state.topic} ${state.contentType}`,
    type: 'keyword-set' as ArtifactType,
    parts: [textPart, dataPart],
    metadata: {
      createdAt: new Date().toISOString(),
      createdBy: 'seo-keyword'
    },
    index: 0,
    version: 1,
    contributors: ['seo-keyword'],
    feedback: [],
    history: [
      {
        version: 1,
        timestamp: new Date().toISOString(),
        agent: 'seo-keyword',
        changes: 'Initial creation',
        parts: [textPart, dataPart],
        reasoning
      }
    ],
    status: 'draft',
    qualityScore: 85
  };
  
  // Add to state updates
  stateUpdates.seoKeywords = keywordAnalysis;
  stateUpdates.artifactIndex = {
    [artifactId]: artifact
  };
  
  // Create response message
  const responseMessage: EnhancedA2AMessage = {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    from: 'seo-keyword',
    to: message.from,
    role: 'agent',
    parts: responseParts,
    conversationId: message.conversationId,
    reasoning,
    intentions: ['inform', 'suggest'],
    replyTo: message.id,
    artifactReferences: [artifactId]
  };
  
  // Add goal progress update
  if (!stateUpdates.goalUpdates) {
    stateUpdates.goalUpdates = {};
  }
  
  // Find the SEO keyword goal ID
  const seoKeywordGoalId = state.goals.find(g => 
    g.description === 'Identify SEO keywords and search intent' && 
    g.assignedTo.includes('seo-keyword')
  )?.id;
  
  if (seoKeywordGoalId) {
    // Update the goal progress to 100%
    stateUpdates.goalUpdates[seoKeywordGoalId] = {
      progress: 100,
      artifacts: [artifact]
    };
    
    // Create a message to trigger the content strategy agent
    const triggerMessage: EnhancedA2AMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'seo-keyword',
      to: 'content-strategy',
      role: 'agent',
      parts: [
        {
          type: 'text',
          text: `I've completed the keyword research for "${state.topic}". Please use these keywords to develop a content strategy and structure.`
        },
        {
          type: 'data',
          data: keywordAnalysis
        }
      ],
      conversationId: message.conversationId,
      reasoning: {
        thoughts: [
          'Keyword research is complete and can now inform content strategy',
          'The content strategy agent needs these keywords to create an effective structure',
          'Automatic handoff ensures smooth workflow progression'
        ],
        considerations: [
          'Content strategy should incorporate primary and secondary keywords',
          'Structure should align with identified search intent'
        ],
        decision: 'Trigger content strategy agent with keyword data',
        confidence: 0.95
      },
      intentions: ['inform', 'request'],
      artifactReferences: [artifactId]
    };
    
    // Add the trigger message to the state updates
    if (!stateUpdates.messages) {
      stateUpdates.messages = [];
    }
    stateUpdates.messages.push(triggerMessage);
  }
  
  return { message: responseMessage, stateUpdates };
}

/**
 * API route handler for enhanced A2A communication
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("SEO Keyword Agent received enhanced POST request:", JSON.stringify(body));
    
    // Extract the message and state from request body
    const { message, state } = body;
    
    // Handle the enhanced message
    const response = await handleEnhancedMessage(message, state);
    
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error in SEO Keyword Agent enhanced POST handler:", error);
    
    // Create error response
    const errorResponse: EnhancedA2AMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'seo-keyword',
      to: 'system',
      role: 'agent',
      parts: [
        {
          type: 'text',
          text: `Error processing message: ${(error as Error).message}`
        }
      ],
      conversationId: uuidv4(),
      reasoning: {
        thoughts: ['An error occurred while processing the message'],
        considerations: ['The error might be due to invalid input or internal processing'],
        decision: 'Return error message with details',
        confidence: 1.0
      },
      intentions: ['inform']
    };
    
    return NextResponse.json(
      { message: errorResponse, error: (error as Error).message },
      { status: 500 }
    );
  }
}

/**
 * API route handler for agent capabilities
 */
export async function GET(request: NextRequest) {
  return NextResponse.json({ 
    agent: "SEO Keyword Agent",
    status: "active",
    capabilities: [
      "keyword-research", 
      "search-intent-analysis", 
      "keyword-optimization"
    ],
    enhancedProtocol: true,
    reasoningEnabled: true,
    artifactCreation: true
  });
}
