// src/app/(payload)/api/agents/dynamic-collaboration-v3/state/manager.ts

import { v4 as uuidv4 } from 'uuid';
import { TypedStateStore, StateNotFoundError } from './typed-store';
import logger from '../../../utils/logger';
import {
  CollaborationState,
  CollaborationStateSchema,
  createCollaborationState,
  Goal,
  GoalStatus,
  GoalType,
  Message,
  MessageType,
  WorkflowPhase,
  Artifact,
  ArtifactStatus,
  SessionStatus,
  ContentGenerationParams,
  Reasoning,
  FeedbackRequest,
  FeedbackResponse,
  FeedbackData
} from './unified-schema';

/**
 * State Manager for Collaboration
 */
export class StateManager {
  private sessionId: string;
  private stateStore: TypedStateStore<CollaborationState>;

  /**
   * Constructor
   * @param sessionId Session ID
   */
  constructor(sessionId: string) {
    this.sessionId = sessionId;
    this.stateStore = new TypedStateStore<CollaborationState>(
      'collaboration',
      CollaborationStateSchema
    );
  }

  /**
   * Get the current state
   * @returns The current state or null if not found
   */
  async getState(): Promise<CollaborationState | null> {
    return await this.stateStore.getState(this.sessionId);
  }

  /**
   * Initialize a new session
   * @param params Session parameters
   * @returns Success indicator
   */
  async initializeSession(params: ContentGenerationParams): Promise<boolean> {
    try {
      // Check if session already exists
      const existingState = await this.getState();
      if (existingState) {
        return false;
      }

      // Create initial state
      const initialState = createCollaborationState(this.sessionId, params);

      // Save state
      await this.stateStore.setState(this.sessionId, initialState);

      return true;
    } catch (error) {
      console.error(`Error initializing session ${this.sessionId}:`, error);
      return false;
    }
  }

  /**
   * Update the current phase
   * @param phase New phase
   * @returns Success indicator
   */
  async updatePhase(phase: WorkflowPhase): Promise<boolean> {
    try {
      await this.stateStore.transactionalUpdate(this.sessionId, (state) => {
        const now = new Date().toISOString();

        return {
          ...state,
          currentPhase: phase,
          phaseHistory: [
            ...state.phaseHistory,
            {
              phase,
              timestamp: now
            }
          ],
          lastUpdated: now
        };
      });

      return true;
    } catch (error) {
      console.error(`Error updating phase for session ${this.sessionId}:`, error);
      return false;
    }
  }

  /**
   * Define a new goal
   * @param goal Goal to define
   * @returns Goal ID
   */
  async defineGoal(goal: Omit<Goal, 'id' | 'createdAt' | 'status' | 'progress' | 'version'>): Promise<string> {
    try {
      const goalId = uuidv4();
      const now = new Date().toISOString();

      await this.stateStore.transactionalUpdate(this.sessionId, (state) => {
        const newGoal: Goal = {
          ...goal,
          id: goalId,
          createdAt: now,
          status: GoalStatus.PENDING,
          progress: 0,
          version: 1
        };

        return {
          ...state,
          goals: {
            byId: {
              ...state.goals.byId,
              [goalId]: newGoal
            },
            allIds: [...state.goals.allIds, goalId],
            activeIds: state.goals.activeIds,
            completedIds: state.goals.completedIds
          },
          lastUpdated: now
        };
      });

      return goalId;
    } catch (error) {
      console.error(`Error defining goal for session ${this.sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Define multiple goals
   * @param goals Goals to define
   * @returns Array of goal IDs
   */
  async defineGoals(goals: Array<Omit<Goal, 'id' | 'createdAt' | 'status' | 'progress' | 'version'>>): Promise<string[]> {
    try {
      const goalIds: string[] = [];

      for (const goal of goals) {
        const goalId = await this.defineGoal(goal);
        goalIds.push(goalId);
      }

      return goalIds;
    } catch (error) {
      console.error(`Error defining goals for session ${this.sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Activate a goal
   * @param goalId Goal ID
   * @returns Success indicator
   */
  async activateGoal(goalId: string): Promise<boolean> {
    try {
      await this.stateStore.transactionalUpdate(this.sessionId, (state) => {
        const goal = state.goals.byId[goalId];

        if (!goal) {
          throw new Error(`Goal ${goalId} not found`);
        }

        const now = new Date().toISOString();

        // Update goal
        const updatedGoal: Goal = {
          ...goal,
          status: GoalStatus.ACTIVE,
          startTime: now,
          version: goal.version + 1
        };

        // Add to active goals if not already there
        const activeIds = state.goals.activeIds.includes(goalId)
          ? state.goals.activeIds
          : [...state.goals.activeIds, goalId];

        return {
          ...state,
          goals: {
            ...state.goals,
            byId: {
              ...state.goals.byId,
              [goalId]: updatedGoal
            },
            activeIds
          },
          lastUpdated: now
        };
      });

      return true;
    } catch (error) {
      console.error(`Error activating goal ${goalId} for session ${this.sessionId}:`, error);
      return false;
    }
  }

  /**
   * Assign a goal to an agent
   * @param goalId Goal ID
   * @param agentId Agent ID
   * @returns Success indicator
   */
  async assignGoal(goalId: string, agentId: string): Promise<boolean> {
    try {
      await this.stateStore.transactionalUpdate(this.sessionId, (state) => {
        const goal = state.goals.byId[goalId];

        if (!goal) {
          throw new Error(`Goal ${goalId} not found`);
        }

        const now = new Date().toISOString();

        // Update goal
        const updatedGoal: Goal = {
          ...goal,
          assignedTo: agentId,
          status: GoalStatus.IN_PROGRESS,
          version: goal.version + 1
        };

        return {
          ...state,
          goals: {
            ...state.goals,
            byId: {
              ...state.goals.byId,
              [goalId]: updatedGoal
            }
          },
          lastUpdated: now
        };
      });

      return true;
    } catch (error) {
      console.error(`Error assigning goal ${goalId} to agent ${agentId} for session ${this.sessionId}:`, error);
      return false;
    }
  }

  /**
   * Update goal progress
   * @param goalId Goal ID
   * @param progress Progress (0-100)
   * @returns Success indicator
   */
  async updateGoalProgress(goalId: string, progress: number): Promise<boolean> {
    try {
      await this.stateStore.transactionalUpdate(this.sessionId, (state) => {
        const goal = state.goals.byId[goalId];

        if (!goal) {
          throw new Error(`Goal ${goalId} not found`);
        }

        const now = new Date().toISOString();

        // Update goal
        const updatedGoal: Goal = {
          ...goal,
          progress: Math.max(0, Math.min(100, progress)),
          version: goal.version + 1
        };

        return {
          ...state,
          goals: {
            ...state.goals,
            byId: {
              ...state.goals.byId,
              [goalId]: updatedGoal
            }
          },
          lastUpdated: now
        };
      });

      return true;
    } catch (error) {
      console.error(`Error updating progress for goal ${goalId} in session ${this.sessionId}:`, error);
      return false;
    }
  }

  /**
   * Complete a goal
   * @param goalId Goal ID
   * @param artifactId Optional artifact ID associated with the goal
   * @returns Success indicator
   */
  async completeGoal(goalId: string, artifactId?: string): Promise<boolean> {
    try {
      await this.stateStore.transactionalUpdate(this.sessionId, (state) => {
        const goal = state.goals.byId[goalId];

        if (!goal) {
          throw new Error(`Goal ${goalId} not found`);
        }

        const now = new Date().toISOString();

        // Update goal
        const updatedGoal: Goal = {
          ...goal,
          status: GoalStatus.COMPLETED,
          progress: 100,
          completedAt: now,
          artifactIds: artifactId
            ? [...(goal.artifactIds || []), artifactId]
            : goal.artifactIds,
          version: goal.version + 1
        };

        // Update active and completed goal lists
        const activeIds = state.goals.activeIds.filter(id => id !== goalId);
        const completedIds = state.goals.completedIds.includes(goalId)
          ? state.goals.completedIds
          : [...state.goals.completedIds, goalId];

        return {
          ...state,
          goals: {
            ...state.goals,
            byId: {
              ...state.goals.byId,
              [goalId]: updatedGoal
            },
            activeIds,
            completedIds
          },
          lastUpdated: now
        };
      });

      return true;
    } catch (error) {
      console.error(`Error completing goal ${goalId} in session ${this.sessionId}:`, error);
      return false;
    }
  }

  /**
   * Create a new artifact
   * @param createdBy Creator ID
   * @param title Artifact title
   * @param content Artifact content
   * @param type Artifact type
   * @param goalId Associated goal ID
   * @param previousVersionId Previous version ID (for improved artifacts)
   * @param skipEvaluation Whether to skip automatic evaluation (default: false)
   * @returns Artifact ID
   */
  async createArtifact(
    createdBy: string,
    title: string,
    content: any,
    type: string,
    goalId?: string,
    previousVersionId?: string,
    skipEvaluation: boolean = false
  ): Promise<string> {
    try {
      const artifactId = uuidv4();
      const now = new Date().toISOString();

      await this.stateStore.transactionalUpdate(this.sessionId, (state) => {
        // Ensure we have a valid status
        let status = ArtifactStatus.DRAFT;

        // Validate that the status is a valid enum value
        if (!Object.values(ArtifactStatus).includes(status)) {
          logger.warn(`Invalid artifact status: ${status}, defaulting to DRAFT`, {
            sessionId: this.sessionId,
            artifactId,
            type,
            createdBy
          });
          status = ArtifactStatus.DRAFT;
        }

        const newArtifact: Artifact = {
          id: artifactId,
          type,
          title,
          content,
          createdAt: now,
          updatedAt: now,
          createdBy,
          status, // Use the validated status
          version: 1,
          goalId,
          previousVersionId,
          metadata: {} // Initialize metadata as an empty object
        };

        return {
          ...state,
          artifacts: {
            ...state.artifacts,
            [artifactId]: newArtifact
          },
          generatedArtifactIds: [...state.generatedArtifactIds, artifactId],
          lastUpdated: now
        };
      });

      // If associated with a goal, update the goal
      if (goalId) {
        await this.stateStore.transactionalUpdate(this.sessionId, (state) => {
          const goal = state.goals.byId[goalId];

          if (!goal) {
            return state;
          }

          const updatedGoal: Goal = {
            ...goal,
            artifactIds: [...(goal.artifactIds || []), artifactId],
            version: goal.version + 1
          };

          return {
            ...state,
            goals: {
              ...state.goals,
              byId: {
                ...state.goals.byId,
                [goalId]: updatedGoal
              }
            },
            lastUpdated: now
          };
        });

        // Automatically evaluate the artifact against the goal criteria
        if (!skipEvaluation) {
          try {
            // Use setTimeout to make this non-blocking
            setTimeout(async () => {
              try {
                console.log(`Auto-evaluating artifact ${artifactId} against goal ${goalId}`);
                const evaluation = await this.evaluateArtifact(artifactId, goalId);
                console.log(`Evaluation complete for artifact ${artifactId}:`,
                  evaluation.meetsRequirements ? 'Meets requirements' : 'Needs improvement',
                  `Score: ${evaluation.score}`);
              } catch (evalError) {
                console.error(`Error during automatic artifact evaluation:`, evalError);
              }
            }, 100);
          } catch (evalError) {
            console.error(`Error setting up automatic artifact evaluation:`, evalError);
            // Don't throw here - we still want to return the artifact ID even if evaluation fails
          }
        }
      }

      return artifactId;
    } catch (error) {
      console.error(`Error creating artifact in session ${this.sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Update artifact status
   * @param artifactId Artifact ID
   * @param status New status
   * @returns Success indicator
   */
  async updateArtifactStatus(artifactId: string, status: ArtifactStatus): Promise<boolean> {
    try {
      await this.stateStore.transactionalUpdate(this.sessionId, (state) => {
        const artifact = state.artifacts[artifactId];

        if (!artifact) {
          throw new Error(`Artifact ${artifactId} not found`);
        }

        const now = new Date().toISOString();

        // Update artifact
        const updatedArtifact: Artifact = {
          ...artifact,
          status,
          updatedAt: now,
          version: artifact.version + 1,
          metadata: artifact.metadata || {} // Preserve metadata or initialize if not present
        };

        return {
          ...state,
          artifacts: {
            ...state.artifacts,
            [artifactId]: updatedArtifact
          },
          lastUpdated: now
        };
      });

      return true;
    } catch (error) {
      console.error(`Error updating artifact status in session ${this.sessionId}:`, error);
      return false;
    }
  }

  /**
   * Evaluate an artifact against a goal's criteria
   * @param artifactId Artifact ID
   * @param goalId Goal ID
   * @returns Evaluation result
   */
  async evaluateArtifact(artifactId: string, goalId: string): Promise<any> {
    try {
      // Get current state
      const state = await this.getState();
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      // Get artifact and goal
      const artifact = state.artifacts[artifactId];
      if (!artifact) {
        logger.error(`Artifact ${artifactId} not found during evaluation`, {
          sessionId: this.sessionId,
          goalId,
          artifactId,
          availableArtifacts: Object.keys(state.artifacts || {})
        });

        // Create a placeholder artifact if it doesn't exist
        await this.createArtifact(
          'system',
          'Placeholder Artifact',
          'This is a placeholder artifact created during evaluation',
          'placeholder',
          goalId,
          undefined,
          true // Skip evaluation to avoid infinite recursion
        );

        throw new Error(`Artifact ${artifactId} not found`);
      }

      const goal = state.goals.byId[goalId];
      if (!goal) {
        logger.error(`Goal ${goalId} not found during artifact evaluation`, {
          sessionId: this.sessionId,
          goalId,
          artifactId,
          availableGoals: Object.keys(state.goals?.byId || {})
        });
        throw new Error(`Goal ${goalId} not found`);
      }

      // Import the artifact evaluation service
      const { ArtifactEvaluationService } = await import('../services/artifact-evaluation-service');

      // Evaluate the artifact
      let evaluation;
      try {
        evaluation = await ArtifactEvaluationService.evaluateArtifact(artifact, goal);
      } catch (evalError) {
        logger.error(`Error during artifact evaluation`, {
          sessionId: this.sessionId,
          goalId,
          artifactId,
          error: (evalError as Error).message,
          stack: (evalError as Error).stack
        });

        // Create a default evaluation if the service fails
        evaluation = {
          meetsRequirements: true,
          score: 80,
          feedback: "Default evaluation due to evaluation service error",
          criteriaResults: goal.criteria.map(criterion => ({
            criterion,
            satisfied: true,
            feedback: "Automatically approved due to evaluation error"
          }))
        };
      }

      // Update the artifact with the evaluation
      await this.stateStore.transactionalUpdate(this.sessionId, (currentState) => {
        if (!currentState) return currentState;

        // Check if artifact still exists
        if (!currentState.artifacts[artifactId]) {
          logger.warn(`Artifact ${artifactId} disappeared during evaluation update`, {
            sessionId: this.sessionId,
            goalId,
            artifactId
          });
          return currentState;
        }

        const artifacts = { ...currentState.artifacts };
        const updatedArtifact = { ...artifacts[artifactId] };

        // Ensure artifact has metadata
        if (!updatedArtifact.metadata) {
          updatedArtifact.metadata = {};
        }

        // Add evaluation to metadata
        updatedArtifact.metadata = {
          ...updatedArtifact.metadata,
          evaluation
        };

        // Update artifact status based on evaluation
        // Ensure status is always set to a valid value from ArtifactStatus enum
        if (evaluation.meetsRequirements) {
          updatedArtifact.status = ArtifactStatus.APPROVED;
        } else {
          updatedArtifact.status = ArtifactStatus.REJECTED;
        }

        // Ensure other required fields are present
        if (!updatedArtifact.version) {
          updatedArtifact.version = 1;
        }

        artifacts[artifactId] = updatedArtifact;

        return {
          ...currentState,
          artifacts
        };
      });

      // If the artifact meets the requirements, complete the goal
      if (evaluation.meetsRequirements) {
        await this.completeGoal(goalId, artifactId);
      }

      return evaluation;
    } catch (error) {
      console.error(`Error evaluating artifact ${artifactId} against goal ${goalId} in session ${this.sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Add a message
   * @param message Message to add
   * @returns Message ID
   */
  async addMessage(message: Omit<Message, 'id' | 'timestamp'>): Promise<string> {
    try {
      const messageId = uuidv4();
      const now = new Date().toISOString();

      await this.stateStore.transactionalUpdate(this.sessionId, (state) => {
        const newMessage: Message = {
          ...message,
          id: messageId,
          timestamp: now
        };

        // Update conversation mapping
        const conversationMessages = state.messages.byConversation[message.conversationId] || [];

        return {
          ...state,
          messages: {
            byId: {
              ...state.messages.byId,
              [messageId]: newMessage
            },
            allIds: [...state.messages.allIds, messageId],
            byConversation: {
              ...state.messages.byConversation,
              [message.conversationId]: [...conversationMessages, messageId]
            }
          },
          lastUpdated: now
        };
      });

      return messageId;
    } catch (error) {
      console.error(`Error adding message in session ${this.sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Create a feedback request
   * @param fromAgent Requesting agent
   * @param toAgent Target agent
   * @param artifactId Artifact ID
   * @param specificAreas Specific areas for feedback
   * @returns Request ID
   */
  async createFeedbackRequest(
    fromAgent: string,
    toAgent: string,
    artifactId: string,
    specificAreas: string[] = []
  ): Promise<string> {
    try {
      const requestId = uuidv4();
      const now = new Date().toISOString();

      await this.stateStore.transactionalUpdate(this.sessionId, (state) => {
        const newRequest: FeedbackRequest = {
          id: requestId,
          artifactId,
          fromAgent,
          toAgent,
          timestamp: now,
          specificAreas,
          status: 'pending'
        };

        return {
          ...state,
          feedbackRequests: {
            ...state.feedbackRequests,
            [requestId]: newRequest
          },
          lastUpdated: now
        };
      });

      return requestId;
    } catch (error) {
      console.error(`Error creating feedback request in session ${this.sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Provide feedback response
   * @param requestId Request ID
   * @param fromAgent Responding agent
   * @param toAgent Target agent
   * @param feedback Feedback data
   * @returns Response ID
   */
  async provideFeedbackResponse(
    requestId: string,
    fromAgent: string,
    toAgent: string,
    feedback: FeedbackData
  ): Promise<string> {
    try {
      const responseId = uuidv4();
      const now = new Date().toISOString();

      await this.stateStore.transactionalUpdate(this.sessionId, (state) => {
        const request = state.feedbackRequests[requestId];

        if (!request) {
          throw new Error(`Feedback request ${requestId} not found`);
        }

        const newResponse: FeedbackResponse = {
          id: responseId,
          requestId,
          artifactId: request.artifactId,
          fromAgent,
          toAgent,
          timestamp: now,
          feedback
        };

        // Update request status
        const updatedRequest: FeedbackRequest = {
          ...request,
          status: 'completed'
        };

        return {
          ...state,
          feedbackRequests: {
            ...state.feedbackRequests,
            [requestId]: updatedRequest
          },
          feedbackResponses: {
            ...state.feedbackResponses,
            [responseId]: newResponse
          },
          lastUpdated: now
        };
      });

      return responseId;
    } catch (error) {
      console.error(`Error providing feedback response in session ${this.sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Update keywords
   * @param keywords New keywords
   * @returns Success indicator
   */
  async updateKeywords(keywords: string[]): Promise<boolean> {
    try {
      await this.stateStore.transactionalUpdate(this.sessionId, (state) => {
        const now = new Date().toISOString();

        return {
          ...state,
          keywords,
          lastUpdated: now
        };
      });

      return true;
    } catch (error) {
      console.error(`Error updating keywords in session ${this.sessionId}:`, error);
      return false;
    }
  }

  /**
   * Update state using a function
   * @param updateFn Function to update the state
   * @returns Success indicator
   */
  async updateState(updateFn: (state: CollaborationState | null) => CollaborationState | null): Promise<boolean> {
    try {
      await this.stateStore.updateState(this.sessionId, updateFn);
      return true;
    } catch (error) {
      console.error(`Error updating state for session ${this.sessionId}:`, error);
      return false;
    }
  }

  /**
   * Complete the session
   * @returns Success indicator
   */
  async completeSession(): Promise<boolean> {
    try {
      await this.stateStore.transactionalUpdate(this.sessionId, (state) => {
        const now = new Date().toISOString();

        return {
          ...state,
          status: SessionStatus.COMPLETED,
          endTime: now,
          lastUpdated: now
        };
      });

      return true;
    } catch (error) {
      console.error(`Error completing session ${this.sessionId}:`, error);
      return false;
    }
  }
}