/**
 * State Sanitizer
 * 
 * This module provides functions to sanitize state objects before validation.
 * It ensures that all required fields are present and have valid values.
 */

import logger from '../../../utils/logger';
import { ArtifactStatus, CollaborationState } from './unified-schema';

/**
 * Sanitize the state object to ensure all required fields are present
 * @param state The state object to sanitize
 * @returns The sanitized state object
 */
export function sanitizeState(state: CollaborationState): CollaborationState {
  if (!state) return state;

  try {
    // Create a deep copy of the state to avoid modifying the original
    const sanitizedState = JSON.parse(JSON.stringify(state));

    // Sanitize artifacts
    if (sanitizedState.artifacts) {
      Object.keys(sanitizedState.artifacts).forEach(artifactId => {
        const artifact = sanitizedState.artifacts[artifactId];
        if (artifact) {
          // Ensure artifact has a valid status
          if (!artifact.status || !Object.values(ArtifactStatus).includes(artifact.status as ArtifactStatus)) {
            logger.warn(`Artifact ${artifactId} has invalid status: ${artifact.status}, setting to DRAFT`, {
              sessionId: sanitizedState.id,
              artifactId
            });
            artifact.status = ArtifactStatus.DRAFT;
          }

          // Ensure artifact has metadata
          if (!artifact.metadata) {
            artifact.metadata = {};
          }

          // Ensure artifact has version
          if (!artifact.version) {
            artifact.version = 1;
          }
        }
      });
    }

    // Sanitize feedback requests
    if (sanitizedState.feedbackRequests) {
      Object.keys(sanitizedState.feedbackRequests).forEach(requestId => {
        const request = sanitizedState.feedbackRequests[requestId];
        if (request) {
          // Ensure request has all required fields
          if (!request.id) {
            request.id = requestId;
          }
          if (!request.artifactId) {
            // Try to find an artifact ID from the state
            const artifactIds = Object.keys(sanitizedState.artifacts || {});
            request.artifactId = artifactIds.length > 0 ? artifactIds[0] : requestId;
            logger.warn(`Feedback request ${requestId} missing artifactId, using ${request.artifactId}`, {
              sessionId: sanitizedState.id,
              requestId
            });
          }
          if (!request.fromAgent) {
            request.fromAgent = 'system';
            logger.warn(`Feedback request ${requestId} missing fromAgent, using 'system'`, {
              sessionId: sanitizedState.id,
              requestId
            });
          }
          if (!request.toAgent) {
            request.toAgent = 'system';
            logger.warn(`Feedback request ${requestId} missing toAgent, using 'system'`, {
              sessionId: sanitizedState.id,
              requestId
            });
          }
          if (!request.timestamp) {
            request.timestamp = new Date().toISOString();
            logger.warn(`Feedback request ${requestId} missing timestamp, using current time`, {
              sessionId: sanitizedState.id,
              requestId
            });
          }
          if (!request.specificAreas) {
            request.specificAreas = [];
            logger.warn(`Feedback request ${requestId} missing specificAreas, using empty array`, {
              sessionId: sanitizedState.id,
              requestId
            });
          }
          if (!request.status) {
            request.status = 'pending';
            logger.warn(`Feedback request ${requestId} missing status, using 'pending'`, {
              sessionId: sanitizedState.id,
              requestId
            });
          }
        }
      });
    }

    // Sanitize feedback responses
    if (sanitizedState.feedbackResponses) {
      Object.keys(sanitizedState.feedbackResponses).forEach(responseId => {
        const response = sanitizedState.feedbackResponses[responseId];
        if (response) {
          // Ensure response has all required fields
          if (!response.id) {
            response.id = responseId;
          }
          if (!response.requestId) {
            // Try to find a request ID from the state
            const requestIds = Object.keys(sanitizedState.feedbackRequests || {});
            response.requestId = requestIds.length > 0 ? requestIds[0] : responseId;
            logger.warn(`Feedback response ${responseId} missing requestId, using ${response.requestId}`, {
              sessionId: sanitizedState.id,
              responseId
            });
          }
          if (!response.artifactId) {
            // Try to find an artifact ID from the state
            const artifactIds = Object.keys(sanitizedState.artifacts || {});
            response.artifactId = artifactIds.length > 0 ? artifactIds[0] : responseId;
            logger.warn(`Feedback response ${responseId} missing artifactId, using ${response.artifactId}`, {
              sessionId: sanitizedState.id,
              responseId
            });
          }
          if (!response.fromAgent) {
            response.fromAgent = 'system';
            logger.warn(`Feedback response ${responseId} missing fromAgent, using 'system'`, {
              sessionId: sanitizedState.id,
              responseId
            });
          }
          if (!response.toAgent) {
            response.toAgent = 'system';
            logger.warn(`Feedback response ${responseId} missing toAgent, using 'system'`, {
              sessionId: sanitizedState.id,
              responseId
            });
          }
          if (!response.timestamp) {
            response.timestamp = new Date().toISOString();
            logger.warn(`Feedback response ${responseId} missing timestamp, using current time`, {
              sessionId: sanitizedState.id,
              responseId
            });
          }
          if (!response.feedback) {
            response.feedback = {
              overallRating: 70,
              strengths: ['Default strength'],
              areasForImprovement: ['Default area for improvement'],
              specificFeedback: [
                {
                  section: 'Overall',
                  feedback: 'Default feedback',
                  suggestions: 'Default suggestions'
                }
              ],
              summary: 'Default summary'
            };
            logger.warn(`Feedback response ${responseId} missing feedback, using default`, {
              sessionId: sanitizedState.id,
              responseId
            });
          }
        }
      });
    }

    return sanitizedState;
  } catch (error) {
    logger.error(`Error sanitizing state`, {
      error: (error as Error).message,
      stack: (error as Error).stack
    });
    return state;
  }
}
