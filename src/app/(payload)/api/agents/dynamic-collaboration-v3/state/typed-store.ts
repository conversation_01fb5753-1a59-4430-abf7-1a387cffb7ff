 // src/app/(payload)/api/agents/dynamic-collaboration-v3/state/typed-store.ts

import { Redis } from '@upstash/redis';
import { z } from 'zod';
import { sanitizeState } from './sanitizer';
import logger from '../../../utils/logger';

/**
 * Error thrown when state is not found
 */
export class StateNotFoundError extends Error {
  constructor(id: string) {
    super(`State not found for ID: ${id}`);
    this.name = 'StateNotFoundError';
  }
}

/**
 * Error thrown when there's a concurrency issue
 */
export class ConcurrencyError extends Error {
  constructor(id: string) {
    super(`Concurrency error for ID: ${id}`);
    this.name = 'ConcurrencyError';
  }
}

/**
 * Generic state store with type parameters
 */
export class TypedStateStore<T> {
  private redis: Redis;
  private prefix: string;
  private validator?: z.ZodType<T>;

  /**
   * Constructor
   * @param prefix Prefix for Redis keys
   * @param validator Optional Zod validator for type T
   */
  constructor(prefix: string, validator?: z.ZodType<T>) {
    this.redis = new Redis({
      url: process.env.UPSTASH_REDIS_REST_URL || '',
      token: process.env.UPSTASH_REDIS_REST_TOKEN || '',
      automaticDeserialization: true
    });
    this.prefix = prefix;
    this.validator = validator;

    // Test Redis connection
    this.testConnection();
  }

  /**
   * Test Redis connection
   */
  private async testConnection(): Promise<void> {
    try {
      const result = await this.redis.ping();
      console.log(`Redis connection test: ${result}`);
    } catch (error) {
      console.error('Failed to connect to Redis:', error);
    }
  }

  /**
   * Get the full Redis key for a session
   * @param id Session ID
   * @returns Full Redis key
   */
  private getKey(id: string): string {
    return `${this.prefix}:${id}`;
  }

  /**
   * Get state for a session
   * @param id Session ID
   * @returns State or null if not found
   */
  async getState(id: string): Promise<T | null> {
    try {
      const data = await this.redis.get(this.getKey(id));

      if (!data) {
        return null;
      }

      // Parse and validate
      let parsed: T;

      // Handle the case when data is already an object
      if (typeof data === 'object' && data !== null) {
        parsed = data as T;
      } else {
        // Parse string data
        try {
          parsed = JSON.parse(data as string) as T;
        } catch (parseError) {
          console.error(`Error parsing JSON for ${id}:`, parseError);
          throw new Error(`Invalid JSON: ${(parseError as Error).message}`);
        }
      }

      if (this.validator) {
        return this.validator.parse(parsed);
      }

      return parsed;
    } catch (error) {
      console.error(`Error getting state for ${id}:`, error);
      throw new Error(`Failed to get state: ${(error as Error).message}`);
    }
  }

  /**
   * Set state for a session
   * @param id Session ID
   * @param state State to set
   */
  async setState(id: string, state: T): Promise<void> {
    try {
      // Sanitize state before validation
      try {
        // Only sanitize if it's a CollaborationState
        if (state && typeof state === 'object' && 'artifacts' in state && 'feedbackRequests' in state) {
          state = sanitizeState(state as any) as T;
          logger.debug(`State sanitized for ${id}`);
        }
      } catch (sanitizeError) {
        logger.warn(`Error sanitizing state for ${id}:`, {
          error: (sanitizeError as Error).message,
          stack: (sanitizeError as Error).stack
        });
        // Continue with original state if sanitization fails
      }

      // Validate state if validator is provided
      if (this.validator) {
        try {
          state = this.validator.parse(state);
        } catch (validationError) {
          logger.error(`Validation error for ${id}:`, {
            error: (validationError as Error).message,
            state: JSON.stringify(state).substring(0, 200) + '...'
          });
          throw validationError;
        }
      }

      // Ensure proper serialization
      let serializedState: string;
      try {
        serializedState = JSON.stringify(state);
      } catch (serializeError) {
        console.error(`Error serializing state for ${id}:`, serializeError);
        throw new Error(`Failed to serialize state: ${(serializeError as Error).message}`);
      }

      await this.redis.set(this.getKey(id), serializedState);
    } catch (error) {
      console.error(`Error setting state for ${id}:`, error);
      throw new Error(`Failed to set state: ${(error as Error).message}`);
    }
  }

  /**
   * Update state for a session using a function
   * @param id Session ID
   * @param updateFn Function to update the state
   */
  async updateState(id: string, updateFn: (state: T | null) => T | null): Promise<void> {
    try {
      // Get current state
      const currentState = await this.getState(id);

      // Apply update function
      const newState = updateFn(currentState);

      // If null is returned, do nothing
      if (newState === null) {
        return;
      }

      // Validate and set new state
      await this.setState(id, newState);
    } catch (error) {
      console.error(`Error updating state for ${id}:`, error);
      throw new Error(`Failed to update state: ${(error as Error).message}`);
    }
  }

  /**
   * Transactional update with optimistic locking
   * @param id Session ID
   * @param updateFn Function to update the state
   * @param maxRetries Maximum number of retries
   */
  async transactionalUpdate(
    id: string,
    updateFn: (state: T) => T,
    maxRetries = 5
  ): Promise<void> {
    let retries = 0;
    let lastError: Error | null = null;

    while (retries < maxRetries) {
      try {
        // Get current state with version
        const currentState = await this.getState(id);

        if (!currentState) {
          throw new StateNotFoundError(id);
        }

        // Apply update function
        let newState: T;
        try {
          newState = updateFn(currentState);
        } catch (updateError) {
          logger.error(`Error in update function for ${id}:`, {
            error: (updateError as Error).message,
            stack: (updateError as Error).stack,
            retries
          });
          throw updateError;
        }

        // Increment version
        // Handle versioning in a type-safe way
        const stateWithVersion = newState as any;
        if (stateWithVersion && typeof stateWithVersion.version === 'number') {
          (newState as any).version += 1;
          (newState as any).lastUpdated = new Date().toISOString();
        }

        // Set new state with version check
        await this.setState(id, newState);

        // Success
        logger.debug(`Transactional update successful for ${id} after ${retries} retries`);
        return;
      } catch (error) {
        lastError = error as Error;
        retries++;

        // Log the error but don't throw yet
        logger.warn(`Transactional update retry ${retries}/${maxRetries} for ${id}:`, {
          error: (error as Error).message,
          type: (error as Error).name
        });

        if (retries >= maxRetries) {
          logger.error(`Transactional update failed after ${maxRetries} retries for ${id}:`, {
            error: (error as Error).message,
            stack: (error as Error).stack
          });
          throw new ConcurrencyError(id);
        }

        // Exponential backoff with jitter
        const baseDelay = 100 * Math.pow(2, retries - 1);
        const jitter = Math.random() * 100;
        const delay = baseDelay + jitter;

        logger.debug(`Waiting ${delay}ms before retry ${retries} for ${id}`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // This should never happen, but just in case
    throw lastError || new ConcurrencyError(id);
  }

  /**
   * Delete state for a session
   * @param id Session ID
   */
  async deleteState(id: string): Promise<void> {
    try {
      await this.redis.del(this.getKey(id));
    } catch (error) {
      console.error(`Error deleting state for ${id}:`, error);
      throw new Error(`Failed to delete state: ${(error as Error).message}`);
    }
  }

  /**
   * List all session IDs
   * @returns Array of session IDs
   */
  async listSessions(): Promise<string[]> {
    try {
      const keys = await this.redis.keys(`${this.prefix}:*`);
      return keys.map(key => key.replace(`${this.prefix}:`, ''));
    } catch (error) {
      console.error('Error listing sessions:', error);
      throw new Error(`Failed to list sessions: ${(error as Error).message}`);
    }
  }
}