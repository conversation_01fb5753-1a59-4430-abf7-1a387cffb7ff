/**
 * Feedback Loop System
 *
 * This class enables agents to evaluate each other's work,
 * request revisions, and track feedback cycles.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import { StateManager } from '../state/manager';
import {
  MessageType,
  FeedbackData,
  FeedbackCycle,
  FeedbackRequest,
  FeedbackResponse,
  CollaborationState,
  Message
} from '../state/unified-schema';
import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export class FeedbackLoopSystem {
  private sessionId: string;
  private stateManager: StateManager;

  constructor(sessionId: string) {
    this.sessionId = sessionId;
    this.stateManager = new StateManager(sessionId);
  }

  /**
   * Request feedback on an artifact
   * @param fromAgent The agent requesting feedback
   * @param toAgent The agent to provide feedback
   * @param artifactId The artifact to get feedback on
   * @param specificAreas Specific areas to focus feedback on
   * @returns The ID of the feedback request message
   */
  public async requestFeedback(
    fromAgent: string,
    toAgent: string,
    artifactId: string,
    specificAreas?: string[]
  ): Promise<string> {
    try {
      // Get current state
      const state = await transactionalStateStore.getState(this.sessionId);
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      // Get the artifact
      const artifact = state.artifacts[artifactId];
      if (!artifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }

      // Create feedback request
      const requestId = uuidv4();
      const request: FeedbackRequest = {
        id: requestId,
        artifactId,
        fromAgent,
        toAgent,
        timestamp: new Date().toISOString(),
        specificAreas: specificAreas || [],
        status: 'pending'
      };

      // Create feedback request message
      const messageId = uuidv4();
      const conversationId = uuidv4();

      // Update state with the request and message
      await transactionalStateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;

        // Add feedback request
        const feedbackRequests = { ...currentState.feedbackRequests };
        feedbackRequests[requestId] = request;

        // Add message
        const messages = { ...currentState.messages };
        messages[messageId] = {
          id: messageId,
          timestamp: new Date().toISOString(),
          from: fromAgent,
          to: toAgent,
          type: MessageType.FEEDBACK_REQUEST,
          conversationId,
          content: {
            requestId,
            artifactId,
            artifactType: artifact.type,
            specificAreas: specificAreas || []
          },
          reasoning: {
            thoughts: [
              `Need feedback on ${artifact.type} artifact`,
              `${toAgent} has expertise relevant to this artifact`
            ],
            considerations: [
              'Feedback will improve artifact quality',
              'Specific areas of focus will get targeted feedback'
            ],
            decision: `Request feedback from ${toAgent} on ${artifact.type} artifact`,
            confidence: 0.9
          }
        };

        return {
          ...currentState,
          feedbackRequests,
          messages
        };
      });

      logger.info(`Feedback requested for artifact ${artifactId}`, {
        sessionId: this.sessionId,
        artifactId,
        fromAgent,
        toAgent,
        requestId
      });

      return requestId;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error requesting feedback`, {
        sessionId: this.sessionId,
        artifactId,
        fromAgent,
        toAgent,
        error: err.message || String(error),
        stack: err.stack
      });
      throw error;
    }
  }

  /**
   * Provide feedback on an artifact
   * @param fromAgent The agent providing feedback
   * @param toAgent The agent who requested feedback
   * @param requestId The ID of the feedback request
   * @param feedback The feedback data
   * @returns The ID of the feedback response
   */
  public async provideFeedback(
    fromAgent: string,
    toAgent: string,
    requestId: string,
    feedback: FeedbackData
  ): Promise<string> {
    try {
      // Get current state
      const state = await transactionalStateStore.getState(this.sessionId);
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      // Get the feedback request
      const request = state.feedbackRequests[requestId];
      if (!request) {
        throw new Error(`Feedback request ${requestId} not found`);
      }

      // Get the artifact
      const artifactId = request.artifactId;
      const artifact = state.artifacts[artifactId];
      if (!artifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }

      // Create feedback response
      const responseId = uuidv4();
      const response: FeedbackResponse = {
        id: responseId,
        requestId,
        artifactId,
        fromAgent,
        toAgent,
        timestamp: new Date().toISOString(),
        feedback
      };

      // Create feedback message
      const messageId = uuidv4();
      const conversationId = uuidv4();

      // Update state with the response and message
      await transactionalStateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;

        // Add feedback response
        const feedbackResponses = { ...currentState.feedbackResponses };
        feedbackResponses[responseId] = response;

        // Update feedback request status
        const feedbackRequests = { ...currentState.feedbackRequests };
        feedbackRequests[requestId] = {
          ...feedbackRequests[requestId],
          status: 'completed'
        };

        // Add message
        const messages = { ...currentState.messages };
        messages[messageId] = {
          id: messageId,
          timestamp: new Date().toISOString(),
          from: fromAgent,
          to: toAgent,
          type: MessageType.FEEDBACK_RESPONSE,
          conversationId,
          content: {
            responseId,
            requestId,
            artifactId,
            feedback
          },
          reasoning: {
            thoughts: [
              `Analyzed ${artifact.type} artifact`,
              `Identified strengths and areas for improvement`
            ],
            considerations: [
              'Provided actionable feedback',
              'Balanced critique with positive reinforcement'
            ],
            decision: `Provide detailed feedback on ${artifact.type} artifact`,
            confidence: 0.85
          }
        };

        return {
          ...currentState,
          feedbackResponses,
          feedbackRequests,
          messages
        };
      });

      logger.info(`Feedback provided for artifact ${artifactId}`, {
        sessionId: this.sessionId,
        artifactId,
        fromAgent,
        toAgent,
        requestId,
        responseId
      });

      return responseId;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error providing feedback`, {
        sessionId: this.sessionId,
        requestId,
        fromAgent,
        toAgent,
        error: err.message || String(error),
        stack: err.stack
      });
      throw error;
    }
  }

  /**
   * Generate feedback on an artifact using AI
   * @param artifactId The artifact to get feedback on
   * @param fromAgent The agent providing feedback
   * @param specificAreas Specific areas to focus feedback on
   * @returns The generated feedback data
   */
  public async generateFeedback(
    artifactId: string,
    fromAgent: string,
    specificAreas?: string[]
  ): Promise<FeedbackData> {
    try {
      // Get current state
      const state = await transactionalStateStore.getState(this.sessionId);
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      // Get the artifact
      const artifact = state.artifacts[artifactId];
      if (!artifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }

      // Extract content from artifact
      const content = typeof artifact.content === 'string'
        ? artifact.content
        : JSON.stringify(artifact.content);

      // Generate feedback using OpenAI
      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content: `You are an expert ${fromAgent} agent providing feedback on a ${artifact.type} artifact. Provide detailed, constructive feedback.`
          },
          {
            role: "user",
            content: `
              Review the following ${artifact.type} artifact and provide detailed feedback:

              ${content.substring(0, 6000)}

              ${specificAreas && specificAreas.length > 0
                ? `Focus on these specific areas: ${specificAreas.join(', ')}`
                : 'Provide comprehensive feedback on all aspects of the artifact.'}

              Format your response as JSON with the following structure:
              {
                "overallRating": 0-100,
                "strengths": ["strength1", "strength2", ...],
                "areasForImprovement": ["area1", "area2", ...],
                "specificFeedback": [
                  {
                    "section": "section name or description",
                    "feedback": "detailed feedback",
                    "suggestions": "specific suggestions for improvement"
                  },
                  ...
                ],
                "summary": "overall summary of feedback"
              }
            `
          }
        ],
        temperature: 0.7,
        response_format: { type: "json_object" }
      });

      // Parse the response
      const feedbackContent = response.choices[0].message.content || '';
      const feedbackData = JSON.parse(feedbackContent) as FeedbackData;

      return feedbackData;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error generating feedback with OpenAI`, {
        sessionId: this.sessionId,
        artifactId,
        fromAgent,
        error: err.message || String(error),
        stack: err.stack
      });

      // Return a fallback response in case of error
      return {
        overallRating: 70,
        strengths: ['Well-structured content', 'Covers key points'],
        areasForImprovement: ['Could be more detailed', 'Consider adding examples'],
        specificFeedback: [
          {
            section: 'Overall',
            feedback: 'The content is generally good but could be improved.',
            suggestions: 'Add more specific examples and details.'
          }
        ],
        summary: 'Good start but needs refinement in some areas.'
      };
    }
  }

  /**
   * Incorporate feedback into an artifact
   * @param artifactId The artifact to update
   * @param responseIds The IDs of the feedback responses to incorporate
   * @param agentId The agent incorporating the feedback
   * @returns The ID of the updated artifact
   */
  public async incorporateFeedback(
    artifactId: string,
    responseIds: string[],
    agentId: string
  ): Promise<string> {
    try {
      // Get current state
      const state = await transactionalStateStore.getState(this.sessionId);
      if (!state) {
        throw new Error(`Session ${this.sessionId} not found`);
      }

      // Get the artifact
      const artifact = state.artifacts[artifactId];
      if (!artifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }

      // Get the feedback responses
      const responses = responseIds
        .map(id => state.feedbackResponses[id])
        .filter(Boolean);

      if (responses.length === 0) {
        throw new Error(`No valid feedback responses found`);
      }

      // Extract content from artifact
      const content = typeof artifact.content === 'string'
        ? artifact.content
        : JSON.stringify(artifact.content);

      // Extract feedback from responses
      const feedbackItems = responses.map(response => response.feedback);

      // Use OpenAI to incorporate feedback
      const improvedContent = await this.improveContentWithFeedback(
        content,
        feedbackItems,
        artifact.type,
        state
      );

      // Create updated artifact
      const updatedArtifactId = uuidv4();

      // Update state with the new artifact
      await transactionalStateStore.updateState(this.sessionId, (currentState) => {
        if (!currentState) return currentState;

        // Add updated artifact
        const artifacts = { ...currentState.artifacts };
        artifacts[updatedArtifactId] = {
          ...artifact,
          id: updatedArtifactId,
          content: improvedContent,
          updatedAt: new Date().toISOString(),
          createdBy: agentId, // Use createdBy instead of updatedBy
          previousVersionId: artifactId
        };

        // Add message about feedback incorporation
        const messageId = uuidv4();
        const conversationId = uuidv4();
        const messages = { ...currentState.messages };
        messages[messageId] = {
          id: messageId,
          timestamp: new Date().toISOString(),
          from: agentId,
          to: 'all',
          type: MessageType.SYSTEM_MESSAGE,
          conversationId,
          content: {
            event: 'FEEDBACK_INCORPORATED',
            originalArtifactId: artifactId,
            updatedArtifactId: updatedArtifactId,
            feedbackResponseIds: responseIds
          },
          reasoning: {
            thoughts: [
              'Feedback has been reviewed and incorporated',
              'Changes improve the artifact quality'
            ],
            considerations: [
              'Prioritized most important feedback points',
              'Balanced different feedback suggestions'
            ],
            decision: 'Incorporate feedback to improve artifact quality',
            confidence: 0.9
          }
        };

        return {
          ...currentState,
          artifacts,
          messages
        };
      });

      logger.info(`Feedback incorporated for artifact ${artifactId}`, {
        sessionId: this.sessionId,
        originalArtifactId: artifactId,
        updatedArtifactId: updatedArtifactId,
        agentId,
        feedbackCount: responseIds.length
      });

      return updatedArtifactId;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error incorporating feedback`, {
        sessionId: this.sessionId,
        artifactId,
        agentId,
        responseIds,
        error: err.message || String(error),
        stack: err.stack
      });
      throw error;
    }
  }

  /**
   * Improve content with feedback using OpenAI
   * @param content The original content
   * @param feedbackItems The feedback items
   * @param artifactType The type of artifact
   * @param state The current state
   * @returns The improved content
   */
  private async improveContentWithFeedback(
    content: string,
    feedbackItems: FeedbackData[],
    artifactType: string,
    state: Partial<CollaborationState>
  ): Promise<string> {
    try {
      // Format feedback for the prompt
      const formattedFeedback = feedbackItems.map((feedback, index) => {
        return `Feedback ${index + 1}:
- Overall Rating: ${feedback.overallRating}/100
- Strengths: ${feedback.strengths.join(', ')}
- Areas for Improvement: ${feedback.areasForImprovement.join(', ')}
- Summary: ${feedback.summary}
- Specific Feedback:
${feedback.specificFeedback.map(item => `  * ${item.section}: ${item.feedback} - Suggestion: ${item.suggestions}`).join('\n')}`;
      }).join('\n\n');

      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content: `You are an expert content creator improving a ${artifactType} based on feedback. Incorporate the feedback to create an improved version.`
          },
          {
            role: "user",
            content: `
              Improve the following ${artifactType} by incorporating the feedback provided:

              Original Content:
              ${content.substring(0, 6000)}

              Feedback to Incorporate:
              ${formattedFeedback}

              Topic: ${state.topic}
              Content Type: ${state.contentType}
              Target Audience: ${state.targetAudience}
              Tone: ${state.tone}

              Create an improved version that addresses the feedback while maintaining the original purpose and structure.
              Return only the improved content.
            `
          }
        ],
        temperature: 0.7,
        max_tokens: 4000
      });

      return response.choices[0].message.content || content;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error improving content with OpenAI`, {
        error: err.message || String(error),
        artifactType,
        feedbackCount: feedbackItems.length
      });

      // Return the original content in case of error
      return content;
    }
  }
}
