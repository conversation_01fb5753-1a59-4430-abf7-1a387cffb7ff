/**
 * Content Generation Utility
 *
 * This file provides utility functions for generating content
 * for the dynamic collaboration system.
 */

import OpenAI from 'openai';
import logger from '../../../utils/logger';
import { Artifact } from '../state/unified-schema';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * Get content strategy prompt based on content type
 */
function getContentStrategyPrompt(
  contentType: string,
  topic: string,
  targetAudience: string,
  tone: string,
  marketResearch: string,
  keywordAnalysis: string
): string {
  const basePrompt = `
    Use the following market research:
    ${marketResearch.substring(0, 2000)}

    And the following keyword analysis:
    ${keywordAnalysis.substring(0, 2000)}

    Format the strategy in markdown with clear headings and bullet points where appropriate.
  `;

  switch (contentType) {
    case 'how-to-guide':
      return `
        Create a comprehensive content strategy for a how-to guide about "${topic}" targeting ${targetAudience} with a ${tone} tone.
        ${basePrompt}

        Include the following sections specific to how-to guides:
        1. Learning Objectives and Outcomes
        2. Target Audience Skill Level Assessment
        3. Step-by-Step Structure Planning
        4. Prerequisites and Required Materials
        5. Visual Aids and Examples Strategy
        6. Troubleshooting and FAQ Sections
        7. Success Metrics and User Feedback

        Focus on creating clear, actionable content that helps users achieve specific goals.
      `;

    case 'listicle':
      return `
        Create a comprehensive content strategy for a listicle about "${topic}" targeting ${targetAudience} with a ${tone} tone.
        ${basePrompt}

        Include the following sections specific to listicles:
        1. List Structure and Item Count Strategy
        2. Ranking Criteria and Methodology
        3. Engaging Introduction and Hook Strategy
        4. Visual Elements and Formatting Plan
        5. Social Sharing Optimization
        6. Scannable Content Design
        7. Engagement and Click-through Metrics

        Focus on creating scannable, engaging content that provides clear value in an easy-to-consume format.
      `;

    case 'case-study':
      return `
        Create a comprehensive content strategy for a case study about "${topic}" targeting ${targetAudience} with a ${tone} tone.
        ${basePrompt}

        Include the following sections specific to case studies:
        1. Problem-Solution-Results Structure
        2. Data and Metrics Presentation Strategy
        3. Stakeholder Perspectives and Quotes
        4. Visual Storytelling Elements
        5. Credibility and Trust Signals
        6. Industry Context and Background
        7. ROI and Impact Measurement

        Focus on creating compelling narratives that demonstrate clear value and measurable outcomes.
      `;

    case 'opinion-piece':
      return `
        Create a comprehensive content strategy for an opinion piece about "${topic}" targeting ${targetAudience} with a ${tone} tone.
        ${basePrompt}

        Include the following sections specific to opinion pieces:
        1. Argument Structure and Thesis Development
        2. Supporting Evidence and Data Strategy
        3. Counter-argument Acknowledgment Plan
        4. Thought Leadership Positioning
        5. Engagement and Discussion Strategy
        6. Authority Building Elements
        7. Conversation Driving Metrics

        Focus on creating persuasive, well-reasoned content that establishes authority and drives meaningful conversations.
      `;

    case 'technical-tutorial':
      return `
        Create a comprehensive content strategy for a technical tutorial about "${topic}" targeting ${targetAudience} with a ${tone} tone.
        ${basePrompt}

        Include the following sections specific to technical tutorials:
        1. Learning Path and Curriculum Design
        2. Code Examples and Practical Exercises
        3. Skill Level Progression Strategy
        4. Best Practices and Common Pitfalls
        5. Community Engagement and Follow-up Resources
        6. Technical Accuracy and Review Process
        7. Developer Learning Metrics

        Focus on creating educational content that effectively teaches technical concepts with practical applications.
      `;

    case 'blog-article':
      return `
        Create a comprehensive content strategy for a blog article about "${topic}" targeting ${targetAudience} with a ${tone} tone.
        ${basePrompt}

        Include the following sections:
        1. Content Goals and Objectives
        2. Target Audience Personas
        3. Content Themes and Topics
        4. Content Structure and Outline
        5. SEO Strategy
        6. Distribution Channels
        7. Success Metrics

        Focus on creating engaging, valuable content that serves the target audience while achieving business objectives.
      `;

    case 'product-page':
      return `
        Create a comprehensive content strategy for a product page about "${topic}" targeting ${targetAudience} with a ${tone} tone.
        ${basePrompt}

        Include the following sections:
        1. Product Positioning and Messaging
        2. Feature-Benefit Mapping
        3. Customer Pain Points and Solutions
        4. Conversion Optimization Strategy
        5. Trust Signals and Social Proof Elements
        6. Competitive Differentiation
        7. Conversion Metrics

        Focus on creating persuasive content that drives conversions while providing clear value to potential customers.
      `;

    default:
      return `
        Create a comprehensive content strategy for a ${contentType} about "${topic}" targeting ${targetAudience} with a ${tone} tone.
        ${basePrompt}

        Include the following sections:
        1. Content Goals and Objectives
        2. Target Audience Personas
        3. Content Themes and Topics
        4. Content Structure and Outline
        5. SEO Strategy
        6. Distribution Channels
        7. Success Metrics

        Focus on creating valuable content that resonates with the target audience and achieves the intended objectives.
      `;
  }
}

/**
 * Generate content strategy
 * @param topic The topic
 * @param contentType The type of content
 * @param targetAudience The target audience
 * @param tone The tone of the content
 * @param marketResearchArtifact The market research artifact
 * @param keywordAnalysisArtifact The keyword analysis artifact
 * @returns The generated content strategy
 */
export async function generateContentStrategy(
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string,
  marketResearchArtifact: Artifact,
  keywordAnalysisArtifact: Artifact
): Promise<string> {
  try {
    // Extract content from artifacts
    const marketResearch = typeof marketResearchArtifact.content === 'string'
      ? marketResearchArtifact.content
      : JSON.stringify(marketResearchArtifact.content);

    const keywordAnalysis = typeof keywordAnalysisArtifact.content === 'string'
      ? keywordAnalysisArtifact.content
      : JSON.stringify(keywordAnalysisArtifact.content);

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are a content strategy expert. Generate a detailed content strategy based on market research and keyword analysis."
        },
        {
          role: "user",
          content: getContentStrategyPrompt(contentType, topic, targetAudience, tone, marketResearch, keywordAnalysis)
        }
      ],
      temperature: 0.7,
    });

    return response.choices[0].message.content || '';
  } catch (error) {
    logger.error(`Error generating content strategy with OpenAI`, {
      error: (error as Error).message,
      topic,
      contentType,
      targetAudience
    });

    // Return a fallback response in case of error
    return `# Content Strategy: ${topic}

## Content Goals and Objectives
- Establish authority in the ${topic} space
- Drive organic traffic through SEO
- Convert readers into leads or customers
- Educate ${targetAudience} about ${topic}

## Target Audience Personas
- Primary: ${targetAudience}
- Secondary: Industry professionals interested in ${topic}

## Content Themes and Topics
- Introduction to ${topic}
- Advanced ${topic} techniques
- ${topic} case studies
- ${topic} best practices

## Content Structure and Outline
1. Introduction
   - Definition of ${topic}
   - Importance for ${targetAudience}
2. Main Sections
   - Key aspects of ${topic}
   - Implementation strategies
   - Common challenges and solutions
3. Conclusion
   - Next steps
   - Call to action

## SEO Strategy
- Primary keyword: ${topic}
- Secondary keywords: ${topic} for ${targetAudience}, ${topic} guide
- Internal linking structure to related content

## Distribution Channels
- Organic search (primary)
- Social media sharing
- Email newsletter
- Industry forums

## Success Metrics
- Organic traffic
- Time on page
- Social shares
- Conversion rate`;
  }
}

/**
 * Generate article content
 * @param topic The topic
 * @param contentType The type of content
 * @param targetAudience The target audience
 * @param tone The tone of the content
 * @param contentStrategyArtifact The content strategy artifact
 * @param keywordAnalysisArtifact The keyword analysis artifact
 * @returns The generated article content
 */
export async function generateArticleContent(
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string,
  contentStrategyArtifact: Artifact,
  keywordAnalysisArtifact: Artifact
): Promise<{
  title: string;
  content: string;
}> {
  try {
    // Extract content from artifacts
    const contentStrategy = typeof contentStrategyArtifact.content === 'string'
      ? contentStrategyArtifact.content
      : JSON.stringify(contentStrategyArtifact.content);

    const keywordAnalysis = typeof keywordAnalysisArtifact.content === 'string'
      ? keywordAnalysisArtifact.content
      : JSON.stringify(keywordAnalysisArtifact.content);

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are an expert content creator. Generate high-quality, engaging content based on the provided strategy and keywords."
        },
        {
          role: "user",
          content: `
            Create a comprehensive ${contentType} about ${topic} targeting ${targetAudience} with a ${tone} tone.

            Use the following content strategy:
            ${contentStrategy.substring(0, 2000)}

            And the following keyword analysis:
            ${keywordAnalysis.substring(0, 2000)}

            First, create an engaging title for the article.

            Then, write the full article content following the structure outlined in the content strategy.

            Format the article in markdown with clear headings, subheadings, and bullet points where appropriate.

            Begin your response with:
            TITLE: [Your Article Title]

            Then continue with the article content.
          `
        }
      ],
      temperature: 0.7,
      max_tokens: 4000,
    });

    const fullResponse = response.choices[0].message.content || '';

    // Extract title and content
    const titleMatch = fullResponse.match(/TITLE: (.*?)(?:\n|$)/);
    const title = titleMatch ? titleMatch[1] : `${topic} Guide for ${targetAudience}`;

    // Remove the TITLE line from the content
    const content = fullResponse.replace(/TITLE: .*?(?:\n|$)/, '').trim();

    return {
      title,
      content
    };
  } catch (error) {
    logger.error(`Error generating article content with OpenAI`, {
      error: (error as Error).message,
      topic,
      contentType,
      targetAudience
    });

    // Return a fallback response in case of error
    return {
      title: `${topic} Guide for ${targetAudience}`,
      content: `# ${topic} Guide for ${targetAudience}

## Introduction
${topic} has become increasingly important for ${targetAudience}. This guide will help you understand the key concepts and implement best practices.

## What is ${topic}?
${topic} refers to a set of methodologies and practices that help ${targetAudience} achieve better results in their work.

## Key Benefits of ${topic}
- Improved efficiency
- Better outcomes
- Reduced costs
- Enhanced user experience

## How to Implement ${topic}
1. Start with a clear understanding of your goals
2. Analyze your current processes
3. Identify areas for improvement
4. Implement changes incrementally
5. Measure results and adjust as needed

## Common Challenges and Solutions
### Challenge 1: Resistance to Change
Solution: Involve stakeholders early and communicate benefits clearly.

### Challenge 2: Technical Complexity
Solution: Break down implementation into manageable steps.

## Case Study: Successful ${topic} Implementation
A leading company in the industry implemented ${topic} and saw a 30% increase in productivity.

## Conclusion
${topic} offers significant benefits for ${targetAudience}. By following the guidelines in this article, you can successfully implement ${topic} in your organization.

## Next Steps
- Assess your current situation
- Develop an implementation plan
- Start with a pilot project
- Scale based on results`
    };
  }
}

/**
 * Generate market research content
 * @param topic The topic to research
 * @param contentType The type of content
 * @param targetAudience The target audience
 * @returns The generated market research content
 */
export async function generateMarketResearch(
  topic: string,
  contentType: string,
  targetAudience: string
): Promise<string> {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are a market research expert. Generate a detailed market research report."
        },
        {
          role: "user",
          content: `
            Create a comprehensive market research report for a ${contentType} about ${topic} targeting ${targetAudience}.

            Include the following sections:
            1. Target Audience Analysis
            2. Market Trends
            3. Competitor Analysis
            4. Content Gap Analysis
            5. Audience Pain Points
            6. Recommendations

            Format the report in markdown with clear headings and bullet points where appropriate.
          `
        }
      ],
      temperature: 0.7,
    });

    return response.choices[0].message.content || '';
  } catch (error) {
    logger.error(`Error generating market research with OpenAI`, {
      error: (error as Error).message,
      topic,
      contentType,
      targetAudience
    });

    // Return a fallback response in case of error
    return `# Market Research Report: ${topic}

## Target Audience Analysis
- ${targetAudience} shows interest in ${topic}
- Demographics include professionals and enthusiasts

## Market Trends
- Growing interest in ${topic} over the past year
- Shift towards mobile consumption of content

## Competitor Analysis
- Several established competitors in the space
- Gap exists for more ${targetAudience}-focused content

## Content Gap Analysis
- Limited in-depth technical content available
- Need for more beginner-friendly explanations

## Audience Pain Points
- Difficulty understanding complex aspects of ${topic}
- Lack of practical implementation guides

## Recommendations
- Create content that addresses specific pain points
- Focus on practical, actionable advice
- Use clear examples and case studies`;
  }
}

/**
 * Generate keyword analysis content
 * @param topic The topic to analyze
 * @param contentType The type of content
 * @param targetAudience The target audience
 * @returns The generated keyword analysis content
 */
export async function generateKeywordAnalysis(
  topic: string,
  contentType: string,
  targetAudience: string
): Promise<{
  content: string;
  keywords: string[];
}> {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are an SEO expert specializing in keyword research. Generate a detailed keyword analysis report."
        },
        {
          role: "user",
          content: `
            Create a comprehensive keyword analysis for a ${contentType} about ${topic} targeting ${targetAudience}.

            Include the following sections:
            1. Primary Keywords
            2. Secondary Keywords
            3. Long-tail Keywords
            4. Keyword Difficulty Analysis
            5. Search Volume Estimates
            6. Keyword Strategy Recommendations

            Format the report in markdown with clear headings and bullet points where appropriate.

            Also provide a JSON array of the top 10 keywords at the end of your response in this format:
            KEYWORDS_JSON: ["keyword1", "keyword2", "keyword3", ...]
          `
        }
      ],
      temperature: 0.7,
    });

    const content = response.choices[0].message.content || '';

    // Extract keywords from the response
    const keywordsMatch = content.match(/KEYWORDS_JSON: (\[.*?\])/s);
    let keywords: string[] = [];

    if (keywordsMatch && keywordsMatch[1]) {
      try {
        keywords = JSON.parse(keywordsMatch[1]);
      } catch (e) {
        logger.error(`Error parsing keywords JSON`, {
          error: (e as Error).message,
          match: keywordsMatch[1]
        });
      }
    }

    // Remove the KEYWORDS_JSON line from the content
    const cleanContent = content.replace(/KEYWORDS_JSON: \[.*?\]/s, '');

    return {
      content: cleanContent,
      keywords: keywords
    };
  } catch (error) {
    logger.error(`Error generating keyword analysis with OpenAI`, {
      error: (error as Error).message,
      topic,
      contentType,
      targetAudience
    });

    // Return a fallback response in case of error
    return {
      content: `# Keyword Analysis: ${topic}

## Primary Keywords
- ${topic}
- ${topic} guide
- ${topic} tutorial

## Secondary Keywords
- ${topic} for ${targetAudience}
- ${topic} best practices
- ${topic} examples

## Long-tail Keywords
- how to implement ${topic} for beginners
- ${topic} step by step guide
- ${topic} advanced techniques

## Keyword Difficulty Analysis
- Primary keywords have high competition
- Long-tail keywords offer better opportunities

## Search Volume Estimates
- Primary keywords: 1,000-5,000 searches/month
- Long-tail keywords: 100-500 searches/month

## Keyword Strategy Recommendations
- Focus on long-tail keywords for initial content
- Build authority before targeting competitive terms
- Include primary keywords in titles and headings`,
      keywords: [
        `${topic}`,
        `${topic} guide`,
        `${topic} tutorial`,
        `${topic} for ${targetAudience}`,
        `${topic} best practices`,
        `${topic} examples`,
        `how to implement ${topic}`,
        `${topic} step by step`,
        `${topic} advanced techniques`,
        `${topic} benefits`
      ]
    };
  }
}

/**
 * Optimize content for SEO
 * @param title The article title
 * @param content The article content
 * @param keywords The target keywords
 * @returns The SEO-optimized content
 */
export async function optimizeContentForSEO(
  title: string,
  content: string,
  keywords: string[] | string,
  topic?: string,
  targetAudience?: string
): Promise<{
  title: string;
  content: string;
  metaDescription?: string;
  keywordDensity?: Record<string, number>;
}> {
  try {
    // Process keywords
    const keywordsStr = Array.isArray(keywords) ? keywords.join(', ') : keywords;

    // Include topic and target audience in the prompt if provided
    const topicStr = topic ? `\nTopic: ${topic}` : '';
    const audienceStr = targetAudience ? `\nTarget Audience: ${targetAudience}` : '';

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are an SEO expert. Optimize the provided content for search engines while maintaining readability and quality."
        },
        {
          role: "user",
          content: `
            Optimize the following content for SEO using these target keywords:
            ${keywordsStr}
            ${topicStr}
            ${audienceStr}

            Title: ${title}

            Content:
            ${content.substring(0, 6000)}

            Please optimize by:
            1. Improving the title for SEO while maintaining its meaning
            2. Enhancing keyword density in a natural way
            3. Improving headings and subheadings
            4. Adding internal linking suggestions in [brackets]
            5. Ensuring proper content structure for readability
            6. Creating a compelling meta description (150-160 characters)
            7. Analyzing keyword density in the optimized content

            Format your response as follows:

            OPTIMIZED_TITLE: [Your Optimized Title]

            META_DESCRIPTION: [Your Meta Description]

            [Optimized Content]

            KEYWORD_DENSITY: {"keyword1": 0.5, "keyword2": 0.3, ...}
          `
        }
      ],
      temperature: 0.7,
      max_tokens: 4000,
    });

    const fullResponse = response.choices[0].message.content || '';

    // Extract optimized title
    const titleMatch = fullResponse.match(/OPTIMIZED_TITLE: (.*?)(?:\n|$)/);
    const optimizedTitle = titleMatch ? titleMatch[1] : title;

    // Extract meta description
    const metaMatch = fullResponse.match(/META_DESCRIPTION: (.*?)(?:\n|$)/);
    const metaDescription = metaMatch ? metaMatch[1] : undefined;

    // Extract keyword density
    const densityMatch = fullResponse.match(/KEYWORD_DENSITY: (\{.*\})/s);
    let keywordDensity: Record<string, number> | undefined = undefined;

    if (densityMatch) {
      try {
        keywordDensity = JSON.parse(densityMatch[1]);
      } catch (e) {
        logger.warn(`Error parsing keyword density JSON`, {
          match: densityMatch[1],
          error: (e as Error).message
        });
      }
    }

    // Remove the special markers from the content
    let optimizedContent = fullResponse
      .replace(/OPTIMIZED_TITLE: .*?(?:\n|$)/, '')
      .replace(/META_DESCRIPTION: .*?(?:\n|$)/, '')
      .replace(/KEYWORD_DENSITY: \{.*\}/s, '')
      .trim();

    return {
      title: optimizedTitle,
      content: optimizedContent,
      metaDescription,
      keywordDensity
    };
  } catch (error) {
    logger.error(`Error optimizing content for SEO with OpenAI`, {
      error: (error as Error).message,
      title,
      keywordCount: keywords.length
    });

    // Return the original content in case of error
    return {
      title,
      content
    };
  }
}

/**
 * Assess content quality
 * @param title Article title
 * @param content Article content
 * @param topic Topic
 * @param targetAudience Target audience
 * @param tone Tone
 * @returns Promise<any> Quality assessment
 */
export async function assessContentQuality(
  title: string,
  content: any,
  topic: string,
  targetAudience: string,
  tone: string
): Promise<any> {
  try {
    // Handle different content formats
    let contentStr: string;
    if (typeof content === 'string') {
      contentStr = content;
    } else if (content && typeof content === 'object') {
      // If content is an object with a content property
      if (typeof content.content === 'string') {
        contentStr = content.content;
      } else if (content.text && typeof content.text === 'string') {
        contentStr = content.text;
      } else {
        // Try to stringify the content object
        try {
          contentStr = JSON.stringify(content);
        } catch (e) {
          contentStr = "Content could not be processed";
          logger.error(`Error stringifying content object`, {
            error: (e as Error).message,
            contentType: typeof content
          });
        }
      }
    } else {
      contentStr = String(content || "");
    }

    const prompt = `
      Assess the quality of the following article:

      Title: ${title}

      Content:
      ${contentStr.substring(0, 6000)}

      Topic: ${topic}
      Target Audience: ${targetAudience}
      Intended Tone: ${tone}

      Please provide a comprehensive quality assessment including:
      1. Content Quality (accuracy, depth, relevance)
      2. Readability (clarity, flow, structure)
      3. SEO Effectiveness (keyword usage, meta description)
      4. Engagement Potential (hook, storytelling, call-to-action)
      5. Overall Score (0-100)
      6. Recommendations for Improvement

      Format your response as a JSON object with these sections.
    `;

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are a content quality assessment expert. Provide detailed, objective quality assessments."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    });

    const assessmentText = response.choices[0].message.content || '{}';

    try {
      return JSON.parse(assessmentText);
    } catch (e) {
      logger.warn(`Error parsing quality assessment JSON`, {
        assessment: assessmentText,
        error: (e as Error).message
      });

      // Return a structured object even if parsing fails
      return {
        contentQuality: {
          accuracy: 'Unable to parse assessment',
          depth: 'Unable to parse assessment',
          relevance: 'Unable to parse assessment'
        },
        readability: {
          clarity: 'Unable to parse assessment',
          flow: 'Unable to parse assessment',
          structure: 'Unable to parse assessment'
        },
        seoEffectiveness: {
          keywordUsage: 'Unable to parse assessment',
          metaDescription: 'Unable to parse assessment'
        },
        engagementPotential: {
          hook: 'Unable to parse assessment',
          storytelling: 'Unable to parse assessment',
          callToAction: 'Unable to parse assessment'
        },
        overallScore: 0,
        recommendations: ['Unable to parse assessment']
      };
    }
  } catch (error) {
    logger.error(`Error assessing content quality with OpenAI`, {
      error: (error as Error).message,
      title,
      topic,
      targetAudience,
      tone
    });

    // Return a fallback assessment in case of error
    return {
      contentQuality: {
        accuracy: 'Error generating assessment',
        depth: 'Error generating assessment',
        relevance: 'Error generating assessment'
      },
      readability: {
        clarity: 'Error generating assessment',
        flow: 'Error generating assessment',
        structure: 'Error generating assessment'
      },
      seoEffectiveness: {
        keywordUsage: 'Error generating assessment',
        metaDescription: 'Error generating assessment'
      },
      engagementPotential: {
        hook: 'Error generating assessment',
        storytelling: 'Error generating assessment',
        callToAction: 'Error generating assessment'
      },
      overallScore: 0,
      recommendations: ['Error generating assessment']
    };
  }
}
