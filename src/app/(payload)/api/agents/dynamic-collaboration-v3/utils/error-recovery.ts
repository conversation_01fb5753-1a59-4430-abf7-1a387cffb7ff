/**
 * Error Recovery System
 *
 * This utility provides robust error handling and recovery mechanisms
 * for the goal-based orchestration system, ensuring that agent tasks
 * can recover from failures and the system remains resilient.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import { StateManager } from '../state/manager';
import { GoalStatus, MessageType } from '../state/unified-schema';

/**
 * Error severity levels
 */
export enum ErrorSeverity {
  LOW = 'low',       // Non-critical errors that don't affect workflow
  MEDIUM = 'medium', // Errors that may affect specific tasks but not the overall workflow
  HIGH = 'high',     // Critical errors that affect the workflow but can be recovered
  FATAL = 'fatal'    // Unrecoverable errors that require manual intervention
}

/**
 * Error record structure
 */
export interface ErrorRecord {
  id: string;
  timestamp: string;
  component: string;
  operation: string;
  message: string;
  details?: any;
  severity: ErrorSeverity;
  goalId?: string;
  artifactId?: string;
  agentId?: string;
  retryCount: number;
  maxRetries: number;
  resolved: boolean;
  resolvedAt?: string;
  resolution?: string;
}

/**
 * Retry configuration
 */
export interface RetryConfig {
  maxRetries: number;
  initialDelayMs: number;
  backoffFactor: number;
  maxDelayMs: number;
}

/**
 * Default retry configuration
 */
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  initialDelayMs: 1000,
  backoffFactor: 2,
  maxDelayMs: 30000
};

/**
 * Error Recovery System
 */
export class ErrorRecoverySystem {
  private sessionId: string;
  private stateManager: StateManager;

  /**
   * Constructor
   * @param sessionId Session ID
   */
  constructor(sessionId: string) {
    this.sessionId = sessionId;
    this.stateManager = new StateManager(sessionId);
  }

  /**
   * Record an error
   * @param component Component where the error occurred
   * @param operation Operation that failed
   * @param error Error object or message
   * @param metadata Additional metadata
   * @param severity Error severity
   * @returns Error record ID
   */
  public async recordError(
    component: string,
    operation: string,
    error: Error | string,
    metadata: {
      goalId?: string;
      artifactId?: string;
      agentId?: string;
      details?: any;
    } = {},
    severity: ErrorSeverity = ErrorSeverity.MEDIUM
  ): Promise<string> {
    try {
      const errorId = uuidv4();
      const errorMessage = error instanceof Error ? error.message : error;
      const errorDetails = error instanceof Error ? { stack: error.stack } : undefined;

      const errorRecord: ErrorRecord = {
        id: errorId,
        timestamp: new Date().toISOString(),
        component,
        operation,
        message: errorMessage,
        details: { ...errorDetails, ...metadata.details },
        severity,
        goalId: metadata.goalId,
        artifactId: metadata.artifactId,
        agentId: metadata.agentId,
        retryCount: 0,
        maxRetries: DEFAULT_RETRY_CONFIG.maxRetries,
        resolved: false
      };

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        logger.error(`Failed to record error: session ${this.sessionId} not found`, {
          errorId,
          component,
          operation,
          message: errorMessage
        });
        return errorId;
      }

      // Update state with error record
      await this.stateManager.updateState(currentState => {
        if (!currentState) return currentState;

        // Initialize errors object if it doesn't exist
        const errors = currentState.errors || {};

        // Add error record
        errors[errorId] = errorRecord;

        // Add system message about the error
        const messageId = uuidv4();
        const messages = { ...currentState.messages };
        messages[messageId] = {
          id: messageId,
          timestamp: new Date().toISOString(),
          from: 'system',
          to: 'all',
          type: MessageType.SYSTEM_ERROR,
          content: {
            errorId,
            component,
            operation,
            message: errorMessage,
            severity,
            goalId: metadata.goalId,
            artifactId: metadata.artifactId,
            agentId: metadata.agentId
          }
        };

        // Update workflow progress with error information
        const workflowProgress = { ...currentState.workflowProgress };
        workflowProgress.lastError = {
          errorId,
          timestamp: errorRecord.timestamp,
          message: errorMessage,
          severity
        };

        if (severity === ErrorSeverity.FATAL) {
          workflowProgress.status = 'error';
        }

        return {
          ...currentState,
          errors,
          messages,
          workflowProgress
        };
      });

      logger.error(`Error recorded: ${errorMessage}`, {
        sessionId: this.sessionId,
        errorId,
        component,
        operation,
        severity,
        goalId: metadata.goalId,
        artifactId: metadata.artifactId,
        agentId: metadata.agentId
      });

      // If the error is related to a goal, update the goal status
      if (metadata.goalId && (severity === ErrorSeverity.HIGH || severity === ErrorSeverity.FATAL)) {
        await this.updateGoalStatus(metadata.goalId, severity);
      }

      return errorId;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`Meta-error: Failed to record error`, {
        sessionId: this.sessionId,
        component,
        operation,
        originalError: error instanceof Error ? error.message : String(error),
        metaError: err.message,
        stack: err.stack
      });
      return uuidv4(); // Return a dummy ID since we couldn't record the error
    }
  }

  /**
   * Update goal status based on error severity
   * @param goalId Goal ID
   * @param severity Error severity
   */
  private async updateGoalStatus(goalId: string, severity: ErrorSeverity): Promise<void> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) return;

      // Get the goal
      const goal = state.goals[goalId];
      if (!goal) return;

      // Update goal status based on severity
      let newStatus: GoalStatus;

      if (severity === ErrorSeverity.FATAL) {
        newStatus = GoalStatus.FAILED;
      } else if (severity === ErrorSeverity.HIGH) {
        newStatus = GoalStatus.BLOCKED;
      } else {
        return; // No need to update for lower severities
      }

      // Update the goal status
      await this.stateManager.updateState(currentState => {
        if (!currentState) return currentState;

        const goals = { ...currentState.goals };
        goals[goalId] = {
          ...goals[goalId],
          status: newStatus
        };

        return {
          ...currentState,
          goals
        };
      });
    } catch (error) {
      logger.error(`Failed to update goal status`, {
        sessionId: this.sessionId,
        goalId,
        severity,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Resolve an error
   * @param errorId Error ID
   * @param resolution Resolution description
   * @returns Success indicator
   */
  public async resolveError(errorId: string, resolution: string): Promise<boolean> {
    try {
      // Get current state
      const state = await this.stateManager.getState();
      if (!state) return false;

      // Get the error record
      const errors = state.errors || {};
      const errorRecord = errors[errorId];
      if (!errorRecord) return false;

      // Update error record
      await this.stateManager.updateState(currentState => {
        if (!currentState) return currentState;

        const errors = { ...currentState.errors };
        errors[errorId] = {
          ...errors[errorId],
          resolved: true,
          resolvedAt: new Date().toISOString(),
          resolution
        };

        return {
          ...currentState,
          errors
        };
      });

      logger.info(`Error resolved: ${errorId}`, {
        sessionId: this.sessionId,
        errorId,
        resolution
      });

      return true;
    } catch (error) {
      logger.error(`Failed to resolve error`, {
        sessionId: this.sessionId,
        errorId,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Execute an operation with retry logic
   * @param operation Operation function to execute
   * @param component Component name
   * @param operationName Operation name
   * @param metadata Additional metadata
   * @param retryConfig Retry configuration
   * @returns Operation result
   */
  public async executeWithRetry<T>(
    operation: () => Promise<T>,
    component: string,
    operationName: string,
    metadata: {
      goalId?: string;
      artifactId?: string;
      agentId?: string;
      details?: any;
    } = {},
    retryConfig: Partial<RetryConfig> = {}
  ): Promise<T> {
    // Merge with default retry config
    const config = { ...DEFAULT_RETRY_CONFIG, ...retryConfig };
    let retryCount = 0;
    let lastError: Error | null = null;

    while (retryCount <= config.maxRetries) {
      try {
        // Execute the operation
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        retryCount++;

        // Record the error
        const severity = retryCount >= config.maxRetries
          ? ErrorSeverity.HIGH
          : ErrorSeverity.MEDIUM;

        await this.recordError(
          component,
          operationName,
          lastError,
          { ...metadata, details: { ...metadata.details, retryCount } },
          severity
        );

        // If we've reached max retries, break out of the loop
        if (retryCount > config.maxRetries) {
          break;
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          config.initialDelayMs * Math.pow(config.backoffFactor, retryCount - 1),
          config.maxDelayMs
        );

        logger.info(`Retrying operation after error (${retryCount}/${config.maxRetries})`, {
          sessionId: this.sessionId,
          component,
          operation: operationName,
          delay,
          error: lastError.message
        });

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // If we get here, all retries failed
    throw new Error(`Operation ${operationName} failed after ${config.maxRetries} retries: ${lastError?.message}`);
  }
}
