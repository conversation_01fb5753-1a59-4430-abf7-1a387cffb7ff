/**
 * OpenAI Integration Utility
 *
 * This file provides utility functions for interacting with the OpenAI API
 * to generate content for the dynamic collaboration system.
 */

import OpenAI from 'openai';
import logger from '../../../utils/logger';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * Generate market research content
 * @param topic The topic to research
 * @param contentType The type of content
 * @param targetAudience The target audience
 * @returns The generated market research content
 */
export async function generateMarketResearch(
  topic: string,
  contentType: string,
  targetAudience: string
): Promise<string> {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are a market research expert. Generate a detailed market research report."
        },
        {
          role: "user",
          content: `
            Create a comprehensive market research report for a ${contentType} about ${topic} targeting ${targetAudience}.

            Include the following sections:
            1. Target Audience Analysis
            2. Market Trends
            3. Competitor Analysis
            4. Content Gap Analysis
            5. Audience Pain Points
            6. Recommendations

            Format the report in markdown with clear headings and bullet points where appropriate.
          `
        }
      ],
      temperature: 0.7,
    });

    return response.choices[0].message.content || '';
  } catch (error) {
    logger.error(`Error generating market research with OpenAI`, {
      error: (error as Error).message,
      topic,
      contentType,
      targetAudience
    });

    // Return a fallback response in case of error
    return `# Market Research Report: ${topic}

## Target Audience Analysis
- ${targetAudience} shows interest in ${topic}
- Demographics include professionals and enthusiasts

## Market Trends
- Growing interest in ${topic} over the past year
- Shift towards mobile consumption of content

## Competitor Analysis
- Several established competitors in the space
- Gap exists for more ${targetAudience}-focused content

## Content Gap Analysis
- Limited in-depth technical content available
- Need for more beginner-friendly explanations

## Audience Pain Points
- Difficulty understanding complex aspects of ${topic}
- Lack of practical implementation guides

## Recommendations
- Create content that addresses specific pain points
- Focus on practical, actionable advice
- Use clear examples and case studies`;
  }
}

/**
 * Generate keyword analysis content
 * @param topic The topic to analyze
 * @param contentType The type of content
 * @param targetAudience The target audience
 * @returns The generated keyword analysis content
 */
export async function generateKeywordAnalysis(
  topic: string,
  contentType: string,
  targetAudience: string
): Promise<{
  content: string;
  keywords: string[];
}> {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are an SEO expert specializing in keyword research. Generate a detailed keyword analysis report."
        },
        {
          role: "user",
          content: `
            Create a comprehensive keyword analysis for a ${contentType} about ${topic} targeting ${targetAudience}.

            Include the following sections:
            1. Primary Keywords
            2. Secondary Keywords
            3. Long-tail Keywords
            4. Keyword Difficulty Analysis
            5. Search Volume Estimates
            6. Keyword Strategy Recommendations

            Format the report in markdown with clear headings and bullet points where appropriate.

            Also provide a JSON array of the top 10 keywords at the end of your response in this format:
            KEYWORDS_JSON: ["keyword1", "keyword2", "keyword3", ...]
          `
        }
      ],
      temperature: 0.7,
    });

    const content = response.choices[0].message.content || '';

    // Extract keywords from the response
    const keywordsMatch = content.match(/KEYWORDS_JSON: (\[.*?\])/s);
    let keywords: string[] = [];

    if (keywordsMatch && keywordsMatch[1]) {
      try {
        keywords = JSON.parse(keywordsMatch[1]);
      } catch (e) {
        logger.error(`Error parsing keywords JSON`, {
          error: (e as Error).message,
          match: keywordsMatch[1]
        });
      }
    }

    // Remove the KEYWORDS_JSON line from the content
    const cleanContent = content.replace(/KEYWORDS_JSON: \[.*?\]/s, '');

    return {
      content: cleanContent,
      keywords: keywords
    };
  } catch (error) {
    logger.error(`Error generating keyword analysis with OpenAI`, {
      error: (error as Error).message,
      topic,
      contentType,
      targetAudience
    });

    // Return a fallback response in case of error
    return {
      content: `# Keyword Analysis: ${topic}

## Primary Keywords
- ${topic}
- ${topic} guide
- ${topic} tutorial

## Secondary Keywords
- ${topic} for ${targetAudience}
- ${topic} best practices
- ${topic} examples

## Long-tail Keywords
- how to implement ${topic} for beginners
- ${topic} step by step guide
- ${topic} advanced techniques

## Keyword Difficulty Analysis
- Primary keywords have high competition
- Long-tail keywords offer better opportunities

## Search Volume Estimates
- Primary keywords: 1,000-5,000 searches/month
- Long-tail keywords: 100-500 searches/month

## Keyword Strategy Recommendations
- Focus on long-tail keywords for initial content
- Build authority before targeting competitive terms
- Include primary keywords in titles and headings`,
      keywords: [
        `${topic}`,
        `${topic} guide`,
        `${topic} tutorial`,
        `${topic} for ${targetAudience}`,
        `${topic} best practices`,
        `${topic} examples`,
        `how to implement ${topic}`,
        `${topic} step by step`,
        `${topic} advanced techniques`,
        `${topic} benefits`
      ]
    };
  }
}

// Export other content generation functions
export { generateContentStrategy } from './content-generation';
export { generateArticleContent } from './content-generation';
export { optimizeContentForSEO } from './content-generation';
