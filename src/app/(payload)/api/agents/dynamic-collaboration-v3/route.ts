/**
 * Dynamic Collaboration V3 API
 *
 * This file implements the API endpoints for the dynamic collaboration v3 system.
 * It provides endpoints for session management, goal processing, and agent communication.
 */

import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import logger from '../../utils/logger';
import { ContentGenerationParams } from './state/unified-schema';
import { GoalOrchestrator } from './workflow/goal-orchestrator';
import { AgentRegistry } from './agents/agent-registry';

/**
 * POST /api/agents/dynamic-collaboration-v3
 * Create a new dynamic collaboration session
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Parse request body
    const body = await req.json();
    const { topic, contentType, targetAudience, tone, keywords, additionalInstructions } = body;

    // Validate required fields
    if (!topic) {
      return NextResponse.json(
        { error: 'Missing required field: topic' },
        { status: 400 }
      );
    }

    // Generate session ID
    const sessionId = uuidv4();

    // Initialize session
    const success = await GoalOrchestrator.initiate(sessionId, {
      topic,
      contentType: contentType || 'blog-article',
      targetAudience: targetAudience || 'general audience',
      tone: tone || 'informative',
      keywords: keywords || [],
      additionalInstructions
    });

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to initialize session' },
        { status: 500 }
      );
    }

    // Return session ID
    return NextResponse.json({ sessionId });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error creating dynamic collaboration session`, {
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/agents/dynamic-collaboration-v3?sessionId=xxx
 * Get the current state of a dynamic collaboration session
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Get session ID from query params
    const { searchParams } = new URL(req.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Create orchestrator
    const orchestrator = new GoalOrchestrator(sessionId);

    // Get state
    const state = await orchestrator.getState();

    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Return state
    return NextResponse.json({ state });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error getting dynamic collaboration session state`, {
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
