/**
 * Artifact Evaluation Service
 *
 * This service evaluates artifacts against goal criteria to determine
 * if they meet the requirements for goal completion.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import { Artifact, Goal, GoalStatus, GoalType } from '../state/unified-schema';
import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * Evaluation result
 */
export interface ArtifactEvaluation {
  artifactId: string;
  goalId: string;
  meetsRequirements: boolean;
  score: number;
  criteriaEvaluation: {
    [criterion: string]: {
      met: boolean;
      score: number;
      feedback: string;
      improvementSuggestions?: string[];
    };
  };
  overallFeedback: string;
  strengths: string[];
  areasForImprovement: string[];
  improvementSuggestions: string[];
  timestamp: string;
}

/**
 * Artifact Evaluation Service
 */
export class ArtifactEvaluationService {
  /**
   * Evaluate an artifact against a goal's criteria
   * @param artifact The artifact to evaluate
   * @param goal The goal to evaluate against
   * @returns The evaluation result
   */
  public static async evaluateArtifact(artifact: Artifact, goal: Goal): Promise<ArtifactEvaluation> {
    try {
      logger.info(`Evaluating artifact ${artifact.id} against goal ${goal.id}`, {
        artifactId: artifact.id,
        goalId: goal.id,
        goalType: goal.type
      });

      // Extract content from artifact
      const content = typeof artifact.content === 'string'
        ? artifact.content
        : JSON.stringify(artifact.content);

      // Use AI to evaluate the artifact against the goal criteria
      const evaluation = await this.evaluateWithAI(content, goal);

      return {
        artifactId: artifact.id,
        goalId: goal.id,
        meetsRequirements: evaluation.meetsRequirements,
        score: evaluation.score,
        criteriaEvaluation: evaluation.criteriaEvaluation,
        overallFeedback: evaluation.overallFeedback,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      const err = error as Error;
      logger.error(`Error evaluating artifact`, {
        artifactId: artifact.id,
        goalId: goal.id,
        error: err.message || String(error),
        stack: err.stack
      });

      // Return a default evaluation in case of error
      return this.createDefaultEvaluation(artifact.id, goal);
    }
  }

  /**
   * Evaluate an artifact against a goal's criteria using AI
   * @param content The artifact content
   * @param goal The goal to evaluate against
   * @returns The evaluation result
   */
  private static async evaluateWithAI(content: string, goal: Goal): Promise<{
    meetsRequirements: boolean;
    score: number;
    criteriaEvaluation: {
      [criterion: string]: {
        met: boolean;
        score: number;
        feedback: string;
        improvementSuggestions?: string[];
      };
    };
    overallFeedback: string;
    strengths: string[];
    areasForImprovement: string[];
    improvementSuggestions: string[];
  }> {
    try {
      // Prepare the criteria for evaluation
      const criteriaPrompt = goal.criteria.map(criterion => `- ${criterion}`).join('\n');

      // Call OpenAI to evaluate the artifact
      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content: `You are an expert evaluator assessing if content meets specific criteria. Evaluate thoroughly and provide detailed, actionable feedback.`
          },
          {
            role: "user",
            content: `
              Evaluate the following content against these criteria for a ${goal.type} goal:

              ${criteriaPrompt}

              Content to evaluate:
              ${content.substring(0, 6000)}

              For each criterion, determine:
              1. Whether it is met (true/false)
              2. A score from 0-100
              3. Specific feedback explaining your evaluation
              4. Specific suggestions for improvement if not fully met

              Then provide an overall assessment including:
              1. Whether the content meets all requirements (true/false)
              2. An overall score from 0-100
              3. Overall feedback
              4. List of strengths (what was done well)
              5. List of areas for improvement (what needs work)
              6. Specific actionable suggestions for improvement

              Format your response as JSON with this structure:
              {
                "criteriaEvaluation": {
                  "criterion1": {
                    "met": true/false,
                    "score": 0-100,
                    "feedback": "detailed feedback",
                    "improvementSuggestions": ["suggestion1", "suggestion2"]
                  },
                  ...
                },
                "meetsRequirements": true/false,
                "score": 0-100,
                "overallFeedback": "detailed overall feedback",
                "strengths": ["strength1", "strength2", ...],
                "areasForImprovement": ["area1", "area2", ...],
                "improvementSuggestions": ["suggestion1", "suggestion2", ...]
              }
            `
          }
        ],
        temperature: 0.7,
        response_format: { type: "json_object" }
      });

      // Parse the response
      const evaluationContent = response.choices[0].message.content || '';
      const evaluation = JSON.parse(evaluationContent);

      // Ensure all required fields exist
      evaluation.strengths = evaluation.strengths || [];
      evaluation.areasForImprovement = evaluation.areasForImprovement || [];
      evaluation.improvementSuggestions = evaluation.improvementSuggestions || [];

      // Set a threshold for meeting requirements (can be adjusted)
      if (evaluation.score < 70) {
        evaluation.meetsRequirements = false;
      }

      return evaluation;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error evaluating with AI`, {
        goalType: goal.type,
        error: err.message || String(error),
        stack: err.stack
      });

      // Return a default evaluation in case of error
      return {
        meetsRequirements: true,
        score: 75,
        criteriaEvaluation: Object.fromEntries(
          goal.criteria.map(criterion => [
            criterion,
            {
              met: true,
              score: 75,
              feedback: `Unable to properly evaluate this criterion due to an error, but assuming it meets minimum requirements.`,
              improvementSuggestions: ["Review this criterion manually to ensure it meets requirements."]
            }
          ])
        ),
        overallFeedback: `Unable to perform a detailed evaluation due to an error, but the content appears to meet basic requirements.`,
        strengths: ["Content was successfully created"],
        areasForImprovement: ["Automated evaluation failed, manual review recommended"],
        improvementSuggestions: ["Review the content manually against the goal criteria"]
      };
    }
  }

  /**
   * Create a default evaluation in case of error
   * @param artifactId The artifact ID
   * @param goal The goal
   * @returns A default evaluation
   */
  private static createDefaultEvaluation(artifactId: string, goal: Goal): ArtifactEvaluation {
    return {
      artifactId,
      goalId: goal.id,
      meetsRequirements: true,
      score: 75,
      criteriaEvaluation: Object.fromEntries(
        goal.criteria.map(criterion => [
          criterion,
          {
            met: true,
            score: 75,
            feedback: `Unable to properly evaluate this criterion due to an error, but assuming it meets minimum requirements.`,
            improvementSuggestions: ["Review this criterion manually to ensure it meets requirements."]
          }
        ])
      ),
      overallFeedback: `Unable to perform a detailed evaluation due to an error, but the content appears to meet basic requirements.`,
      strengths: ["Content was successfully created"],
      areasForImprovement: ["Automated evaluation failed, manual review recommended"],
      improvementSuggestions: ["Review the content manually against the goal criteria"],
      timestamp: new Date().toISOString()
    };
  }
}
