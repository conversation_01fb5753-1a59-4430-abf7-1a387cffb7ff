/**
 * Workflow State Machine
 *
 * This file implements a state machine for the dynamic collaboration workflow.
 * It defines states, transitions, and guards for the workflow.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../collaborative-iteration/utils/logger';
import {
  WorkflowPhase,
  GoalType,
  GoalStatus,
  MessageType,
  CollaborationState,
  ContentGenerationParams
} from '../state/unified-schema';
import { StateManager } from '../state/manager';
import { StateNotFoundError, ConcurrencyError } from '../state/store';

/**
 * Transition guard function type
 */
type TransitionGuard = (state: CollaborationState) => boolean;

/**
 * Transition action function type
 */
type TransitionAction = (stateManager: StateManager, state: CollaborationState) => Promise<boolean>;

/**
 * Transition definition
 */
interface Transition {
  from: WorkflowPhase;
  to: WorkflowPhase;
  guard: TransitionGuard;
  action: TransitionAction;
}

/**
 * Check if all goals of a specific type are completed
 */
function areGoalsOfTypeCompleted(state: CollaborationState, goalType: GoalType): boolean {
  const goalsOfType = Object.values(state.goals).filter(goal => goal.type === goalType);

  if (goalsOfType.length === 0) {
    return false;
  }

  return goalsOfType.every(goal => goal.status === GoalStatus.COMPLETED);
}

/**
 * Check if any goals of a specific type are in progress
 */
function areGoalsOfTypeInProgress(state: CollaborationState, goalType: GoalType): boolean {
  const goalsOfType = Object.values(state.goals).filter(goal => goal.type === goalType);

  if (goalsOfType.length === 0) {
    return false;
  }

  return goalsOfType.some(goal => goal.status === GoalStatus.IN_PROGRESS);
}

/**
 * Define research phase goals
 */
async function defineResearchPhaseGoals(stateManager: StateManager, state: CollaborationState): Promise<boolean> {
  try {
    // Define market research goal
    const marketResearchGoal = {
      description: `Conduct market research for ${state.topic}`,
      type: GoalType.MARKET_RESEARCH,
      dependencies: [],
      criteria: [
        'Identify target audience demographics',
        'Analyze market trends',
        'Identify key competitors',
        'Determine market gaps and opportunities'
      ]
    };

    // Define keyword analysis goal
    const keywordAnalysisGoal = {
      description: `Analyze keywords for ${state.topic}`,
      type: GoalType.KEYWORD_ANALYSIS,
      dependencies: [],
      criteria: [
        'Identify primary keywords',
        'Identify secondary keywords',
        'Analyze keyword competition',
        'Determine keyword search volume'
      ]
    };

    // Define goals
    await stateManager.defineGoals([marketResearchGoal, keywordAnalysisGoal]);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error defining research phase goals`, {
      sessionId: state.id,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Define creation phase goals
 */
async function defineCreationPhaseGoals(stateManager: StateManager, state: CollaborationState): Promise<boolean> {
  try {
    // Define content strategy goal
    const contentStrategyGoal = {
      description: `Develop content strategy for ${state.topic}`,
      type: GoalType.CONTENT_STRATEGY,
      dependencies: state.completedGoalIds,
      criteria: [
        'Define content structure',
        'Outline key sections',
        'Determine tone and style',
        'Identify key points to cover'
      ]
    };

    // Define content creation goal
    const contentCreationGoal = {
      description: `Create content for ${state.topic}`,
      type: GoalType.CONTENT_CREATION,
      dependencies: [],
      criteria: [
        'Write engaging introduction',
        'Develop comprehensive body content',
        'Create compelling conclusion',
        'Include relevant examples and data'
      ]
    };

    // Define goals
    await stateManager.defineGoals([contentStrategyGoal, contentCreationGoal]);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error defining creation phase goals`, {
      sessionId: state.id,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Define review phase goals
 */
async function defineReviewPhaseGoals(stateManager: StateManager, state: CollaborationState): Promise<boolean> {
  try {
    // Define SEO optimization goal
    const seoOptimizationGoal = {
      description: `Optimize content for SEO`,
      type: GoalType.SEO_OPTIMIZATION,
      dependencies: state.completedGoalIds,
      criteria: [
        'Optimize title and headings',
        'Ensure proper keyword density',
        'Add meta description',
        'Improve readability'
      ]
    };

    // Define quality assessment goal
    const qualityAssessmentGoal = {
      description: `Assess content quality`,
      type: GoalType.QUALITY_ASSESSMENT,
      dependencies: [],
      criteria: [
        'Check grammar and spelling',
        'Ensure factual accuracy',
        'Verify content meets requirements',
        'Assess overall quality'
      ]
    };

    // Define goals
    await stateManager.defineGoals([seoOptimizationGoal, qualityAssessmentGoal]);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error defining review phase goals`, {
      sessionId: state.id,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Transition definitions
 */
const transitions: Transition[] = [
  // Planning to Research
  {
    from: WorkflowPhase.PLANNING,
    to: WorkflowPhase.RESEARCH,
    guard: (state) => state.status === 'active',
    action: async (stateManager, state) => {
      // Define research phase goals
      const success = await defineResearchPhaseGoals(stateManager, state);

      // Transition to research phase
      if (success) {
        return await stateManager.updatePhase(WorkflowPhase.RESEARCH);
      }

      return false;
    }
  },

  // Research to Creation
  {
    from: WorkflowPhase.RESEARCH,
    to: WorkflowPhase.CREATION,
    guard: (state) =>
      areGoalsOfTypeCompleted(state, GoalType.MARKET_RESEARCH) &&
      areGoalsOfTypeCompleted(state, GoalType.KEYWORD_ANALYSIS),
    action: async (stateManager, state) => {
      // Define creation phase goals
      const success = await defineCreationPhaseGoals(stateManager, state);

      // Transition to creation phase
      if (success) {
        return await stateManager.updatePhase(WorkflowPhase.CREATION);
      }

      return false;
    }
  },

  // Creation to Review
  {
    from: WorkflowPhase.CREATION,
    to: WorkflowPhase.REVIEW,
    guard: (state) =>
      areGoalsOfTypeCompleted(state, GoalType.CONTENT_STRATEGY) &&
      areGoalsOfTypeCompleted(state, GoalType.CONTENT_CREATION),
    action: async (stateManager, state) => {
      // Define review phase goals
      const success = await defineReviewPhaseGoals(stateManager, state);

      // Transition to review phase
      if (success) {
        return await stateManager.updatePhase(WorkflowPhase.REVIEW);
      }

      return false;
    }
  },

  // Review to Finalization
  {
    from: WorkflowPhase.REVIEW,
    to: WorkflowPhase.FINALIZATION,
    guard: (state) =>
      areGoalsOfTypeCompleted(state, GoalType.SEO_OPTIMIZATION) &&
      areGoalsOfTypeCompleted(state, GoalType.QUALITY_ASSESSMENT),
    action: async (stateManager, state) => {
      // Transition to finalization phase
      return await stateManager.updatePhase(WorkflowPhase.FINALIZATION);
    }
  }
];

/**
 * Workflow State Machine
 */
export class WorkflowStateMachine {
  private sessionId: string;
  private stateManager: StateManager;

  /**
   * Constructor
   * @param sessionId The session ID
   */
  constructor(sessionId: string) {
    this.sessionId = sessionId;
    this.stateManager = new StateManager(sessionId);
  }

  /**
   * Initialize the workflow
   * @param params Content generation parameters
   * @returns Promise<boolean> indicating success
   */
  public async initialize(params: ContentGenerationParams): Promise<boolean> {
    try {
      // Initialize session
      const success = await this.stateManager.initializeSession(params);

      if (!success) {
        return false;
      }

      // Start the workflow by transitioning to the research phase
      return await this.checkAndTransition();
    } catch (error) {
      const err = error as Error;
      logger.error(`Error initializing workflow`, {
        sessionId: this.sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Check if a transition is possible and execute it
   * @returns Promise<boolean> indicating if a transition was executed
   */
  public async checkAndTransition(): Promise<boolean> {
    try {
      // Get current state
      const state = await this.stateManager.getState();

      // Find applicable transitions
      const applicableTransitions = transitions.filter(
        transition => transition.from === state.currentPhase && transition.guard(state)
      );

      // Execute the first applicable transition
      if (applicableTransitions.length > 0) {
        const transition = applicableTransitions[0];

        logger.info(`Executing transition from ${transition.from} to ${transition.to}`, {
          sessionId: this.sessionId
        });

        return await transition.action(this.stateManager, state);
      }

      return false;
    } catch (error) {
      if (error instanceof StateNotFoundError) {
        logger.error(`Session not found`, { sessionId: this.sessionId });
        return false;
      }

      const err = error as Error;
      logger.error(`Error checking and executing transitions`, {
        sessionId: this.sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }
}
