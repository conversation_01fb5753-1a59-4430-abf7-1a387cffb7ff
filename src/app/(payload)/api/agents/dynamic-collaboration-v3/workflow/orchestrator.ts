/**
 * Workflow Orchestrator
 *
 * This file implements the workflow orchestrator for the dynamic collaboration system.
 * It coordinates the workflow state machine and agent interactions.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../collaborative-iteration/utils/logger';
import {
  WorkflowPhase,
  GoalType,
  GoalStatus,
  MessageType,
  ContentGenerationParams,
  SessionStatus
} from '../state/unified-schema';
import { StateManager } from '../state/manager';
import { StateNotFoundError, ConcurrencyError } from '../state/store';
import { WorkflowStateMachine } from './state-machine';

/**
 * Workflow Orchestrator
 *
 * This class orchestrates the dynamic collaboration workflow,
 * managing state transitions and agent interactions.
 */
export class WorkflowOrchestrator {
  private sessionId: string;
  private stateManager: StateManager;
  private stateMachine: WorkflowStateMachine;

  /**
   * Constructor
   * @param sessionId The session ID
   */
  constructor(sessionId: string) {
    this.sessionId = sessionId;
    this.stateManager = new StateManager(sessionId);
    this.stateMachine = new WorkflowStateMachine(sessionId);
  }

  /**
   * Initialize a new collaboration session
   * @param params Content generation parameters
   * @returns Promise<boolean> indicating success
   */
  public static async initiate(sessionId: string, params: ContentGenerationParams): Promise<boolean> {
    try {
      logger.info(`Initializing dynamic collaboration session`, {
        sessionId,
        topic: params.topic
      });

      // Create a new orchestrator instance
      const orchestrator = new WorkflowOrchestrator(sessionId);

      // Initialize the workflow
      const success = await orchestrator.stateMachine.initialize(params);

      if (!success) {
        throw new Error('Failed to initialize workflow');
      }

      // Trigger initial agent activities
      await orchestrator.triggerAgentActivities();

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error initializing dynamic collaboration session`, {
        sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Get the current state
   * @returns Promise<CollaborationState | null>
   */
  public async getState() {
    try {
      return await this.stateManager.getState();
    } catch (error) {
      if (error instanceof StateNotFoundError) {
        logger.error(`Session not found`, { sessionId: this.sessionId });
        return null;
      }

      const err = error as Error;
      logger.error(`Error getting state`, {
        sessionId: this.sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      return null;
    }
  }

  /**
   * Trigger agent activities based on the current phase and goals
   * @returns Promise<boolean> indicating success
   */
  public async triggerAgentActivities(): Promise<boolean> {
    try {
      // Get current state
      const state = await this.stateManager.getState();

      // Get active goals
      const activeGoals = state.activeGoalIds.map(id => state.goals[id]).filter(Boolean);

      // If no active goals, check if we can transition to the next phase
      if (activeGoals.length === 0) {
        return await this.stateMachine.checkAndTransition();
      }

      // Trigger agents based on goal types
      for (const goal of activeGoals) {
        switch (goal.type) {
          case GoalType.MARKET_RESEARCH:
            await this.triggerMarketResearchAgent(goal.id);
            break;

          case GoalType.KEYWORD_ANALYSIS:
            await this.triggerKeywordAnalysisAgent(goal.id);
            break;

          case GoalType.CONTENT_STRATEGY:
            await this.triggerContentStrategyAgent(goal.id);
            break;

          case GoalType.CONTENT_CREATION:
            await this.triggerContentCreationAgent(goal.id);
            break;

          case GoalType.SEO_OPTIMIZATION:
            await this.triggerSeoOptimizationAgent(goal.id);
            break;

          case GoalType.QUALITY_ASSESSMENT:
            await this.triggerQualityAssessmentAgent(goal.id);
            break;
        }
      }

      return true;
    } catch (error) {
      if (error instanceof StateNotFoundError) {
        logger.error(`Session not found`, { sessionId: this.sessionId });
        return false;
      }

      const err = error as Error;
      logger.error(`Error triggering agent activities`, {
        sessionId: this.sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Process a user message
   * @param content Message content
   * @returns Promise<string> message ID
   */
  public async processUserMessage(content: string): Promise<string> {
    try {
      // Add user message
      const messageId = await this.stateManager.addMessage({
        from: 'user',
        to: 'system',
        type: MessageType.USER_MESSAGE,
        content,
        conversationId: uuidv4()
      });

      // Trigger agent activities
      await this.triggerAgentActivities();

      return messageId;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error processing user message`, {
        sessionId: this.sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      throw error;
    }
  }

  /**
   * Complete the session
   * @returns Promise<boolean> indicating success
   */
  public async completeSession(): Promise<boolean> {
    try {
      return await this.stateManager.completeSession();
    } catch (error) {
      const err = error as Error;
      logger.error(`Error completing session`, {
        sessionId: this.sessionId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Trigger the market research agent
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  private async triggerMarketResearchAgent(goalId: string): Promise<boolean> {
    try {
      logger.info(`Triggering market research agent`, {
        sessionId: this.sessionId,
        goalId
      });

      // Assign goal to agent
      await this.stateManager.assignGoal(goalId, 'market-research');

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Import the OpenAI integration
      const { generateMarketResearch } = await import('../utils/openai-integration');

      // Generate market research content using OpenAI
      const marketResearchContent = await generateMarketResearch(
        state.topic,
        state.contentType,
        state.targetAudience
      );

      // Create the artifact with the generated content
      const artifactId = await this.stateManager.createArtifact(
        'market-research',
        'Market Research Report',
        marketResearchContent,
        'market-research',
        goalId
      );

      // Complete the goal
      await this.stateManager.completeGoal(goalId, artifactId);

      // Check if we can transition to the next phase
      await this.stateMachine.checkAndTransition();

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error triggering market research agent`, {
        sessionId: this.sessionId,
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Trigger the keyword analysis agent
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  private async triggerKeywordAnalysisAgent(goalId: string): Promise<boolean> {
    try {
      logger.info(`Triggering keyword analysis agent`, {
        sessionId: this.sessionId,
        goalId
      });

      // Assign goal to agent
      await this.stateManager.assignGoal(goalId, 'keyword-analysis');

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Import the OpenAI integration
      const { generateKeywordAnalysis } = await import('../utils/openai-integration');

      // Generate keyword analysis content using OpenAI
      const { content, keywords } = await generateKeywordAnalysis(
        state.topic,
        state.contentType,
        state.targetAudience
      );

      // Create the artifact with the generated content
      const artifactId = await this.stateManager.createArtifact(
        'keyword-analysis',
        'SEO Keyword Analysis',
        content,
        'keyword-analysis',
        goalId
      );

      // Update the state with the keywords
      await this.stateManager.updateKeywords(keywords);

      // Complete the goal
      await this.stateManager.completeGoal(goalId, artifactId);

      // Check if we can transition to the next phase
      await this.stateMachine.checkAndTransition();

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error triggering keyword analysis agent`, {
        sessionId: this.sessionId,
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Trigger the content strategy agent
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  private async triggerContentStrategyAgent(goalId: string): Promise<boolean> {
    try {
      logger.info(`Triggering content strategy agent`, {
        sessionId: this.sessionId,
        goalId
      });

      // Assign goal to agent
      await this.stateManager.assignGoal(goalId, 'content-strategy');

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Find market research and keyword analysis artifacts
      const marketResearchGoals = Object.values(state.goals).filter(
        goal => goal.type === GoalType.MARKET_RESEARCH && goal.status === GoalStatus.COMPLETED
      );

      const keywordAnalysisGoals = Object.values(state.goals).filter(
        goal => goal.type === GoalType.KEYWORD_ANALYSIS && goal.status === GoalStatus.COMPLETED
      );

      if (marketResearchGoals.length === 0 || !marketResearchGoals[0].artifactIds) {
        throw new Error('Market research artifact not found');
      }

      if (keywordAnalysisGoals.length === 0 || !keywordAnalysisGoals[0].artifactIds) {
        throw new Error('Keyword analysis artifact not found');
      }

      const marketResearchArtifactId = marketResearchGoals[0].artifactIds[0];
      const keywordAnalysisArtifactId = keywordAnalysisGoals[0].artifactIds[0];

      const marketResearchArtifact = state.artifacts[marketResearchArtifactId];
      const keywordAnalysisArtifact = state.artifacts[keywordAnalysisArtifactId];

      if (!marketResearchArtifact || !keywordAnalysisArtifact) {
        throw new Error('Required artifacts not found');
      }

      // Import the OpenAI integration
      const { generateContentStrategy } = await import('../utils/content-generation');

      // Generate content strategy using OpenAI
      const contentStrategyContent = await generateContentStrategy(
        state.topic,
        state.contentType,
        state.targetAudience,
        state.tone,
        marketResearchArtifact,
        keywordAnalysisArtifact
      );

      // Create the artifact with the generated content
      const artifactId = await this.stateManager.createArtifact(
        'content-strategy',
        'Content Strategy Plan',
        contentStrategyContent,
        'content-strategy',
        goalId
      );

      // Complete the goal
      await this.stateManager.completeGoal(goalId, artifactId);

      // Check if we can transition to the next phase
      await this.stateMachine.checkAndTransition();

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error triggering content strategy agent`, {
        sessionId: this.sessionId,
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Trigger the content creation agent
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  private async triggerContentCreationAgent(goalId: string): Promise<boolean> {
    try {
      logger.info(`Triggering content creation agent`, {
        sessionId: this.sessionId,
        goalId
      });

      // Assign goal to agent
      await this.stateManager.assignGoal(goalId, 'content-creation');

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Find content strategy and keyword analysis artifacts
      const contentStrategyGoals = Object.values(state.goals).filter(
        goal => goal.type === GoalType.CONTENT_STRATEGY && goal.status === GoalStatus.COMPLETED
      );

      const keywordAnalysisGoals = Object.values(state.goals).filter(
        goal => goal.type === GoalType.KEYWORD_ANALYSIS && goal.status === GoalStatus.COMPLETED
      );

      if (contentStrategyGoals.length === 0 || !contentStrategyGoals[0].artifactIds) {
        throw new Error('Content strategy artifact not found');
      }

      if (keywordAnalysisGoals.length === 0 || !keywordAnalysisGoals[0].artifactIds) {
        throw new Error('Keyword analysis artifact not found');
      }

      const contentStrategyArtifactId = contentStrategyGoals[0].artifactIds[0];
      const keywordAnalysisArtifactId = keywordAnalysisGoals[0].artifactIds[0];

      const contentStrategyArtifact = state.artifacts[contentStrategyArtifactId];
      const keywordAnalysisArtifact = state.artifacts[keywordAnalysisArtifactId];

      if (!contentStrategyArtifact || !keywordAnalysisArtifact) {
        throw new Error('Required artifacts not found');
      }

      // Import the OpenAI integration
      const { generateArticleContent } = await import('../utils/content-generation');

      // Generate article content using OpenAI
      const { title, content } = await generateArticleContent(
        state.topic,
        state.contentType,
        state.targetAudience,
        state.tone,
        contentStrategyArtifact,
        keywordAnalysisArtifact
      );

      // Create the artifact with the generated content
      const artifactId = await this.stateManager.createArtifact(
        'content-creation',
        title,
        content,
        'content-creation',
        goalId
      );

      // Complete the goal
      await this.stateManager.completeGoal(goalId, artifactId);

      // Check if we can transition to the next phase
      await this.stateMachine.checkAndTransition();

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error triggering content creation agent`, {
        sessionId: this.sessionId,
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Trigger the SEO optimization agent
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  private async triggerSeoOptimizationAgent(goalId: string): Promise<boolean> {
    try {
      logger.info(`Triggering SEO optimization agent`, {
        sessionId: this.sessionId,
        goalId
      });

      // Assign goal to agent
      await this.stateManager.assignGoal(goalId, 'seo-optimization');

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Find content creation artifact
      const contentCreationGoals = Object.values(state.goals).filter(
        goal => goal.type === GoalType.CONTENT_CREATION && goal.status === GoalStatus.COMPLETED
      );

      if (contentCreationGoals.length === 0 || !contentCreationGoals[0].artifactIds) {
        throw new Error('Content creation artifact not found');
      }

      const contentArtifactId = contentCreationGoals[0].artifactIds[0];
      const contentArtifact = state.artifacts[contentArtifactId];

      if (!contentArtifact) {
        throw new Error('Content creation artifact not found');
      }

      // Import the OpenAI integration
      const { optimizeContentForSEO } = await import('../utils/content-generation');

      // Get the title and content from the content creation artifact
      let title = '';
      let content = '';

      if (typeof contentArtifact.content === 'string') {
        // If content is a string, extract title from the first line (assuming markdown)
        const lines = contentArtifact.content.split('\n');
        if (lines[0].startsWith('# ')) {
          title = lines[0].substring(2);
          content = contentArtifact.content;
        } else {
          title = contentArtifact.title;
          content = contentArtifact.content;
        }
      } else if (typeof contentArtifact.content === 'object') {
        // If content is an object, extract title and content fields
        title = contentArtifact.content.title || contentArtifact.title;
        content = contentArtifact.content.content || JSON.stringify(contentArtifact.content);
      } else {
        title = contentArtifact.title;
        content = JSON.stringify(contentArtifact.content);
      }

      // Optimize content for SEO using OpenAI
      const { title: optimizedTitle, content: optimizedContent } = await optimizeContentForSEO(
        title,
        content,
        state.keywords
      );

      // Create the artifact with the optimized content
      const artifactId = await this.stateManager.createArtifact(
        'seo-optimization',
        optimizedTitle,
        optimizedContent,
        'seo-optimization',
        goalId
      );

      // Complete the goal
      await this.stateManager.completeGoal(goalId, artifactId);

      // Check if we can transition to the next phase
      await this.stateMachine.checkAndTransition();

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error triggering SEO optimization agent`, {
        sessionId: this.sessionId,
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Trigger the quality assessment agent
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  private async triggerQualityAssessmentAgent(goalId: string): Promise<boolean> {
    try {
      logger.info(`Triggering quality assessment agent`, {
        sessionId: this.sessionId,
        goalId
      });

      // Assign goal to agent
      await this.stateManager.assignGoal(goalId, 'quality-assessment');

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Find SEO optimization artifact
      const seoOptimizationGoals = Object.values(state.goals).filter(
        goal => goal.type === GoalType.SEO_OPTIMIZATION && goal.status === GoalStatus.COMPLETED
      );

      if (seoOptimizationGoals.length === 0 || !seoOptimizationGoals[0].artifactIds) {
        throw new Error('SEO optimization artifact not found');
      }

      const seoArtifactId = seoOptimizationGoals[0].artifactIds[0];
      const seoArtifact = state.artifacts[seoArtifactId];

      if (!seoArtifact) {
        throw new Error('SEO optimization artifact not found');
      }

      // Create the feedback loop system
      const feedbackSystem = new (await import('../utils/feedback-loop-system')).FeedbackLoopSystem(this.sessionId);

      // Generate feedback on the SEO-optimized content
      const feedback = await feedbackSystem.generateFeedback(
        seoArtifactId,
        'quality-assessment'
      );

      // Create a quality assessment artifact
      const artifactId = await this.stateManager.createArtifact(
        'quality-assessment',
        'Quality Assessment Report',
        {
          contentQuality: {
            accuracy: feedback.overallRating,
            relevance: feedback.overallRating - 5,
            comprehensiveness: feedback.overallRating - 10,
            clarity: feedback.overallRating - 3,
            engagement: feedback.overallRating - 7,
            overall: feedback.overallRating
          },
          strengths: feedback.strengths,
          areasForImprovement: feedback.areasForImprovement,
          specificFeedback: feedback.specificFeedback,
          summary: feedback.summary,
          finalVerdict: feedback.overallRating >= 80
            ? 'Approved for publication with minor improvements'
            : 'Needs significant improvements before publication'
        },
        'quality-assessment',
        goalId
      );

      // If the quality is good enough, incorporate the feedback
      if (feedback.overallRating >= 80) {
        // Create a feedback request
        const requestId = await feedbackSystem.requestFeedback(
          'quality-assessment',
          'seo-optimization',
          seoArtifactId,
          ['content quality', 'SEO optimization', 'readability']
        );

        // Provide feedback
        const responseId = await feedbackSystem.provideFeedback(
          'quality-assessment',
          'seo-optimization',
          requestId,
          feedback
        );

        // Incorporate feedback
        await feedbackSystem.incorporateFeedback(
          seoArtifactId,
          [responseId],
          'seo-optimization'
        );
      }

      // Complete the goal
      await this.stateManager.completeGoal(goalId, artifactId);

      // Check if we can transition to the next phase
      await this.stateMachine.checkAndTransition();

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error triggering quality assessment agent`, {
        sessionId: this.sessionId,
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }
}
