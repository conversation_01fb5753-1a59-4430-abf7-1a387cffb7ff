/**
 * Dynamic Collaboration V3 Goal-Based API Route
 *
 * This file implements the API routes for the goal-based dynamic collaboration system.
 */

import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import { GoalOrchestrator } from '../workflow/goal-orchestrator';
import { StateManager } from '../state/manager';
import { ContentGenerationParams } from '../state/unified-schema';
import { z } from 'zod';

/**
 * GET /api/agents/dynamic-collaboration-v3/goal-based
 *
 * Get the state of a goal-based dynamic collaboration session
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Get session ID from query params
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing sessionId parameter' },
        { status: 400 }
      );
    }

    // Get session state
    const stateManager = new StateManager(sessionId);
    const state = await stateManager.getState();

    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Return session state
    return NextResponse.json(state);
  } catch (error) {
    const err = error as Error;
    logger.error(`Error getting goal-based dynamic collaboration session`, {
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/agents/dynamic-collaboration-v3/goal-based
 *
 * Create a new goal-based dynamic collaboration session
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Create a new session
    // Parse request body
    const body = await request.json();

    // Validate request body
    const schema = z.object({
      topic: z.string().min(1),
      contentType: z.enum([
        'blog-article',
        'product-page',
        'buying-guide',
        'how-to-guide',
        'listicle',
        'case-study',
        'opinion-piece',
        'technical-tutorial'
      ]),
      targetAudience: z.string().min(1),
      tone: z.string().min(1),
      keywords: z.array(z.string()).optional(),
      additionalInstructions: z.string().optional(),
      referenceUrls: z.array(z.string()).optional()
    });

    const result = schema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: result.error.format() },
        { status: 400 }
      );
    }

    // Create session ID
    const sessionId = uuidv4();

    // Create content generation params
    const params: ContentGenerationParams = {
      topic: body.topic,
      contentType: body.contentType,
      targetAudience: body.targetAudience,
      tone: body.tone,
      keywords: body.keywords || [],
      additionalInstructions: body.additionalInstructions,
      referenceUrls: body.referenceUrls
    };

    // Initialize session
    const success = await GoalOrchestrator.initiate(sessionId, params);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to initialize session' },
        { status: 500 }
      );
    }

    // Return session ID
    return NextResponse.json({ sessionId });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error handling goal-based dynamic collaboration request`, {
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
