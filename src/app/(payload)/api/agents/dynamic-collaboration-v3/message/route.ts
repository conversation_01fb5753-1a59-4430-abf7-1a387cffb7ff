/**
 * Dynamic Collaboration V3 Message API
 *
 * This file implements the API endpoint for sending messages to a dynamic collaboration session.
 * It allows the frontend to send user messages to the agents.
 */

import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import { StateManager } from '../state/manager';
import { MessageType } from '../state/unified-schema';
import { enhancedMessageBus } from '../../../agents/collaborative-iteration/utils/enhanced-message-bus';

/**
 * POST /api/agents/dynamic-collaboration-v3/message
 * Send a message to a dynamic collaboration session
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Parse request body
    const body = await req.json();
    const { sessionId, content } = body;

    // Validate required fields
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required field: sessionId' },
        { status: 400 }
      );
    }

    if (!content) {
      return NextResponse.json(
        { error: 'Missing required field: content' },
        { status: 400 }
      );
    }

    // Get state manager
    const stateManager = new StateManager(sessionId);

    // Get current state
    const state = await stateManager.getState();
    if (!state) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Create message ID
    const messageId = uuidv4();
    const conversationId = uuidv4();
    const now = new Date().toISOString();

    // Add message to state
    await stateManager.updateState(currentState => {
      if (!currentState) return currentState;

      // Add message
      const messages = { ...currentState.messages };
      messages[messageId] = {
        id: messageId,
        timestamp: now,
        from: 'user',
        to: 'all',
        type: MessageType.USER,
        content: { text: content },
        conversationId
      };

      // Add to conversation mapping
      const conversations = { ...currentState.conversations };
      conversations[conversationId] = [messageId];

      // Add to message pages if they exist
      let messagePages = currentState.messagePages;
      if (messagePages) {
        messagePages = { ...messagePages };
        const pageKeys = Object.keys(messagePages).sort((a, b) => parseInt(b) - parseInt(a));
        const latestPageKey = pageKeys.length > 0 ? pageKeys[0] : '1';
        const latestPage = messagePages[latestPageKey] || [];

        // Check if the latest page is full
        if (latestPage.length >= 50) {
          // Create a new page
          const newPageKey = (parseInt(latestPageKey) + 1).toString();
          messagePages[newPageKey] = [messageId];
        } else {
          // Add to the latest page
          messagePages[latestPageKey] = [...latestPage, messageId];
        }
      }

      return {
        ...currentState,
        messages,
        conversations,
        messagePages
      };
    });

    // Send message to message bus
    const messageBus = enhancedMessageBus.createSessionBus(sessionId);
    await messageBus.sendMessage({
      id: messageId,
      timestamp: now,
      from: 'user',
      to: 'all',
      type: 'USER', // Use a string instead of enum to avoid dependency issues
      content: { text: content },
      conversationId
    });

    // Return success
    return NextResponse.json({
      success: true,
      messageId,
      conversationId
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error sending message to dynamic collaboration session`, {
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
