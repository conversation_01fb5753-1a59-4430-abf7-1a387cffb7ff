/**
 * Dynamic Collaboration V3 Progress API (Fixed Version)
 *
 * This file implements the API endpoint for progressing a dynamic collaboration session.
 * It includes enhanced error handling and detailed logging.
 */

import { NextRequest, NextResponse } from 'next/server';
import logger from '../../../utils/logger';
import { GoalOrchestrator } from '../workflow/goal-orchestrator';
import { StateManager } from '../state/manager';
import { GoalType, GoalStatus, WorkflowPhase } from '../state/unified-schema';
import { v4 as uuidv4 } from 'uuid';
import { GoalProcessorFixed } from '../workflow/goal-processor-fixed';

/**
 * POST /api/agents/dynamic-collaboration-v3/progress-fixed
 * Progress a dynamic collaboration session
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Parse request body
    const body = await req.json();
    const { sessionId, steps = 1 } = body;

    // Log the request
    logger.info(`Progress request received`, {
      sessionId,
      steps
    });

    // Validate required fields
    if (!sessionId) {
      logger.warn(`Missing required field: sessionId`);
      return NextResponse.json(
        { error: 'Missing required field: sessionId' },
        { status: 400 }
      );
    }

    // Verify session exists
    const stateManager = new StateManager(sessionId);
    const state = await stateManager.getState();

    if (!state) {
      logger.warn(`Session not found: ${sessionId}`);
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Check if goals are defined, if not, create them
    const hasGoals = state.goals &&
                    state.goals.byId &&
                    Object.keys(state.goals.byId).length > 0;

    if (!hasGoals) {
      logger.info(`No goals defined for session: ${sessionId}, creating initial goals`);
      await fixGoals(sessionId, stateManager);
    }

    // Create goal processor
    logger.info(`Creating goal processor for session: ${sessionId}`);
    const goalProcessor = new GoalProcessorFixed(sessionId);

    // Process goals with detailed error handling
    let success = false;
    let error = null;

    try {
      logger.info(`Processing goals for session: ${sessionId}, steps: ${steps}`);

      for (let i = 0; i < steps; i++) {
        logger.info(`Processing step ${i + 1} of ${steps}`);

        // Use our fixed goal processor instead of the orchestrator
        success = await goalProcessor.processGoals();

        if (!success) {
          logger.warn(`Failed to process goals at step ${i + 1}`);
          break;
        }
      }
    } catch (err) {
      error = err;
      logger.error(`Error processing goals`, {
        sessionId,
        error: err instanceof Error ? err.message : String(err),
        stack: err instanceof Error ? err.stack : undefined
      });
    }

    if (error) {
      return NextResponse.json(
        {
          error: 'Error processing goals',
          details: error instanceof Error ? error.message : String(error)
        },
        { status: 500 }
      );
    }

    if (!success) {
      return NextResponse.json(
        {
          error: 'Failed to progress session',
          details: 'The orchestrator reported failure but did not throw an exception'
        },
        { status: 500 }
      );
    }

    // Get updated state
    logger.info(`Getting updated state for session: ${sessionId}`);
    const updatedState = await stateManager.getState();

    // Return updated state
    logger.info(`Progress successful for session: ${sessionId}`);
    return NextResponse.json({
      success: true,
      state: updatedState
    });
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error(`Unhandled error progressing dynamic collaboration session`, {
      error: err.message,
      stack: err.stack
    });

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: err.message
      },
      { status: 500 }
    );
  }
}

/**
 * Fix goals by manually creating initial goals
 */
async function fixGoals(sessionId: string, stateManager: StateManager): Promise<any> {
  try {
    // Get current state
    const state = await stateManager.getState();
    if (!state) {
      throw new Error('Session state not found');
    }

    // Check if goals are already defined
    const hasGoals = state.goals &&
                    state.goals.byId &&
                    Object.keys(state.goals.byId).length > 0;

    if (hasGoals) {
      return { message: 'Goals already defined', goalsCount: Object.keys(state.goals.byId).length };
    }

    // Create initial goals
    const now = new Date().toISOString();

    // Research goal
    const researchGoalId = uuidv4();
    const researchGoal = {
      id: researchGoalId,
      type: GoalType.RESEARCH,
      description: `Research ${state.topic} for ${state.contentType}`,
      criteria: [
        'Gather comprehensive information about the topic',
        'Identify target audience needs and interests',
        'Analyze market trends and competition',
        'Identify key keywords and search terms'
      ],
      status: GoalStatus.ACTIVE,
      progress: 0,
      dependencies: [],
      createdAt: now,
      version: 1
    };

    // Content goal
    const contentGoalId = uuidv4();
    const contentGoal = {
      id: contentGoalId,
      type: GoalType.CONTENT,
      description: `Create content for ${state.topic}`,
      criteria: [
        'Develop a content strategy',
        'Create engaging and informative content',
        'Ensure content meets target audience needs',
        'Optimize content for search engines'
      ],
      status: GoalStatus.PENDING,
      progress: 0,
      dependencies: [researchGoalId],
      createdAt: now,
      version: 1
    };

    // Quality goal
    const qualityGoalId = uuidv4();
    const qualityGoal = {
      id: qualityGoalId,
      type: GoalType.QUALITY,
      description: `Ensure quality of content for ${state.topic}`,
      criteria: [
        'Verify content accuracy and completeness',
        'Check grammar and spelling',
        'Ensure content is engaging and well-structured',
        'Validate SEO optimization'
      ],
      status: GoalStatus.PENDING,
      progress: 0,
      dependencies: [contentGoalId],
      createdAt: now,
      version: 1
    };

    // Update state with new goals
    await stateManager.updateState(currentState => {
      if (!currentState) return currentState;

      return {
        ...currentState,
        goals: {
          byId: {
            [researchGoalId]: researchGoal,
            [contentGoalId]: contentGoal,
            [qualityGoalId]: qualityGoal
          },
          allIds: [researchGoalId, contentGoalId, qualityGoalId],
          activeIds: [researchGoalId],
          completedIds: []
        },
        currentPhase: WorkflowPhase.PLANNING,
        workflowProgress: {
          ...currentState.workflowProgress,
          status: 'in_progress',
          currentPhase: WorkflowPhase.PLANNING,
          phaseProgress: {
            ...currentState.workflowProgress.phaseProgress,
            [WorkflowPhase.PLANNING]: 25
          },
          overallProgress: 5,
          lastUpdated: now
        },
        lastUpdated: now
      };
    });

    return {
      message: 'Goals fixed successfully',
      goals: {
        research: researchGoalId,
        content: contentGoalId,
        quality: qualityGoalId
      }
    };
  } catch (error) {
    logger.error(`Error fixing goals`, {
      sessionId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}
