/**
 * SEO Optimization Agent
 *
 * This file implements the specialized SEO optimization agent for the goal-based orchestration system.
 * It handles SEO optimization goals and produces SEO-optimized content artifacts.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import {
  MessageType,
  Message,
  Goal,
  GoalStatus,
  GoalType,
  CollaborationState,
  Artifact
} from '../state/unified-schema';
import { StateManager } from '../state/manager';
import { ArtifactDecisionFramework, ArtifactDecisionType } from '../services/artifact-decision-framework';
import { FeedbackLoopSystem } from '../utils/feedback-loop-system-new';
import { AgentBase } from './agent-base';

/**
 * SEO Optimization Agent
 */
export class SEOOptimizationAgent extends AgentBase {
  /**
   * Constructor
   * @param sessionId Session ID
   */
  constructor(sessionId: string) {
    super('seo-optimization', sessionId);
    logger.info(`SEO Optimization Agent initialized for session ${sessionId}`);
  }

  /**
   * Register message handlers
   */
  protected registerHandlers(): void {
    this.registerHandler(MessageType.GOAL_ASSIGNMENT, this.handleGoalAssignment.bind(this));
    this.registerHandler(MessageType.ARTIFACT_REQUEST, this.handleArtifactRequest.bind(this));
    this.registerHandler(MessageType.FEEDBACK, this.handleFeedback.bind(this));
    this.registerHandler(MessageType.CONSULTATION_REQUEST, this.handleConsultationRequest.bind(this));
    this.registerHandler(MessageType.REQUEST, this.handleGoalAssignment.bind(this));
    this.registerHandler(MessageType.INITIAL_REQUEST, this.handleGoalAssignment.bind(this));
  }

  /**
   * Process a goal
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  public async processGoal(goalId: string): Promise<boolean> {
    try {
      logger.info(`SEO Optimization Agent processing goal ${goalId}`, {
        sessionId: this.getSessionId(),
        goalId
      });

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Get the goal from the byId object
      const goal = state.goals.byId[goalId];
      if (!goal) {
        throw new Error(`Goal ${goalId} not found`);
      }

      // Assign goal to agent
      await this.stateManager.assignGoal(goalId, this.agentId);

      // Make a decision about artifact creation
      const decision = await this.decisionFramework.decideArtifactCreation('seo-optimization', goalId);

      // If we should use an existing artifact
      if (decision.type === ArtifactDecisionType.USE_EXISTING && decision.existingArtifactId) {
        // Complete the goal with the existing artifact
        await this.stateManager.completeGoal(goalId, decision.existingArtifactId);
        return true;
      }

      // Find content creation and keyword analysis artifacts
      const contentCreationArtifacts = Object.values(state.artifacts).filter(
        artifact => artifact.type === 'content-creation'
      );

      const keywordAnalysisArtifacts = Object.values(state.artifacts).filter(
        artifact => artifact.type === 'keyword-analysis'
      );

      // If content creation artifact is not found, create a dummy one
      if (contentCreationArtifacts.length === 0) {
        logger.info(`Content creation artifact not found, creating a dummy one`, {
          sessionId: this.getSessionId(),
          goalId
        });

        // Create a dummy content creation artifact
        const contentGoalId = uuidv4();
        const contentTitle = `${state.topic} - Draft Article`;
        const contentBody = `This is a draft article about ${state.topic}. It will be expanded with more detailed content.`;

        // Create the artifact
        const contentArtifactId = await this.stateManager.createArtifact(
          'content-creation',
          contentTitle,
          {
            title: contentTitle,
            content: contentBody,
            sections: [
              {
                title: 'Introduction',
                content: `Introduction to ${state.topic}`
              },
              {
                title: 'Main Content',
                content: `Main content about ${state.topic}`
              },
              {
                title: 'Conclusion',
                content: `Conclusion about ${state.topic}`
              }
            ]
          },
          'content-creation',
          contentGoalId
        );

        // Add the new artifact to the list
        contentCreationArtifacts.push(state.artifacts[contentArtifactId]);
      }

      // If keyword analysis artifact is not found, create a dummy one
      if (keywordAnalysisArtifacts.length === 0) {
        logger.info(`Keyword analysis artifact not found, creating a dummy one`, {
          sessionId: this.getSessionId(),
          goalId
        });

        // Create a dummy keyword analysis artifact
        const keywordGoalId = uuidv4();
        const keywordTitle = `${state.topic} - Keyword Analysis`;
        const keywordContent = {
          primaryKeywords: [`${state.topic}`, `${state.topic} guide`, `${state.topic} tutorial`],
          secondaryKeywords: [`best ${state.topic}`, `${state.topic} examples`, `${state.topic} tips`],
          relatedTerms: [`${state.topic} strategies`, `${state.topic} tools`, `${state.topic} benefits`]
        };

        // Create the artifact
        const keywordArtifactId = await this.stateManager.createArtifact(
          'keyword-analysis',
          keywordTitle,
          keywordContent,
          'keyword-analysis',
          keywordGoalId
        );

        // Add the new artifact to the list
        keywordAnalysisArtifacts.push(state.artifacts[keywordArtifactId]);
      }

      // Get the most recent artifacts
      const contentCreationArtifact = contentCreationArtifacts.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      const keywordAnalysisArtifact = keywordAnalysisArtifacts.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      // If we should improve an existing artifact
      if (decision.type === ArtifactDecisionType.IMPROVE_EXISTING && decision.existingArtifactId) {
        // Get the existing artifact
        const existingArtifact = state.artifacts[decision.existingArtifactId];
        if (!existingArtifact) {
          throw new Error(`Artifact ${decision.existingArtifactId} not found`);
        }

        // Import the OpenAI integration
        const { optimizeContentForSEO } = await import('../utils/content-generation');

        // Extract title and content from the content creation artifact
        let contentTitle = '';
        let contentText = '';

        // Handle different content structures
        if (typeof contentCreationArtifact.content === 'string') {
          contentText = contentCreationArtifact.content;
          contentTitle = contentCreationArtifact.title || state.topic;
        } else if (typeof contentCreationArtifact.content === 'object' && contentCreationArtifact.content !== null) {
          contentTitle = contentCreationArtifact.content.title || contentCreationArtifact.title || state.topic;
          contentText = contentCreationArtifact.content.content ||
                       (contentCreationArtifact.content.sections ?
                         contentCreationArtifact.content.sections.map(s => `## ${s.title}\n\n${s.content}`).join('\n\n') :
                         JSON.stringify(contentCreationArtifact.content));
        } else {
          contentTitle = contentCreationArtifact.title || state.topic;
          contentText = `# ${contentTitle}\n\nThis is a draft article about ${state.topic}.`;
        }

        // Extract keyword content
        let keywordContent = keywordAnalysisArtifact.content;
        if (typeof keywordContent === 'object' && keywordContent !== null) {
          keywordContent = keywordContent.keywords ||
                          keywordContent.text ||
                          JSON.stringify(keywordContent);
        }

        // Generate improved SEO-optimized content using OpenAI
        const { title, content, metaDescription, keywordDensity } = await optimizeContentForSEO(
          contentTitle,
          contentText,
          keywordContent,
          state.topic,
          state.targetAudience
        );

        // Create the improved artifact
        const artifactId = await this.stateManager.createArtifact(
          'seo-optimization',
          title,
          {
            content,
            metaDescription,
            keywordDensity,
            originalContent: contentCreationArtifact.content
          },
          'seo-optimization',
          goalId,
          decision.existingArtifactId
        );

        // Initialize feedback loop system
        const feedbackSystem = new FeedbackLoopSystem(this.getSessionId());

        // Generate feedback on the SEO optimization
        const feedback = await feedbackSystem.generateFeedback(
          artifactId,
          'quality-assessment',
          ['keyword optimization', 'meta description', 'content structure']
        );

        // Create a feedback request
        const requestId = await feedbackSystem.requestFeedback(
          'seo-optimization',
          'quality-assessment',
          artifactId,
          ['keyword optimization', 'meta description', 'content structure']
        );

        // Provide feedback
        await feedbackSystem.provideFeedback(
          'quality-assessment',
          'seo-optimization',
          requestId,
          feedback
        );

        // Complete the goal
        await this.stateManager.completeGoal(goalId, artifactId);
        return true;
      }

      // Otherwise, create a new artifact
      // Import the OpenAI integration
      const { optimizeContentForSEO } = await import('../utils/content-generation');

      // Extract title and content from the content creation artifact
      let contentTitle = '';
      let contentText = '';

      // Handle different content structures
      if (typeof contentCreationArtifact.content === 'string') {
        contentText = contentCreationArtifact.content;
        contentTitle = contentCreationArtifact.title || state.topic;
      } else if (typeof contentCreationArtifact.content === 'object' && contentCreationArtifact.content !== null) {
        contentTitle = contentCreationArtifact.content.title || contentCreationArtifact.title || state.topic;
        contentText = contentCreationArtifact.content.content ||
                     (contentCreationArtifact.content.sections ?
                       contentCreationArtifact.content.sections.map(s => `## ${s.title}\n\n${s.content}`).join('\n\n') :
                       JSON.stringify(contentCreationArtifact.content));
      } else {
        contentTitle = contentCreationArtifact.title || state.topic;
        contentText = `# ${contentTitle}\n\nThis is a draft article about ${state.topic}.`;
      }

      // Extract keyword content
      let keywordContent = keywordAnalysisArtifact.content;
      if (typeof keywordContent === 'object' && keywordContent !== null) {
        keywordContent = keywordContent.keywords ||
                        keywordContent.text ||
                        JSON.stringify(keywordContent);
      }

      // Generate SEO-optimized content using OpenAI
      const { title, content, metaDescription, keywordDensity } = await optimizeContentForSEO(
        contentTitle,
        contentText,
        keywordContent,
        state.topic,
        state.targetAudience
      );

      // Create the artifact with the generated content
      const artifactId = await this.stateManager.createArtifact(
        'seo-optimization',
        title,
        {
          content,
          metaDescription,
          keywordDensity,
          originalContent: contentCreationArtifact.content
        },
        'seo-optimization',
        goalId
      );

      // Initialize feedback loop system
      const feedbackSystem = new FeedbackLoopSystem(this.getSessionId());

      // Generate feedback on the SEO optimization
      const feedback = await feedbackSystem.generateFeedback(
        artifactId,
        'quality-assessment',
        ['keyword optimization', 'meta description', 'content structure']
      );

      // Create a feedback request
      const requestId = await feedbackSystem.requestFeedback(
        'seo-optimization',
        'quality-assessment',
        artifactId,
        ['keyword optimization', 'meta description', 'content structure']
      );

      // Provide feedback
      await feedbackSystem.provideFeedback(
        'quality-assessment',
        'seo-optimization',
        requestId,
        feedback
      );

      // Complete the goal
      await this.stateManager.completeGoal(goalId, artifactId);

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error processing SEO optimization goal`, {
        sessionId: this.getSessionId(),
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Handle goal assignment message
   * @param message Goal assignment message
   * @param state Current state
   * @param stateManager State manager
   * @returns Promise<Message | null> Response message or null
   */
  private async handleGoalAssignment(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    try {
      // Extract goal ID from message content
      let goalId = message.content?.goalId;

      // If no goalId in content, check if there's a goal in the state that needs processing
      if (!goalId && message.type === MessageType.INITIAL_REQUEST) {
        // Find an active goal assigned to this agent
        const activeGoals = Object.values(state.goals.byId).filter(
          goal => goal.status === GoalStatus.ACTIVE && goal.assignedTo === this.agentId
        );

        if (activeGoals.length > 0) {
          goalId = activeGoals[0].id;
        } else {
          // Create a new goal if none exists
          const newGoal = {
            description: `Optimize content for SEO for ${state.topic}`,
            type: GoalType.SEO_OPTIMIZATION,
            dependencies: [],
            criteria: [
              'Optimize title and headings',
              'Ensure proper keyword density',
              'Add meta description',
              'Improve readability'
            ]
          };

          const goals = await stateManager.defineGoals([newGoal]);
          goalId = goals[0];
        }
      }

      if (!goalId) {
        throw new Error('Goal ID not provided and no active goals found');
      }

      // Process the goal
      await this.processGoal(goalId);

      // Send acknowledgment
      return this.createMessage(
        message.from,
        MessageType.ACKNOWLEDGMENT,
        {
          goalId,
          status: 'processing',
          message: `SEO Optimization Agent is processing goal ${goalId}`
        },
        message.conversationId,
        message.id
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling goal assignment`, {
        sessionId: this.getSessionId(),
        error: err.message || String(error),
        stack: err.stack
      });

      // Send error message
      return this.createMessage(
        message.from,
        MessageType.ERROR,
        {
          error: err.message || String(error),
          originalMessage: message.id
        },
        message.conversationId,
        message.id
      );
    }
  }

  /**
   * Handle artifact request message
   * @param message Artifact request message
   * @param state Current state
   * @param stateManager State manager
   * @returns Promise<Message | null> Response message or null
   */
  private async handleArtifactRequest(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    try {
      // Find the most recent SEO optimization artifact
      const seoArtifacts = Object.values(state.artifacts).filter(
        artifact => artifact.type === 'seo-optimization'
      );

      if (seoArtifacts.length === 0) {
        // No artifact found, create one
        const contentCreationArtifacts = Object.values(state.artifacts).filter(
          artifact => artifact.type === 'content-creation'
        );

        if (contentCreationArtifacts.length === 0) {
          throw new Error('Content creation artifact required but not found');
        }

        const newGoal = {
          description: `Optimize content for SEO for ${state.topic}`,
          type: GoalType.SEO_OPTIMIZATION,
          dependencies: [],
          criteria: [
            'Optimize title and headings',
            'Ensure proper keyword density',
            'Add meta description',
            'Improve readability'
          ]
        };

        const goals = await stateManager.defineGoals([newGoal]);
        await this.processGoal(goals[0]);

        // Get the artifact we just created
        const updatedState = await stateManager.getState();
        const newArtifacts = Object.values(updatedState.artifacts).filter(
          artifact => artifact.type === 'seo-optimization'
        );

        if (newArtifacts.length === 0) {
          throw new Error('Failed to create SEO optimization artifact');
        }

        // Return the artifact
        return this.createMessage(
          message.from,
          MessageType.ARTIFACT_DELIVERY,
          {
            artifactId: newArtifacts[0].id,
            artifactType: 'seo-optimization',
            content: newArtifacts[0].content,
            title: newArtifacts[0].title
          },
          message.conversationId,
          message.id
        );
      }

      // Get the most recent artifact
      const artifact = seoArtifacts.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      // Return the artifact
      return this.createMessage(
        message.from,
        MessageType.ARTIFACT_DELIVERY,
        {
          artifactId: artifact.id,
          artifactType: 'seo-optimization',
          content: artifact.content,
          title: artifact.title
        },
        message.conversationId,
        message.id
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling artifact request`, {
        sessionId: this.getSessionId(),
        error: err.message || String(error),
        stack: err.stack
      });

      return this.createMessage(
        message.from,
        MessageType.ERROR,
        {
          error: err.message || String(error),
          originalMessage: message.id
        },
        message.conversationId,
        message.id
      );
    }
  }

  /**
   * Handle feedback message
   * @param message Feedback message
   * @param state Current state
   * @param stateManager State manager
   * @returns Promise<Message | null> Response message or null
   */
  private async handleFeedback(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    try {
      const { feedback, artifactId } = message.content;

      if (!feedback || !artifactId) {
        throw new Error('Missing required fields: feedback, artifactId');
      }

      // Get the artifact
      const artifact = state.artifacts[artifactId];
      if (!artifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }

      // Initialize feedback loop system
      const feedbackSystem = new FeedbackLoopSystem(this.getSessionId());

      // Process the feedback
      await feedbackSystem.processFeedback(artifactId, feedback);

      // Check if we need to improve the artifact
      if (feedback.improvementNeeded) {
        // Create a new goal to improve the artifact
        const newGoal = {
          description: `Improve SEO optimization for ${state.topic} based on feedback`,
          type: GoalType.SEO_OPTIMIZATION,
          dependencies: [],
          criteria: [
            'Address feedback points',
            'Improve keyword optimization',
            'Enhance meta description',
            'Optimize content structure'
          ]
        };

        const goals = await stateManager.defineGoals([newGoal]);
        await this.processGoal(goals[0]);
      }

      // Send acknowledgment
      return this.createMessage(
        message.from,
        MessageType.ACKNOWLEDGMENT,
        {
          message: `Feedback received and processed for artifact ${artifactId}`,
          status: 'success'
        },
        message.conversationId,
        message.id
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling feedback`, {
        sessionId: this.getSessionId(),
        error: err.message || String(error),
        stack: err.stack
      });

      return this.createMessage(
        message.from,
        MessageType.ERROR,
        {
          error: err.message || String(error),
          originalMessage: message.id
        },
        message.conversationId,
        message.id
      );
    }
  }

  /**
   * Handle consultation request message
   * @param message Consultation request message
   * @param state Current state
   * @param stateManager State manager
   * @returns Promise<Message | null> Response message or null
   */
  private async handleConsultationRequest(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    try {
      const { question, context } = message.content;

      if (!question) {
        throw new Error('Missing required field: question');
      }

      // Generate a response based on the question and context
      const response = `SEO Optimization Agent consultation response to: "${question}"\n\n` +
        `Based on our SEO optimization process for ${state.topic}, we can provide the following insights:\n\n` +
        `1. We've optimized the title and headings for better search engine visibility\n` +
        `2. We've ensured proper keyword density throughout the content\n` +
        `3. We've added a compelling meta description to improve click-through rates\n` +
        `4. We've improved readability while maintaining keyword optimization`;

      // Send consultation response
      return this.createMessage(
        message.from,
        MessageType.CONSULTATION_RESPONSE,
        {
          response,
          originalQuestion: question
        },
        message.conversationId,
        message.id
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling consultation request`, {
        sessionId: this.getSessionId(),
        error: err.message || String(error),
        stack: err.stack
      });

      return this.createMessage(
        message.from,
        MessageType.ERROR,
        {
          error: err.message || String(error),
          originalMessage: message.id
        },
        message.conversationId,
        message.id
      );
    }
  }
}
