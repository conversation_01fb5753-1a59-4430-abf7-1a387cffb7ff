/**
 * Content Strategy Agent
 *
 * This file implements the specialized content strategy agent for the goal-based orchestration system.
 * It handles content strategy goals and produces content strategy artifacts.
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../../utils/logger';
import {
  MessageType,
  Message,
  GoalStatus,
  GoalType,
  CollaborationState,
  Artifact
} from '../state/unified-schema';
import { StateManager } from '../state/manager';
import { ArtifactDecisionFramework, ArtifactDecisionType } from '../services/artifact-decision-framework';
import { FeedbackLoopSystem } from '../utils/feedback-loop-system-new';
import { AgentBase } from './agent-base';

/**
 * Content Strategy Agent
 */
export class ContentStrategyAgent extends AgentBase {
  /**
   * Constructor
   * @param sessionId Session ID
   */
  constructor(sessionId: string) {
    super('content-strategy', sessionId);
    logger.info(`Content Strategy Agent initialized for session ${sessionId}`);
  }

  /**
   * Register message handlers
   */
  protected registerHandlers(): void {
    this.registerHandler(MessageType.GOAL_ASSIGNMENT, this.handleGoalAssignment.bind(this));
    this.registerHandler(MessageType.ARTIFACT_REQUEST, this.handleArtifactRequest.bind(this));
    this.registerHandler(MessageType.FEEDBACK, this.handleFeedback.bind(this));
    this.registerHandler(MessageType.CONSULTATION_REQUEST, this.handleConsultationRequest.bind(this));
  }

  /**
   * Process a goal
   * @param goalId Goal ID
   * @returns Promise<boolean> indicating success
   */
  public async processGoal(goalId: string): Promise<boolean> {
    try {
      logger.info(`Content Strategy Agent processing goal ${goalId}`, {
        sessionId: this.getSessionId(),
        goalId
      });

      // Get current state
      const state = await this.stateManager.getState();
      if (!state) {
        throw new Error('Session state not found');
      }

      // Get the goal from the byId object
      const goal = state.goals.byId[goalId];
      if (!goal) {
        throw new Error(`Goal ${goalId} not found`);
      }

      // Assign goal to agent
      await this.stateManager.assignGoal(goalId, this.agentId);

      // Make a decision about artifact creation
      const decision = await this.decisionFramework.decideArtifactCreation('content-strategy', goalId);

      // If we should use an existing artifact
      if (decision.type === ArtifactDecisionType.USE_EXISTING && decision.existingArtifactId) {
        // Complete the goal with the existing artifact
        await this.stateManager.completeGoal(goalId, decision.existingArtifactId);
        return true;
      }

      // Find market research and keyword analysis artifacts
      const marketResearchArtifacts = Object.values(state.artifacts).filter(
        artifact => artifact.type === 'market-research'
      );

      const keywordAnalysisArtifacts = Object.values(state.artifacts).filter(
        artifact => artifact.type === 'keyword-analysis'
      );

      if (marketResearchArtifacts.length === 0) {
        throw new Error('Market research artifact not found');
      }

      if (keywordAnalysisArtifacts.length === 0) {
        throw new Error('Keyword analysis artifact not found');
      }

      // Get the most recent artifacts
      const marketResearchArtifact = marketResearchArtifacts.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      const keywordAnalysisArtifact = keywordAnalysisArtifacts.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      // If we should improve an existing artifact
      if (decision.type === ArtifactDecisionType.IMPROVE_EXISTING && decision.existingArtifactId) {
        // Get the existing artifact
        const existingArtifact = state.artifacts[decision.existingArtifactId];
        if (!existingArtifact) {
          throw new Error(`Artifact ${decision.existingArtifactId} not found`);
        }

        // Import the OpenAI integration
        const { generateContentStrategy } = await import('../utils/content-generation');

        // Generate improved content strategy using OpenAI
        const contentStrategyContent = await generateContentStrategy(
          state.topic,
          state.contentType,
          state.targetAudience,
          state.tone,
          marketResearchArtifact,
          keywordAnalysisArtifact
        );

        // Create the improved artifact
        const artifactId = await this.stateManager.createArtifact(
          'content-strategy',
          'Content Strategy Plan (Improved)',
          contentStrategyContent,
          'content-strategy',
          goalId,
          decision.existingArtifactId
        );

        // Initialize feedback loop system
        const feedbackSystem = new FeedbackLoopSystem(this.getSessionId());

        // Generate feedback on the content strategy
        const feedback = await feedbackSystem.generateFeedback(
          artifactId,
          'content-creation',
          ['content structure', 'topic coverage', 'audience targeting']
        );

        // Create a feedback request
        const requestId = await feedbackSystem.requestFeedback(
          'content-strategy',
          'content-creation',
          artifactId,
          ['content structure', 'topic coverage', 'audience targeting']
        );

        // Provide feedback
        await feedbackSystem.provideFeedback(
          'content-creation',
          'content-strategy',
          requestId,
          feedback
        );

        // Complete the goal
        await this.stateManager.completeGoal(goalId, artifactId);
        return true;
      }

      // Otherwise, create a new artifact
      // Import the OpenAI integration
      const { generateContentStrategy } = await import('../utils/content-generation');

      // Generate content strategy using OpenAI
      const contentStrategyContent = await generateContentStrategy(
        state.topic,
        state.contentType,
        state.targetAudience,
        state.tone,
        marketResearchArtifact,
        keywordAnalysisArtifact
      );

      // Create the artifact with the generated content
      const artifactId = await this.stateManager.createArtifact(
        'content-strategy',
        'Content Strategy Plan',
        contentStrategyContent,
        'content-strategy',
        goalId
      );

      // Initialize feedback loop system
      const feedbackSystem = new FeedbackLoopSystem(this.getSessionId());

      // Generate feedback on the content strategy
      const feedback = await feedbackSystem.generateFeedback(
        artifactId,
        'content-creation',
        ['content structure', 'topic coverage', 'audience targeting']
      );

      // Create a feedback request
      const requestId = await feedbackSystem.requestFeedback(
        'content-strategy',
        'content-creation',
        artifactId,
        ['content structure', 'topic coverage', 'audience targeting']
      );

      // Provide feedback
      await feedbackSystem.provideFeedback(
        'content-creation',
        'content-strategy',
        requestId,
        feedback
      );

      // Complete the goal
      await this.stateManager.completeGoal(goalId, artifactId);

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error processing content strategy goal`, {
        sessionId: this.getSessionId(),
        goalId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }

  // Message handlers...
  private async handleGoalAssignment(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    try {
      const { goalId } = message.content;
      if (!goalId) {
        throw new Error('Goal ID not provided');
      }

      // Process the goal
      await this.processGoal(goalId);

      // Send acknowledgment
      return this.createMessage(
        message.from,
        MessageType.ACKNOWLEDGMENT,
        {
          goalId,
          status: 'processing'
        },
        message.conversationId,
        message.id
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error handling goal assignment`, {
        sessionId: this.getSessionId(),
        error: err.message || String(error),
        stack: err.stack
      });
      return null;
    }
  }

  // Other message handlers...
  private async handleArtifactRequest(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    // Implementation...
    return null;
  }

  private async handleFeedback(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    // Implementation...
    return null;
  }

  private async handleConsultationRequest(
    message: Message,
    state: CollaborationState,
    stateManager: StateManager
  ): Promise<Message | null> {
    // Implementation...
    return null;
  }
}
