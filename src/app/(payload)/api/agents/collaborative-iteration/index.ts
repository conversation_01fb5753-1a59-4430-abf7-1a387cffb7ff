// src/app/(payload)/api/agents/collaborative-iteration/index.ts

import { IterativeCollaborationController } from './controller';

// Import all agent implementations from the consolidated agents directory
import {
  contentGenerationAgent,
  seoKeywordAgent,
  marketResearchAgent,
  contentStrategyAgent,
  seoOptimizationAgent,
  messageBus,
  stateStore,
  createAgentStateManager,
  AgentStateManager,
  createAgentMessaging,
  AgentMessaging,
  createAgentHandler,
  AgentHandler,
  createAgentCommunicationManager,
  AgentCommunicationManager
} from './agents';

// Import types
import {
  IterativeMessage,
  IterativeCollaborationState,
  AgentState,
  IterativeArtifact,
  Consultation,
  Discussion,
  EnhancedReasoning,
  IterativeMessageType,
  AgentId,
  AgentRole,
  ArtifactStatus,
  Goal,
  GoalStatus,
  Decision,
  DiscussionMessage
} from './types';

// Export all consolidated agent implementations
export {
  // Controller
  IterativeCollaborationController,
  
  // Agents
  contentGenerationAgent,
  seoKeywordAgent,
  marketResearchAgent,
  contentStrategyAgent,
  seoOptimizationAgent,
  
  // Utilities
  messageBus,
  stateStore,
  createAgentStateManager,
  AgentStateManager,
  createAgentMessaging,
  AgentMessaging,
  createAgentHandler,
  AgentHandler,
  createAgentCommunicationManager,
  AgentCommunicationManager
};

// Export types
export type {
  IterativeMessage,
  IterativeCollaborationState,
  IterativeMessageType,
  AgentState,
  IterativeArtifact,
  Consultation,
  Discussion,
  EnhancedReasoning,
  AgentId,
  AgentRole,
  ArtifactStatus,
  Goal,
  GoalStatus,
  Decision,
  DiscussionMessage
};

// We'll create the communication manager when needed with the appropriate session ID
// rather than a singleton, since each session needs its own instance

/**
 * Process a message using the appropriate agent handler or the message bus
 */
export async function processAgentMessage(
  message: IterativeMessage,
  agentState: AgentState,
  artifacts: Record<string, IterativeArtifact> = {},
  consultations: Record<string, Consultation> = {}
) {
  // Ensure we have a session ID for the message bus
  const sessionId = agentState.id.includes('-') ? agentState.id : `session-${agentState.id}`;
  
  // Check if this is a message type that should be handled by the message bus
  const specialMessageTypes = [
    'CONSULTATION_REQUEST', 'CONSULTATION_RESPONSE', 
    'FEEDBACK_REQUEST', 'FEEDBACK_RESPONSE',
    'DISCUSSION_START', 'DISCUSSION_CONTRIBUTION', 'DISCUSSION_RESOLUTION',
    'BROADCAST_ARTIFACT'
  ];
  
  if (specialMessageTypes.includes(message.type)) {
    // Use the message bus to handle special collaborative messages
    return messageBus.sendMessage(sessionId, message, async (sessionId, routedMessage) => {
      // This is the processor function that will be called after message bus processing
      // Route the message to the appropriate agent handler based on destination
      return routeMessageToAgent(routedMessage, agentState, artifacts, consultations);
    });
  }
  
  // For standard messages, route directly to the agent handler
  return routeMessageToAgent(message, agentState, artifacts, consultations);
}

/**
 * Route a message to the appropriate agent handler
 */
async function routeMessageToAgent(
  message: IterativeMessage,
  agentState: AgentState,
  artifacts: Record<string, IterativeArtifact> = {},
  consultations: Record<string, Consultation> = {}
) {
  // Route the message to the appropriate agent handler
  switch (message.to) {
    case 'market-research':
      return handleMarketResearchMessage(message, agentState, artifacts, consultations);
    
    case 'seo-keyword':
      return handleSeoKeywordMessage(message, agentState, artifacts, consultations);
    
    case 'content-strategy':
      return handleContentStrategyMessage(message, agentState, artifacts, consultations);
    
    case 'content-generation':
      // Content generation agent expects IterativeCollaborationState but receives AgentState
      // Create a compatible state object with minimum required properties
      const contentGenState: IterativeCollaborationState = {
        // Basic required properties
        id: agentState.id.includes('-') ? agentState.id : `session-${agentState.id}`,
        topic: '',
        contentType: 'blog-article',
        targetAudience: '',
        tone: 'professional',
        keywords: [],
        status: 'active',
        startTime: agentState.lastUpdated || new Date().toISOString(),
        artifacts: artifacts || {},
        consultations: consultations || {},
        currentPhase: 'creation',
        messages: [],
        iterations: 0,
        maxIterations: 3,
        
        // Add agentStates with our current agent state
        agentStates: {
          'content-generation': agentState
        },
        
        // Add discussions field to support our enhanced collaboration features
        discussions: {},
        events: []
      };
      return handleContentGenerationMessage(message, contentGenState, artifacts, consultations);
    
    case 'seo-optimization':
      return handleSeoOptimizationMessage(message, agentState, artifacts, consultations);
    
    default:
      throw new Error(`Unknown agent: ${message.to}`);
  }
}
