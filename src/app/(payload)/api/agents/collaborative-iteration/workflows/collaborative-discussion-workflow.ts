// src/app/(payload)/api/agents/collaborative-iteration/workflows/collaborative-discussion-workflow.ts

import { v4 as uuidv4 } from 'uuid';
import {
  contentGenerationAgent,
  seoKeywordAgent,
  marketResearchAgent,
  contentStrategyAgent,
  seoOptimizationAgent,
  stateStore,
  messageBus
} from '../server-based';
import logger from '../utils/logger';
import {
  IterativeMessageType,
  IterativeMessage,
  IterativeCollaborationState,
  IterativeArtifact,
  ArtifactStatus,
  AgentId
} from '../types';

// Type definitions for the workflow
interface WorkflowState {
  currentPhase: 'research' | 'planning' | 'writing' | 'review' | 'optimization' | 'complete';
  phaseProgress: number;
  phaseStartTime: string;
  phaseAttempts: number;
  completedPhases: string[];
  failedAttempts: Record<string, number>;
  qualityScores: Record<string, number>;
  aggregatedFeedback: StructuredFeedback[];
}
import { createEnhancedReasoning } from '../utils/reasoningUtils';

/**
 * Quality threshold constants for artifact validation
 */
const QUALITY_THRESHOLDS = {
  MARKET_RESEARCH: 0.75,
  KEYWORD_RESEARCH: 0.75,
  CONTENT_STRATEGY: 0.80,
  CONTENT_GENERATION: 0.75,
  SEO_OPTIMIZATION: 0.80,
  FEEDBACK: 0.70
};

/**
 * Feedback priority levels
 */
enum FeedbackPriority {
  CRITICAL = 'critical',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
  SUGGESTION = 'suggestion'
}

/**
 * Structured feedback format
 */
interface StructuredFeedback {
  id: string;
  from: AgentId;
  artifactId: string;
  section?: string;
  content: string;
  priority: FeedbackPriority;
  suggestions: string[];
  confidence: number;
  timestamp: string;
}

/**
 * Orchestrates a collaborative discussion workflow between specialized agents
 * with enhanced structure, validation, and quality checks
 *
 * This workflow creates a dynamic collaboration pattern where:
 * 1. Each agent can consult other agents for specific information
 * 2. Agents provide structured feedback on each other's artifacts
 * 3. Quality thresholds ensure high-quality output at each phase
 * 4. The system maintains a coherent discussion thread with reasoning
 * 5. Phases have clear success criteria and error recovery
 */
export async function initiateCollaborativeDiscussion(
  sessionId: string,
  initialTopic: string,
  initialParams: any = {}
): Promise<boolean> {
  logger.info(`Initiating enhanced collaborative discussion workflow`, {
    sessionId,
    phase: 'initialization',
    topic: initialTopic,
    params: initialParams
  });

  // Get the current state or return if not found
  const state = await stateStore.getState(sessionId);
  if (!state) {
    logger.error(`No state found for session`, { sessionId });
    return false;
  }

  // Log the initial state
  logger.debug(`Initial state retrieved`, {
    sessionId,
    phase: 'initialization',
    state: JSON.stringify(state).substring(0, 200) + '...'
  });

  try {
    // Create a discussion ID for tracking the discussion thread
    const discussionId = uuidv4();

    // Initialize workflow state tracking
    const workflowState: WorkflowState = {
      currentPhase: 'research',
      phaseProgress: 0,
      phaseStartTime: new Date().toISOString(),
      phaseAttempts: 1,
      completedPhases: [],
      failedAttempts: {},
      qualityScores: {},
      aggregatedFeedback: []
    };

    // Create a system message to start the discussion with enhanced reasoning
    const systemMessage: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.UPDATE,
      from: 'system',
      to: 'all',
      content: {
        topic: initialTopic,
        instructions: 'Initiate a collaborative discussion to create high-quality content',
        discussionId,
        workflowState,
        ...initialParams
      },
      timestamp: new Date().toISOString(),
      conversationId: discussionId,
      reasoning: {
        context: { topic: initialTopic, workflow: 'collaborative-content-creation' },
        thoughts: [
          'Initializing collaborative content creation workflow',
          'Establishing communication channels between specialized agents',
          'Setting up structured phases for content development',
          'Implementing quality validation at each phase'
        ],
        conclusion: 'Begin collaborative content creation with research phase',
        process: 'system',
        metadata: {
          confidence: 0.95,
          steps: [
            'Initialize workflow state tracking',
            'Create discussion thread',
            'Notify all participating agents',
            'Begin with research phase'
          ]
        }
      }
    };

    // Add the message to the state
    if (!state.messages) state.messages = [];
    state.messages.push(systemMessage);

    // Initialize the discussion in the state
    if (!state.discussions) state.discussions = {};
    state.discussions[discussionId] = {
      id: discussionId,
      topic: initialTopic,
      status: 'active',
      participants: ['market-research', 'seo-keyword', 'content-strategy', 'content-generation', 'seo-optimization'],
      messages: [],
      leadAgent: 'content-strategy',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      data: { workflowState }
    };

    // Initialize workflow progress tracking
    if (!state.workflowProgress) {
      state.workflowProgress = {
        marketResearchComplete: false,
        keywordResearchComplete: false,
        contentStrategyComplete: false,
        contentGenerationComplete: false,
        seoOptimizationComplete: false,
        phase: 'research',
        overallProgress: 0
      };
    }

    // Save the updated state
    await stateStore.setState(sessionId, state);

    // Begin the research phase by triggering both market research and SEO keyword research
    setTimeout(() => {
      // Log phase transition
      logger.logPhaseTransition(sessionId, 'initialization', 'research');
      initiateResearchPhase(sessionId, discussionId, initialTopic, initialParams).catch((err: Error) => {
        logger.error(`Error in research phase`, {
          sessionId,
          phase: 'research',
          error: err.message || String(err),
          stack: err.stack
        });
      });
    }, 2000);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error initiating collaborative discussion`, {
      sessionId,
      phase: 'initialization',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * First phase of the workflow: Research Phase
 * Coordinates market research and SEO keyword research to build the foundation for content
 */
export async function initiateResearchPhase(
  sessionId: string,
  discussionId: string,
  topic: string,
  contextData: any = {}
): Promise<boolean> {
  logger.info(`Initiating research phase for session ${sessionId}`, {
    sessionId,
    phase: 'research',
    topic,
    discussionId
  });

  // Get the current state or return if not found
  const state = await stateStore.getState(sessionId);
  if (!state) {
    logger.error(`No state found for session ${sessionId}`);
    return false;
  }

  try {
    // Update workflow state to reflect research phase
    if (state.discussions && state.discussions[discussionId]) {
      if (state.discussions[discussionId].data && state.discussions[discussionId].data.workflowState) {
        state.discussions[discussionId].data.workflowState.currentPhase = 'research';
        state.discussions[discussionId].data.workflowState.phaseProgress = 0;
        state.discussions[discussionId].data.workflowState.phaseStartTime = new Date().toISOString();
      }
    }

    // Update the state
    await stateStore.setState(sessionId, state);

    // Create detailed market research request with specific questions
    const marketResearchRequest: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.REQUEST,
      from: 'system',
      to: 'market-research',
      content: {
        topic,
        questions: [
          `What is the target audience for content about "${topic}"?`,
          `What are the key market trends related to "${topic}"?`,
          `Who are the main competitors creating content about "${topic}"?`,
          `What are the audience pain points related to "${topic}"?`,
          `What content formats are most effective for "${topic}"?`
        ],
        context: {
          contentType: contextData.contentType || 'blog-article',
          purpose: 'comprehensive market analysis',
          requiredComponents: ['audience-analysis', 'trend-analysis', 'competitor-analysis'],
          qualityThreshold: QUALITY_THRESHOLDS.MARKET_RESEARCH
        },
        discussionId
      },
      timestamp: new Date().toISOString(),
      conversationId: discussionId,
      reasoning: {
        context: { topic, phase: 'research', agent: 'market-research' },
        thoughts: [
          'Comprehensive market research is essential for content relevance',
          'Understanding audience demographics and preferences will guide content strategy',
          'Identifying market trends ensures timely and valuable content',
          'Competitor analysis reveals content gaps and opportunities'
        ],
        conclusion: 'Request detailed market research to establish content foundation',
        process: 'system',
        metadata: {
          confidence: 0.95,
          steps: [
            'Define specific research questions',
            'Request audience analysis',
            'Request trend identification',
            'Request competitor analysis',
            'Establish quality threshold for acceptance'
          ]
        }
      }
    };

    // Process the market research request
    await messageBus.sendMessage(sessionId, marketResearchRequest, async (message: IterativeMessage) => {
      logger.info(`Market research agent processing detailed research request`, {
        sessionId,
        phase: 'research',
        messageId: message.id
      });

      // Validate message
      if (!message || typeof message !== 'object') {
        logger.error(`Invalid message object passed to market research agent`, {
          sessionId,
          phase: 'research',
          message: String(message)
        });

        return {
          success: false,
          error: `Invalid message object: ${message}`,
          response: {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: 'system',
            to: 'market-research',
            type: IterativeMessageType.ERROR,
            content: { error: `Invalid message object: ${message}` },
            conversationId: sessionId
          }
        };
      }

      return await marketResearchAgent.processMessage(sessionId, message);
    });

    // Create detailed SEO keyword research request
    const keywordResearchRequest: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.REQUEST,
      from: 'system',
      to: 'seo-keyword',
      content: {
        topic,
        questions: [
          `What are the highest-value primary keywords for "${topic}"?`,
          `What long-tail keywords should be targeted for "${topic}"?`,
          `What is the search intent behind queries related to "${topic}"?`,
          `What is the keyword difficulty and competition level for "${topic}"?`,
          `What semantic keywords and related terms should be included?`
        ],
        context: {
          contentType: contextData.contentType || 'blog-article',
          purpose: 'comprehensive keyword strategy',
          requiredComponents: ['primary-keywords', 'long-tail-keywords', 'search-intent-analysis'],
          qualityThreshold: QUALITY_THRESHOLDS.KEYWORD_RESEARCH
        },
        discussionId
      },
      timestamp: new Date().toISOString(),
      conversationId: discussionId,
      reasoning: {
        context: { topic, phase: 'research', agent: 'seo-keyword' },
        thoughts: [
          'Effective SEO requires strategic keyword selection',
          'Understanding search intent ensures content meets user needs',
          'Long-tail keywords provide targeted traffic opportunities',
          'Keyword difficulty analysis helps prioritize content focus'
        ],
        conclusion: 'Request comprehensive keyword research for SEO optimization',
        process: 'system',
        metadata: {
          confidence: 0.95,
          steps: [
            'Define specific keyword research questions',
            'Request primary keyword identification',
            'Request long-tail keyword analysis',
            'Request search intent analysis',
            'Establish quality threshold for acceptance'
          ]
        }
      }
    };

    // Process the SEO keyword research request
    await messageBus.sendMessage(sessionId, keywordResearchRequest, async (message: IterativeMessage) => {
      logger.info(`SEO keyword agent processing detailed research request`, {
        sessionId,
        phase: 'research',
        messageId: message.id
      });

      // Validate message
      if (!message || typeof message !== 'object') {
        logger.error(`Invalid message object passed to SEO keyword agent`, {
          sessionId,
          phase: 'research',
          message: String(message)
        });

        return {
          success: false,
          error: `Invalid message object: ${message}`,
          response: {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: 'system',
            to: 'seo-keyword',
            type: IterativeMessageType.ERROR,
            content: { error: `Invalid message object: ${message}` },
            conversationId: sessionId
          }
        };
      }

      return await seoKeywordAgent.processMessage(sessionId, message);
    });

    // Schedule validation of research phase results
    setTimeout(() => {
      validateResearchPhase(sessionId, discussionId, topic, contextData).catch((err: Error) => {
        logger.error(`Error validating research phase`, {
          sessionId,
          phase: 'research-validation',
          error: err.message || String(err),
          stack: err.stack
        });
      });
    }, 10000);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error initiating research phase`, {
      sessionId,
      phase: 'research',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Validates the research phase results and transitions to planning phase if successful
 */
export async function validateResearchPhase(
  sessionId: string,
  discussionId: string,
  topic: string,
  contextData: any = {}
): Promise<boolean> {
  logger.info(`Validating research phase results for session ${sessionId}`, {
    sessionId,
    phase: 'research-validation',
    discussionId
  });

  // Get the current state
  const state = await stateStore.getState(sessionId);
  if (!state) {
    logger.error(`No state found for session ${sessionId}`);
    return false;
  }

  try {
    // Find market research and keyword research artifacts
    const marketResearchArtifacts = state.artifacts && typeof state.artifacts === 'object'
      ? Object.values(state.artifacts).filter(artifact =>
          artifact.type === 'market-research' &&
          artifact.status === 'completed' as ArtifactStatus)
      : [];

    const keywordArtifacts = state.artifacts && typeof state.artifacts === 'object'
      ? Object.values(state.artifacts).filter(artifact =>
          artifact.type === 'seo-keywords' &&
          artifact.status === 'completed' as ArtifactStatus)
      : [];

    logger.info(`Research validation: Found ${marketResearchArtifacts.length} market research artifacts and ${keywordArtifacts.length} keyword artifacts`, {
      sessionId,
      phase: 'research-validation'
    });

    // Check if both required artifacts exist
    const researchComplete = marketResearchArtifacts.length > 0 && keywordArtifacts.length > 0;

    // If research is not complete, check if we need to retry
    if (!researchComplete) {
      // Get current workflow state
      let workflowState: WorkflowState | undefined;
      if (state.discussions &&
          state.discussions[discussionId] &&
          state.discussions[discussionId].metadata &&
          state.discussions[discussionId].metadata.workflowState) {
        workflowState = state.discussions[discussionId].metadata.workflowState;
      }

      // If we've already attempted research multiple times, proceed anyway with warning
      if (workflowState && workflowState.phaseAttempts >= 3) {
        logger.warn(`Research phase incomplete after ${workflowState.phaseAttempts} attempts, proceeding with limited data`, {
          sessionId,
          phase: 'research-validation',
          marketResearchFound: marketResearchArtifacts.length > 0,
          keywordResearchFound: keywordArtifacts.length > 0
        });

        // Proceed to planning phase with warning
        setTimeout(() => {
          initiatePlanningPhase(sessionId, discussionId, topic, contextData, true).catch((err: Error) => {
            logger.error(`Error initiating planning phase`, {
              sessionId,
              phase: 'planning',
              error: err.message || String(err),
              stack: err.stack
            });
          });
        }, 2000);

        return true;
      }

      // Retry research phase
      if (workflowState) {
        workflowState.phaseAttempts += 1;

        // Update state with new attempt count
        if (state.discussions && state.discussions[discussionId] && state.discussions[discussionId].metadata) {
          state.discussions[discussionId].metadata.workflowState = workflowState;
          await stateStore.setState(sessionId, state);
        }

        logger.info(`Research phase incomplete, retrying (attempt ${workflowState.phaseAttempts})`, {
          sessionId,
          phase: 'research-validation'
        });

        // Retry research phase
        setTimeout(() => {
          initiateResearchPhase(sessionId, discussionId, topic, contextData).catch((err: Error) => {
            logger.error(`Error retrying research phase`, {
              sessionId,
              phase: 'research',
              error: err.message || String(err),
              stack: err.stack
            });
          });
        }, 5000);

        return false;
      }
    }

    // Research is complete, validate quality
    let qualityCheckPassed = true;
    const qualityIssues: string[] = [];

    // Check market research quality
    if (marketResearchArtifacts.length > 0) {
      const marketResearch = marketResearchArtifacts[0];

      // Check for required fields
      const requiredFields = ['audience', 'trends', 'competitors'];
      const missingFields = requiredFields.filter(field =>
        !marketResearch.content ||
        typeof marketResearch.content !== 'object' ||
        !(field in (marketResearch.content as Record<string, any>)) ||
        (Array.isArray((marketResearch.content as Record<string, any>)[field]) &&
         (marketResearch.content as Record<string, any>)[field].length === 0)
      );

      if (missingFields.length > 0) {
        qualityCheckPassed = false;
        qualityIssues.push(`Market research missing required fields: ${missingFields.join(', ')}`);
      }
    }

    // Check keyword research quality
    if (keywordArtifacts.length > 0) {
      const keywordResearch = keywordArtifacts[0];

      // Check for required fields
      const requiredFields = ['primaryKeywords', 'longTailKeywords', 'searchIntent'];
      const missingFields = requiredFields.filter(field =>
        !keywordResearch.content ||
        typeof keywordResearch.content !== 'object' ||
        !(field in (keywordResearch.content as Record<string, any>)) ||
        (Array.isArray((keywordResearch.content as Record<string, any>)[field]) &&
         (keywordResearch.content as Record<string, any>)[field].length === 0)
      );

      if (missingFields.length > 0) {
        qualityCheckPassed = false;
        qualityIssues.push(`Keyword research missing required fields: ${missingFields.join(', ')}`);
      }
    }

    // If quality check failed, retry or proceed with warning
    if (!qualityCheckPassed) {
      // Get current workflow state
      let workflowState: WorkflowState | undefined;
      if (state.discussions &&
          state.discussions[discussionId] &&
          state.discussions[discussionId].metadata &&
          state.discussions[discussionId].metadata.workflowState) {
        workflowState = state.discussions[discussionId].metadata.workflowState;
      }

      // If we've already attempted research multiple times, proceed anyway with warning
      if (workflowState && workflowState.phaseAttempts >= 3) {
        logger.warn(`Research quality check failed after ${workflowState.phaseAttempts} attempts, proceeding with limited data`, {
          sessionId,
          phase: 'research-validation',
          qualityIssues
        });

        // Proceed to planning phase with warning
        setTimeout(() => {
          initiatePlanningPhase(sessionId, discussionId, topic, contextData, true).catch((err: Error) => {
            logger.error(`Error initiating planning phase`, {
              sessionId,
              phase: 'planning',
              error: err.message || String(err),
              stack: err.stack
            });
          });
        }, 2000);

        return true;
      }

      // Retry research phase with quality feedback
      if (workflowState) {
        workflowState.phaseAttempts += 1;

        // Update state with new attempt count
        if (state.discussions && state.discussions[discussionId] && state.discussions[discussionId].metadata) {
          state.discussions[discussionId].metadata.workflowState = workflowState;
          await stateStore.setState(sessionId, state);
        }

        logger.info(`Research quality check failed, retrying with feedback (attempt ${workflowState.phaseAttempts})`, {
          sessionId,
          phase: 'research-validation',
          qualityIssues
        });

        // Send feedback to relevant agents
        if (qualityIssues.some(issue => issue.includes('Market research'))) {
          const feedbackMessage: IterativeMessage = {
            id: uuidv4(),
            type: IterativeMessageType.FEEDBACK,
            from: 'system',
            to: 'market-research',
            content: {
              feedback: `Quality check failed: ${qualityIssues.filter(issue => issue.includes('Market research')).join(', ')}`,
              priority: FeedbackPriority.HIGH,
              requestImprovement: true
            },
            timestamp: new Date().toISOString(),
            conversationId: discussionId
          };

          await messageBus.sendMessage(sessionId, feedbackMessage, async (message: IterativeMessage) => {
            return await marketResearchAgent.processMessage(sessionId, message);
          });
        }

        if (qualityIssues.some(issue => issue.includes('Keyword research'))) {
          const feedbackMessage: IterativeMessage = {
            id: uuidv4(),
            type: IterativeMessageType.FEEDBACK,
            from: 'system',
            to: 'seo-keyword',
            content: {
              feedback: `Quality check failed: ${qualityIssues.filter(issue => issue.includes('Keyword research')).join(', ')}`,
              priority: FeedbackPriority.HIGH,
              requestImprovement: true
            },
            timestamp: new Date().toISOString(),
            conversationId: discussionId
          };

          await messageBus.sendMessage(sessionId, feedbackMessage, async (message: IterativeMessage) => {
            return await seoKeywordAgent.processMessage(sessionId, message);
          });
        }

        // Schedule another validation check
        setTimeout(() => {
          validateResearchPhase(sessionId, discussionId, topic, contextData).catch((err: Error) => {
            logger.error(`Error validating research phase`, {
              sessionId,
              phase: 'research-validation',
              error: err.message || String(err),
              stack: err.stack
            });
          });
        }, 10000);

        return false;
      }
    }

    // Research phase is complete and quality check passed
    logger.info(`Research phase complete and validated successfully`, {
      sessionId,
      phase: 'research-validation'
    });

    // Update workflow progress
    if (state.workflowProgress) {
      state.workflowProgress.marketResearchComplete = true;
      state.workflowProgress.keywordResearchComplete = true;
      state.workflowProgress.currentPhase = 'planning';
    }

    // Update workflow state
    if (state.discussions &&
        state.discussions[discussionId] &&
        state.discussions[discussionId].metadata &&
        state.discussions[discussionId].metadata.workflowState) {
      const workflowState = state.discussions[discussionId].metadata.workflowState;
      workflowState.completedPhases.push('research');
      workflowState.currentPhase = 'planning';
      workflowState.phaseProgress = 0;
      workflowState.phaseStartTime = new Date().toISOString();
      workflowState.phaseAttempts = 1;
    }

    // Save updated state
    await stateStore.setState(sessionId, state);

    // Proceed to planning phase
    setTimeout(() => {
      logger.logPhaseTransition(sessionId, 'research', 'planning');
      initiatePlanningPhase(sessionId, discussionId, topic, contextData).catch((err: Error) => {
        logger.error(`Error initiating planning phase`, {
          sessionId,
          phase: 'planning',
          error: err.message || String(err),
          stack: err.stack
        });
      });
    }, 2000);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error validating research phase`, {
      sessionId,
      phase: 'research-validation',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Second phase of the workflow: Planning Phase (Content Strategy Development)
 * Creates a comprehensive content strategy and outline based on research findings
 */
export async function initiatePlanningPhase(
  sessionId: string,
  discussionId: string,
  topic: string,
  contextData: any = {},
  skipValidation: boolean = false
): Promise<boolean> {
  logger.info(`Initiating planning phase for session ${sessionId}`, {
    sessionId,
    phase: 'planning',
    topic,
    discussionId,
    skipValidation
  });

  // Get the current state or return if not found
  const state = await stateStore.getState(sessionId);
  if (!state) {
    logger.error(`No state found for session ${sessionId}`);
    return false;
  }

  try {
    // Update workflow state to reflect planning phase
    if (state.discussions && state.discussions[discussionId]) {
      if (state.discussions[discussionId].data && state.discussions[discussionId].data.workflowState) {
        state.discussions[discussionId].data.workflowState.currentPhase = 'planning';
        state.discussions[discussionId].data.workflowState.phaseProgress = 0;
        state.discussions[discussionId].data.workflowState.phaseStartTime = new Date().toISOString();
      }
    }

    // Update the state
    await stateStore.setState(sessionId, state);

    // Find market research and keyword research artifacts
    const marketResearchArtifacts = state.artifacts && typeof state.artifacts === 'object'
      ? Object.values(state.artifacts).filter(artifact =>
          artifact.type === 'market-research' &&
          artifact.status === 'completed' as ArtifactStatus)
      : [];

    const keywordArtifacts = state.artifacts && typeof state.artifacts === 'object'
      ? Object.values(state.artifacts).filter(artifact =>
          artifact.type === 'seo-keywords' &&
          artifact.status === 'completed' as ArtifactStatus)
      : [];

    // Build context from existing artifacts and messages
    const consultationContext = {
      topic,
      marketResearchInsights: marketResearchArtifacts.length > 0
        ? marketResearchArtifacts[0].content || marketResearchArtifacts[0].data
        : 'No market research available',
      keywordInsights: keywordArtifacts.length > 0
        ? keywordArtifacts[0].content || keywordArtifacts[0].data
        : 'No keyword research available',
      contentType: contextData.contentType || 'blog-article',
      targetAudience: contextData.targetAudience ||
        (marketResearchArtifacts.length > 0 &&
         typeof marketResearchArtifacts[0].content === 'object' &&
         marketResearchArtifacts[0].content?.audience) ||
        'general audience',
      ...contextData
    };

    // Create detailed content strategy request with specific questions
    const contentStrategyRequest: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.REQUEST,
      from: 'system',
      to: 'content-strategy',
      content: {
        topic,
        questions: [
          `What content structure would be most effective for "${topic}" based on the research?`,
          `What key sections should be included in content about "${topic}"?`,
          `What tone and style would resonate best with the target audience for "${topic}"?`,
          `How should keywords be incorporated into the content structure for "${topic}"?`,
          `What content format and length would be optimal for "${topic}"?`
        ],
        context: consultationContext,
        requiredComponents: ['content-structure', 'section-outline', 'tone-guidelines', 'keyword-placement'],
        qualityThreshold: QUALITY_THRESHOLDS.CONTENT_STRATEGY,
        discussionId,
        marketResearchArtifactIds: marketResearchArtifacts.map(a => a.id),
        keywordArtifactIds: keywordArtifacts.map(a => a.id),
        skipValidation
      },
      timestamp: new Date().toISOString(),
      conversationId: discussionId,
      reasoning: {
        context: { topic, phase: 'planning', agent: 'content-strategy' },
        thoughts: [
          'Effective content requires strategic planning based on research insights',
          'Content structure should align with audience needs and search intent',
          'Keyword placement strategy ensures SEO effectiveness',
          'Clear section planning improves content organization and readability'
        ],
        conclusion: 'Request comprehensive content strategy development based on research findings',
        process: 'system',
        metadata: {
          confidence: 0.95,
          steps: [
            'Define specific content strategy questions',
            'Provide research context from previous phase',
            'Request content structure development',
            'Request section outline creation',
            'Establish quality threshold for acceptance'
          ]
        }
      }
    };

    // Process the content strategy request
    await messageBus.sendMessage(sessionId, contentStrategyRequest, async (message: IterativeMessage) => {
      logger.info(`Content strategy agent processing detailed strategy request`, {
        sessionId,
        phase: 'planning',
        messageId: message.id
      });

      // Validate message
      if (!message || typeof message !== 'object') {
        logger.error(`Invalid message object passed to content strategy agent`, {
          sessionId,
          phase: 'planning',
          message: String(message)
        });

        return {
          success: false,
          error: `Invalid message object: ${message}`,
          response: {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: 'system',
            to: 'content-strategy',
            type: IterativeMessageType.ERROR,
            content: { error: `Invalid message object: ${message}` },
            conversationId: sessionId
          }
        };
      }

      return await contentStrategyAgent.processMessage(sessionId, message);
    });

    // Schedule validation of planning phase results
    setTimeout(() => {
      validatePlanningPhase(sessionId, discussionId, topic, contextData).catch((err: Error) => {
        logger.error(`Error validating planning phase`, {
          sessionId,
          phase: 'planning-validation',
          error: err.message || String(err),
          stack: err.stack
        });
      });
    }, 10000);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error initiating planning phase`, {
      sessionId,
      phase: 'planning',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Validates the planning phase results and transitions to writing phase if successful
 */
export async function validatePlanningPhase(
  sessionId: string,
  discussionId: string,
  topic: string,
  contextData: any = {}
): Promise<boolean> {
  logger.info(`Validating planning phase results for session ${sessionId}`, {
    sessionId,
    phase: 'planning-validation',
    discussionId
  });

  // Get the current state
  const state = await stateStore.getState(sessionId);
  if (!state) {
    logger.error(`No state found for session ${sessionId}`);
    return false;
  }

  try {
    // Find content strategy artifacts
    const contentStrategyArtifacts = state.artifacts && typeof state.artifacts === 'object'
      ? Object.values(state.artifacts).filter(artifact =>
          (artifact.type === 'content-strategy' || artifact.type === 'content-outline') &&
          artifact.status === 'completed' as ArtifactStatus)
      : [];

    logger.info(`Planning validation: Found ${contentStrategyArtifacts.length} content strategy artifacts`, {
      sessionId,
      phase: 'planning-validation'
    });

    // Check if required artifacts exist
    const planningComplete = contentStrategyArtifacts.length > 0;

    // If planning is not complete, check if we need to retry
    if (!planningComplete) {
      // Get current workflow state
      let workflowState: WorkflowState | undefined;
      if (state.discussions &&
          state.discussions[discussionId] &&
          state.discussions[discussionId].metadata &&
          state.discussions[discussionId].metadata.workflowState) {
        workflowState = state.discussions[discussionId].metadata.workflowState;
      }

      // If we've already attempted planning multiple times, proceed anyway with warning
      if (workflowState && workflowState.phaseAttempts >= 3) {
        logger.warn(`Planning phase incomplete after ${workflowState.phaseAttempts} attempts, proceeding with limited data`, {
          sessionId,
          phase: 'planning-validation'
        });

        // Proceed to writing phase with warning
        setTimeout(() => {
          initiateWritingPhase(sessionId, discussionId, topic, contextData, true).catch((err: Error) => {
            logger.error(`Error initiating writing phase`, {
              sessionId,
              phase: 'writing',
              error: err.message || String(err),
              stack: err.stack
            });
          });
        }, 2000);

        return true;
      }

      // Retry planning phase
      if (workflowState) {
        workflowState.phaseAttempts += 1;

        // Update state with new attempt count
        if (state.discussions && state.discussions[discussionId] && state.discussions[discussionId].metadata) {
          state.discussions[discussionId].metadata.workflowState = workflowState;
          await stateStore.setState(sessionId, state);
        }

        logger.info(`Planning phase incomplete, retrying (attempt ${workflowState.phaseAttempts})`, {
          sessionId,
          phase: 'planning-validation'
        });

        // Retry planning phase
        setTimeout(() => {
          initiatePlanningPhase(sessionId, discussionId, topic, contextData).catch((err: Error) => {
            logger.error(`Error retrying planning phase`, {
              sessionId,
              phase: 'planning',
              error: err.message || String(err),
              stack: err.stack
            });
          });
        }, 5000);

        return false;
      }
    }

    // Planning is complete, validate quality
    let qualityCheckPassed = true;
    const qualityIssues: string[] = [];

    // Check content strategy quality
    if (contentStrategyArtifacts.length > 0) {
      const contentStrategy = contentStrategyArtifacts[0];

      // Check for required fields
      const requiredFields = ['structure', 'sections', 'keypoints'];
      const missingFields = requiredFields.filter(field =>
        !contentStrategy.content ||
        typeof contentStrategy.content !== 'object' ||
        !(field in (contentStrategy.content as Record<string, any>)) ||
        (Array.isArray((contentStrategy.content as Record<string, any>)[field]) &&
         (contentStrategy.content as Record<string, any>)[field].length === 0)
      );

      if (missingFields.length > 0) {
        qualityCheckPassed = false;
        qualityIssues.push(`Content strategy missing required fields: ${missingFields.join(', ')}`);
      }
    }

    // If quality check failed, retry or proceed with warning
    if (!qualityCheckPassed) {
      // Get current workflow state
      let workflowState: WorkflowState | undefined;
      if (state.discussions &&
          state.discussions[discussionId] &&
          state.discussions[discussionId].metadata &&
          state.discussions[discussionId].metadata.workflowState) {
        workflowState = state.discussions[discussionId].metadata.workflowState;
      }

      // If we've already attempted planning multiple times, proceed anyway with warning
      if (workflowState && workflowState.phaseAttempts >= 3) {
        logger.warn(`Planning quality check failed after ${workflowState.phaseAttempts} attempts, proceeding with limited data`, {
          sessionId,
          phase: 'planning-validation',
          qualityIssues
        });

        // Proceed to writing phase with warning
        setTimeout(() => {
          initiateWritingPhase(sessionId, discussionId, topic, contextData, true).catch((err: Error) => {
            logger.error(`Error initiating writing phase`, {
              sessionId,
              phase: 'writing',
              error: err.message || String(err),
              stack: err.stack
            });
          });
        }, 2000);

        return true;
      }

      // Retry planning phase with quality feedback
      if (workflowState) {
        workflowState.phaseAttempts += 1;

        // Update state with new attempt count
        if (state.discussions && state.discussions[discussionId] && state.discussions[discussionId].metadata) {
          state.discussions[discussionId].metadata.workflowState = workflowState;
          await stateStore.setState(sessionId, state);
        }

        logger.info(`Planning quality check failed, retrying with feedback (attempt ${workflowState.phaseAttempts})`, {
          sessionId,
          phase: 'planning-validation',
          qualityIssues
        });

        // Send feedback to content strategy agent
        const feedbackMessage: IterativeMessage = {
          id: uuidv4(),
          type: IterativeMessageType.FEEDBACK,
          from: 'system',
          to: 'content-strategy',
          content: {
            feedback: `Quality check failed: ${qualityIssues.join(', ')}`,
            priority: FeedbackPriority.HIGH,
            requestImprovement: true
          },
          timestamp: new Date().toISOString(),
          conversationId: discussionId
        };

        await messageBus.sendMessage(sessionId, feedbackMessage, async (message: IterativeMessage) => {
          return await contentStrategyAgent.processMessage(sessionId, message);
        });

        // Schedule another validation check
        setTimeout(() => {
          validatePlanningPhase(sessionId, discussionId, topic, contextData).catch((err: Error) => {
            logger.error(`Error validating planning phase`, {
              sessionId,
              phase: 'planning-validation',
              error: err.message || String(err),
              stack: err.stack
            });
          });
        }, 10000);

        return false;
      }
    }

    // Planning phase is complete and quality check passed
    logger.info(`Planning phase complete and validated successfully`, {
      sessionId,
      phase: 'planning-validation'
    });

    // Update workflow progress
    if (state.workflowProgress) {
      state.workflowProgress.contentStrategyComplete = true;
      state.workflowProgress.currentPhase = 'writing';
    }

    // Update workflow state
    if (state.discussions &&
        state.discussions[discussionId] &&
        state.discussions[discussionId].metadata &&
        state.discussions[discussionId].metadata.workflowState) {
      const workflowState = state.discussions[discussionId].metadata.workflowState;
      workflowState.completedPhases.push('planning');
      workflowState.currentPhase = 'writing';
      workflowState.phaseProgress = 0;
      workflowState.phaseStartTime = new Date().toISOString();
      workflowState.phaseAttempts = 1;
    }

    // Save updated state
    await stateStore.setState(sessionId, state);

    // Proceed to writing phase
    setTimeout(() => {
      logger.logPhaseTransition(sessionId, 'planning', 'writing');
      initiateWritingPhase(sessionId, discussionId, topic, contextData).catch((err: Error) => {
        logger.error(`Error initiating writing phase`, {
          sessionId,
          phase: 'writing',
          error: err.message || String(err),
          stack: err.stack
        });
      });
    }, 2000);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error validating planning phase`, {
      sessionId,
      phase: 'planning-validation',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Third phase of the workflow: Writing Phase (Content Generation)
 * Creates the actual content based on the research and planning
 */
export async function initiateWritingPhase(
  sessionId: string,
  discussionId: string,
  topic: string,
  contextData: any = {},
  skipValidation: boolean = false
): Promise<boolean> {
  logger.info(`Initiating writing phase for session ${sessionId}`, {
    sessionId,
    phase: 'writing',
    topic,
    discussionId,
    skipValidation
  });

  // Get the current state or return if not found
  const state = await stateStore.getState(sessionId);
  if (!state) {
    logger.error(`No state found for session ${sessionId}`);
    return false;
  }

  try {
    // Update workflow state to reflect writing phase
    if (state.discussions && state.discussions[discussionId]) {
      if (state.discussions[discussionId].data && state.discussions[discussionId].data.workflowState) {
        state.discussions[discussionId].data.workflowState.currentPhase = 'writing';
        state.discussions[discussionId].data.workflowState.phaseProgress = 0;
        state.discussions[discussionId].data.workflowState.phaseStartTime = new Date().toISOString();
      }
    }

    // Update the state
    await stateStore.setState(sessionId, state);

    // Find market research, keyword research, and content strategy artifacts
    const marketResearchArtifacts = state.artifacts && typeof state.artifacts === 'object'
      ? Object.values(state.artifacts).filter(artifact =>
          artifact.type === 'market-research' &&
          artifact.status === 'completed' as ArtifactStatus)
      : [];

    const keywordArtifacts = state.artifacts && typeof state.artifacts === 'object'
      ? Object.values(state.artifacts).filter(artifact =>
          artifact.type === 'seo-keywords' &&
          artifact.status === 'completed' as ArtifactStatus)
      : [];

    const contentStrategyArtifacts = state.artifacts && typeof state.artifacts === 'object'
      ? Object.values(state.artifacts).filter(artifact =>
          (artifact.type === 'content-strategy' || artifact.type === 'content-outline') &&
          artifact.status === 'completed' as ArtifactStatus)
      : [];

    // Build context from existing artifacts
    const contentGenerationContext = {
      topic,
      marketResearchInsights: marketResearchArtifacts.length > 0
        ? marketResearchArtifacts[0].content || marketResearchArtifacts[0].data
        : 'No market research available',
      keywordInsights: keywordArtifacts.length > 0
        ? keywordArtifacts[0].content || keywordArtifacts[0].data
        : 'No keyword research available',
      contentStrategy: contentStrategyArtifacts.length > 0
        ? contentStrategyArtifacts[0].content || contentStrategyArtifacts[0].data
        : 'No content strategy available',
      contentType: contextData.contentType || 'blog-article',
      targetAudience: contextData.targetAudience ||
        (marketResearchArtifacts.length > 0 &&
         typeof marketResearchArtifacts[0].content === 'object' &&
         marketResearchArtifacts[0].content?.audience) ||
        'general audience',
      ...contextData
    };

    // Create detailed content generation request with specific instructions
    const contentGenerationRequest: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.REQUEST,
      from: 'system',
      to: 'content-generation',
      content: {
        topic,
        instructions: [
          `Create comprehensive content about "${topic}" following the content strategy`,
          `Incorporate primary and secondary keywords naturally throughout the content`,
          `Structure the content according to the outline provided in the content strategy`,
          `Use a tone and style appropriate for the target audience`,
          `Include engaging headings, subheadings, and transitions between sections`
        ],
        context: contentGenerationContext,
        requiredComponents: ['title', 'introduction', 'body-sections', 'conclusion'],
        qualityThreshold: QUALITY_THRESHOLDS.CONTENT_GENERATION,
        discussionId,
        marketResearchArtifactIds: marketResearchArtifacts.map(a => a.id),
        keywordArtifactIds: keywordArtifacts.map(a => a.id),
        contentStrategyArtifactIds: contentStrategyArtifacts.map(a => a.id),
        skipValidation
      },
      timestamp: new Date().toISOString(),
      conversationId: discussionId,
      reasoning: {
        context: { topic, phase: 'writing', agent: 'content-generation' },
        thoughts: [
          'Content should integrate research insights and strategic planning',
          'Keyword placement must feel natural while optimizing for SEO',
          'Structure should follow the content strategy outline',
          'Tone and style must resonate with the target audience',
          'Content should be comprehensive, engaging, and valuable'
        ],
        conclusion: 'Generate high-quality content based on research and strategy',
        process: 'system',
        metadata: {
          confidence: 0.95,
          steps: [
            'Analyze research and strategy artifacts',
            'Structure content according to outline',
            'Incorporate keywords strategically',
            'Develop comprehensive content sections',
            'Ensure tone matches audience expectations'
          ]
        }
      }
    };

    // Process the content generation request
    await messageBus.sendMessage(sessionId, contentGenerationRequest, async (message: IterativeMessage) => {
      logger.info(`Content generation agent processing detailed content request`, {
        sessionId,
        phase: 'writing',
        messageId: message.id
      });

      // Validate message
      if (!message || typeof message !== 'object') {
        logger.error(`Invalid message object passed to content generation agent`, {
          sessionId,
          phase: 'writing',
          message: String(message)
        });

        return {
          success: false,
          error: `Invalid message object: ${message}`,
          response: {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: 'system',
            to: 'content-generation',
            type: IterativeMessageType.ERROR,
            content: { error: `Invalid message object: ${message}` },
            conversationId: sessionId
          }
        };
      }

      try {
        // The contentGenerationAgent.processMessage method expects (sessionId, message) parameters
        // But the messageBus.sendMessage callback provides (message, sessionId) parameters
        // So we need to make sure we're passing the parameters in the correct order
        return await contentGenerationAgent.processMessage(sessionId, message);
      } catch (error) {
        logger.error(`Error processing content generation message`, {
          sessionId,
          phase: 'writing',
          messageId: message.id,
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : 'No stack trace'
        });

        return {
          success: false,
          error: `Error processing message: ${error instanceof Error ? error.message : String(error)}`,
          response: {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: 'system',
            to: 'content-generation',
            type: IterativeMessageType.ERROR,
            content: { error: `Error processing message: ${error instanceof Error ? error.message : String(error)}` },
            conversationId: sessionId
          }
        };
      }
    });

    // Schedule validation of writing phase results
    setTimeout(() => {
      validateWritingPhase(sessionId, discussionId, topic, contextData).catch((err: Error) => {
        logger.error(`Error validating writing phase`, {
          sessionId,
          phase: 'writing-validation',
          error: err.message || String(err),
          stack: err.stack
        });
      });
    }, 15000);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error initiating writing phase`, {
      sessionId,
      phase: 'writing',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Validates the writing phase results and transitions to review phase if successful
 */
export async function validateWritingPhase(
  sessionId: string,
  discussionId: string,
  topic: string,
  contextData: any = {}
): Promise<boolean> {
  logger.info(`Validating writing phase results for session ${sessionId}`, {
    sessionId,
    phase: 'writing-validation',
    discussionId
  });

  // Get the current state
  const state = await stateStore.getState(sessionId);
  if (!state) {
    logger.error(`No state found for session ${sessionId}`);
    return false;
  }

  try {
    // Find content draft artifacts
    const contentDraftArtifacts = state.artifacts && typeof state.artifacts === 'object'
      ? Object.values(state.artifacts).filter(artifact =>
          artifact.type === 'content-draft' &&
          artifact.status === 'completed' as ArtifactStatus)
      : [];

    logger.info(`Writing validation: Found ${contentDraftArtifacts.length} content draft artifacts`, {
      sessionId,
      phase: 'writing-validation'
    });

    // Check if required artifacts exist
    const writingComplete = contentDraftArtifacts.length > 0;

    // If writing is not complete, check if we need to retry
    if (!writingComplete) {
      // Get current workflow state
      let workflowState: WorkflowState | undefined;
      if (state.discussions &&
          state.discussions[discussionId] &&
          state.discussions[discussionId].data &&
          state.discussions[discussionId].data.workflowState) {
        workflowState = state.discussions[discussionId].data.workflowState;
      }

      // If we've already attempted writing multiple times, proceed anyway with warning
      if (workflowState && workflowState.phaseAttempts >= 3) {
        logger.warn(`Writing phase incomplete after ${workflowState.phaseAttempts} attempts, proceeding with limited data`, {
          sessionId,
          phase: 'writing-validation'
        });

        // Proceed to review phase with warning
        setTimeout(() => {
          initiateReviewPhase(sessionId, discussionId, topic, contextData, true).catch((err: Error) => {
            logger.error(`Error initiating review phase`, {
              sessionId,
              phase: 'review',
              error: err.message || String(err),
              stack: err.stack
            });
          });
        }, 2000);

        return true;
      }

      // Retry writing phase
      if (workflowState) {
        workflowState.phaseAttempts += 1;

        // Update state with new attempt count
        if (state.discussions && state.discussions[discussionId] && state.discussions[discussionId].data) {
          state.discussions[discussionId].data.workflowState = workflowState;
          await stateStore.setState(sessionId, state);
        }

        logger.info(`Writing phase incomplete, retrying (attempt ${workflowState.phaseAttempts})`, {
          sessionId,
          phase: 'writing-validation'
        });

        // Retry writing phase
        setTimeout(() => {
          initiateWritingPhase(sessionId, discussionId, topic, contextData).catch((err: Error) => {
            logger.error(`Error retrying writing phase`, {
              sessionId,
              phase: 'writing',
              error: err.message || String(err),
              stack: err.stack
            });
          });
        }, 5000);

        return false;
      }
    }

    // Writing is complete, validate quality
    let qualityCheckPassed = true;
    const qualityIssues: string[] = [];

    // Check content draft quality
    if (contentDraftArtifacts.length > 0) {
      const contentDraft = contentDraftArtifacts[0];

      // Check for required fields
      const requiredFields = ['title', 'content', 'sections'];
      const missingFields = requiredFields.filter(field =>
        !contentDraft.content ||
        typeof contentDraft.content !== 'object' ||
        !(field in (contentDraft.content as Record<string, any>)) ||
        (Array.isArray((contentDraft.content as Record<string, any>)[field]) &&
         (contentDraft.content as Record<string, any>)[field].length === 0)
      );

      if (missingFields.length > 0) {
        qualityCheckPassed = false;
        qualityIssues.push(`Content draft missing required fields: ${missingFields.join(', ')}`);
      }

      // Check content length
      if (contentDraft.content &&
          typeof contentDraft.content === 'object' &&
          'content' in (contentDraft.content as Record<string, any>) &&
          typeof (contentDraft.content as Record<string, any>).content === 'string' &&
          (contentDraft.content as Record<string, any>).content.length < 500) {
        qualityCheckPassed = false;
        qualityIssues.push('Content draft is too short (less than 500 characters)');
      }
    }

    // If quality check failed, retry or proceed with warning
    if (!qualityCheckPassed) {
      // Get current workflow state
      let workflowState: WorkflowState | undefined;
      if (state.discussions &&
          state.discussions[discussionId] &&
          state.discussions[discussionId].data &&
          state.discussions[discussionId].data.workflowState) {
        workflowState = state.discussions[discussionId].data.workflowState;
      }

      // If we've already attempted writing multiple times, proceed anyway with warning
      if (workflowState && workflowState.phaseAttempts >= 3) {
        logger.warn(`Writing quality check failed after ${workflowState.phaseAttempts} attempts, proceeding with limited data`, {
          sessionId,
          phase: 'writing-validation',
          qualityIssues
        });

        // Proceed to review phase with warning
        setTimeout(() => {
          initiateReviewPhase(sessionId, discussionId, topic, contextData, true).catch((err: Error) => {
            logger.error(`Error initiating review phase`, {
              sessionId,
              phase: 'review',
              error: err.message || String(err),
              stack: err.stack
            });
          });
        }, 2000);

        return true;
      }

      // Retry writing phase with quality feedback
      if (workflowState) {
        workflowState.phaseAttempts += 1;

        // Update state with new attempt count
        if (state.discussions && state.discussions[discussionId] && state.discussions[discussionId].data) {
          state.discussions[discussionId].data.workflowState = workflowState;
          await stateStore.setState(sessionId, state);
        }

        logger.info(`Writing quality check failed, retrying with feedback (attempt ${workflowState.phaseAttempts})`, {
          sessionId,
          phase: 'writing-validation',
          qualityIssues
        });

        // Send feedback to content generation agent
        const feedbackMessage: IterativeMessage = {
          id: uuidv4(),
          type: IterativeMessageType.FEEDBACK,
          from: 'system',
          to: 'content-generation',
          content: {
            feedback: `Quality check failed: ${qualityIssues.join(', ')}`,
            priority: FeedbackPriority.HIGH,
            requestImprovement: true
          },
          timestamp: new Date().toISOString(),
          conversationId: discussionId
        };

        await messageBus.sendMessage(sessionId, feedbackMessage, async (message: IterativeMessage) => {
          return await contentGenerationAgent.processMessage(sessionId, message);
        });

        // Schedule another validation check
        setTimeout(() => {
          validateWritingPhase(sessionId, discussionId, topic, contextData).catch((err: Error) => {
            logger.error(`Error validating writing phase`, {
              sessionId,
              phase: 'writing-validation',
              error: err.message || String(err),
              stack: err.stack
            });
          });
        }, 15000);

        return false;
      }
    }

    // Writing phase is complete and quality check passed
    logger.info(`Writing phase complete and validated successfully`, {
      sessionId,
      phase: 'writing-validation'
    });

    // Update workflow progress
    if (state.workflowProgress) {
      state.workflowProgress.contentGenerationComplete = true;
      state.workflowProgress.currentPhase = 'review';
    }

    // Update workflow state
    if (state.discussions &&
        state.discussions[discussionId] &&
        state.discussions[discussionId].data &&
        state.discussions[discussionId].data.workflowState) {
      const workflowState = state.discussions[discussionId].data.workflowState;
      workflowState.completedPhases.push('writing');
      workflowState.currentPhase = 'review';
      workflowState.phaseProgress = 0;
      workflowState.phaseStartTime = new Date().toISOString();
      workflowState.phaseAttempts = 1;
    }

    // Save updated state
    await stateStore.setState(sessionId, state);

    // Proceed to review phase
    setTimeout(() => {
      logger.logPhaseTransition(sessionId, 'writing', 'review');
      initiateReviewPhase(sessionId, discussionId, topic, contextData).catch((err: Error) => {
        logger.error(`Error initiating review phase`, {
          sessionId,
          phase: 'review',
          error: err.message || String(err),
          stack: err.stack
        });
      });
    }, 2000);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error validating writing phase`, {
      sessionId,
      phase: 'writing-validation',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Fourth phase of the workflow: Review Phase
 * Coordinates feedback from all agents on the generated content
 */
export async function initiateReviewPhase(
  sessionId: string,
  discussionId: string,
  topic: string,
  contextData: any = {},
  skipValidation: boolean = false
): Promise<boolean> {
  logger.info(`Initiating review phase for session ${sessionId}`, {
    sessionId,
    phase: 'review',
    topic,
    discussionId,
    skipValidation
  });

  // Implementation will be added in the next iteration
  return true;
}

/**
 * Legacy function for backward compatibility
 */
export async function triggerContentStrategy(
  sessionId: string,
  discussionId: string,
  topic: string,
  contextData: any = {}
): Promise<boolean> {
  console.log(`Triggering content strategy phase for session ${sessionId}`);

  // Get the current state or return if not found
  const state = await stateStore.getState(sessionId);
  if (!state) {
    console.error(`No state found for session ${sessionId}`);
    return false;
  }

  try {
    // Find market research and keyword research artifacts
    const marketResearchArtifacts = state.artifacts && typeof state.artifacts === 'object'
      ? Object.values(state.artifacts).filter(artifact => artifact.type === 'market-research' && artifact.status === 'completed' as ArtifactStatus)
      : [];

    console.log(`Content Strategy phase: Found ${marketResearchArtifacts.length} market research artifacts`);

    const keywordArtifacts = state.artifacts && typeof state.artifacts === 'object'
      ? Object.values(state.artifacts).filter(artifact => artifact.type === 'seo-keywords' && artifact.status === 'completed' as ArtifactStatus)
      : [];

    console.log(`Content Strategy phase: Found ${keywordArtifacts.length} keyword artifacts`);

    // Find messages from market research and SEO keyword agents
    const marketResearchMessages = Array.isArray(state.messages)
      ? state.messages.filter(msg => msg.from === 'market-research' &&
        (msg.type === IterativeMessageType.CONSULTATION_RESPONSE ||
         msg.type === IterativeMessageType.ARTIFACT_DELIVERY))
      : [];

    const keywordMessages = Array.isArray(state.messages)
      ? state.messages.filter(msg => msg.from === 'seo-keyword' &&
        (msg.type === IterativeMessageType.CONSULTATION_RESPONSE ||
         msg.type === IterativeMessageType.ARTIFACT_DELIVERY))
      : [];

    // Build context from existing artifacts and messages
    const consultationContext = {
      topic,
      marketResearchInsights: marketResearchArtifacts.length > 0
        ? marketResearchArtifacts[0].content || marketResearchArtifacts[0].data
        : marketResearchMessages.length > 0
          ? marketResearchMessages[0].content?.response || 'No market research available'
          : 'No market research available',
      keywordInsights: keywordArtifacts.length > 0
        ? keywordArtifacts[0].content || keywordArtifacts[0].data
        : keywordMessages.length > 0
          ? keywordMessages[0].content?.response || 'No keyword research available'
          : 'No keyword research available',
      ...contextData
    };

    // Create content strategy consultation request that references the previous research
    const contentStrategyConsultation: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.REQUEST,
      from: 'system',
      to: 'content-strategy',
      content: {
        topic,
        question: `Based on the market research and keyword insights, what content strategy would be most effective for "${topic}"?`,
        context: consultationContext,
        discussionId,
        marketResearchArtifactIds: marketResearchArtifacts.map(a => a.id),
        keywordArtifactIds: keywordArtifacts.map(a => a.id),
      },
      timestamp: new Date().toISOString(),
      conversationId: discussionId
    };

    // Process the consultation request
    await messageBus.sendMessage(sessionId, contentStrategyConsultation, async (message: IterativeMessage) => {
      console.log(`Content strategy agent processing consultation with previous research`);
      // Ensure we're passing valid parameters
      if (!message || typeof message !== 'object') {
        console.error(`Invalid message object passed to content strategy agent: ${message}`);
        return {
          success: false,
          error: `Invalid message object: ${message}`,
          message: 'Failed to process message due to invalid message object',
          response: {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: 'system',
            to: 'content-strategy',
            type: IterativeMessageType.ERROR,
            content: { error: `Invalid message object: ${message}` },
            conversationId: sessionId
          }
        };
      }
      return await contentStrategyAgent.processMessage(sessionId, message);
    });

    // Schedule the collaborative discussion phase
    setTimeout(() => {
      facilitateAgentDiscussion(sessionId, discussionId, topic, contextData).catch(console.error);
    }, 3000);

    return true;
  } catch (error) {
    console.error(`Error triggering content strategy phase:`, error);
    return false;
  }
}

/**
 * Third phase: Collaborative discussion between all agents
 * This is the key part that enables dynamic agent collaboration
 */
export async function facilitateAgentDiscussion(
  sessionId: string,
  discussionId: string,
  topic: string,
  contextData: any = {}
): Promise<boolean> {
  console.log(`Facilitating collaborative discussion for session ${sessionId}`);

  // Get the current state or return if not found
  const state = await stateStore.getState(sessionId);
  if (!state) {
    console.error(`No state found for session ${sessionId}`);
    return false;
  }

  try {
    // Find content strategy artifact or messages
    const contentStrategyArtifacts = state.artifacts && typeof state.artifacts === 'object'
      ? Object.values(state.artifacts).filter(artifact => artifact.type === 'content-strategy' && artifact.status === 'completed' as ArtifactStatus)
      : [];

    console.log(`Discussion phase: Found ${contentStrategyArtifacts.length} content strategy artifacts`);

    const contentStrategyMessages = Array.isArray(state.messages)
      ? state.messages.filter(msg => msg.from === 'content-strategy' &&
        (msg.type === IterativeMessageType.CONSULTATION_RESPONSE ||
         msg.type === IterativeMessageType.ARTIFACT_DELIVERY))
      : [];

    // Get the content strategy
    const contentStrategy = contentStrategyArtifacts.length > 0
      ? contentStrategyArtifacts[0].content || contentStrategyArtifacts[0].data
      : contentStrategyMessages.length > 0
        ? contentStrategyMessages[0].content?.response || contentStrategyMessages[0].content?.data || 'No content strategy available'
        : 'No content strategy available';

    // Create a discussion start message
    const discussionStartMessage: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.UPDATE,
      from: 'system',
      to: 'all',
      content: {
        topic,
        discussionTopic: `Collaborative planning for "${topic}" content`,
        instructions: `This is a multi-turn collaborative discussion to refine the content plan for "${topic}". All agents should actively participate and provide insights from their area of expertise.`,
        context: {
          contentStrategy,
          ...contextData
        },
        discussionId
      },
      timestamp: new Date().toISOString(),
      conversationId: discussionId
    };

    // Add to messages and update discussion in state
    if (!state.messages) state.messages = [];
    state.messages.push(discussionStartMessage);

    if (!state.discussions) state.discussions = {};
    if (state.discussions[discussionId]) {
      state.discussions[discussionId].status = 'active';
      state.discussions[discussionId].updatedAt = discussionStartMessage.timestamp;
    }

    // Save the updated state
    await stateStore.setState(sessionId, state);

    // Send discussion start to all agents
    await Promise.all([
      // Ensure we're passing valid parameters to each agent
      (async () => {
        try {
          if (!discussionStartMessage || typeof discussionStartMessage !== 'object') {
            console.error(`Invalid message object for market research agent: ${discussionStartMessage}`);
            return;
          }
          return await marketResearchAgent.processMessage(sessionId, discussionStartMessage);
        } catch (error) {
          console.error(`Error processing message in market research agent: ${error}`);
        }
      })(),
      (async () => {
        try {
          if (!discussionStartMessage || typeof discussionStartMessage !== 'object') {
            console.error(`Invalid message object for SEO keyword agent: ${discussionStartMessage}`);
            return;
          }
          return await seoKeywordAgent.processMessage(sessionId, discussionStartMessage);
        } catch (error) {
          console.error(`Error processing message in SEO keyword agent: ${error}`);
        }
      })(),
      (async () => {
        try {
          if (!discussionStartMessage || typeof discussionStartMessage !== 'object') {
            console.error(`Invalid message object for content strategy agent: ${discussionStartMessage}`);
            return;
          }
          return await contentStrategyAgent.processMessage(sessionId, discussionStartMessage);
        } catch (error) {
          console.error(`Error processing message in content strategy agent: ${error}`);
        }
      })(),
      (async () => {
        try {
          if (!discussionStartMessage || typeof discussionStartMessage !== 'object') {
            console.error(`Invalid message object for content generation agent: ${discussionStartMessage}`);
            return;
          }
          return await contentGenerationAgent.processMessage(sessionId, discussionStartMessage);
        } catch (error) {
          console.error(`Error processing message in content generation agent: ${error}`);
        }
      })(),
      (async () => {
        try {
          if (!discussionStartMessage || typeof discussionStartMessage !== 'object') {
            console.error(`Invalid message object for SEO optimization agent: ${discussionStartMessage}`);
            return;
          }
          return await seoOptimizationAgent.processMessage(sessionId, discussionStartMessage);
        } catch (error) {
          console.error(`Error processing message in SEO optimization agent: ${error}`);
        }
      })()
    ]);

    // Trigger the first round of multi-agent collaboration

    // 1. Content Strategy asks for audience specifics from Market Research
    const strategyToResearch: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.REQUEST,
      from: 'content-strategy',
      to: 'market-research',
      content: {
        question: `What specific audience segments should we prioritize for "${topic}" and what are their key pain points?`,
        context: {
          currentStrategy: contentStrategy
        },
        discussionId
      },
      timestamp: new Date().toISOString(),
      conversationId: discussionId,
      inReplyTo: discussionStartMessage.id
    };

    // 2. Content Strategy asks for keyword prioritization from SEO
    const strategyToSeo: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.REQUEST,
      from: 'content-strategy',
      to: 'seo-keyword',
      content: {
        question: `Which keywords should be prioritized in the title, headings, and body for maximum SEO impact for "${topic}"?`,
        context: {
          currentStrategy: contentStrategy
        },
        discussionId
      },
      timestamp: new Date().toISOString(),
      conversationId: discussionId,
      inReplyTo: discussionStartMessage.id
    };

    // 3. Content Writer asks for structure guidance from Content Strategy
    const writerToStrategy: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.REQUEST,
      from: 'content-generation',
      to: 'content-strategy',
      content: {
        question: `What content structure would be most effective for engaging our target audience while covering "${topic}" comprehensively?`,
        context: {
          currentStrategy: contentStrategy
        },
        discussionId
      },
      timestamp: new Date().toISOString(),
      conversationId: discussionId,
      inReplyTo: discussionStartMessage.id
    };

    // Process all consultation requests in parallel
    await Promise.all([
      messageBus.sendMessage(sessionId, strategyToResearch, async (message: IterativeMessage) => {
        // Ensure we're passing valid parameters
        if (!message || typeof message !== 'object') {
          console.error(`Invalid message object passed to market research agent: ${message}`);
          return {
            success: false,
            error: `Invalid message object: ${message}`,
            message: 'Failed to process message due to invalid message object',
            response: {
              id: uuidv4(),
              timestamp: new Date().toISOString(),
              from: 'system',
              to: 'market-research',
              type: IterativeMessageType.ERROR,
              content: { error: `Invalid message object: ${message}` },
              conversationId: sessionId
            }
          };
        }
        return await marketResearchAgent.processMessage(sessionId, message);
      }),

      messageBus.sendMessage(sessionId, strategyToSeo, async (message: IterativeMessage) => {
        // Ensure we're passing valid parameters
        if (!message || typeof message !== 'object') {
          console.error(`Invalid message object passed to SEO keyword agent: ${message}`);
          return {
            success: false,
            error: `Invalid message object: ${message}`,
            message: 'Failed to process message due to invalid message object',
            response: {
              id: uuidv4(),
              timestamp: new Date().toISOString(),
              from: 'system',
              to: 'seo-keyword',
              type: IterativeMessageType.ERROR,
              content: { error: `Invalid message object: ${message}` },
              conversationId: sessionId
            }
          };
        }
        return await seoKeywordAgent.processMessage(sessionId, message);
      }),

      messageBus.sendMessage(sessionId, writerToStrategy, async (message: IterativeMessage) => {
        // Ensure we're passing valid parameters
        if (!message || typeof message !== 'object') {
          console.error(`Invalid message object passed to content strategy agent: ${message}`);
          return {
            success: false,
            error: `Invalid message object: ${message}`,
            message: 'Failed to process message due to invalid message object',
            response: {
              id: uuidv4(),
              timestamp: new Date().toISOString(),
              from: 'system',
              to: 'content-strategy',
              type: IterativeMessageType.ERROR,
              content: { error: `Invalid message object: ${message}` },
              conversationId: sessionId
            }
          };
        }
        return await contentStrategyAgent.processMessage(sessionId, message);
      })
    ]);

    // Schedule the content generation phase after discussions
    setTimeout(() => {
      triggerContentGeneration(sessionId, discussionId, topic, contextData).catch(console.error);
    }, 5000);

    return true;
  } catch (error) {
    console.error(`Error facilitating agent discussion:`, error);
    return false;
  }
}

/**
 * Fourth phase: Content generation with collaborative feedback
 */
export async function triggerContentGeneration(
  sessionId: string,
  discussionId: string,
  topic: string,
  contextData: any = {}
): Promise<boolean> {
  console.log(`Triggering content generation for session ${sessionId}`);

  // Get the current state or return if not found
  const state = await stateStore.getState(sessionId);
  if (!state) {
    console.error(`No state found for session ${sessionId}`);
    return false;
  }

  try {
    // Find all relevant artifacts and consultation responses
    const allArtifacts = state.artifacts && typeof state.artifacts === 'object' ? Object.values(state.artifacts) : [];
    console.log(`Agent discussion phase: Found ${allArtifacts.length} total artifacts`);
    const allMessages = state.messages || [];

    // Extract insights from previous discussions
    const discussionMessages = Array.isArray(allMessages)
      ? allMessages.filter(msg =>
          msg.conversationId === discussionId &&
          msg.type === IterativeMessageType.RESPONSE)
      : [];

    // Compile insights for content generation
    const compiledInsights = {
      marketResearch: discussionMessages
        .filter(msg => msg.from === 'market-research')
        .map(msg => msg.content?.response || '')
        .join('\n\n'),

      seoKeywords: discussionMessages
        .filter(msg => msg.from === 'seo-keyword')
        .map(msg => msg.content?.response || '')
        .join('\n\n'),

      contentStrategy: discussionMessages
        .filter(msg => msg.from === 'content-strategy')
        .map(msg => msg.content?.response || '')
        .join('\n\n'),

      // Add existing artifacts
      artifacts: allArtifacts.map(artifact => ({
          type: artifact.type,
          content: artifact.content || artifact.data || ''
        }))
    };

    // Create the content generation request
    const contentGenRequest: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.REQUEST,
      from: 'system',
      to: 'content-generation',
      content: {
        topic,
        contentType: state.contentType || 'blog-article',
        targetAudience: state.targetAudience || 'general audience',
        tone: state.tone || 'informative',
        keywords: state.keywords || [],
        insights: compiledInsights,
        discussionId
      },
      timestamp: new Date().toISOString(),
      conversationId: discussionId
    };

    // Process the content generation request
    await messageBus.sendMessage(sessionId, contentGenRequest, async (message: IterativeMessage) => {
      console.log(`Content generation agent processing generation request with collaborative insights`);
      // Ensure we're passing valid parameters
      if (!message || typeof message !== 'object') {
        console.error(`Invalid message object passed to content generation agent: ${message}`);
        return {
          success: false,
          error: `Invalid message object: ${message}`,
          message: 'Failed to process message due to invalid message object',
          response: {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: 'system',
            to: 'content-generation',
            type: IterativeMessageType.ERROR,
            content: { error: `Invalid message object: ${message}` },
            conversationId: sessionId
          }
        };
      }
      return await contentGenerationAgent.processMessage(sessionId, message);
    });

    // Schedule the feedback and optimization phase
    setTimeout(() => {
      triggerFeedbackPhase(sessionId, discussionId, topic, contextData).catch(console.error);
    }, 7000);

    return true;
  } catch (error) {
    console.error(`Error triggering content generation:`, error);
    return false;
  }
}

/**
 * Fifth phase: Collaborative feedback and optimization
 */
export async function triggerFeedbackPhase(
  sessionId: string,
  discussionId: string,
  topic: string,
  contextData: any = {}
): Promise<boolean> {
  console.log(`Triggering feedback phase for session ${sessionId}`);

  // Get the current state or return if not found
  const state = await stateStore.getState(sessionId);
  if (!state) {
    console.error(`No state found for session ${sessionId}`);
    return false;
  }

  try {
    // Find content artifact - ensure we're using the standardized object format
    const contentArtifacts = state.artifacts && typeof state.artifacts === 'object'
      ? Object.values(state.artifacts).filter(artifact => artifact.type === 'content' && artifact.status === 'completed' as ArtifactStatus)
      : [];

    const contentMessages = Array.isArray(state.messages)
      ? state.messages.filter(msg => msg.from === 'content-generation' &&
          msg.type === IterativeMessageType.ARTIFACT_DELIVERY)
      : [];

    // Get the generated content
    let generatedContent = 'No content available';
    let contentId = '';

    if (contentArtifacts.length > 0) {
      generatedContent = contentArtifacts[0].content || contentArtifacts[0].data || 'No content available';
      contentId = contentArtifacts[0].id;
    } else if (contentMessages.length > 0) {
      generatedContent = contentMessages[0].content?.data || contentMessages[0].content?.content || 'No content available';
      contentId = contentMessages[0].content?.artifactId || uuidv4();
    }

    // If no content is found, request real content generation from the content-generation agent
    if (generatedContent === 'No content available') {
      console.log(`[DEBUG] Session ${sessionId} - No content artifacts found`);
      console.log(`[DEBUG] Session ${sessionId} - Requesting content generation from content-generation agent`);

      // Request real content generation instead of creating mock content
      try {
        // Create a discussion contribution message to the content generation agent to generate content
        const discussionId = uuidv4();

        // Find all the previous insights and discussions
        const allAgentInsights = Object.values(state.messages || {})
          .filter(msg => msg.type === IterativeMessageType.DISCUSSION_CONTRIBUTION)
          .map(msg => ({
            agent: msg.from,
            contribution: msg.content?.contribution || msg.content?.perspective || ''
          }));

        const formattedInsights = allAgentInsights
          .map(insight => `${insight.agent}: ${insight.contribution}`)
          .join('\n\n');

        // Send a request to the content generation agent
        const contentRequest: IterativeMessage = {
          id: uuidv4(),
          sessionId,
          from: 'system',
          to: 'content-generation',
          type: IterativeMessageType.DISCUSSION_CONTRIBUTION,
          content: {
            discussionId,
            contribution: `Please generate content for ${topic} based on all accumulated insights. This is an explicit request for real content generation.`,
            forceRealContent: true, // Force real content generation
            insights: formattedInsights
          },
          conversationId: discussionId,
          timestamp: new Date().toISOString()
        };

        // Add the request message to the state
        if (!state.messages) state.messages = [];
        if (Array.isArray(state.messages)) {
          state.messages.push(contentRequest);
        }

        // Save the updated state with the new message
        await stateStore.setState(sessionId, state);

        // Process the message directly with the content generation agent
        try {
          console.log(`[DEBUG] Session ${sessionId} - Processing content generation request directly with content generation agent`);
          const result = await contentGenerationAgent.processMessage(sessionId, contentRequest);
          console.log(`[DEBUG] Session ${sessionId} - Content generation agent processed request:`, result ? 'Successfully' : 'Failed');

          // If the agent returned a response, add it to the state
          if (result && result.response) {
            console.log(`[DEBUG] Session ${sessionId} - Adding content generation response to state`);
            if (Array.isArray(state.messages)) {
              state.messages.push(result.response);
            }

            // If the agent created an artifact, ensure it's properly stored
            if (result.artifactUpdates && result.artifactUpdates.new) {
              console.log(`[DEBUG] Session ${sessionId} - Adding new artifacts to state:`, Object.keys(result.artifactUpdates.new).length);

              // Initialize artifacts as an object if it doesn't exist
              if (!state.artifacts) {
                console.log(`[DEBUG] Session ${sessionId} - Initializing artifacts object`);
                state.artifacts = {};
              }

              // Always ensure artifacts are stored in object format
              if (Array.isArray(state.artifacts)) {
                // Convert array to object format (standardized format)
                console.log(`[DEBUG] Session ${sessionId} - Converting artifacts array to object format`);
                const artifactsObj: Record<string, any> = {};
                state.artifacts.forEach(a => {
                  if (a && a.id) artifactsObj[a.id] = a;
                });

                // Add new artifacts
                Object.entries(result.artifactUpdates.new).forEach(([id, artifact]) => {
                  console.log(`[DEBUG] Session ${sessionId} - Adding new artifact ${id} to converted object`);
                  artifactsObj[id] = artifact;
                });

                // Replace the array with the object format
                state.artifacts = artifactsObj;
              } else {
                // Add new artifacts to existing object
                Object.entries(result.artifactUpdates.new).forEach(([id, artifact]) => {
                  console.log(`[DEBUG] Session ${sessionId} - Adding new artifact ${id} to existing object`);
                  state.artifacts[id] = artifact;
                });
              }

              console.log(`[DEBUG] Session ${sessionId} - Artifacts now stored in standardized object format with ${Object.keys(state.artifacts).length} artifacts`);

              // Log the keys of all artifacts for debugging
              console.log(`[DEBUG] Session ${sessionId} - Artifact keys:`, Object.keys(state.artifacts));
            }

            // Save the updated state with the response and artifacts
            console.log(`[DEBUG] Session ${sessionId} - Saving state with updated artifacts and messages`);
            await stateStore.setState(sessionId, state);

            // Verify the state was saved correctly
            const verificationState = await stateStore.getState(sessionId);
            console.log(`[DEBUG] Session ${sessionId} - Verification after save:`, {
              hasArtifacts: !!verificationState?.artifacts,
              artifactsType: verificationState?.artifacts ? typeof verificationState.artifacts : 'undefined',
              artifactsCount: verificationState?.artifacts ?
                (Array.isArray(verificationState.artifacts) ? verificationState.artifacts.length : Object.keys(verificationState.artifacts).length) : 0,
              artifactsKeys: verificationState?.artifacts && typeof verificationState.artifacts === 'object' ? Object.keys(verificationState.artifacts) : []
            });
          }
        } catch (processingError) {
          console.error(`[ERROR] Session ${sessionId} - Error processing content generation request:`, processingError);
        }

        // Log the content generation request
        console.log(`[DEBUG] Session ${sessionId} - Sent content generation request to content-generation agent`);

        // Update the state in the state store with the new message
        await stateStore.setState(sessionId, state);

        // Return true to indicate success
        return true;
      } catch (error) {
        console.error(`[ERROR] Session ${sessionId} - Failed to request content generation:`, error);
        // Continue with the rest of the function without the content
      }
    }

    // Create feedback requests for all agents to review the content
    const feedbackRequests = [
      {
        id: uuidv4(),
        type: IterativeMessageType.REQUEST,
        from: 'system',
        to: 'market-research',
        content: {
          contentId,
          content: generatedContent,
          question: `Does this content address the key market trends and audience needs for "${topic}"? What specific improvements would make it more relevant to our target audience?`,
          artifactType: 'content',
          discussionId
        },
        timestamp: new Date().toISOString(),
        conversationId: discussionId
      },
      {
        id: uuidv4(),
        type: IterativeMessageType.REQUEST,
        from: 'system',
        to: 'seo-keyword',
        content: {
          contentId,
          content: generatedContent,
          question: `How effectively does this content incorporate key SEO elements for "${topic}"? What specific keyword optimizations would improve its search performance?`,
          artifactType: 'content',
          discussionId
        },
        timestamp: new Date().toISOString(),
        conversationId: discussionId
      },
      {
        id: uuidv4(),
        type: IterativeMessageType.REQUEST,
        from: 'system',
        to: 'content-strategy',
        content: {
          contentId,
          content: generatedContent,
          question: `How well does this content align with our content strategy for "${topic}"? What structural or narrative improvements would strengthen it?`,
          artifactType: 'content',
          discussionId
        },
        timestamp: new Date().toISOString(),
        conversationId: discussionId
      }
    ];

    // Process all feedback requests in parallel
    await Promise.all(
      feedbackRequests.map(async (request) => {
        try {
          const targetAgent = request.to as string;
          console.log(`${targetAgent} agent processing feedback request`);

          switch (targetAgent) {
            case 'market-research':
              return await marketResearchAgent.processMessage(sessionId, request);
            case 'seo-keyword':
              return await seoKeywordAgent.processMessage(sessionId, request);
            case 'content-strategy':
              return await contentStrategyAgent.processMessage(sessionId, request);
            default:
              return null;
          }
        } catch (error) {
          console.error(`Error processing feedback request:`, error);
          return null;
        }
      })
    );

    // Schedule the final optimization phase
    setTimeout(() => {
      triggerFinalOptimization(sessionId, discussionId, topic, contextData).catch(console.error);
    }, 5000);

    return true;
  } catch (error) {
    console.error(`Error triggering feedback phase:`, error);
    return false;
  }
}

/**
 * Final phase: SEO optimization and finalization
 */
export async function triggerFinalOptimization(
  sessionId: string,
  discussionId: string,
  topic: string,
  contextData: any = {}
): Promise<boolean> {
  console.log(`Triggering final optimization for session ${sessionId}`);

  // Get the current state or return if not found
  const state = await stateStore.getState(sessionId);
  if (!state) {
    console.error(`No state found for session ${sessionId}`);
    return false;
  }

  try {
    // Find content artifact - ensure we're using the standardized object format
    console.log(`[DEBUG] Session ${sessionId} - Looking for content artifacts in state:`, {
      hasArtifacts: !!state.artifacts,
      artifactsType: typeof state.artifacts,
      artifactsCount: state.artifacts ?
        (Array.isArray(state.artifacts) ? state.artifacts.length : Object.keys(state.artifacts).length) : 0,
      artifactsKeys: state.artifacts && typeof state.artifacts === 'object' ? Object.keys(state.artifacts) : []
    });

    const contentArtifacts = state.artifacts && typeof state.artifacts === 'object'
      ? Object.values(state.artifacts).filter(artifact => {
          const isContentArtifact = artifact.type === 'content' ||
                                   artifact.type === 'blog-post' ||
                                   artifact.type === 'article';
          const isCompleted = artifact.status === 'completed' as ArtifactStatus;

          console.log(`[DEBUG] Session ${sessionId} - Checking artifact ${artifact.id}:`, {
            type: artifact.type,
            isContentArtifact,
            status: artifact.status,
            isCompleted
          });

          return isContentArtifact && isCompleted;
        })
      : [];

    // Find feedback responses
    const feedbackResponses = Array.isArray(state.messages)
      ? state.messages.filter(msg =>
          msg.type === 'RESPONSE' &&
          msg.conversationId === discussionId &&
          msg.content?.feedback)
      : [];

    // Get the content
    let content = '';
    let contentId = '';

    if (contentArtifacts.length > 0) {
      content = contentArtifacts[0].content || contentArtifacts[0].data || '';
      contentId = contentArtifacts[0].id;
    } else {
      // Look for content in messages if no artifact is found
      const contentMessages = Array.isArray(state.messages)
        ? state.messages.filter(msg => msg.from === 'content-generation' &&
            msg.type === IterativeMessageType.ARTIFACT_DELIVERY)
        : [];

      if (contentMessages.length > 0) {
        content = contentMessages[0].content?.data || contentMessages[0].content?.content || '';
        contentId = contentMessages[0].content?.artifactId || uuidv4();
      }
    }

    // If no content is found, request real content generation from content-generation agent
    if (!content) {
      console.log(`[DEBUG] Session ${sessionId} - No content found for optimization`);
      console.log(`[DEBUG] Session ${sessionId} - Requesting content generation from content-generation agent`);

      // Request real content generation instead of creating mock content
      try {
        // Create a discussion contribution message to the content generation agent to generate content
        const discussionId = uuidv4();

        // Find all the previous insights and discussions
        const allAgentInsights = Object.values(state.messages || {})
          .filter(msg => msg.type === IterativeMessageType.DISCUSSION_CONTRIBUTION)
          .map(msg => ({
            agent: msg.from,
            contribution: msg.content?.contribution || msg.content?.perspective || ''
          }));

        const formattedInsights = allAgentInsights
          .map(insight => `${insight.agent}: ${insight.contribution}`)
          .join('\n\n');

        // Send a request to the content generation agent
        const contentRequest: IterativeMessage = {
          id: uuidv4(),
          sessionId,
          from: 'system',
          to: 'content-generation',
          type: IterativeMessageType.DISCUSSION_CONTRIBUTION,
          content: {
            discussionId,
            contribution: `Please generate SEO-optimized content for ${topic} based on all accumulated insights. This is an explicit request for real content generation.`,
            forceRealContent: true, // Force real content generation
            insights: formattedInsights
          },
          conversationId: discussionId,
          timestamp: new Date().toISOString()
        };

        // Add the request message to the state
        if (!state.messages) state.messages = [];
        if (Array.isArray(state.messages)) {
          state.messages.push(contentRequest);
        }

        // Push the message to the message bus to be processed by the content generation agent
        try {
          const sessionBus = messageBus.createSessionBus(sessionId);
          await sessionBus.sendMessage(contentRequest);
          console.log(`[DEBUG] Session ${sessionId} - Content generation request sent through message bus`);
        } catch (busError) {
          console.error(`[ERROR] Session ${sessionId} - Error publishing content generation request:`, busError);
        }

        // Update the state in the state store with the new message
        await stateStore.setState(sessionId, state);

        // Return true to indicate success
        return true;
      } catch (error) {
        console.error(`[ERROR] Session ${sessionId} - Failed to request content generation:`, error);
        // Continue with the rest of the function without the content
      }
    }

    // Compile feedback for SEO optimization
    const compiledFeedback = feedbackResponses.map(response => ({
      from: response.from,
      feedback: response.content?.feedback || response.content?.response || '',
      suggestions: response.content?.suggestions || []
    }));

    // Create SEO optimization request
    const seoOptimizationRequest: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.REQUEST,
      from: 'system',
      to: 'seo-optimization',
      content: {
        contentId,
        content,
        feedback: compiledFeedback,
        topic,
        context: contextData,
        discussionId
      },
      timestamp: new Date().toISOString(),
      conversationId: discussionId
    };

    // Process the SEO optimization request directly
    try {
      console.log(`SEO optimization agent processing optimization request`);
      await seoOptimizationAgent.processMessage(sessionId, seoOptimizationRequest);
    } catch (error) {
      console.error(`Error processing SEO optimization request:`, error);
    }

    // Complete the workflow
    console.log(`Workflow completed for session ${sessionId}`);

    // Update workflow progress
    if (state.workflowProgress) {
      state.workflowProgress.marketResearchComplete = true;
      state.workflowProgress.keywordResearchComplete = true;
      state.workflowProgress.contentStrategyComplete = true;
      state.workflowProgress.contentGenerationComplete = true;
      state.workflowProgress.seoOptimizationComplete = true;
    }

    // Update discussion status
    if (state.discussions && state.discussions[discussionId]) {
      state.discussions[discussionId].status = 'completed';
      state.discussions[discussionId].updatedAt = new Date().toISOString();
      state.discussions[discussionId].resolution = 'Content generation and optimization complete';
    }

    // Add system message about completion
    const completionMessage: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.UPDATE,
      from: 'system',
      to: 'all',
      content: {
        message: `Collaborative workflow completed for "${topic}". All agents have contributed to the content.`,
        discussionId
      },
      timestamp: new Date().toISOString(),
      conversationId: discussionId
    };

    if (!state.messages) state.messages = [];
    state.messages.push(completionMessage);

    // Save the final state
    console.log(`[DEBUG] Session ${sessionId} - Saving final state with artifacts:`, {
      artifactsCount: state.artifacts ?
        (Array.isArray(state.artifacts) ? state.artifacts.length : Object.keys(state.artifacts).length) : 0,
      artifactsKeys: state.artifacts && typeof state.artifacts === 'object' ? Object.keys(state.artifacts) : []
    });

    await stateStore.setState(sessionId, state);

    // Verify the state was saved correctly
    const verificationState = await stateStore.getState(sessionId);
    console.log(`[DEBUG] Session ${sessionId} - Final verification:`, {
      hasArtifacts: !!verificationState?.artifacts,
      artifactsType: verificationState?.artifacts ? typeof verificationState.artifacts : 'undefined',
      artifactsCount: verificationState?.artifacts ?
        (Array.isArray(verificationState.artifacts) ? verificationState.artifacts.length : Object.keys(verificationState.artifacts).length) : 0,
      artifactsKeys: verificationState?.artifacts && typeof verificationState.artifacts === 'object' ? Object.keys(verificationState.artifacts) : []
    });

    return true;
  } catch (error) {
    console.error(`Error triggering final optimization:`, error);
    return false;
  }
}
