// src/app/(payload)/api/agents/collaborative-iteration/workflows/workflow-orchestrator.ts

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeMessageType,
  IterativeCollaborationState,
  Goal,
  GoalStatus,
  ArtifactStatus
} from '../types';
import { stateStore } from '../utils/stateStore';
import logger from '../utils/logger';
import { initiateResearchPhase, validateResearchPhase } from './research-phase-workflow';
import { initiateContentGenerationPhase, validateContentGeneration } from './content-generation-workflow';
import { initiateReviewPhase, validateSeoOptimization } from './review-phase-workflow';

// Workflow state interface
interface WorkflowState {
  currentPhase: 'initialization' | 'research' | 'creation' | 'review' | 'finalization' | 'complete';
  phaseProgress: number;
  phaseStartTime: string;
  phaseAttempts: number;
  completedPhases: string[];
  failedAttempts: Record<string, number>;
  qualityScores: Record<string, number>;
  aggregatedFeedback: string[];
}

/**
 * Orchestrates the entire collaborative workflow between specialized agents
 * with enhanced structure, validation, and quality checks
 *
 * This workflow creates a dynamic collaboration pattern where:
 * 1. Each agent can consult other agents for specific information
 * 2. Agents provide structured feedback on each other's artifacts
 * 3. Quality thresholds ensure high-quality output at each phase
 * 4. The system maintains a coherent discussion thread with reasoning
 * 5. Phases have clear success criteria and error recovery
 */
export async function initiateCollaborativeWorkflow(
  sessionId: string,
  initialTopic: string,
  initialParams: any = {}
): Promise<boolean> {
  logger.info(`Initiating collaborative workflow`, {
    sessionId,
    topic: initialTopic,
    params: Object.keys(initialParams)
  });

  try {
    // Create a discussion ID for tracking the discussion thread
    const discussionId = uuidv4();

    // Initialize workflow state tracking
    const workflowState: WorkflowState = {
      currentPhase: 'initialization',
      phaseProgress: 0,
      phaseStartTime: new Date().toISOString(),
      phaseAttempts: 1,
      completedPhases: [],
      failedAttempts: {},
      qualityScores: {},
      aggregatedFeedback: []
    };

    // Extract parameters
    const contentType = initialParams.contentType || 'blog-article';
    const targetAudience = initialParams.targetAudience || 'general audience';
    const tone = initialParams.tone || 'informative';
    const keywords = initialParams.keywords || [];

    // Create a system message to start the workflow
    const systemMessage: IterativeMessage = {
      id: uuidv4(),
      type: IterativeMessageType.UPDATE,
      from: 'system',
      to: 'all',
      content: {
        topic: initialTopic,
        contentType,
        targetAudience,
        tone,
        keywords,
        instructions: 'Initiate a collaborative workflow to create high-quality content',
        discussionId,
        workflowState
      },
      timestamp: new Date().toISOString(),
      conversationId: discussionId,
      reasoning: {
        context: { topic: initialTopic, workflow: 'collaborative-content-creation' },
        thoughts: [
          'Initializing collaborative content creation workflow',
          'Establishing communication channels between specialized agents',
          'Setting up structured phases for content development',
          'Implementing quality validation at each phase'
        ],
        conclusion: 'Begin collaborative content creation with research phase',
        process: 'system',
        metadata: {
          confidence: 0.95,
          steps: [
            'Initialize workflow state tracking',
            'Create discussion thread',
            'Notify all participating agents',
            'Begin with research phase'
          ]
        }
      }
    };

    // Initialize or update the state
    let state = await stateStore.getState(sessionId);
    if (!state) {
      // Initialize a new state
      state = {
        id: sessionId,
        topic: initialTopic,
        contentType,
        targetAudience,
        tone,
        keywords,
        status: 'active',
        startTime: new Date().toISOString(),
        artifacts: {},
        generatedArtifacts: [],
        consultations: {},
        agentStates: {},
        messages: [systemMessage],
        goals: [],
        currentPhase: 'initialization',
        workflowProgress: {
          marketResearchComplete: false,
          keywordResearchComplete: false,
          contentStrategyComplete: false,
          contentGenerationComplete: false,
          seoOptimizationComplete: false,
          currentPhase: 'initialization'
        },
        discussions: {
          [discussionId]: {
            id: discussionId,
            leadAgent: 'content-strategy',
            participants: [
              'market-research',
              'seo-keyword',
              'content-strategy',
              'content-generation',
              'seo-optimization'
            ],
            status: 'active',
            topic: initialTopic,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            data: {
              workflowState
            },
            messages: []
          }
        }
      };
    } else {
      // Update existing state
      if (!state.discussions) {
        state.discussions = {};
      }

      state.discussions[discussionId] = {
        id: discussionId,
        leadAgent: 'content-strategy',
        participants: [
          'market-research',
          'seo-keyword',
          'content-strategy',
          'content-generation',
          'seo-optimization'
        ],
        status: 'active',
        topic: initialTopic,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        data: {
          workflowState
        },
        messages: []
      };

      if (!state.messages) {
        state.messages = [];
      }

      state.messages.push(systemMessage);

      state.currentPhase = 'initialization';
      state.workflowProgress = {
        ...state.workflowProgress,
        currentPhase: 'initialization'
      };
    }

    // Save the updated state
    await stateStore.setState(sessionId, state);

    // Explicitly update the phase to research
    await stateStore.updateState(sessionId, (state) => {
      state.currentPhase = 'research';
      if (state.workflowProgress) {
        state.workflowProgress.currentPhase = 'research';
      }
      return state;
    });

    logger.info(`Explicitly updated phase to research for session ${sessionId}`, {
      sessionId,
      timestamp: new Date().toISOString()
    });

    // Begin the research phase
    setTimeout(() => {
      // Log phase transition
      logger.logPhaseTransition(sessionId, 'initialization', 'research');

      // Start the research phase
      initiateResearchPhase(
        sessionId,
        initialTopic,
        contentType,
        targetAudience,
        tone,
        {
          discussionId,
          keywords,
          ...initialParams
        }
      ).catch((err: Error) => {
        logger.error(`Error in research phase`, {
          sessionId,
          phase: 'research',
          error: err.message || String(err),
          stack: err.stack
        });
      });
    }, 2000);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error initiating collaborative workflow`, {
      sessionId,
      phase: 'initialization',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Transition from research phase to content generation phase
 * This is called when the research phase is complete
 */
export async function transitionToContentGeneration(
  sessionId: string,
  topic: string,
  contentType: string = 'blog-article',
  targetAudience: string = 'general audience',
  tone: string = 'informative',
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  logger.info(`Transitioning from research to content generation phase`, {
    sessionId,
    topic,
    contentType,
    targetAudience,
    tone,
    additionalParamsKeys: Object.keys(additionalParams || {})
  });

  try {
    // Get current state to log
    const currentState = await stateStore.getState(sessionId);
    logger.info(`Current state before transition to content generation`, {
      sessionId,
      currentPhase: currentState?.currentPhase,
      workflowProgress: currentState?.workflowProgress,
      contentStrategyComplete: currentState?.workflowProgress?.contentStrategyComplete,
      artifactsCount: currentState?.generatedArtifacts?.length
    });

    // Update workflow progress
    await stateStore.updateState(sessionId, (state) => {
      if (state.workflowProgress) {
        state.workflowProgress.currentPhase = 'creation';
      }
      state.currentPhase = 'creation';
      return state;
    });

    // Log phase transition
    logger.logPhaseTransition(sessionId, 'research', 'creation');

    logger.info(`About to call initiateContentGenerationPhase`, {
      sessionId,
      timestamp: new Date().toISOString()
    });

    try {
      // Start the content generation phase
      const result = await initiateContentGenerationPhase(
        sessionId,
        topic,
        contentType,
        targetAudience,
        tone,
        additionalParams
      );

      logger.info(`initiateContentGenerationPhase result: ${result}`, {
        sessionId,
        result,
        timestamp: new Date().toISOString()
      });

      // If initiateContentGenerationPhase fails, try a direct approach
      if (!result) {
        logger.warn(`initiateContentGenerationPhase failed, trying direct approach`, {
          sessionId,
          timestamp: new Date().toISOString()
        });

        // Get the current state
        const state = await stateStore.getState(sessionId);
        if (!state) {
          logger.error(`No state found for session ${sessionId} in direct approach`);
          return false;
        }

        // Check if content strategy artifact exists
        const contentStrategyArtifacts = state.generatedArtifacts
          .filter(id => state.artifacts[id]?.type === 'content-strategy');

        if (contentStrategyArtifacts.length === 0) {
          logger.warn(`No content strategy artifact found for session ${sessionId} in direct approach`, { sessionId });
          return false;
        }

        // Get the content strategy artifact
        const contentStrategyArtifact = state.artifacts[contentStrategyArtifacts[0]];

        // Create a content generation goal directly
        const contentGenerationGoal: Goal = {
          id: uuidv4(),
          name: 'Generate Content',
          description: `Generate content for ${topic} based on the content strategy`,
          status: 'active' as GoalStatus,
          assignedTo: 'content-generation',
          createdAt: new Date().toISOString(),
          type: 'generate-content',
          metadata: {
            topic,
            contentType,
            targetAudience,
            tone,
            contentStrategyId: contentStrategyArtifact.id,
            contentStrategy: contentStrategyArtifact.content,
            ...additionalParams
          }
        };

        // Add goal to state
        await stateStore.updateState(sessionId, (currentState) => {
          return {
            ...currentState,
            goals: [...(currentState.goals || []), contentGenerationGoal],
            currentPhase: 'creation',
            workflowProgress: {
              ...currentState.workflowProgress,
              currentPhase: 'creation'
            }
          };
        });

        // Import content generation agent
        const { contentGenerationAgent } = require('../agents');

        // Trigger the content generation agent to act
        logger.info(`Directly triggering content generation agent to act for session ${sessionId}`, {
          sessionId,
          phase: 'content-generation',
          agentId: 'content-generation',
          timestamp: new Date().toISOString()
        });

        const directResult = await contentGenerationAgent.act(sessionId);

        logger.info(`Direct content generation agent act result: ${directResult}`, {
          sessionId,
          phase: 'content-generation',
          result: directResult,
          timestamp: new Date().toISOString()
        });

        return directResult;
      }

      return result;
    } catch (error) {
      logger.error(`Error in initiateContentGenerationPhase`, {
        sessionId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      });
      return false;
    }
  } catch (error) {
    const err = error as Error;
    logger.error(`Error transitioning to content generation phase`, {
      sessionId,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Transition from content generation phase to review phase
 * This is called when the content generation phase is complete
 */
export async function transitionToReviewPhase(
  sessionId: string,
  topic: string,
  contentType: string = 'blog-article',
  targetAudience: string = 'general audience',
  tone: string = 'informative',
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  logger.info(`Transitioning from content generation to review phase`, {
    sessionId,
    topic,
    contentType
  });

  try {
    // Get current state to check content generation status
    const currentState = await stateStore.getState(sessionId);
    logger.info(`Current state before transition to review phase`, {
      sessionId,
      currentPhase: currentState?.currentPhase,
      workflowProgress: currentState?.workflowProgress,
      contentGenerationComplete: currentState?.workflowProgress?.contentGenerationComplete,
      artifactsCount: currentState?.generatedArtifacts?.length
    });

    // Check if we already have content generation artifacts
    const contentGenerationArtifacts = currentState?.generatedArtifacts
      ?.filter(id =>
        currentState.artifacts[id]?.type === 'blog-content' ||
        currentState.artifacts[id]?.type === 'generated-content' ||
        currentState.artifacts[id]?.type === 'blog-post' ||
        currentState.artifacts[id]?.type === 'content-draft'
      ) || [];

    logger.info(`Found ${contentGenerationArtifacts.length} content artifacts before transition`, {
      sessionId,
      contentGenerationArtifactsCount: contentGenerationArtifacts.length,
      contentGenerationArtifactIds: contentGenerationArtifacts,
      artifactTypes: contentGenerationArtifacts.map(id => currentState?.artifacts[id]?.type)
    });

    // Update workflow progress with explicit contentGenerationComplete flag
    await stateStore.updateState(sessionId, (state) => {
      if (state.workflowProgress) {
        state.workflowProgress.currentPhase = 'review';
        state.workflowProgress.contentGenerationComplete = true;
      }
      state.currentPhase = 'review';
      return state;
    });

    // Log phase transition
    logger.logPhaseTransition(sessionId, 'creation', 'review');

    // Log the updated state
    const updatedState = await stateStore.getState(sessionId);
    logger.info(`Updated state before initiating review phase`, {
      sessionId,
      currentPhase: updatedState?.currentPhase,
      workflowProgress: updatedState?.workflowProgress,
      contentGenerationComplete: updatedState?.workflowProgress?.contentGenerationComplete
    });

    // Cancel any pending content generation goals to prevent further processing
    await stateStore.updateState(sessionId, (state) => {
      if (state.goals) {
        state.goals = state.goals.map((goal: Goal) => {
          if (goal.assignedTo === 'content-generation' && goal.status === 'active') {
            return {
              ...goal,
              status: 'cancelled',
              cancelledAt: new Date().toISOString(),
              cancellationReason: 'Workflow transitioned to review phase'
            };
          }
          return goal;
        });
      }
      return state;
    });

    logger.info(`Cancelled any pending content generation goals`, {
      sessionId,
      timestamp: new Date().toISOString()
    });

    // Start the review phase
    const result = await initiateReviewPhase(
      sessionId,
      topic,
      contentType,
      targetAudience,
      tone,
      additionalParams
    );

    // Log the result
    logger.info(`Review phase initiation result: ${result}`, {
      sessionId,
      result,
      timestamp: new Date().toISOString()
    });

    return result;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error transitioning to review phase`, {
      sessionId,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Complete the workflow
 * This is called when all phases are complete
 */
export async function completeWorkflow(
  sessionId: string,
  topic: string
): Promise<boolean> {
  logger.info(`Completing workflow for session ${sessionId}`, {
    sessionId,
    topic
  });

  try {
    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      logger.error(`No state found for session ${sessionId}`);
      return false;
    }

    // Perform final quality assessment and SEO optimization
    await performFinalQualityAssessment(sessionId, state);

    // Update workflow progress
    await stateStore.updateState(sessionId, (state) => {
      if (state.workflowProgress) {
        state.workflowProgress.currentPhase = 'complete';
      }
      state.currentPhase = 'complete';
      state.status = 'completed';
      state.completedAt = new Date().toISOString();
      return state;
    });

    // Log phase transition
    logger.logPhaseTransition(sessionId, 'finalization', 'complete');

    logger.info(`Workflow completed successfully for session ${sessionId}`, {
      sessionId,
      topic
    });

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error completing workflow`, {
      sessionId,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Perform final quality assessment and SEO optimization
 * This adds comprehensive quality metrics to the session state
 */
async function performFinalQualityAssessment(
  sessionId: string,
  state: IterativeCollaborationState
): Promise<void> {
  try {
    // Import the necessary utilities
    const { assessArtifactQuality } = require('../utils/quality-assessment');
    const { calculateContentQualityMetrics } = require('../utils/content-quality-metrics');
    const { optimizeForSeo } = require('../utils/seo-optimization');

    // Find the final content artifact
    const finalContentArtifact = state.artifacts &&
      Object.values(state.artifacts).find(artifact =>
        artifact.type === 'final-article' ||
        artifact.type === 'seo-optimized-content' ||
        artifact.type === 'content'
      );

    if (!finalContentArtifact) {
      logger.warn(`No final content artifact found for session ${sessionId}`);
      return;
    }

    // Extract keywords from the state
    const keywords = state.keywords || [];

    // Perform quality assessment
    const qualityAssessment = assessArtifactQuality(finalContentArtifact, keywords);

    // Calculate content quality metrics
    const contentMetrics = calculateContentQualityMetrics(finalContentArtifact, keywords);

    // Perform SEO optimization
    const seoOptimizationResult = optimizeForSeo(finalContentArtifact, keywords);

    // Generate SEO analysis and recommendations
    const seoAnalysis = generateSeoAnalysis(seoOptimizationResult, contentMetrics);
    const seoRecommendations = generateSeoRecommendations(seoOptimizationResult);

    // Extract prioritized tasks and keyword suggestions
    const seoPrioritizedTasks = extractPrioritizedTasks(seoOptimizationResult);
    const keywordSuggestions = extractKeywordSuggestions(seoOptimizationResult);

    // Update the state with the quality assessment and SEO optimization results
    await stateStore.updateState(sessionId, (currentState) => {
      return {
        ...currentState,
        qualityAssessment,
        contentMetrics,
        seoOptimization: seoOptimizationResult,
        seoAnalysis,
        seoRecommendations,
        seoPrioritizedTasks,
        keywordSuggestions
      };
    });

    logger.info(`Completed final quality assessment for session ${sessionId}`, {
      sessionId,
      qualityScore: qualityAssessment.overallScore,
      seoScore: seoOptimizationResult.overallScore
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error performing final quality assessment`, {
      sessionId,
      error: err.message || String(error),
      stack: err.stack
    });
  }
}

/**
 * Generate SEO analysis from SEO optimization result
 */
function generateSeoAnalysis(seoResult: any, contentMetrics: any): string {
  // Create a comprehensive analysis from the SEO optimization result
  const analysis = [
    `## SEO Analysis Summary\n`,
    `Overall SEO Score: ${Math.round(seoResult.overallScore * 100)}/100\n`,

    `### On-Page SEO Analysis\n`,
    `- **Title Tag**: ${seoResult.onPageSeo.titleTag.score >= 0.8 ? '✅ Excellent' : seoResult.onPageSeo.titleTag.score >= 0.6 ? '✓ Good' : '❌ Needs improvement'}\n`,
    `  Original: "${seoResult.onPageSeo.titleTag.original}"\n`,
    `  ${seoResult.onPageSeo.titleTag.original !== seoResult.onPageSeo.titleTag.optimized ? `Optimized: "${seoResult.onPageSeo.titleTag.optimized}"\n` : ''}`,

    `- **Meta Description**: ${seoResult.onPageSeo.metaDescription.score >= 0.8 ? '✅ Excellent' : seoResult.onPageSeo.metaDescription.score >= 0.6 ? '✓ Good' : '❌ Needs improvement'}\n`,
    `  ${seoResult.onPageSeo.metaDescription.original ? `Original: "${seoResult.onPageSeo.metaDescription.original.substring(0, 100)}${seoResult.onPageSeo.metaDescription.original.length > 100 ? '...' : ''}"\n` : ''}`,

    `- **Headings Structure**: ${seoResult.onPageSeo.headings.score >= 0.8 ? '✅ Excellent' : seoResult.onPageSeo.headings.score >= 0.6 ? '✓ Good' : '❌ Needs improvement'}\n`,
    `  Found ${seoResult.onPageSeo.headings.original.length} headings\n`,

    `- **Keyword Usage**: ${seoResult.onPageSeo.content.score >= 0.8 ? '✅ Excellent' : seoResult.onPageSeo.content.score >= 0.6 ? '✓ Good' : '❌ Needs improvement'}\n`,
    Object.entries(seoResult.onPageSeo.content.keywordDensity).map(([keyword, density]) =>
      `  "${keyword}": ${density}% density ${density >= 0.5 && density <= 2.5 ? '(optimal)' : density < 0.5 ? '(too low)' : '(too high)'}\n`
    ).join(''),

    `### Semantic SEO Analysis\n`,
    `- **Topic Clusters**: ${seoResult.semanticSeo.topicClusters.slice(0, 3).join(', ')}${seoResult.semanticSeo.topicClusters.length > 3 ? '...' : ''}\n`,
    `- **Related Entities**: ${seoResult.semanticSeo.relatedEntities.slice(0, 5).join(', ')}${seoResult.semanticSeo.relatedEntities.length > 5 ? '...' : ''}\n`,

    `### SERP Feature Potential\n`,
    `- **Featured Snippet Potential**: ${Math.round(seoResult.serpFeatures.featuredSnippetPotential * 100)}%\n`,
    `- **FAQ Schema Potential**: ${Math.round(seoResult.serpFeatures.faqSchemaPotential * 100)}%\n`,
    `- **How-To Schema Potential**: ${Math.round(seoResult.serpFeatures.howToSchemaPotential * 100)}%\n`,

    `### Content Quality Metrics\n`,
    `- **Readability**: Flesch-Kincaid Score ${contentMetrics.readability.fleschKincaidScore.toFixed(1)} (${
      contentMetrics.readability.fleschKincaidScore >= 60 && contentMetrics.readability.fleschKincaidScore <= 80 ? 'Optimal - Grade 7-8 level' :
      contentMetrics.readability.fleschKincaidScore > 80 ? 'Very Easy - Grade 6 or below' :
      contentMetrics.readability.fleschKincaidScore >= 50 ? 'Fairly Difficult - Grade 10-12' :
      'Difficult - College level or above'
    })\n`,
    `- **Estimated Reading Time**: ${contentMetrics.readability.readingTimeMinutes} minutes\n`,
    `- **Content Structure**: ${contentMetrics.structure.headingCount} headings, ${contentMetrics.structure.paragraphCount} paragraphs, ${contentMetrics.structure.listCount} lists\n`,
    `- **Engagement Potential**: ${Math.round(contentMetrics.engagement.engagementScore * 100)}%\n`
  ].join('');

  return analysis;
}

/**
 * Generate SEO recommendations from SEO optimization result
 */
function generateSeoRecommendations(seoResult: any): string {
  // Create comprehensive recommendations from the SEO optimization result
  const recommendations = [
    `## SEO Improvement Recommendations\n\n`,

    `### High Priority Improvements\n`,

    // Title tag recommendations
    ...(seoResult.onPageSeo.titleTag.suggestions.length > 0 ?
      [`#### Title Tag Optimization\n`,
       ...seoResult.onPageSeo.titleTag.suggestions.map(s => `- ${s}\n`),
       `\n`] : []),

    // Meta description recommendations
    ...(seoResult.onPageSeo.metaDescription.suggestions.length > 0 ?
      [`#### Meta Description Optimization\n`,
       ...seoResult.onPageSeo.metaDescription.suggestions.map(s => `- ${s}\n`),
       `\n`] : []),

    // Content recommendations
    ...(seoResult.onPageSeo.content.suggestions.length > 0 ?
      [`#### Content Optimization\n`,
       ...seoResult.onPageSeo.content.suggestions.map(s => `- ${s}\n`),
       `\n`] : []),

    `### Semantic SEO Improvements\n`,

    // Semantic SEO recommendations
    ...(seoResult.semanticSeo.suggestions.length > 0 ?
      [...seoResult.semanticSeo.suggestions.map(s => `- ${s}\n`),
       `\n`] : [`- No specific semantic SEO recommendations.\n\n`]),

    `### SERP Feature Opportunities\n`,

    // SERP feature recommendations
    ...(seoResult.serpFeatures.suggestions.length > 0 ?
      [...seoResult.serpFeatures.suggestions.map(s => `- ${s}\n`),
       `\n`] : [`- No specific SERP feature recommendations.\n\n`]),

    `### Structured Data Implementation\n`,

    // Structured data recommendations
    ...(seoResult.structuredData.suggestions.length > 0 ?
      [...seoResult.structuredData.suggestions.map(s => `- ${s}\n`),
       `\n`] : [`- No specific structured data recommendations.\n\n`]),

    `### Additional Recommendations\n`,

    // Overall recommendations
    ...(seoResult.suggestions.length > 0 ?
      [...seoResult.suggestions.map(s => `- ${s}\n`),
       `\n`] : [`- No additional recommendations.\n\n`])
  ].join('');

  return recommendations;
}

/**
 * Extract prioritized tasks from SEO optimization result
 */
function extractPrioritizedTasks(seoResult: any): string[] {
  const prioritizedTasks: string[] = [];

  // Add title tag task if score is low
  if (seoResult.onPageSeo.titleTag.score < 0.7 && seoResult.onPageSeo.titleTag.suggestions.length > 0) {
    prioritizedTasks.push(`Optimize title tag: ${seoResult.onPageSeo.titleTag.suggestions[0]}`);
  }

  // Add meta description task if score is low
  if (seoResult.onPageSeo.metaDescription.score < 0.7 && seoResult.onPageSeo.metaDescription.suggestions.length > 0) {
    prioritizedTasks.push(`Improve meta description: ${seoResult.onPageSeo.metaDescription.suggestions[0]}`);
  }

  // Add content optimization task if score is low
  if (seoResult.onPageSeo.content.score < 0.7 && seoResult.onPageSeo.content.suggestions.length > 0) {
    prioritizedTasks.push(`Enhance content: ${seoResult.onPageSeo.content.suggestions[0]}`);
  }

  // Add semantic SEO task
  if (seoResult.semanticSeo.suggestions.length > 0) {
    prioritizedTasks.push(`Improve semantic relevance: ${seoResult.semanticSeo.suggestions[0]}`);
  }

  // Add SERP feature task
  if (seoResult.serpFeatures.suggestions.length > 0) {
    prioritizedTasks.push(`Optimize for SERP features: ${seoResult.serpFeatures.suggestions[0]}`);
  }

  // Add structured data task
  if (seoResult.structuredData.suggestions.length > 0) {
    prioritizedTasks.push(`Implement structured data: ${seoResult.structuredData.suggestions[0]}`);
  }

  // Add general task
  if (seoResult.suggestions.length > 0) {
    prioritizedTasks.push(seoResult.suggestions[0]);
  }

  // Limit to 5 tasks
  return prioritizedTasks.slice(0, 5);
}

/**
 * Extract keyword suggestions from SEO optimization result
 */
function extractKeywordSuggestions(seoResult: any): string[] {
  const keywordSuggestions: string[] = [];

  // Add related entities as keyword suggestions
  if (seoResult.semanticSeo.relatedEntities && seoResult.semanticSeo.relatedEntities.length > 0) {
    keywordSuggestions.push(...seoResult.semanticSeo.relatedEntities.slice(0, 5));
  }

  // Add topic clusters as keyword suggestions
  if (seoResult.semanticSeo.topicClusters && seoResult.semanticSeo.topicClusters.length > 0) {
    keywordSuggestions.push(...seoResult.semanticSeo.topicClusters.slice(0, 5));
  }

  // Limit to 10 suggestions
  return keywordSuggestions.slice(0, 10);
}
