// src/app/(payload)/api/agents/collaborative-iteration/workflows/content-generation-workflow.ts

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeMessageType,
  IterativeCollaborationState,
  IterativeArtifact,
  Goal,
  GoalStatus,
  ArtifactStatus
} from '../types';
import { stateStore } from '../utils/stateStore';
import logger from '../utils/logger';
import {
  contentGenerationAgent,
  seoOptimizationAgent
} from '../agents';

// Quality threshold constants for artifact validation
const QUALITY_THRESHOLDS = {
  CONTENT_GENERATION: 0.75,
  SEO_OPTIMIZATION: 0.80
};

// Workflow state interface
interface ContentGenerationWorkflowState {
  currentPhase: 'content-generation' | 'seo-optimization' | 'complete';
  phaseProgress: number;
  phaseStartTime: string;
  phaseAttempts: Record<string, number>;
  completedPhases: string[];
  failedAttempts: Record<string, number>;
  qualityScores: Record<string, number>;
}

/**
 * Initiates the content generation phase workflow
 * This workflow coordinates content generation and SEO optimization
 *
 * @param sessionId The session ID
 * @param topic The content topic
 * @param contentType The type of content (blog-article, product-page, etc.)
 * @param targetAudience The target audience for the content
 * @param tone The tone of the content
 * @param additionalParams Additional parameters for the content generation
 * @returns Promise<boolean> indicating success or failure
 */
export async function initiateContentGenerationPhase(
  sessionId: string,
  topic: string,
  contentType: string = 'blog-article',
  targetAudience: string = 'general audience',
  tone: string = 'informative',
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  logger.info(`Initiating content generation phase workflow for session ${sessionId}`, {
    sessionId,
    topic,
    contentType,
    targetAudience,
    tone,
    timestamp: new Date().toISOString(),
    additionalParamsKeys: Object.keys(additionalParams || {})
  });

  try {
    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      logger.error(`No state found for session ${sessionId}`);
      return false;
    }

    logger.info(`Current state in initiateContentGenerationPhase`, {
      sessionId,
      currentPhase: state.currentPhase,
      workflowProgress: state.workflowProgress,
      contentStrategyComplete: state.workflowProgress?.contentStrategyComplete,
      artifactsCount: state.generatedArtifacts?.length,
      artifactTypes: state.generatedArtifacts?.map(id => state.artifacts[id]?.type)
    });

    // Check if the research phase is complete
    if (!state.workflowProgress?.contentStrategyComplete) {
      logger.warn(`Content strategy is not complete for session ${sessionId}. Cannot proceed with content generation.`, {
        sessionId,
        workflowProgress: state.workflowProgress
      });
      return false;
    }

    // Get the content strategy artifact
    const contentStrategyArtifacts = state.generatedArtifacts
      .filter(id => state.artifacts[id]?.type === 'content-strategy');

    logger.info(`Content strategy artifacts in initiateContentGenerationPhase: ${contentStrategyArtifacts.length}`, {
      sessionId,
      contentStrategyArtifactIds: contentStrategyArtifacts,
      allArtifactTypes: state.generatedArtifacts.map(id => state.artifacts[id]?.type)
    });

    if (contentStrategyArtifacts.length === 0) {
      logger.warn(`No content strategy artifact found for session ${sessionId}`, { sessionId });
      return false;
    }

    // Initialize content generation workflow state
    const workflowState: ContentGenerationWorkflowState = {
      currentPhase: 'content-generation',
      phaseProgress: 0,
      phaseStartTime: new Date().toISOString(),
      phaseAttempts: {
        'content-generation': 1,
        'seo-optimization': 1
      },
      completedPhases: [],
      failedAttempts: {},
      qualityScores: {}
    };

    // Update state with workflow state
    await stateStore.updateState(sessionId, (currentState) => {
      return {
        ...currentState,
        currentPhase: 'creation',
        metadata: {
          ...currentState.metadata,
          contentGenerationWorkflowState: workflowState
        }
      };
    });

    // Get the content strategy artifact to use for content generation
    const contentStrategyArtifact = state.artifacts[contentStrategyArtifacts[0]];

    // Create a content generation goal
    const contentGenerationGoal: Goal = {
      id: uuidv4(),
      name: 'Generate Content',
      description: `Generate content for ${topic} based on the content strategy`,
      status: 'active' as GoalStatus,
      assignedTo: 'content-generation',
      createdAt: new Date().toISOString(),
      type: 'generate-content',
      metadata: {
        topic,
        contentType,
        targetAudience,
        tone,
        contentStrategyId: contentStrategyArtifact.id,
        contentStrategy: contentStrategyArtifact.content,
        ...additionalParams
      }
    };

    // Add goal to state
    await stateStore.updateState(sessionId, (currentState) => {
      return {
        ...currentState,
        goals: [...(currentState.goals || []), contentGenerationGoal]
      };
    });

    // Trigger the content generation agent to act
    logger.info(`Triggering content generation agent to act for session ${sessionId}`, {
      sessionId,
      phase: 'content-generation',
      agentId: 'content-generation'
    });

    const contentGenResult = await contentGenerationAgent.act(sessionId);

    logger.info(`Content generation agent act result: ${contentGenResult}`, {
      sessionId,
      phase: 'content-generation',
      result: contentGenResult
    });

    // Schedule validation of content generation results
    setTimeout(() => {
      validateContentGeneration(sessionId, topic, contentType, targetAudience, tone, additionalParams).catch((err: Error) => {
        logger.error(`Error validating content generation`, {
          sessionId,
          phase: 'content-generation-validation',
          error: err.message || String(err),
          stack: err.stack
        });
      });
    }, 15000);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error initiating content generation phase`, {
      sessionId,
      phase: 'content-generation',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Validates the content generation results and initiates the SEO optimization phase if successful
 *
 * @param sessionId The session ID
 * @param topic The content topic
 * @param contentType The type of content
 * @param targetAudience The target audience
 * @param tone The tone of the content
 * @param additionalParams Additional parameters
 * @returns Promise<boolean> indicating success or failure
 */
export async function validateContentGeneration(
  sessionId: string,
  topic: string,
  contentType: string = 'blog-article',
  targetAudience: string = 'general audience',
  tone: string = 'informative',
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  logger.info(`Validating content generation for session ${sessionId}`, {
    sessionId,
    topic,
    contentType,
    targetAudience,
    tone
  });

  try {
    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      logger.error(`No state found for session ${sessionId}`);
      return false;
    }

    // Check if content generation artifact exists
    logger.info(`Validating content generation artifacts for session ${sessionId}`, {
      sessionId,
      generatedArtifactsCount: state.generatedArtifacts?.length || 0,
      artifactsCount: Object.keys(state.artifacts || {}).length
    });

    // Check if we've already validated content generation to prevent infinite loops
    if (state.workflowProgress?.contentGenerationComplete) {
      logger.info(`Content generation already marked as complete for session ${sessionId}, skipping validation`, {
        sessionId,
        workflowProgress: state.workflowProgress
      });
      return true;
    }

    const contentGenerationArtifacts = state.generatedArtifacts
      .filter(id => state.artifacts[id]?.type === 'blog-content' ||
                    state.artifacts[id]?.type === 'generated-content' ||
                    state.artifacts[id]?.type === 'blog-post' ||
                    state.artifacts[id]?.type === 'content-draft');

    logger.info(`Found ${contentGenerationArtifacts.length} content artifacts for session ${sessionId}`, {
      sessionId,
      contentGenerationArtifactsCount: contentGenerationArtifacts.length,
      contentGenerationArtifactIds: contentGenerationArtifacts,
      artifactTypes: contentGenerationArtifacts.map(id => state.artifacts[id]?.type)
    });

    // Check if content generation is complete
    const contentGenerationComplete = contentGenerationArtifacts.length > 0;

    // Update workflow progress
    await stateStore.updateState(sessionId, (currentState) => {
      return {
        ...currentState,
        workflowProgress: {
          ...currentState.workflowProgress,
          contentGenerationComplete,
          currentPhase: contentGenerationComplete ? 'review' : currentState.workflowProgress?.currentPhase
        }
      };
    });

    // Log the updated workflow progress
    logger.info(`Updated workflow progress for session ${sessionId}`, {
      sessionId,
      contentGenerationComplete,
      workflowProgress: (await stateStore.getState(sessionId))?.workflowProgress
    });

    // If content generation is complete, immediately transition to review phase
    if (contentGenerationComplete) {
      // Import here to avoid circular dependency
      const { transitionToReviewPhase } = require('./workflow-orchestrator');

      logger.info(`Content generation complete, transitioning directly to review phase for session ${sessionId}`, {
        sessionId,
        contentGenerationArtifactIds: contentGenerationArtifacts,
        timestamp: new Date().toISOString()
      });

      // Execute the transition to review phase
      try {
        await transitionToReviewPhase(
          sessionId,
          topic,
          contentType,
          targetAudience,
          tone,
          additionalParams
        );

        logger.info(`Successfully transitioned to review phase for session ${sessionId}`, {
          sessionId,
          phase: 'review',
          timestamp: new Date().toISOString()
        });
      } catch (transitionError) {
        logger.error(`Error transitioning to review phase`, {
          sessionId,
          error: transitionError instanceof Error ? transitionError.message : String(transitionError),
          stack: transitionError instanceof Error ? transitionError.stack : undefined
        });
      }
    }

    // If content generation is complete, proceed to SEO optimization
    if (contentGenerationComplete) {
      // Log the current phase before proceeding
      logger.info(`Content generation complete, current phase before SEO optimization: ${state.currentPhase}`, {
        sessionId,
        currentPhase: state.currentPhase,
        workflowProgress: state.workflowProgress
      });

      // Get the content generation artifact to use for SEO optimization
      const contentGenerationArtifact = state.artifacts[contentGenerationArtifacts[0]];

      // Log the artifact we're using for SEO optimization
      logger.info(`Using content artifact for SEO optimization: ${contentGenerationArtifact.id}`, {
        sessionId,
        artifactId: contentGenerationArtifact.id,
        artifactType: contentGenerationArtifact.type,
        contentLength: typeof contentGenerationArtifact.content === 'string'
          ? contentGenerationArtifact.content.length
          : JSON.stringify(contentGenerationArtifact.content).length
      });

      // Create an SEO optimization goal
      const seoOptimizationGoal: Goal = {
        id: uuidv4(),
        name: 'Optimize Content for SEO',
        description: `Optimize the generated content for ${topic} for search engines`,
        status: 'active' as GoalStatus,
        assignedTo: 'seo-optimization',
        createdAt: new Date().toISOString(),
        type: 'optimize-content',
        metadata: {
          topic,
          contentType,
          targetAudience,
          tone,
          contentGenerationId: contentGenerationArtifact.id,
          generatedContent: contentGenerationArtifact.content,
          ...additionalParams
        }
      };

      // Add goal to state
      await stateStore.updateState(sessionId, (currentState) => {
        return {
          ...currentState,
          goals: [...(currentState.goals || []), seoOptimizationGoal]
        };
      });

      // Update workflow state
      const workflowState = state.metadata?.contentGenerationWorkflowState as ContentGenerationWorkflowState;

      if (workflowState) {
        workflowState.completedPhases.push('content-generation');
        workflowState.currentPhase = 'seo-optimization';
        workflowState.phaseStartTime = new Date().toISOString();

        // Update state with updated workflow state
        await stateStore.updateState(sessionId, (currentState) => {
          return {
            ...currentState,
            metadata: {
              ...currentState.metadata,
              contentGenerationWorkflowState: workflowState
            }
          };
        });
      }

      // Trigger the SEO optimization agent to act
      logger.info(`Triggering SEO optimization agent to act for session ${sessionId}`, {
        sessionId,
        phase: 'seo-optimization',
        agentId: 'seo-optimization'
      });

      const seoOptResult = await seoOptimizationAgent.act(sessionId);

      logger.info(`SEO optimization agent act result: ${seoOptResult}`, {
        sessionId,
        phase: 'seo-optimization',
        result: seoOptResult
      });

      // Schedule validation of SEO optimization
      setTimeout(() => {
        validateSeoOptimization(sessionId, topic, contentType, targetAudience, tone, additionalParams).catch((err: Error) => {
          logger.error(`Error validating SEO optimization`, {
            sessionId,
            phase: 'seo-optimization-validation',
            error: err.message || String(err),
            stack: err.stack
          });
        });
      }, 10000);

      return true;
    } else {
      // If content generation is incomplete, retry if needed
      const workflowState = state.metadata?.contentGenerationWorkflowState as ContentGenerationWorkflowState;

      if (workflowState) {
        // Increment attempt counter for content generation
        workflowState.phaseAttempts['content-generation'] += 1;

        // Update state with new attempt count
        await stateStore.updateState(sessionId, (currentState) => {
          return {
            ...currentState,
            metadata: {
              ...currentState.metadata,
              contentGenerationWorkflowState: workflowState
            }
          };
        });

        // If we've tried too many times, proceed anyway with a warning
        const maxAttempts = 3;
        if (workflowState.phaseAttempts['content-generation'] > maxAttempts) {
          logger.warn(`Content generation phase incomplete after multiple attempts, proceeding anyway`, {
            sessionId,
            contentGenerationAttempts: workflowState.phaseAttempts['content-generation'],
            contentGenerationComplete
          });

          // Create a mock content generation artifact
          const mockContentArtifact: IterativeArtifact = {
            id: uuidv4(),
            name: `Blog Article: ${topic} (Mock)`,
            type: 'blog-content',
            status: 'completed' as ArtifactStatus,
            createdBy: 'content-generation',
            createdAt: new Date().toISOString(),
            currentVersion: 1,
            iterations: [
              {
                version: 1,
                timestamp: new Date().toISOString(),
                agent: 'content-generation',
                content: {
                  title: `The Ultimate Guide to ${topic}`,
                  metaDescription: `Learn everything you need to know about ${topic} in this comprehensive guide.`,
                  introduction: `This is a placeholder introduction for ${topic}.`,
                  tableOfContents: `## Table of Contents\n\n1. [Introduction](#introduction)\n2. [Section 1](#section-1)\n3. [Section 2](#section-2)\n4. [Conclusion](#conclusion)`,
                  body: `
## Introduction
This is a placeholder introduction for ${topic}.

## Section 1
This is placeholder content for section 1.

## Section 2
This is placeholder content for section 2.

## Conclusion
This is a placeholder conclusion for ${topic}.
`,
                  conclusion: `This is a placeholder conclusion for ${topic}.`,
                  callToAction: `Ready to learn more about ${topic}? Contact us today!`,
                  keywords: {
                    primary: topic,
                    secondary: [`${topic} guide`, `${topic} examples`],
                    usage: {
                      title: true,
                      headings: true,
                      introduction: true,
                      body: true,
                      conclusion: true
                    }
                  },
                  metadata: {
                    wordCount: 500,
                    readingTime: '2-3 minutes',
                    targetAudience: targetAudience,
                    contentType: contentType || 'blog-article',
                    tone: tone || 'informative',
                    mockGenerated: true
                  }
                },
                feedback: [],
                incorporatedConsultations: []
              }
            ],
            qualityScore: 70,
            contentType: contentType || 'blog-article',
            wordCount: 500,
            seoScore: 65
          };

          // Add the mock artifact to state
          await stateStore.updateState(sessionId, (currentState) => {
            // Add the artifact to the artifacts object
            const updatedArtifacts = {
              ...currentState.artifacts,
              [mockContentArtifact.id]: mockContentArtifact
            };

            // Add the artifact ID to the generatedArtifacts array
            const updatedGeneratedArtifacts = [
              ...(currentState.generatedArtifacts || []),
              mockContentArtifact.id
            ];

            return {
              ...currentState,
              artifacts: updatedArtifacts,
              generatedArtifacts: updatedGeneratedArtifacts,
              workflowProgress: {
                ...currentState.workflowProgress,
                contentGenerationComplete: true
              }
            };
          });

          // Proceed to SEO optimization with the mock content
          // Create an SEO optimization goal
          const seoOptimizationGoal: Goal = {
            id: uuidv4(),
            name: 'Optimize Content for SEO',
            description: `Optimize the generated content for ${topic} for search engines`,
            status: 'active' as GoalStatus,
            assignedTo: 'seo-optimization',
            createdAt: new Date().toISOString(),
            type: 'optimize-content',
            metadata: {
              topic,
              contentType,
              targetAudience,
              tone,
              contentGenerationId: mockContentArtifact.id,
              generatedContent: mockContentArtifact.content,
              mockContent: true,
              ...additionalParams
            }
          };

          // Add goal to state
          await stateStore.updateState(sessionId, (currentState) => {
            return {
              ...currentState,
              goals: [...(currentState.goals || []), seoOptimizationGoal]
            };
          });

          // Update workflow state
          workflowState.completedPhases.push('content-generation');
          workflowState.currentPhase = 'seo-optimization';
          workflowState.phaseStartTime = new Date().toISOString();

          // Update state with updated workflow state
          await stateStore.updateState(sessionId, (currentState) => {
            return {
              ...currentState,
              metadata: {
                ...currentState.metadata,
                contentGenerationWorkflowState: workflowState
              }
            };
          });

          // Trigger the SEO optimization agent to act
          await seoOptimizationAgent.act(sessionId);

          return true;
        } else {
          // Retry the content generation phase
          // Create a new content generation goal
          const retryContentGenerationGoal: Goal = {
            id: uuidv4(),
            name: 'Retry Content Generation',
            description: `Retry generating content for ${topic}`,
            status: 'active' as GoalStatus,
            assignedTo: 'content-generation',
            createdAt: new Date().toISOString(),
            type: 'generate-content',
            metadata: {
              topic,
              contentType,
              targetAudience,
              tone,
              retryAttempt: workflowState.phaseAttempts['content-generation'],
              ...additionalParams
            }
          };

          // Add goal to state
          await stateStore.updateState(sessionId, (currentState) => {
            return {
              ...currentState,
              goals: [...(currentState.goals || []), retryContentGenerationGoal]
            };
          });

          // Trigger the content generation agent to act
          await contentGenerationAgent.act(sessionId);

          // Schedule another validation check
          setTimeout(() => {
            validateContentGeneration(sessionId, topic, contentType, targetAudience, tone, additionalParams).catch((err: Error) => {
              logger.error(`Error validating content generation`, {
                sessionId,
                phase: 'content-generation-validation',
                error: err.message || String(err),
                stack: err.stack
              });
            });
          }, 15000);

          return true;
        }
      }
    }

    return false;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error validating content generation`, {
      sessionId,
      phase: 'content-generation-validation',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Validates the SEO optimization results and completes the content generation workflow
 *
 * @param sessionId The session ID
 * @param topic The content topic
 * @param contentType The type of content
 * @param targetAudience The target audience
 * @param tone The tone of the content
 * @param additionalParams Additional parameters
 * @returns Promise<boolean> indicating success or failure
 */
export async function validateSeoOptimization(
  sessionId: string,
  topic: string,
  contentType: string = 'blog-article',
  targetAudience: string = 'general audience',
  tone: string = 'informative',
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  logger.info(`Validating SEO optimization for session ${sessionId}`, {
    sessionId,
    topic,
    contentType,
    targetAudience,
    tone
  });

  try {
    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      logger.error(`No state found for session ${sessionId}`);
      return false;
    }

    // Check if SEO optimization artifact exists
    logger.info(`Validating SEO optimization artifacts for session ${sessionId}`, {
      sessionId,
      generatedArtifactsCount: state.generatedArtifacts?.length || 0,
      artifactsCount: Object.keys(state.artifacts || {}).length,
      artifactTypes: Object.values(state.artifacts || {}).map((a: any) => a.type)
    });

    // Check if we've already validated SEO optimization to prevent infinite loops
    if (state.workflowProgress?.seoOptimizationComplete) {
      logger.info(`SEO optimization already marked as complete for session ${sessionId}, skipping validation`, {
        sessionId,
        workflowProgress: state.workflowProgress
      });
      return true;
    }

    const seoOptimizationArtifacts = state.generatedArtifacts
      .filter(id => state.artifacts[id]?.type === 'seo-optimization' ||
                    state.artifacts[id]?.type === 'seo-optimized-content');

    logger.info(`Found ${seoOptimizationArtifacts.length} SEO optimization artifacts for session ${sessionId}`, {
      sessionId,
      seoOptimizationArtifactsCount: seoOptimizationArtifacts.length,
      seoOptimizationArtifactIds: seoOptimizationArtifacts,
      artifactTypes: seoOptimizationArtifacts.map(id => state.artifacts[id]?.type)
    });

    // Check if SEO optimization is complete
    const seoOptimizationComplete = seoOptimizationArtifacts.length > 0;

    // Update workflow progress
    await stateStore.updateState(sessionId, (currentState) => {
      return {
        ...currentState,
        workflowProgress: {
          ...currentState.workflowProgress,
          seoOptimizationComplete
        }
      };
    });

    // If SEO optimization is complete, mark the content generation phase as complete
    if (seoOptimizationComplete) {
      // Update workflow state
      const workflowState = state.metadata?.contentGenerationWorkflowState as ContentGenerationWorkflowState;

      if (workflowState) {
        workflowState.completedPhases.push('seo-optimization');
        workflowState.currentPhase = 'complete';

        // Update state with completed workflow
        await stateStore.updateState(sessionId, (currentState) => {
          return {
            ...currentState,
            currentPhase: 'complete', // Move to the complete phase
            metadata: {
              ...currentState.metadata,
              contentGenerationWorkflowState: workflowState
            }
          };
        });
      }

      logger.info(`Content generation workflow completed successfully for session ${sessionId}`, {
        sessionId,
        topic,
        contentType,
        targetAudience,
        tone
      });

      // Transition to the review phase immediately
      try {
        // First, update the current phase to review in the state
        await stateStore.updateState(sessionId, (currentState) => {
          return {
            ...currentState,
            currentPhase: 'review',
            workflowProgress: {
              ...currentState.workflowProgress,
              contentGenerationComplete: true,
              currentPhase: 'review'
            }
          };
        });

        // Log the phase transition
        logger.info(`Updated state to review phase for session ${sessionId}`, {
          sessionId,
          phase: 'review',
          timestamp: new Date().toISOString()
        });

        // Import here to avoid circular dependency
        const { transitionToReviewPhase } = require('./workflow-orchestrator');

        // Log that we're about to transition
        logger.info(`Immediately transitioning to review phase for session ${sessionId}`, {
          sessionId,
          phase: 'creation-to-review-transition',
          timestamp: new Date().toISOString()
        });

        // Execute the transition synchronously
        await transitionToReviewPhase(
          sessionId,
          topic,
          contentType,
          targetAudience,
          tone,
          additionalParams
        );

        logger.info(`Successfully transitioned to review phase for session ${sessionId}`, {
          sessionId,
          phase: 'review',
          timestamp: new Date().toISOString()
        });

        // Double-check that the state was updated correctly
        const updatedState = await stateStore.getState(sessionId);
        logger.info(`State after transition to review phase for session ${sessionId}`, {
          sessionId,
          currentPhase: updatedState?.currentPhase,
          workflowProgress: updatedState?.workflowProgress,
          timestamp: new Date().toISOString()
        });
      } catch (err) {
        logger.error(`Error transitioning to review phase`, {
          sessionId,
          phase: 'creation-to-review-transition',
          error: err instanceof Error ? err.message : String(err),
          stack: err instanceof Error ? err.stack : undefined
        });

        // Fallback: Try again with setTimeout
        setTimeout(async () => {
          try {
            // First update the state directly with explicit contentGenerationComplete flag
            await stateStore.updateState(sessionId, (currentState) => {
              return {
                ...currentState,
                currentPhase: 'review',
                workflowProgress: {
                  ...currentState.workflowProgress,
                  contentGenerationComplete: true,
                  currentPhase: 'review'
                }
              };
            });

            logger.info(`Fallback: Updated state to review phase for session ${sessionId}`, {
              sessionId,
              phase: 'review',
              timestamp: new Date().toISOString()
            });

            // Then try the transition again
            const { transitionToReviewPhase } = require('./workflow-orchestrator');
            await transitionToReviewPhase(
              sessionId,
              topic,
              contentType,
              targetAudience,
              tone,
              additionalParams
            );

            logger.info(`Fallback: Successfully transitioned to review phase for session ${sessionId}`, {
              sessionId,
              phase: 'review',
              timestamp: new Date().toISOString()
            });

            // Double-check that the state was updated correctly
            const updatedState = await stateStore.getState(sessionId);
            logger.info(`Fallback: State after transition to review phase for session ${sessionId}`, {
              sessionId,
              currentPhase: updatedState?.currentPhase,
              workflowProgress: updatedState?.workflowProgress,
              timestamp: new Date().toISOString()
            });
          } catch (retryErr) {
            logger.error(`Error in fallback transition to review phase`, {
              sessionId,
              phase: 'creation-to-review-transition-fallback',
              error: retryErr instanceof Error ? retryErr.message : String(retryErr),
              stack: retryErr instanceof Error ? retryErr.stack : undefined
            });
          }
        }, 2000);
      }

      return true;
    } else {
      // If SEO optimization is incomplete, retry if needed
      const workflowState = state.metadata?.contentGenerationWorkflowState as ContentGenerationWorkflowState;

      if (workflowState) {
        // Increment attempt counter for SEO optimization
        workflowState.phaseAttempts['seo-optimization'] += 1;

        // Update state with new attempt count
        await stateStore.updateState(sessionId, (currentState) => {
          return {
            ...currentState,
            metadata: {
              ...currentState.metadata,
              contentGenerationWorkflowState: workflowState
            }
          };
        });

        // If we've tried too many times, proceed anyway with a warning
        const maxAttempts = 3;
        if (workflowState.phaseAttempts['seo-optimization'] > maxAttempts) {
          logger.warn(`SEO optimization phase incomplete after multiple attempts, proceeding anyway`, {
            sessionId,
            seoOptimizationAttempts: workflowState.phaseAttempts['seo-optimization'],
            seoOptimizationComplete
          });

          // Create a mock SEO optimization artifact
          const mockSeoArtifact: IterativeArtifact = {
            id: uuidv4(),
            name: `SEO Optimization Report: ${topic} (Mock)`,
            type: 'seo-optimization',
            status: 'completed' as ArtifactStatus,
            createdBy: 'seo-optimization',
            createdAt: new Date().toISOString(),
            currentVersion: 1,
            iterations: [
              {
                version: 1,
                timestamp: new Date().toISOString(),
                agent: 'seo-optimization',
                content: {
                  topic,
                  contentType: contentType || 'blog-article',
                  keywordAnalysis: {
                    primaryKeyword: topic,
                    primaryKeywordDensity: '1.0%',
                    secondaryKeywords: [`${topic} guide`, `${topic} examples`, `how to use ${topic}`],
                    secondaryKeywordPresence: 'Partial',
                    missingKeywords: [`${topic} benefits`, `${topic} best practices`],
                    keywordPlacement: {
                      title: 'Present',
                      headings: 'Partial',
                      introduction: 'Present',
                      conclusion: 'Missing',
                      imageAlt: 'Missing'
                    }
                  },
                  contentStructure: {
                    headingHierarchy: 'Good',
                    paragraphLength: 'Good',
                    contentFlow: 'Good',
                    readability: 'Flesch-Kincaid Grade Level: 9.2 (Good)',
                    contentLength: 'Adequate (1800 words)'
                  },
                  technicalSEO: {
                    metaTitle: `The Ultimate Guide to ${topic}`,
                    metaDescription: `Learn everything you need to know about ${topic} in this comprehensive guide.`,
                    urlStructure: `example.com/guide/${topic.toLowerCase().replace(/\s+/g, '-')}`,
                    imageOptimization: 'Needs improvement',
                    internalLinking: 'Needs improvement',
                    externalLinking: 'Adequate',
                    schemaMarkup: 'Missing'
                  },
                  recommendations: [
                    `Increase the density of the primary keyword "${topic}" to 1.5-2%`,
                    `Add missing secondary keywords: ${topic} benefits, ${topic} best practices`,
                    `Include the primary keyword in the conclusion`,
                    `Add alt text to all images, including the primary keyword where relevant`,
                    `Add schema markup for Article or HowTo`
                  ],
                  optimizedElements: {
                    title: `The Ultimate Guide to ${topic}: Best Practices and Examples`,
                    metaDescription: `Discover everything you need to know about ${topic} in our comprehensive guide.`,
                    headings: [
                      {
                        level: 'H1',
                        text: `The Ultimate Guide to ${topic}`
                      },
                      {
                        level: 'H2',
                        text: `Understanding ${topic}: Key Concepts and Benefits`
                      },
                      {
                        level: 'H2',
                        text: `${topic} Best Practices for Optimal Results`
                      }
                    ],
                    imageAltText: [
                      `${topic} implementation diagram`,
                      `${topic} best practices infographic`
                    ]
                  },
                  mockGenerated: true
                },
                feedback: [],
                incorporatedConsultations: []
              }
            ],
            qualityScore: 70
          };

          // Add the mock artifact to state
          await stateStore.updateState(sessionId, (currentState) => {
            // Add the artifact to the artifacts object
            const updatedArtifacts = {
              ...currentState.artifacts,
              [mockSeoArtifact.id]: mockSeoArtifact
            };

            // Add the artifact ID to the generatedArtifacts array
            const updatedGeneratedArtifacts = [
              ...(currentState.generatedArtifacts || []),
              mockSeoArtifact.id
            ];

            return {
              ...currentState,
              artifacts: updatedArtifacts,
              generatedArtifacts: updatedGeneratedArtifacts,
              workflowProgress: {
                ...currentState.workflowProgress,
                seoOptimizationComplete: true
              }
            };
          });

          // Update workflow state to mark content generation phase as complete despite issues
          workflowState.completedPhases.push('seo-optimization');
          workflowState.currentPhase = 'complete';

          // Update state with completed workflow
          await stateStore.updateState(sessionId, (currentState) => {
            return {
              ...currentState,
              currentPhase: 'complete', // Move to the complete phase
              metadata: {
                ...currentState.metadata,
                contentGenerationWorkflowState: workflowState,
                contentGenerationPhaseWarning: 'SEO optimization incomplete after multiple attempts'
              }
            };
          });

          return true;
        } else {
          // Retry the SEO optimization phase
          // Create a new SEO optimization goal
          const retrySeoOptimizationGoal: Goal = {
            id: uuidv4(),
            name: 'Retry SEO Optimization',
            description: `Retry optimizing content for ${topic} for search engines`,
            status: 'active' as GoalStatus,
            assignedTo: 'seo-optimization',
            createdAt: new Date().toISOString(),
            type: 'optimize-content',
            metadata: {
              topic,
              contentType,
              targetAudience,
              tone,
              retryAttempt: workflowState.phaseAttempts['seo-optimization'],
              ...additionalParams
            }
          };

          // Add goal to state
          await stateStore.updateState(sessionId, (currentState) => {
            return {
              ...currentState,
              goals: [...(currentState.goals || []), retrySeoOptimizationGoal]
            };
          });

          // Trigger the SEO optimization agent to act
          await seoOptimizationAgent.act(sessionId);

          // Schedule another validation check
          setTimeout(() => {
            validateSeoOptimization(sessionId, topic, contentType, targetAudience, tone, additionalParams).catch((err: Error) => {
              logger.error(`Error validating SEO optimization`, {
                sessionId,
                phase: 'seo-optimization-validation',
                error: err.message || String(err),
                stack: err.stack
              });
            });
          }, 10000);

          return true;
        }
      }
    }

    return false;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error validating SEO optimization`, {
      sessionId,
      phase: 'seo-optimization-validation',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}