// src/app/(payload)/api/agents/collaborative-iteration/workflows/research-phase-workflow.ts

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeMessageType,
  IterativeCollaborationState,
  IterativeArtifact,
  Goal,
  GoalStatus,
  ArtifactStatus
} from '../types';
import { stateStore } from '../utils/stateStore';
import logger from '../utils/logger';
import {
  marketResearchAgent,
  seoKeywordAgent,
  contentStrategyAgent
} from '../agents';

// Quality threshold constants for artifact validation
const QUALITY_THRESHOLDS = {
  MARKET_RESEARCH: 0.75,
  KEYWORD_RESEARCH: 0.75,
  CONTENT_STRATEGY: 0.80
};

// Workflow state interface
interface ResearchWorkflowState {
  currentPhase: 'market-research' | 'keyword-research' | 'content-strategy' | 'complete';
  phaseProgress: number;
  phaseStartTime: string;
  phaseAttempts: Record<string, number>;
  completedPhases: string[];
  failedAttempts: Record<string, number>;
  qualityScores: Record<string, number>;
}

/**
 * Initiates the research phase workflow
 * This workflow coordinates market research, SEO keyword research, and content strategy development
 *
 * @param sessionId The session ID
 * @param topic The content topic
 * @param contentType The type of content (blog-article, product-page, etc.)
 * @param targetAudience The target audience for the content
 * @param tone The tone of the content
 * @param additionalParams Additional parameters for the research
 * @returns Promise<boolean> indicating success or failure
 */
export async function initiateResearchPhase(
  sessionId: string,
  topic: string,
  contentType: string = 'blog-article',
  targetAudience: string = 'general audience',
  tone: string = 'informative',
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  logger.info(`Initiating research phase workflow for session ${sessionId}`, {
    sessionId,
    topic,
    contentType,
    targetAudience,
    tone
  });

  try {
    // Get or initialize the state
    let state = await stateStore.getState(sessionId);
    if (!state) {
      // Initialize a new state if none exists
      state = {
        id: sessionId,
        topic,
        contentType,
        targetAudience,
        tone,
        keywords: additionalParams.keywords || [],
        status: 'active',
        startTime: new Date().toISOString(),
        artifacts: {},
        generatedArtifacts: [],
        consultations: {},
        agentStates: {},
        messages: [],
        goals: [],
        currentPhase: 'research',
        workflowProgress: {
          marketResearchComplete: false,
          keywordResearchComplete: false,
          contentStrategyComplete: false,
          contentGenerationComplete: false,
          seoOptimizationComplete: false
        }
      };
    }

    // Initialize research workflow state
    const workflowState: ResearchWorkflowState = {
      currentPhase: 'market-research',
      phaseProgress: 0,
      phaseStartTime: new Date().toISOString(),
      phaseAttempts: {
        'market-research': 1,
        'keyword-research': 1,
        'content-strategy': 1
      },
      completedPhases: [],
      failedAttempts: {},
      qualityScores: {}
    };

    // Create goals for each agent in the research phase
    const marketResearchGoal: Goal = {
      id: uuidv4(),
      name: 'Conduct Market Research',
      description: `Conduct comprehensive market research for ${topic}`,
      status: 'active' as GoalStatus,
      assignedTo: 'market-research',
      createdAt: new Date().toISOString(),
      type: 'market-research',
      metadata: {
        topic,
        contentType,
        targetAudience,
        tone,
        ...additionalParams
      }
    };

    const keywordResearchGoal: Goal = {
      id: uuidv4(),
      name: 'Conduct SEO Keyword Research',
      description: `Identify optimal SEO keywords for ${topic}`,
      status: 'active' as GoalStatus,
      assignedTo: 'seo-keyword',
      createdAt: new Date().toISOString(),
      type: 'keyword-research',
      metadata: {
        topic,
        contentType,
        targetAudience,
        tone,
        ...additionalParams
      }
    };

    // Add goals to state
    state.goals = [...(state.goals || []), marketResearchGoal, keywordResearchGoal];

    // Update state with workflow state
    state.metadata = {
      ...state.metadata,
      researchWorkflowState: workflowState
    };

    // Save the updated state
    await stateStore.setState(sessionId, state);

    // Trigger the market research and SEO keyword agents to act
    // We'll run these in parallel for efficiency
    const marketResearchPromise = marketResearchAgent.act(sessionId);
    const keywordResearchPromise = seoKeywordAgent.act(sessionId);

    // Wait for both agents to complete their initial actions
    await Promise.all([marketResearchPromise, keywordResearchPromise]);

    // Schedule validation of research phase results
    setTimeout(() => {
      validateResearchPhase(sessionId, topic, contentType, targetAudience, tone, additionalParams).catch((err: Error) => {
        logger.error(`Error validating research phase`, {
          sessionId,
          phase: 'research-validation',
          error: err.message || String(err),
          stack: err.stack
        });
      });
    }, 10000);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error initiating research phase`, {
      sessionId,
      phase: 'research',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Validates the research phase results and initiates the content strategy phase if successful
 *
 * @param sessionId The session ID
 * @param topic The content topic
 * @param contentType The type of content
 * @param targetAudience The target audience
 * @param tone The tone of the content
 * @param additionalParams Additional parameters
 * @returns Promise<boolean> indicating success or failure
 */
export async function validateResearchPhase(
  sessionId: string,
  topic: string,
  contentType: string = 'blog-article',
  targetAudience: string = 'general audience',
  tone: string = 'informative',
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  logger.info(`Validating research phase for session ${sessionId}`, {
    sessionId,
    topic,
    contentType,
    targetAudience,
    tone
  });

  try {
    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      logger.error(`No state found for session ${sessionId}`);
      return false;
    }

    // Check if market research and keyword research artifacts exist
    const marketResearchArtifacts = state.generatedArtifacts
      .filter(id => state.artifacts[id]?.type === 'market-research');

    const keywordArtifacts = state.generatedArtifacts
      .filter(id => state.artifacts[id]?.type === 'seo-keywords' || state.artifacts[id]?.type === 'seo-keyword');

    // Log the artifacts found for debugging
    logger.info(`Research phase validation - artifacts found:`, {
      sessionId,
      marketResearchArtifacts: marketResearchArtifacts.length,
      keywordArtifacts: keywordArtifacts.length,
      marketResearchArtifactIds: marketResearchArtifacts,
      keywordArtifactIds: keywordArtifacts,
      allArtifactTypes: state.generatedArtifacts.map(id => state.artifacts[id]?.type),
      workflowProgress: state.workflowProgress
    });

    // Check if both required artifacts exist
    const marketResearchComplete = marketResearchArtifacts.length > 0;

    // Check if keyword research is complete either by artifacts or by flag in workflowProgress
    const keywordResearchComplete = keywordArtifacts.length > 0 ||
                                   (state.workflowProgress && state.workflowProgress.keywordResearchComplete === true);

    // Update workflow progress
    await stateStore.updateState(sessionId, (currentState) => {
      return {
        ...currentState,
        workflowProgress: {
          ...currentState.workflowProgress,
          marketResearchComplete,
          keywordResearchComplete
        }
      };
    });

    // If both research phases are complete, proceed to content strategy
    if (marketResearchComplete && keywordResearchComplete) {
      // Create a content strategy goal
      const contentStrategyGoal: Goal = {
        id: uuidv4(),
        name: 'Develop Content Strategy',
        description: `Develop a comprehensive content strategy for ${topic} based on market and keyword research`,
        status: 'active' as GoalStatus,
        assignedTo: 'content-strategy',
        createdAt: new Date().toISOString(),
        type: 'create-content-strategy',
        metadata: {
          topic,
          contentType,
          targetAudience,
          tone,
          ...additionalParams
        }
      };

      // Add goal to state
      await stateStore.updateState(sessionId, (currentState) => {
        return {
          ...currentState,
          goals: [...(currentState.goals || []), contentStrategyGoal]
        };
      });

      // Trigger the content strategy agent to act
      await contentStrategyAgent.act(sessionId);

      // Schedule validation of content strategy
      setTimeout(() => {
        validateContentStrategy(sessionId, topic, contentType, targetAudience, tone, additionalParams).catch((err: Error) => {
          logger.error(`Error validating content strategy`, {
            sessionId,
            phase: 'content-strategy-validation',
            error: err.message || String(err),
            stack: err.stack
          });
        });
      }, 10000);

      return true;
    } else {
      // If research is incomplete, retry if needed
      const workflowState = state.metadata?.researchWorkflowState as ResearchWorkflowState;

      if (workflowState) {
        // Increment attempt counters for incomplete phases
        if (!marketResearchComplete) {
          workflowState.phaseAttempts['market-research'] += 1;
        }

        if (!keywordResearchComplete) {
          workflowState.phaseAttempts['keyword-research'] += 1;
        }

        // Update state with new attempt counts
        await stateStore.updateState(sessionId, (currentState) => {
          return {
            ...currentState,
            metadata: {
              ...currentState.metadata,
              researchWorkflowState: workflowState
            }
          };
        });

        // If we've tried too many times, proceed anyway with a warning
        const maxAttempts = 3;
        if (workflowState.phaseAttempts['market-research'] > maxAttempts ||
            workflowState.phaseAttempts['keyword-research'] > maxAttempts) {
          logger.warn(`Research phase incomplete after multiple attempts, proceeding anyway`, {
            sessionId,
            marketResearchAttempts: workflowState.phaseAttempts['market-research'],
            keywordResearchAttempts: workflowState.phaseAttempts['keyword-research'],
            marketResearchComplete,
            keywordResearchComplete
          });

          // Create a content strategy goal even with incomplete research
          const contentStrategyGoal: Goal = {
            id: uuidv4(),
            name: 'Develop Content Strategy',
            description: `Develop a content strategy for ${topic} with limited research data`,
            status: 'active' as GoalStatus,
            assignedTo: 'content-strategy',
            createdAt: new Date().toISOString(),
            type: 'create-content-strategy',
            metadata: {
              topic,
              contentType,
              targetAudience,
              tone,
              limitedResearch: true,
              ...additionalParams
            }
          };

          // Add goal to state
          await stateStore.updateState(sessionId, (currentState) => {
            return {
              ...currentState,
              goals: [...(currentState.goals || []), contentStrategyGoal]
            };
          });

          // Trigger the content strategy agent to act
          await contentStrategyAgent.act(sessionId);

          return true;
        } else {
          // Retry the incomplete research phases
          if (!marketResearchComplete) {
            // Create a new market research goal
            const retryMarketResearchGoal: Goal = {
              id: uuidv4(),
              name: 'Retry Market Research',
              description: `Retry market research for ${topic}`,
              status: 'active' as GoalStatus,
              assignedTo: 'market-research',
              createdAt: new Date().toISOString(),
              type: 'market-research',
              metadata: {
                topic,
                contentType,
                targetAudience,
                tone,
                retryAttempt: workflowState.phaseAttempts['market-research'],
                ...additionalParams
              }
            };

            // Add goal to state
            await stateStore.updateState(sessionId, (currentState) => {
              return {
                ...currentState,
                goals: [...(currentState.goals || []), retryMarketResearchGoal]
              };
            });

            // Trigger the market research agent to act
            await marketResearchAgent.act(sessionId);
          }

          if (!keywordResearchComplete) {
            // Create a new keyword research goal
            const retryKeywordResearchGoal: Goal = {
              id: uuidv4(),
              name: 'Retry SEO Keyword Research',
              description: `Retry SEO keyword research for ${topic}`,
              status: 'active' as GoalStatus,
              assignedTo: 'seo-keyword',
              createdAt: new Date().toISOString(),
              type: 'keyword-research',
              metadata: {
                topic,
                contentType,
                targetAudience,
                tone,
                retryAttempt: workflowState.phaseAttempts['keyword-research'],
                ...additionalParams
              }
            };

            // Add goal to state
            await stateStore.updateState(sessionId, (currentState) => {
              return {
                ...currentState,
                goals: [...(currentState.goals || []), retryKeywordResearchGoal]
              };
            });

            // Trigger the SEO keyword agent to act
            await seoKeywordAgent.act(sessionId);
          }

          // Schedule another validation check
          setTimeout(() => {
            validateResearchPhase(sessionId, topic, contentType, targetAudience, tone, additionalParams).catch((err: Error) => {
              logger.error(`Error validating research phase`, {
                sessionId,
                phase: 'research-validation',
                error: err.message || String(err),
                stack: err.stack
              });
            });
          }, 10000);

          return true;
        }
      }
    }

    return false;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error validating research phase`, {
      sessionId,
      phase: 'research-validation',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Validates the content strategy phase and completes the research workflow
 *
 * @param sessionId The session ID
 * @param topic The content topic
 * @param contentType The type of content
 * @param targetAudience The target audience
 * @param tone The tone of the content
 * @param additionalParams Additional parameters
 * @returns Promise<boolean> indicating success or failure
 */
export async function validateContentStrategy(
  sessionId: string,
  topic: string,
  contentType: string = 'blog-article',
  targetAudience: string = 'general audience',
  tone: string = 'informative',
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  logger.info(`Validating content strategy for session ${sessionId}`, {
    sessionId,
    topic,
    contentType,
    targetAudience,
    tone
  });

  try {
    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      logger.error(`No state found for session ${sessionId}`);
      return false;
    }

    // Check if content strategy artifact exists
    const contentStrategyArtifacts = state.generatedArtifacts
      .filter(id => state.artifacts[id]?.type === 'content-strategy');

    logger.info(`Content strategy artifacts found: ${contentStrategyArtifacts.length}`, {
      sessionId,
      artifactIds: contentStrategyArtifacts,
      generatedArtifactsCount: state.generatedArtifacts.length,
      allArtifactTypes: state.generatedArtifacts.map(id => state.artifacts[id]?.type)
    });

    // Check if content strategy is complete
    const contentStrategyComplete = contentStrategyArtifacts.length > 0;

    // Update workflow progress
    await stateStore.updateState(sessionId, (currentState) => {
      return {
        ...currentState,
        workflowProgress: {
          ...currentState.workflowProgress,
          contentStrategyComplete
        }
      };
    });

    // If content strategy is complete, mark the research phase as complete
    if (contentStrategyComplete) {
      // Update workflow state
      const workflowState = state.metadata?.researchWorkflowState as ResearchWorkflowState;

      if (workflowState) {
        workflowState.completedPhases.push('content-strategy');
        workflowState.currentPhase = 'complete';

        // Update state with completed workflow
        await stateStore.updateState(sessionId, (currentState) => {
          return {
            ...currentState,
            currentPhase: 'creation', // Move to the next phase
            metadata: {
              ...currentState.metadata,
              researchWorkflowState: workflowState
            }
          };
        });
      }

      logger.info(`Research phase workflow completed successfully for session ${sessionId}`, {
        sessionId,
        topic,
        contentType,
        targetAudience,
        tone
      });

      // Transition to the content generation phase
      logger.info(`Scheduling transition to content generation phase for session ${sessionId}`, {
        sessionId,
        topic,
        contentType,
        targetAudience,
        tone
      });

      // First, try to call transitionToContentGeneration directly
      try {
        logger.info(`Directly calling transitionToContentGeneration for session ${sessionId}`, {
          sessionId,
          topic,
          contentType,
          targetAudience,
          tone,
          workflowProgress: state.workflowProgress,
          currentPhase: state.currentPhase,
          timestamp: new Date().toISOString()
        });

        // Import directly from workflow-orchestrator
        const { transitionToContentGeneration } = require('./workflow-orchestrator');

        // Call directly without setTimeout
        const result = await transitionToContentGeneration(
          sessionId,
          topic,
          contentType,
          targetAudience,
          tone,
          additionalParams
        );

        logger.info(`Direct transitionToContentGeneration call result: ${result}`, {
          sessionId,
          result,
          timestamp: new Date().toISOString()
        });

        // If direct call fails, try again with setTimeout as a fallback
        if (!result) {
          logger.warn(`Direct call to transitionToContentGeneration failed, scheduling retry`, {
            sessionId,
            timestamp: new Date().toISOString()
          });

          // Schedule a retry with setTimeout
          setTimeout(() => {
            logger.info(`Executing scheduled transition to content generation phase for session ${sessionId}`, {
              sessionId,
              topic,
              contentType,
              targetAudience,
              tone,
              workflowProgress: state.workflowProgress,
              currentPhase: state.currentPhase,
              timestamp: new Date().toISOString()
            });

            try {
              // Import here to avoid circular dependency
              const { transitionToContentGeneration } = require('./workflow-orchestrator');

              logger.info(`Successfully imported transitionToContentGeneration function for retry`, {
                sessionId,
                functionExists: typeof transitionToContentGeneration === 'function',
                timestamp: new Date().toISOString()
              });

              transitionToContentGeneration(
                sessionId,
                topic,
                contentType,
                targetAudience,
                tone,
                additionalParams
              ).then((result) => {
                logger.info(`Scheduled transition to content generation phase result: ${result}`, {
                  sessionId,
                  result,
                  timestamp: new Date().toISOString()
                });
              }).catch((err: Error) => {
                logger.error(`Error in scheduled transition to content generation phase`, {
                  sessionId,
                  phase: 'research-to-creation-transition',
                  error: err.message || String(err),
                  stack: err.stack,
                  timestamp: new Date().toISOString()
                });
              });
            } catch (importError) {
              logger.error(`Error importing or calling transitionToContentGeneration in scheduled retry`, {
                sessionId,
                error: importError instanceof Error ? importError.message : String(importError),
                stack: importError instanceof Error ? importError.stack : undefined,
                timestamp: new Date().toISOString()
              });
            }
          }, 2000);
        }
      } catch (directCallError) {
        logger.error(`Error in direct call to transitionToContentGeneration`, {
          sessionId,
          error: directCallError instanceof Error ? directCallError.message : String(directCallError),
          stack: directCallError instanceof Error ? directCallError.stack : undefined,
          timestamp: new Date().toISOString()
        });

        // Fallback to the original setTimeout approach
        setTimeout(() => {
          logger.info(`Executing fallback transition to content generation phase for session ${sessionId}`, {
            sessionId,
            topic,
            contentType,
            targetAudience,
            tone,
            workflowProgress: state.workflowProgress,
            currentPhase: state.currentPhase,
            timestamp: new Date().toISOString()
          });

          try {
            // Import here to avoid circular dependency
            const { transitionToContentGeneration } = require('./workflow-orchestrator');

            logger.info(`Successfully imported transitionToContentGeneration function in fallback`, {
              sessionId,
              functionExists: typeof transitionToContentGeneration === 'function',
              timestamp: new Date().toISOString()
            });

            transitionToContentGeneration(
              sessionId,
              topic,
              contentType,
              targetAudience,
              tone,
              additionalParams
            ).then((result) => {
              logger.info(`Fallback transition to content generation phase result: ${result}`, {
                sessionId,
                result,
                timestamp: new Date().toISOString()
              });
            }).catch((err: Error) => {
              logger.error(`Error in fallback transition to content generation phase`, {
                sessionId,
                phase: 'research-to-creation-transition',
                error: err.message || String(err),
                stack: err.stack,
                timestamp: new Date().toISOString()
              });
            });
          } catch (importError) {
            logger.error(`Error importing or calling transitionToContentGeneration in fallback`, {
              sessionId,
              error: importError instanceof Error ? importError.message : String(importError),
              stack: importError instanceof Error ? importError.stack : undefined,
              timestamp: new Date().toISOString()
            });
          }
        }, 2000);
      }

      return true;
    } else {
      // If content strategy is incomplete, retry if needed
      const workflowState = state.metadata?.researchWorkflowState as ResearchWorkflowState;

      if (workflowState) {
        // Increment attempt counter for content strategy
        workflowState.phaseAttempts['content-strategy'] += 1;

        // Update state with new attempt count
        await stateStore.updateState(sessionId, (currentState) => {
          return {
            ...currentState,
            metadata: {
              ...currentState.metadata,
              researchWorkflowState: workflowState
            }
          };
        });

        // If we've tried too many times, proceed anyway with a warning
        const maxAttempts = 3;
        if (workflowState.phaseAttempts['content-strategy'] > maxAttempts) {
          logger.warn(`Content strategy phase incomplete after multiple attempts, proceeding anyway`, {
            sessionId,
            contentStrategyAttempts: workflowState.phaseAttempts['content-strategy'],
            contentStrategyComplete
          });

          // Update workflow state to mark research phase as complete despite issues
          workflowState.currentPhase = 'complete';

          // Update state with completed workflow
          await stateStore.updateState(sessionId, (currentState) => {
            return {
              ...currentState,
              currentPhase: 'creation', // Move to the next phase
              metadata: {
                ...currentState.metadata,
                researchWorkflowState: workflowState,
                researchPhaseWarning: 'Content strategy incomplete after multiple attempts'
              }
            };
          });

          return true;
        } else {
          // Retry the content strategy phase
          // Create a new content strategy goal
          const retryContentStrategyGoal: Goal = {
            id: uuidv4(),
            name: 'Retry Content Strategy Development',
            description: `Retry developing content strategy for ${topic}`,
            status: 'active' as GoalStatus,
            assignedTo: 'content-strategy',
            createdAt: new Date().toISOString(),
            type: 'create-content-strategy',
            metadata: {
              topic,
              contentType,
              targetAudience,
              tone,
              retryAttempt: workflowState.phaseAttempts['content-strategy'],
              ...additionalParams
            }
          };

          // Add goal to state
          await stateStore.updateState(sessionId, (currentState) => {
            return {
              ...currentState,
              goals: [...(currentState.goals || []), retryContentStrategyGoal]
            };
          });

          // Trigger the content strategy agent to act
          await contentStrategyAgent.act(sessionId);

          // Schedule another validation check
          setTimeout(() => {
            validateContentStrategy(sessionId, topic, contentType, targetAudience, tone, additionalParams).catch((err: Error) => {
              logger.error(`Error validating content strategy`, {
                sessionId,
                phase: 'content-strategy-validation',
                error: err.message || String(err),
                stack: err.stack
              });
            });
          }, 10000);

          return true;
        }
      }
    }

    return false;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error validating content strategy`, {
      sessionId,
      phase: 'content-strategy-validation',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}