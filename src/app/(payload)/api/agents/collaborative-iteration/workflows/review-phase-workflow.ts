// src/app/(payload)/api/agents/collaborative-iteration/workflows/review-phase-workflow.ts

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeMessageType,
  IterativeCollaborationState,
  Goal,
  GoalStatus,
  ArtifactStatus,
  AgentId
} from '../types';
import { stateStore } from '../utils/stateStore';
import logger from '../utils/logger';
import { seoOptimizationAgent } from '../server-based';

// Workflow state interface
interface ReviewWorkflowState {
  currentPhase: 'seo-optimization' | 'final-review' | 'complete';
  phaseProgress: number;
  phaseStartTime: string;
  phaseAttempts: Record<string, number>;
  completedPhases: string[];
  failedAttempts: Record<string, number>;
  qualityScores: Record<string, number>;
}

/**
 * Initiates the review phase workflow
 * This workflow coordinates SEO optimization and final review
 *
 * @param sessionId The session ID
 * @param topic The content topic
 * @param contentType The type of content (blog-article, product-page, etc.)
 * @param targetAudience The target audience for the content
 * @param tone The tone of the content
 * @param additionalParams Additional parameters for the review
 * @returns Promise<boolean> indicating success or failure
 */
export async function initiateReviewPhase(
  sessionId: string,
  topic: string,
  contentType: string = 'blog-article',
  targetAudience: string = 'general audience',
  tone: string = 'informative',
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  logger.info(`Initiating review phase workflow for session ${sessionId}`, {
    sessionId,
    topic,
    contentType,
    targetAudience,
    tone
  });

  try {
    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      logger.error(`No state found for session ${sessionId}`);
      return false;
    }

    // Check if the content generation phase is complete
    if (!state.workflowProgress?.contentGenerationComplete) {
      logger.warn(`Content generation is not complete for session ${sessionId}. Cannot proceed with review.`, {
        sessionId,
        workflowProgress: state.workflowProgress
      });

      // Force update the workflowProgress to mark content generation as complete
      // This is a fallback in case the content generation phase didn't properly update the flag
      await stateStore.updateState(sessionId, (currentState) => {
        return {
          ...currentState,
          workflowProgress: {
            ...currentState.workflowProgress,
            contentGenerationComplete: true,
            currentPhase: 'review'
          }
        };
      });

      logger.info(`Forced update of workflowProgress for session ${sessionId}`, {
        sessionId,
        updatedWorkflowProgress: (await stateStore.getState(sessionId))?.workflowProgress
      });

      // Cancel any pending content generation goals to prevent further processing
      await stateStore.updateState(sessionId, (currentState) => {
        if (currentState.goals) {
          currentState.goals = currentState.goals.map((goal: Goal) => {
            if (goal.assignedTo === 'content-generation' && goal.status === 'active') {
              return {
                ...goal,
                status: 'cancelled',
                cancelledAt: new Date().toISOString(),
                cancellationReason: 'Workflow transitioned to review phase'
              };
            }
            return goal;
          });
        }
        return currentState;
      });

      logger.info(`Cancelled any pending content generation goals`, {
        sessionId,
        timestamp: new Date().toISOString()
      });
    }

    // Get the content draft artifact - look for multiple possible types
    const contentDraftArtifacts = state.generatedArtifacts
      ? state.generatedArtifacts.filter(id =>
          state.artifacts[id]?.type === 'content-draft' ||
          state.artifacts[id]?.type === 'blog-content' ||
          state.artifacts[id]?.type === 'generated-content' ||
          state.artifacts[id]?.type === 'blog-post')
      : [];

    logger.info(`Found ${contentDraftArtifacts.length} content draft artifacts for session ${sessionId}`, {
      sessionId,
      contentDraftArtifactIds: contentDraftArtifacts,
      artifactTypes: contentDraftArtifacts.map(id => state.artifacts[id]?.type)
    });

    // If no content draft artifacts are found, create a mock one to allow the workflow to continue
    if (contentDraftArtifacts.length === 0) {
      logger.warn(`No content draft artifacts found for session ${sessionId}, creating a mock one`, {
        sessionId,
        timestamp: new Date().toISOString()
      });

      // Create a mock content draft artifact
      const mockContentArtifact: IterativeArtifact = {
        id: uuidv4(),
        name: `Blog Article: ${state.topic} (Mock)`,
        type: 'generated-content',
        status: 'completed' as ArtifactStatus,
        createdBy: 'content-generation',
        createdAt: new Date().toISOString(),
        currentVersion: 1,
        iterations: [
          {
            version: 1,
            timestamp: new Date().toISOString(),
            agent: 'content-generation',
            content: {
              title: `The Ultimate Guide to ${state.topic}`,
              text: `# The Ultimate Guide to ${state.topic}\n\n## Introduction\nThis is a placeholder introduction for ${state.topic}.\n\n## Key Points\nThis is placeholder content for key points about ${state.topic}.\n\n## Conclusion\nThis is a placeholder conclusion for ${state.topic}.`,
              sections: [
                { title: 'Introduction', content: `This is a placeholder introduction for ${state.topic}.` },
                { title: 'Key Points', content: `This is placeholder content for key points about ${state.topic}.` },
                { title: 'Conclusion', content: `This is a placeholder conclusion for ${state.topic}.` }
              ]
            },
            feedback: [],
            incorporatedConsultations: []
          }
        ],
        qualityScore: 70,
        metadata: {
          wordCount: 200,
          readingTime: 1,
          mockGenerated: true
        }
      };

      // Add the mock artifact to state
      await stateStore.updateState(sessionId, (currentState) => {
        // Add the artifact to the artifacts object
        const updatedArtifacts = {
          ...currentState.artifacts,
          [mockContentArtifact.id]: mockContentArtifact
        };

        // Add the artifact ID to the generatedArtifacts array
        const updatedGeneratedArtifacts = [
          ...(currentState.generatedArtifacts || []),
          mockContentArtifact.id
        ];

        return {
          ...currentState,
          artifacts: updatedArtifacts,
          generatedArtifacts: updatedGeneratedArtifacts
        };
      });

      // Update contentDraftArtifacts to include the new mock artifact
      contentDraftArtifacts.push(mockContentArtifact.id);

      logger.info(`Created mock content draft artifact for session ${sessionId}`, {
        sessionId,
        artifactId: mockContentArtifact.id,
        timestamp: new Date().toISOString()
      });
    }

    if (contentDraftArtifacts.length === 0) {
      logger.warn(`No content draft artifact found for session ${sessionId}`, { sessionId });
      return false;
    }

    // Initialize review workflow state
    const workflowState: ReviewWorkflowState = {
      currentPhase: 'seo-optimization',
      phaseProgress: 0,
      phaseStartTime: new Date().toISOString(),
      phaseAttempts: {
        'seo-optimization': 1,
        'final-review': 1
      },
      completedPhases: [],
      failedAttempts: {},
      qualityScores: {}
    };

    // Update state with workflow state
    await stateStore.updateState(sessionId, (currentState) => {
      return {
        ...currentState,
        currentPhase: 'review',
        metadata: {
          ...currentState.metadata,
          reviewWorkflowState: workflowState
        }
      };
    });

    // Get the content draft artifact to use for SEO optimization
    const contentDraftArtifact = state.artifacts[contentDraftArtifacts[0]];

    // Create an SEO optimization goal
    const seoOptimizationGoal: Goal = {
      id: uuidv4(),
      name: 'Optimize Content for SEO',
      description: `Optimize content for ${topic} for SEO`,
      status: 'active' as GoalStatus,
      assignedTo: 'seo-optimization',
      createdAt: new Date().toISOString(),
      type: 'seo-optimization',
      metadata: {
        topic,
        contentType,
        targetAudience,
        tone,
        contentDraftId: contentDraftArtifact.id,
        contentDraft: contentDraftArtifact.content,
        ...additionalParams
      }
    };

    // Add goal to state
    await stateStore.updateState(sessionId, (currentState) => {
      return {
        ...currentState,
        goals: [...(currentState.goals || []), seoOptimizationGoal]
      };
    });

    // Trigger the SEO optimization agent to act
    await seoOptimizationAgent.act(sessionId);

    // Schedule validation of SEO optimization results
    setTimeout(() => {
      validateSeoOptimization(sessionId, topic, contentType, targetAudience, tone, additionalParams).catch((err: Error) => {
        logger.error(`Error validating SEO optimization`, {
          sessionId,
          phase: 'seo-optimization-validation',
          error: err.message || String(err),
          stack: err.stack
        });
      });
    }, 15000);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error initiating review phase`, {
      sessionId,
      phase: 'review',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Validates the SEO optimization results and finalizes the content
 *
 * @param sessionId The session ID
 * @param topic The content topic
 * @param contentType The type of content
 * @param targetAudience The target audience
 * @param tone The tone of the content
 * @param additionalParams Additional parameters
 * @returns Promise<boolean> indicating success or failure
 */
export async function validateSeoOptimization(
  sessionId: string,
  topic: string,
  contentType: string = 'blog-article',
  targetAudience: string = 'general audience',
  tone: string = 'informative',
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  logger.info(`Validating SEO optimization for session ${sessionId}`, {
    sessionId,
    topic,
    contentType
  });

  try {
    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      logger.error(`No state found for session ${sessionId}`);
      return false;
    }

    // Find SEO optimization artifacts
    const seoOptimizationArtifacts = state.generatedArtifacts
      ? state.generatedArtifacts.filter(id => state.artifacts[id]?.type === 'seo-optimization')
      : [];

    // Check if SEO optimization is complete
    const seoOptimizationComplete = seoOptimizationArtifacts.length > 0;

    // Create a final article artifact if SEO optimization is complete
    if (seoOptimizationComplete) {
      // Find the SEO optimized content artifact
      const seoOptimizedArtifact = state.artifacts[seoOptimizationArtifacts[0]];

      // Create a final article artifact
      const finalArticleId = uuidv4();
      const finalArticleArtifact = {
        id: finalArticleId,
        name: `Final Article: ${topic}`,
        type: 'final-article',
        status: 'completed' as ArtifactStatus,
        createdBy: 'system',
        createdAt: new Date().toISOString(),
        currentVersion: 1,
        iterations: [
          {
            version: 1,
            timestamp: new Date().toISOString(),
            agent: 'system',
            content: seoOptimizedArtifact.content,
            feedback: [],
            incorporatedConsultations: []
          }
        ],
        qualityScore: seoOptimizedArtifact.qualityScore || 80,
        metadata: {
          wordCount: typeof seoOptimizedArtifact.content === 'string'
            ? seoOptimizedArtifact.content.split(/\s+/).length
            : 1000,
          readingTime: typeof seoOptimizedArtifact.content === 'string'
            ? Math.ceil(seoOptimizedArtifact.content.split(/\s+/).length / 200)
            : 5,
          sourceArtifactId: seoOptimizedArtifact.id
        }
      };

      // Add the final article artifact to state
      await stateStore.updateState(sessionId, (currentState) => {
        // Add the artifact to the artifacts object
        const updatedArtifacts = {
          ...currentState.artifacts,
          [finalArticleId]: finalArticleArtifact
        };

        // Add the artifact ID to the generatedArtifacts array
        const updatedGeneratedArtifacts = [
          ...(currentState.generatedArtifacts || []),
          finalArticleId
        ];

        return {
          ...currentState,
          artifacts: updatedArtifacts,
          generatedArtifacts: updatedGeneratedArtifacts
        };
      });

      logger.info(`Created final article artifact for session ${sessionId}`, {
        sessionId,
        artifactId: finalArticleId,
        timestamp: new Date().toISOString()
      });
    }

    // Update workflow progress
    await stateStore.updateState(sessionId, (currentState) => {
      if (currentState.workflowProgress) {
        currentState.workflowProgress.seoOptimizationComplete = seoOptimizationComplete;
        currentState.workflowProgress.currentPhase = 'finalization';
      }
      currentState.currentPhase = 'finalization';
      return currentState;
    });

    // Log completion
    logger.info(`Review phase workflow completed for session ${sessionId}`, {
      sessionId,
      topic,
      contentType,
      seoOptimizationComplete
    });

    // Complete the workflow
    setTimeout(() => {
      // Import here to avoid circular dependency
      const { completeWorkflow } = require('./workflow-orchestrator');
      completeWorkflow(
        sessionId,
        topic
      ).catch((err: Error) => {
        logger.error(`Error completing workflow`, {
          sessionId,
          phase: 'review-to-complete-transition',
          error: err.message || String(err),
          stack: err.stack
        });
      });
    }, 2000);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error validating SEO optimization`, {
      sessionId,
      phase: 'seo-optimization-validation',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}
