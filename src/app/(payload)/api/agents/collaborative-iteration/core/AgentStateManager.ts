// src/app/(payload)/api/agents/collaborative-iteration/core/AgentStateManager.ts

import {
  IterativeCollaborationState,
  IterativeArtifact,
  Consultation,
  AgentId,
  AgentState,
  StandardizedHandlerResult
} from '../types';
import { stateStore } from '../utils/stateStore';

/**
 * Agent State Manager
 * Manages state for a specific agent within the collaboration
 */
export class AgentStateManager {
  private agentId: AgentId;

  constructor(agentId: AgentId) {
    this.agentId = agentId;
  }

  /**
   * Get the current session state
   */
  async getSessionState(sessionId: string): Promise<IterativeCollaborationState | null> {
    return await stateStore.getState(sessionId);
  }

  /**
   * Get the agent's state from the session state
   */
  async getAgentState(sessionId: string): Promise<AgentState | null> {
    const state = await this.getSessionState(sessionId);
    if (!state || !state.agentStates) return null;

    return state.agentStates[this.agentId] || null;
  }

  /**
   * Initialize the agent state if it doesn't exist
   */
  async initializeAgentState(sessionId: string): Promise<AgentState> {
    const agentState = await this.getAgentState(sessionId);
    if (agentState) return agentState;

    // Create new agent state
    const newAgentState: AgentState = {
      id: this.agentId,
      processedRequests: [],
      generatedArtifacts: [],
      consultationsProvided: [],
      consultationsReceived: [],
      lastUpdated: new Date().toISOString()
    };

    // Update session state with new agent state
    await stateStore.updateState(sessionId, (state) => {
      if (!state.agentStates) {
        state.agentStates = {};
      }

      state.agentStates[this.agentId] = newAgentState;
      return state;
    });

    return newAgentState;
  }

  /**
   * Check if a message has been processed by this agent
   */
  async hasProcessedMessage(sessionId: string, messageId: string): Promise<boolean> {
    const agentState = await this.getAgentState(sessionId);
    if (!agentState || !agentState.processedRequests) return false;

    return agentState.processedRequests.includes(messageId);
  }

  /**
   * Track a processed message
   */
  async trackProcessedMessage(sessionId: string, messageId: string): Promise<void> {
    await this.initializeAgentState(sessionId);

    await stateStore.updateState(sessionId, (state) => {
      if (!state.agentStates) {
        state.agentStates = {};
      }

      if (!state.agentStates[this.agentId]) {
        state.agentStates[this.agentId] = {
          id: this.agentId,
          processedRequests: [],
          generatedArtifacts: [],
          consultationsProvided: [],
          consultationsReceived: [],
          lastUpdated: new Date().toISOString()
        };
      }

      // Add the message ID to processed requests if not already there
      if (!state.agentStates[this.agentId].processedRequests) {
        state.agentStates[this.agentId].processedRequests = [];
      }

      if (!state.agentStates[this.agentId].processedRequests.includes(messageId)) {
        state.agentStates[this.agentId].processedRequests.push(messageId);
      }

      // Update the last updated timestamp
      state.agentStates[this.agentId].lastUpdated = new Date().toISOString();

      return state;
    });
  }

  /**
   * Add an artifact to the collaboration state
   */
  async addArtifact(sessionId: string, artifact: IterativeArtifact): Promise<void> {
    // Update the artifact with the agent ID if not set
    if (!artifact.createdBy) {
      artifact.createdBy = this.agentId;
    }

    // Add timestamps if not set
    const now = new Date().toISOString();
    if (!artifact.createdAt) {
      artifact.createdAt = now;
    }
    if (!artifact.updatedAt) {
      artifact.updatedAt = now;
    }

    await stateStore.updateState(sessionId, (state) => {
      // Initialize artifacts object if it doesn't exist
      if (!state.artifacts) {
        state.artifacts = {};
      }

      // Add the artifact using its ID as the key
      state.artifacts[artifact.id] = artifact;

      // Initialize generatedArtifacts array if it doesn't exist
      if (!state.generatedArtifacts) {
        state.generatedArtifacts = [];
      }

      // Add the artifact ID to the generatedArtifacts array if not already present
      if (!state.generatedArtifacts.includes(artifact.id)) {
        state.generatedArtifacts.push(artifact.id);
      }

      // Update agent state
      if (!state.agentStates) {
        state.agentStates = {};
      }

      if (!state.agentStates[this.agentId]) {
        state.agentStates[this.agentId] = {
          id: this.agentId,
          processedRequests: [],
          generatedArtifacts: [],
          consultationsProvided: [],
          consultationsReceived: [],
          lastUpdated: now
        };
      }

      if (!state.agentStates[this.agentId].generatedArtifacts) {
        state.agentStates[this.agentId].generatedArtifacts = [];
      }

      state.agentStates[this.agentId].generatedArtifacts.push(artifact.id);
      state.agentStates[this.agentId].lastUpdated = now;

      return state;
    });
  }

  /**
   * Update an existing artifact
   */
  async updateArtifact(sessionId: string, artifact: IterativeArtifact): Promise<void> {
    await stateStore.updateState(sessionId, (state) => {
      // Initialize artifacts object if it doesn't exist
      if (!state.artifacts) {
        state.artifacts = {};
      }

      // Update the artifact with the latest timestamp
      artifact.updatedAt = new Date().toISOString();

      // Store artifact by its ID
      if (artifact.id) {
        state.artifacts[artifact.id] = artifact;

        // Make sure this agent is credited with the artifact if it's not already tracked
        if (!state.agentStates) {
          state.agentStates = {};
        }

        if (!state.agentStates[this.agentId]) {
          state.agentStates[this.agentId] = {
            id: this.agentId,
            processedRequests: [],
            generatedArtifacts: [],
            consultationsProvided: [],
            consultationsReceived: [],
            lastUpdated: new Date().toISOString()
          };
        }

        // Track the artifact ID in the agent's generated artifacts list if not already there
        if (!state.agentStates[this.agentId].generatedArtifacts) {
          state.agentStates[this.agentId].generatedArtifacts = [];
        }

        if (!state.agentStates[this.agentId].generatedArtifacts.includes(artifact.id)) {
          state.agentStates[this.agentId].generatedArtifacts.push(artifact.id);
        }
      } else {
        console.error('Attempted to update an artifact without an ID');
      }

      return state;
    });
  }

  /**
   * Add a consultation to the collaboration state
   */
  async addConsultation(sessionId: string, consultation: Consultation): Promise<void> {
    await stateStore.updateState(sessionId, (state) => {
      // Initialize consultations array if it doesn't exist
      if (!state.consultations) {
        state.consultations = [];
      }

      // Add the consultation
      state.consultations.push(consultation);

      // Update agent state
      if (!state.agentStates) {
        state.agentStates = {};
      }

      // Update provider agent state
      if (!state.agentStates[this.agentId]) {
        state.agentStates[this.agentId] = {
          id: this.agentId,
          processedRequests: [],
          generatedArtifacts: [],
          consultationsProvided: [],
          consultationsReceived: [],
          lastUpdated: new Date().toISOString()
        };
      }

      if (!state.agentStates[this.agentId].consultationsProvided) {
        state.agentStates[this.agentId].consultationsProvided = [];
      }

      state.agentStates[this.agentId].consultationsProvided.push(consultation.id);
      state.agentStates[this.agentId].lastUpdated = new Date().toISOString();

      // Update requester agent state if different from provider
      if (consultation.fromAgent !== this.agentId && state.agentStates[consultation.fromAgent]) {
        if (!state.agentStates[consultation.fromAgent].consultationsReceived) {
          state.agentStates[consultation.fromAgent].consultationsReceived = [];
        }

        state.agentStates[consultation.fromAgent].consultationsReceived.push(consultation.id);
        state.agentStates[consultation.fromAgent].lastUpdated = new Date().toISOString();
      }

      return state;
    });
  }

  /**
   * Apply a standardized result to the state
   */
  async applyResult(sessionId: string, result: StandardizedHandlerResult): Promise<void> {
    // Track the message if it was processed successfully
    if (result.response) {
      if (result.response.id) {
        await this.trackProcessedMessage(sessionId, result.response.id);
      }

      // Store the response message in the state
      await stateStore.updateState(sessionId, (state) => {
        if (!state.messages) {
          state.messages = [];
        }

        state.messages.push(result.response);

        return state;
      });
    }

    // Handle single artifact if present (older format)
    if ('artifact' in result && result.artifact) {
      console.log(`AgentStateManager: Adding single artifact from result: ${result.artifact.id}`);
      await this.addArtifact(sessionId, result.artifact);
    }

    // Handle artifact updates (newer format used by content generation agent)
    if ('artifactUpdates' in result && result.artifactUpdates) {
      // Handle new artifacts
      if (result.artifactUpdates.new && typeof result.artifactUpdates.new === 'object') {
        console.log(`AgentStateManager: Adding ${Object.keys(result.artifactUpdates.new).length} new artifacts from artifactUpdates`);
        for (const artifactId in result.artifactUpdates.new) {
          const artifact = result.artifactUpdates.new[artifactId];
          if (artifact) {
            await this.addArtifact(sessionId, artifact);
          }
        }
      }

      // Handle updated artifacts
      if (result.artifactUpdates.updated && typeof result.artifactUpdates.updated === 'object') {
        console.log(`AgentStateManager: Updating ${Object.keys(result.artifactUpdates.updated).length} artifacts from artifactUpdates`);
        for (const artifactId in result.artifactUpdates.updated) {
          const artifact = result.artifactUpdates.updated[artifactId];
          if (artifact) {
            await this.updateArtifact(sessionId, artifact);
          }
        }
      }
    }
  }

  /**
   * Find an artifact by ID
   */
  async findArtifact(sessionId: string, artifactId: string): Promise<IterativeArtifact | null> {
    const state = await this.getSessionState(sessionId);
    if (!state || !state.artifacts) return null;

    // Direct lookup in artifacts object by ID
    return state.artifacts[artifactId] || null;
  }

  /**
   * Find artifacts by type
   */
  async findArtifactsByType(sessionId: string, artifactType: string): Promise<IterativeArtifact[]> {
    const state = await this.getSessionState(sessionId);
    if (!state || !state.artifacts) return [];

    // Convert object to array and filter by type
    return Object.values(state.artifacts).filter(a => a.type === artifactType);
  }

  /**
   * Track a new artifact in the agent state
   * @param sessionId The session ID
   * @param artifact The artifact to track
   */
  async trackNewArtifact(sessionId: string, artifact: IterativeArtifact): Promise<void> {
    await stateStore.updateState(sessionId, (state) => {
      // Initialize agent state if it doesn't exist
      if (!state.agentStates) {
        state.agentStates = {};
      }

      if (!state.agentStates[this.agentId]) {
        state.agentStates[this.agentId] = {
          id: this.agentId,
          processedRequests: [],
          generatedArtifacts: [],
          consultationsProvided: [],
          consultationsReceived: [],
          lastUpdated: new Date().toISOString()
        };
      }

      // Initialize generatedArtifacts array if it doesn't exist
      if (!state.agentStates[this.agentId].generatedArtifacts) {
        state.agentStates[this.agentId].generatedArtifacts = [];
      }

      // Add the artifact ID to the agent's generatedArtifacts array if not already present
      if (!state.agentStates[this.agentId].generatedArtifacts.includes(artifact.id)) {
        state.agentStates[this.agentId].generatedArtifacts.push(artifact.id);
      }

      // Update the last updated timestamp
      state.agentStates[this.agentId].lastUpdated = new Date().toISOString();

      return state;
    });
  }

  /**
   * Check if a message has been processed by this agent (synchronous version)
   * This is useful when we already have the state and don't want to fetch it again
   */
  hasProcessedMessageSync(sessionId: string, messageId: string): boolean {
    const state = stateStore.getStateSync(sessionId);
    if (!state || !state.agentStates || !state.agentStates[this.agentId]) return false;

    const agentState = state.agentStates[this.agentId];
    if (!agentState.processedRequests) return false;

    return agentState.processedRequests.includes(messageId);
  }
}
