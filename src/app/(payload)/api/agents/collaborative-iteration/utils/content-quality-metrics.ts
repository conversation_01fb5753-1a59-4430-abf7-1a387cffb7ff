/**
 * Content Quality Metrics Module
 *
 * This module provides advanced content quality metrics for assessing and improving content.
 * It implements various readability metrics, content structure analysis, engagement potential scoring,
 * and content coherence and flow analysis.
 *
 * Features:
 * - Multiple readability metrics (<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>OG, Coleman-<PERSON>u)
 * - Content structure analysis (headings, paragraphs, lists)
 * - Engagement potential scoring
 * - Content coherence and flow analysis
 * - Semantic richness assessment
 */

import { IterativeArtifact } from '../types';

/**
 * Content quality metrics result interface
 */
export interface ContentQualityMetricsResult {
  /** Overall quality score (0-1) */
  overallScore: number;
  /** Readability metrics */
  readability: {
    /** Flesch-Kincaid reading ease score (0-100) */
    fleschKincaidScore: number;
    /** SMOG index */
    smogIndex: number;
    /** Coleman-Liau index */
    colemanLiauIndex: number;
    /** Automated readability index */
    automatedReadabilityIndex: number;
    /** Estimated reading grade level */
    readingGradeLevel: number;
    /** Estimated reading time in minutes */
    readingTimeMinutes: number;
  };
  /** Structure metrics */
  structure: {
    /** Heading count */
    headingCount: number;
    /** Paragraph count */
    paragraphCount: number;
    /** List count */
    listCount: number;
    /** Image count */
    imageCount: number;
    /** Table count */
    tableCount: number;
    /** Link count */
    linkCount: number;
    /** Structure score (0-1) */
    structureScore: number;
  };
  /** Engagement metrics */
  engagement: {
    /** Question count */
    questionCount: number;
    /** Personal pronoun count */
    personalPronounCount: number;
    /** Storytelling element count */
    storytellingElementCount: number;
    /** Call to action count */
    callToActionCount: number;
    /** Engagement score (0-1) */
    engagementScore: number;
  };
  /** Content metrics */
  content: {
    /** Word count */
    wordCount: number;
    /** Sentence count */
    sentenceCount: number;
    /** Average words per sentence */
    avgWordsPerSentence: number;
    /** Paragraph count */
    paragraphCount: number;
    /** Average sentences per paragraph */
    avgSentencesPerParagraph: number;
    /** Passive voice percentage */
    passiveVoicePercentage: number;
    /** Adverb percentage */
    adverbPercentage: number;
    /** Cliché count */
    clicheCount: number;
    /** Jargon count */
    jargonCount: number;
    /** Transition words count */
    transitionWordsCount: number;
    /** Content score (0-1) */
    contentScore: number;
  };
  /** Coherence metrics */
  coherence: {
    /** Topic consistency score (0-1) */
    topicConsistencyScore: number;
    /** Logical flow score (0-1) */
    logicalFlowScore: number;
    /** Transition quality score (0-1) */
    transitionQualityScore: number;
    /** Coherence score (0-1) */
    coherenceScore: number;
  };
  /** SEO metrics */
  seo: {
    /** Keyword density for primary keywords */
    keywordDensity: Record<string, number>;
    /** Keyword in title */
    keywordInTitle: boolean;
    /** Keyword in headings count */
    keywordInHeadingsCount: number;
    /** Keyword in first paragraph */
    keywordInFirstParagraph: boolean;
    /** Keyword in last paragraph */
    keywordInLastParagraph: boolean;
    /** SEO score (0-1) */
    seoScore: number;
  };
  /** Improvement suggestions */
  suggestions: string[];
}

/**
 * Calculate comprehensive content quality metrics
 *
 * @param artifact - The artifact to analyze
 * @param targetKeywords - Target keywords for SEO assessment
 * @returns Comprehensive content quality metrics
 */
export function calculateContentQualityMetrics(
  artifact: IterativeArtifact,
  targetKeywords: string[] = []
): ContentQualityMetricsResult {
  // Extract content as string
  const content = extractContentAsString(artifact);

  // Initialize result
  const result: ContentQualityMetricsResult = {
    overallScore: 0,
    readability: {
      fleschKincaidScore: 0,
      smogIndex: 0,
      colemanLiauIndex: 0,
      automatedReadabilityIndex: 0,
      readingGradeLevel: 0,
      readingTimeMinutes: 0
    },
    structure: {
      headingCount: 0,
      paragraphCount: 0,
      listCount: 0,
      imageCount: 0,
      tableCount: 0,
      linkCount: 0,
      structureScore: 0
    },
    engagement: {
      questionCount: 0,
      personalPronounCount: 0,
      storytellingElementCount: 0,
      callToActionCount: 0,
      engagementScore: 0
    },
    content: {
      wordCount: 0,
      sentenceCount: 0,
      avgWordsPerSentence: 0,
      paragraphCount: 0,
      avgSentencesPerParagraph: 0,
      passiveVoicePercentage: 0,
      adverbPercentage: 0,
      clicheCount: 0,
      jargonCount: 0,
      transitionWordsCount: 0,
      contentScore: 0
    },
    coherence: {
      topicConsistencyScore: 0,
      logicalFlowScore: 0,
      transitionQualityScore: 0,
      coherenceScore: 0
    },
    seo: {
      keywordDensity: {},
      keywordInTitle: false,
      keywordInHeadingsCount: 0,
      keywordInFirstParagraph: false,
      keywordInLastParagraph: false,
      seoScore: 0
    },
    suggestions: []
  };

  try {
    // Skip empty content
    if (!content) {
      return result;
    }

    // Calculate basic content metrics
    calculateBasicContentMetrics(content, result);

    // Calculate readability metrics
    calculateReadabilityMetrics(content, result);

    // Calculate structure metrics
    calculateStructureMetrics(content, artifact, result);

    // Calculate engagement metrics
    calculateEngagementMetrics(content, result);

    // Calculate coherence metrics
    calculateCoherenceMetrics(content, result);

    // Calculate SEO metrics
    calculateSeoMetrics(content, targetKeywords, result);

    // Generate improvement suggestions
    generateImprovementSuggestions(result);

    // Calculate overall score (weighted average)
    const weights = {
      readability: 0.2,
      structure: 0.2,
      engagement: 0.2,
      content: 0.2,
      coherence: 0.1,
      seo: 0.1
    };

    const readabilityScore = normalizeReadabilityScore(result.readability.fleschKincaidScore);

    result.overallScore =
      readabilityScore * weights.readability +
      result.structure.structureScore * weights.structure +
      result.engagement.engagementScore * weights.engagement +
      result.content.contentScore * weights.content +
      result.coherence.coherenceScore * weights.coherence +
      result.seo.seoScore * weights.seo;

    return result;
  } catch (error) {
    console.error('Error calculating content quality metrics:', error);
    return result;
  }
}

/**
 * Extract content as string from artifact
 */
function extractContentAsString(artifact: IterativeArtifact): string {
  if (!artifact || !artifact.content) {
    return '';
  }

  // Handle string content
  if (typeof artifact.content === 'string') {
    return artifact.content;
  }

  // Handle object content with text field
  if (typeof artifact.content === 'object' && artifact.content.text) {
    return typeof artifact.content.text === 'string' ? artifact.content.text : '';
  }

  // Handle object content with content field
  if (typeof artifact.content === 'object' && artifact.content.content) {
    return typeof artifact.content.content === 'string' ? artifact.content.content : '';
  }

  // Handle object content with sections
  if (typeof artifact.content === 'object' && Array.isArray(artifact.content.sections)) {
    return artifact.content.sections
      .map(section => {
        if (typeof section === 'string') {
          return section;
        } else if (typeof section === 'object') {
          return section.content || section.text || '';
        }
        return '';
      })
      .join('\n\n');
  }

  // Fallback: stringify the content
  try {
    return JSON.stringify(artifact.content);
  } catch (e) {
    return '';
  }
}

/**
 * Calculate basic content metrics
 */
function calculateBasicContentMetrics(content: string, result: ContentQualityMetricsResult): void {
  // Calculate word count
  const words = content.split(/\s+/).filter(word => word.length > 0);
  result.content.wordCount = words.length;

  // Calculate sentence count
  const sentences = content.split(/[.!?]+\s/).filter(sentence => sentence.length > 0);
  result.content.sentenceCount = sentences.length;

  // Calculate average words per sentence
  result.content.avgWordsPerSentence = result.content.sentenceCount > 0
    ? result.content.wordCount / result.content.sentenceCount
    : 0;

  // Calculate paragraph count
  const paragraphs = content.split(/\n\s*\n/).filter(paragraph => paragraph.length > 0);
  result.content.paragraphCount = paragraphs.length;
  result.structure.paragraphCount = paragraphs.length;

  // Calculate average sentences per paragraph
  result.content.avgSentencesPerParagraph = result.content.paragraphCount > 0
    ? result.content.sentenceCount / result.content.paragraphCount
    : 0;

  // Calculate passive voice percentage (simplified)
  const passiveVoiceMatches = content.match(/\b(?:is|are|was|were|be|been|being)\s+\w+ed\b/g);
  result.content.passiveVoicePercentage = result.content.sentenceCount > 0 && passiveVoiceMatches
    ? (passiveVoiceMatches.length / result.content.sentenceCount) * 100
    : 0;

  // Calculate adverb percentage (simplified)
  const adverbMatches = content.match(/\w+ly\b/g);
  result.content.adverbPercentage = result.content.wordCount > 0 && adverbMatches
    ? (adverbMatches.length / result.content.wordCount) * 100
    : 0;

  // Calculate cliché count (simplified)
  const cliches = [
    'at the end of the day',
    'think outside the box',
    'cutting edge',
    'win-win situation',
    'low hanging fruit',
    'paradigm shift',
    'push the envelope',
    'game changer',
    'back to the drawing board',
    'hit the ground running'
  ];

  result.content.clicheCount = cliches.reduce((count, cliche) => {
    const regex = new RegExp(cliche, 'gi');
    const matches = content.match(regex) || [];
    return count + matches.length;
  }, 0);

  // Calculate transition words count
  const transitionWords = [
    'however', 'therefore', 'consequently', 'furthermore', 'moreover',
    'nevertheless', 'nonetheless', 'meanwhile', 'subsequently', 'conversely',
    'similarly', 'likewise', 'in contrast', 'on the other hand', 'in conclusion'
  ];

  result.content.transitionWordsCount = transitionWords.reduce((count, word) => {
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    const matches = content.match(regex) || [];
    return count + matches.length;
  }, 0);

  // Calculate content score
  calculateContentScore(result);
}

/**
 * Calculate content score
 */
function calculateContentScore(result: ContentQualityMetricsResult): void {
  let score = 0.7; // Default score

  // Assess word count
  if (result.content.wordCount >= 1000) {
    score += 0.1;
  } else if (result.content.wordCount >= 500) {
    score += 0.05;
  } else if (result.content.wordCount < 300) {
    score -= 0.1;
  }

  // Assess paragraph structure
  if (result.content.paragraphCount >= 5) {
    score += 0.05;
  } else if (result.content.paragraphCount < 3) {
    score -= 0.05;
  }

  // Assess sentence length
  if (result.content.avgWordsPerSentence >= 10 && result.content.avgWordsPerSentence <= 20) {
    score += 0.05;
  } else if (result.content.avgWordsPerSentence > 25) {
    score -= 0.05;
  } else if (result.content.avgWordsPerSentence < 8) {
    score -= 0.03;
  }

  // Assess transition words
  const transitionWordsRatio = result.content.sentenceCount > 0
    ? result.content.transitionWordsCount / result.content.sentenceCount
    : 0;

  if (transitionWordsRatio >= 0.2) {
    score += 0.05;
  } else if (transitionWordsRatio < 0.1) {
    score -= 0.03;
  }

  // Assess clichés
  if (result.content.clicheCount > 3) {
    score -= 0.05;
  }

  // Assess passive voice
  if (result.content.passiveVoicePercentage > 20) {
    score -= 0.05;
  } else if (result.content.passiveVoicePercentage < 10) {
    score += 0.03;
  }

  // Assess adverb usage
  if (result.content.adverbPercentage > 5) {
    score -= 0.05;
  } else if (result.content.adverbPercentage < 2) {
    score += 0.03;
  }

  // Ensure score is in valid range
  result.content.contentScore = Math.max(0, Math.min(1, score));
}

/**
 * Calculate readability metrics
 */
function calculateReadabilityMetrics(content: string, result: ContentQualityMetricsResult): void {
  // Calculate Flesch-Kincaid reading ease score
  result.readability.fleschKincaidScore = calculateFleschKincaidScore(
    result.content.wordCount,
    result.content.sentenceCount
  );

  // Calculate SMOG index
  result.readability.smogIndex = calculateSmogIndex(content);

  // Calculate Coleman-Liau index
  result.readability.colemanLiauIndex = calculateColemanLiauIndex(content);

  // Calculate automated readability index
  result.readability.automatedReadabilityIndex = calculateAutomatedReadabilityIndex(
    result.content.wordCount,
    result.content.sentenceCount,
    countCharacters(content)
  );

  // Calculate reading grade level (average of all metrics)
  result.readability.readingGradeLevel = calculateReadingGradeLevel(result);

  // Calculate reading time in minutes
  result.readability.readingTimeMinutes = calculateReadingTime(result.content.wordCount);
}

/**
 * Calculate Flesch-Kincaid readability score
 * Formula: 206.835 - 1.015 * (words / sentences) - 84.6 * (syllables / words)
 *
 * This is a simplified implementation that estimates syllables
 */
function calculateFleschKincaidScore(wordCount: number, sentenceCount: number): number {
  if (wordCount === 0 || sentenceCount === 0) {
    return 0;
  }

  // Estimate average syllables per word (English average is around 1.5)
  const avgSyllablesPerWord = 1.5;

  // Calculate score
  const score = 206.835 - 1.015 * (wordCount / sentenceCount) - 84.6 * avgSyllablesPerWord;

  // Clamp score to 0-100 range
  return Math.max(0, Math.min(100, score));
}

/**
 * Calculate SMOG index
 * Formula: 1.043 * sqrt(polysyllables * (30 / sentences)) + 3.1291
 *
 * This is a simplified implementation that estimates polysyllables
 */
function calculateSmogIndex(content: string): number {
  // Count sentences
  const sentences = content.split(/[.!?]+\s/).filter(sentence => sentence.length > 0);
  const sentenceCount = sentences.length;

  if (sentenceCount === 0) {
    return 0;
  }

  // Estimate polysyllables (words with 3+ syllables)
  // For simplicity, we'll count words with 8+ characters as a rough approximation
  const words = content.split(/\s+/).filter(word => word.length > 0);
  const polysyllableCount = words.filter(word => word.length >= 8).length;

  // Calculate SMOG index
  return 1.043 * Math.sqrt(polysyllableCount * (30 / sentenceCount)) + 3.1291;
}

/**
 * Calculate Coleman-Liau index
 * Formula: 0.0588 * L - 0.296 * S - 15.8
 * where L is average number of characters per 100 words
 * and S is average number of sentences per 100 words
 */
function calculateColemanLiauIndex(content: string): number {
  // Count words
  const words = content.split(/\s+/).filter(word => word.length > 0);
  const wordCount = words.length;

  if (wordCount === 0) {
    return 0;
  }

  // Count characters (excluding spaces)
  const charCount = content.replace(/\s+/g, '').length;

  // Count sentences
  const sentences = content.split(/[.!?]+\s/).filter(sentence => sentence.length > 0);
  const sentenceCount = sentences.length;

  // Calculate L (average number of characters per 100 words)
  const L = (charCount / wordCount) * 100;

  // Calculate S (average number of sentences per 100 words)
  const S = (sentenceCount / wordCount) * 100;

  // Calculate Coleman-Liau index
  return 0.0588 * L - 0.296 * S - 15.8;
}

/**
 * Calculate automated readability index
 * Formula: 4.71 * (characters / words) + 0.5 * (words / sentences) - 21.43
 */
function calculateAutomatedReadabilityIndex(
  wordCount: number,
  sentenceCount: number,
  charCount: number
): number {
  if (wordCount === 0 || sentenceCount === 0) {
    return 0;
  }

  // Calculate automated readability index
  return 4.71 * (charCount / wordCount) + 0.5 * (wordCount / sentenceCount) - 21.43;
}

/**
 * Count characters in text (excluding spaces)
 */
function countCharacters(text: string): number {
  return text.replace(/\s+/g, '').length;
}

/**
 * Calculate reading grade level (average of all metrics)
 */
function calculateReadingGradeLevel(result: ContentQualityMetricsResult): number {
  // Convert Flesch-Kincaid score to grade level
  const fleschKincaidGradeLevel =
    result.readability.fleschKincaidScore >= 90 ? 5 :
    result.readability.fleschKincaidScore >= 80 ? 6 :
    result.readability.fleschKincaidScore >= 70 ? 7 :
    result.readability.fleschKincaidScore >= 60 ? 8 :
    result.readability.fleschKincaidScore >= 50 ? 10 :
    result.readability.fleschKincaidScore >= 30 ? 12 :
    16; // College level

  // Calculate average grade level
  const gradeLevel = (
    fleschKincaidGradeLevel +
    result.readability.smogIndex +
    result.readability.colemanLiauIndex +
    result.readability.automatedReadabilityIndex
  ) / 4;

  // Round to one decimal place
  return Math.round(gradeLevel * 10) / 10;
}

/**
 * Calculate reading time in minutes
 * Average reading speed is about 200-250 words per minute
 */
function calculateReadingTime(wordCount: number): number {
  const wordsPerMinute = 225; // Average reading speed
  const minutes = wordCount / wordsPerMinute;

  // Round to one decimal place
  return Math.round(minutes * 10) / 10;
}

/**
 * Normalize readability score to 0-1 range
 */
function normalizeReadabilityScore(fleschKincaidScore: number): number {
  // Flesch-Kincaid score is 0-100 where higher is better (easier to read)
  // We want to normalize to 0-1 where higher is better

  // Optimal readability is 60-80 (grade 7-8 level)
  if (fleschKincaidScore >= 60 && fleschKincaidScore <= 80) {
    return 0.9;
  } else if (fleschKincaidScore > 80) {
    // Very easy to read (grade 6 or below)
    return 0.8;
  } else if (fleschKincaidScore >= 50) {
    // Fairly difficult (grade 10-12)
    return 0.7;
  } else if (fleschKincaidScore >= 30) {
    // Difficult (college level)
    return 0.5;
  } else {
    // Very difficult (graduate level)
    return 0.3;
  }
}

/**
 * Calculate structure metrics
 */
function calculateStructureMetrics(
  content: string,
  artifact: IterativeArtifact,
  result: ContentQualityMetricsResult
): void {
  // Calculate heading count
  const headingMatches = content.match(/#+\s+|<h[1-6]>/g);
  result.structure.headingCount = headingMatches ? headingMatches.length : 0;

  // Calculate list count
  result.structure.listCount = countLists(content);

  // Calculate image count
  result.structure.imageCount = countImages(content);

  // Calculate table count
  result.structure.tableCount = countTables(content);

  // Calculate link count
  result.structure.linkCount = countLinks(content);

  // Calculate structure score
  calculateStructureScore(result);
}

/**
 * Count lists in content
 */
function countLists(content: string): number {
  // Count markdown lists
  const markdownListMatches = content.match(/^[\s]*[-*+][\s]+|^[\s]*\d+\.[\s]+/gm);
  const markdownListCount = markdownListMatches ? markdownListMatches.length : 0;

  // Count HTML lists
  const htmlListMatches = content.match(/<[uo]l>|<li>/gi);
  const htmlListCount = htmlListMatches ? htmlListMatches.length : 0;

  // Return total list count
  return markdownListCount + htmlListCount;
}

/**
 * Count images in content
 */
function countImages(content: string): number {
  // Count markdown images
  const markdownImageMatches = content.match(/!\[.*?\]\(.*?\)/g);
  const markdownImageCount = markdownImageMatches ? markdownImageMatches.length : 0;

  // Count HTML images
  const htmlImageMatches = content.match(/<img[^>]*>/gi);
  const htmlImageCount = htmlImageMatches ? htmlImageMatches.length : 0;

  // Return total image count
  return markdownImageCount + htmlImageCount;
}

/**
 * Count tables in content
 */
function countTables(content: string): number {
  // Count markdown tables
  const markdownTableMatches = content.match(/\|.*\|.*\n\|[-:|\s]+\|/g);
  const markdownTableCount = markdownTableMatches ? markdownTableMatches.length : 0;

  // Count HTML tables
  const htmlTableMatches = content.match(/<table[^>]*>/gi);
  const htmlTableCount = htmlTableMatches ? htmlTableMatches.length : 0;

  // Return total table count
  return markdownTableCount + htmlTableCount;
}

/**
 * Count links in content
 */
function countLinks(content: string): number {
  // Count markdown links
  const markdownLinkMatches = content.match(/\[.*?\]\(.*?\)/g);
  const markdownLinkCount = markdownLinkMatches ? markdownLinkMatches.length : 0;

  // Count HTML links
  const htmlLinkMatches = content.match(/<a[^>]*>/gi);
  const htmlLinkCount = htmlLinkMatches ? htmlLinkMatches.length : 0;

  // Return total link count
  return markdownLinkCount + htmlLinkCount;
}

/**
 * Calculate structure score
 */
function calculateStructureScore(result: ContentQualityMetricsResult): void {
  let score = 0.7; // Default score

  // Assess heading usage
  if (result.structure.headingCount >= 3) {
    score += 0.1;
  } else if (result.structure.headingCount === 0) {
    score -= 0.1;
  } else if (result.structure.headingCount < 2) {
    score -= 0.05;
  }

  // Assess paragraph to heading ratio
  const paragraphsPerHeading = result.structure.headingCount > 0
    ? result.structure.paragraphCount / result.structure.headingCount
    : result.structure.paragraphCount;

  if (paragraphsPerHeading > 5) {
    score -= 0.05;
  } else if (paragraphsPerHeading >= 1.5 && paragraphsPerHeading <= 3) {
    score += 0.05;
  }

  // Assess list usage
  if (result.structure.listCount > 0) {
    score += 0.05;
  }

  // Assess image usage
  if (result.structure.imageCount > 0) {
    score += 0.05;
  }

  // Assess table usage
  if (result.structure.tableCount > 0) {
    score += 0.05;
  }

  // Assess link usage
  if (result.structure.linkCount > 0) {
    score += 0.05;
  }

  // Ensure score is in valid range
  result.structure.structureScore = Math.max(0, Math.min(1, score));
}

/**
 * Calculate engagement metrics
 */
function calculateEngagementMetrics(content: string, result: ContentQualityMetricsResult): void {
  // Calculate question count
  result.engagement.questionCount = (content.match(/\?/g) || []).length;

  // Calculate personal pronoun count
  const personalPronounMatches = content.match(/\b(you|your|we|our|us)\b/gi);
  result.engagement.personalPronounCount = personalPronounMatches ? personalPronounMatches.length : 0;

  // Calculate storytelling element count
  result.engagement.storytellingElementCount = countStorytellingElements(content);

  // Calculate call to action count
  result.engagement.callToActionCount = countCallToActions(content);

  // Calculate engagement score
  calculateEngagementScore(result);
}

/**
 * Count storytelling elements in content
 */
function countStorytellingElements(content: string): number {
  // Check for storytelling markers
  const storytellingMarkers = [
    /once upon a time/i,
    /story/i,
    /experience/i,
    /journey/i,
    /challenge/i,
    /struggle/i,
    /learned/i,
    /discovered/i,
    /realized/i,
    /imagine/i
  ];

  return storytellingMarkers.reduce((count, marker) => {
    return count + (marker.test(content) ? 1 : 0);
  }, 0);
}

/**
 * Count calls to action in content
 */
function countCallToActions(content: string): number {
  // Check for CTA markers
  const ctaMarkers = [
    /sign up/i,
    /subscribe/i,
    /download/i,
    /register/i,
    /buy now/i,
    /get started/i,
    /learn more/i,
    /click here/i,
    /contact us/i,
    /try it/i,
    /start your/i,
    /begin your/i,
    /join our/i,
    /call us/i,
    /email us/i
  ];

  return ctaMarkers.reduce((count, marker) => {
    const matches = content.match(marker) || [];
    return count + matches.length;
  }, 0);
}

/**
 * Calculate engagement score
 */
function calculateEngagementScore(result: ContentQualityMetricsResult): void {
  let score = 0.7; // Default score

  // Assess question usage
  const questionRatio = result.content.sentenceCount > 0
    ? result.engagement.questionCount / result.content.sentenceCount
    : 0;

  if (questionRatio >= 0.1 && questionRatio <= 0.2) {
    score += 0.1;
  } else if (questionRatio > 0.2) {
    score -= 0.05;
  } else if (result.engagement.questionCount === 0) {
    score -= 0.05;
  }

  // Assess personal pronoun usage
  const personalPronounRatio = result.content.wordCount > 0
    ? result.engagement.personalPronounCount / result.content.wordCount
    : 0;

  if (personalPronounRatio >= 0.01 && personalPronounRatio <= 0.03) {
    score += 0.1;
  } else if (result.engagement.personalPronounCount === 0) {
    score -= 0.05;
  }

  // Assess storytelling elements
  if (result.engagement.storytellingElementCount >= 3) {
    score += 0.1;
  } else if (result.engagement.storytellingElementCount > 0) {
    score += 0.05;
  }

  // Assess calls to action
  if (result.engagement.callToActionCount >= 2) {
    score += 0.1;
  } else if (result.engagement.callToActionCount > 0) {
    score += 0.05;
  } else {
    score -= 0.05;
  }

  // Ensure score is in valid range
  result.engagement.engagementScore = Math.max(0, Math.min(1, score));
}

/**
 * Calculate coherence metrics
 */
function calculateCoherenceMetrics(content: string, result: ContentQualityMetricsResult): void {
  // Calculate topic consistency score
  result.coherence.topicConsistencyScore = calculateTopicConsistencyScore(content);

  // Calculate logical flow score
  result.coherence.logicalFlowScore = calculateLogicalFlowScore(content);

  // Calculate transition quality score
  result.coherence.transitionQualityScore = calculateTransitionQualityScore(content, result);

  // Calculate overall coherence score
  result.coherence.coherenceScore = (
    result.coherence.topicConsistencyScore * 0.4 +
    result.coherence.logicalFlowScore * 0.3 +
    result.coherence.transitionQualityScore * 0.3
  );
}

/**
 * Calculate topic consistency score
 */
function calculateTopicConsistencyScore(content: string): number {
  // This is a simplified implementation
  // In a real implementation, this would use NLP to analyze topic consistency

  // For now, we'll use a simple heuristic based on paragraph similarity
  const paragraphs = content.split(/\n\s*\n/).filter(paragraph => paragraph.length > 0);

  if (paragraphs.length <= 1) {
    return 0.7; // Default score for single paragraph
  }

  // Extract key terms from each paragraph
  const paragraphTerms = paragraphs.map(paragraph => {
    const words = paragraph.toLowerCase().split(/\s+/);
    const stopWords = ['a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by'];
    return words.filter(word => word.length > 3 && !stopWords.includes(word));
  });

  // Calculate similarity between consecutive paragraphs
  let totalSimilarity = 0;

  for (let i = 0; i < paragraphTerms.length - 1; i++) {
    const currentTerms = new Set(paragraphTerms[i]);
    const nextTerms = new Set(paragraphTerms[i + 1]);

    // Calculate Jaccard similarity
    const intersection = new Set([...currentTerms].filter(term => nextTerms.has(term)));
    const union = new Set([...currentTerms, ...nextTerms]);

    const similarity = union.size > 0 ? intersection.size / union.size : 0;
    totalSimilarity += similarity;
  }

  // Calculate average similarity
  const avgSimilarity = paragraphTerms.length > 1
    ? totalSimilarity / (paragraphTerms.length - 1)
    : 0;

  // Convert to score (0-1)
  return Math.min(0.5 + avgSimilarity * 0.5, 1);
}

/**
 * Calculate logical flow score
 */
function calculateLogicalFlowScore(content: string): number {
  // This is a simplified implementation
  // In a real implementation, this would use NLP to analyze logical flow

  // For now, we'll use a simple heuristic based on transition words and structure

  // Check for logical flow markers
  const logicalFlowMarkers = [
    /first|second|third|fourth|fifth|finally/i,
    /initially|subsequently|eventually|ultimately/i,
    /because|therefore|thus|consequently|as a result/i,
    /however|nevertheless|despite|although|even though/i,
    /similarly|likewise|in contrast|on the other hand/i,
    /for example|for instance|specifically|in particular/i,
    /in conclusion|to summarize|in summary|to conclude/i
  ];

  const markerMatches = logicalFlowMarkers.reduce((count, marker) => {
    return count + (marker.test(content) ? 1 : 0);
  }, 0);

  // Calculate score based on marker presence
  const markerScore = Math.min(markerMatches / 5, 1);

  // Check for heading structure
  const headingMatches = content.match(/#+\s+|<h[1-6]>/g);
  const hasHeadings = headingMatches && headingMatches.length > 0;

  // Calculate final score
  return hasHeadings ? 0.6 + markerScore * 0.4 : markerScore * 0.8;
}

/**
 * Calculate transition quality score
 */
function calculateTransitionQualityScore(content: string, result: ContentQualityMetricsResult): number {
  // This is a simplified implementation
  // In a real implementation, this would use NLP to analyze transition quality

  // For now, we'll use a simple heuristic based on transition words ratio
  const transitionWordsRatio = result.content.sentenceCount > 0
    ? result.content.transitionWordsCount / result.content.sentenceCount
    : 0;

  // Calculate score based on transition words ratio
  if (transitionWordsRatio >= 0.3) {
    return 0.9; // Excellent transition quality
  } else if (transitionWordsRatio >= 0.2) {
    return 0.8; // Very good transition quality
  } else if (transitionWordsRatio >= 0.1) {
    return 0.7; // Good transition quality
  } else if (transitionWordsRatio >= 0.05) {
    return 0.6; // Acceptable transition quality
  } else {
    return 0.5; // Poor transition quality
  }
}

/**
 * Calculate SEO metrics
 */
function calculateSeoMetrics(
  content: string,
  targetKeywords: string[] = [],
  result: ContentQualityMetricsResult
): void {
  // Skip if no target keywords
  if (targetKeywords.length === 0) {
    result.seo.seoScore = 0.5;
    return;
  }

  // Calculate keyword density
  calculateKeywordDensity(content, targetKeywords, result);

  // Check for keyword in title
  result.seo.keywordInTitle = checkKeywordInTitle(content, targetKeywords);

  // Check for keyword in headings
  result.seo.keywordInHeadingsCount = countKeywordInHeadings(content, targetKeywords);

  // Check for keyword in first paragraph
  result.seo.keywordInFirstParagraph = checkKeywordInFirstParagraph(content, targetKeywords);

  // Check for keyword in last paragraph
  result.seo.keywordInLastParagraph = checkKeywordInLastParagraph(content, targetKeywords);

  // Calculate SEO score
  calculateSeoScore(result);
}

/**
 * Calculate keyword density
 */
function calculateKeywordDensity(
  content: string,
  targetKeywords: string[],
  result: ContentQualityMetricsResult
): void {
  // Calculate word count
  const words = content.split(/\s+/).filter(word => word.length > 0);
  const wordCount = words.length;

  if (wordCount === 0) {
    return;
  }

  // Calculate keyword density for each target keyword
  const contentLower = content.toLowerCase();

  targetKeywords.forEach(keyword => {
    const keywordLower = keyword.toLowerCase();
    const regex = new RegExp(`\\b${keywordLower}\\b`, 'g');
    const matches = contentLower.match(regex) || [];
    const density = (matches.length / wordCount) * 100;
    result.seo.keywordDensity[keyword] = parseFloat(density.toFixed(2));
  });
}

/**
 * Check for keyword in title
 */
function checkKeywordInTitle(content: string, targetKeywords: string[]): boolean {
  // Extract title (first heading)
  const titleMatch = content.match(/^#\s+(.*)$|^<h1[^>]*>(.*?)<\/h1>/im);

  if (!titleMatch) {
    return false;
  }

  const title = (titleMatch[1] || titleMatch[2] || '').toLowerCase();

  // Check if any target keyword is in the title
  return targetKeywords.some(keyword =>
    title.includes(keyword.toLowerCase())
  );
}

/**
 * Count keyword in headings
 */
function countKeywordInHeadings(content: string, targetKeywords: string[]): number {
  // Extract all headings
  const headingMatches = content.match(/^#+\s+(.*)$|<h[1-6][^>]*>(.*?)<\/h[1-6]>/gim);

  if (!headingMatches) {
    return 0;
  }

  // Count headings with target keywords
  return headingMatches.filter(heading => {
    const headingText = heading.toLowerCase();
    return targetKeywords.some(keyword =>
      headingText.includes(keyword.toLowerCase())
    );
  }).length;
}

/**
 * Check for keyword in first paragraph
 */
function checkKeywordInFirstParagraph(content: string, targetKeywords: string[]): boolean {
  // Extract first paragraph
  const paragraphs = content.split(/\n\s*\n/).filter(paragraph => paragraph.length > 0);

  if (paragraphs.length === 0) {
    return false;
  }

  const firstParagraph = paragraphs[0].toLowerCase();

  // Check if any target keyword is in the first paragraph
  return targetKeywords.some(keyword =>
    firstParagraph.includes(keyword.toLowerCase())
  );
}

/**
 * Check for keyword in last paragraph
 */
function checkKeywordInLastParagraph(content: string, targetKeywords: string[]): boolean {
  // Extract last paragraph
  const paragraphs = content.split(/\n\s*\n/).filter(paragraph => paragraph.length > 0);

  if (paragraphs.length === 0) {
    return false;
  }

  const lastParagraph = paragraphs[paragraphs.length - 1].toLowerCase();

  // Check if any target keyword is in the last paragraph
  return targetKeywords.some(keyword =>
    lastParagraph.includes(keyword.toLowerCase())
  );
}

/**
 * Calculate SEO score
 */
function calculateSeoScore(result: ContentQualityMetricsResult): void {
  let score = 0.5; // Default score

  // Assess keyword density
  let keywordsWithGoodDensity = 0;
  let totalKeywords = Object.keys(result.seo.keywordDensity).length;

  for (const density of Object.values(result.seo.keywordDensity)) {
    if (density >= 0.5 && density <= 2.5) {
      keywordsWithGoodDensity++;
    }
  }

  const keywordDensityScore = totalKeywords > 0
    ? keywordsWithGoodDensity / totalKeywords
    : 0;

  if (keywordDensityScore >= 0.7) {
    score += 0.1;
  } else if (keywordDensityScore >= 0.3) {
    score += 0.05;
  } else {
    score -= 0.1;
  }

  // Assess keyword in title
  if (result.seo.keywordInTitle) {
    score += 0.1;
  } else {
    score -= 0.1;
  }

  // Assess keyword in headings
  if (result.seo.keywordInHeadingsCount >= 2) {
    score += 0.1;
  } else if (result.seo.keywordInHeadingsCount === 1) {
    score += 0.05;
  } else {
    score -= 0.05;
  }

  // Assess keyword in first paragraph
  if (result.seo.keywordInFirstParagraph) {
    score += 0.1;
  } else {
    score -= 0.05;
  }

  // Assess keyword in last paragraph
  if (result.seo.keywordInLastParagraph) {
    score += 0.05;
  }

  // Assess content length for SEO
  if (result.content.wordCount >= 1500) {
    score += 0.1;
  } else if (result.content.wordCount >= 800) {
    score += 0.05;
  } else if (result.content.wordCount < 500) {
    score -= 0.1;
  }

  // Ensure score is in valid range
  result.seo.seoScore = Math.max(0, Math.min(1, score));
}

/**
 * Generate improvement suggestions
 */
function generateImprovementSuggestions(result: ContentQualityMetricsResult): void {
  // Generate readability suggestions
  if (result.readability.fleschKincaidScore < 50) {
    result.suggestions.push('Simplify language to improve readability (aim for grade 7-8 level)');
  }

  if (result.content.avgWordsPerSentence > 25) {
    result.suggestions.push('Break up long sentences to improve readability');
  }

  // Generate structure suggestions
  if (result.structure.headingCount === 0) {
    result.suggestions.push('Add headings to organize content and improve structure');
  } else if (result.structure.headingCount < 3 && result.content.wordCount > 500) {
    result.suggestions.push('Add more headings to better organize content sections');
  }

  if (result.structure.listCount === 0 && result.content.wordCount > 500) {
    result.suggestions.push('Consider using bullet points or numbered lists to organize information');
  }

  if (result.structure.imageCount === 0 && result.content.wordCount > 800) {
    result.suggestions.push('Add images to enhance visual appeal and engagement');
  }

  // Generate engagement suggestions
  if (result.engagement.questionCount === 0) {
    result.suggestions.push('Add 2-3 thoughtful questions to engage readers');
  }

  if (result.engagement.personalPronounCount === 0) {
    result.suggestions.push('Add personal pronouns (you, your) to connect with readers');
  }

  if (result.engagement.callToActionCount === 0) {
    result.suggestions.push('Add clear calls to action to guide readers');
  }

  // Generate SEO suggestions
  const keywordsWithLowDensity = Object.entries(result.seo.keywordDensity)
    .filter(([_, density]) => density < 0.5)
    .map(([keyword, _]) => keyword);

  if (keywordsWithLowDensity.length > 0) {
    result.suggestions.push(`Increase usage of keywords with low density: ${keywordsWithLowDensity.join(', ')}`);
  }

  const keywordsWithHighDensity = Object.entries(result.seo.keywordDensity)
    .filter(([_, density]) => density > 2.5)
    .map(([keyword, _]) => keyword);

  if (keywordsWithHighDensity.length > 0) {
    result.suggestions.push(`Reduce keyword density for overused keywords: ${keywordsWithHighDensity.join(', ')}`);
  }

  if (!result.seo.keywordInTitle) {
    result.suggestions.push('Include a primary keyword in the title');
  }

  if (!result.seo.keywordInFirstParagraph) {
    result.suggestions.push('Include a primary keyword in the first paragraph');
  }

  // Generate content suggestions
  if (result.content.wordCount < 500) {
    result.suggestions.push('Expand content to at least 800 words for better depth and SEO performance');
  }

  if (result.content.passiveVoicePercentage > 20) {
    result.suggestions.push('Reduce passive voice usage to less than 10% of sentences');
  }

  if (result.content.clicheCount > 3) {
    result.suggestions.push('Replace clichés with more original phrasing');
  }

  // Limit to top 10 suggestions
  if (result.suggestions.length > 10) {
    result.suggestions = result.suggestions.slice(0, 10);
  }
}
