/**
 * SEO Optimization Module
 *
 * This module provides advanced SEO optimization strategies for content,
 * implementing semantic SEO analysis, competitor content analysis,
 * SERP feature optimization, and structured data recommendations.
 *
 * Features:
 * - Semantic SEO analysis
 * - Competitor content analysis
 * - SERP feature optimization
 * - Structured data recommendations
 * - Advanced keyword optimization
 */

import { IterativeArtifact } from '../types';

/**
 * SEO optimization result interface
 */
export interface SeoOptimizationResult {
  /** Overall SEO score (0-1) */
  overallScore: number;
  /** On-page SEO analysis */
  onPageSeo: {
    /** Title tag optimization */
    titleTag: {
      /** Original title */
      original: string;
      /** Optimized title */
      optimized: string;
      /** Title score (0-1) */
      score: number;
      /** Improvement suggestions */
      suggestions: string[];
    };
    /** Meta description optimization */
    metaDescription: {
      /** Original meta description */
      original: string;
      /** Optimized meta description */
      optimized: string;
      /** Meta description score (0-1) */
      score: number;
      /** Improvement suggestions */
      suggestions: string[];
    };
    /** Heading optimization */
    headings: {
      /** Original headings */
      original: string[];
      /** Optimized headings */
      optimized: string[];
      /** Headings score (0-1) */
      score: number;
      /** Improvement suggestions */
      suggestions: string[];
    };
    /** Content optimization */
    content: {
      /** Keyword density */
      keywordDensity: Record<string, number>;
      /** Content score (0-1) */
      score: number;
      /** Improvement suggestions */
      suggestions: string[];
    };
    /** Internal linking */
    internalLinking: {
      /** Suggested internal links */
      suggestedLinks: Array<{
        /** Anchor text */
        anchorText: string;
        /** Target URL */
        targetUrl: string;
        /** Relevance score (0-1) */
        relevance: number;
      }>;
      /** Internal linking score (0-1) */
      score: number;
      /** Improvement suggestions */
      suggestions: string[];
    };
  };
  /** Semantic SEO analysis */
  semanticSeo: {
    /** Topic clusters */
    topicClusters: string[];
    /** Related entities */
    relatedEntities: string[];
    /** Semantic score (0-1) */
    score: number;
    /** Improvement suggestions */
    suggestions: string[];
  };
  /** SERP feature optimization */
  serpFeatures: {
    /** Featured snippet potential */
    featuredSnippetPotential: number;
    /** FAQ schema potential */
    faqSchemaPotential: number;
    /** How-to schema potential */
    howToSchemaPotential: number;
    /** SERP features score (0-1) */
    score: number;
    /** Improvement suggestions */
    suggestions: string[];
  };
  /** Structured data recommendations */
  structuredData: {
    /** Recommended schema types */
    recommendedSchemas: string[];
    /** Schema examples */
    schemaExamples: Record<string, any>;
    /** Structured data score (0-1) */
    score: number;
    /** Improvement suggestions */
    suggestions: string[];
  };
  /** Overall improvement suggestions */
  suggestions: string[];
}

/**
 * Optimize content for SEO
 *
 * @param artifact - The artifact to optimize
 * @param targetKeywords - Target keywords for SEO optimization
 * @param competitorUrls - Competitor URLs for analysis (optional)
 * @returns SEO optimization result
 */
export function optimizeForSeo(
  artifact: IterativeArtifact,
  targetKeywords: string[],
  competitorUrls: string[] = []
): SeoOptimizationResult {
  // Extract content as string
  const content = extractContentAsString(artifact);

  // Initialize result
  const result: SeoOptimizationResult = {
    overallScore: 0,
    onPageSeo: {
      titleTag: {
        original: '',
        optimized: '',
        score: 0,
        suggestions: []
      },
      metaDescription: {
        original: '',
        optimized: '',
        score: 0,
        suggestions: []
      },
      headings: {
        original: [],
        optimized: [],
        score: 0,
        suggestions: []
      },
      content: {
        keywordDensity: {},
        score: 0,
        suggestions: []
      },
      internalLinking: {
        suggestedLinks: [],
        score: 0,
        suggestions: []
      }
    },
    semanticSeo: {
      topicClusters: [],
      relatedEntities: [],
      score: 0,
      suggestions: []
    },
    serpFeatures: {
      featuredSnippetPotential: 0,
      faqSchemaPotential: 0,
      howToSchemaPotential: 0,
      score: 0,
      suggestions: []
    },
    structuredData: {
      recommendedSchemas: [],
      schemaExamples: {},
      score: 0,
      suggestions: []
    },
    suggestions: []
  };

  try {
    // Skip empty content
    if (!content) {
      return result;
    }

    // Optimize on-page SEO
    optimizeOnPageSeo(content, targetKeywords, result);

    // Analyze semantic SEO
    analyzeSemanticSeo(content, targetKeywords, result);

    // Optimize for SERP features
    optimizeForSerpFeatures(content, targetKeywords, result);

    // Recommend structured data
    recommendStructuredData(content, targetKeywords, result);

    // Analyze competitor content if URLs provided
    if (competitorUrls.length > 0) {
      analyzeCompetitorContent(competitorUrls, targetKeywords, result);
    }

    // Generate overall improvement suggestions
    generateOverallSuggestions(result);

    // Calculate overall score (weighted average)
    const weights = {
      onPageSeo: 0.4,
      semanticSeo: 0.3,
      serpFeatures: 0.2,
      structuredData: 0.1
    };

    const onPageSeoScore = calculateOnPageSeoScore(result.onPageSeo);

    result.overallScore =
      onPageSeoScore * weights.onPageSeo +
      result.semanticSeo.score * weights.semanticSeo +
      result.serpFeatures.score * weights.serpFeatures +
      result.structuredData.score * weights.structuredData;

    return result;
  } catch (error) {
    console.error('Error optimizing for SEO:', error);
    return result;
  }
}

/**
 * Extract content as string from artifact
 */
function extractContentAsString(artifact: IterativeArtifact): string {
  if (!artifact || !artifact.content) {
    return '';
  }

  // Handle string content
  if (typeof artifact.content === 'string') {
    return artifact.content;
  }

  // Handle object content with text field
  if (typeof artifact.content === 'object' && artifact.content.text) {
    return typeof artifact.content.text === 'string' ? artifact.content.text : '';
  }

  // Handle object content with content field
  if (typeof artifact.content === 'object' && artifact.content.content) {
    return typeof artifact.content.content === 'string' ? artifact.content.content : '';
  }

  // Handle object content with sections
  if (typeof artifact.content === 'object' && Array.isArray(artifact.content.sections)) {
    return artifact.content.sections
      .map(section => {
        if (typeof section === 'string') {
          return section;
        } else if (typeof section === 'object') {
          return section.content || section.text || '';
        }
        return '';
      })
      .join('\n\n');
  }

  // Fallback: stringify the content
  try {
    return JSON.stringify(artifact.content);
  } catch (e) {
    return '';
  }
}

/**
 * Optimize on-page SEO
 */
function optimizeOnPageSeo(
  content: string,
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  // Optimize title tag
  optimizeTitleTag(content, targetKeywords, result);

  // Optimize meta description
  optimizeMetaDescription(content, targetKeywords, result);

  // Optimize headings
  optimizeHeadings(content, targetKeywords, result);

  // Optimize content
  optimizeContent(content, targetKeywords, result);

  // Suggest internal linking
  suggestInternalLinking(targetKeywords, result);
}

/**
 * Optimize title tag
 */
function optimizeTitleTag(
  content: string,
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  // Extract title (first heading)
  const titleMatch = content.match(/^#\s+(.*)$|^<h1[^>]*>(.*?)<\/h1>/im);

  if (titleMatch) {
    const originalTitle = titleMatch[1] || titleMatch[2] || '';
    result.onPageSeo.titleTag.original = originalTitle;

    // Check if title contains primary keyword
    const primaryKeyword = targetKeywords.length > 0 ? targetKeywords[0] : '';
    const containsPrimaryKeyword = primaryKeyword &&
      originalTitle.toLowerCase().includes(primaryKeyword.toLowerCase());

    // Check title length (optimal is 50-60 characters)
    const titleLength = originalTitle.length;
    const optimalTitleLength = titleLength >= 50 && titleLength <= 60;

    // Generate optimized title if needed
    if (!containsPrimaryKeyword || !optimalTitleLength) {
      let optimizedTitle = originalTitle;

      // Add primary keyword if missing
      if (!containsPrimaryKeyword && primaryKeyword) {
        if (titleLength < 40) {
          // Append keyword
          optimizedTitle = `${originalTitle} - ${primaryKeyword}`;
        } else {
          // Try to integrate keyword
          const words = originalTitle.split(' ');
          if (words.length > 4) {
            // Replace last few words with keyword
            words.splice(words.length - 2, 2, primaryKeyword);
            optimizedTitle = words.join(' ');
          } else {
            // Prepend keyword
            optimizedTitle = `${primaryKeyword}: ${originalTitle}`;
          }
        }
      }

      // Adjust title length if needed
      if (optimizedTitle.length > 60) {
        // Truncate to 57 chars and add ellipsis
        optimizedTitle = optimizedTitle.substring(0, 57) + '...';
      } else if (optimizedTitle.length < 50 && targetKeywords.length > 1) {
        // Add secondary keyword if title is too short
        const secondaryKeyword = targetKeywords[1];
        optimizedTitle = `${optimizedTitle} | ${secondaryKeyword}`;
      }

      result.onPageSeo.titleTag.optimized = optimizedTitle;
    } else {
      // Title is already optimized
      result.onPageSeo.titleTag.optimized = originalTitle;
    }

    // Calculate title score
    let titleScore = 0.5; // Default score

    if (containsPrimaryKeyword) {
      titleScore += 0.2;
    }

    if (optimalTitleLength) {
      titleScore += 0.2;
    } else if (titleLength > 0 && titleLength < 70) {
      titleScore += 0.1;
    }

    // Check if title is compelling
    if (/^(how|why|what|when|where|who|top|best|\d+)/i.test(originalTitle)) {
      titleScore += 0.1;
    }

    result.onPageSeo.titleTag.score = Math.min(titleScore, 1);

    // Generate suggestions
    if (!containsPrimaryKeyword) {
      result.onPageSeo.titleTag.suggestions.push(
        `Include primary keyword "${primaryKeyword}" in the title`
      );
    }

    if (!optimalTitleLength) {
      if (titleLength < 50) {
        result.onPageSeo.titleTag.suggestions.push(
          `Extend title to 50-60 characters (currently ${titleLength})`
        );
      } else if (titleLength > 60) {
        result.onPageSeo.titleTag.suggestions.push(
          `Shorten title to 50-60 characters (currently ${titleLength})`
        );
      }
    }

    if (!/^(how|why|what|when|where|who|top|best|\d+)/i.test(originalTitle)) {
      result.onPageSeo.titleTag.suggestions.push(
        'Consider using a compelling format (How, Why, What, Top X, etc.)'
      );
    }
  } else {
    // No title found
    result.onPageSeo.titleTag.original = '';

    // Generate a title based on primary keyword
    if (targetKeywords.length > 0) {
      const primaryKeyword = targetKeywords[0];
      const capitalizedKeyword = primaryKeyword.charAt(0).toUpperCase() + primaryKeyword.slice(1);

      // Generate title with primary keyword
      result.onPageSeo.titleTag.optimized = `${capitalizedKeyword}: Complete Guide and Best Practices`;

      // Add secondary keyword if available
      if (targetKeywords.length > 1) {
        const secondaryKeyword = targetKeywords[1];
        result.onPageSeo.titleTag.optimized = `${capitalizedKeyword}: Complete Guide to ${secondaryKeyword}`;
      }
    } else {
      result.onPageSeo.titleTag.optimized = 'Comprehensive Guide: Add a Descriptive Title';
    }

    result.onPageSeo.titleTag.score = 0.2;
    result.onPageSeo.titleTag.suggestions.push('Add a title (H1) to the content');
  }
}

/**
 * Optimize meta description
 */
function optimizeMetaDescription(
  content: string,
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  // Extract first paragraph as potential meta description
  const paragraphs = content.split(/\n\s*\n/).filter(paragraph => paragraph.length > 0);

  if (paragraphs.length > 0) {
    const firstParagraph = paragraphs[0].replace(/^#+\s+.*$|^<h[1-6][^>]*>.*<\/h[1-6]>/i, '').trim();

    if (firstParagraph.length > 0) {
      result.onPageSeo.metaDescription.original = firstParagraph;

      // Check if description contains primary keyword
      const primaryKeyword = targetKeywords.length > 0 ? targetKeywords[0] : '';
      const containsPrimaryKeyword = primaryKeyword &&
        firstParagraph.toLowerCase().includes(primaryKeyword.toLowerCase());

      // Check description length (optimal is 150-160 characters)
      const descriptionLength = firstParagraph.length;
      const optimalDescriptionLength = descriptionLength >= 150 && descriptionLength <= 160;

      // Generate optimized description if needed
      if (!containsPrimaryKeyword || !optimalDescriptionLength) {
        let optimizedDescription = firstParagraph;

        // Add primary keyword if missing
        if (!containsPrimaryKeyword && primaryKeyword) {
          if (descriptionLength < 130) {
            // Append keyword
            optimizedDescription = `${firstParagraph} Learn more about ${primaryKeyword}.`;
          } else {
            // Try to integrate keyword
            const words = firstParagraph.split(' ');
            if (words.length > 10) {
              // Replace some words with keyword
              words.splice(5, 2, primaryKeyword);
              optimizedDescription = words.join(' ');
            } else {
              // Prepend keyword
              optimizedDescription = `${primaryKeyword}: ${firstParagraph}`;
            }
          }
        }

        // Adjust description length if needed
        if (optimizedDescription.length > 160) {
          // Truncate to 157 chars and add ellipsis
          optimizedDescription = optimizedDescription.substring(0, 157) + '...';
        } else if (optimizedDescription.length < 150) {
          // Add call to action if description is too short
          optimizedDescription = `${optimizedDescription} Learn more about best practices and expert tips.`;

          // Truncate if still too long
          if (optimizedDescription.length > 160) {
            optimizedDescription = optimizedDescription.substring(0, 157) + '...';
          }
        }

        result.onPageSeo.metaDescription.optimized = optimizedDescription;
      } else {
        // Description is already optimized
        result.onPageSeo.metaDescription.optimized = firstParagraph;
      }

      // Calculate description score
      let descriptionScore = 0.5; // Default score

      if (containsPrimaryKeyword) {
        descriptionScore += 0.2;
      }

      if (optimalDescriptionLength) {
        descriptionScore += 0.2;
      } else if (descriptionLength > 0 && descriptionLength < 200) {
        descriptionScore += 0.1;
      }

      // Check if description has a call to action
      if (/learn|discover|find|get|read|download|try|start|begin|explore/i.test(firstParagraph)) {
        descriptionScore += 0.1;
      }

      result.onPageSeo.metaDescription.score = Math.min(descriptionScore, 1);

      // Generate suggestions
      if (!containsPrimaryKeyword) {
        result.onPageSeo.metaDescription.suggestions.push(
          `Include primary keyword "${primaryKeyword}" in the meta description`
        );
      }

      if (!optimalDescriptionLength) {
        if (descriptionLength < 150) {
          result.onPageSeo.metaDescription.suggestions.push(
            `Extend meta description to 150-160 characters (currently ${descriptionLength})`
          );
        } else if (descriptionLength > 160) {
          result.onPageSeo.metaDescription.suggestions.push(
            `Shorten meta description to 150-160 characters (currently ${descriptionLength})`
          );
        }
      }

      if (!/learn|discover|find|get|read|download|try|start|begin|explore/i.test(firstParagraph)) {
        result.onPageSeo.metaDescription.suggestions.push(
          'Add a call to action in the meta description'
        );
      }
    } else {
      // No valid first paragraph
      generateDefaultMetaDescription(targetKeywords, result);
    }
  } else {
    // No paragraphs found
    generateDefaultMetaDescription(targetKeywords, result);
  }
}

/**
 * Generate default meta description
 */
function generateDefaultMetaDescription(
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  result.onPageSeo.metaDescription.original = '';

  // Generate a description based on primary keyword
  if (targetKeywords.length > 0) {
    const primaryKeyword = targetKeywords[0];
    const capitalizedKeyword = primaryKeyword.charAt(0).toUpperCase() + primaryKeyword.slice(1);

    // Generate description with primary keyword
    result.onPageSeo.metaDescription.optimized =
      `Discover everything you need to know about ${primaryKeyword}. Our comprehensive guide covers best practices, expert tips, and practical advice. Learn more now.`;

    // Add secondary keyword if available
    if (targetKeywords.length > 1) {
      const secondaryKeyword = targetKeywords[1];
      result.onPageSeo.metaDescription.optimized =
        `Discover everything you need to know about ${primaryKeyword} and ${secondaryKeyword}. Our comprehensive guide covers best practices and expert tips. Learn more now.`;
    }

    // Ensure description is not too long
    if (result.onPageSeo.metaDescription.optimized.length > 160) {
      result.onPageSeo.metaDescription.optimized =
        result.onPageSeo.metaDescription.optimized.substring(0, 157) + '...';
    }
  } else {
    result.onPageSeo.metaDescription.optimized =
      'Discover valuable insights and practical advice in our comprehensive guide. Learn best practices, expert tips, and strategies to achieve better results. Read more now.';
  }

  result.onPageSeo.metaDescription.score = 0.3;
  result.onPageSeo.metaDescription.suggestions.push('Add a descriptive first paragraph that can serve as a meta description');
}

/**
 * Optimize headings
 */
function optimizeHeadings(
  content: string,
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  // Extract all headings
  const headingMatches = content.match(/^#+\s+(.*)$|<h[1-6][^>]*>(.*?)<\/h[1-6]>/gim);

  if (headingMatches) {
    // Clean up heading matches
    const headings = headingMatches.map(heading => {
      // Extract text from markdown or HTML heading
      const match = heading.match(/^#+\s+(.*)$|<h[1-6][^>]*>(.*?)<\/h[1-6]>/i);
      return match ? (match[1] || match[2] || '') : heading;
    });

    result.onPageSeo.headings.original = headings;

    // Check if headings contain keywords
    const primaryKeyword = targetKeywords.length > 0 ? targetKeywords[0] : '';
    const secondaryKeywords = targetKeywords.slice(1);

    const headingsWithPrimaryKeyword = headings.filter(heading =>
      heading.toLowerCase().includes(primaryKeyword.toLowerCase())
    ).length;

    const headingsWithSecondaryKeywords = headings.filter(heading =>
      secondaryKeywords.some(keyword =>
        heading.toLowerCase().includes(keyword.toLowerCase())
      )
    ).length;

    // Optimize headings if needed
    const optimizedHeadings = [...headings];
    let headingsOptimized = false;

    // Ensure primary keyword is in at least one heading
    if (headingsWithPrimaryKeyword === 0 && primaryKeyword && headings.length > 1) {
      // Add primary keyword to a suitable heading
      for (let i = 1; i < optimizedHeadings.length; i++) {
        if (!optimizedHeadings[i].toLowerCase().includes(primaryKeyword.toLowerCase())) {
          optimizedHeadings[i] = `${primaryKeyword}: ${optimizedHeadings[i]}`;
          headingsOptimized = true;
          break;
        }
      }
    }

    // Ensure secondary keywords are in headings
    if (headingsWithSecondaryKeywords < Math.min(secondaryKeywords.length, headings.length - 1)) {
      // Add secondary keywords to suitable headings
      let keywordIndex = 0;

      for (let i = 1; i < optimizedHeadings.length && keywordIndex < secondaryKeywords.length; i++) {
        const keyword = secondaryKeywords[keywordIndex];

        if (!optimizedHeadings[i].toLowerCase().includes(keyword.toLowerCase()) &&
            !optimizedHeadings[i].toLowerCase().includes(primaryKeyword.toLowerCase())) {
          // Check if heading is already optimized
          if (optimizedHeadings[i] === headings[i]) {
            optimizedHeadings[i] = `${keyword}: ${optimizedHeadings[i]}`;
            headingsOptimized = true;
            keywordIndex++;
          }
        }
      }
    }

    // Check heading hierarchy
    let hasHierarchyIssues = false;
    let previousLevel = 1;

    for (let i = 0; i < headingMatches.length; i++) {
      const match = headingMatches[i].match(/^(#+)\s+|<h([1-6])[^>]*>/i);

      if (match) {
        const level = match[1] ? match[1].length : parseInt(match[2]);

        // Check for skipped levels (e.g., H1 to H3)
        if (level > previousLevel + 1) {
          hasHierarchyIssues = true;
          break;
        }

        previousLevel = level;
      }
    }

    result.onPageSeo.headings.optimized = optimizedHeadings;

    // Calculate headings score
    let headingsScore = 0.5; // Default score

    if (headingsWithPrimaryKeyword > 0) {
      headingsScore += 0.1;
    }

    if (headingsWithSecondaryKeywords > 0) {
      headingsScore += 0.1;
    }

    if (headings.length >= 3) {
      headingsScore += 0.1;
    }

    if (!hasHierarchyIssues) {
      headingsScore += 0.1;
    }

    // Check if headings follow a logical structure
    if (headings.length >= 3 &&
        /^(how|why|what|when|where|who|top|best|\d+)/i.test(headings[0]) &&
        headings.some(heading => /conclusion|summary|final/i.test(heading))) {
      headingsScore += 0.1;
    }

    result.onPageSeo.headings.score = Math.min(headingsScore, 1);

    // Generate suggestions
    if (headingsWithPrimaryKeyword === 0) {
      result.onPageSeo.headings.suggestions.push(
        `Include primary keyword "${primaryKeyword}" in at least one heading`
      );
    }

    if (headingsWithSecondaryKeywords === 0 && secondaryKeywords.length > 0) {
      result.onPageSeo.headings.suggestions.push(
        'Include secondary keywords in subheadings'
      );
    }

    if (headings.length < 3) {
      result.onPageSeo.headings.suggestions.push(
        'Add more headings to structure content (aim for at least 3-5 headings)'
      );
    }

    if (hasHierarchyIssues) {
      result.onPageSeo.headings.suggestions.push(
        'Fix heading hierarchy (avoid skipping levels, e.g., H1 to H3)'
      );
    }

    if (headings.length >= 3 &&
        !(/^(how|why|what|when|where|who|top|best|\d+)/i.test(headings[0])) &&
        !headings.some(heading => /conclusion|summary|final/i.test(heading))) {
      result.onPageSeo.headings.suggestions.push(
        'Consider using a more structured approach with introduction and conclusion'
      );
    }
  } else {
    // No headings found
    result.onPageSeo.headings.original = [];

    // Generate default headings based on keywords
    if (targetKeywords.length > 0) {
      const primaryKeyword = targetKeywords[0];
      const capitalizedKeyword = primaryKeyword.charAt(0).toUpperCase() + primaryKeyword.slice(1);

      const defaultHeadings = [
        `Complete Guide to ${capitalizedKeyword}`,
        `What is ${capitalizedKeyword}?`,
        `Benefits of ${capitalizedKeyword}`,
        `How to Use ${capitalizedKeyword}`,
        `Best Practices for ${capitalizedKeyword}`,
        `${capitalizedKeyword} Tips and Tricks`,
        `Conclusion: Getting Started with ${capitalizedKeyword}`
      ];

      result.onPageSeo.headings.optimized = defaultHeadings;
    } else {
      result.onPageSeo.headings.optimized = [
        'Complete Guide',
        'Introduction',
        'Key Benefits',
        'How to Get Started',
        'Best Practices',
        'Tips and Tricks',
        'Conclusion'
      ];
    }

    result.onPageSeo.headings.score = 0.2;
    result.onPageSeo.headings.suggestions.push('Add headings to structure content');
  }
}

/**
 * Optimize content
 */
function optimizeContent(
  content: string,
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  // Calculate keyword density
  calculateKeywordDensity(content, targetKeywords, result);

  // Calculate content score
  calculateContentScore(content, targetKeywords, result);

  // Generate content suggestions
  generateContentSuggestions(content, targetKeywords, result);
}

/**
 * Calculate keyword density
 */
function calculateKeywordDensity(
  content: string,
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  // Calculate word count
  const words = content.split(/\s+/).filter(word => word.length > 0);
  const wordCount = words.length;

  if (wordCount === 0) {
    return;
  }

  // Calculate keyword density for each target keyword
  const contentLower = content.toLowerCase();

  targetKeywords.forEach(keyword => {
    const keywordLower = keyword.toLowerCase();
    const regex = new RegExp(`\\b${keywordLower}\\b`, 'g');
    const matches = contentLower.match(regex) || [];
    const density = (matches.length / wordCount) * 100;
    result.onPageSeo.content.keywordDensity[keyword] = parseFloat(density.toFixed(2));
  });
}

/**
 * Calculate content score
 */
function calculateContentScore(
  content: string,
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  let score = 0.5; // Default score

  // Assess keyword density
  let keywordsWithGoodDensity = 0;
  let totalKeywords = Object.keys(result.onPageSeo.content.keywordDensity).length;

  for (const density of Object.values(result.onPageSeo.content.keywordDensity)) {
    if (density >= 0.5 && density <= 2.5) {
      keywordsWithGoodDensity++;
    }
  }

  const keywordDensityScore = totalKeywords > 0
    ? keywordsWithGoodDensity / totalKeywords
    : 0;

  if (keywordDensityScore >= 0.7) {
    score += 0.1;
  } else if (keywordDensityScore >= 0.3) {
    score += 0.05;
  } else {
    score -= 0.1;
  }

  // Assess content length
  const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;

  if (wordCount >= 1500) {
    score += 0.1;
  } else if (wordCount >= 800) {
    score += 0.05;
  } else if (wordCount < 500) {
    score -= 0.1;
  }

  // Assess paragraph structure
  const paragraphs = content.split(/\n\s*\n/).filter(paragraph => paragraph.length > 0);

  if (paragraphs.length >= 5) {
    score += 0.05;
  } else if (paragraphs.length < 3) {
    score -= 0.05;
  }

  // Assess image usage
  const imageCount = (content.match(/!\[.*?\]\(.*?\)/g) || []).length +
                    (content.match(/<img[^>]*>/gi) || []).length;

  if (imageCount >= 2) {
    score += 0.05;
  } else if (imageCount === 0 && wordCount > 500) {
    score -= 0.05;
  }

  // Assess list usage
  const listCount = (content.match(/^[\s]*[-*+][\s]+|^[\s]*\d+\.[\s]+/gm) || []).length +
                   (content.match(/<[uo]l>|<li>/gi) || []).length;

  if (listCount >= 5) {
    score += 0.05;
  }

  // Assess outbound links
  const linkCount = (content.match(/\[.*?\]\(.*?\)/g) || []).length +
                   (content.match(/<a[^>]*>/gi) || []).length;

  if (linkCount >= 3) {
    score += 0.05;
  } else if (linkCount === 0 && wordCount > 500) {
    score -= 0.05;
  }

  // Ensure score is in valid range
  result.onPageSeo.content.score = Math.max(0, Math.min(1, score));
}

/**
 * Generate content suggestions
 */
function generateContentSuggestions(
  content: string,
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  // Generate keyword density suggestions
  const keywordsWithLowDensity = Object.entries(result.onPageSeo.content.keywordDensity)
    .filter(([_, density]) => density < 0.5)
    .map(([keyword, _]) => keyword);

  if (keywordsWithLowDensity.length > 0) {
    result.onPageSeo.content.suggestions.push(
      `Increase usage of keywords with low density: ${keywordsWithLowDensity.join(', ')}`
    );
  }

  const keywordsWithHighDensity = Object.entries(result.onPageSeo.content.keywordDensity)
    .filter(([_, density]) => density > 2.5)
    .map(([keyword, _]) => keyword);

  if (keywordsWithHighDensity.length > 0) {
    result.onPageSeo.content.suggestions.push(
      `Reduce keyword density for overused keywords: ${keywordsWithHighDensity.join(', ')}`
    );
  }

  // Generate content length suggestions
  const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;

  if (wordCount < 500) {
    result.onPageSeo.content.suggestions.push(
      'Expand content to at least 800 words for better SEO performance'
    );
  } else if (wordCount < 800) {
    result.onPageSeo.content.suggestions.push(
      'Consider expanding content to 1000+ words for comprehensive coverage'
    );
  }

  // Generate image suggestions
  const imageCount = (content.match(/!\[.*?\]\(.*?\)/g) || []).length +
                    (content.match(/<img[^>]*>/gi) || []).length;

  if (imageCount === 0 && wordCount > 500) {
    result.onPageSeo.content.suggestions.push(
      'Add images to enhance visual appeal and engagement'
    );
  } else if (imageCount === 1 && wordCount > 800) {
    result.onPageSeo.content.suggestions.push(
      'Add more images (aim for one image per 300-500 words)'
    );
  }

  // Generate list suggestions
  const listCount = (content.match(/^[\s]*[-*+][\s]+|^[\s]*\d+\.[\s]+/gm) || []).length +
                   (content.match(/<[uo]l>|<li>/gi) || []).length;

  if (listCount === 0 && wordCount > 500) {
    result.onPageSeo.content.suggestions.push(
      'Add bullet points or numbered lists to organize information'
    );
  }

  // Generate link suggestions
  const linkCount = (content.match(/\[.*?\]\(.*?\)/g) || []).length +
                   (content.match(/<a[^>]*>/gi) || []).length;

  if (linkCount === 0 && wordCount > 500) {
    result.onPageSeo.content.suggestions.push(
      'Add outbound links to authoritative sources to improve credibility'
    );
  }

  // Generate LSI keyword suggestions
  if (targetKeywords.length > 0) {
    const primaryKeyword = targetKeywords[0];
    const lsiKeywords = generateLsiKeywords(primaryKeyword);

    if (lsiKeywords.length > 0) {
      result.onPageSeo.content.suggestions.push(
        `Consider using these LSI keywords: ${lsiKeywords.join(', ')}`
      );
    }
  }
}

/**
 * Generate LSI (Latent Semantic Indexing) keywords
 */
function generateLsiKeywords(keyword: string): string[] {
  // This is a simplified implementation
  // In a real implementation, this would use an API or database to get LSI keywords

  // Sample LSI keywords for common topics
  const lsiKeywordMap: Record<string, string[]> = {
    'seo': ['search engine optimization', 'search rankings', 'google algorithm', 'keyword research', 'backlinks'],
    'marketing': ['digital marketing', 'content strategy', 'social media', 'lead generation', 'conversion rate'],
    'content': ['content marketing', 'blog posts', 'articles', 'copywriting', 'content strategy'],
    'social media': ['facebook', 'instagram', 'twitter', 'linkedin', 'social strategy'],
    'ecommerce': ['online store', 'shopping cart', 'product pages', 'conversion rate', 'customer experience'],
    'website': ['web design', 'user experience', 'responsive design', 'site speed', 'mobile friendly']
  };

  // Check if we have predefined LSI keywords for this keyword
  const keywordLower = keyword.toLowerCase();

  for (const [topic, lsiKeywords] of Object.entries(lsiKeywordMap)) {
    if (keywordLower.includes(topic) || topic.includes(keywordLower)) {
      return lsiKeywords;
    }
  }

  // Generate generic LSI keywords based on the keyword
  return [
    `best ${keyword}`,
    `${keyword} guide`,
    `${keyword} tips`,
    `${keyword} examples`,
    `${keyword} strategies`
  ];
}

/**
 * Suggest internal linking
 */
function suggestInternalLinking(
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  // This is a simplified implementation
  // In a real implementation, this would analyze the site structure and suggest relevant internal links

  if (targetKeywords.length === 0) {
    result.onPageSeo.internalLinking.score = 0.5;
    result.onPageSeo.internalLinking.suggestions.push(
      'Define target keywords to generate internal linking suggestions'
    );
    return;
  }

  // Generate sample internal linking suggestions based on keywords
  const primaryKeyword = targetKeywords[0];
  const secondaryKeywords = targetKeywords.slice(1);

  // Suggest links for primary keyword
  result.onPageSeo.internalLinking.suggestedLinks.push({
    anchorText: primaryKeyword,
    targetUrl: `/category/${primaryKeyword.toLowerCase().replace(/\s+/g, '-')}`,
    relevance: 0.9
  });

  // Suggest links for secondary keywords
  secondaryKeywords.forEach(keyword => {
    result.onPageSeo.internalLinking.suggestedLinks.push({
      anchorText: keyword,
      targetUrl: `/category/${keyword.toLowerCase().replace(/\s+/g, '-')}`,
      relevance: 0.8
    });
  });

  // Suggest related content links
  const relatedTopics = generateRelatedTopics(primaryKeyword);

  relatedTopics.forEach((topic, index) => {
    result.onPageSeo.internalLinking.suggestedLinks.push({
      anchorText: topic,
      targetUrl: `/blog/${topic.toLowerCase().replace(/\s+/g, '-')}`,
      relevance: 0.7 - (index * 0.1)
    });
  });

  // Calculate internal linking score
  result.onPageSeo.internalLinking.score = 0.7;

  // Generate suggestions
  result.onPageSeo.internalLinking.suggestions.push(
    'Add internal links to related content to improve site structure'
  );

  result.onPageSeo.internalLinking.suggestions.push(
    'Use descriptive anchor text that includes target keywords'
  );

  result.onPageSeo.internalLinking.suggestions.push(
    'Link to high-authority pages within your site'
  );
}

/**
 * Generate related topics
 */
function generateRelatedTopics(keyword: string): string[] {
  // This is a simplified implementation
  // In a real implementation, this would use an API or database to get related topics

  // Sample related topics for common keywords
  const relatedTopicsMap: Record<string, string[]> = {
    'seo': ['Keyword Research', 'Link Building Strategies', 'On-Page SEO Techniques'],
    'marketing': ['Content Marketing', 'Email Marketing', 'Social Media Strategy'],
    'content': ['Content Creation', 'Content Distribution', 'Content Optimization'],
    'social media': ['Facebook Marketing', 'Instagram Strategy', 'LinkedIn for Business'],
    'ecommerce': ['Product Page Optimization', 'Checkout Process', 'Customer Retention'],
    'website': ['Website Speed Optimization', 'Mobile Responsiveness', 'User Experience Design']
  };

  // Check if we have predefined related topics for this keyword
  const keywordLower = keyword.toLowerCase();

  for (const [topic, relatedTopics] of Object.entries(relatedTopicsMap)) {
    if (keywordLower.includes(topic) || topic.includes(keywordLower)) {
      return relatedTopics;
    }
  }

  // Generate generic related topics based on the keyword
  return [
    `${keyword} Best Practices`,
    `${keyword} for Beginners`,
    `Advanced ${keyword} Techniques`
  ];
}

/**
 * Analyze semantic SEO
 */
function analyzeSemanticSeo(
  content: string,
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  // Generate topic clusters
  generateTopicClusters(content, targetKeywords, result);

  // Generate related entities
  generateRelatedEntities(content, targetKeywords, result);

  // Calculate semantic score
  calculateSemanticScore(content, targetKeywords, result);

  // Generate semantic suggestions
  generateSemanticSuggestions(content, targetKeywords, result);
}

/**
 * Generate topic clusters
 */
function generateTopicClusters(
  content: string,
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  // This is a simplified implementation
  // In a real implementation, this would use NLP to analyze topic clusters

  if (targetKeywords.length === 0) {
    result.semanticSeo.topicClusters = ['General Content'];
    return;
  }

  const primaryKeyword = targetKeywords[0];

  // Generate topic clusters based on primary keyword
  const topicClusters = [primaryKeyword];

  // Add related topics
  const relatedTopics = generateRelatedTopics(primaryKeyword);
  topicClusters.push(...relatedTopics);

  // Add secondary keywords as topics
  const secondaryKeywords = targetKeywords.slice(1);
  topicClusters.push(...secondaryKeywords);

  // Extract potential topics from content
  const paragraphs = content.split(/\n\s*\n/).filter(paragraph => paragraph.length > 0);
  const headingMatches = content.match(/^#+\s+(.*)$|<h[1-6][^>]*>(.*?)<\/h[1-6]>/gim);

  if (headingMatches) {
    const headings = headingMatches.map(heading => {
      const match = heading.match(/^#+\s+(.*)$|<h[1-6][^>]*>(.*?)<\/h[1-6]>/i);
      return match ? (match[1] || match[2] || '') : heading;
    });

    // Add headings as potential topics
    headings.forEach(heading => {
      if (heading.length > 10 && !topicClusters.includes(heading)) {
        topicClusters.push(heading);
      }
    });
  }

  // Limit to top 5 topic clusters
  result.semanticSeo.topicClusters = topicClusters.slice(0, 5);
}

/**
 * Generate related entities
 */
function generateRelatedEntities(
  content: string,
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  // This is a simplified implementation
  // In a real implementation, this would use NLP to extract entities

  if (targetKeywords.length === 0) {
    result.semanticSeo.relatedEntities = [];
    return;
  }

  const primaryKeyword = targetKeywords[0];

  // Generate related entities based on primary keyword
  const relatedEntities: string[] = [];

  // Sample related entities for common topics
  const relatedEntitiesMap: Record<string, string[]> = {
    'seo': ['Google', 'Bing', 'Search Console', 'Analytics', 'SERP'],
    'marketing': ['Social Media', 'Email Marketing', 'Content Marketing', 'PPC', 'ROI'],
    'content': ['Blog Posts', 'Articles', 'Whitepapers', 'Case Studies', 'Infographics'],
    'social media': ['Facebook', 'Instagram', 'Twitter', 'LinkedIn', 'TikTok'],
    'ecommerce': ['Amazon', 'Shopify', 'WooCommerce', 'Cart Abandonment', 'Conversion Rate'],
    'website': ['WordPress', 'Wix', 'Squarespace', 'HTML', 'CSS']
  };

  // Check if we have predefined related entities for this keyword
  const keywordLower = primaryKeyword.toLowerCase();

  for (const [topic, entities] of Object.entries(relatedEntitiesMap)) {
    if (keywordLower.includes(topic) || topic.includes(keywordLower)) {
      relatedEntities.push(...entities);
      break;
    }
  }

  // Extract potential entities from content
  const words = content.split(/\s+/);
  const capitalizedWords = words.filter(word =>
    word.length > 1 &&
    word[0] === word[0].toUpperCase() &&
    word[1] === word[1].toLowerCase()
  );

  // Count frequency of capitalized words
  const wordFrequency: Record<string, number> = {};

  capitalizedWords.forEach(word => {
    const cleanWord = word.replace(/[^\w]/g, '');
    if (cleanWord.length > 1) {
      wordFrequency[cleanWord] = (wordFrequency[cleanWord] || 0) + 1;
    }
  });

  // Add frequent capitalized words as entities
  Object.entries(wordFrequency)
    .filter(([_, frequency]) => frequency > 2)
    .sort(([_, a], [__, b]) => b - a)
    .slice(0, 5)
    .forEach(([word, _]) => {
      if (!relatedEntities.includes(word)) {
        relatedEntities.push(word);
      }
    });

  // Limit to top 10 related entities
  result.semanticSeo.relatedEntities = relatedEntities.slice(0, 10);
}

/**
 * Calculate semantic score
 */
function calculateSemanticScore(
  content: string,
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  let score = 0.5; // Default score

  // Assess topic clusters
  if (result.semanticSeo.topicClusters.length >= 3) {
    score += 0.1;
  }

  // Assess related entities
  if (result.semanticSeo.relatedEntities.length >= 5) {
    score += 0.1;
  }

  // Assess content depth
  const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;

  if (wordCount >= 1500) {
    score += 0.1;
  } else if (wordCount >= 800) {
    score += 0.05;
  }

  // Assess content structure
  const headingMatches = content.match(/^#+\s+(.*)$|<h[1-6][^>]*>(.*?)<\/h[1-6]>/gim);

  if (headingMatches && headingMatches.length >= 5) {
    score += 0.1;
  } else if (headingMatches && headingMatches.length >= 3) {
    score += 0.05;
  }

  // Assess LSI keyword usage
  if (targetKeywords.length > 0) {
    const primaryKeyword = targetKeywords[0];
    const lsiKeywords = generateLsiKeywords(primaryKeyword);

    let lsiKeywordCount = 0;
    const contentLower = content.toLowerCase();

    lsiKeywords.forEach(keyword => {
      if (contentLower.includes(keyword.toLowerCase())) {
        lsiKeywordCount++;
      }
    });

    if (lsiKeywordCount >= 3) {
      score += 0.1;
    } else if (lsiKeywordCount >= 1) {
      score += 0.05;
    }
  }

  // Ensure score is in valid range
  result.semanticSeo.score = Math.max(0, Math.min(1, score));
}

/**
 * Generate semantic suggestions
 */
function generateSemanticSuggestions(
  content: string,
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  // Generate topic cluster suggestions
  if (result.semanticSeo.topicClusters.length < 3) {
    result.semanticSeo.suggestions.push(
      'Expand content to cover more related topics for better topical authority'
    );
  }

  // Generate related entity suggestions
  if (result.semanticSeo.relatedEntities.length < 5) {
    result.semanticSeo.suggestions.push(
      'Include more relevant entities (people, places, organizations, concepts) related to your topic'
    );
  }

  // Generate LSI keyword suggestions
  if (targetKeywords.length > 0) {
    const primaryKeyword = targetKeywords[0];
    const lsiKeywords = generateLsiKeywords(primaryKeyword);

    let unusedLsiKeywords: string[] = [];
    const contentLower = content.toLowerCase();

    lsiKeywords.forEach(keyword => {
      if (!contentLower.includes(keyword.toLowerCase())) {
        unusedLsiKeywords.push(keyword);
      }
    });

    if (unusedLsiKeywords.length > 0) {
      result.semanticSeo.suggestions.push(
        `Consider using these semantically related terms: ${unusedLsiKeywords.join(', ')}`
      );
    }
  }

  // Generate content depth suggestions
  const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;

  if (wordCount < 800) {
    result.semanticSeo.suggestions.push(
      'Create more in-depth content (1000+ words) to better cover the topic'
    );
  }

  // Generate content structure suggestions
  const headingMatches = content.match(/^#+\s+(.*)$|<h[1-6][^>]*>(.*?)<\/h[1-6]>/gim);

  if (!headingMatches || headingMatches.length < 3) {
    result.semanticSeo.suggestions.push(
      'Add more headings to structure content and cover subtopics'
    );
  }

  // Generate E-A-T suggestions
  result.semanticSeo.suggestions.push(
    'Enhance E-A-T (Expertise, Authoritativeness, Trustworthiness) by citing authoritative sources'
  );

  // Generate semantic markup suggestions
  result.semanticSeo.suggestions.push(
    'Consider adding semantic HTML5 elements (article, section, nav, etc.) to improve content structure'
  );
}

/**
 * Optimize for SERP features
 */
function optimizeForSerpFeatures(
  content: string,
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  // Assess featured snippet potential
  assessFeaturedSnippetPotential(content, result);

  // Assess FAQ schema potential
  assessFaqSchemaPotential(content, result);

  // Assess How-To schema potential
  assessHowToSchemaPotential(content, result);

  // Calculate SERP features score
  calculateSerpFeaturesScore(result);

  // Generate SERP features suggestions
  generateSerpFeaturesSuggestions(content, targetKeywords, result);
}

/**
 * Assess featured snippet potential
 */
function assessFeaturedSnippetPotential(
  content: string,
  result: SeoOptimizationResult
): void {
  let potential = 0.5; // Default potential

  // Check for definition-style content
  const hasDefinition = /what is|definition of|means|refers to/i.test(content);

  if (hasDefinition) {
    potential += 0.1;
  }

  // Check for list content
  const listCount = (content.match(/^[\s]*[-*+][\s]+|^[\s]*\d+\.[\s]+/gm) || []).length;

  if (listCount >= 5) {
    potential += 0.2;
  } else if (listCount >= 3) {
    potential += 0.1;
  }

  // Check for table content
  const hasTable = /\|.*\|.*\n\|[-:|\s]+\|/g.test(content) || /<table[^>]*>/i.test(content);

  if (hasTable) {
    potential += 0.2;
  }

  // Check for step-by-step content
  const hasSteps = /steps?|guide|how to|process|procedure/i.test(content) &&
                  (/step \d|first|second|third|next|finally/i.test(content) || listCount > 0);

  if (hasSteps) {
    potential += 0.1;
  }

  // Check for paragraph with clear answer
  const paragraphs = content.split(/\n\s*\n/).filter(paragraph => paragraph.length > 0);
  const hasConciseParagraph = paragraphs.some(paragraph =>
    paragraph.length >= 40 && paragraph.length <= 300 &&
    /is|are|means|refers to|defined as/i.test(paragraph)
  );

  if (hasConciseParagraph) {
    potential += 0.1;
  }

  // Ensure potential is in valid range
  result.serpFeatures.featuredSnippetPotential = Math.max(0, Math.min(1, potential));
}

/**
 * Assess FAQ schema potential
 */
function assessFaqSchemaPotential(
  content: string,
  result: SeoOptimizationResult
): void {
  let potential = 0.3; // Default potential

  // Check for question-answer patterns
  const questionPatterns = [
    /\?[\s\n]+[A-Z]/g, // Question mark followed by capital letter
    /^Q:.*\n+A:/gim,   // Q: ... A: format
    /question.*\n+answer/gi, // Question... Answer format
    /^what\s|^how\s|^why\s|^when\s|^where\s|^who\s|^which\s/gim // Question words
  ];

  let questionCount = 0;

  questionPatterns.forEach(pattern => {
    const matches = content.match(pattern) || [];
    questionCount += matches.length;
  });

  if (questionCount >= 5) {
    potential = 0.9;
  } else if (questionCount >= 3) {
    potential = 0.7;
  } else if (questionCount >= 1) {
    potential = 0.5;
  }

  // Check for FAQ-like headings
  const headingMatches = content.match(/^#+\s+(.*)$|<h[1-6][^>]*>(.*?)<\/h[1-6]>/gim);

  if (headingMatches) {
    const headings = headingMatches.map(heading => {
      const match = heading.match(/^#+\s+(.*)$|<h[1-6][^>]*>(.*?)<\/h[1-6]>/i);
      return match ? (match[1] || match[2] || '') : heading;
    });

    const questionHeadings = headings.filter(heading =>
      /\?$/.test(heading) || // Ends with question mark
      /^what\s|^how\s|^why\s|^when\s|^where\s|^who\s|^which\s/i.test(heading) // Starts with question word
    );

    if (questionHeadings.length >= 3) {
      potential = Math.max(potential, 0.8);
    } else if (questionHeadings.length >= 1) {
      potential = Math.max(potential, 0.6);
    }
  }

  // Check for FAQ section
  if (/FAQ|Frequently Asked Questions|Common Questions/i.test(content)) {
    potential = Math.max(potential, 0.7);
  }

  // Ensure potential is in valid range
  result.serpFeatures.faqSchemaPotential = Math.max(0, Math.min(1, potential));
}

/**
 * Assess How-To schema potential
 */
function assessHowToSchemaPotential(
  content: string,
  result: SeoOptimizationResult
): void {
  let potential = 0.3; // Default potential

  // Check for How-To title
  const titleMatch = content.match(/^#\s+(.*)$|^<h1[^>]*>(.*?)<\/h1>/im);
  const hasHowToTitle = titleMatch && /how to|guide|tutorial|steps|process/i.test(titleMatch[0]);

  if (hasHowToTitle) {
    potential += 0.2;
  }

  // Check for step-by-step content
  const hasStepHeadings = /^#+\s+Step \d|^#+\s+\d+\./im.test(content);

  if (hasStepHeadings) {
    potential += 0.2;
  }

  // Check for numbered list
  const numberedListMatches = content.match(/^[\s]*\d+\.[\s]+/gm);
  const hasNumberedList = numberedListMatches && numberedListMatches.length >= 3;

  if (hasNumberedList) {
    potential += 0.2;
  }

  // Check for step keywords
  const hasStepKeywords = /steps?|first|second|third|next|finally|begin|start|complete|finish/i.test(content);

  if (hasStepKeywords) {
    potential += 0.1;
  }

  // Check for materials/tools section
  const hasMaterialsSection = /materials|tools|ingredients|supplies|what you('ll| will) need/i.test(content);

  if (hasMaterialsSection) {
    potential += 0.1;
  }

  // Check for time indicators
  const hasTimeIndicators = /minutes|hours|time required|duration|takes about/i.test(content);

  if (hasTimeIndicators) {
    potential += 0.1;
  }

  // Ensure potential is in valid range
  result.serpFeatures.howToSchemaPotential = Math.max(0, Math.min(1, potential));
}

/**
 * Calculate SERP features score
 */
function calculateSerpFeaturesScore(result: SeoOptimizationResult): void {
  // Calculate weighted average of potentials
  const weights = {
    featuredSnippet: 0.4,
    faqSchema: 0.3,
    howToSchema: 0.3
  };

  const score =
    result.serpFeatures.featuredSnippetPotential * weights.featuredSnippet +
    result.serpFeatures.faqSchemaPotential * weights.faqSchema +
    result.serpFeatures.howToSchemaPotential * weights.howToSchema;

  // Ensure score is in valid range
  result.serpFeatures.score = Math.max(0, Math.min(1, score));
}

/**
 * Generate SERP features suggestions
 */
function generateSerpFeaturesSuggestions(
  content: string,
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  // Generate featured snippet suggestions
  if (result.serpFeatures.featuredSnippetPotential < 0.7) {
    if (!/what is|definition of|means|refers to/i.test(content)) {
      result.serpFeatures.suggestions.push(
        'Include a clear definition paragraph that directly answers "what is [keyword]"'
      );
    }

    const listCount = (content.match(/^[\s]*[-*+][\s]+|^[\s]*\d+\.[\s]+/gm) || []).length;

    if (listCount < 3) {
      result.serpFeatures.suggestions.push(
        'Add a numbered or bulleted list summarizing key points or steps'
      );
    }

    const hasTable = /\|.*\|.*\n\|[-:|\s]+\|/g.test(content) || /<table[^>]*>/i.test(content);

    if (!hasTable) {
      result.serpFeatures.suggestions.push(
        'Consider adding a comparison table to increase featured snippet potential'
      );
    }
  }

  // Generate FAQ schema suggestions
  if (result.serpFeatures.faqSchemaPotential < 0.7) {
    result.serpFeatures.suggestions.push(
      'Add an FAQ section with at least 3-5 common questions and answers'
    );

    result.serpFeatures.suggestions.push(
      'Format questions as headings (H2 or H3) followed by concise answers'
    );
  }

  // Generate How-To schema suggestions
  if (result.serpFeatures.howToSchemaPotential < 0.7) {
    const titleMatch = content.match(/^#\s+(.*)$|^<h1[^>]*>(.*?)<\/h1>/im);
    const hasHowToTitle = titleMatch && /how to|guide|tutorial|steps|process/i.test(titleMatch[0]);

    if (!hasHowToTitle && targetKeywords.length > 0) {
      const primaryKeyword = targetKeywords[0];
      result.serpFeatures.suggestions.push(
        `Consider reformatting content as a "How to ${primaryKeyword}" guide`
      );
    }

    result.serpFeatures.suggestions.push(
      'Structure content with clear, numbered steps (Step 1, Step 2, etc.)'
    );

    const hasMaterialsSection = /materials|tools|ingredients|supplies|what you('ll| will) need/i.test(content);

    if (!hasMaterialsSection) {
      result.serpFeatures.suggestions.push(
        'Add a "What You\'ll Need" or "Materials" section before the steps'
      );
    }

    const hasTimeIndicators = /minutes|hours|time required|duration|takes about/i.test(content);

    if (!hasTimeIndicators) {
      result.serpFeatures.suggestions.push(
        'Include time estimates for completing the process or individual steps'
      );
    }
  }

  // Generate general SERP feature suggestions
  result.serpFeatures.suggestions.push(
    'Implement proper schema markup for the most relevant SERP feature'
  );

  // Limit to top 5 suggestions
  if (result.serpFeatures.suggestions.length > 5) {
    result.serpFeatures.suggestions = result.serpFeatures.suggestions.slice(0, 5);
  }
}

/**
 * Recommend structured data
 */
function recommendStructuredData(
  content: string,
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  // Determine recommended schema types
  determineRecommendedSchemas(content, result);

  // Generate schema examples
  generateSchemaExamples(content, targetKeywords, result);

  // Calculate structured data score
  calculateStructuredDataScore(result);

  // Generate structured data suggestions
  generateStructuredDataSuggestions(result);
}

/**
 * Determine recommended schema types
 */
function determineRecommendedSchemas(
  content: string,
  result: SeoOptimizationResult
): void {
  const recommendedSchemas: string[] = [];

  // Always recommend Article schema
  recommendedSchemas.push('Article');

  // Recommend FAQ schema if potential is high
  if (result.serpFeatures.faqSchemaPotential >= 0.7) {
    recommendedSchemas.push('FAQPage');
  }

  // Recommend HowTo schema if potential is high
  if (result.serpFeatures.howToSchemaPotential >= 0.7) {
    recommendedSchemas.push('HowTo');
  }

  // Check for product-related content
  if (/product|price|review|rating|feature|specification/i.test(content)) {
    recommendedSchemas.push('Product');
  }

  // Check for event-related content
  if (/event|conference|webinar|workshop|seminar|meetup|date|time|location|venue/i.test(content)) {
    recommendedSchemas.push('Event');
  }

  // Check for recipe-related content
  if (/recipe|ingredient|instruction|cook|bake|preparation|serving|yield/i.test(content)) {
    recommendedSchemas.push('Recipe');
  }

  // Check for review-related content
  if (/review|rating|star|opinion|recommend|verdict/i.test(content)) {
    recommendedSchemas.push('Review');
  }

  // Check for course-related content
  if (/course|class|lesson|module|curriculum|learn|study|education|training/i.test(content)) {
    recommendedSchemas.push('Course');
  }

  // Set recommended schemas
  result.structuredData.recommendedSchemas = recommendedSchemas;
}

/**
 * Generate schema examples
 */
function generateSchemaExamples(
  content: string,
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  const schemaExamples: Record<string, any> = {};

  // Generate Article schema example
  if (result.structuredData.recommendedSchemas.includes('Article')) {
    const titleMatch = content.match(/^#\s+(.*)$|^<h1[^>]*>(.*?)<\/h1>/im);
    const title = titleMatch ? (titleMatch[1] || titleMatch[2] || 'Article Title') : 'Article Title';

    const paragraphs = content.split(/\n\s*\n/).filter(paragraph => paragraph.length > 0);
    const description = paragraphs.length > 0 ?
      (paragraphs[0].length > 150 ? paragraphs[0].substring(0, 147) + '...' : paragraphs[0]) :
      'Article description';

    schemaExamples['Article'] = {
      '@context': 'https://schema.org',
      '@type': 'Article',
      'headline': title,
      'description': description,
      'image': 'https://example.com/article-image.jpg',
      'datePublished': new Date().toISOString(),
      'dateModified': new Date().toISOString(),
      'author': {
        '@type': 'Person',
        'name': 'Author Name'
      },
      'publisher': {
        '@type': 'Organization',
        'name': 'Publisher Name',
        'logo': {
          '@type': 'ImageObject',
          'url': 'https://example.com/logo.png'
        }
      },
      'mainEntityOfPage': {
        '@type': 'WebPage',
        '@id': 'https://example.com/article-url'
      }
    };
  }

  // Generate FAQPage schema example
  if (result.structuredData.recommendedSchemas.includes('FAQPage')) {
    const headingMatches = content.match(/^#+\s+(.*)$|<h[1-6][^>]*>(.*?)<\/h[1-6]>/gim);
    const questions: Array<{question: string, answer: string}> = [];

    if (headingMatches) {
      const headings = headingMatches.map(heading => {
        const match = heading.match(/^#+\s+(.*)$|<h[1-6][^>]*>(.*?)<\/h[1-6]>/i);
        return match ? (match[1] || match[2] || '') : heading;
      });

      const questionHeadings = headings.filter(heading =>
        /\?$/.test(heading) || // Ends with question mark
        /^what\s|^how\s|^why\s|^when\s|^where\s|^who\s|^which\s/i.test(heading) // Starts with question word
      );

      // Take up to 3 question headings
      questionHeadings.slice(0, 3).forEach(question => {
        questions.push({
          question,
          answer: 'This is the answer to the question. Replace with actual content from your article.'
        });
      });
    }

    // If no question headings found, generate sample questions
    if (questions.length === 0 && targetKeywords.length > 0) {
      const primaryKeyword = targetKeywords[0];

      questions.push(
        {
          question: `What is ${primaryKeyword}?`,
          answer: 'This is a definition of the keyword. Replace with actual content from your article.'
        },
        {
          question: `Why is ${primaryKeyword} important?`,
          answer: 'This explains the importance of the keyword. Replace with actual content from your article.'
        },
        {
          question: `How to get started with ${primaryKeyword}?`,
          answer: 'This provides getting started information. Replace with actual content from your article.'
        }
      );
    }

    schemaExamples['FAQPage'] = {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      'mainEntity': questions.map(q => ({
        '@type': 'Question',
        'name': q.question,
        'acceptedAnswer': {
          '@type': 'Answer',
          'text': q.answer
        }
      }))
    };
  }

  // Generate HowTo schema example
  if (result.structuredData.recommendedSchemas.includes('HowTo')) {
    const titleMatch = content.match(/^#\s+(.*)$|^<h1[^>]*>(.*?)<\/h1>/im);
    const title = titleMatch ? (titleMatch[1] || titleMatch[2] || 'How-To Guide') : 'How-To Guide';

    const steps: string[] = [];

    // Extract steps from numbered lists
    const numberedListMatches = content.match(/^[\s]*\d+\.[\s]+(.*)/gm);

    if (numberedListMatches) {
      numberedListMatches.slice(0, 5).forEach(match => {
        const stepText = match.replace(/^[\s]*\d+\.[\s]+/, '').trim();
        if (stepText) {
          steps.push(stepText);
        }
      });
    }

    // If no steps found, generate sample steps
    if (steps.length === 0 && targetKeywords.length > 0) {
      const primaryKeyword = targetKeywords[0];

      steps.push(
        `Prepare for ${primaryKeyword}`,
        `Set up ${primaryKeyword}`,
        `Configure ${primaryKeyword}`,
        `Test ${primaryKeyword}`,
        `Finalize ${primaryKeyword}`
      );
    }

    schemaExamples['HowTo'] = {
      '@context': 'https://schema.org',
      '@type': 'HowTo',
      'name': title,
      'description': 'This is a step-by-step guide. Replace with actual content from your article.',
      'step': steps.map((step, index) => ({
        '@type': 'HowToStep',
        'position': index + 1,
        'name': `Step ${index + 1}`,
        'text': step
      }))
    };
  }

  // Set schema examples
  result.structuredData.schemaExamples = schemaExamples;
}

/**
 * Calculate structured data score
 */
function calculateStructuredDataScore(result: SeoOptimizationResult): void {
  let score = 0.5; // Default score

  // Assess number of recommended schemas
  if (result.structuredData.recommendedSchemas.length >= 3) {
    score += 0.2;
  } else if (result.structuredData.recommendedSchemas.length >= 2) {
    score += 0.1;
  }

  // Assess schema examples
  const schemaExampleCount = Object.keys(result.structuredData.schemaExamples).length;

  if (schemaExampleCount >= 2) {
    score += 0.2;
  } else if (schemaExampleCount >= 1) {
    score += 0.1;
  }

  // Assess specific schema types
  if (result.structuredData.recommendedSchemas.includes('Article')) {
    score += 0.1;
  }

  if (result.structuredData.recommendedSchemas.includes('FAQPage') ||
      result.structuredData.recommendedSchemas.includes('HowTo')) {
    score += 0.1;
  }

  // Ensure score is in valid range
  result.structuredData.score = Math.max(0, Math.min(1, score));
}

/**
 * Generate structured data suggestions
 */
function generateStructuredDataSuggestions(result: SeoOptimizationResult): void {
  // Generate general structured data suggestions
  result.structuredData.suggestions.push(
    'Implement structured data markup using JSON-LD format'
  );

  // Generate schema-specific suggestions
  if (result.structuredData.recommendedSchemas.includes('Article')) {
    result.structuredData.suggestions.push(
      'Add Article schema with accurate headline, description, and publication date'
    );
  }

  if (result.structuredData.recommendedSchemas.includes('FAQPage')) {
    result.structuredData.suggestions.push(
      'Implement FAQPage schema with at least 3-5 question-answer pairs'
    );
  }

  if (result.structuredData.recommendedSchemas.includes('HowTo')) {
    result.structuredData.suggestions.push(
      'Add HowTo schema with clear steps, tools/materials, and time estimates'
    );
  }

  if (result.structuredData.recommendedSchemas.includes('Product')) {
    result.structuredData.suggestions.push(
      'Implement Product schema with name, description, price, and availability'
    );
  }

  if (result.structuredData.recommendedSchemas.includes('Event')) {
    result.structuredData.suggestions.push(
      'Add Event schema with name, date, time, location, and description'
    );
  }

  // Generate testing suggestions
  result.structuredData.suggestions.push(
    'Test structured data implementation using Google\'s Rich Results Test tool'
  );

  // Limit to top 5 suggestions
  if (result.structuredData.suggestions.length > 5) {
    result.structuredData.suggestions = result.structuredData.suggestions.slice(0, 5);
  }
}

/**
 * Analyze competitor content
 */
function analyzeCompetitorContent(
  competitorUrls: string[],
  targetKeywords: string[],
  result: SeoOptimizationResult
): void {
  // This is a simplified implementation
  // In a real implementation, this would fetch and analyze competitor content

  // Add competitor analysis suggestion
  result.suggestions.push(
    'Analyze top-ranking competitor content for keyword usage, structure, and length'
  );
}

/**
 * Generate overall improvement suggestions
 */
function generateOverallSuggestions(result: SeoOptimizationResult): void {
  // Collect top suggestions from each category
  const allSuggestions: string[] = [];

  // Add on-page SEO suggestions
  if (result.onPageSeo.titleTag.suggestions.length > 0) {
    allSuggestions.push(result.onPageSeo.titleTag.suggestions[0]);
  }

  if (result.onPageSeo.metaDescription.suggestions.length > 0) {
    allSuggestions.push(result.onPageSeo.metaDescription.suggestions[0]);
  }

  if (result.onPageSeo.headings.suggestions.length > 0) {
    allSuggestions.push(result.onPageSeo.headings.suggestions[0]);
  }

  if (result.onPageSeo.content.suggestions.length > 0) {
    allSuggestions.push(result.onPageSeo.content.suggestions[0]);
  }

  // Add semantic SEO suggestions
  if (result.semanticSeo.suggestions.length > 0) {
    allSuggestions.push(result.semanticSeo.suggestions[0]);
  }

  // Add SERP features suggestions
  if (result.serpFeatures.suggestions.length > 0) {
    allSuggestions.push(result.serpFeatures.suggestions[0]);
  }

  // Add structured data suggestions
  if (result.structuredData.suggestions.length > 0) {
    allSuggestions.push(result.structuredData.suggestions[0]);
  }

  // Add general SEO suggestions
  allSuggestions.push(
    'Ensure content is comprehensive, well-structured, and addresses user intent'
  );

  allSuggestions.push(
    'Optimize page loading speed and mobile responsiveness for better user experience'
  );

  // Set overall suggestions (limit to top 10)
  result.suggestions = allSuggestions.slice(0, 10);
}

/**
 * Calculate on-page SEO score
 */
function calculateOnPageSeoScore(onPageSeo: SeoOptimizationResult['onPageSeo']): number {
  // Calculate weighted average of on-page SEO scores
  const weights = {
    titleTag: 0.25,
    metaDescription: 0.2,
    headings: 0.25,
    content: 0.2,
    internalLinking: 0.1
  };

  const score =
    onPageSeo.titleTag.score * weights.titleTag +
    onPageSeo.metaDescription.score * weights.metaDescription +
    onPageSeo.headings.score * weights.headings +
    onPageSeo.content.score * weights.content +
    onPageSeo.internalLinking.score * weights.internalLinking;

  // Ensure score is in valid range
  return Math.max(0, Math.min(1, score));
}