import { v4 as uuidv4 } from 'uuid';
import { 
  IterativeMessage, 
  IterativeArtifact, 
  Consultation,
  AgentState,
  IterativeCollaborationState
} from '../types';
import { AgentId, StandardizedHandlerResult } from './agentTypes';
import { stateStore } from './stateStore';

/**
 * Agent State Manager
 * Provides utilities for agents to interact with the collaboration state
 */
export class AgentStateManager {
  private agentId: AgentId;
  
  constructor(agentId: AgentId) {
    this.agentId = agentId;
  }

  /**
   * Get the current state for a session
   */
  async getSessionState(sessionId: string): Promise<IterativeCollaborationState | null> {
    return stateStore.getState(sessionId);
  }
  
  /**
   * Get agent-specific state from the session state
   */
  async getAgentState(sessionId: string): Promise<AgentState | null> {
    const state = await this.getSessionState(sessionId);
    
    if (!state) {
      return null;
    }
    
    return state.agentStates?.[this.agentId] || {
      id: this.agentId,
      processedRequests: [],
      generatedArtifacts: [],
      consultationsProvided: [],
      consultationsReceived: [],
      lastUpdated: new Date().toISOString()
    };
  }
  
  /**
   * Check if a message has been processed by this agent
   */
  async hasProcessedMessage(sessionId: string, messageId: string): Promise<boolean> {
    const agentState = await this.getAgentState(sessionId);
    
    if (!agentState) {
      return false;
    }
    
    return (agentState.processedRequests || []).includes(messageId);
  }
  
  /**
   * Track a message as processed by this agent
   */
  async trackProcessedMessage(sessionId: string, messageId: string): Promise<void> {
    await stateStore.updateState(sessionId, (state) => {
      const agentState = state.agentStates?.[this.agentId] || {
        id: this.agentId,
        processedRequests: [],
        generatedArtifacts: [],
        consultationsProvided: [],
        consultationsReceived: [],
        lastUpdated: new Date().toISOString()
      };
      
      // Skip if already processed
      if ((agentState.processedRequests || []).includes(messageId)) {
        return state;
      }
      
      // Update processed requests
      const processedRequests = [...(agentState.processedRequests || []), messageId];
      
      // Update state
      return {
        ...state,
        agentStates: {
          ...state.agentStates,
          [this.agentId]: {
            ...agentState,
            processedRequests,
            lastUpdated: new Date().toISOString()
          }
        }
      };
    });
  }
  
  /**
   * Add a new artifact to the collaboration state
   */
  async trackNewArtifact(sessionId: string, artifact: IterativeArtifact): Promise<void> {
    await stateStore.updateState(sessionId, (state) => {
      const agentState = state.agentStates?.[this.agentId] || {
        id: this.agentId,
        processedRequests: [],
        generatedArtifacts: [],
        consultationsProvided: [],
        consultationsReceived: [],
        lastUpdated: new Date().toISOString()
      };
      
      // Skip tracking if already tracked
      if ((agentState.generatedArtifacts || []).includes(artifact.id)) {
        return {
          ...state,
          artifacts: {
            ...state.artifacts,
            [artifact.id]: artifact
          }
        };
      }
      
      // Update generated artifacts
      const generatedArtifacts = [...(agentState.generatedArtifacts || []), artifact.id];
      
      // Update state
      return {
        ...state,
        artifacts: {
          ...state.artifacts,
          [artifact.id]: artifact
        },
        agentStates: {
          ...state.agentStates,
          [this.agentId]: {
            ...agentState,
            generatedArtifacts,
            lastUpdated: new Date().toISOString()
          }
        }
      };
    });
  }
  
  /**
   * Update an existing artifact in the collaboration state
   */
  async updateArtifact(sessionId: string, artifactId: string, updates: Partial<IterativeArtifact>): Promise<void> {
    await stateStore.updateState(sessionId, (state) => {
      const artifact = state.artifacts?.[artifactId];
      
      if (!artifact) {
        return state;
      }
      
      // Update the artifact
      return {
        ...state,
        artifacts: {
          ...state.artifacts,
          [artifactId]: {
            ...artifact,
            ...updates,
            // If this is our artifact, update the version
            ...(artifact.agent === this.agentId ? { version: (artifact.version || 1) + 1 } : {})
          }
        }
      };
    });
  }
  
  /**
   * Add a new consultation to the collaboration state
   */
  async trackNewConsultation(sessionId: string, consultation: Consultation): Promise<void> {
    await stateStore.updateState(sessionId, (state) => {
      const providerState = state.agentStates?.[this.agentId] || {
        id: this.agentId,
        processedRequests: [],
        generatedArtifacts: [],
        consultationsProvided: [],
        consultationsReceived: [],
        lastUpdated: new Date().toISOString()
      };
      
      const requesterState = state.agentStates?.[consultation.requester] || {
        id: consultation.requester,
        processedRequests: [],
        generatedArtifacts: [],
        consultationsProvided: [],
        consultationsReceived: [],
        lastUpdated: new Date().toISOString()
      };
      
      // Skip if already tracked
      if ((providerState.consultationsProvided || []).includes(consultation.id)) {
        return {
          ...state,
          consultations: {
            ...state.consultations,
            [consultation.id]: consultation
          }
        };
      }
      
      // Update consultations provided by provider
      const consultationsProvided = [...(providerState.consultationsProvided || []), consultation.id];
      
      // Update consultations received by requester
      const consultationsReceived = [...(requesterState.consultationsReceived || []), consultation.id];
      
      // Update state
      return {
        ...state,
        consultations: {
          ...state.consultations,
          [consultation.id]: consultation
        },
        agentStates: {
          ...state.agentStates,
          [this.agentId]: {
            ...providerState,
            consultationsProvided,
            lastUpdated: new Date().toISOString()
          },
          [consultation.requester]: {
            ...requesterState,
            consultationsReceived,
            lastUpdated: new Date().toISOString()
          }
        }
      };
    });
  }
  
  /**
   * Track feedback received from another agent
   */
  async trackFeedback(
    sessionId: string,
    fromAgent: AgentId,
    messageId: string,
    content: string,
    artifactId?: string
  ): Promise<void> {
    await stateStore.updateState(sessionId, (state) => {
      const agentState = state.agentStates?.[this.agentId] || {
        id: this.agentId,
        processedRequests: [],
        generatedArtifacts: [],
        consultationsProvided: [],
        consultationsReceived: [],
        lastUpdated: new Date().toISOString()
      };
      
      const feedback = agentState.feedback || {};
      
      const newFeedback = {
        ...feedback,
        [messageId]: {
          fromAgent,
          timestamp: new Date().toISOString(),
          content,
          artifactId,
          acknowledged: false
        }
      };
      
      // Update state
      return {
        ...state,
        agentStates: {
          ...state.agentStates,
          [this.agentId]: {
            ...agentState,
            feedback: newFeedback,
            lastUpdated: new Date().toISOString()
          }
        }
      };
    });
  }
  
  /**
   * Add a message to the conversation
   */
  async addMessage(sessionId: string, message: IterativeMessage): Promise<void> {
    await stateStore.updateState(sessionId, (state) => {
      return {
        ...state,
        messages: [...(state.messages || []), message]
      };
    });
  }
  
  /**
   * Apply a standardized result to the collaboration state
   */
  async applyResult(sessionId: string, result: StandardizedHandlerResult): Promise<void> {
    await stateStore.updateState(sessionId, (state) => {
      let updatedState = { ...state };
      
      // Apply state updates
      if (result.stateUpdates) {
        updatedState = {
          ...updatedState,
          ...result.stateUpdates
        };
      }
      
      // Apply artifact updates
      if (result.artifactUpdates) {
        // Apply new artifacts
        if (result.artifactUpdates.new) {
          updatedState = {
            ...updatedState,
            artifacts: {
              ...updatedState.artifacts,
              ...result.artifactUpdates.new
            }
          };
        }
        
        // Apply updated artifacts
        if (result.artifactUpdates.updated) {
          updatedState = {
            ...updatedState,
            artifacts: {
              ...updatedState.artifacts,
              ...result.artifactUpdates.updated
            }
          };
        }
      }
      
      // Apply consultation updates
      if (result.consultationUpdates?.new) {
        updatedState = {
          ...updatedState,
          consultations: {
            ...updatedState.consultations,
            ...result.consultationUpdates.new
          }
        };
      }
      
      // Add message to messages array
      updatedState = {
        ...updatedState,
        messages: [
          ...updatedState.messages,
          result.response
        ]
      };
      
      return updatedState;
    });
  }
}

/**
 * Create an agent state manager for a specific agent
 */
export function createAgentStateManager(agentId: AgentId): AgentStateManager {
  return new AgentStateManager(agentId);
}
