// src/app/(payload)/api/agents/collaborative-iteration/utils/artifactManager.ts

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeArtifact,
  ArtifactStatus,
  AgentId,
  Iteration,
  IterativeCollaborationState
} from '../types';
import { stateStore } from './stateStore';
import logger from './logger';

/**
 * ArtifactManager provides standardized methods for creating, storing, and retrieving artifacts
 * This ensures consistent artifact handling across all agents
 */
export class ArtifactManager {
  /**
   * Create a new artifact with standardized structure
   * @param name The name of the artifact
   * @param type The type of the artifact (e.g., 'market-research', 'seo-keywords')
   * @param content The content of the artifact
   * @param createdBy The agent that created the artifact
   * @param status The initial status of the artifact
   * @param metadata Additional metadata for the artifact
   * @returns The created artifact
   */
  static createArtifact(
    name: string,
    type: string,
    content: any,
    createdBy: AgentId,
    status: ArtifactStatus = ArtifactStatus.COMPLETED,
    metadata: Record<string, any> = {}
  ): IterativeArtifact {
    const timestamp = new Date().toISOString();
    const id = uuidv4();

    const artifact: IterativeArtifact = {
      id,
      name,
      type,
      createdBy,
      createdAt: timestamp,
      updatedAt: timestamp,
      currentVersion: 1,
      iterations: [
        {
          version: 1,
          timestamp,
          agent: createdBy,
          content,
          feedback: [],
          incorporatedConsultations: []
        }
      ],
      status,
      qualityScore: 0.8, // Default quality score
      content,
      metadata: {
        ...metadata,
        createdAt: timestamp,
        createdBy
      }
    };

    return artifact;
  }

  /**
   * Store an artifact in the session state
   * @param sessionId The session ID
   * @param artifact The artifact to store
   * @returns True if successful, false otherwise
   */
  static async storeArtifact(
    sessionId: string,
    artifact: IterativeArtifact
  ): Promise<boolean> {
    try {
      // Update the state with the new artifact
      await stateStore.updateState(sessionId, (state) => {
        if (!state) return state;

        // Initialize artifacts object if it doesn't exist
        if (!state.artifacts) {
          state.artifacts = {};
        }

        // Add the artifact to the artifacts object
        state.artifacts[artifact.id] = artifact;

        // Initialize generatedArtifacts array if it doesn't exist
        if (!state.generatedArtifacts) {
          state.generatedArtifacts = [];
        }

        // Add the artifact ID to the generatedArtifacts array if not already present
        if (!state.generatedArtifacts.includes(artifact.id)) {
          state.generatedArtifacts.push(artifact.id);
        }

        return state;
      });

      logger.info(`Stored artifact ${artifact.id} in session ${sessionId}`, {
        sessionId,
        artifactId: artifact.id,
        artifactType: artifact.type
      });

      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error storing artifact ${artifact.id} in session ${sessionId}`, {
        sessionId,
        artifactId: artifact.id,
        error: err.message,
        stack: err.stack
      });
      return false;
    }
  }

  /**
   * Get an artifact by ID
   * @param sessionId The session ID
   * @param artifactId The artifact ID
   * @returns The artifact, or null if not found
   */
  static async getArtifact(
    sessionId: string,
    artifactId: string
  ): Promise<IterativeArtifact | null> {
    try {
      const state = await stateStore.getState(sessionId);
      if (!state || !state.artifacts) return null;

      return state.artifacts[artifactId] || null;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error getting artifact ${artifactId} from session ${sessionId}`, {
        sessionId,
        artifactId,
        error: err.message,
        stack: err.stack
      });
      return null;
    }
  }

  /**
   * Get all artifacts of a specific type
   * @param sessionId The session ID
   * @param type The artifact type
   * @returns Array of artifacts of the specified type
   */
  static async getArtifactsByType(
    sessionId: string,
    type: string
  ): Promise<IterativeArtifact[]> {
    try {
      const state = await stateStore.getState(sessionId);
      if (!state || !state.artifacts) return [];

      return Object.values(state.artifacts).filter(
        (artifact) => artifact.type === type
      );
    } catch (error) {
      const err = error as Error;
      logger.error(`Error getting artifacts of type ${type} from session ${sessionId}`, {
        sessionId,
        artifactType: type,
        error: err.message,
        stack: err.stack
      });
      return [];
    }
  }

  /**
   * Update an existing artifact with a new iteration
   * @param sessionId The session ID
   * @param artifactId The artifact ID
   * @param content The new content
   * @param agent The agent making the update
   * @param changes Description of changes made
   * @param feedback Feedback that led to this iteration
   * @param incorporatedConsultations IDs of consultations incorporated
   * @returns The updated artifact, or null if not found
   */
  static async updateArtifact(
    sessionId: string,
    artifactId: string,
    content: any,
    agent: AgentId,
    changes: string = 'Updated content',
    feedback: string[] = [],
    incorporatedConsultations: string[] = []
  ): Promise<IterativeArtifact | null> {
    try {
      let updatedArtifact: IterativeArtifact | null = null;

      await stateStore.updateState(sessionId, (state) => {
        if (!state || !state.artifacts || !state.artifacts[artifactId]) return state;

        const artifact = state.artifacts[artifactId];
        const timestamp = new Date().toISOString();
        const newVersion = artifact.currentVersion + 1;

        // Create the new iteration
        const newIteration: Iteration = {
          version: newVersion,
          timestamp,
          agent,
          content,
          feedback,
          incorporatedConsultations,
          changes
        };

        // Update the artifact
        const updatedArtifact: IterativeArtifact = {
          ...artifact,
          updatedAt: timestamp,
          currentVersion: newVersion,
          iterations: [...artifact.iterations, newIteration],
          content // Update the current content
        };

        // Store the updated artifact
        state.artifacts[artifactId] = updatedArtifact;
        return state;
      });

      // Get the updated artifact
      updatedArtifact = await this.getArtifact(sessionId, artifactId);

      logger.info(`Updated artifact ${artifactId} in session ${sessionId}`, {
        sessionId,
        artifactId,
        newVersion: updatedArtifact?.currentVersion
      });

      return updatedArtifact;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error updating artifact ${artifactId} in session ${sessionId}`, {
        sessionId,
        artifactId,
        error: err.message,
        stack: err.stack
      });
      return null;
    }
  }

  /**
   * Update the status of an artifact
   * @param sessionId The session ID
   * @param artifactId The artifact ID
   * @param status The new status
   * @returns The updated artifact, or null if not found
   */
  static async updateArtifactStatus(
    sessionId: string,
    artifactId: string,
    status: ArtifactStatus
  ): Promise<IterativeArtifact | null> {
    try {
      let updatedArtifact: IterativeArtifact | null = null;

      await stateStore.updateState(sessionId, (state) => {
        if (!state || !state.artifacts || !state.artifacts[artifactId]) return state;

        // Update the artifact status
        state.artifacts[artifactId].status = status;
        state.artifacts[artifactId].updatedAt = new Date().toISOString();
        return state;
      });

      // Get the updated artifact
      updatedArtifact = await this.getArtifact(sessionId, artifactId);

      logger.info(`Updated artifact ${artifactId} status to ${status} in session ${sessionId}`, {
        sessionId,
        artifactId,
        status
      });

      return updatedArtifact;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error updating artifact ${artifactId} status in session ${sessionId}`, {
        sessionId,
        artifactId,
        status,
        error: err.message,
        stack: err.stack
      });
      return null;
    }
  }

  /**
   * Validate an artifact against type-specific requirements
   * @param artifact The artifact to validate
   * @returns Validation result with success flag and issues
   */
  static validateArtifact(
    artifact: IterativeArtifact
  ): { success: boolean; issues: string[] } {
    const issues: string[] = [];

    // Check basic required fields
    if (!artifact.id) issues.push('Missing artifact ID');
    if (!artifact.type) issues.push('Missing artifact type');
    if (!artifact.name) issues.push('Missing artifact name');
    if (!artifact.createdBy) issues.push('Missing creator information');
    if (!artifact.content) issues.push('Missing artifact content');

    // Type-specific validation
    switch (artifact.type) {
      case 'market-research':
        this.validateMarketResearchArtifact(artifact, issues);
        break;
      case 'seo-keywords':
        this.validateSeoKeywordsArtifact(artifact, issues);
        break;
      case 'content-strategy':
        this.validateContentStrategyArtifact(artifact, issues);
        break;
      case 'content-generation':
        this.validateContentGenerationArtifact(artifact, issues);
        break;
      case 'seo-optimization':
        this.validateSeoOptimizationArtifact(artifact, issues);
        break;
    }

    return {
      success: issues.length === 0,
      issues
    };
  }

  /**
   * Validate a market research artifact
   * @param artifact The artifact to validate
   * @param issues Array to add validation issues to
   */
  private static validateMarketResearchArtifact(
    artifact: IterativeArtifact,
    issues: string[]
  ): void {
    const content = artifact.content as any;
    
    // Check for required fields
    if (!content.audience) issues.push('Missing audience information');
    if (!content.trends) issues.push('Missing trends information');
    if (!content.competitors) issues.push('Missing competitors information');
    
    // Check for audience details
    if (content.audience && typeof content.audience === 'object') {
      if (!content.audience.demographics) issues.push('Missing audience demographics');
      if (!content.audience.needs || !Array.isArray(content.audience.needs) || content.audience.needs.length === 0) {
        issues.push('Missing or empty audience needs');
      }
      if (!content.audience.painPoints || !Array.isArray(content.audience.painPoints) || content.audience.painPoints.length === 0) {
        issues.push('Missing or empty audience pain points');
      }
    }
  }

  /**
   * Validate an SEO keywords artifact
   * @param artifact The artifact to validate
   * @param issues Array to add validation issues to
   */
  private static validateSeoKeywordsArtifact(
    artifact: IterativeArtifact,
    issues: string[]
  ): void {
    const content = artifact.content as any;
    
    // Check for required fields
    if (!content.primaryKeywords || !Array.isArray(content.primaryKeywords) || content.primaryKeywords.length === 0) {
      issues.push('Missing or empty primary keywords');
    }
    if (!content.longTailKeywords || !Array.isArray(content.longTailKeywords) || content.longTailKeywords.length === 0) {
      issues.push('Missing or empty long-tail keywords');
    }
    if (!content.searchIntent || typeof content.searchIntent !== 'object') {
      issues.push('Missing search intent information');
    }
  }

  /**
   * Validate a content strategy artifact
   * @param artifact The artifact to validate
   * @param issues Array to add validation issues to
   */
  private static validateContentStrategyArtifact(
    artifact: IterativeArtifact,
    issues: string[]
  ): void {
    const content = artifact.content as any;
    
    // Check for required fields
    if (!content.structure || typeof content.structure !== 'object') {
      issues.push('Missing content structure');
    }
    if (!content.sections || !Array.isArray(content.sections) || content.sections.length === 0) {
      issues.push('Missing or empty content sections');
    }
    if (!content.keypoints || !Array.isArray(content.keypoints) || content.keypoints.length === 0) {
      issues.push('Missing or empty key points');
    }
  }

  /**
   * Validate a content generation artifact
   * @param artifact The artifact to validate
   * @param issues Array to add validation issues to
   */
  private static validateContentGenerationArtifact(
    artifact: IterativeArtifact,
    issues: string[]
  ): void {
    const content = artifact.content as any;
    
    // Check for required fields
    if (!content.title) issues.push('Missing content title');
    if (!content.introduction) issues.push('Missing introduction');
    if (!content.bodySections || !Array.isArray(content.bodySections) || content.bodySections.length === 0) {
      issues.push('Missing or empty body sections');
    }
    if (!content.conclusion) issues.push('Missing conclusion');
  }

  /**
   * Validate an SEO optimization artifact
   * @param artifact The artifact to validate
   * @param issues Array to add validation issues to
   */
  private static validateSeoOptimizationArtifact(
    artifact: IterativeArtifact,
    issues: string[]
  ): void {
    const content = artifact.content as any;
    
    // Check for required fields
    if (!content.analysis || typeof content.analysis !== 'object') {
      issues.push('Missing SEO analysis');
    }
    if (!content.recommendations || !Array.isArray(content.recommendations) || content.recommendations.length === 0) {
      issues.push('Missing or empty SEO recommendations');
    }
    if (!content.metaTags || typeof content.metaTags !== 'object') {
      issues.push('Missing meta tags');
    }
  }
}

export default ArtifactManager;
