/**
 * Quality Assessment Module
 *
 * This module provides comprehensive quality assessment for content artifacts,
 * implementing multi-dimensional scoring, automated feedback generation,
 * and quality improvement suggestions.
 *
 * Features:
 * - Multi-dimensional quality scoring (content, structure, SEO, readability)
 * - Automated feedback generation based on quality assessment
 * - Quality improvement suggestions with actionable recommendations
 * - Comparative quality assessment against benchmarks
 */

import { v4 as uuidv4 } from 'uuid';
import { IterativeArtifact } from '../types';

/**
 * Quality assessment result interface
 */
export interface QualityAssessmentResult {
  /** Overall quality score (0-1) */
  overallScore: number;
  /** Whether the artifact meets quality standards */
  meetsStandards: boolean;
  /** Detailed scores for different dimensions */
  dimensions: {
    /** Content quality score (0-1) */
    content: number;
    /** Structure quality score (0-1) */
    structure: number;
    /** SEO quality score (0-1) */
    seo: number;
    /** Readability quality score (0-1) */
    readability: number;
    /** Engagement potential score (0-1) */
    engagement: number;
  };
  /** Strengths identified in the artifact */
  strengths: string[];
  /** Weaknesses identified in the artifact */
  weaknesses: string[];
  /** Improvement suggestions */
  suggestions: string[];
  /** Assessment timestamp */
  timestamp: string;
  /** Assessment ID */
  id: string;
}

/**
 * Content quality metrics interface
 */
export interface ContentQualityMetrics {
  /** Word count */
  wordCount: number;
  /** Sentence count */
  sentenceCount: number;
  /** Average words per sentence */
  avgWordsPerSentence: number;
  /** Paragraph count */
  paragraphCount: number;
  /** Heading count */
  headingCount: number;
  /** Flesch-Kincaid readability score (0-100) */
  fleschKincaidScore: number;
  /** Keyword density for primary keywords */
  keywordDensity: Record<string, number>;
  /** Passive voice percentage */
  passiveVoicePercentage: number;
  /** Cliché count */
  clicheCount: number;
  /** Jargon count */
  jargonCount: number;
  /** Transition words count */
  transitionWordsCount: number;
}

/**
 * Assess the quality of an artifact
 *
 * @param artifact - The artifact to assess
 * @param targetKeywords - Target keywords for SEO assessment
 * @param qualityThreshold - Minimum quality threshold (0-1)
 * @returns Quality assessment result
 */
export function assessArtifactQuality(
  artifact: IterativeArtifact,
  targetKeywords: string[] = [],
  qualityThreshold: number = 0.75
): QualityAssessmentResult {
  // Initialize result
  const result: QualityAssessmentResult = {
    overallScore: 0,
    meetsStandards: false,
    dimensions: {
      content: 0,
      structure: 0,
      seo: 0,
      readability: 0,
      engagement: 0
    },
    strengths: [],
    weaknesses: [],
    suggestions: [],
    timestamp: new Date().toISOString(),
    id: uuidv4()
  };

  try {
    // Extract content as string
    const content = extractContentAsString(artifact);

    // Calculate content quality metrics
    const metrics = calculateContentQualityMetrics(content, targetKeywords);

    // Assess content quality
    const contentQuality = assessContentQuality(metrics);
    result.dimensions.content = contentQuality.score;

    if (contentQuality.strengths.length > 0) {
      result.strengths.push(...contentQuality.strengths);
    }

    if (contentQuality.weaknesses.length > 0) {
      result.weaknesses.push(...contentQuality.weaknesses);
    }

    if (contentQuality.suggestions.length > 0) {
      result.suggestions.push(...contentQuality.suggestions);
    }

    // Assess structure quality
    const structureQuality = assessStructureQuality(metrics, artifact);
    result.dimensions.structure = structureQuality.score;

    if (structureQuality.strengths.length > 0) {
      result.strengths.push(...structureQuality.strengths);
    }

    if (structureQuality.weaknesses.length > 0) {
      result.weaknesses.push(...structureQuality.weaknesses);
    }

    if (structureQuality.suggestions.length > 0) {
      result.suggestions.push(...structureQuality.suggestions);
    }

    // Assess SEO quality
    const seoQuality = assessSeoQuality(metrics, targetKeywords);
    result.dimensions.seo = seoQuality.score;

    if (seoQuality.strengths.length > 0) {
      result.strengths.push(...seoQuality.strengths);
    }

    if (seoQuality.weaknesses.length > 0) {
      result.weaknesses.push(...seoQuality.weaknesses);
    }

    if (seoQuality.suggestions.length > 0) {
      result.suggestions.push(...seoQuality.suggestions);
    }

    // Assess readability quality
    const readabilityQuality = assessReadabilityQuality(metrics);
    result.dimensions.readability = readabilityQuality.score;

    if (readabilityQuality.strengths.length > 0) {
      result.strengths.push(...readabilityQuality.strengths);
    }

    if (readabilityQuality.weaknesses.length > 0) {
      result.weaknesses.push(...readabilityQuality.weaknesses);
    }

    if (readabilityQuality.suggestions.length > 0) {
      result.suggestions.push(...readabilityQuality.suggestions);
    }

    // Assess engagement potential
    const engagementQuality = assessEngagementQuality(metrics, artifact);
    result.dimensions.engagement = engagementQuality.score;

    if (engagementQuality.strengths.length > 0) {
      result.strengths.push(...engagementQuality.strengths);
    }

    if (engagementQuality.weaknesses.length > 0) {
      result.weaknesses.push(...engagementQuality.weaknesses);
    }

    if (engagementQuality.suggestions.length > 0) {
      result.suggestions.push(...engagementQuality.suggestions);
    }

    // Calculate overall score (weighted average)
    const weights = {
      content: 0.3,
      structure: 0.2,
      seo: 0.2,
      readability: 0.15,
      engagement: 0.15
    };

    result.overallScore =
      result.dimensions.content * weights.content +
      result.dimensions.structure * weights.structure +
      result.dimensions.seo * weights.seo +
      result.dimensions.readability * weights.readability +
      result.dimensions.engagement * weights.engagement;

    // Determine if artifact meets quality standards
    result.meetsStandards = result.overallScore >= qualityThreshold;

    return result;
  } catch (error) {
    console.error('Error assessing artifact quality:', error);

    // Return a basic assessment result in case of error
    return {
      overallScore: 0.5,
      meetsStandards: false,
      dimensions: {
        content: 0.5,
        structure: 0.5,
        seo: 0.5,
        readability: 0.5,
        engagement: 0.5
      },
      strengths: [],
      weaknesses: ['Error occurred during quality assessment'],
      suggestions: ['Retry quality assessment with valid artifact'],
      timestamp: new Date().toISOString(),
      id: uuidv4()
    };
  }
}

/**
 * Extract content as string from artifact
 */
function extractContentAsString(artifact: IterativeArtifact): string {
  if (!artifact || !artifact.content) {
    return '';
  }

  // Handle string content
  if (typeof artifact.content === 'string') {
    return artifact.content;
  }

  // Handle object content with text field
  if (typeof artifact.content === 'object' && artifact.content.text) {
    return typeof artifact.content.text === 'string' ? artifact.content.text : '';
  }

  // Handle object content with content field
  if (typeof artifact.content === 'object' && artifact.content.content) {
    return typeof artifact.content.content === 'string' ? artifact.content.content : '';
  }

  // Handle object content with sections
  if (typeof artifact.content === 'object' && Array.isArray(artifact.content.sections)) {
    return artifact.content.sections
      .map(section => {
        if (typeof section === 'string') {
          return section;
        } else if (typeof section === 'object') {
          return section.content || section.text || '';
        }
        return '';
      })
      .join('\n\n');
  }

  // Fallback: stringify the content
  try {
    return JSON.stringify(artifact.content);
  } catch (e) {
    return '';
  }
}

/**
 * Calculate content quality metrics
 */
function calculateContentQualityMetrics(
  content: string,
  targetKeywords: string[] = []
): ContentQualityMetrics {
  // Initialize metrics
  const metrics: ContentQualityMetrics = {
    wordCount: 0,
    sentenceCount: 0,
    avgWordsPerSentence: 0,
    paragraphCount: 0,
    headingCount: 0,
    fleschKincaidScore: 0,
    keywordDensity: {},
    passiveVoicePercentage: 0,
    clicheCount: 0,
    jargonCount: 0,
    transitionWordsCount: 0
  };

  // Skip empty content
  if (!content) {
    return metrics;
  }

  // Calculate word count
  const words = content.split(/\s+/).filter(word => word.length > 0);
  metrics.wordCount = words.length;

  // Calculate sentence count
  const sentences = content.split(/[.!?]+\s/).filter(sentence => sentence.length > 0);
  metrics.sentenceCount = sentences.length;

  // Calculate average words per sentence
  metrics.avgWordsPerSentence = metrics.sentenceCount > 0
    ? metrics.wordCount / metrics.sentenceCount
    : 0;

  // Calculate paragraph count
  const paragraphs = content.split(/\n\s*\n/).filter(paragraph => paragraph.length > 0);
  metrics.paragraphCount = paragraphs.length;

  // Calculate heading count
  const headingMatches = content.match(/#+\s+|<h[1-6]>/g);
  metrics.headingCount = headingMatches ? headingMatches.length : 0;

  // Calculate Flesch-Kincaid readability score
  metrics.fleschKincaidScore = calculateFleschKincaidScore(metrics.wordCount, metrics.sentenceCount);

  // Calculate keyword density
  if (targetKeywords.length > 0) {
    const contentLower = content.toLowerCase();
    const wordCount = metrics.wordCount;

    targetKeywords.forEach(keyword => {
      const keywordLower = keyword.toLowerCase();
      const regex = new RegExp(`\\b${keywordLower}\\b`, 'g');
      const matches = contentLower.match(regex) || [];
      const density = wordCount > 0 ? (matches.length / wordCount) * 100 : 0;
      metrics.keywordDensity[keyword] = parseFloat(density.toFixed(2));
    });
  }

  // Calculate passive voice percentage (simplified)
  const passiveVoiceMatches = content.match(/\b(?:is|are|was|were|be|been|being)\s+\w+ed\b/g);
  metrics.passiveVoicePercentage = metrics.sentenceCount > 0 && passiveVoiceMatches
    ? (passiveVoiceMatches.length / metrics.sentenceCount) * 100
    : 0;

  // Calculate cliché count (simplified)
  const cliches = [
    'at the end of the day',
    'think outside the box',
    'cutting edge',
    'win-win situation',
    'low hanging fruit',
    'paradigm shift',
    'push the envelope',
    'game changer',
    'back to the drawing board',
    'hit the ground running'
  ];

  metrics.clicheCount = cliches.reduce((count, cliche) => {
    const regex = new RegExp(cliche, 'gi');
    const matches = content.match(regex) || [];
    return count + matches.length;
  }, 0);

  // Calculate transition words count
  const transitionWords = [
    'however', 'therefore', 'consequently', 'furthermore', 'moreover',
    'nevertheless', 'nonetheless', 'meanwhile', 'subsequently', 'conversely',
    'similarly', 'likewise', 'in contrast', 'on the other hand', 'in conclusion'
  ];

  metrics.transitionWordsCount = transitionWords.reduce((count, word) => {
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    const matches = content.match(regex) || [];
    return count + matches.length;
  }, 0);

  return metrics;
}

/**
 * Calculate Flesch-Kincaid readability score
 * Formula: 206.835 - 1.015 * (words / sentences) - 84.6 * (syllables / words)
 *
 * This is a simplified implementation that estimates syllables
 */
function calculateFleschKincaidScore(wordCount: number, sentenceCount: number): number {
  if (wordCount === 0 || sentenceCount === 0) {
    return 0;
  }

  // Estimate average syllables per word (English average is around 1.5)
  const avgSyllablesPerWord = 1.5;

  // Calculate score
  const score = 206.835 - 1.015 * (wordCount / sentenceCount) - 84.6 * (avgSyllablesPerWord);

  // Clamp score to 0-100 range
  return Math.max(0, Math.min(100, score));
}

/**
 * Assess content quality
 */
function assessContentQuality(metrics: ContentQualityMetrics): {
  score: number;
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
} {
  const result = {
    score: 0.7, // Default score
    strengths: [] as string[],
    weaknesses: [] as string[],
    suggestions: [] as string[]
  };

  // Assess word count
  if (metrics.wordCount >= 1000) {
    result.score += 0.1;
    result.strengths.push('Content has good depth with substantial word count');
  } else if (metrics.wordCount >= 500) {
    result.score += 0.05;
    result.strengths.push('Content has adequate length');
  } else if (metrics.wordCount < 300) {
    result.score -= 0.1;
    result.weaknesses.push('Content is too short');
    result.suggestions.push('Expand content to at least 500 words for better depth');
  }

  // Assess paragraph structure
  if (metrics.paragraphCount >= 5) {
    result.score += 0.05;
    result.strengths.push('Content is well-structured with multiple paragraphs');
  } else if (metrics.paragraphCount < 3) {
    result.score -= 0.05;
    result.weaknesses.push('Content has too few paragraphs');
    result.suggestions.push('Break content into more paragraphs (3-5) for better readability');
  }

  // Assess sentence length
  if (metrics.avgWordsPerSentence >= 10 && metrics.avgWordsPerSentence <= 20) {
    result.score += 0.05;
    result.strengths.push('Sentence length is optimal for readability');
  } else if (metrics.avgWordsPerSentence > 25) {
    result.score -= 0.05;
    result.weaknesses.push('Sentences are too long on average');
    result.suggestions.push('Shorten sentences to improve readability (aim for 15-20 words per sentence)');
  } else if (metrics.avgWordsPerSentence < 8) {
    result.score -= 0.03;
    result.weaknesses.push('Sentences are very short on average');
    result.suggestions.push('Consider combining some short sentences for better flow');
  }

  // Assess transition words
  const transitionWordsRatio = metrics.sentenceCount > 0
    ? metrics.transitionWordsCount / metrics.sentenceCount
    : 0;

  if (transitionWordsRatio >= 0.2) {
    result.score += 0.05;
    result.strengths.push('Good use of transition words improves content flow');
  } else if (transitionWordsRatio < 0.1) {
    result.score -= 0.03;
    result.weaknesses.push('Limited use of transition words');
    result.suggestions.push('Add more transition words to improve content flow and coherence');
  }

  // Assess clichés
  if (metrics.clicheCount > 3) {
    result.score -= 0.05;
    result.weaknesses.push('Content contains multiple clichés');
    result.suggestions.push('Replace clichés with more original phrasing');
  }

  // Assess passive voice
  if (metrics.passiveVoicePercentage > 20) {
    result.score -= 0.05;
    result.weaknesses.push('Excessive use of passive voice');
    result.suggestions.push('Reduce passive voice usage to less than 10% of sentences');
  } else if (metrics.passiveVoicePercentage < 10) {
    result.score += 0.03;
    result.strengths.push('Appropriate use of active voice');
  }

  // Ensure score is in valid range
  result.score = Math.max(0, Math.min(1, result.score));

  return result;
}

/**
 * Assess structure quality
 */
function assessStructureQuality(
  metrics: ContentQualityMetrics,
  artifact: IterativeArtifact
): {
  score: number;
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
} {
  const result = {
    score: 0.7, // Default score
    strengths: [] as string[],
    weaknesses: [] as string[],
    suggestions: [] as string[]
  };

  // Assess heading usage
  if (metrics.headingCount >= 3) {
    result.score += 0.1;
    result.strengths.push('Content is well-structured with multiple headings');
  } else if (metrics.headingCount === 0) {
    result.score -= 0.1;
    result.weaknesses.push('Content lacks headings for structure');
    result.suggestions.push('Add headings (H2, H3) to organize content and improve structure');
  } else if (metrics.headingCount < 2) {
    result.score -= 0.05;
    result.weaknesses.push('Content has minimal heading structure');
    result.suggestions.push('Add more headings to better organize content sections');
  }

  // Assess paragraph to heading ratio
  const paragraphsPerHeading = metrics.headingCount > 0
    ? metrics.paragraphCount / metrics.headingCount
    : metrics.paragraphCount;

  if (paragraphsPerHeading > 5) {
    result.score -= 0.05;
    result.weaknesses.push('Too many paragraphs per section');
    result.suggestions.push('Break up content with more headings (aim for 2-3 paragraphs per section)');
  } else if (paragraphsPerHeading >= 1.5 && paragraphsPerHeading <= 3) {
    result.score += 0.05;
    result.strengths.push('Good balance of paragraphs per section');
  }

  // Check for introduction and conclusion
  const hasIntroAndConclusion = checkForIntroAndConclusion(artifact);

  if (hasIntroAndConclusion) {
    result.score += 0.1;
    result.strengths.push('Content includes both introduction and conclusion');
  } else {
    result.score -= 0.05;
    result.weaknesses.push('Content may be missing introduction or conclusion');
    result.suggestions.push('Ensure content has a clear introduction and conclusion');
  }

  // Check for lists
  const hasLists = checkForLists(artifact);

  if (hasLists) {
    result.score += 0.05;
    result.strengths.push('Content uses lists to organize information');
  } else if (metrics.wordCount > 500) {
    result.suggestions.push('Consider using bullet points or numbered lists to organize information');
  }

  // Ensure score is in valid range
  result.score = Math.max(0, Math.min(1, result.score));

  return result;
}

/**
 * Assess SEO quality
 */
function assessSeoQuality(
  metrics: ContentQualityMetrics,
  targetKeywords: string[] = []
): {
  score: number;
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
} {
  const result = {
    score: 0.7, // Default score
    strengths: [] as string[],
    weaknesses: [] as string[],
    suggestions: [] as string[]
  };

  // Skip if no target keywords
  if (targetKeywords.length === 0) {
    result.score = 0.5;
    result.weaknesses.push('No target keywords specified for SEO assessment');
    result.suggestions.push('Define target keywords to enable proper SEO assessment');
    return result;
  }

  // Assess keyword density
  let keywordDensityScore = 0;
  let keywordsWithGoodDensity = 0;
  let keywordsWithLowDensity = 0;
  let keywordsWithHighDensity = 0;

  for (const [keyword, density] of Object.entries(metrics.keywordDensity)) {
    if (density >= 0.5 && density <= 2.5) {
      keywordsWithGoodDensity++;
    } else if (density < 0.5) {
      keywordsWithLowDensity++;
    } else if (density > 2.5) {
      keywordsWithHighDensity++;
    }
  }

  // Calculate keyword density score
  if (targetKeywords.length > 0) {
    keywordDensityScore = keywordsWithGoodDensity / targetKeywords.length;
  }

  if (keywordDensityScore >= 0.7) {
    result.score += 0.1;
    result.strengths.push('Optimal keyword density for most target keywords');
  } else if (keywordDensityScore >= 0.3) {
    result.score += 0.05;
    result.strengths.push('Acceptable keyword density for some target keywords');
  } else {
    result.score -= 0.1;
    result.weaknesses.push('Poor keyword density for most target keywords');
  }

  if (keywordsWithLowDensity > 0) {
    result.suggestions.push(`Increase usage of ${keywordsWithLowDensity} keywords with low density (aim for 0.5-2.5%)`);
  }

  if (keywordsWithHighDensity > 0) {
    result.weaknesses.push(`${keywordsWithHighDensity} keywords have excessive density (keyword stuffing)`);
    result.suggestions.push('Reduce keyword density for overused keywords to avoid keyword stuffing');
  }

  // Assess content length for SEO
  if (metrics.wordCount >= 1500) {
    result.score += 0.1;
    result.strengths.push('Content length is excellent for SEO (1500+ words)');
  } else if (metrics.wordCount >= 800) {
    result.score += 0.05;
    result.strengths.push('Content length is good for SEO (800+ words)');
  } else if (metrics.wordCount < 500) {
    result.score -= 0.1;
    result.weaknesses.push('Content is too short for optimal SEO');
    result.suggestions.push('Expand content to at least 800 words for better SEO performance');
  }

  // Assess heading structure for SEO
  if (metrics.headingCount >= 3) {
    result.score += 0.05;
    result.strengths.push('Good heading structure for SEO');
  } else {
    result.score -= 0.05;
    result.weaknesses.push('Insufficient heading structure for SEO');
    result.suggestions.push('Add more headings with keywords for better SEO structure');
  }

  // Ensure score is in valid range
  result.score = Math.max(0, Math.min(1, result.score));

  return result;
}

/**
 * Assess readability quality
 */
function assessReadabilityQuality(metrics: ContentQualityMetrics): {
  score: number;
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
} {
  const result = {
    score: 0.7, // Default score
    strengths: [] as string[],
    weaknesses: [] as string[],
    suggestions: [] as string[]
  };

  // Assess Flesch-Kincaid score
  if (metrics.fleschKincaidScore >= 60 && metrics.fleschKincaidScore <= 80) {
    result.score += 0.1;
    result.strengths.push('Content has optimal readability (grade 7-8 level)');
  } else if (metrics.fleschKincaidScore > 80) {
    result.score += 0.05;
    result.strengths.push('Content is very easy to read (grade 6 level or below)');
  } else if (metrics.fleschKincaidScore < 50) {
    result.score -= 0.1;
    result.weaknesses.push('Content is difficult to read (college level or above)');
    result.suggestions.push('Simplify language to improve readability (aim for grade 7-8 level)');
  } else if (metrics.fleschKincaidScore < 60) {
    result.score -= 0.05;
    result.weaknesses.push('Content readability could be improved (grade 10-12 level)');
    result.suggestions.push('Consider simplifying some sentences and vocabulary');
  }

  // Assess sentence length
  if (metrics.avgWordsPerSentence > 25) {
    result.score -= 0.1;
    result.weaknesses.push('Sentences are too long on average');
    result.suggestions.push('Break up long sentences to improve readability');
  } else if (metrics.avgWordsPerSentence >= 12 && metrics.avgWordsPerSentence <= 18) {
    result.score += 0.1;
    result.strengths.push('Sentence length is optimal for readability');
  }

  // Assess paragraph length
  const avgSentencesPerParagraph = metrics.paragraphCount > 0
    ? metrics.sentenceCount / metrics.paragraphCount
    : 0;

  if (avgSentencesPerParagraph > 5) {
    result.score -= 0.05;
    result.weaknesses.push('Paragraphs are too long on average');
    result.suggestions.push('Break up long paragraphs (aim for 2-4 sentences per paragraph)');
  } else if (avgSentencesPerParagraph >= 2 && avgSentencesPerParagraph <= 4) {
    result.score += 0.05;
    result.strengths.push('Paragraph length is optimal for readability');
  }

  // Assess passive voice
  if (metrics.passiveVoicePercentage > 20) {
    result.score -= 0.05;
    result.weaknesses.push('Excessive use of passive voice reduces readability');
    result.suggestions.push('Convert passive voice to active voice where possible');
  } else if (metrics.passiveVoicePercentage < 10) {
    result.score += 0.05;
    result.strengths.push('Limited use of passive voice improves readability');
  }

  // Ensure score is in valid range
  result.score = Math.max(0, Math.min(1, result.score));

  return result;
}

/**
 * Assess engagement quality
 */
function assessEngagementQuality(
  metrics: ContentQualityMetrics,
  artifact: IterativeArtifact
): {
  score: number;
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
} {
  const result = {
    score: 0.7, // Default score
    strengths: [] as string[],
    weaknesses: [] as string[],
    suggestions: [] as string[]
  };

  // Extract content as string
  const content = extractContentAsString(artifact);

  // Check for questions (engagement markers)
  const questionCount = (content.match(/\?/g) || []).length;
  const questionRatio = metrics.sentenceCount > 0
    ? questionCount / metrics.sentenceCount
    : 0;

  if (questionRatio >= 0.1 && questionRatio <= 0.2) {
    result.score += 0.1;
    result.strengths.push('Content uses questions effectively to engage readers');
  } else if (questionRatio > 0.2) {
    result.score -= 0.05;
    result.weaknesses.push('Too many questions may overwhelm readers');
    result.suggestions.push('Reduce the number of questions to 10-20% of sentences');
  } else if (questionCount === 0) {
    result.score -= 0.05;
    result.weaknesses.push('Content lacks questions to engage readers');
    result.suggestions.push('Add 2-3 thoughtful questions to engage readers');
  }

  // Check for personal pronouns (you, your)
  const personalPronounMatches = content.match(/\b(you|your)\b/gi);
  const personalPronounCount = personalPronounMatches ? personalPronounMatches.length : 0;
  const personalPronounRatio = metrics.wordCount > 0
    ? personalPronounCount / metrics.wordCount
    : 0;

  if (personalPronounRatio >= 0.01 && personalPronounRatio <= 0.03) {
    result.score += 0.1;
    result.strengths.push('Content uses personal pronouns to connect with readers');
  } else if (personalPronounCount === 0) {
    result.score -= 0.05;
    result.weaknesses.push('Content lacks personal connection with readers');
    result.suggestions.push('Add personal pronouns (you, your) to connect with readers');
  }

  // Check for storytelling elements
  const hasStorytelling = checkForStorytelling(content);

  if (hasStorytelling) {
    result.score += 0.1;
    result.strengths.push('Content uses storytelling elements to engage readers');
  } else if (metrics.wordCount > 800) {
    result.suggestions.push('Consider adding storytelling elements to increase engagement');
  }

  // Check for calls to action
  const hasCallToAction = checkForCallToAction(content);

  if (hasCallToAction) {
    result.score += 0.1;
    result.strengths.push('Content includes effective calls to action');
  } else {
    result.score -= 0.05;
    result.weaknesses.push('Content lacks clear calls to action');
    result.suggestions.push('Add clear calls to action to guide readers');
  }

  // Ensure score is in valid range
  result.score = Math.max(0, Math.min(1, result.score));

  return result;
}

/**
 * Check if content has introduction and conclusion
 */
function checkForIntroAndConclusion(artifact: IterativeArtifact): boolean {
  const content = extractContentAsString(artifact);

  // Check for introduction markers
  const introMarkers = [
    /^introduction/i,
    /^in this article/i,
    /^this article/i,
    /^in this post/i,
    /^this post/i,
    /^overview/i,
    /^let's explore/i,
    /^let's discuss/i
  ];

  // Check for conclusion markers
  const conclusionMarkers = [
    /conclusion/i,
    /in summary/i,
    /to summarize/i,
    /to conclude/i,
    /wrapping up/i,
    /final thoughts/i,
    /in closing/i,
    /to sum up/i
  ];

  const hasIntro = introMarkers.some(marker => marker.test(content));
  const hasConclusion = conclusionMarkers.some(marker => marker.test(content));

  return hasIntro && hasConclusion;
}

/**
 * Check if content has lists
 */
function checkForLists(artifact: IterativeArtifact): boolean {
  const content = extractContentAsString(artifact);

  // Check for markdown lists
  const markdownListMarkers = /^[\s]*[-*+][\s]+|^[\s]*\d+\.[\s]+/m;

  // Check for HTML lists
  const htmlListMarkers = /<[uo]l>|<li>/i;

  return markdownListMarkers.test(content) || htmlListMarkers.test(content);
}

/**
 * Check if content has storytelling elements
 */
function checkForStorytelling(content: string): boolean {
  // Check for storytelling markers
  const storytellingMarkers = [
    /once upon a time/i,
    /story/i,
    /experience/i,
    /journey/i,
    /challenge/i,
    /struggle/i,
    /learned/i,
    /discovered/i,
    /realized/i,
    /imagine/i
  ];

  return storytellingMarkers.some(marker => marker.test(content));
}

/**
 * Check if content has calls to action
 */
function checkForCallToAction(content: string): boolean {
  // Check for CTA markers
  const ctaMarkers = [
    /sign up/i,
    /subscribe/i,
    /download/i,
    /register/i,
    /buy now/i,
    /get started/i,
    /learn more/i,
    /click here/i,
    /contact us/i,
    /try it/i,
    /start your/i,
    /begin your/i,
    /join our/i,
    /call us/i,
    /email us/i
  ];

  return ctaMarkers.some(marker => marker.test(content));
}
