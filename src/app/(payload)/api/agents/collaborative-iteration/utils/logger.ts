// src/app/(payload)/api/agents/collaborative-iteration/utils/logger.ts

/**
 * Advanced logging utility for the collaborative agent system
 * Provides structured, level-based logging with timestamps and context
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogContext {
  sessionId?: string;
  agent?: string;
  phase?: string;
  [key: string]: any;
}

// Enable different log levels based on environment
const LOG_LEVEL = process.env.NODE_ENV === 'production' ? 'info' : 'debug';
const LOG_LEVELS: Record<LogLevel, number> = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3
};

/**
 * Formats a log message with contextual information
 */
function formatLogMessage(level: LogLevel, message: string, context: LogContext = {}): string {
  const timestamp = new Date().toISOString();
  const sessionId = context.sessionId ? `[Session: ${context.sessionId}]` : '';
  const agent = context.agent ? `[Agent: ${context.agent}]` : '';
  const phase = context.phase ? `[Phase: ${context.phase}]` : '';
  
  // Stringify any additional context, excluding the special fields
  const { sessionId: _, agent: __, phase: ___, ...restContext } = context;
  const contextStr = Object.keys(restContext).length > 0 
    ? ` | Context: ${JSON.stringify(restContext)}` 
    : '';

  return `${timestamp} [${level.toUpperCase()}] ${sessionId}${agent}${phase} ${message}${contextStr}`;
}

/**
 * Checks if the message should be logged based on current log level
 */
function shouldLog(level: LogLevel): boolean {
  return LOG_LEVELS[level] >= LOG_LEVELS[LOG_LEVEL as LogLevel];
}

/**
 * Core logging function
 */
function log(level: LogLevel, message: string, context: LogContext = {}): void {
  if (!shouldLog(level)) return;

  const formattedMessage = formatLogMessage(level, message, context);
  
  switch (level) {
    case 'debug':
      console.debug(formattedMessage);
      break;
    case 'info':
      console.info(formattedMessage);
      break;
    case 'warn':
      console.warn(formattedMessage);
      break;
    case 'error':
      console.error(formattedMessage);
      break;
  }
}

/**
 * Debug level logging
 */
export function debug(message: string, context: LogContext = {}): void {
  log('debug', message, context);
}

/**
 * Info level logging
 */
export function info(message: string, context: LogContext = {}): void {
  log('info', message, context);
}

/**
 * Warning level logging
 */
export function warn(message: string, context: LogContext = {}): void {
  log('warn', message, context);
}

/**
 * Error level logging
 */
export function error(message: string, context: LogContext = {}): void {
  log('error', message, context);
}

/**
 * Log state changes
 */
export function logStateChange(sessionId: string, phase: string, stateBefore: any, stateAfter: any): void {
  info(`State updated in ${phase}`, { 
    sessionId, 
    phase,
    stateChanges: {
      from: JSON.stringify(stateBefore),
      to: JSON.stringify(stateAfter)
    }
  });
}

/**
 * Log agent messages
 */
export function logAgentMessage(sessionId: string, message: any): void {
  const { from, to, type, content } = message;
  
  debug(`Message: ${from} → ${to} (${type})`, {
    sessionId,
    agent: from,
    messageType: type,
    messageContent: typeof content === 'string' ? content.substring(0, 100) + '...' : '[Complex content]',
    messageTo: to
  });
}

/**
 * Log artifact creation or update
 */
export function logArtifact(sessionId: string, phase: string, artifact: any): void {
  info(`Artifact ${artifact.id} created/updated in ${phase}`, {
    sessionId,
    phase,
    artifactType: artifact.type,
    artifactId: artifact.id,
    artifactStatus: artifact.status
  });
}

/**
 * Log agent decision with reasoning
 */
export function logDecision(sessionId: string, agent: string, decision: any): void {
  info(`Decision made by ${agent}`, {
    sessionId,
    agent,
    decisionId: decision.id,
    decisionContext: decision.context,
    reasoning: decision.reasoning?.substring(0, 100) + '...',
    outcome: decision.outcome?.substring(0, 100) + '...'
  });
}

/**
 * Log workflow phase transition
 */
export function logPhaseTransition(sessionId: string, fromPhase: string, toPhase: string): void {
  info(`Workflow phase transition: ${fromPhase} → ${toPhase}`, {
    sessionId,
    fromPhase,
    toPhase,
    timestamp: new Date().toISOString()
  });
}

export default {
  debug,
  info,
  warn,
  error,
  logStateChange,
  logAgentMessage,
  logArtifact,
  logDecision,
  logPhaseTransition
};
