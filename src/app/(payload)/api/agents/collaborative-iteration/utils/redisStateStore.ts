// src/app/(payload)/api/agents/collaborative-iteration/utils/redisStateStore.ts

import { Redis } from '@upstash/redis';
import { IterativeCollaborationState } from '../types';
import logger from './logger';

/**
 * Redis-based state store for collaborative agent sessions
 * Provides persistent storage across API routes and serverless functions
 * Eliminates state loss issues during Next.js hot reloading
 */
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL || '',
  token: process.env.UPSTASH_REDIS_REST_TOKEN || ''
});

// Simple connection test
(async () => {
  try {
    const pong = await redis.ping();
    logger.info(`Redis connection test: ${pong}`);
  } catch (err) {
    logger.error('Redis connection failed', {
      error: err instanceof Error ? err.message : String(err)
    });
  }
})();

// In-memory fallback cache for reliability
const stateCache = new Map<string, IterativeCollaborationState>();

/**
 * Redis-backed implementation of state store for collaborative agents
 * with in-memory fallback for reliability
 */
export class RedisStateStore {
  private prefix = 'collab-session:';
  private cacheTTL = 24 * 60 * 60; // 24 hours in seconds

  constructor() {
    logger.info('Redis State Store initialized');
  }

  /**
   * Get the state for a session from Redis with fallback to in-memory cache
   */
  async getState(sessionId: string): Promise<IterativeCollaborationState | null> {
    if (!sessionId) {
      logger.error('Attempted to get state with null or undefined sessionId');
      return null;
    }

    try {
      const key = this.prefix + sessionId;
      logger.debug(`Getting state for session ${sessionId}`, { sessionId, key });

      // Check in-memory cache first for immediate retrieval
      if (stateCache.has(sessionId)) {
        const cachedState = stateCache.get(sessionId)!;
        logger.debug(`Retrieved state from in-memory cache for session ${sessionId}`, { sessionId });
        return cachedState;
      }

      // Try Redis if not in cache
      const rawState = await redis.json.get<any>(key, "$");
      logger.debug(`Retrieved raw state from Redis for session ${sessionId}`, { sessionId });

      // Handle the nested array structure from Redis
      let state: IterativeCollaborationState | null = null;

      if (!rawState) {
        logger.debug(`No state found for session ${sessionId}`, { sessionId });
        return null;
      }

      // Handle nested array structure if present
      if (Array.isArray(rawState)) {
        // First level of nesting
        if (Array.isArray(rawState[0])) {
          // Second level of nesting
          if (Array.isArray(rawState[0][0])) {
            // Third level of nesting - this is what we're seeing in logs
            state = rawState[0][0][0] as IterativeCollaborationState;
          } else {
            state = rawState[0][0] as IterativeCollaborationState;
          }
        } else {
          state = rawState[0] as IterativeCollaborationState;
        }
      } else {
        // Not nested, use as is
        state = rawState as IterativeCollaborationState;
      }

      if (!state || typeof state !== 'object') {
        logger.error(`Retrieved invalid state format for session ${sessionId}`, {
          sessionId,
          stateType: typeof state,
          isArray: Array.isArray(state)
        });
        return null;
      }

      // Update the in-memory cache
      stateCache.set(sessionId, state);

      logger.debug(`Successfully retrieved state for session ${sessionId}`, { sessionId });
      return state;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error retrieving state for session ${sessionId}`, {
        sessionId,
        error: err.message,
        stack: err.stack
      });
      return null;
    }
  }

  /**
   * Save the state for a session to Redis and in-memory cache
   */
  async setState(sessionId: string, state: IterativeCollaborationState): Promise<void> {
    if (!sessionId) {
      logger.error('Attempted to set state with null or undefined sessionId');
      return;
    }

    // Always update the in-memory cache immediately
    stateCache.set(sessionId, { ...state });
    logger.debug(`Saved state to in-memory cache for session ${sessionId}`);

    try {
      const key = this.prefix + sessionId;
      console.log("Redis set key", key);
      // Store the state in Redis - cast to unknown then Record<string, unknown> to satisfy TypeScript
      const result = await redis.json.set(key, "$", state as unknown as Record<string, unknown>);
      logger.info("Redis save result status");
      console.log("Redis save result status", result);

      // Set an expiration time to prevent Redis from filling up with old sessions
      const result1 = await redis.expire(key, this.cacheTTL);
      logger.info("Redis expire result status");
      console.log("Redis expire result status", result1);

      // Verify data was stored correctly by pinging Redis
      try {
        // Just check if it exists, don't actually fetch it to avoid
        // unnecessary data transfer
        const exists = await redis.exists(key) > 0;
        if (!exists) {
          logger.error(`Verification failed - Redis key not found after save: ${key}`);
        }
      } catch (verifyErr) {
        logger.error(`Error verifying Redis state persistence: ${verifyErr instanceof Error ? verifyErr.message : String(verifyErr)}`);
      }

      logger.debug(`Successfully saved state for session ${sessionId}`, {
        sessionId,
        artifactsCount: Object.keys(state.artifacts || {}).length,
        messagesCount: (state.messages || []).length,
        decisionsCount: (state.decisions || []).length
      });
    } catch (error) {
      const err = error as Error;
      logger.error(`Error saving state to Redis for session ${sessionId}`, {
        sessionId,
        error: err.message,
        stack: err.stack,
        fallback: "State still available in memory cache"
      });
    }
  }

  /**
   * Update a state atomically using a callback function
   * This is used extensively by agent components to perform atomic updates
   */
  async updateState(
    sessionId: string,
    updateFn: (state: IterativeCollaborationState) => IterativeCollaborationState
  ): Promise<IterativeCollaborationState | null> {
    if (!sessionId) {
      logger.error('Attempted to update state with null or undefined sessionId');
      return null;
    }

    try {
      // Get the current state
      const currentState = await this.getState(sessionId);

      if (!currentState) {
        logger.error(`Cannot update state - session ${sessionId} not found`);
        return null;
      }

      // Apply the update function to create a new state
      // Make a deep copy to prevent accidental mutations
      const updatedState = updateFn(JSON.parse(JSON.stringify(currentState)));

      // Save the updated state
      await this.setState(sessionId, updatedState);

      return updatedState;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error updating state for session ${sessionId}`, {
        sessionId,
        error: err.message,
        stack: err.stack
      });
      return null;
    }
  }

  /**
   * List all available session IDs
   */
  async listSessions(): Promise<string[]> {
    const sessions = new Set<string>();

    // Add sessions from in-memory cache
    stateCache.forEach((_, key) => sessions.add(key));

    try {
      const keys = await redis.keys(this.prefix + '*');
      keys.forEach(key => {
        const id = key.replace(this.prefix, '');
        sessions.add(id);
      });
    } catch (error) {
      const err = error as Error;
      logger.error('Error listing sessions from Redis', {
        error: err.message,
        stack: err.stack,
        fallback: "Using in-memory sessions only"
      });
    }

    return Array.from(sessions);
  }

  /**
   * Delete a session and its state
   */
  async deleteSession(sessionId: string): Promise<boolean> {
    // Remove from in-memory cache first
    stateCache.delete(sessionId);

    try {
      const key = this.prefix + sessionId;
      await redis.del(key);
      logger.info(`Deleted session ${sessionId}`);
      return true;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error deleting session ${sessionId} from Redis`, {
        sessionId,
        error: err.message,
        stack: err.stack,
        result: "Successfully removed from memory cache"
      });
      return false;
    }
  }

  /**
   * Check if a session exists
   */
  async sessionExists(sessionId: string): Promise<boolean> {
    // Check in-memory cache first for faster response
    if (stateCache.has(sessionId)) {
      return true;
    }

    try {
      const key = this.prefix + sessionId;
      return await redis.exists(key) > 0;
    } catch (error) {
      logger.error(`Error checking if session ${sessionId} exists in Redis`, {
        error: error instanceof Error ? error.message : String(error),
        fallback: "Checking memory cache only"
      });
      return stateCache.has(sessionId);
    }
  }

  /**
   * Get the state for a session synchronously from in-memory cache only
   * This is useful for quick checks that don't need to wait for Redis
   */
  getStateSync(sessionId: string): IterativeCollaborationState | null {
    if (!sessionId) {
      logger.error('Attempted to get state synchronously with null or undefined sessionId');
      return null;
    }

    // Only check in-memory cache for sync operations
    if (stateCache.has(sessionId)) {
      const cachedState = stateCache.get(sessionId)!;
      logger.debug(`Retrieved state synchronously from in-memory cache for session ${sessionId}`, { sessionId });
      return cachedState;
    }

    logger.debug(`No state found in memory cache for session ${sessionId} during sync retrieval`, { sessionId });
    return null;
  }
}

// Export a singleton instance
export const redisStateStore = new RedisStateStore();
