import { v4 as uuidv4 } from 'uuid';
import {
  IterativeArtifact,
  Iteration,
  ArtifactStatus,
  AgentId,
  IterativeMessageType,
  Reasoning
} from '../types';
import { EnhancedIterativeMessage, createEnhancedIterativeMessage } from '../types/enhanced-message';
import { stateStore } from './stateStore';
import { enhancedMessageBus } from './enhanced-message-bus';
import logger from './logger';

/**
 * Artifact Manager
 * Handles the creation, versioning, and validation of artifacts
 */
export class ArtifactManager {
  /**
   * Create a new artifact
   */
  async createArtifact(
    sessionId: string,
    type: string,
    name: string,
    content: any,
    createdBy: AgentId | string,
    metadata: Record<string, any> = {},
    qualityScore?: number
  ): Promise<string> {
    try {
      // Generate artifact ID
      const artifactId = `artifact-${uuidv4()}`;
      
      // Create initial iteration
      const initialIteration: Iteration = {
        version: 1,
        timestamp: new Date().toISOString(),
        agent: createdBy as AgentId,
        content,
        feedback: [],
        incorporatedConsultations: []
      };
      
      // Create artifact
      const artifact: IterativeArtifact = {
        id: artifactId,
        type,
        name,
        status: ArtifactStatus.DRAFT,
        createdBy: createdBy as AgentId,
        createdAt: new Date().toISOString(),
        currentVersion: 1,
        iterations: [initialIteration],
        content,
        metadata,
        qualityScore
      };
      
      // Store artifact in state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        throw new Error(`Session ${sessionId} not found`);
      }
      
      await stateStore.updateState(sessionId, {
        artifacts: {
          ...(state.artifacts || {}),
          [artifactId]: artifact
        },
        generatedArtifacts: [
          ...(state.generatedArtifacts || []),
          artifactId
        ]
      });
      
      // Create artifact creation message
      const artifactMessage = createEnhancedIterativeMessage(
        createdBy,
        'all',
        IterativeMessageType.ARTIFACT_DELIVERY,
        {
          artifactId,
          artifactType: type,
          artifactName: name,
          version: 1,
          summary: typeof content === 'string' 
            ? content.substring(0, 100) + '...' 
            : 'Structured artifact created'
        },
        {
          sessionId,
          artifactId,
          version: 1,
          reasoning: {
            thoughts: [`Created new ${type} artifact`],
            considerations: [
              'Artifact should meet quality standards',
              'Other agents may need to review or build upon this',
              'Artifact should be properly structured for collaboration'
            ],
            decision: `Create and share ${type} artifact with all agents`,
            confidence: 0.9
          },
          metadata: {
            artifactType: type,
            qualityScore
          }
        }
      );
      
      // Send artifact message
      await enhancedMessageBus.sendMessage(sessionId, artifactMessage);
      
      logger.info(`Artifact created`, {
        sessionId,
        artifactId,
        type,
        name,
        createdBy,
        timestamp: new Date().toISOString()
      });
      
      return artifactId;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error creating artifact`, {
        sessionId,
        type,
        name,
        createdBy,
        error: err.message || String(error),
        stack: err.stack
      });
      throw error;
    }
  }
  
  /**
   * Create a new iteration of an artifact
   */
  async createIteration(
    sessionId: string,
    artifactId: string,
    content: any,
    agent: AgentId | string,
    changes: string,
    incorporatedConsultations: string[] = [],
    reasoning?: Reasoning,
    qualityScore?: number
  ): Promise<number> {
    try {
      // Get artifact from state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        throw new Error(`Session ${sessionId} not found`);
      }
      
      const artifact = state.artifacts?.[artifactId];
      if (!artifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }
      
      // Create new version number
      const newVersion = artifact.currentVersion + 1;
      
      // Create new iteration
      const newIteration: Iteration = {
        version: newVersion,
        timestamp: new Date().toISOString(),
        agent: agent as AgentId,
        content,
        feedback: [],
        incorporatedConsultations,
        changes,
        reasoning
      };
      
      // Update artifact
      const updatedArtifact: IterativeArtifact = {
        ...artifact,
        currentVersion: newVersion,
        iterations: [...artifact.iterations, newIteration],
        content,
        updatedAt: new Date().toISOString(),
        qualityScore
      };
      
      // Store updated artifact
      await stateStore.updateState(sessionId, {
        artifacts: {
          ...(state.artifacts || {}),
          [artifactId]: updatedArtifact
        }
      });
      
      // Create iteration message
      const iterationMessage = createEnhancedIterativeMessage(
        agent,
        'all',
        IterativeMessageType.ITERATION_RESPONSE,
        {
          artifactId,
          artifactType: artifact.type,
          artifactName: artifact.name,
          previousVersion: artifact.currentVersion,
          newVersion,
          changes,
          incorporatedConsultations,
          qualityScore
        },
        {
          sessionId,
          artifactId,
          version: newVersion,
          reasoning,
          metadata: {
            artifactType: artifact.type,
            qualityScore
          }
        }
      );
      
      // Send iteration message
      await enhancedMessageBus.sendMessage(sessionId, iterationMessage);
      
      logger.info(`Artifact iteration created`, {
        sessionId,
        artifactId,
        version: newVersion,
        agent,
        timestamp: new Date().toISOString()
      });
      
      return newVersion;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error creating artifact iteration`, {
        sessionId,
        artifactId,
        agent,
        error: err.message || String(error),
        stack: err.stack
      });
      throw error;
    }
  }
  
  /**
   * Update artifact status
   */
  async updateArtifactStatus(
    sessionId: string,
    artifactId: string,
    status: ArtifactStatus,
    agent: AgentId | string,
    reasoning?: Reasoning
  ): Promise<void> {
    try {
      // Get artifact from state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        throw new Error(`Session ${sessionId} not found`);
      }
      
      const artifact = state.artifacts?.[artifactId];
      if (!artifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }
      
      // Update artifact status
      const updatedArtifact: IterativeArtifact = {
        ...artifact,
        status,
        updatedAt: new Date().toISOString()
      };
      
      // Store updated artifact
      await stateStore.updateState(sessionId, {
        artifacts: {
          ...(state.artifacts || {}),
          [artifactId]: updatedArtifact
        }
      });
      
      // Create status update message
      const statusMessage = createEnhancedIterativeMessage(
        agent,
        'all',
        IterativeMessageType.SYSTEM_MESSAGE,
        {
          event: 'ARTIFACT_STATUS_UPDATE',
          artifactId,
          artifactType: artifact.type,
          artifactName: artifact.name,
          previousStatus: artifact.status,
          newStatus: status
        },
        {
          sessionId,
          artifactId,
          reasoning,
          metadata: {
            artifactType: artifact.type,
            statusUpdate: true
          }
        }
      );
      
      // Send status message
      await enhancedMessageBus.sendMessage(sessionId, statusMessage);
      
      logger.info(`Artifact status updated`, {
        sessionId,
        artifactId,
        previousStatus: artifact.status,
        newStatus: status,
        agent,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      const err = error as Error;
      logger.error(`Error updating artifact status`, {
        sessionId,
        artifactId,
        status,
        agent,
        error: err.message || String(error),
        stack: err.stack
      });
      throw error;
    }
  }
  
  /**
   * Validate artifact quality
   */
  async validateArtifact(
    sessionId: string,
    artifactId: string,
    qualityThreshold: number
  ): Promise<boolean> {
    try {
      // Get artifact from state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        throw new Error(`Session ${sessionId} not found`);
      }
      
      const artifact = state.artifacts?.[artifactId];
      if (!artifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }
      
      // Check if artifact has quality score
      if (artifact.qualityScore === undefined) {
        logger.warn(`Artifact ${artifactId} has no quality score, validation failed`, {
          sessionId,
          artifactId,
          threshold: qualityThreshold
        });
        return false;
      }
      
      // Validate against threshold
      const isValid = artifact.qualityScore >= qualityThreshold;
      
      logger.info(`Artifact validation ${isValid ? 'passed' : 'failed'}`, {
        sessionId,
        artifactId,
        qualityScore: artifact.qualityScore,
        threshold: qualityThreshold,
        timestamp: new Date().toISOString()
      });
      
      return isValid;
    } catch (error) {
      const err = error as Error;
      logger.error(`Error validating artifact`, {
        sessionId,
        artifactId,
        error: err.message || String(error),
        stack: err.stack
      });
      return false;
    }
  }
  
  /**
   * Get all artifacts for a session
   */
  async getArtifacts(sessionId: string): Promise<Record<string, IterativeArtifact>> {
    const state = await stateStore.getState(sessionId);
    if (!state) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    return state.artifacts || {};
  }
  
  /**
   * Get a specific artifact
   */
  async getArtifact(sessionId: string, artifactId: string): Promise<IterativeArtifact | null> {
    const state = await stateStore.getState(sessionId);
    if (!state) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    return state.artifacts?.[artifactId] || null;
  }
}

// Create singleton instance
export const artifactManager = new ArtifactManager();
