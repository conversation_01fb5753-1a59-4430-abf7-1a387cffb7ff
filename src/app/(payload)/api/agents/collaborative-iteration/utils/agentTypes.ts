/**
 * This file defines types and interfaces for agent collaboration utilities
 */
import { 
  IterativeMessage, 
  IterativeArtifact, 
  Consultation,
  AgentState
} from '../types';

/**
 * Defines valid agent IDs in the system
 */
export type AgentId = 
  | 'content-generation'
  | 'seo-keyword'
  | 'market-research'
  | 'content-strategy'
  | 'seo-optimization'
  | 'orchestrator';

/**
 * Also export an enum for AgentId for compatibility
 */
export enum AgentIdEnum {
  CONTENT_GENERATION = 'content-generation',
  SEO_KEYWORD = 'seo-keyword',
  MARKET_RESEARCH = 'market-research',
  CONTENT_STRATEGY = 'content-strategy',
  SEO_OPTIMIZATION = 'seo-optimization',
  ORCHESTRATOR = 'orchestrator'
}

/**
 * StandardizedHandlerResult provides a consistent structure for all agent handler results
 * This makes it easier to integrate with the central collaboration state
 */
export interface StandardizedHandlerResult {
  response: IterativeMessage;
  stateUpdates?: Record<string, any>;
  artifactUpdates?: {
    new?: Record<string, IterativeArtifact>;
    updated?: Record<string, IterativeArtifact>;
  };
  consultationUpdates?: {
    new?: Record<string, Consultation>;
  };
}
