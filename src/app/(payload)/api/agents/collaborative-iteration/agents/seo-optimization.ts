/**
 * SEO Optimization Agent
 *
 * This agent is responsible for optimizing content for search engines.
 */

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeMessageType,
  IterativeArtifact,
  ArtifactStatus,
  Goal
} from '../types';
import { stateStore } from '../utils/stateStore';
import logger from '../utils/logger';
import { handleSeoOptimizationInitialRequest, handleSeoOptimizationArtifactRequest } from './index';

/**
 * SEO Optimization Agent class
 */
export class SeoOptimizationAgent {
  private agentId = 'seo-optimization';

  /**
   * Process a message sent to this agent
   */
  async processMessage(sessionId: string, message: IterativeMessage): Promise<any> {
    logger.info(`SEO Optimization Agent processing message of type ${message.type}`, {
      sessionId,
      messageId: message.id,
      messageType: message.type
    });

    try {
      // Get the current state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        logger.error(`Session ${sessionId} not found`, { sessionId });
        return null;
      }

      // Create a simple state manager for the handlers
      const stateManager = {
        addArtifact: async (sessionId: string, artifact: IterativeArtifact) => {
          return await stateStore.updateState(sessionId, (state) => {
            if (!state.artifacts) {
              state.artifacts = {};
            }
            state.artifacts[artifact.id] = artifact;

            // Also add to generatedArtifacts array if it exists
            if (state.generatedArtifacts) {
              state.generatedArtifacts.push(artifact.id);
            } else {
              state.generatedArtifacts = [artifact.id];
            }

            return state;
          });
        },
        updateArtifact: async (sessionId: string, artifactId: string, updateFn: (artifact: IterativeArtifact) => IterativeArtifact) => {
          return await stateStore.updateState(sessionId, (state) => {
            if (state.artifacts && state.artifacts[artifactId]) {
              state.artifacts[artifactId] = updateFn(state.artifacts[artifactId]);
            }
            return state;
          });
        }
      };

      // Create a simple messaging utility for the handlers
      const messaging = {
        send: async (
          sessionId: string,
          to: string,
          type: IterativeMessageType,
          content: any,
          conversationId?: string,
          inReplyTo?: string,
          artifactId?: string
        ) => {
          const message: IterativeMessage = {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: this.agentId,
            to,
            type,
            content,
            conversationId: conversationId || sessionId,
            inReplyTo,
            artifactId
          };

          // Store the message in the state
          await stateStore.updateState(sessionId, (state) => {
            if (!state.messages) {
              state.messages = [];
            }
            state.messages.push(message);
            return state;
          });

          return message;
        }
      };

      // Handle different message types
      switch (message.type) {
        case IterativeMessageType.INITIAL_REQUEST:
        case IterativeMessageType.REQUEST:
          return await handleSeoOptimizationInitialRequest(message, state, stateManager, messaging);

        case IterativeMessageType.ARTIFACT_REQUEST:
          return await handleSeoOptimizationArtifactRequest(message, state, stateManager, messaging);

        case IterativeMessageType.DISCUSSION_START:
          return await this.handleDiscussionStart(sessionId, message, state);

        case IterativeMessageType.FEEDBACK:
          // Import the handleFeedback function from the handlers file
          const { handleFeedback } = await import('./seo-optimization/handlers');
          return await handleFeedback(message, state, stateManager, messaging);

        default:
          logger.warn(`SEO Optimization Agent: Unhandled message type ${message.type}`, {
            sessionId,
            messageType: message.type
          });
          return null;
      }
    } catch (error) {
      logger.error(`Error processing message in SEO Optimization Agent`, {
        sessionId,
        messageId: message.id,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * Act on the current state of the session
   * This method is called by the workflow orchestrator
   */
  async act(sessionId: string): Promise<boolean> {
    logger.info(`SEO Optimization Agent acting on session ${sessionId}`, { sessionId });

    try {
      // Get the current state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        logger.error(`Session ${sessionId} not found`, { sessionId });
        return false;
      }

      // Find active goals assigned to this agent
      const activeGoals = (state.goals || []).filter((goal: Goal) =>
        goal.status === 'active' && goal.assignedTo === this.agentId
      );

      if (activeGoals.length === 0) {
        logger.info(`No active goals for SEO Optimization Agent in session ${sessionId}`, { sessionId });
        return false;
      }

      // Process each active goal
      for (const goal of activeGoals) {
        logger.info(`SEO Optimization Agent processing goal ${goal.id}`, {
          sessionId,
          goalId: goal.id,
          goalName: goal.name
        });

        // Create a message for this goal
        const message: IterativeMessage = {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: 'system',
          to: this.agentId,
          type: IterativeMessageType.INITIAL_REQUEST,
          content: {
            topic: state.topic,
            contentType: state.contentType,
            targetAudience: state.targetAudience,
            tone: state.tone,
            ...goal.metadata
          },
          conversationId: sessionId,
          goalId: goal.id
        };

        // Process the message
        const result = await this.processMessage(sessionId, message);

        if (result && result.success) {
          // Mark the goal as completed
          await stateStore.updateState(sessionId, (state) => {
            const goalIndex = state.goals.findIndex((g: Goal) => g.id === goal.id);
            if (goalIndex !== -1) {
              state.goals[goalIndex].status = 'completed';
              state.goals[goalIndex].completedAt = new Date().toISOString();
            }
            return state;
          });

          logger.info(`SEO Optimization Agent completed goal ${goal.id}`, {
            sessionId,
            goalId: goal.id,
            goalName: goal.name
          });
        } else {
          logger.error(`SEO Optimization Agent failed to complete goal ${goal.id}`, {
            sessionId,
            goalId: goal.id,
            goalName: goal.name,
            error: result?.message || 'Unknown error'
          });

          // Mark the goal as failed
          await stateStore.updateState(sessionId, (state) => {
            const goalIndex = state.goals.findIndex((g: Goal) => g.id === goal.id);
            if (goalIndex !== -1) {
              state.goals[goalIndex].status = 'failed';
              state.goals[goalIndex].failedAt = new Date().toISOString();
              state.goals[goalIndex].failureReason = result?.message || 'Unknown error';
            }
            return state;
          });
        }
      }

      return true;
    } catch (error) {
      logger.error(`Error in SEO Optimization Agent act method`, {
        sessionId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      return false;
    }
  }

  /**
   * Handle initial request for SEO optimization
   */
  private async handleInitialRequest(sessionId: string, message: IterativeMessage, state: any): Promise<any> {
    logger.info(`SEO Optimization Agent: Handling initial request`, { sessionId });

    // Extract request details
    const { topic, contentType, content } = message.content || {};

    if (!topic || !content) {
      return {
        success: false,
        message: 'Topic and content are required for SEO optimization',
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: this.agentId,
          to: message.from,
          type: IterativeMessageType.ERROR,
          content: {
            error: 'Missing required fields',
            message: 'Topic and content are required for SEO optimization'
          },
          conversationId: message.conversationId
        }
      };
    }

    // Find keyword research artifact if available
    let keywordResearch = null;

    if (state.artifacts) {
      // Find keyword research artifact
      const keywordArtifacts = Object.values(state.artifacts)
        .filter((a: any) => a.type === 'seo-keywords')
        .sort((a: any, b: any) => {
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        });

      if (keywordArtifacts.length > 0) {
        keywordResearch = keywordArtifacts[0];
      }
    }

    // Generate SEO optimization report
    const seoOptimizationReport = {
      topic,
      contentType: contentType || 'blog-article',
      keywordAnalysis: keywordResearch ? {
        primaryKeyword: keywordResearch.iterations[0].content.primaryKeyword,
        primaryKeywordDensity: '1.2%',
        secondaryKeywords: keywordResearch.iterations[0].content.secondaryKeywords,
        secondaryKeywordPresence: 'Good',
        missingKeywords: keywordResearch.iterations[0].content.secondaryKeywords.slice(0, 2),
        keywordPlacement: {
          title: 'Present',
          headings: 'Partial',
          introduction: 'Present',
          conclusion: 'Missing',
          imageAlt: 'Missing'
        }
      } : {
        primaryKeyword: topic,
        primaryKeywordDensity: '1.0%',
        secondaryKeywords: [`${topic} guide`, `${topic} examples`, `how to use ${topic}`],
        secondaryKeywordPresence: 'Partial',
        missingKeywords: [`${topic} benefits`, `${topic} best practices`],
        keywordPlacement: {
          title: 'Present',
          headings: 'Partial',
          introduction: 'Present',
          conclusion: 'Missing',
          imageAlt: 'Missing'
        }
      },
      contentStructure: {
        headingHierarchy: 'Good',
        paragraphLength: 'Good',
        contentFlow: 'Good',
        readability: 'Flesch-Kincaid Grade Level: 9.2 (Good)',
        contentLength: 'Adequate (1800 words)'
      },
      technicalSEO: {
        metaTitle: `The Ultimate Guide to ${topic}`,
        metaDescription: `Learn everything you need to know about ${topic} in this comprehensive guide. Discover best practices, examples, and step-by-step instructions.`,
        urlStructure: `example.com/guide/${topic.toLowerCase().replace(/\s+/g, '-')}`,
        imageOptimization: 'Needs improvement',
        internalLinking: 'Needs improvement',
        externalLinking: 'Adequate',
        schemaMarkup: 'Missing'
      },
      recommendations: [
        `Increase the density of the primary keyword "${topic}" to 1.5-2%`,
        `Add missing secondary keywords: ${keywordResearch ? keywordResearch.iterations[0].content.secondaryKeywords.slice(0, 2).join(', ') : `${topic} benefits, ${topic} best practices`}`,
        `Include the primary keyword in the conclusion`,
        `Add alt text to all images, including the primary keyword where relevant`,
        `Add schema markup for Article or HowTo`,
        `Include 2-3 internal links to related content`,
        `Add 1-2 external links to authoritative sources`,
        `Break up longer paragraphs for better readability`,
        `Add a table of contents for longer articles`,
        `Include FAQ section targeting long-tail keywords`
      ],
      optimizedElements: {
        title: `The Ultimate Guide to ${topic}: Best Practices, Examples, and Implementation`,
        metaDescription: `Discover everything you need to know about ${topic} in our comprehensive guide. Learn best practices, see real-world examples, and follow our step-by-step implementation instructions.`,
        headings: [
          {
            level: 'H1',
            text: `The Ultimate Guide to ${topic}`
          },
          {
            level: 'H2',
            text: `Understanding ${topic}: Key Concepts and Benefits`
          },
          {
            level: 'H2',
            text: `${topic} Best Practices for Optimal Results`
          },
          {
            level: 'H2',
            text: `Step-by-Step Guide to Implementing ${topic}`
          },
          {
            level: 'H2',
            text: `Real-World Examples of ${topic} in Action`
          },
          {
            level: 'H2',
            text: `Common Challenges and Solutions for ${topic}`
          },
          {
            level: 'H2',
            text: `Frequently Asked Questions About ${topic}`
          }
        ],
        imageAltText: [
          `${topic} implementation diagram`,
          `${topic} best practices infographic`,
          `${topic} case study results chart`,
          `Step-by-step ${topic} process`
        ]
      }
    };

    // Create SEO optimization artifact
    const artifact: IterativeArtifact = {
      id: uuidv4(),
      name: `SEO Optimization Report: ${topic}`,
      type: 'seo-optimization',
      status: 'completed' as ArtifactStatus,
      createdBy: this.agentId,
      createdAt: new Date().toISOString(),
      currentVersion: 1,
      iterations: [
        {
          version: 1,
          timestamp: new Date().toISOString(),
          agent: this.agentId,
          content: seoOptimizationReport,
          feedback: [],
          incorporatedConsultations: []
        }
      ],
      qualityScore: 85
    };

    // Add the artifact to the state
    if (!state.artifacts) {
      state.artifacts = {};
    }
    state.artifacts[artifact.id] = artifact;

    // Add to generatedArtifacts array if it exists
    if (state.generatedArtifacts) {
      state.generatedArtifacts.push(artifact.id);
    } else {
      state.generatedArtifacts = [artifact.id];
    }

    // Update workflow progress
    if (state.workflowProgress) {
      state.workflowProgress.seoOptimizationComplete = true;
    }

    // Save the updated state
    await stateStore.setState(sessionId, state);

    // Return the response
    return {
      success: true,
      message: `Successfully generated SEO optimization report for ${topic}`,
      response: {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: this.agentId,
        to: message.from,
        type: IterativeMessageType.ARTIFACT_DELIVERY,
        content: {
          artifact,
          message: `Generated SEO optimization report for ${topic}`
        },
        artifactId: artifact.id,
        conversationId: message.conversationId
      }
    };
  }

  /**
   * Handle artifact request
   */
  private async handleArtifactRequest(sessionId: string, message: IterativeMessage, state: any): Promise<any> {
    logger.info(`SEO Optimization Agent: Handling artifact request`, { sessionId });

    // Extract request details
    const { artifactId, artifactType } = message.content || {};

    // Find the requested artifact
    let artifact: IterativeArtifact | null = null;

    if (artifactId && state.artifacts && state.artifacts[artifactId]) {
      artifact = state.artifacts[artifactId];
    } else if (artifactType === 'seo-optimization' && state.artifacts) {
      // Find the latest SEO optimization artifact
      const seoArtifacts = Object.values(state.artifacts)
        .filter((a: any) => a.type === 'seo-optimization' && a.createdBy === this.agentId)
        .sort((a: any, b: any) => {
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        });

      if (seoArtifacts.length > 0) {
        artifact = seoArtifacts[0] as IterativeArtifact;
      }
    }

    if (!artifact) {
      return {
        success: false,
        message: 'Artifact not found',
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: this.agentId,
          to: message.from,
          type: IterativeMessageType.ERROR,
          content: {
            error: 'Artifact not found',
            message: `Could not find the requested artifact`
          },
          conversationId: message.conversationId
        }
      };
    }

    // Return the artifact
    return {
      success: true,
      message: `Successfully retrieved artifact ${artifact.id}`,
      response: {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: this.agentId,
        to: message.from,
        type: IterativeMessageType.ARTIFACT_DELIVERY,
        content: {
          artifact,
          message: `Retrieved artifact ${artifact.id}`
        },
        artifactId: artifact.id,
        conversationId: message.conversationId
      }
    };
  }

  /**
   * Handle discussion start
   */
  private async handleDiscussionStart(sessionId: string, message: IterativeMessage, state: any): Promise<any> {
    logger.info(`SEO Optimization Agent: Handling discussion start`, { sessionId });

    // Extract discussion details
    const { discussionId, topic, prompt } = message.content || {};

    if (!discussionId || !topic) {
      return {
        success: false,
        message: 'Discussion ID and topic are required',
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: this.agentId,
          to: message.from,
          type: IterativeMessageType.ERROR,
          content: {
            error: 'Missing required fields',
            message: 'Discussion ID and topic are required'
          },
          conversationId: message.conversationId
        }
      };
    }

    // Generate a perspective on the topic from an SEO optimization standpoint
    const perspective = `From an SEO optimization perspective, I recommend the following approach for "${topic}":

1. **Technical SEO Foundations**:
   - Use a semantic HTML structure with proper heading hierarchy (H1 → H2 → H3)
   - Implement schema markup for enhanced SERP features (Article, HowTo, or FAQ)
   - Optimize page speed by compressing images and minimizing render-blocking resources
   - Ensure mobile responsiveness and accessibility compliance

2. **On-Page Optimization**:
   - Create an SEO-optimized title tag (50-60 characters) including the primary keyword
   - Write a compelling meta description (150-160 characters) with a call-to-action
   - Use the primary keyword in the first 100 words and maintain a 1.5-2% keyword density
   - Include secondary keywords naturally in subheadings and throughout the content

3. **Content Enhancement**:
   - Add a table of contents for longer articles to improve user experience
   - Include multimedia elements (images, videos, infographics) with optimized alt text
   - Create FAQ sections targeting long-tail keywords and featured snippet opportunities
   - Implement internal linking to related content and authoritative external sources

4. **User Experience Considerations**:
   - Optimize for Core Web Vitals (LCP, FID, CLS)
   - Ensure clear content structure with appropriate white space
   - Use bullet points and numbered lists for scannable content
   - Include interactive elements to increase engagement and dwell time

These optimizations will help the content rank higher in search results while providing a better user experience.`;

    // Return the perspective
    return {
      success: true,
      message: `Successfully contributed to discussion ${discussionId}`,
      response: {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: this.agentId,
        to: message.from,
        type: IterativeMessageType.DISCUSSION_CONTRIBUTION,
        content: {
          discussionId,
          perspective,
          contributor: this.agentId,
          contributionType: 'seo-optimization-perspective'
        },
        conversationId: message.conversationId
      }
    };
  }
}
