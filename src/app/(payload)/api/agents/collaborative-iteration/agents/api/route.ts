import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { 
  AgentId, 
  IterativeMessage, 
  IterativeMessageType, 
  IterativeCollaborationState, 
  Goal, 
  Consultation 
} from '../../types';
import { stateStore } from '../../utils/stateStore';
import { messageBus } from '../../utils/messageBus';

// Import agent instances
import { contentStrategyAgent } from '../content-strategy';
import { seoOptimizationAgent } from '../seo-optimization';
import { marketResearchAgent } from '../market-research';
import { seoKeywordAgent } from '../seo-keyword';
import { contentGenerationAgent } from '../content-generation';

// Agent map for accessing the appropriate agent by role
const agentMap = {
  [AgentId.MARKET_RESEARCH]: marketResearchAgent,
  [AgentId.SEO_KEYWORD]: seoKeywordAgent,
  [AgentId.CONTENT_STRATEGY]: contentStrategyAgent,
  [AgentId.CONTENT_GENERATION]: contentGenerationAgent,
  [AgentId.SEO_OPTIMIZATION]: seoOptimizationAgent
};

// Default goals for new sessions
const defaultGoals: Partial<Goal>[] = [
  {
    id: 'market-research',
    title: 'Market Research',
    description: 'Research market trends and customer pain points',
    status: 'pending',
    agent: AgentId.MARKET_RESEARCH,
    dependentGoals: [],
    priority: 'high',
    createdAt: new Date().toISOString()
  },
  {
    id: 'seo-keywords',
    title: 'SEO Keywords',
    description: 'Identify target keywords and search intent',
    status: 'pending',
    agent: AgentId.SEO_KEYWORD,
    dependentGoals: ['market-research'],
    priority: 'high',
    createdAt: new Date().toISOString()
  },
  {
    id: 'content-strategy',
    title: 'Content Strategy',
    description: 'Develop content structure and outline',
    status: 'pending',
    agent: AgentId.CONTENT_STRATEGY,
    dependentGoals: ['seo-keywords'],
    priority: 'high',
    createdAt: new Date().toISOString()
  },
  {
    id: 'content-generation',
    title: 'Content Generation',
    description: 'Generate initial content draft',
    status: 'pending',
    agent: AgentId.CONTENT_GENERATION,
    dependentGoals: ['content-strategy'],
    priority: 'high',
    createdAt: new Date().toISOString()
  },
  {
    id: 'seo-optimization',
    title: 'SEO Optimization',
    description: 'Optimize content for search engines',
    status: 'pending',
    agent: AgentId.SEO_OPTIMIZATION,
    dependentGoals: ['content-generation'],
    priority: 'high',
    createdAt: new Date().toISOString()
  }
];

/**
 * API route for collaborative agent iteration system
 * Supports creation of new sessions, state updates, and message handling
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    
    // Extract action type
    const { action } = body;
    
    switch (action) {
      case 'createSession':
        return await handleSessionCreation(body);
      case 'sendMessage':
        return await handleSendMessage(body);
      case 'getSessionState':
        return await handleGetSessionState(body);
      case 'updateSessionState':
        return await handleUpdateSessionState(body);
      case 'advanceGoal':
        return await handleAdvanceGoal(body);
      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }
  } catch (error: any) {
    console.error('Error in collaborative agent API:', error);
    return NextResponse.json(
      { error: error.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

/**
 * Helper function to send a system message
 */
async function sendSystemMessage(sessionId: string, content: string): Promise<IterativeMessage> {
  const systemMessage: IterativeMessage = {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    from: 'system',
    to: 'all',
    type: IterativeMessageType.SYSTEM_MESSAGE,
    content: { text: content },
    conversationId: sessionId,
    metadata: { sessionId }
  };
  
  const sessionBus = await messageBus.createSessionBus(sessionId);
  await sessionBus.queueMessage(systemMessage);
  
  return systemMessage;
}

/**
 * Handle session creation
 * Initializes a new collaborative session with the provided parameters
 */
async function handleSessionCreation(body: any) {
  // Extract session parameters
  const sessionId = body.sessionId || uuidv4();
  
  // Create initial state
  const initialState: IterativeCollaborationState = {
    id: sessionId,
    topic: body.topic,
    contentType: body.contentType as 'blog-article' | 'product-page' | 'buying-guide',
    targetAudience: body.targetAudience,
    keywords: body.keywords || [],
    tone: body.tone,
    clientName: body.clientName || '',
    additionalInstructions: body.additionalInstructions || '',
    startTime: new Date().toISOString(),
    lastUpdateTime: new Date().toISOString(),
    artifacts: {},
    messages: [],
    decisions: [],
    completed: false,
    consultations: {} as Record<string, Consultation>,
    agentStates: {},
    iterations: 0,
    maxIterations: 5,
    goals: body.goals?.map((goal: any) => ({
      ...goal,
      id: goal.id || uuidv4(),
      status: goal.status || 'pending',
      createdAt: goal.createdAt || new Date().toISOString()
    })) || defaultGoals.map(goal => ({ ...goal, id: uuidv4() })) as Goal[],
    agents: {
      'market-research': { active: true },
      'seo-keyword': { active: true },
      'content-strategy': { active: true },
      'content-generation': { active: true },
      'seo-optimization': { active: true }
    },
    collaborationState: 'planning',
    status: 'active',
    active: true
  };
  
  // Store initial state
  await stateStore.set(sessionId, initialState);
  
  // Create a dedicated message bus for this session
  await messageBus.createSessionBus(sessionId);
  
  // Send initial message to Market Research agent
  const initialMessage: IterativeMessage = {
    id: uuidv4(),
    sessionId: sessionId,
    type: IterativeMessageType.INITIAL_REQUEST,
    from: 'user',
    to: AgentId.MARKET_RESEARCH,
    content: {
      topic: body.topic,
      contentType: body.contentType,
      targetAudience: body.targetAudience,
      keywords: body.keywords,
      tone: body.tone,
      additionalInstructions: body.additionalInstructions
    },
    timestamp: new Date().toISOString(),
    conversationId: sessionId,
    metadata: { sessionId }
  };
  
  // Add the message to the session state
  initialState.messages.push(initialMessage);
  await stateStore.set(sessionId, initialState);
  
  // Process the message
  // Define a processor function that will handle the message routing
  const processorFn = async (sessionId: string, message: IterativeMessage) => {
    // Get the target agent
    if (message.to === AgentId.MARKET_RESEARCH) {
      console.log(`Routing initial message to Market Research agent`);
      // Route to the Market Research agent
      const agent = agentMap[AgentId.MARKET_RESEARCH];
      if (agent && typeof agent.processMessage === 'function') {
        try {
          return await agent.processMessage(sessionId, message);
        } catch (error) {
          console.error(`Error processing message by Market Research agent:`, error);
        }
      }
    }
    
    return { success: true };
  };
  
  await messageBus.sendMessage(sessionId, initialMessage, processorFn);
  
  // Return session ID and initial state
  return NextResponse.json({ 
    sessionId, 
    state: initialState,
    message: initialMessage,
    success: true 
  });
}

/**
 * Handle sending a message within a session
 */
async function handleSendMessage(body: any) {
  const { sessionId, message } = body;
  
  // Get current session state
  const state = await stateStore.get(sessionId);
  if (!state) {
    return NextResponse.json({ error: 'Session not found' }, { status: 404 });
  }
  
  // Ensure the message has a conversation ID
  const fullMessage: IterativeMessage = {
    ...message,
    id: message.id || uuidv4(),
    timestamp: message.timestamp || new Date().toISOString(),
    conversationId: sessionId,
    metadata: { ...message.metadata, sessionId }
  };
  
  // Process the message
  // Define a processor function that will handle the message routing
  const processorFn = async (sessionId: string, message: IterativeMessage) => {
    // Get the target agent(s)
    const targets = Array.isArray(message.to) ? message.to : [message.to];
    
    // If message is to 'all', broadcast to all agents
    if (message.to === 'all' || targets.includes('all')) {
      console.log(`Broadcasting message to all agents from ${message.from}`);
      // Implementation would handle broadcasting to all agents
      return { success: true };
    }
    
    // For direct messages to specific agents
    let responses: any[] = [];
    for (const target of targets) {
      if (target in agentMap) {
        console.log(`Routing message from ${message.from} to ${target}`);
        // Route to the appropriate agent
        const agent = agentMap[target as keyof typeof agentMap];
        if (agent && typeof agent.processMessage === 'function') {
          try {
            const response = await agent.processMessage(sessionId, message);
            if (response) responses.push(response);
          } catch (error) {
            console.error(`Error processing message by agent ${target}:`, error);
          }
        }
      }
    }
    
    return { success: true, responses };
  };

  await messageBus.sendMessage(sessionId, fullMessage, processorFn);
  
  // Add the message to the session state
  state.messages = state.messages || [];
  state.messages.push(fullMessage);
  state.lastUpdateTime = new Date().toISOString();
  await stateStore.set(sessionId, state);
  
  return NextResponse.json({
    success: true,
    message: fullMessage
  });
}

/**
 * Handle retrieving the current session state
 */
async function handleGetSessionState(body: any) {
  const { sessionId } = body;
  
  // Get current session state
  const state = await stateStore.get(sessionId);
  if (!state) {
    return NextResponse.json({ error: 'Session not found' }, { status: 404 });
  }
  
  return NextResponse.json({ state });
}

/**
 * Handle updating the session state
 */
async function handleUpdateSessionState(body: any) {
  const { sessionId, updates } = body;
  
  // Get current session state
  const state = await stateStore.get(sessionId);
  if (!state) {
    return NextResponse.json({ error: 'Session not found' }, { status: 404 });
  }
  
  // Apply updates to state
  const updatedState = { ...state, ...updates, lastUpdateTime: new Date().toISOString() };
  await stateStore.set(sessionId, updatedState);
  
  return NextResponse.json({ success: true, state: updatedState });
}

/**
 * Handle advancing a goal's status
 */
async function handleAdvanceGoal(body: any) {
  const { sessionId, goalId } = body;
  
  // Get current session state
  const state = await stateStore.get(sessionId);
  if (!state) {
    return NextResponse.json({ error: 'Session not found' }, { status: 404 });
  }
  
  // Find the goal to advance
  const goals = state.goals || [];
  const goalIndex = goals.findIndex((g: Goal) => g.id === goalId);
  
  if (goalIndex === -1) {
    return NextResponse.json({ error: 'Goal not found' }, { status: 404 });
  }
  
  // Update the goal status to completed
  const goal = goals[goalIndex];
  goal.status = 'completed';
  goal.completedAt = new Date().toISOString();
  
  // Send system message announcing goal completion
  const message: IterativeMessage = {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    from: 'system',
    to: 'all',
    type: IterativeMessageType.SYSTEM_MESSAGE,
    content: { text: `Goal ${goalId} has been manually completed` },
    conversationId: sessionId,
    metadata: { sessionId, goalId }
  };
  
  // Add message to state
  state.messages = state.messages || [];
  state.messages.push(message);
  
  // Activate dependent goals
  goals.forEach((g: Goal) => {
    if (g.dependentGoals?.includes(goalId) && g.status === 'pending') {
      g.status = 'active';
      g.activationTime = new Date().toISOString();
      
      // Send notification about newly activated goal
      const activationMessage: IterativeMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: 'system',
        to: 'all',
        type: IterativeMessageType.SYSTEM_MESSAGE,
        content: { text: `Goal ${g.id} has been activated` },
        conversationId: sessionId,
        metadata: { sessionId, goalId: g.id }
      };
      
      state.messages.push(activationMessage);
      
      // Define a standard message processing function
      const systemProcessorFn = async (sid: string, msg: IterativeMessage) => {
        console.log(`Broadcasting system message to all agents`);
        return { success: true };
      };
      
      // Queue the message asynchronously
      messageBus.sendMessage(sessionId, activationMessage, systemProcessorFn).catch(console.error);
    }
  });
  
  // Update state
  state.lastUpdateTime = new Date().toISOString();
  await stateStore.set(sessionId, state);
  
  // Process the goal completion message
  // Define a processor function that will handle the message routing
  const goalProcessorFn = async (sid: string, msg: IterativeMessage) => {
    // Get the target agent(s)
    const targets = Array.isArray(msg.to) ? msg.to : [msg.to];
    
    // For direct messages to specific agents
    let responses: any[] = [];
    for (const target of targets) {
      if (target in agentMap) {
        console.log(`Routing goal completion message from ${msg.from} to ${target}`);
        // Route to the appropriate agent
        const agent = agentMap[target as keyof typeof agentMap];
        if (agent && typeof agent.processMessage === 'function') {
          try {
            const response = await agent.processMessage(sid, msg);
            if (response) responses.push(response);
          } catch (error) {
            console.error(`Error processing message by agent ${target}:`, error);
          }
        }
      }
    }
    
    return { success: true, responses };
  };

  await messageBus.sendMessage(sessionId, message, goalProcessorFn);
  
  return NextResponse.json({
    success: true,
    goal,
    state
  });
}

/**
 * Checks if a goal's dependent goals are all completed
 */
async function checkDependentGoals(sessionId: string, goalId: string): Promise<boolean> {
  const state = await stateStore.get(sessionId);
  if (!state || !state.goals) return false;
  
  const goal = state.goals.find((g: Goal) => g.id === goalId);
  if (!goal || !goal.dependentGoals || goal.dependentGoals.length === 0) return true;
  
  // Check if all dependent goals are completed
  const allDependenciesCompleted = goal.dependentGoals.every(dependentId => {
    const dependentGoal = state.goals?.find((g: Goal) => g.id === dependentId);
    return dependentGoal && dependentGoal.status === 'completed';
  });
  
  if (allDependenciesCompleted && goal.status === 'pending') {
    // Automatically complete this goal
    goal.status = 'completed';
    goal.completedAt = new Date().toISOString();
    
    // Notify about automatic completion
    const systemMessage: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'system',
      to: 'all',
      type: IterativeMessageType.SYSTEM_MESSAGE,
      content: { text: `Goal ${goalId} has been completed automatically as dependency goals are complete` },
      conversationId: sessionId,
      metadata: { sessionId, goalId }
    };
    
    // Add message to state and update
    state.messages = state.messages || [];
    state.messages.push(systemMessage);
    await stateStore.set(sessionId, state);
    
    // Process the message
    // Define a standard processor function for system messages
    const systemProcessorFn = async (sid: string, msg: IterativeMessage) => {
      console.log(`Broadcasting automatic goal completion message to all agents`);
      return { success: true };
    };
    
    await messageBus.sendMessage(sessionId, systemMessage, systemProcessorFn);
    
    return true;
  }
  
  return allDependenciesCompleted;
}
