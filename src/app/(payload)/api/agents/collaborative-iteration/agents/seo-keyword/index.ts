// src/app/(payload)/api/agents/collaborative-iteration/agents/seo-keyword/index.ts

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeMessageType,
  IterativeCollaborationState,
  Goal,
  StandardizedHandlerResult,
  AgentId,
  ArtifactStatus
} from '../../types';
import { AgentBase } from '../../core/AgentBase';
import { AgentHandler } from '../../core/AgentHandler';
import { AgentStateManager } from '../../core/AgentStateManager';
import { AgentMessaging } from '../../core/AgentMessaging';
import { stateStore } from '../../utils/stateStore';
import { ArtifactManager } from '../../utils/artifactManager';
import logger from '../../utils/logger';

// Import all handlers from a single file
import {
  handleInitialRequest,
  handleArtifactRequest,
  handleFeedback,
  handleConsultationRequest,
  handleArtifactDelivery,
  handleDiscussionStart,
  handleDiscussionContribution,
  handleDiscussionSynthesisRequest
} from './handlers';

/**
 * SEO Keyword Agent
 *
 * This agent is responsible for keyword research and analysis
 * to support content creation and SEO optimization.
 */
export class SeoKeywordAgent extends AgentBase {
  /**
   * Constructor initializes the agent with required handlers
   * and sets up message handling
   */
  constructor() {
    super('seo-keyword' as AgentId);
    logger.info('SEO Keyword Agent initialized');
  }

  /**
   * Process an incoming message
   */
  async processMessage(
    sessionId: string,
    message: IterativeMessage
  ): Promise<StandardizedHandlerResult | null> {
    // Validate parameters
    if (!sessionId || typeof sessionId !== 'string') {
      console.error(`SEO Keyword Agent: Invalid sessionId parameter: ${sessionId}`);
      return {
        success: false,
        error: `Invalid sessionId parameter: ${sessionId}`,
        message: 'Failed to process message due to invalid sessionId',
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: this.agentId,
          to: 'system',
          type: IterativeMessageType.ERROR,
          content: { error: `Invalid sessionId parameter: ${sessionId}` },
          conversationId: typeof message === 'object' && message?.conversationId ? message.conversationId : sessionId
        }
      };
    }

    // Validate message parameter
    if (!message || typeof message !== 'object') {
      console.error(`SEO Keyword Agent: Invalid message parameter: ${message}`);
      return {
        success: false,
        error: `Invalid message parameter: ${message}`,
        message: 'Failed to process message due to invalid message object',
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: this.agentId,
          to: 'system',
          type: IterativeMessageType.ERROR,
          content: { error: `Invalid message parameter: ${message}` },
          conversationId: sessionId
        }
      };
    }

    // Validate message type - ensure it's not undefined
    if (!message.type) {
      console.warn(`SEO Keyword Agent: Message type is undefined for message ${message.id} from ${message.from}`);

      // Set a default type for messages with undefined type
      message.type = 'REQUEST' as IterativeMessageType;
      console.log(`SEO Keyword Agent: Setting default message type 'REQUEST' for message ${message.id}`);
    }

    console.log(`SEO Keyword Agent: Processing message of type ${message.type} from ${message.from}`);
    return await this.handler.processMessage(sessionId, message);
  }

  /**
   * Determine if this agent should act in the current turn
   */
  async shouldAct(sessionId: string): Promise<boolean> {
    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) return false;

    // Find messages directed to this agent that haven't been processed yet
    const unprocessedMessages = state.messages?.filter(m => {
      // Check if message is directed to this agent
      const isForThisAgent =
        (typeof m.to === 'string' && (m.to === this.agentId || m.to === 'all')) ||
        (Array.isArray(m.to) && (m.to.includes(this.agentId) || m.to.includes('all')));

      // Get agent state
      const agentState = state.agentStates?.[this.agentId];

      // Check if message has been processed
      const isProcessed = agentState?.processedRequests?.includes(m.id) || false;

      return isForThisAgent && !isProcessed;
    }) || [];

    // Check if there are any active goals assigned to this agent
    const activeGoals = state.goals?.filter(g => {
      // Use type assertion to safely access dynamic properties
      const goal = g as any; // Using any to access properties that might not be in the type
      return goal.assignedTo === this.agentId &&
             goal.status === 'active';
    }) || [];

    // Act if there are unprocessed messages or active goals
    return unprocessedMessages.length > 0 || activeGoals.length > 0;
  }

  /**
   * Act on the current state
   */
  async act(sessionId: string): Promise<void> {
    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) return;

    // Find messages directed to this agent that haven't been processed yet
    const unprocessedMessages = state.messages?.filter(m => {
      // Check if message is directed to this agent
      const isForThisAgent =
        (typeof m.to === 'string' && (m.to === this.agentId || m.to === 'all')) ||
        (Array.isArray(m.to) && (m.to.includes(this.agentId) || m.to.includes('all')));

      // Get agent state
      const agentState = state.agentStates?.[this.agentId];

      // Check if message has been processed
      const isProcessed = agentState?.processedRequests?.includes(m.id) || false;

      return isForThisAgent && !isProcessed;
    }) || [];

    // Process each unprocessed message
    for (const message of unprocessedMessages) {
      await this.processMessage(sessionId, message);
    }

    // Check for goals that need to be addressed
    const activeGoals = state.goals?.filter(g => {
      // Use type assertion to safely access dynamic properties
      const goal = g as any; // Using any to access properties that might not be in the type
      return goal.assignedTo === this.agentId &&
             goal.status === 'active';
    }) || [];

    // Process each active goal
    for (const goal of activeGoals) {
      await this.processGoal(sessionId, goal);
    }
  }

  /**
   * Process a goal assigned to this agent
   */
  private async processGoal(sessionId: string, goal: Goal): Promise<void> {
    console.log(`SEO Keyword Agent: Processing goal - ${goal.name}`);

    // Create a message to handle the goal
    const goalMessage: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'orchestrator',
      to: this.agentId,
      type: IterativeMessageType.SYSTEM_MESSAGE, // Use SYSTEM_MESSAGE as GOAL_EXECUTION is not in IterativeMessageType enum
      content: {
        goalId: goal.id,
        name: goal.name,
        description: goal.description,
        parameters: {} // Goal interface doesn't have parameters property
      },
      conversationId: sessionId
    };

    // Process the goal as a message
    await this.processMessage(sessionId, goalMessage);
  }

  /**
   * Register message handlers for different message types
   * This method is called by the AgentBase constructor
   */
  protected registerHandlers(): void {
    // Register handlers for different message types
    this.handler.registerHandler(IterativeMessageType.INITIAL_REQUEST, handleInitialRequest);
    // Also handle REQUEST type with the same handler for consistent workflow messaging
    this.handler.registerHandler(IterativeMessageType.REQUEST, handleInitialRequest);
    this.handler.registerHandler(IterativeMessageType.ARTIFACT_REQUEST, handleArtifactRequest);
    this.handler.registerHandler(IterativeMessageType.FEEDBACK, handleFeedback);
    this.handler.registerHandler(IterativeMessageType.CONSULTATION_REQUEST, handleConsultationRequest);
    this.handler.registerHandler(IterativeMessageType.ARTIFACT_DELIVERY, handleArtifactDelivery);
    this.handler.registerHandler(IterativeMessageType.DISCUSSION_START, handleDiscussionStart);
    this.handler.registerHandler(IterativeMessageType.DISCUSSION_CONTRIBUTION, handleDiscussionContribution);
    this.handler.registerHandler(IterativeMessageType.DISCUSSION_SYNTHESIS_REQUEST, handleDiscussionSynthesisRequest);
    this.handler.registerHandler(IterativeMessageType.UPDATE, this.handleUpdateMessage.bind(this));

    // Add a default handler for ACKNOWLEDGMENT messages
    this.handler.registerHandler(IterativeMessageType.ACKNOWLEDGMENT, this.handleAcknowledgmentMessage.bind(this));

    // Add a default handler for SYSTEM_MESSAGE messages
    this.handler.registerHandler(IterativeMessageType.SYSTEM_MESSAGE, this.handleSystemMessage.bind(this));

    // Add a default handler for any undefined message types
    this.handler.registerHandler('undefined' as any, this.handleDefaultMessage.bind(this));
  }

  /**
   * Handle ACKNOWLEDGMENT message type
   */
  private async handleAcknowledgmentMessage(
    message: IterativeMessage,
    state: IterativeCollaborationState,
    stateManager: AgentStateManager,
    messaging: AgentMessaging
  ): Promise<StandardizedHandlerResult> {
    logger.info(`SEO Keyword Agent: Handling ACKNOWLEDGMENT message from ${message.from}`, {
      sessionId: state.id,
      messageId: message.id
    });

    // Simply acknowledge the acknowledgment
    const response = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ACKNOWLEDGMENT,
      {
        message: `SEO Keyword Agent received your acknowledgment`,
        status: 'success',
        originalMessageId: message.id
      },
      message.conversationId,
      message.id
    );

    return { response };
  }

  /**
   * Handle SYSTEM_MESSAGE message type
   */
  private async handleSystemMessage(
    message: IterativeMessage,
    state: IterativeCollaborationState,
    stateManager: AgentStateManager,
    messaging: AgentMessaging
  ): Promise<StandardizedHandlerResult> {
    logger.info(`SEO Keyword Agent: Handling SYSTEM_MESSAGE from ${message.from}`, {
      sessionId: state.id,
      messageId: message.id
    });

    // Simply acknowledge the system message
    const response = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ACKNOWLEDGMENT,
      {
        message: `SEO Keyword Agent acknowledged system message`,
        status: 'success',
        originalMessageId: message.id
      },
      message.conversationId,
      message.id
    );

    return { response };
  }

  /**
   * Handle any undefined or unregistered message types
   */
  private async handleDefaultMessage(
    message: IterativeMessage,
    state: IterativeCollaborationState,
    stateManager: AgentStateManager,
    messaging: AgentMessaging
  ): Promise<StandardizedHandlerResult> {
    logger.info(`SEO Keyword Agent: Handling default message type ${message.type} from ${message.from}`, {
      sessionId: state.id,
      messageId: message.id,
      messageType: message.type
    });

    // Simply acknowledge the message
    const response = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ACKNOWLEDGMENT,
      {
        message: `SEO Keyword Agent received your message of type ${message.type}`,
        status: 'success',
        originalMessageId: message.id
      },
      message.conversationId,
      message.id
    );

    return { response };
  }

  /**
   * Handle UPDATE message type
   */
  private async handleUpdateMessage(
    message: IterativeMessage,
    state: IterativeCollaborationState,
    stateManager: AgentStateManager,
    messaging: AgentMessaging
  ): Promise<StandardizedHandlerResult> {
    logger.info(`SEO Keyword Agent: Handling UPDATE message from ${message.from}`, {
      sessionId: state.id,
      messageId: message.id
    });

    // Simply acknowledge the update
    const response = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ACKNOWLEDGMENT,
      {
        message: `SEO Keyword Agent acknowledged the update`,
        status: 'success'
      },
      message.conversationId,
      message.id
    );

    return { response };
  }

  /**
   * Create an SEO keyword artifact using the standardized ArtifactManager
   *
   * @param sessionId The session ID
   * @param topic The topic for the keyword research
   * @param primaryKeywords Primary keywords
   * @param longTailKeywords Long-tail keywords
   * @param searchIntent Search intent analysis
   * @returns The result of creating the artifact
   */
  async createSeoKeywordArtifact(
    sessionId: string,
    topic: string,
    primaryKeywords: any[],
    longTailKeywords: any[],
    searchIntent: Record<string, any>
  ): Promise<StandardizedHandlerResult> {
    logger.info(`SEO Keyword Agent: Creating SEO keyword artifact for topic "${topic}"`, {
      sessionId,
      topic
    });

    // Create the artifact using the ArtifactManager
    const artifact = ArtifactManager.createArtifact(
      `SEO Keywords: ${topic}`,
      'seo-keywords',
      {
        primaryKeywords,
        longTailKeywords,
        searchIntent,
        semanticKeywords: primaryKeywords.map(k => k.keyword).slice(0, 5)
      },
      this.agentId,
      ArtifactStatus.COMPLETED,
      {
        topic,
        createdAt: new Date().toISOString(),
        qualityScore: 0.85
      }
    );

    // Store the artifact
    const stored = await ArtifactManager.storeArtifact(sessionId, artifact);

    if (!stored) {
      logger.error(`SEO Keyword Agent: Failed to store SEO keyword artifact for topic "${topic}"`, {
        sessionId,
        topic,
        artifactId: artifact.id
      });

      return {
        success: false,
        message: 'Failed to store SEO keyword artifact',
        error: 'Storage error',
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: this.agentId,
          to: 'system',
          type: IterativeMessageType.ERROR,
          content: { error: 'Failed to store SEO keyword artifact' },
          conversationId: sessionId
        }
      };
    }

    // Update the agent state to track the generated artifact
    await this.stateManager.trackNewArtifact(sessionId, artifact);

    // Create a response message
    const response: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: this.agentId,
      to: 'system',
      type: IterativeMessageType.ARTIFACT_DELIVERY,
      content: {
        artifactId: artifact.id,
        artifactType: 'seo-keywords',
        message: `SEO keyword research for "${topic}" completed`
      },
      conversationId: sessionId,
      artifactId: artifact.id
    };

    return {
      success: true,
      message: 'SEO keyword artifact created successfully',
      error: null,
      response,
      artifactUpdates: {
        new: {
          [artifact.id]: artifact
        }
      }
    };
  }
}

// Create and export a singleton instance
export const seoKeywordAgent = new SeoKeywordAgent();
