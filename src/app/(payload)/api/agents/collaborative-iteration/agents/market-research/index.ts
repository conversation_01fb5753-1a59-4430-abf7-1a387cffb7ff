// src/app/(payload)/api/agents/collaborative-iteration/agents/market-research/index.ts

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeCollaborationState,
  AgentId,
  StandardizedHandlerResult,
  Goal,
  IterativeMessageType,
  ArtifactStatus
} from '../../types';
import { AgentBase } from '../../core/AgentBase';
import { AgentHandler } from '../../core/AgentHandler';
import { AgentStateManager } from '../../core/AgentStateManager';
import { AgentMessaging } from '../../core/AgentMessaging';
import { stateStore } from '../../utils/stateStore';
import { ArtifactManager } from '../../utils/artifactManager';
import logger from '../../utils/logger';

// Import handlers with structured reasoning
import {
  handleMarketResearchInitialRequest,
  handleMarketResearchArtifactRequest,
  handleFeedback,
  handleConsultationRequest,
  handleArtifactDelivery,
  handleDiscussionStart,
  handleDiscussionContribution,
  handleDiscussionSynthesisRequest
} from './handlers';

// Re-export the handlers for use in the consolidated system
export {
  handleMarketResearchInitialRequest,
  handleMarketResearchArtifactRequest
};

/**
 * Market Research Agent
 *
 * This agent is responsible for conducting market research and analysis
 * to inform content strategy and keyword targeting.
 */
export class MarketResearchAgent extends AgentBase {
  /**
   * Constructor initializes the agent with enhanced handlers that use structured reasoning
   * and sets up message handling
   */
  constructor() {
    super('market-research' as AgentId);
    logger.info('Market Research Agent initialized');
  }

  /**
   * Handle incoming messages
   */
  async handleMessage(message: IterativeMessage, state: IterativeCollaborationState): Promise<StandardizedHandlerResult> {
    // Validate message type - ensure it's not undefined
    if (!message.type) {
      console.warn(`Market Research Agent: Message type is undefined for message ${message.id} from ${message.from}`);

      // Set a default type for messages with undefined type
      message.type = 'REQUEST' as IterativeMessageType;
      console.log(`Market Research Agent: Setting default message type 'REQUEST' for message ${message.id}`);
    }

    console.log(`Market Research Agent: Handling message of type ${message.type} from ${message.from}`);

    // Use the correct method from AgentHandler with error handling to ensure we never return null
    try {
      const result = await this.handler.processMessage(state.id, message);
      if (result === null) {
        // If handler returns null, provide a fallback error response
        return {
          success: false,
          error: `No handler found for message type ${message.type}`,
          message: `Market Research Agent cannot process message of type ${message.type}`,
          response: {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: this.agentId,
            to: typeof message.from === 'string' ? message.from : 'orchestrator',
            type: IterativeMessageType.ERROR,
            content: { error: `No handler registered for message type ${message.type}` },
            conversationId: message.conversationId || state.id
          }
        };
      }
      return result;
    } catch (error) {
      // Catch any errors and provide a standardized response
      console.error(`Error processing message in Market Research Agent: ${error}`);
      return {
        success: false,
        error: `Error processing message: ${error}`,
        message: 'An error occurred while processing your request',
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: this.agentId,
          to: typeof message.from === 'string' ? message.from : 'orchestrator',
          type: IterativeMessageType.ERROR,
          content: { error: `Internal error: ${error}` },
          conversationId: message.conversationId || state.id
        }
      };
    }
  }

  /**
   * Process an incoming message (legacy method)
   */
  async processMessage(
    sessionId: string,
    message: IterativeMessage
  ): Promise<StandardizedHandlerResult> {
    // Validate parameters
    if (!sessionId || typeof sessionId !== 'string') {
      console.error(`Market Research Agent: Invalid sessionId parameter: ${sessionId}`);
      return {
        success: false,
        error: `Invalid sessionId parameter: ${sessionId}`,
        message: 'Failed to process message due to invalid sessionId',
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: this.agentId,
          to: 'system',
          type: IterativeMessageType.ERROR,
          content: { error: `Invalid sessionId parameter: ${sessionId}` },
          conversationId: typeof message === 'object' && message?.conversationId ? message.conversationId : sessionId
        }
      };
    }

    // Validate message parameter
    if (!message || typeof message !== 'object') {
      console.error(`Market Research Agent: Invalid message parameter: ${message}`);
      return {
        success: false,
        error: `Invalid message parameter: ${message}`,
        message: 'Failed to process message due to invalid message object',
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: this.agentId,
          to: 'system',
          type: IterativeMessageType.ERROR,
          content: { error: `Invalid message parameter: ${message}` },
          conversationId: sessionId
        }
      };
    }

    // Validate message type - ensure it's not undefined
    if (!message.type) {
      console.warn(`Market Research Agent: Message type is undefined for message ${message.id} from ${message.from}`);

      // Set a default type for messages with undefined type
      message.type = 'REQUEST' as IterativeMessageType;
      console.log(`Market Research Agent: Setting default message type 'REQUEST' for message ${message.id}`);
    }

    console.log(`Market Research Agent: Processing message of type ${message.type} from ${message.from}`);
    const state = await stateStore.getState(sessionId);
    if (!state) {
      // Return a default error response if state is not found
      return {
        success: false,
        error: `No state found for session ${sessionId}`,
        message: 'Failed to process message due to missing state',
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: this.agentId,
          to: typeof message.from === 'string' ? message.from : 'orchestrator',
          type: IterativeMessageType.ERROR,
          content: { error: `No state found for session ${sessionId}` },
          conversationId: message.conversationId || sessionId
        }
      };
    }
    return await this.handleMessage(message, state);
  }

  /**
   * Determine if this agent should act in the current turn
   */
  async shouldAct(sessionId: string): Promise<boolean> {
    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) return false;

    // Find messages directed to this agent that haven't been processed yet
    const unprocessedMessages = state.messages?.filter(m => {
      // Check if message is directed to this agent
      const isForThisAgent =
        (typeof m.to === 'string' && (m.to === this.agentId || m.to === 'all')) ||
        (Array.isArray(m.to) && (m.to.includes(this.agentId) || m.to.includes('all')));

      // Get agent state
      const agentState = state.agentStates?.[this.agentId];

      // Check if message has been processed
      const isProcessed = agentState?.processedRequests?.includes(m.id) || false;

      return isForThisAgent && !isProcessed;
    }) || [];

    // Check if there are any active goals assigned to this agent
    const assignedGoals = state.goals?.filter(g =>
      g.agent === this.agentId &&
      g.status === 'in-progress'
    ) || [];

    // Act if there are unprocessed messages or active goals
    return unprocessedMessages.length > 0 || assignedGoals.length > 0;
  }

  /**
   * Act on the current state
   */
  async act(sessionId: string): Promise<void> {
    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) return;

    // Find messages directed to this agent that haven't been processed yet
    const unprocessedMessages = state.messages?.filter(m => {
      // Check if message is directed to this agent
      const isForThisAgent =
        (typeof m.to === 'string' && (m.to === this.agentId || m.to === 'all')) ||
        (Array.isArray(m.to) && (m.to.includes(this.agentId) || m.to.includes('all')));

      // Get agent state
      const agentState = state.agentStates?.[this.agentId];

      // Check if message has been processed
      const isProcessed = agentState?.processedRequests?.includes(m.id) || false;

      return isForThisAgent && !isProcessed;
    }) || [];

    // Process each unprocessed message
    for (const message of unprocessedMessages) {
      await this.handleMessage(message, state);
    }

    // Check for goals that need to be addressed
    const assignedGoals = state.goals?.filter(g =>
      g.agent === this.agentId &&
      g.status === 'in-progress'
    ) || [];

    // Process each active goal
    for (const goal of assignedGoals) {
      await this.processGoal(sessionId, goal);
    }
  }

  /**
   * Execute a specific goal
   */
  private async executeGoal(goal: Goal, sessionId: string): Promise<void> {
    console.log(`Market Research Agent: Executing goal ${goal.id}`);

    // Get the current state
    const state = await this.stateManager.getSessionState(sessionId);
    if (!state) return;

    // Check if the goal is still in progress
    const updatedGoal = state.goals?.find(g => g.id === goal.id);
    if (!updatedGoal || updatedGoal.status !== 'in-progress') {
      console.log(`Goal ${goal.id} is no longer in progress`);
      return;
    }
  }

  /**
   * Process goals for this agent
   */
  async processGoals(sessionId: string): Promise<void> {
    console.log(`Market Research Agent: Processing goals for session ${sessionId}`);

    const state = await this.stateManager.getSessionState(sessionId);
    if (!state || !state.goals) return;

    // Find all goals assigned to this agent
    const assignedGoals = state.goals.filter(goal =>
      goal.agent === this.agentId && goal.status === 'in-progress'
    );

    for (const goal of assignedGoals) {
      await this.executeGoal(goal, sessionId);
    }
  }

  /**
   * Process a goal assigned to this agent
   */
  private async processGoal(sessionId: string, goal: Goal): Promise<void> {
    console.log(`Market Research Agent: Processing goal ${goal.id}`);

    // Create a synthetic message to trigger the appropriate handler
    const syntheticMessage: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'orchestrator',
      to: this.agentId,
      type: IterativeMessageType.SYSTEM_MESSAGE,
      content: {
        goalId: goal.id,
        title: goal.title || 'Unknown Goal',
        description: goal.description,
        metadata: goal.metadata || {}
      },
      conversationId: sessionId
    };

    // Get the current state
    const state = await stateStore.getState(sessionId);

    // Process the goal as a message if state exists
    if (state) {
      await this.handleMessage(syntheticMessage, state);
    }
  }

  /**
   * Register message handlers for different message types
   * This method is called by the AgentBase constructor
   */
  protected registerHandlers(): void {
    // Register handlers for different message types
    this.handler.registerHandler(IterativeMessageType.INITIAL_REQUEST, handleMarketResearchInitialRequest);
    // Also handle the REQUEST type with the same handler since the workflow sends this type
    this.handler.registerHandler(IterativeMessageType.REQUEST, handleMarketResearchInitialRequest);
    this.handler.registerHandler(IterativeMessageType.ARTIFACT_REQUEST, handleMarketResearchArtifactRequest);
    this.handler.registerHandler(IterativeMessageType.FEEDBACK, handleFeedback);
    this.handler.registerHandler(IterativeMessageType.CONSULTATION_REQUEST, handleConsultationRequest);
    this.handler.registerHandler(IterativeMessageType.ARTIFACT_DELIVERY, handleArtifactDelivery);
    this.handler.registerHandler(IterativeMessageType.DISCUSSION_START, handleDiscussionStart);
    this.handler.registerHandler(IterativeMessageType.DISCUSSION_CONTRIBUTION, handleDiscussionContribution);
    this.handler.registerHandler(IterativeMessageType.DISCUSSION_SYNTHESIS_REQUEST, handleDiscussionSynthesisRequest);
    this.handler.registerHandler(IterativeMessageType.UPDATE, this.handleUpdateMessage.bind(this));

    // Add a default handler for ACKNOWLEDGMENT messages
    this.handler.registerHandler(IterativeMessageType.ACKNOWLEDGMENT, this.handleAcknowledgmentMessage.bind(this));

    // Add a default handler for SYSTEM_MESSAGE messages
    this.handler.registerHandler(IterativeMessageType.SYSTEM_MESSAGE, this.handleSystemMessage.bind(this));

    // Add a default handler for any undefined message types
    this.handler.registerHandler('undefined' as any, this.handleDefaultMessage.bind(this));
  }

  /**
   * Handle ACKNOWLEDGMENT message type
   */
  private async handleAcknowledgmentMessage(
    message: IterativeMessage,
    state: IterativeCollaborationState,
    stateManager: AgentStateManager,
    messaging: AgentMessaging
  ): Promise<StandardizedHandlerResult> {
    console.log(`Market Research Agent: Handling ACKNOWLEDGMENT message from ${message.from}`);

    // Simply acknowledge the acknowledgment
    const response = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ACKNOWLEDGMENT,
      {
        message: `Market Research Agent received your acknowledgment`,
        status: 'success',
        originalMessageId: message.id
      },
      message.conversationId,
      message.id
    );

    return { response };
  }

  /**
   * Handle SYSTEM_MESSAGE message type
   */
  private async handleSystemMessage(
    message: IterativeMessage,
    state: IterativeCollaborationState,
    stateManager: AgentStateManager,
    messaging: AgentMessaging
  ): Promise<StandardizedHandlerResult> {
    console.log(`Market Research Agent: Handling SYSTEM_MESSAGE from ${message.from}`);

    // Simply acknowledge the system message
    const response = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ACKNOWLEDGMENT,
      {
        message: `Market Research Agent acknowledged system message`,
        status: 'success',
        originalMessageId: message.id
      },
      message.conversationId,
      message.id
    );

    return { response };
  }

  /**
   * Handle any undefined or unregistered message types
   */
  private async handleDefaultMessage(
    message: IterativeMessage,
    state: IterativeCollaborationState,
    stateManager: AgentStateManager,
    messaging: AgentMessaging
  ): Promise<StandardizedHandlerResult> {
    console.log(`Market Research Agent: Handling default message type ${message.type} from ${message.from}`);

    // Simply acknowledge the message
    const response = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ACKNOWLEDGMENT,
      {
        message: `Market Research Agent received your message of type ${message.type}`,
        status: 'success',
        originalMessageId: message.id
      },
      message.conversationId,
      message.id
    );

    return { response };
  }

  /**
   * Handle UPDATE message type
   */
  private async handleUpdateMessage(
    message: IterativeMessage,
    state: IterativeCollaborationState,
    stateManager: AgentStateManager,
    messaging: AgentMessaging
  ): Promise<StandardizedHandlerResult> {
    logger.info(`Market Research Agent: Handling UPDATE message from ${message.from}`, {
      sessionId: state.id,
      messageId: message.id
    });

    // Simply acknowledge the update
    const response = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ACKNOWLEDGMENT,
      {
        message: `Market Research Agent acknowledged the update`,
        status: 'success'
      },
      message.conversationId,
      message.id
    );

    return { response };
  }

  /**
   * Create a market research artifact using the standardized ArtifactManager
   *
   * @param sessionId The session ID
   * @param topic The topic for the market research
   * @param audience Audience information
   * @param trends Market trends
   * @param competitors Competitor analysis
   * @returns The result of creating the artifact
   */
  async createMarketResearchArtifact(
    sessionId: string,
    topic: string,
    audience: any,
    trends: any,
    competitors: any
  ): Promise<StandardizedHandlerResult> {
    logger.info(`Market Research Agent: Creating market research artifact for topic "${topic}"`, {
      sessionId,
      topic
    });

    // Create the artifact using the ArtifactManager
    const artifact = ArtifactManager.createArtifact(
      `Market Research: ${topic}`,
      'market-research',
      {
        audience,
        trends,
        competitors
      },
      this.agentId,
      ArtifactStatus.COMPLETED,
      {
        topic,
        createdAt: new Date().toISOString(),
        qualityScore: 0.85
      }
    );

    // Store the artifact
    const stored = await ArtifactManager.storeArtifact(sessionId, artifact);

    if (!stored) {
      logger.error(`Market Research Agent: Failed to store market research artifact for topic "${topic}"`, {
        sessionId,
        topic,
        artifactId: artifact.id
      });

      return {
        success: false,
        message: 'Failed to store market research artifact',
        error: 'Storage error',
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: this.agentId,
          to: 'system',
          type: IterativeMessageType.ERROR,
          content: { error: 'Failed to store market research artifact' },
          conversationId: sessionId
        }
      };
    }

    // Update the agent state to track the generated artifact
    await this.stateManager.trackNewArtifact(sessionId, artifact);

    // Create a response message
    const response: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: this.agentId,
      to: 'system',
      type: IterativeMessageType.ARTIFACT_DELIVERY,
      content: {
        artifactId: artifact.id,
        artifactType: 'market-research',
        message: `Market research for "${topic}" completed`
      },
      conversationId: sessionId,
      artifactId: artifact.id
    };

    return {
      success: true,
      message: 'Market research artifact created successfully',
      error: null,
      response,
      artifactUpdates: {
        new: {
          [artifact.id]: artifact
        }
      }
    };
  }
}

// Create and export a singleton instance
export const marketResearchAgent = new MarketResearchAgent();
