// src/app/(payload)/api/agents/collaborative-iteration/agents/content-generation/index.ts

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeCollaborationState,
  AgentId,
  StandardizedHandlerResult,
  Goal,
  IterativeMessageType
} from '../../types';
import { AgentBase } from '../../core/AgentBase';
import { AgentHandler } from '../../core/AgentHandler';
import { AgentStateManager } from '../../core/AgentStateManager';
import { AgentMessaging } from '../../core/AgentMessaging';
import { stateStore } from '../../utils/stateStore';
import logger from '../../utils/logger';

// Import all handlers from a single file
import {
  handleContentGenerationInitialRequest,
  handleContentGenerationArtifactRequest,
  handleFeedback,
  handleConsultationRequest,
  handleArtifactDelivery,
  handleDiscussionStart,
  handleDiscussionContribution,
  handleDiscussionSynthesisRequest
} from './handlers';

// Re-export the handlers for use in the consolidated system
export {
  handleContentGenerationInitialRequest,
  handleContentGenerationArtifactRequest
};

/**
 * Content Generation Agent
 *
 * This agent is responsible for generating various types of content
 * based on strategy and optimization recommendations from other agents.
 */
export class ContentGenerationAgent extends AgentBase {
  /**
   * Constructor initializes the agent with required handlers
   * and sets up message handling
   */
  constructor() {
    super('content-generation' as AgentId);
    logger.info('Content Generation Agent initialized');
  }

  /**
   * Process a goal assigned to this agent
   * Override the base implementation to handle specific goal types
   */
  protected async processGoal(sessionId: string, goal: Goal): Promise<void> {
    logger.info(`Content Generation Agent: Processing goal - ${goal.name}`, {
      sessionId,
      goalId: goal.id,
      agent: this.agentId
    });

    // Get the current state to access topic and other metadata
    const state = await stateStore.getState(sessionId);

    try {
      // Different handling based on goal type
      if (goal.type === 'generate-content') {
        // Create a message to handle the goal
        const goalMessage: IterativeMessage = {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: 'orchestrator',
          to: this.agentId,
          type: IterativeMessageType.INITIAL_REQUEST,
          content: {
            goalId: goal.id,
            name: goal.name,
            description: goal.description,
            parameters: goal.metadata || {}
          },
          conversationId: sessionId
        };

        // Process the message
        await this.processMessage(sessionId, goalMessage);

        // Mark the goal as completed and update workflow progress
        await stateStore.updateState(sessionId, (currentState) => {
          const goals = currentState.goals || [];
          const goalIndex = goals.findIndex(g => g.id === goal.id);
          if (goalIndex >= 0) {
            goals[goalIndex].status = 'completed';
            goals[goalIndex].completedAt = new Date().toISOString();
          }
          return {
            ...currentState,
            goals,
            workflowProgress: {
              ...currentState.workflowProgress,
              contentGenerationComplete: true
            }
          };
        });

        // Explicitly trigger content generation validation to move to the next phase
        // But only if it hasn't been validated yet
        try {
          // Get the latest state to check if content generation is already complete
          const currentState = await stateStore.getState(sessionId);

          if (!currentState?.workflowProgress?.contentGenerationComplete) {
            logger.info(`Triggering content generation validation for session ${sessionId}`, {
              sessionId,
              goalId: goal.id,
              timestamp: new Date().toISOString()
            });

            const { validateContentGeneration } = require('../../workflows/content-generation-workflow');

            // Execute validation immediately instead of with setTimeout
            await validateContentGeneration(
              sessionId,
              state.topic,
              state.contentType,
              state.targetAudience,
              state.tone,
              {}
            );

            logger.info(`Content generation validation completed for session ${sessionId}`, {
              sessionId,
              goalId: goal.id,
              timestamp: new Date().toISOString()
            });
          } else {
            logger.info(`Skipping content generation validation as it's already complete for session ${sessionId}`, {
              sessionId,
              goalId: goal.id,
              workflowProgress: currentState.workflowProgress
            });
          }
        } catch (error) {
          logger.error(`Error triggering content generation validation`, {
            sessionId,
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined
          });
        }
      } else {
        // For other goal types, use the default implementation
        await super.processGoal(sessionId, goal);
      }
    } catch (error) {
      logger.error(`Error processing goal ${goal.id}:`, {
        sessionId,
        goalId: goal.id,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });

      // Mark the goal as blocked
      await stateStore.updateState(sessionId, (currentState) => {
        const goals = currentState.goals || [];
        const goalIndex = goals.findIndex(g => g.id === goal.id);
        if (goalIndex >= 0) {
          goals[goalIndex].status = 'blocked';
          goals[goalIndex].blockReason = error instanceof Error ? error.message : String(error);
        }
        return {
          ...currentState,
          goals
        };
      });
    }
  }

  /**
   * Register message handlers for different message types
   * Implementation of the abstract method from AgentBase
   */
  protected registerHandlers(): void {
    // Register handlers for different message types
    this.handler.registerHandler(IterativeMessageType.INITIAL_REQUEST, handleContentGenerationInitialRequest);
    // Also handle REQUEST type with the same handler for consistent workflow messaging
    this.handler.registerHandler(IterativeMessageType.REQUEST, handleContentGenerationInitialRequest);
    this.handler.registerHandler(IterativeMessageType.ARTIFACT_REQUEST, handleContentGenerationArtifactRequest);
    this.handler.registerHandler(IterativeMessageType.FEEDBACK, handleFeedback);
    this.handler.registerHandler(IterativeMessageType.CONSULTATION_REQUEST, handleConsultationRequest);
    this.handler.registerHandler(IterativeMessageType.ARTIFACT_DELIVERY, handleArtifactDelivery);
    this.handler.registerHandler(IterativeMessageType.DISCUSSION_START, handleDiscussionStart);
    this.handler.registerHandler(IterativeMessageType.DISCUSSION_CONTRIBUTION, handleDiscussionContribution);
    this.handler.registerHandler(IterativeMessageType.DISCUSSION_SYNTHESIS_REQUEST, handleDiscussionSynthesisRequest);
    this.handler.registerHandler(IterativeMessageType.UPDATE, this.handleUpdateMessage.bind(this));
  }

  /**
   * Handle UPDATE message type
   */
  private async handleUpdateMessage(
    message: IterativeMessage,
    state: IterativeCollaborationState,
    stateManager: AgentStateManager,
    messaging: AgentMessaging
  ): Promise<StandardizedHandlerResult> {
    logger.info(`Content Generation Agent: Handling UPDATE message from ${message.from}`, {
      messageId: message.id,
      from: message.from
    });

    // Simply acknowledge the update
    const response = await messaging.send(
      state.id,
      message.from as AgentId,
      IterativeMessageType.ACKNOWLEDGMENT,
      {
        message: `Content Generation Agent acknowledged the update`,
        status: 'success'
      },
      message.conversationId,
      message.id
    );

    return {
      success: true,
      message: 'Update acknowledged',
      error: null,
      response
    };
  }
}

// Create and export a singleton instance
export const contentGenerationAgent = new ContentGenerationAgent();
