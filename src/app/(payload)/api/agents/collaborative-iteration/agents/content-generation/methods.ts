// src/app/(payload)/api/agents/collaborative-iteration/agents/content-generation/methods.ts

import { v4 as uuidv4 } from 'uuid';
import OpenAI from 'openai';
import {
  IterativeArtifact,
  AgentId,
  EnhancedReasoning
} from '../../types';

// Initialize OpenAI if API key is available
const openai = process.env.OPENAI_API_KEY ? new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
}) : null;

/**
 * Generate content based on the given parameters
 */
export async function generateContent(
  contentType: string,
  topic: string,
  keywords: string[] = [],
  targetAudience?: string,
  tone?: string,
  length?: string | number,
  strategy?: any
): Promise<{
  title: string;
  content: string;
  sections: { title: string; content: string }[];
  summary: string;
  wordCount: number;
  qualityScore: number;
}> {
  console.log(`Generating ${contentType} content about ${topic}`);

  // If OpenAI API key isn't available, return mock content
  if (!openai) {
    return getMockContent(contentType, topic, keywords, targetAudience, tone, length);
  }

  try {
    // Determine word count based on content type and length parameter
    let targetWordCount = 500; // Default

    if (typeof length === 'number') {
      targetWordCount = length;
    } else if (typeof length === 'string') {
      switch (length.toLowerCase()) {
        case 'short':
          targetWordCount = 300;
          break;
        case 'medium':
          targetWordCount = 800;
          break;
        case 'long':
          targetWordCount = 1500;
          break;
        default:
          // Try to parse the string as a number
          const parsedLength = parseInt(length);
          if (!isNaN(parsedLength)) {
            targetWordCount = parsedLength;
          }
      }
    } else {
      // Use defaults based on content type
      switch (contentType.toLowerCase()) {
        case 'blog-post':
          targetWordCount = 1000;
          break;
        case 'article':
          targetWordCount = 1500;
          break;
        case 'product-description':
          targetWordCount = 300;
          break;
        case 'social-post':
          targetWordCount = 100;
          break;
        case 'newsletter':
          targetWordCount = 800;
          break;
        case 'email':
          targetWordCount = 400;
          break;
      }
    }

    // Build the prompt based on available parameters
    let prompt = `Generate a high-quality ${contentType} about ${topic}.`;

    if (keywords && keywords.length > 0) {
      prompt += ` Include these keywords naturally: ${keywords.join(', ')}.`;
    }

    if (targetAudience) {
      prompt += ` The target audience is: ${targetAudience}.`;
    }

    if (tone) {
      prompt += ` Use a ${tone} tone.`;
    }

    prompt += ` Create approximately ${targetWordCount} words of content.`;

    // Add strategy guidelines if available
    if (strategy) {
      let strategyGuidelines = '';

      if (typeof strategy === 'string') {
        strategyGuidelines = strategy;
      } else if (strategy.points) {
        strategyGuidelines = Array.isArray(strategy.points) ? strategy.points.join('\n- ') : strategy.points;
      } else if (strategy.guidelines) {
        strategyGuidelines = Array.isArray(strategy.guidelines) ? strategy.guidelines.join('\n- ') : strategy.guidelines;
      } else if (strategy.content) {
        strategyGuidelines = typeof strategy.content === 'string' ? strategy.content : JSON.stringify(strategy.content);
      }

      if (strategyGuidelines) {
        prompt += `\n\nFollow these content strategy guidelines:\n- ${strategyGuidelines}`;
      }
    }

    // Use OpenAI to generate content
    const completion = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: `You are an expert content creator specializing in ${contentType} creation. Your task is to generate high-quality, engaging content that meets the specifications provided. Please structure your response with the following sections: TITLE, CONTENT (with appropriate headings and paragraphs), and SUMMARY.`
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: Math.min(4000, targetWordCount * 2.5),
    });

    const generatedText = completion.choices[0].message.content || '';

    // Parse the response to extract title, content, and summary
    const titleMatch = generatedText.match(/TITLE:?\s*(.*?)(?=\n\n|CONTENT:)/is);
    const contentMatch = generatedText.match(/CONTENT:?\s*([\s\S]*?)(?=\n\nSUMMARY:|$)/is);
    const summaryMatch = generatedText.match(/SUMMARY:?\s*([\s\S]*?)$/is);

    const title = titleMatch ? titleMatch[1].trim() : `${contentType} about ${topic}`;
    const content = contentMatch ? contentMatch[1].trim() : generatedText;
    const summary = summaryMatch ? summaryMatch[1].trim() : `${contentType} about ${topic}`;

    // Parse sections based on headings in the content
    const sectionRegex = /#+\s+(.*?)(?=\n)([\s\S]*?)(?=\n#+\s+|$)/g;
    const sections: { title: string; content: string }[] = [];

    let match;
    while ((match = sectionRegex.exec(content)) !== null) {
      sections.push({
        title: match[1].trim(),
        content: match[2].trim()
      });
    }

    // If no sections were found, create one with the entire content
    if (sections.length === 0) {
      sections.push({
        title: 'Main Content',
        content
      });
    }

    // Calculate word count
    const wordCount = content.split(/\s+/).length;

    // Calculate a rough quality score (70-95 range)
    const qualityScore = calculateQualityScore(content, keywords, wordCount, targetWordCount);

    return {
      title,
      content,
      sections,
      summary,
      wordCount,
      qualityScore
    };
  } catch (error) {
    console.error('Error generating content:', error);
    return getMockContent(contentType, topic, keywords, targetAudience, tone, length);
  }
}

/**
 * Refine content based on feedback
 */
export async function refineContent(
  originalContent: string,
  feedback: string,
  keywords: string[] = [],
  targetAudience?: string,
  tone?: string
): Promise<{
  title: string;
  content: string;
  sections: { title: string; content: string }[];
  summary: string;
  wordCount: number;
  qualityScore: number;
  changes: string;
}> {
  console.log('Refining content based on feedback');

  // If OpenAI API key isn't available, return slightly modified content
  if (!openai) {
    return getMockRefinedContent(originalContent, feedback, keywords);
  }

  try {
    // Extract title from original content if it starts with a heading
    const titleMatch = originalContent.match(/^#\s+(.*?)(?=\n)/);
    const originalTitle = titleMatch ? titleMatch[1].trim() : '';

    // Build the prompt for content refinement
    let prompt = `Refine the following content based on this feedback: "${feedback}"\n\nOriginal Content:\n${originalContent}`;

    if (keywords && keywords.length > 0) {
      prompt += `\n\nMake sure to include these keywords naturally: ${keywords.join(', ')}.`;
    }

    if (targetAudience) {
      prompt += `\n\nThe target audience is: ${targetAudience}.`;
    }

    if (tone) {
      prompt += `\n\nUse a ${tone} tone.`;
    }

    // Use OpenAI to refine the content
    const completion = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are an expert content editor who refines content based on feedback while maintaining its core message and improving its quality. Structure your response with the following sections: TITLE, CONTENT, SUMMARY, and CHANGES (explaining the changes you've made)."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 4000,
    });

    const refinedText = completion.choices[0].message.content || '';

    // Parse the response to extract title, content, summary, and changes
    const titleMatch2 = refinedText.match(/TITLE:?\s*(.*?)(?=\n\n|CONTENT:)/is);
    const contentMatch = refinedText.match(/CONTENT:?\s*([\s\S]*?)(?=\n\nSUMMARY:|$)/is);
    const summaryMatch = refinedText.match(/SUMMARY:?\s*([\s\S]*?)(?=\n\nCHANGES:|$)/is);
    const changesMatch = refinedText.match(/CHANGES:?\s*([\s\S]*?)$/is);

    const title = titleMatch2 ? titleMatch2[1].trim() : originalTitle || 'Refined Content';
    const content = contentMatch ? contentMatch[1].trim() : refinedText;
    const summary = summaryMatch ? summaryMatch[1].trim() : 'Refined content based on feedback';
    const changes = changesMatch ? changesMatch[1].trim() : 'Content refined based on feedback';

    // Parse sections based on headings in the content
    const sectionRegex = /#+\s+(.*?)(?=\n)([\s\S]*?)(?=\n#+\s+|$)/g;
    const sections: { title: string; content: string }[] = [];

    let match;
    while ((match = sectionRegex.exec(content)) !== null) {
      sections.push({
        title: match[1].trim(),
        content: match[2].trim()
      });
    }

    // If no sections were found, create one with the entire content
    if (sections.length === 0) {
      sections.push({
        title: 'Main Content',
        content
      });
    }

    // Calculate word count
    const wordCount = content.split(/\s+/).length;

    // Calculate quality score (75-98 range for refined content)
    const baseQualityScore = calculateQualityScore(content, keywords, wordCount);
    const qualityScore = Math.min(98, baseQualityScore + 5); // Refinement should improve quality

    return {
      title,
      content,
      sections,
      summary,
      wordCount,
      qualityScore,
      changes
    };
  } catch (error) {
    console.error('Error refining content:', error);
    return getMockRefinedContent(originalContent, feedback, keywords);
  }
}

/**
 * Generate consultation response for content questions
 */
export async function generateContentConsultation(
  question: string,
  context: Record<string, any> = {},
  contentToAnalyze: string = ''
): Promise<{
  response: string;
  suggestions: Array<{
    area: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
  }>;
}> {
  console.log('Generating content consultation response');

  if (!question) {
    throw new Error('No question provided for content consultation');
  }

  // If OpenAI API key isn't available, return a mock consultation
  if (!openai) {
    return getMockContentConsultation(question);
  }

  try {
    // Use OpenAI to generate a consultation response
    const messages = [
      {
        role: "system",
        content: "You are an expert content creator providing guidance to others. Provide detailed, specific, and actionable advice for the question."
      },
      {
        role: "user",
        content: `Provide content creation consultation for this question: "${question}"\n\nContext: ${JSON.stringify(context)}`
      }
    ];

    // Add content to analyze if provided
    if (contentToAnalyze) {
      messages.push({
        role: "user",
        content: `Here's the content to consider in your response:\n\n${contentToAnalyze.substring(0, 4000)}`
      });
    }

    const completion = await openai.chat.completions.create({
      model: "gpt-4o",
      messages,
      temperature: 0.7,
      max_tokens: 1000,
    });

    const consultationResponse = completion.choices[0].message.content || '';

    // Extract response and suggestions
    const responseMatch = consultationResponse.match(/Response:([\s\S]*?)(?=Suggestions:|$)/i);
    const suggestionsMatch = consultationResponse.match(/Suggestions:([\s\S]*?)$/i);

    const response = responseMatch ? responseMatch[1].trim() : consultationResponse;
    const suggestionsContent = suggestionsMatch ? suggestionsMatch[1].trim() : '';

    // Parse suggestions
    const suggestionRegex = /(\d+\.|\-)\s*(?:Area:\s*([^,]+),\s*)?Suggestion:\s*([^,]+)(?:,\s*Priority:\s*(high|medium|low))?/gi;
    const suggestions: Array<{
      area: string;
      suggestion: string;
      priority: 'high' | 'medium' | 'low';
    }> = [];

    let match;
    while ((match = suggestionRegex.exec(suggestionsContent)) !== null) {
      suggestions.push({
        area: match[2]?.trim() || 'Content Creation',
        suggestion: match[3]?.trim() || 'Improve content quality',
        priority: (match[4]?.trim() as 'high' | 'medium' | 'low') || 'medium'
      });
    }

    // If no suggestions were extracted, create some based on the response
    if (suggestions.length === 0) {
      const lines = response.split('\n').filter(line => line.trim().length > 20);

      for (let i = 0; i < Math.min(3, lines.length); i++) {
        suggestions.push({
          area: 'Content Creation',
          suggestion: lines[i].substring(0, 100).trim(),
          priority: i === 0 ? 'high' : i === 1 ? 'medium' : 'low'
        });
      }
    }

    return {
      response,
      suggestions: suggestions.length > 0 ? suggestions : [
        { area: 'Content Structure', suggestion: 'Use clear headings and subheadings', priority: 'high' },
        { area: 'Engagement', suggestion: 'Include narratives or examples to illustrate points', priority: 'medium' },
        { area: 'Quality', suggestion: 'Edit for clarity, conciseness, and correctness', priority: 'medium' }
      ]
    };
  } catch (error) {
    console.error('Error providing content consultation:', error);
    return getMockContentConsultation(question);
  }
}

/**
 * Generate feedback response for content based on received feedback
 */
export async function generateContentFeedback(
  artifact: IterativeArtifact,
  feedback: string,
  fromAgent: AgentId
): Promise<string> {
  console.log('Generating content feedback response');

  if (!artifact || !feedback) {
    throw new Error('Artifact and feedback are required for generating content feedback');
  }

  // If OpenAI API key isn't available, return a mock feedback response
  if (!openai) {
    return `Thank you for your feedback about ${artifact.name}. We'll use your suggestions to improve the content.`;
  }

  try {
    // Extract content from the artifact
    let content = '';
    if (typeof artifact.content === 'string') {
      content = artifact.content;
    } else if (artifact.content.text) {
      content = artifact.content.text;
    } else {
      content = JSON.stringify(artifact.content);
    }

    // Use OpenAI to generate a feedback response
    const completion = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are a professional content creator responding to feedback about your content. Respond in a helpful, appreciative manner."
        },
        {
          role: "user",
          content: `Generate a response to this feedback for a ${artifact.type}.\n\nFeedback from ${fromAgent}: "${feedback}"\n\nContent excerpt: ${content.substring(0, 500)}...`
        }
      ],
      temperature: 0.7,
      max_tokens: 300,
    });

    return completion.choices[0].message.content || 'Thank you for your feedback. We appreciate your insights.';
  } catch (error) {
    console.error('Error generating content feedback response:', error);
    return `Thank you for your feedback about our ${artifact.type}. We appreciate your insights and will use them to improve the content.`;
  }
}

import { assessArtifactQuality } from '../../utils/quality-assessment';
import { calculateContentQualityMetrics } from '../../utils/content-quality-metrics';
import { optimizeForSeo } from '../../utils/seo-optimization';

/**
 * Calculate a comprehensive quality score for the content using enhanced metrics
 */
function calculateQualityScore(
  content: string,
  keywords: string[] = [],
  wordCount: number,
  targetWordCount: number = 0
): number {
  try {
    // Create a mock artifact for quality assessment
    const mockArtifact = {
      id: 'temp-content-assessment',
      type: 'content',
      content: content,
      createdAt: new Date().toISOString(),
      createdBy: 'content-generation',
      status: 'completed'
    };

    // Perform comprehensive quality assessment
    const qualityAssessment = assessArtifactQuality(mockArtifact, keywords);

    // Calculate content quality metrics for additional insights
    const contentMetrics = calculateContentQualityMetrics(mockArtifact, keywords);

    // Use the overall score from quality assessment (0-1 scale) and convert to 0-100 scale
    let score = Math.round(qualityAssessment.overallScore * 100);

    // Adjust for target word count if specified
    if (targetWordCount > 0) {
      // If within 10% of target, give bonus points
      const percentDifference = Math.abs(wordCount - targetWordCount) / targetWordCount;
      if (percentDifference <= 0.1) {
        score += 5;
      } else if (percentDifference <= 0.2) {
        score += 3;
      } else if (percentDifference <= 0.3) {
        score += 1;
      }
    }

    // Use content quality metrics for additional adjustments

    // Structure factors from content metrics
    if (contentMetrics.structure.structureScore > 0.8) {
      score += 5;
    } else if (contentMetrics.structure.structureScore > 0.6) {
      score += 3;
    }

    // Readability factors from content metrics
    if (contentMetrics.readability.fleschKincaidScore >= 60 && contentMetrics.readability.fleschKincaidScore <= 80) {
      score += 5; // Optimal readability (grade 7-8 level)
    } else if (contentMetrics.readability.fleschKincaidScore > 80) {
      score += 3; // Very easy to read (grade 6 level or below)
    }

    // Engagement factors from content metrics
    if (contentMetrics.engagement.engagementScore > 0.7) {
      score += 5;
    } else if (contentMetrics.engagement.engagementScore > 0.5) {
      score += 3;
    }

    // SEO factors from content metrics
    if (contentMetrics.seo.seoScore > 0.7) {
      score += 5;
    } else if (contentMetrics.seo.seoScore > 0.5) {
      score += 3;
    }

    // Cap the score at 100
    return Math.min(100, Math.max(0, score));
  } catch (error) {
    console.error('Error calculating enhanced quality score:', error);

    // Fallback to basic quality assessment if enhanced metrics fail

    // Structure factors
    const paragraphs = content.split(/\n\s*\n/).length;
    if (paragraphs >= 3 && paragraphs <= 5) {
      score += 3;
    } else if (paragraphs > 5) {
      score += 5;
    }

    const headings = (content.match(/#+\s+/g) || []).length;
    if (headings >= 1 && headings <= 3) {
      score += 3;
    } else if (headings > 3) {
      score += 5;
    }

    // Keyword usage
    if (keywords.length > 0) {
      let keywordsFound = 0;
      const contentLower = content.toLowerCase();

      for (const keyword of keywords) {
        if (contentLower.includes(keyword.toLowerCase())) {
          keywordsFound++;
        }
      }

      const keywordPercentage = keywordsFound / keywords.length;
      score += Math.floor(keywordPercentage * 10);
    }

    // Readability factors (simple approximation)
    const sentences = content.split(/[.!?]+\s/).length;
    const avgWordsPerSentence = wordCount / sentences;

    if (avgWordsPerSentence >= 10 && avgWordsPerSentence <= 20) {
      score += 5;
    } else if (avgWordsPerSentence < 10 || avgWordsPerSentence <= 25) {
      score += 3;
    }

    // Ensure score is in the acceptable range
    return Math.max(70, Math.min(95, score));
}

/**
 * Mock content when OpenAI is not available
 */
function getMockContent(
  contentType: string,
  topic: string,
  keywords: string[] = [],
  targetAudience?: string,
  tone?: string,
  length?: string | number
): {
  title: string;
  content: string;
  sections: { title: string; content: string }[];
  summary: string;
  wordCount: number;
  qualityScore: number;
} {
  const keywordText = keywords.length > 0 ? ` incorporating keywords like ${keywords.join(', ')}` : '';
  const audienceText = targetAudience ? ` targeted at ${targetAudience}` : '';
  const toneText = tone ? ` with a ${tone} tone` : '';

  const title = `${topic.charAt(0).toUpperCase() + topic.slice(1)}: A Comprehensive Guide`;

  const intro = `# ${title}\n\nThis ${contentType} explores the fascinating topic of ${topic}${keywordText}${audienceText}${toneText}. We'll examine the key aspects that make ${topic} important and provide valuable insights for readers.\n\n`;

  const section1 = `## Understanding ${topic}\n\n${topic} has become increasingly important in today's world. Many experts believe that mastering this subject can lead to significant advantages in various fields. The core concepts involve detailed analysis and strategic application of principles.\n\n`;

  const section2 = `## Key Benefits of ${topic}\n\n- Improved understanding of related concepts\n- Enhanced ability to navigate complex scenarios\n- Better decision-making capabilities\n- Increased efficiency in relevant tasks\n- Long-term advantages in the field\n\n`;

  const section3 = `## Practical Applications\n\nThe practical applications of ${topic} span across multiple domains. In professional settings, it can be leveraged to improve productivity and outcomes. In personal contexts, it enables better life choices and enhanced experiences.\n\n`;

  const conclusion = `## Conclusion\n\nIn summary, ${topic} represents a valuable area of study and application. By understanding its core principles and implementing best practices, you can gain significant advantages in relevant contexts. Continue exploring this fascinating subject to deepen your knowledge and expertise.`;

  const content = intro + section1 + section2 + section3 + conclusion;

  const sections = [
    { title: 'Understanding ' + topic, content: section1.replace(`## Understanding ${topic}\n\n`, '') },
    { title: 'Key Benefits of ' + topic, content: section2.replace(`## Key Benefits of ${topic}\n\n`, '') },
    { title: 'Practical Applications', content: section3.replace('## Practical Applications\n\n', '') },
    { title: 'Conclusion', content: conclusion.replace('## Conclusion\n\n', '') }
  ];

  const summary = `This ${contentType} provides a comprehensive overview of ${topic}, including its key concepts, benefits, and practical applications. It serves as a valuable resource for anyone interested in understanding and applying ${topic} in various contexts.`;

  const wordCount = content.split(/\s+/).length;

  return {
    title,
    content,
    sections,
    summary,
    wordCount,
    qualityScore: 75
  };
}

/**
 * Mock refined content when OpenAI is not available
 */
function getMockRefinedContent(
  originalContent: string,
  feedback: string,
  keywords: string[] = []
): {
  title: string;
  content: string;
  sections: { title: string; content: string }[];
  summary: string;
  wordCount: number;
  qualityScore: number;
  changes: string;
} {
  // Extract title from original content if it starts with a heading
  const titleMatch = originalContent.match(/^#\s+(.*?)(?=\n)/);
  const title = titleMatch ? titleMatch[1].trim() : 'Refined Content';

  // Make some simple modifications to the original content
  const keywordInsertions = keywords.length > 0
    ? `\n\nThis content includes important keywords such as ${keywords.join(', ')} that enhance its relevance.`
    : '';

  // Split content into paragraphs and modify the first one
  const paragraphs = originalContent.split(/\n\n/);
  if (paragraphs.length > 0) {
    paragraphs[0] = `${paragraphs[0]} (Refined based on feedback)`;
  }

  // Add a note about the feedback
  const feedbackNote = `\n\n## Note on Improvements\n\nThis content has been refined based on feedback that suggested: "${feedback}". The improvements enhance clarity, relevance, and overall quality.`;

  // Recombine the content
  const content = paragraphs.join('\n\n') + keywordInsertions + feedbackNote;

  // Parse sections based on headings in the content
  const sectionRegex = /#+\s+(.*?)(?=\n)([\s\S]*?)(?=\n#+\s+|$)/g;
  const sections: { title: string; content: string }[] = [];

  let match;
  while ((match = sectionRegex.exec(content)) !== null) {
    sections.push({
      title: match[1].trim(),
      content: match[2].trim()
    });
  }

  // If no sections were found, create one with the entire content
  if (sections.length === 0) {
    sections.push({
      title: 'Main Content',
      content
    });
  }

  const summary = `This content has been refined based on feedback, with improvements to clarity, structure, and relevance. The refinements address specific concerns while maintaining the core message.`;

  const wordCount = content.split(/\s+/).length;

  const changes = `Based on the feedback: "${feedback}", the following changes were made:
1. Enhanced clarity in the introduction
2. Improved structure throughout the content
3. Added relevant keywords for better context
4. Addressed specific feedback points
5. Added a note about the improvements made`;

  return {
    title,
    content,
    sections,
    summary,
    wordCount,
    qualityScore: 85,
    changes
  };
}

/**
 * Mock consultation response when OpenAI is not available
 */
function getMockContentConsultation(question: string): {
  response: string;
  suggestions: Array<{
    area: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
  }>;
} {
  return {
    response: `Regarding your question about ${question.substring(0, 50)}..., effective content creation requires a balance of strategic planning and creative execution. Start by clearly defining your audience and objectives, then structure your content to address key pain points while maintaining engagement throughout. Use a variety of content formats to appeal to different learning preferences, and always include a clear call to action.`,

    suggestions: [
      {
        area: 'Content Structure',
        suggestion: 'Use clear headings and subheadings to organize information',
        priority: 'high'
      },
      {
        area: 'Audience Engagement',
        suggestion: 'Include stories, examples, or case studies to illustrate key points',
        priority: 'high'
      },
      {
        area: 'SEO Optimization',
        suggestion: 'Strategically integrate target keywords in titles, headings, and content',
        priority: 'medium'
      },
      {
        area: 'Content Quality',
        suggestion: 'Edit for clarity, conciseness, and correctness',
        priority: 'medium'
      },
      {
        area: 'Call to Action',
        suggestion: 'Include clear, compelling CTAs that guide readers on next steps',
        priority: 'high'
      }
    ]
  };
}
