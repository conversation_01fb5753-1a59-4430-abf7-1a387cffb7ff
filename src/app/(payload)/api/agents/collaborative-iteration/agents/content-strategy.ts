/**
 * Content Strategy Agent
 *
 * This agent is responsible for developing content strategy based on market research and keyword analysis.
 */

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeMessageType,
  IterativeArtifact,
  ArtifactStatus,
  Goal
} from '../types';
import { stateStore } from '../utils/stateStore';
import logger from '../utils/logger';
import { handleContentStrategyInitialRequest, handleContentStrategyArtifactDelivery } from './index';

/**
 * Content Strategy Agent class
 */
export class ContentStrategyAgent {
  private agentId = 'content-strategy';

  /**
   * Process a message sent to this agent
   */
  async processMessage(sessionId: string, message: IterativeMessage): Promise<any> {
    logger.info(`Content Strategy Agent processing message of type ${message.type}`, {
      sessionId,
      messageId: message.id,
      messageType: message.type
    });

    try {
      // Get the current state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        logger.error(`Session ${sessionId} not found`, { sessionId });
        return null;
      }

      // Create a simple state manager for the handlers
      const stateManager = {
        addArtifact: async (sessionId: string, artifact: IterativeArtifact) => {
          return await stateStore.updateState(sessionId, (state) => {
            if (!state.artifacts) {
              state.artifacts = {};
            }
            state.artifacts[artifact.id] = artifact;

            // Also add to generatedArtifacts array if it exists
            if (state.generatedArtifacts) {
              state.generatedArtifacts.push(artifact.id);
            } else {
              state.generatedArtifacts = [artifact.id];
            }

            return state;
          });
        },
        updateArtifact: async (sessionId: string, artifactId: string, updateFn: (artifact: IterativeArtifact) => IterativeArtifact) => {
          return await stateStore.updateState(sessionId, (state) => {
            if (state.artifacts && state.artifacts[artifactId]) {
              state.artifacts[artifactId] = updateFn(state.artifacts[artifactId]);
            }
            return state;
          });
        }
      };

      // Create a simple messaging utility for the handlers
      const messaging = {
        send: async (
          sessionId: string,
          to: string,
          type: IterativeMessageType,
          content: any,
          conversationId?: string,
          inReplyTo?: string,
          artifactId?: string
        ) => {
          const message: IterativeMessage = {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: this.agentId,
            to,
            type,
            content,
            conversationId: conversationId || sessionId,
            inReplyTo,
            artifactId
          };

          // Store the message in the state
          await stateStore.updateState(sessionId, (state) => {
            if (!state.messages) {
              state.messages = [];
            }
            state.messages.push(message);
            return state;
          });

          return message;
        }
      };

      // Handle different message types
      switch (message.type) {
        case IterativeMessageType.INITIAL_REQUEST:
        case IterativeMessageType.REQUEST:
          return await handleContentStrategyInitialRequest(message, state, stateManager, messaging);

        case IterativeMessageType.ARTIFACT_REQUEST:
          return await this.handleArtifactRequest(sessionId, message, state);

        case IterativeMessageType.DISCUSSION_START:
          return await this.handleDiscussionStart(sessionId, message, state);

        case IterativeMessageType.FEEDBACK:
          // Import the handleFeedback function from the handlers file
          const { handleFeedback } = await import('./content-strategy/handlers');
          return await handleFeedback(message, state, stateManager, messaging);

        default:
          logger.warn(`Content Strategy Agent: Unhandled message type ${message.type}`, {
            sessionId,
            messageType: message.type
          });
          return null;
      }
    } catch (error) {
      logger.error(`Error processing message in Content Strategy Agent`, {
        sessionId,
        messageId: message.id,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * Act on the current state of the session
   * This method is called by the workflow orchestrator
   */
  async act(sessionId: string): Promise<boolean> {
    logger.info(`Content Strategy Agent acting on session ${sessionId}`, { sessionId });

    try {
      // Get the current state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        logger.error(`Session ${sessionId} not found`, { sessionId });
        return false;
      }

      // Find active goals assigned to this agent
      const activeGoals = (state.goals || []).filter((goal: Goal) =>
        goal.status === 'active' && goal.assignedTo === this.agentId
      );

      if (activeGoals.length === 0) {
        logger.info(`No active goals for Content Strategy Agent in session ${sessionId}`, { sessionId });
        return false;
      }

      // Process each active goal
      for (const goal of activeGoals) {
        logger.info(`Content Strategy Agent processing goal ${goal.id}`, {
          sessionId,
          goalId: goal.id,
          goalName: goal.name
        });

        // Create a message for this goal
        const message: IterativeMessage = {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: 'system',
          to: this.agentId,
          type: IterativeMessageType.INITIAL_REQUEST,
          content: {
            topic: state.topic,
            contentType: state.contentType,
            targetAudience: state.targetAudience,
            tone: state.tone,
            ...goal.metadata
          },
          conversationId: sessionId,
          goalId: goal.id
        };

        // Process the message
        const result = await this.processMessage(sessionId, message);

        if (result && result.success) {
          // Mark the goal as completed
          await stateStore.updateState(sessionId, (state) => {
            const goalIndex = state.goals.findIndex((g: Goal) => g.id === goal.id);
            if (goalIndex !== -1) {
              state.goals[goalIndex].status = 'completed';
              state.goals[goalIndex].completedAt = new Date().toISOString();
            }
            return state;
          });

          logger.info(`Content Strategy Agent completed goal ${goal.id}`, {
            sessionId,
            goalId: goal.id,
            goalName: goal.name
          });

          // Explicitly trigger the transition to content generation phase
          try {
            logger.info(`Content Strategy Agent: Explicitly triggering transition to content generation phase`, {
              sessionId,
              goalId: goal.id,
              timestamp: new Date().toISOString()
            });

            // Import the validateContentStrategy function
            const { validateContentStrategy } = require('../workflows/research-phase-workflow');

            // Call validateContentStrategy directly
            setTimeout(() => {
              validateContentStrategy(
                sessionId,
                state.topic,
                state.contentType,
                state.targetAudience,
                state.tone,
                goal.metadata || {}
              ).catch((err: Error) => {
                logger.error(`Error validating content strategy after goal completion`, {
                  sessionId,
                  goalId: goal.id,
                  error: err.message || String(err),
                  stack: err.stack
                });
              });
            }, 1000);
          } catch (transitionError) {
            logger.error(`Error triggering transition to content generation phase`, {
              sessionId,
              goalId: goal.id,
              error: transitionError instanceof Error ? transitionError.message : String(transitionError),
              stack: transitionError instanceof Error ? transitionError.stack : undefined
            });
          }
        } else {
          logger.error(`Content Strategy Agent failed to complete goal ${goal.id}`, {
            sessionId,
            goalId: goal.id,
            goalName: goal.name,
            error: result?.message || 'Unknown error'
          });

          // Mark the goal as failed
          await stateStore.updateState(sessionId, (state) => {
            const goalIndex = state.goals.findIndex((g: Goal) => g.id === goal.id);
            if (goalIndex !== -1) {
              state.goals[goalIndex].status = 'failed';
              state.goals[goalIndex].failedAt = new Date().toISOString();
              state.goals[goalIndex].failureReason = result?.message || 'Unknown error';
            }
            return state;
          });
        }
      }

      return true;
    } catch (error) {
      logger.error(`Error in Content Strategy Agent act method`, {
        sessionId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      return false;
    }
  }

  /**
   * Handle initial request for content strategy
   */
  private async handleInitialRequest(sessionId: string, message: IterativeMessage, state: any): Promise<any> {
    logger.info(`Content Strategy Agent: Handling initial request`, { sessionId });

    // Extract request details
    const { topic, contentType, targetAudience, tone } = message.content || {};

    if (!topic) {
      return {
        success: false,
        message: 'Topic is required for content strategy',
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: this.agentId,
          to: message.from,
          type: IterativeMessageType.ERROR,
          content: {
            error: 'Missing required fields',
            message: 'Topic is required for content strategy'
          },
          conversationId: message.conversationId
        }
      };
    }

    // Find market research and keyword research artifacts if available
    let marketResearch = null;
    let keywordResearch = null;

    if (state.artifacts) {
      // Find market research artifact
      const marketResearchArtifacts = Object.values(state.artifacts)
        .filter((a: any) => a.type === 'market-research')
        .sort((a: any, b: any) => {
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        });

      if (marketResearchArtifacts.length > 0) {
        marketResearch = marketResearchArtifacts[0];
      }

      // Find keyword research artifact
      const keywordArtifacts = Object.values(state.artifacts)
        .filter((a: any) => a.type === 'seo-keywords')
        .sort((a: any, b: any) => {
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        });

      if (keywordArtifacts.length > 0) {
        keywordResearch = keywordArtifacts[0];
      }
    }

    // Generate content strategy based on available information
    const contentStrategy = {
      topic,
      contentType: contentType || 'blog-article',
      targetAudience: targetAudience || 'general audience',
      tone: tone || 'informative',
      contentGoals: [
        'Establish authority on the topic',
        'Improve search engine visibility',
        'Engage target audience',
        'Drive conversions',
        'Build brand awareness'
      ],
      contentStructure: {
        title: `The Ultimate Guide to ${topic}`,
        introduction: `An engaging introduction to ${topic} that hooks the reader and establishes the value of the content.`,
        sections: [
          {
            heading: `Understanding ${topic}`,
            content: `A comprehensive explanation of ${topic}, including its definition, importance, and key concepts.`,
            keywords: keywordResearch ? [keywordResearch.iterations[0].content.primaryKeyword] : [topic]
          },
          {
            heading: `Benefits of ${topic}`,
            content: `A detailed exploration of the benefits and advantages of ${topic} for the target audience.`,
            keywords: keywordResearch ? keywordResearch.iterations[0].content.secondaryKeywords.slice(0, 2) : [`${topic} benefits`]
          },
          {
            heading: `How to Implement ${topic}`,
            content: `Step-by-step instructions on how to implement or use ${topic} effectively.`,
            keywords: keywordResearch ? keywordResearch.iterations[0].content.longTailKeywords.slice(0, 2) : [`how to use ${topic}`]
          },
          {
            heading: `${topic} Best Practices`,
            content: `Expert tips and best practices for maximizing the value of ${topic}.`,
            keywords: keywordResearch ? keywordResearch.iterations[0].content.secondaryKeywords.slice(2, 4) : [`${topic} best practices`]
          },
          {
            heading: `Common Challenges with ${topic} and How to Overcome Them`,
            content: `A discussion of common challenges and obstacles related to ${topic}, along with practical solutions.`,
            keywords: keywordResearch ? keywordResearch.iterations[0].content.longTailKeywords.slice(2, 4) : [`${topic} challenges`]
          },
          {
            heading: `Case Studies: ${topic} in Action`,
            content: `Real-world examples and case studies demonstrating the successful application of ${topic}.`,
            keywords: keywordResearch ? keywordResearch.iterations[0].content.relatedKeywords.slice(0, 2) : [`${topic} examples`]
          },
          {
            heading: `Future Trends in ${topic}`,
            content: `An exploration of emerging trends and future developments in ${topic}.`,
            keywords: keywordResearch ? keywordResearch.iterations[0].content.relatedKeywords.slice(2, 4) : [`${topic} trends`]
          },
          {
            heading: `Conclusion`,
            content: `A summary of key points and a call to action.`,
            keywords: keywordResearch ? [keywordResearch.iterations[0].content.primaryKeyword] : [topic]
          }
        ],
        callToAction: `Learn more about how we can help you with ${topic} by contacting us today.`
      },
      contentFormat: contentType || 'blog-article',
      contentLength: '2000-2500 words',
      mediaRecommendations: [
        'Infographics illustrating key concepts',
        'Charts or graphs showing relevant statistics',
        'Images demonstrating practical applications',
        'Video tutorials for complex processes',
        'Interactive elements to increase engagement'
      ],
      distributionChannels: [
        'Company blog',
        'Email newsletter',
        'Social media platforms',
        'Industry forums',
        'Partner websites'
      ],
      keywordStrategy: keywordResearch ? {
        primaryKeyword: keywordResearch.iterations[0].content.primaryKeyword,
        secondaryKeywords: keywordResearch.iterations[0].content.secondaryKeywords,
        keywordPlacement: [
          'Title tag',
          'H1 heading',
          'First paragraph',
          'Subheadings',
          'Meta description',
          'Image alt text',
          'URL'
        ]
      } : {
        primaryKeyword: topic,
        secondaryKeywords: [`${topic} guide`, `${topic} examples`, `how to use ${topic}`],
        keywordPlacement: [
          'Title tag',
          'H1 heading',
          'First paragraph',
          'Subheadings',
          'Meta description',
          'Image alt text',
          'URL'
        ]
      },
      audienceInsights: marketResearch ? {
        demographics: marketResearch.iterations[0].content.demographics,
        interests: marketResearch.iterations[0].content.demographics.interests,
        painPoints: [
          'Lack of understanding about the topic',
          'Difficulty implementing the concepts',
          'Uncertainty about best practices',
          'Challenges measuring results'
        ]
      } : {
        demographics: {
          age: '25-45',
          gender: 'mixed',
          interests: ['technology', 'business', 'education'],
          location: 'global'
        },
        painPoints: [
          'Lack of understanding about the topic',
          'Difficulty implementing the concepts',
          'Uncertainty about best practices',
          'Challenges measuring results'
        ]
      }
    };

    // Create content strategy artifact
    const artifact: IterativeArtifact = {
      id: uuidv4(),
      name: `Content Strategy: ${topic}`,
      type: 'content-strategy',
      status: 'completed' as ArtifactStatus,
      createdBy: this.agentId,
      createdAt: new Date().toISOString(),
      currentVersion: 1,
      iterations: [
        {
          version: 1,
          timestamp: new Date().toISOString(),
          agent: this.agentId,
          content: contentStrategy,
          feedback: [],
          incorporatedConsultations: []
        }
      ],
      qualityScore: 85
    };

    // Add the artifact to the state
    if (!state.artifacts) {
      state.artifacts = {};
    }
    state.artifacts[artifact.id] = artifact;

    // Add to generatedArtifacts array if it exists
    if (state.generatedArtifacts) {
      state.generatedArtifacts.push(artifact.id);
    } else {
      state.generatedArtifacts = [artifact.id];
    }

    // Update workflow progress
    if (state.workflowProgress) {
      state.workflowProgress.contentStrategyComplete = true;
    }

    // Save the updated state
    await stateStore.setState(sessionId, state);

    // Return the response
    return {
      success: true,
      message: `Successfully generated content strategy for ${topic}`,
      response: {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: this.agentId,
        to: message.from,
        type: IterativeMessageType.ARTIFACT_DELIVERY,
        content: {
          artifact,
          message: `Generated content strategy for ${topic}`
        },
        artifactId: artifact.id,
        conversationId: message.conversationId
      }
    };
  }

  /**
   * Handle artifact request
   */
  private async handleArtifactRequest(sessionId: string, message: IterativeMessage, state: any): Promise<any> {
    logger.info(`Content Strategy Agent: Handling artifact request`, { sessionId });

    // Extract request details
    const { artifactId, artifactType } = message.content || {};

    // Find the requested artifact
    let artifact: IterativeArtifact | null = null;

    if (artifactId && state.artifacts && state.artifacts[artifactId]) {
      artifact = state.artifacts[artifactId];
    } else if (artifactType === 'content-strategy' && state.artifacts) {
      // Find the latest content strategy artifact
      const strategyArtifacts = Object.values(state.artifacts)
        .filter((a: any) => a.type === 'content-strategy' && a.createdBy === this.agentId)
        .sort((a: any, b: any) => {
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        });

      if (strategyArtifacts.length > 0) {
        artifact = strategyArtifacts[0] as IterativeArtifact;
      }
    }

    if (!artifact) {
      return {
        success: false,
        message: 'Artifact not found',
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: this.agentId,
          to: message.from,
          type: IterativeMessageType.ERROR,
          content: {
            error: 'Artifact not found',
            message: `Could not find the requested artifact`
          },
          conversationId: message.conversationId
        }
      };
    }

    // Return the artifact
    return {
      success: true,
      message: `Successfully retrieved artifact ${artifact.id}`,
      response: {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: this.agentId,
        to: message.from,
        type: IterativeMessageType.ARTIFACT_DELIVERY,
        content: {
          artifact,
          message: `Retrieved artifact ${artifact.id}`
        },
        artifactId: artifact.id,
        conversationId: message.conversationId
      }
    };
  }

  /**
   * Handle discussion start
   */
  private async handleDiscussionStart(sessionId: string, message: IterativeMessage, state: any): Promise<any> {
    logger.info(`Content Strategy Agent: Handling discussion start`, { sessionId });

    // Extract discussion details
    const { discussionId, topic, prompt } = message.content || {};

    if (!discussionId || !topic) {
      return {
        success: false,
        message: 'Discussion ID and topic are required',
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: this.agentId,
          to: message.from,
          type: IterativeMessageType.ERROR,
          content: {
            error: 'Missing required fields',
            message: 'Discussion ID and topic are required'
          },
          conversationId: message.conversationId
        }
      };
    }

    // Generate a perspective on the topic from a content strategy standpoint
    const perspective = `From a content strategy perspective, I recommend the following approach for "${topic}":

1. **Content Structure**:
   - Start with a compelling introduction that establishes the importance of ${topic}
   - Divide the content into 6-8 logical sections with clear H2 headings
   - Include a mix of educational, practical, and inspirational content
   - End with a strong conclusion and clear call-to-action

2. **Content Depth and Format**:
   - Aim for comprehensive coverage (2000-2500 words) to establish authority
   - Include practical examples, case studies, and actionable advice
   - Use a mix of content formats: text, images, infographics, and possibly video
   - Create a content hierarchy with H2 and H3 headings for easy scanning

3. **Audience Engagement**:
   - Address common pain points and questions about ${topic}
   - Use a conversational tone while maintaining expertise
   - Include interactive elements where possible (quizzes, polls, calculators)
   - Incorporate storytelling elements to make complex concepts relatable

4. **Distribution Strategy**:
   - Optimize for both search and social sharing
   - Create modular content that can be repurposed across channels
   - Plan for follow-up content to build a content ecosystem around ${topic}
   - Consider creating companion pieces (checklists, templates, guides)

This strategy balances SEO requirements with audience needs to create content that performs well and provides genuine value.`;

    // Return the perspective
    return {
      success: true,
      message: `Successfully contributed to discussion ${discussionId}`,
      response: {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: this.agentId,
        to: message.from,
        type: IterativeMessageType.DISCUSSION_CONTRIBUTION,
        content: {
          discussionId,
          perspective,
          contributor: this.agentId,
          contributionType: 'content-strategy-perspective'
        },
        conversationId: message.conversationId
      }
    };
  }
}
