/**
 * SEO Keyword Agent
 *
 * This agent is responsible for keyword research and SEO keyword strategy.
 */

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeMessageType,
  IterativeArtifact,
  ArtifactStatus,
  Goal
} from '../types';
import { stateStore } from '../utils/stateStore';
import logger from '../utils/logger';

/**
 * Handle initial request for keyword research
 */
export async function handleSeoKeywordInitialRequest(
  message: IterativeMessage,
  state: any,
  stateManager: any,
  messaging: any
): Promise<any> {
  logger.info(`SEO Keyword Agent: Handling initial request`, { sessionId: message.conversationId });

  // Extract request details
  const { topic, contentType } = message.content || {};

  if (!topic) {
    return {
      success: false,
      message: 'Topic is required for keyword research',
      response: await messaging.send(
        message.conversationId,
        message.from,
        IterativeMessageType.ERROR,
        {
          error: 'Missing required fields',
          message: 'Topic is required for keyword research'
        },
        message.conversationId,
        message.id
      )
    };
  }

  // Generate mock keyword research data
  const keywordResearch = {
    topic,
    contentType: contentType || 'blog-article',
    primaryKeyword: topic,
    secondaryKeywords: [
      `best ${topic}`,
      `${topic} guide`,
      `${topic} examples`,
      `how to use ${topic}`,
      `${topic} benefits`
    ],
    relatedKeywords: [
      `${topic} tools`,
      `${topic} strategies`,
      `${topic} for beginners`,
      `advanced ${topic}`,
      `${topic} case studies`
    ],
    longTailKeywords: [
      `how to implement ${topic} in business`,
      `best practices for ${topic}`,
      `${topic} step by step guide`,
      `${topic} vs traditional methods`,
      `${topic} for small businesses`
    ],
    keywordDifficulty: {
      [`${topic}`]: 65,
      [`best ${topic}`]: 45,
      [`${topic} guide`]: 40,
      [`how to use ${topic}`]: 35,
      [`${topic} benefits`]: 30
    },
    searchVolume: {
      [`${topic}`]: 10000,
      [`best ${topic}`]: 5000,
      [`${topic} guide`]: 3000,
      [`how to use ${topic}`]: 2500,
      [`${topic} benefits`]: 2000
    },
    searchIntent: {
      [`${topic}`]: 'informational',
      [`best ${topic}`]: 'commercial',
      [`${topic} guide`]: 'informational',
      [`how to use ${topic}`]: 'informational',
      [`${topic} benefits`]: 'commercial'
    },
    recommendations: [
      `Focus on "${topic}" as the primary keyword with a density of 1-2%`,
      `Include secondary keywords naturally throughout the content`,
      `Use long-tail keywords in headings and subheadings`,
      `Create content that addresses both informational and commercial intent`,
      `Include a FAQ section targeting common questions about ${topic}`
    ]
  };

  // Create keyword research artifact
  const artifact: IterativeArtifact = {
    id: uuidv4(),
    name: `SEO Keyword Research: ${topic}`,
    type: 'seo-keywords',
    status: 'completed' as ArtifactStatus,
    createdBy: 'seo-keyword',
    createdAt: new Date().toISOString(),
    currentVersion: 1,
    iterations: [
      {
        version: 1,
        timestamp: new Date().toISOString(),
        agent: 'seo-keyword',
        content: keywordResearch,
        feedback: [],
        incorporatedConsultations: []
      }
    ],
    qualityScore: 85,
    keywordAnalysis: {
      primary: topic,
      secondary: keywordResearch.secondaryKeywords,
      related: keywordResearch.relatedKeywords,
      volumeData: keywordResearch.searchVolume,
      difficulty: keywordResearch.keywordDifficulty,
      intent: keywordResearch.searchIntent
    }
  };

  // Add the artifact to the state
  await stateManager.addArtifact(message.conversationId, artifact);

  // Update workflow progress
  await stateStore.updateState(message.conversationId, (state) => {
    if (state.workflowProgress) {
      state.workflowProgress.keywordResearchComplete = true;
    }
    return state;
  });

  // Return the response
  return {
    success: true,
    message: `Successfully generated keyword research for ${topic}`,
    response: await messaging.send(
      message.conversationId,
      message.from,
      IterativeMessageType.ARTIFACT_DELIVERY,
      {
        artifact,
        message: `Generated keyword research for ${topic}`
      },
      message.conversationId,
      message.id,
      artifact.id
    )
  };
}

/**
 * Handle artifact request for keyword research
 */
export async function handleSeoKeywordArtifactRequest(
  message: IterativeMessage,
  state: any,
  stateManager: any,
  messaging: any
): Promise<any> {
  logger.info(`SEO Keyword Agent: Handling artifact request`, { sessionId: message.conversationId });

  // Extract request details
  const { artifactId, artifactType } = message.content || {};

  // Find the requested artifact
  let artifact: IterativeArtifact | null = null;

  if (artifactId && state.artifacts && state.artifacts[artifactId]) {
    artifact = state.artifacts[artifactId];
  } else if (artifactType === 'seo-keywords' && state.artifacts) {
    // Find the latest keyword research artifact
    const keywordArtifacts = Object.values(state.artifacts)
      .filter((a: any) => a.type === 'seo-keywords' && a.createdBy === 'seo-keyword')
      .sort((a: any, b: any) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

    if (keywordArtifacts.length > 0) {
      artifact = keywordArtifacts[0] as IterativeArtifact;
    }
  }

  if (!artifact) {
    return {
      success: false,
      message: 'Artifact not found',
      response: await messaging.send(
        message.conversationId,
        message.from,
        IterativeMessageType.ERROR,
        {
          error: 'Artifact not found',
          message: `Could not find the requested artifact`
        },
        message.conversationId,
        message.id
      )
    };
  }

  // Return the artifact
  return {
    success: true,
    message: `Successfully retrieved artifact ${artifact.id}`,
    response: await messaging.send(
      message.conversationId,
      message.from,
      IterativeMessageType.ARTIFACT_DELIVERY,
      {
        artifact,
        message: `Retrieved artifact ${artifact.id}`
      },
      message.conversationId,
      message.id,
      artifact.id
    )
  };
}

/**
 * SEO Keyword Agent class
 */
export class SeoKeywordAgent {
  private agentId = 'seo-keyword';

  /**
   * Process a message sent to this agent
   */
  async processMessage(sessionId: string, message: IterativeMessage): Promise<any> {
    logger.info(`SEO Keyword Agent processing message of type ${message.type}`, {
      sessionId,
      messageId: message.id,
      messageType: message.type
    });

    try {
      // Get the current state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        logger.error(`Session ${sessionId} not found`, { sessionId });
        return null;
      }

      // Create a simple state manager for the handlers
      const stateManager = {
        addArtifact: async (sessionId: string, artifact: IterativeArtifact) => {
          return await stateStore.updateState(sessionId, (state) => {
            if (!state.artifacts) {
              state.artifacts = {};
            }
            state.artifacts[artifact.id] = artifact;

            // Also add to generatedArtifacts array if it exists
            if (state.generatedArtifacts) {
              state.generatedArtifacts.push(artifact.id);
            } else {
              state.generatedArtifacts = [artifact.id];
            }

            return state;
          });
        },
        updateArtifact: async (sessionId: string, artifactId: string, updateFn: (artifact: IterativeArtifact) => IterativeArtifact) => {
          return await stateStore.updateState(sessionId, (state) => {
            if (state.artifacts && state.artifacts[artifactId]) {
              state.artifacts[artifactId] = updateFn(state.artifacts[artifactId]);
            }
            return state;
          });
        }
      };

      // Create a simple messaging utility for the handlers
      const messaging = {
        send: async (
          sessionId: string,
          to: string,
          type: IterativeMessageType,
          content: any,
          conversationId?: string,
          inReplyTo?: string,
          artifactId?: string
        ) => {
          const message: IterativeMessage = {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: this.agentId,
            to,
            type,
            content,
            conversationId: conversationId || sessionId,
            inReplyTo,
            artifactId
          };

          // Store the message in the state
          await stateStore.updateState(sessionId, (state) => {
            if (!state.messages) {
              state.messages = [];
            }
            state.messages.push(message);
            return state;
          });

          return message;
        }
      };

      // Handle different message types
      switch (message.type) {
        case IterativeMessageType.INITIAL_REQUEST:
        case IterativeMessageType.REQUEST:
          return await handleSeoKeywordInitialRequest(message, state, stateManager, messaging);

        case IterativeMessageType.ARTIFACT_REQUEST:
          return await handleSeoKeywordArtifactRequest(message, state, stateManager, messaging);

        case IterativeMessageType.DISCUSSION_START:
          return await this.handleDiscussionStart(sessionId, message, state);

        case IterativeMessageType.FEEDBACK:
          // Import the handleFeedback function from the handlers file
          const { handleFeedback } = await import('./seo-keyword/handlers');
          return await handleFeedback(message, state, stateManager, messaging);

        default:
          logger.warn(`SEO Keyword Agent: Unhandled message type ${message.type}`, {
            sessionId,
            messageType: message.type
          });
          return null;
      }
    } catch (error) {
      logger.error(`Error processing message in SEO Keyword Agent`, {
        sessionId,
        messageId: message.id,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * Act on the current state of the session
   * This method is called by the workflow orchestrator
   */
  async act(sessionId: string): Promise<boolean> {
    logger.info(`SEO Keyword Agent acting on session ${sessionId}`, { sessionId });

    try {
      // Get the current state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        logger.error(`Session ${sessionId} not found`, { sessionId });
        return false;
      }

      // Find active goals assigned to this agent
      const activeGoals = (state.goals || []).filter((goal: Goal) =>
        goal.status === 'active' && goal.assignedTo === this.agentId
      );

      if (activeGoals.length === 0) {
        logger.info(`No active goals for SEO Keyword Agent in session ${sessionId}`, { sessionId });
        return false;
      }

      // Process each active goal
      for (const goal of activeGoals) {
        logger.info(`SEO Keyword Agent processing goal ${goal.id}`, {
          sessionId,
          goalId: goal.id,
          goalName: goal.name
        });

        // Create a message for this goal
        const message: IterativeMessage = {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: 'system',
          to: this.agentId,
          type: IterativeMessageType.INITIAL_REQUEST,
          content: {
            topic: state.topic,
            contentType: state.contentType,
            targetAudience: state.targetAudience,
            tone: state.tone,
            ...goal.metadata
          },
          conversationId: sessionId,
          goalId: goal.id
        };

        // Process the message
        const result = await this.processMessage(sessionId, message);

        if (result && result.success) {
          // Mark the goal as completed and update workflow progress
          await stateStore.updateState(sessionId, (state) => {
            const goalIndex = state.goals.findIndex((g: Goal) => g.id === goal.id);
            if (goalIndex !== -1) {
              state.goals[goalIndex].status = 'completed';
              state.goals[goalIndex].completedAt = new Date().toISOString();
            }

            // Explicitly update the keywordResearchComplete flag
            if (state.workflowProgress) {
              state.workflowProgress.keywordResearchComplete = true;

              // Log the update for debugging
              logger.info(`Updated keywordResearchComplete flag to true for session ${sessionId}`, {
                sessionId,
                goalId: goal.id,
                workflowProgress: {
                  ...state.workflowProgress,
                  keywordResearchComplete: true
                }
              });
            }

            return state;
          });

          logger.info(`SEO Keyword Agent completed goal ${goal.id} and updated keywordResearchComplete flag`, {
            sessionId,
            goalId: goal.id,
            goalName: goal.name
          });

          // Explicitly trigger a check to validate research phase completion
          try {
            const { validateResearchPhase } = require('../workflows/research-phase-workflow');
            setTimeout(() => {
              validateResearchPhase(
                sessionId,
                state.topic,
                state.contentType,
                state.targetAudience,
                state.tone,
                {}
              ).catch((err: Error) => {
                logger.error(`Error validating research phase after keyword research completion`, {
                  sessionId,
                  goalId: goal.id,
                  error: err.message || String(err),
                  stack: err.stack
                });
              });
            }, 1000);
          } catch (error) {
            logger.error(`Error triggering research phase validation`, {
              sessionId,
              error: error instanceof Error ? error.message : String(error),
              stack: error instanceof Error ? error.stack : undefined
            });
          }
        } else {
          logger.error(`SEO Keyword Agent failed to complete goal ${goal.id}`, {
            sessionId,
            goalId: goal.id,
            goalName: goal.name,
            error: result?.message || 'Unknown error'
          });

          // Mark the goal as failed
          await stateStore.updateState(sessionId, (state) => {
            const goalIndex = state.goals.findIndex((g: Goal) => g.id === goal.id);
            if (goalIndex !== -1) {
              state.goals[goalIndex].status = 'failed';
              state.goals[goalIndex].failedAt = new Date().toISOString();
              state.goals[goalIndex].failureReason = result?.message || 'Unknown error';
            }
            return state;
          });
        }
      }

      return true;
    } catch (error) {
      logger.error(`Error in SEO Keyword Agent act method`, {
        sessionId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      return false;
    }
  }

  /**
   * Handle discussion start
   */
  private async handleDiscussionStart(sessionId: string, message: IterativeMessage, state: any): Promise<any> {
    logger.info(`SEO Keyword Agent: Handling discussion start`, { sessionId });

    // Extract discussion details
    const { discussionId, topic, prompt } = message.content || {};

    if (!discussionId || !topic) {
      return {
        success: false,
        message: 'Discussion ID and topic are required',
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: this.agentId,
          to: message.from,
          type: IterativeMessageType.ERROR,
          content: {
            error: 'Missing required fields',
            message: 'Discussion ID and topic are required'
          },
          conversationId: message.conversationId
        }
      };
    }

    // Generate a perspective on the topic from an SEO keyword standpoint
    const perspective = `From an SEO keyword perspective, I recommend the following approach for "${topic}":

1. **Primary Keyword Strategy**:
   - Focus on "${topic}" as the main keyword with a density of 1-2%
   - Include variations like "${topic}s", "${topic}ing", and "${topic} for [specific audience]"
   - Place the primary keyword in the title, first paragraph, and at least one H2 heading

2. **Secondary Keyword Integration**:
   - Incorporate related terms like "best ${topic}", "${topic} guide", and "${topic} examples"
   - Use these terms naturally in subheadings and throughout the content
   - Create dedicated sections for high-value secondary keywords

3. **Search Intent Alignment**:
   - Address both informational queries (how-to, guides) and commercial intent (best, top, reviews)
   - Create content that matches the search intent behind each keyword
   - Include FAQ sections targeting specific long-tail keywords

4. **Content Structure Recommendations**:
   - Use keyword-rich headings in a clear H2/H3 hierarchy
   - Include a table of contents with keyword-optimized section titles
   - Create skimmable content with bullet points and numbered lists

This approach balances keyword optimization with readability to maximize both search visibility and user engagement.`;

    // Return the perspective
    return {
      success: true,
      message: `Successfully contributed to discussion ${discussionId}`,
      response: {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: this.agentId,
        to: message.from,
        type: IterativeMessageType.DISCUSSION_CONTRIBUTION,
        content: {
          discussionId,
          perspective,
          contributor: this.agentId,
          contributionType: 'seo-keyword-perspective'
        },
        conversationId: message.conversationId
      }
    };
  }
}
