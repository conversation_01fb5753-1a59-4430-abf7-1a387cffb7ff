/**
 * Content Generation Agent
 *
 * This agent is responsible for generating content based on inputs from other agents.
 */

import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessage,
  IterativeMessageType,
  IterativeArtifact,
  ArtifactStatus,
  Goal
} from '../types';
import { stateStore } from '../utils/stateStore';
import logger from '../utils/logger';
import {
  handleContentGenerationInitialRequest,
  handleContentGenerationArtifactRequest,
  handleFeedback,
  handleConsultationRequest,
  handleArtifactDelivery,
  handleDiscussionStart,
  handleDiscussionContribution,
  handleDiscussionSynthesisRequest
} from './content-generation/handlers';

/**
 * Content Generation Agent class
 */
export class ContentGenerationAgent {
  private agentId = 'content-generation';

  /**
   * Process a message sent to this agent
   */
  async processMessage(sessionId: string, message: IterativeMessage): Promise<any> {
    logger.info(`Content Generation Agent processing message of type ${message.type}`, {
      sessionId,
      messageId: message.id,
      messageType: message.type
    });

    try {
      // Get the current state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        logger.error(`Session ${sessionId} not found`, { sessionId });
        return null;
      }

      // Create a simple state manager for the handlers
      const stateManager = {
        addArtifact: async (sessionId: string, artifact: IterativeArtifact) => {
          return await stateStore.updateState(sessionId, (state) => {
            if (!state.artifacts) {
              state.artifacts = {};
            }
            state.artifacts[artifact.id] = artifact;

            // Also add to generatedArtifacts array if it exists
            if (state.generatedArtifacts) {
              state.generatedArtifacts.push(artifact.id);
            } else {
              state.generatedArtifacts = [artifact.id];
            }

            return state;
          });
        },
        updateArtifact: async (sessionId: string, artifactId: string, updateFn: (artifact: IterativeArtifact) => IterativeArtifact) => {
          return await stateStore.updateState(sessionId, (state) => {
            if (state.artifacts && state.artifacts[artifactId]) {
              state.artifacts[artifactId] = updateFn(state.artifacts[artifactId]);
            }
            return state;
          });
        }
      };

      // Create a simple messaging utility for the handlers
      const messaging = {
        send: async (
          sessionId: string,
          to: string,
          type: IterativeMessageType,
          content: any,
          conversationId?: string,
          inReplyTo?: string
        ) => {
          const message: IterativeMessage = {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: this.agentId,
            to,
            type,
            content,
            conversationId: conversationId || sessionId,
            inReplyTo
          };

          // Store the message in the state
          await stateStore.updateState(sessionId, (state) => {
            if (!state.messages) {
              state.messages = [];
            }
            state.messages.push(message);
            return state;
          });

          return message;
        }
      };

      // Route the message to the appropriate handler based on message type
      logger.info(`Content Generation Agent routing message of type ${message.type}`, {
        sessionId,
        messageId: message.id,
        messageType: message.type,
        content: JSON.stringify(message.content).substring(0, 100) + '...'
      });

      switch (message.type) {
        case IterativeMessageType.INITIAL_REQUEST:
        case IterativeMessageType.REQUEST:
          logger.info(`Content Generation Agent calling handleContentGenerationInitialRequest`, {
            sessionId,
            messageId: message.id
          });

          const initialResult = await handleContentGenerationInitialRequest(message, state, stateManager, messaging);

          logger.info(`Content Generation Initial Request handler result: ${initialResult?.success}`, {
            sessionId,
            messageId: message.id,
            success: initialResult?.success,
            message: initialResult?.message
          });

          return initialResult;

        case IterativeMessageType.ARTIFACT_REQUEST:
          logger.info(`Content Generation Agent calling handleContentGenerationArtifactRequest`, {
            sessionId,
            messageId: message.id
          });

          const artifactResult = await handleContentGenerationArtifactRequest(message, state, stateManager, messaging);

          logger.info(`Content Generation Artifact Request handler result: ${artifactResult?.success}`, {
            sessionId,
            messageId: message.id,
            success: artifactResult?.success,
            message: artifactResult?.message
          });

          return artifactResult;

        case IterativeMessageType.FEEDBACK:
        case IterativeMessageType.FEEDBACK_REQUEST:
          return await handleFeedback(message, state, stateManager, messaging);

        case IterativeMessageType.CONSULTATION_REQUEST:
          return await handleConsultationRequest(message, state, stateManager, messaging);

        case IterativeMessageType.ARTIFACT_DELIVERY:
          return await handleArtifactDelivery(message, state, stateManager, messaging);

        case IterativeMessageType.DISCUSSION_START:
          return await handleDiscussionStart(state, message, messaging);

        case IterativeMessageType.DISCUSSION_CONTRIBUTION:
          return await handleDiscussionContribution(message, state, stateManager, messaging);

        case IterativeMessageType.DISCUSSION_SYNTHESIS_REQUEST:
          return await handleDiscussionSynthesisRequest(message, state, stateManager, messaging);

        default:
          logger.warn(`Content Generation Agent: Unhandled message type ${message.type}`, {
            sessionId,
            messageType: message.type
          });
          return null;
      }
    } catch (error) {
      logger.error(`Error processing message in Content Generation Agent`, {
        sessionId,
        messageId: message.id,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * Act on the current state of the session
   * This method is called by the workflow orchestrator
   */
  async act(sessionId: string): Promise<boolean> {
    logger.info(`Content Generation Agent acting on session ${sessionId}`, { sessionId });

    try {
      // Get the current state
      const state = await stateStore.getState(sessionId);
      if (!state) {
        logger.error(`Session ${sessionId} not found`, { sessionId });
        return false;
      }

      logger.info(`Content Generation Agent state retrieved`, {
        sessionId,
        topic: state.topic,
        contentType: state.contentType,
        currentPhase: state.currentPhase,
        workflowProgress: JSON.stringify(state.workflowProgress),
        goalsCount: (state.goals || []).length
      });

      // Find active goals assigned to this agent
      const activeGoals = (state.goals || []).filter((goal: Goal) =>
        goal.status === 'active' && goal.assignedTo === this.agentId
      );

      logger.info(`Content Generation Agent found ${activeGoals.length} active goals`, {
        sessionId,
        activeGoalsCount: activeGoals.length,
        activeGoals: activeGoals.map(g => ({ id: g.id, name: g.name }))
      });

      if (activeGoals.length === 0) {
        logger.info(`No active goals for Content Generation Agent in session ${sessionId}`, { sessionId });
        return false;
      }

      // Process each active goal
      for (const goal of activeGoals) {
        logger.info(`Content Generation Agent processing goal ${goal.id}`, {
          sessionId,
          goalId: goal.id,
          goalName: goal.name
        });

        // Create a message for this goal
        const message: IterativeMessage = {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: 'system',
          to: this.agentId,
          type: IterativeMessageType.INITIAL_REQUEST,
          content: {
            topic: state.topic,
            contentType: state.contentType,
            targetAudience: state.targetAudience,
            tone: state.tone,
            ...goal.metadata
          },
          conversationId: sessionId,
          goalId: goal.id
        };

        // Process the message
        const result = await this.processMessage(sessionId, message);

        if (result && result.success) {
          // Mark the goal as completed
          await stateStore.updateState(sessionId, (state) => {
            const goalIndex = state.goals.findIndex((g: Goal) => g.id === goal.id);
            if (goalIndex !== -1) {
              state.goals[goalIndex].status = 'completed';
              state.goals[goalIndex].completedAt = new Date().toISOString();
            }
            return state;
          });

          logger.info(`Content Generation Agent completed goal ${goal.id}`, {
            sessionId,
            goalId: goal.id,
            goalName: goal.name
          });
        } else {
          logger.error(`Content Generation Agent failed to complete goal ${goal.id}`, {
            sessionId,
            goalId: goal.id,
            goalName: goal.name,
            error: result?.message || 'Unknown error'
          });

          // Mark the goal as failed
          await stateStore.updateState(sessionId, (state) => {
            const goalIndex = state.goals.findIndex((g: Goal) => g.id === goal.id);
            if (goalIndex !== -1) {
              state.goals[goalIndex].status = 'failed';
              state.goals[goalIndex].failedAt = new Date().toISOString();
              state.goals[goalIndex].failureReason = result?.message || 'Unknown error';
            }
            return state;
          });
        }
      }

      return true;
    } catch (error) {
      logger.error(`Error in Content Generation Agent act method`, {
        sessionId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      return false;
    }
  }
}
