// src/app/(payload)/api/agents/collaborative-iteration/controller.ts

import { v4 as uuidv4 } from 'uuid';
import { 
  IterativeCollaborationState, 
  IterativeMessage, 
  IterativeArtifact,
  Consultation,
  AgentState,
  ConsultationRequest,
  ConsultationResponse,
  IterationRequest,
  IterationResponse,
  ITERATIVE_CONFIG,
  IterativeMessageType,
  AgentId
} from './types';
import { Reasoning } from '../a2atypes';
import { stateStore } from './server-based';
import { messageBus, getSessionMessageBus } from './server-based';
import { AgentStateManager } from './core/AgentStateManager';

/**
 * Standard return type for message handler methods
 */
interface StandardizedHandlerResult {
  response: IterativeMessage | null;
  stateUpdates: Partial<IterativeCollaborationState>;
}
import { AgentMessaging } from './core/AgentMessaging';
import { getAgentRoleType } from './utils/agentRoleMapper';
import logger from './utils/logger';

/**
 * Controller for managing iterative collaboration between agents
 * 
 * This controller handles:
 * 1. State management for iterative collaboration
 * 2. Message routing between agents
 * 3. Tracking consultations and iterations
 * 4. Convergence detection for final output
 */
export class IterativeCollaborationController {
  /**
   * Helper method to get agent role type
   */
  private getAgentRoleType(agentId: string): string {
    // Map agent IDs to their role types
    const roleMap: Record<string, string> = {
      'market-research': 'researcher',
      'seo-keyword': 'researcher',
      'content-strategy': 'strategist',
      'content-generation': 'creator',
      'seo-optimization': 'optimizer'
    };
    
    return roleMap[agentId] || 'unknown';
  }
  private sessions: Map<string, IterativeCollaborationState> = new Map();
  private baseUrl: string;
  
  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl || process.env.NEXT_PUBLIC_API_URL || 
      `http://${process.env.VERCEL_URL || 'localhost:3000'}`;
  }
  
  /**
   * Start a new iterative collaboration session with improved state management
   */
  async startCollaboration(
    topic: string,
    contentType: 'blog-article' | 'product-page' | 'buying-guide',
    targetAudience: string,
    tone: string = 'professional',
    keywords: string[] = [],
    additionalInstructions: string = '',
    clientName: string = ''
  ): Promise<IterativeCollaborationState> {
    const sessionId = uuidv4();
    logger.info(`Starting new collaboration session ${sessionId}`, { 
      sessionId, 
      topic, 
      contentType, 
      targetAudience 
    });
    
    // Create initial state
    const state: IterativeCollaborationState = {
      id: sessionId,
      topic,
      contentType,
      targetAudience,
      tone,
      keywords,
      status: 'active',
      startTime: new Date().toISOString(),
      artifacts: {},
      consultations: {},
      agentStates: {},
      currentPhase: 'planning',
      collaborationState: 'planning',
      messages: [],
      iterations: 0,
      maxIterations: ITERATIVE_CONFIG.maxIterations,
      events: [],
      sessionId,
      clientName: clientName || undefined,
      additionalInstructions: additionalInstructions || undefined
    };
    
    // Initialize agent states with improved tracking
    const agents = Object.values(AgentId);
    
    state.agents = {};
    
    agents.forEach(agentId => {
      // Create agent state record
      state.agentStates[agentId] = {
        id: agentId,
        processedRequests: [],
        generatedArtifacts: [],
        consultationsProvided: [],
        consultationsReceived: [],
        lastUpdated: new Date().toISOString()
      };
      
      // Track active agents
      if (state.agents) {
        state.agents[agentId] = {
          active: true,
          role: this.getAgentRoleType(agentId as AgentId),
          status: 'ready'
        };
      }
    });
    
    // Store in the enhanced stateStore
    try {
      await stateStore.setState(sessionId, state);
      logger.debug(`Session ${sessionId} state initialized in stateStore`);
    } catch (err) {
      logger.error(`Error storing new session in stateStore`, {
        sessionId,
        error: err instanceof Error ? err.message : String(err)
      });
    }
    
    // Store the session in the controller's sessions Map as well for backward compatibility
    this.sessions.set(sessionId, state);
    
    // Create a session-specific message bus
    try {
      const sessionBus = getSessionMessageBus(sessionId);
      
      // Create initial system message to record session creation
      const systemMessage = sessionBus.createBroadcastMessage(
        'system',
        IterativeMessageType.SYSTEM_MESSAGE,
        {
          event: 'SESSION_CREATED',
          details: {
            topic,
            contentType,
            targetAudience,
            tone,
            keywords: keywords.length
          }
        }
      );
      
      // Ensure message has the sessionId set
      systemMessage.sessionId = sessionId;
      systemMessage.conversationId = sessionId;
      
      // Send the message to all agents
      await sessionBus.sendMessage(systemMessage);
      
      // Also log this event for tracking
      logger.info(`Created new collaboration session ${sessionId}`, { 
        sessionId,
        messageId: systemMessage.id 
      });
    } catch (err) {
      logger.error(`Error initializing session message bus`, {
        sessionId,
        error: err instanceof Error ? err.message : String(err)
      });
    }
    
    return state;
  }
  
  /**
   * Get a session by ID (synchronous version)
   * Only checks the controller's sessions Map for backward compatibility
   */
  getSession(sessionId: string): IterativeCollaborationState | null {
    return this.sessions.get(sessionId) || null;
  }
  
  /**
   * Get a session by ID (async version)
   * Checks both the controller's sessions Map and the stateStore
   * This should be used for new code where possible
   */
  async getSessionAsync(sessionId: string): Promise<IterativeCollaborationState | null> {
    // Check the controller's sessions Map first for performance
    const fromMap = this.sessions.get(sessionId);
    if (fromMap) return fromMap;
    
    // If not found in Map, try the stateStore
    try {
      const fromStore = await stateStore.getState(sessionId);
      
      // If found in stateStore, sync back to the controller's Map
      if (fromStore) {
        this.sessions.set(sessionId, fromStore);
        return fromStore;
      }
    } catch (error) {
      console.error(`Error retrieving session from stateStore: ${error}`);
    }
    
    return null;
  }
  
  /**
   * Update a session state
   * Updates both the controller's sessions Map and the shared stateStore
   */
  async updateSession(sessionId: string, updater: (state: IterativeCollaborationState) => IterativeCollaborationState): Promise<IterativeCollaborationState | null> {
    // Check both storage mechanisms using getSessionAsync
    const session = await this.getSessionAsync(sessionId);
    if (!session) {
      console.error(`Failed to update session ${sessionId}: Session not found in either controller or stateStore`);
      return null;
    }
    
    const updatedSession = updater(session);
    // Update both storage locations
    this.sessions.set(sessionId, updatedSession);
    await stateStore.setState(sessionId, updatedSession);
    
    return updatedSession;
  }
  
  /**
   * Process a message for a session
   */
  async processMessage(sessionId: string, message: IterativeMessage): Promise<{ response: IterativeMessage | null, stateUpdates: Partial<IterativeCollaborationState> }> {
    console.log(`Processing message from ${message.from} to ${message.to} of type ${message.type}`);
    
    // Validate the message
    if (!message) {
      console.error('[CONTROLLER] No message provided to process');
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    try {
      // Get the session using the async method that checks both sources
      const session = await this.getSessionAsync(sessionId);
      if (!session) {
        console.error(`[CONTROLLER] Session with ID ${sessionId} not found`);
        return {
          response: {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: 'system',
            to: message.from,
            type: IterativeMessageType.RESPONSE,
            content: {
              error: `Session with ID ${sessionId} not found`,
              originalMessage: message
            }
          },
          stateUpdates: {}
        };
      }

      // Process the message based on its type
      switch (message.type) {
        case IterativeMessageType.INITIAL_REQUEST:
          return this.handleInitialRequest(sessionId, message as IterativeMessage);
        case IterativeMessageType.CONSULTATION_REQUEST:
          return this.handleConsultationRequest(sessionId, message as ConsultationRequest);
        case IterativeMessageType.CONSULTATION_RESPONSE:
          return this.handleConsultationResponse(sessionId, message as ConsultationResponse);
        case IterativeMessageType.ITERATION_REQUEST:
          return this.handleIterationRequest(sessionId, message as IterationRequest);
        case IterativeMessageType.ITERATION_RESPONSE:
          return this.handleIterationResponse(sessionId, message as IterationResponse); 
        case IterativeMessageType.ARTIFACT_DELIVERY:
          return this.handleArtifactDelivery(sessionId, message);
        case IterativeMessageType.ARTIFACT_REQUEST:
          return this.handleArtifactRequest(sessionId, message);
        case IterativeMessageType.FINAL_OUTPUT:
          console.log(`Final output received from ${message.from}:`, message.content);
          return {
            response: null,
            stateUpdates: {
              status: 'completed',
              endTime: new Date().toISOString()
            }
          };
        case 'ERROR':
          console.error(`Error message from ${message.from}:`, message.content);
          return {
            response: null,
            stateUpdates: {}
          };
        case 'ACKNOWLEDGMENT':
          console.log(`Acknowledgment from ${message.from} to ${Array.isArray(message.to) ? message.to.join(', ') : message.to}`);
          return {
            response: null,
            stateUpdates: {}
          };
        default:
          console.warn(`Unhandled message type: ${message.type}`);
          return {
            response: null,
            stateUpdates: {}
          };
      }
    } catch (error) {
      console.error(`Error processing message of type ${message.type}:`, error instanceof Error ? error.message : String(error));
      return {
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: 'system',
          to: message.from,
          type: IterativeMessageType.RESPONSE,
          content: {
            error: `Error processing message: ${error instanceof Error ? error.message : String(error)}`,
            originalMessage: message
          },
          conversationId: message.conversationId
        },
        stateUpdates: {
          status: 'active' // Keep the session active despite the error
        }
      };
    }
  }
  
  /**
   * Handle initial content generation request
   */
  private async handleInitialRequest(sessionId: string, message: IterativeMessage): Promise<{
    response: IterativeMessage | null;
    stateUpdates: Partial<IterativeCollaborationState>;
  }> {
    console.log('Handling initial request for session:', sessionId);
    
    const session = this.getSession(sessionId);
    if (!session) {
      throw new Error(`Session with ID ${sessionId} not found`);
    }
    
    console.log('Session state:', JSON.stringify(session, null, 2));
    
    // Route the initial request to the market research agent first
    const initialMessage: IterativeMessage = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'system',
      to: 'market-research',
      type: IterativeMessageType.INITIAL_REQUEST,
      content: {
        topic: session.topic,
        contentType: session.contentType,
        targetAudience: session.targetAudience,
        tone: session.tone,
        keywords: session.keywords
      },
      conversationId: uuidv4()
    };
    
    console.log('Created initial message:', JSON.stringify(initialMessage, null, 2));
    
    // Get the market research agent state
    const marketResearchState = session.agentStates['market-research'] || {
      id: 'market-research',
      processedRequests: [],
      generatedArtifacts: [],
      consultationsProvided: [],
      consultationsReceived: [],
      lastUpdated: new Date().toISOString()
    };
    
    console.log('Market research agent state:', JSON.stringify(marketResearchState, null, 2));
    
    try {
      // Import the market research agent handler
      console.log('Importing market research agent handler...');
      const { handleMarketResearchInitialRequest } = await import('./agents/market-research');
      console.log('Market research agent handler imported successfully');
      
      // Process the message with the market research agent
      console.log('Processing message with market research agent...');
      const result = await handleMarketResearchInitialRequest(
        initialMessage,
        marketResearchState,
        session.artifacts || {},
        session.consultations || {}
      );
      
      console.log('Market research agent result:', JSON.stringify(result, null, 2));
      
      // Update the session with the result
      const updatedArtifacts = { ...session.artifacts };
      if (result.newArtifacts) {
        console.log('New artifacts:', JSON.stringify(result.newArtifacts, null, 2));
        Object.assign(updatedArtifacts, result.newArtifacts);
      }
      if (result.updatedArtifacts) {
        console.log('Updated artifacts:', JSON.stringify(result.updatedArtifacts, null, 2));
        Object.assign(updatedArtifacts, result.updatedArtifacts);
      }
      
      const updatedConsultations = { ...session.consultations };
      if (result.newConsultations) {
        console.log('New consultations:', JSON.stringify(result.newConsultations, null, 2));
        Object.assign(updatedConsultations, result.newConsultations);
      }
      
      // Update agent states
      const updatedAgentStates = { ...session.agentStates };
      updatedAgentStates['market-research'] = result.updatedState;
      
      // Update session phase
      const stateUpdates: Partial<IterativeCollaborationState> = {
        currentPhase: 'research',
        artifacts: updatedArtifacts,
        consultations: updatedConsultations,
        agentStates: updatedAgentStates,
        messages: [...session.messages, initialMessage, result.response],
        iterations: session.iterations + 1
      };
      
      console.log('State updates:', JSON.stringify(stateUpdates, null, 2));
      
      // Process the response from market research agent
      if (result.response.to !== 'system') {
        console.log('Queueing next message for processing to:', result.response.to);
        // Queue the next message for processing
        setTimeout(() => {
          this.processMessage(sessionId, result.response)
            .catch(err => console.error('Error processing message:', err));
        }, 100);
      }
      
      return {
        response: result.response,
        stateUpdates
      };
    } catch (error) {
      console.error('Error processing initial request:', error);
      
      return {
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: 'system',
          to: 'system',
          type: IterativeMessageType.RESPONSE,
          content: {
            error: error instanceof Error ? error.message : 'Unknown error',
            originalMessage: initialMessage
          },
          conversationId: initialMessage.conversationId
        },
        stateUpdates: {
          currentPhase: 'error',
          status: 'failed'
        }
      };
    }
    // This should never be reached but is needed to satisfy TypeScript
    return {
      response: null,
      stateUpdates: {}
    };
  }
  
  /**
   * Handle consultation request
   */
  private async handleConsultationRequest(sessionId: string, message: ConsultationRequest): Promise<StandardizedHandlerResult> {
    console.log(`Handling consultation request from ${message.from} to ${Array.isArray(message.to) ? message.to.join(', ') : message.to}:`, message.content);
    
    const session = this.getSession(sessionId);
    if (!session) {
      console.error(`Session with ID ${sessionId} not found`);
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    // Synchronize artifacts to ensure consistent state
    this.synchronizeAgentArtifacts(sessionId);
    
    // Determine target agent (the agent being consulted)
    const targetAgent = Array.isArray(message.to) ? message.to[0] : message.to;
    if (!targetAgent) {
      console.error('No target agent specified in consultation request');
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    // Get the target agent state or create a new one if it doesn't exist
    const targetAgentState = session.agentStates[targetAgent] || {
      id: targetAgent,
      processedRequests: [],
      generatedArtifacts: [],
      consultationsProvided: [],
      consultationsReceived: [],
      lastUpdated: new Date().toISOString()
    };
    
    console.log(`[CONTROLLER] Target agent state: processedRequests=${targetAgentState.processedRequests?.length || 0}, generatedArtifacts=${targetAgentState.generatedArtifacts?.length || 0}`);

    try {
      // Import the target agent handler
      console.log(`[CONTROLLER] Importing ${targetAgent} agent handler...`);
      let agentHandler;

      switch (targetAgent) {
        case 'seo-keyword':
          console.log(`[CONTROLLER] Loading SEO Keyword agent module...`);
          const seoKeywordModule = await import('./agents/seo-keyword');
          agentHandler = seoKeywordModule.seoKeywordAgent.processMessage.bind(seoKeywordModule.seoKeywordAgent);
          break;
        case 'content-strategy':
          console.log(`[CONTROLLER] Loading Content Strategy agent module...`);
          const contentStrategyModule = await import('./agents/content-strategy');
          agentHandler = contentStrategyModule.contentStrategyAgent.processMessage.bind(contentStrategyModule.contentStrategyAgent);
          break;
        case 'market-research':
          console.log(`[CONTROLLER] Loading Market Research agent module...`);
          const marketResearchModule = await import('./agents/market-research');
          agentHandler = marketResearchModule.marketResearchAgent.processMessage.bind(marketResearchModule.marketResearchAgent);
          break;
        case 'content-generation':
          console.log(`[CONTROLLER] Loading Content Generation agent module...`);
          const contentGenerationModule = await import('./agents/content-generation');
          agentHandler = contentGenerationModule.contentGenerationAgent.processMessage.bind(contentGenerationModule.contentGenerationAgent);
          break;
        case 'seo-optimization':
          console.log(`[CONTROLLER] Loading SEO Optimization agent module...`);
          const seoOptimizationModule = await import('./agents/seo-optimization');
          agentHandler = seoOptimizationModule.seoOptimizationAgent.processMessage.bind(seoOptimizationModule.seoOptimizationAgent);
          break;
        default:
          throw new Error(`Unknown agent: ${targetAgent}`);
      }

      console.log(`[CONTROLLER] ${targetAgent} agent handler imported successfully`);

      // Process the message with the target agent
      console.log(`[CONTROLLER] Processing message with ${targetAgent} agent...`);
      const result = await agentHandler(message, targetAgentState);

      console.log(`[CONTROLLER] ${targetAgent} agent result:`, JSON.stringify(result, null, 2));

      // Create a copy of the consultations to update
      const updatedConsultations = { 
        ...session.consultations
      };

      // Create a copy of the artifacts to update
      const updatedArtifacts = { ...(session.artifacts || {}) };

      // Process the response from the agent
      if (result.response) {
        console.log(`[CONTROLLER] Got response from ${targetAgent} agent:`, result.response);
      }

      // Extract artifact from message content
      let artifactId: string | undefined;
      let artifact: IterativeArtifact | undefined;

      if (typeof message.content === 'object' && message.content !== null) {
        // Try to get the artifact ID
        if ('artifactId' in message.content && typeof message.content.artifactId === 'string') {
          artifactId = message.content.artifactId;
          console.log(`[CONTROLLER] Found artifactId in message.content: ${artifactId}`);
        } else if ('artifactId' in message && typeof message.artifactId === 'string') {
          artifactId = message.artifactId;
          console.log(`[CONTROLLER] Found artifactId in message: ${artifactId}`);
        }

        // Try to get the artifact object
        if ('artifact' in message.content && message.content.artifact) {
          artifact = message.content.artifact as IterativeArtifact;
          console.log(`[CONTROLLER] Found artifact in message.content with ID: ${artifact?.id || 'undefined'}, Type: ${artifact?.type || 'undefined'}`);
        }
      }

      // If we have an ID but no artifact, check if this is a reference to an artifact that
      // might be in the agent's state but not yet in the global session
      if (artifactId && !artifact && session.agentStates && message.from && session.agentStates[message.from]) {
        const agentState = session.agentStates[message.from];

        // If the agent has generated artifacts, check if this one is in their list
        if (agentState.generatedArtifacts && artifactId && agentState.generatedArtifacts.includes(artifactId)) {
          console.log(`Agent ${message.from} has generated artifact ${artifactId}, but it's not in the global session yet`);

          // Create a placeholder artifact if we can't find the actual one
          const summary = message.content && typeof message.content === 'object' && 'summary' in message.content 
            ? message.content.summary as string || 'Content not available'
            : 'Content not available';

          artifact = {
            id: artifactId,
            name: `Artifact from ${message.from}`,
            type: message.from.includes('keyword') ? 'seo-keywords' : 
                  message.from.includes('research') ? 'market-research' : 
                  message.from.includes('strategy') ? 'content-strategy' : 
                  message.from.includes('generation') ? 'content' : 'other',
            createdBy: message.from,
            createdAt: new Date().toISOString(),
            currentVersion: 1,
            iterations: [{
              version: 1,
              timestamp: new Date().toISOString(),
              agent: message.from,
              content: summary,
              feedback: [],
              incorporatedConsultations: [],
              changes: 'Initial creation'
            }],
            status: 'draft',
            qualityScore: 50
          };

          console.log(`Created placeholder artifact for ${artifactId}`);
        }
      }

      // If we have both an ID and an artifact, add it to the updated artifacts
      if (artifactId && artifact) {
        // Make sure the artifact has the required fields
        if (!artifact.createdBy) {
          artifact.createdBy = message.from;
        }
        if (!artifact.createdAt) {
          artifact.createdAt = new Date().toISOString();
        }

        // Ensure iterations have the required agent field
        if (artifact.iterations) {
          artifact.iterations.forEach(iteration => {
            if (!iteration.agent) {
              iteration.agent = message.from;
            }
          });
        }

        // Add the artifact to the updated artifacts
        updatedArtifacts[artifactId] = artifact;
        console.log(`Added artifact ${artifactId} to updated artifacts`);
      }

      // Update agent states with the result from the agent handler
      const updatedAgentStates = { ...(session.agentStates || {}) };
      
      // Check if result has updatedState property
      if (result && 'updatedState' in result && result.updatedState) {
        updatedAgentStates[targetAgent] = result.updatedState;
      } else {
        // If no updated state, keep the current state but mark it as updated
        updatedAgentStates[targetAgent] = {
          ...targetAgentState,
          lastUpdated: new Date().toISOString()
        };
      }

      // Create the state updates to return
      const stateUpdates: Partial<IterativeCollaborationState> = {
        currentPhase: 'research', // Using 'research' for consultations as it's a valid phase
        artifacts: updatedArtifacts,
        consultations: updatedConsultations,
        agentStates: updatedAgentStates,
        messages: [...(session.messages || []), message]
      };

      // Add the response message to the messages array if it exists
      if (result.response) {
        stateUpdates.messages = [...(stateUpdates.messages || []), result.response];
      }

      // Increment the iterations counter
      if (typeof session.iterations === 'number') {
        stateUpdates.iterations = session.iterations + 1;
      } else {
        stateUpdates.iterations = 1;
      }

      console.log('[CONTROLLER] State updates:', JSON.stringify(stateUpdates, null, 2));

      // Queue the next message for processing if it's not addressed to the system
      if (result && result.response && result.response.to && result.response.to !== 'system') {
        console.log('[CONTROLLER] Queueing next message for processing to:', result.response.to);
        // Store a reference to the response to ensure it doesn't become null
        const responseToProcess = result.response;
        setTimeout(() => {
          this.processMessage(sessionId, responseToProcess)
            .catch(err => console.error('[CONTROLLER] Error processing message:', err));
        }, 100);
      }

      // Return the StandardizedHandlerResult
      return {
        response: result && result.response ? result.response : null,
        stateUpdates
      };
    } catch (error) {
      console.error('Error processing consultation request:', error);

      return {
        response: {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          from: 'system',
          to: 'system',
          type: IterativeMessageType.RESPONSE,
          content: {
            error: error instanceof Error ? error.message : 'Unknown error',
            originalMessage: message
          },
          conversationId: message.conversationId
        },
        stateUpdates: {
          currentPhase: 'error',
          status: 'failed'
        }
      };
    }

    // If the result has a response, return it
    if (result && result.response) {
      console.log(`Got response from ${targetAgent} agent:`, result.response);
      
      // Prepare state updates object if not already created
      const stateUpdates: Partial<IterativeCollaborationState> = {};
      
      // Update agent state to reflect completion
      stateUpdates.agentStates = {
        ...session?.agentStates,
        [targetAgent]: {
          ...session?.agentStates?.[targetAgent],
          lastUpdated: new Date().toISOString(),
          status: 'completed'
        }
      };
      
      // If the response doesn't have an ID, add one
      if (!result.response.id) {
        result.response.id = uuidv4();
      }
      
      // Set the response timestamp if not already set
      if (!result.response.timestamp) {
        result.response.timestamp = new Date().toISOString();
      }
    }

    // Update state with any changes from the handler
    if (result.stateUpdates) {
      Object.assign(stateUpdates, result.stateUpdates);
    }

    // Increment the iteration count
    if (session.iterations) {
      stateUpdates.iterations = session.iterations + 1;
    } else {
      stateUpdates.iterations = 1;
    }

    console.log('[CONTROLLER] State updates:', JSON.stringify(stateUpdates, null, 2));

    // Queue the next message for processing if it's not addressed to the system
    if (result && result.response && result.response.to && result.response.to !== 'system') {
      console.log('[CONTROLLER] Queueing next message for processing to:', result.response.to);
      // Store a reference to the response to ensure it doesn't become null
      const responseToProcess = result.response;
      setTimeout(() => {
        this.processMessage(sessionId, responseToProcess)
          .catch(err => console.error('[CONTROLLER] Error processing message:', err));
      }, 100);
    }

    // Return the StandardizedHandlerResult
    return {
      response: result && result.response ? result.response : null,
      stateUpdates
    };
  } catch (error) {
    console.error('Error processing consultation request:', error);

    return {
      response: {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: 'system',
        to: message.from,
        type: IterativeMessageType.ERROR,
        content: `Error processing your consultation request: ${error instanceof Error ? error.message : String(error)}`
      },
      stateUpdates: {}
    };
  }
}

private async addToProcessorQueue(sessionId: string, message: IterativeMessage): Promise<{ response: IterativeMessage | null, stateUpdates: Partial<IterativeCollaborationState> }> {
  console.log(`Adding message to processor queue for ${message.type} from ${message.from} to ${Array.isArray(message.to) ? message.to.join(', ') : message.to}`);

  const session = this.getSession(sessionId);
  if (!session) {
    console.error(`Session ${sessionId} not found`);
    return { response: null, stateUpdates: {} };
  }

  // Mark message as processed by sender
  const fromAgentState = session.agentStates[message.from];
  if (fromAgentState && fromAgentState.processedRequests) {
    fromAgentState.processedRequests.push(message.id);
  }

  // Extract the target agent information from the message
  const targetAgent = Array.isArray(message.to) ? message.to[0] : message.to;
  console.log(`Target agent for consultation: ${targetAgent}`);

  try {
    // Import the target agent handler dynamically
    console.log(`Importing ${targetAgent} agent handler...`);
    let agentHandler;

    // Import handlers from their respective modules
    const { handleContentGenerationInitialRequest, handleContentGenerationArtifactRequest } = await import('./agents/content-generation');
    const { handleMarketResearchInitialRequest } = await import('./agents/market-research');
    const { handleSeoOptimizationInitialRequest } = await import('./agents/seo-optimization/index');

    // Determine appropriate handler based on agent and message type
    switch (targetAgent) {
      case AgentId.MARKET_RESEARCH:
        agentHandler = handleMarketResearchInitialRequest;
        break;
      case AgentId.CONTENT_GENERATION:
        // Select the appropriate handler based on message type
        if (message.type === IterativeMessageType.INITIAL_REQUEST) {
          agentHandler = handleContentGenerationInitialRequest;
        } else if (message.type === IterativeMessageType.ARTIFACT_REQUEST) {
          agentHandler = handleContentGenerationArtifactRequest;
        }
        break;
      case AgentId.SEO_OPTIMIZATION:
        agentHandler = handleSeoOptimizationInitialRequest;
        break;
      default:
        throw new Error(`Unknown agent: ${targetAgent} or no handler for message type ${message.type}`);
    }
    
    console.log(`${targetAgent} agent handler imported successfully`);
    
    // Create a unique ID for this consultation
    const consultationId = uuidv4();
    
    // Get the target agent's state
    const targetAgentState = session.agentStates[targetAgent] || {
      id: targetAgent,
      processedRequests: [],
      generatedArtifacts: [],
      consultationsProvided: [],
      consultationsReceived: [],
      lastUpdated: new Date().toISOString()
    };
    
    // Update the target agent state to include the received consultation
    targetAgentState.consultationsReceived = targetAgentState.consultationsReceived || [];
    targetAgentState.consultationsReceived.push(consultationId);
    
    // Create a consultation record
    const consultation = {
      id: consultationId,
      requestId: message.id,
      requestingAgent: message.from,
      targetAgent,
      timestamp: new Date().toISOString(),
      status: 'pending'
    };
    
    // Create a combined state with both agent state and session state for the handler
    const combinedState = {
      ...targetAgentState,
      topic: session.topic,
      contentType: session.contentType,
      targetAudience: session.targetAudience,
      tone: session.tone,
      keywords: session.keywords,
      status: session.status,
      startTime: session.startTime,
      endTime: session.endTime,
      artifacts: session.artifacts,
      // Use message ID for tracking instead of a separate consultation ID
      consultations: {
        ...session.consultations,
        [message.id]: {
          id: message.id,
          requestId: message.id,
          requestingAgent: message.from,
          targetAgent,
          timestamp: new Date().toISOString(),
          status: 'pending'
        }
      },
      agentStates: session.agentStates,
      currentPhase: session.currentPhase,
      messages: session.messages,
      iterations: session.iterations,
      maxIterations: session.maxIterations
    };
    
    // Process the message with the target agent
    console.log(`Processing consultation request with ${targetAgent} agent...`);
    
    // Check if agentHandler is defined before calling it
    if (!agentHandler) {
      throw new Error(`No handler found for ${targetAgent} agent and message type ${message.type}`);
    }
    
    // Process the message directly with the agent handler
    // Adding missing parameters based on agent handler requirements
    const result = await agentHandler(
      message,
      combinedState,
      this.messageService,  // Add message service as third parameter
      this.stateManager     // Add state manager as fourth parameter
    );
    
    console.log(`${targetAgent} agent result:`, result ? JSON.stringify(result, null, 2) : 'No result');
    
    // Check if we got a valid result, if not, return empty response
    if (!result) {
      console.error(`No result returned from ${Array.isArray(message.to) ? message.to[0] : message.to} agent`);
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    // Log the response if we have one
    if (result.response) {
      console.log(`Got response from ${Array.isArray(message.to) ? message.to[0] : message.to} agent:`, result.response);
    }
    
    // Extract artifact from message content
    let artifactId: string | undefined;
    let artifact: IterativeArtifact | undefined;
    
    if (typeof message.content === 'object' && message.content !== null) {
      // Try to get the artifact ID
      if ('artifactId' in message.content && typeof message.content.artifactId === 'string') {
        artifactId = message.content.artifactId;
        console.log(`[CONTROLLER] Found artifactId in message.content: ${artifactId}`);
      } else if ('artifactId' in message && typeof message.artifactId === 'string') {
        artifactId = message.artifactId;
        console.log(`[CONTROLLER] Found artifactId in message: ${artifactId}`);
      }
  
  // Try to get the artifact object
  if ('artifact' in message.content && message.content.artifact) {
    artifact = message.content.artifact as IterativeArtifact;
    console.log(`[CONTROLLER] Found artifact in message.content with ID: ${artifact?.id || 'undefined'}, Type: ${artifact?.type || 'undefined'}`);
  }
  
  // If we have an ID but no artifact, check if this is a reference to an artifact that
  // might be in the agent's state but not yet in the global session
  if (artifactId && !artifact && session.agentStates && message.from && session.agentStates[message.from]) {
    const agentState = session.agentStates[message.from];
    
    // If the agent has generated artifacts, check if this one is in their list
    if (agentState.generatedArtifacts && artifactId && agentState.generatedArtifacts.includes(artifactId)) {
      console.log(`Agent ${message.from} has generated artifact ${artifactId}, but it's not in the global session yet`);
      
      // Create a placeholder artifact if we can't find the actual one
      const summary = message.content && typeof message.content === 'object' && 'summary' in message.content 
        ? message.content.summary as string || 'Content not available'
        : 'Content not available';
        
      artifact = {
        id: artifactId,
        name: `Artifact from ${message.from}`,
        type: message.from.includes('keyword') ? 'seo-keywords' : 
              message.from.includes('research') ? 'market-research' : 
              message.from.includes('strategy') ? 'content-strategy' : 
              message.from.includes('generation') ? 'content' : 'other',
        createdBy: message.from,
        createdAt: new Date().toISOString(),
        currentVersion: 1,
        iterations: [{
          version: 1,
          timestamp: new Date().toISOString(),
          agent: message.from,
          content: summary,
          feedback: [],
          incorporatedConsultations: [],
          changes: 'Initial creation'
        }],
        status: 'draft',
        qualityScore: 50
      };
      
      console.log(`Created placeholder artifact for ${artifactId}`);
    }
  }
  
  // If we have both an ID and an artifact, store it in the session
  if (artifactId && artifact) {
    // Make sure the artifact has the required fields
    if (!artifact.createdBy) {
      artifact.createdBy = message.from;
    }
    if (!artifact.createdAt) {
      artifact.createdAt = new Date().toISOString();
    }
    
    // Ensure iterations have the required agent field
    if (artifact.iterations) {
      artifact.iterations.forEach(iteration => {
        if (!iteration.agent) {
          iteration.agent = message.from;
        }
      });
    }
    
    const localArtifactId = artifactId; // Create a local reference that won't be reassigned
    const localArtifact = artifact; // Create a local reference that won't be reassigned
    
    // Update the session with the new artifact
    this.updateSession(sessionId, (state) => ({
      ...state,
      artifacts: {
        ...state.artifacts,
        [localArtifactId]: localArtifact
      }
    }));
    
    console.log(`Stored artifact ${artifactId} in session`);
  }
  
  // Extract the target agent
  const targetAgent = Array.isArray(message.to) ? message.to[0] : message.to;
  console.log(`[CONTROLLER] Target agent: ${targetAgent}`);
  
  // Get the target agent's state
  const targetAgentState = session.agentStates[targetAgent] || {
    id: targetAgent,
    processedRequests: [],
    generatedArtifacts: [],
    consultationsProvided: [],
    consultationsReceived: [],
    lastUpdated: new Date().toISOString()
  };
  console.log(`[CONTROLLER] Target agent state: processedRequests=${targetAgentState.processedRequests.length}, generatedArtifacts=${targetAgentState.generatedArtifacts.length}`);
  
  try {
    // Import the target agent handler
    console.log(`[CONTROLLER] Importing ${targetAgent} agent handler...`);
    let agentHandler;
    
    switch (targetAgent) {
      case 'seo-keyword':
        console.log(`[CONTROLLER] Loading SEO Keyword agent module...`);
        const seoKeywordModule = await import('./agents/seo-keyword');
        agentHandler = seoKeywordModule.seoKeywordAgent.processMessage.bind(seoKeywordModule.seoKeywordAgent);
        break;
      case 'content-strategy':
        console.log(`[CONTROLLER] Loading Content Strategy agent module...`);
        const contentStrategyModule = await import('./agents/content-strategy');
        agentHandler = contentStrategyModule.contentStrategyAgent.processMessage.bind(contentStrategyModule.contentStrategyAgent);
        break;
      case 'content-generation':
        console.log(`[CONTROLLER] Loading Content Generation agent module...`);
        const contentGenerationModule = await import('./agents/content-generation');
        agentHandler = contentGenerationModule.contentGenerationAgent.processMessage.bind(contentGenerationModule.contentGenerationAgent);
        break;
      case 'seo-optimization':
        console.log(`[CONTROLLER] Loading SEO Optimization agent module...`);
        const seoOptimizationModule = await import('./agents/seo-optimization');
        agentHandler = seoOptimizationModule.seoOptimizationAgent.processMessage.bind(seoOptimizationModule.seoOptimizationAgent);
        break;
      default:
        console.error(`[CONTROLLER] ERROR: Unknown agent: ${targetAgent}`);
        throw new Error(`Unknown agent: ${targetAgent}`);
    }
    
    console.log(`[CONTROLLER] ${targetAgent} agent handler imported successfully`);
    
    // Process the message with the target agent
    console.log(`[CONTROLLER] Processing message with ${targetAgent} agent...`);
    console.log(`[CONTROLLER] Message ID: ${message.id}, Message type: ${message.type}`);
    if (artifact && artifact.id && artifact.type) {
      console.log(`[CONTROLLER] Sending artifact with ID: ${artifact.id}, Type: ${artifact.type} to agent: ${targetAgent}`);
    }
    // Create a combined state with both agent state and session state for the handler
    const combinedState = {
      ...targetAgentState,
      topic: session.topic,
      contentType: session.contentType,
      targetAudience: session.targetAudience,
      tone: session.tone,
      keywords: session.keywords,
      status: session.status,
      startTime: session.startTime,
      endTime: session.endTime,
      artifacts: session.artifacts,
      consultations: session.consultations,
      agentStates: session.agentStates,
      currentPhase: session.currentPhase,
      messages: session.messages,
      iterations: session.iterations,
      maxIterations: session.maxIterations
    };
    
    const result = await agentHandler(
      message,
      combinedState,
      session.artifacts || {},
      session.consultations || {}
    );
    
    console.log(`${targetAgent} agent result:`, JSON.stringify(result, null, 2));
    
    console.log('[CONTROLLER] Result received from agent handler');
    console.log(`[CONTROLLER] Result contains response: ${'response' in result}, artifactUpdates: ${'artifactUpdates' in result}`);
    
    // Extract the next message to forward and any state updates
    const nextMessage = 'response' in result ? result.response : null;
    if (nextMessage) {
      console.log(`[CONTROLLER] Next message extracted - ID: ${nextMessage.id}, Type: ${nextMessage.type}`);
      console.log(`[CONTROLLER] Next message from: ${nextMessage.from}, to: ${nextMessage.to}`);
      console.log(`[CONTROLLER] Next message contains artifact: ${!!nextMessage.content?.artifact}`);
      if (nextMessage.content?.artifact) {
        console.log(`[CONTROLLER] Next message artifact - ID: ${nextMessage.content.artifact.id}, Type: ${nextMessage.content.artifact.type}`);
      }
    } else {
      console.log(`[CONTROLLER] No next message extracted from agent result`);
    }
    
    // Prepare state updates
    const stateUpdates: Partial<IterativeCollaborationState> = {};
    
    // Track processed requests in the agent's state
    if (!targetAgentState.processedRequests.includes(message.id)) {
      targetAgentState.processedRequests.push(message.id);
      targetAgentState.lastUpdated = new Date().toISOString();
      console.log(`[CONTROLLER] Added message ${message.id} to agent's processed requests. Total: ${targetAgentState.processedRequests.length}`);
    }
    
    // Update agent state in the session
    stateUpdates.agentStates = {
      ...session.agentStates,
      [targetAgent]: targetAgentState
    };
    console.log(`[CONTROLLER] Updated agent state for ${targetAgent}`);
    
    // Handle any artifact updates from the agent
    let artifactUpdates: Record<string, IterativeArtifact> = {};
    
    if ('artifactUpdates' in result && result.artifactUpdates) {
      console.log('[CONTROLLER] Processing artifact updates from agent result');
      
      // Process new artifacts
      if ('new' in result.artifactUpdates && result.artifactUpdates.new) {
        const newArtifactsCount = Object.keys(result.artifactUpdates.new).length;
        console.log(`[CONTROLLER] Found ${newArtifactsCount} new artifacts`);
        
        Object.entries(result.artifactUpdates.new).forEach(([id, artifactItem]) => {
          const typedArtifact = artifactItem as IterativeArtifact;
          artifactUpdates[id] = typedArtifact;
          console.log(`[CONTROLLER] Processing new artifact - ID: ${id}, Type: ${typedArtifact.type}, CreatedBy: ${typedArtifact.createdBy}`);
          
          // Add the artifact ID to the agent's state if it's not already there
          if (!targetAgentState.generatedArtifacts.includes(id)) {
            targetAgentState.generatedArtifacts.push(id);
            targetAgentState.lastUpdated = new Date().toISOString();
            console.log(`[CONTROLLER] Added artifact ${id} to agent's generatedArtifacts. Total: ${targetAgentState.generatedArtifacts.length}`);
          }
        });
      }
      
      // Process updated artifacts
      if ('updated' in result.artifactUpdates && result.artifactUpdates.updated) {
        const updatedArtifactsCount = Object.keys(result.artifactUpdates.updated).length;
        console.log(`[CONTROLLER] Found ${updatedArtifactsCount} updated artifacts`);
        
        Object.entries(result.artifactUpdates.updated).forEach(([id, artifactItem]) => {
          const typedArtifact = artifactItem as IterativeArtifact;
          artifactUpdates[id] = typedArtifact;
          console.log(`[CONTROLLER] Processing updated artifact - ID: ${id}, Type: ${typedArtifact.type}, CurrentVersion: ${typedArtifact.currentVersion}`);
        });
      }
    } else {
      console.log('[CONTROLLER] No artifact updates in agent result');
    }
    
    // Update the session with the result
    const updatedArtifacts = { ...session.artifacts };
    
    // Handle different result formats - some may use stateUpdates instead of specific properties
    if ('stateUpdates' in result) {
      // Handle the case where result has stateUpdates property
      if (result.stateUpdates && result.stateUpdates.artifacts) {
        console.log('Merging artifacts from stateUpdates into global session');
        Object.entries(result.stateUpdates.artifacts).forEach(([artifactId, artifact]) => {
          console.log(`Adding/updating artifact ${artifactId} to global session state`);
          updatedArtifacts[artifactId] = artifact as IterativeArtifact;
        });
      }
    } else {
      // Handle the case with explicit newArtifacts and updatedArtifacts properties
      if ('newArtifacts' in result && result.newArtifacts) {
        console.log('New artifacts:', JSON.stringify(result.newArtifacts, null, 2));
        Object.entries(result.newArtifacts).forEach(([artifactId, artifact]) => {
          console.log(`Adding new artifact ${artifactId} to global session state`);
          updatedArtifacts[artifactId] = artifact as IterativeArtifact;
          
          // Also add this artifact to the agent's generatedArtifacts list if not already there
          const agentState = session.agentStates[targetAgent];
          if (agentState && !agentState.generatedArtifacts.includes(artifactId)) {
            agentState.generatedArtifacts.push(artifactId);
          }
        });
      }
      if ('updatedArtifacts' in result && result.updatedArtifacts) {
        console.log('Updated artifacts:', JSON.stringify(result.updatedArtifacts, null, 2));
        Object.entries(result.updatedArtifacts).forEach(([artifactId, artifact]) => {
          console.log(`Updating artifact ${artifactId} in global session state`);
          updatedArtifacts[artifactId] = artifact as IterativeArtifact;
        });
      }
    }
    
    const updatedConsultations = { ...session.consultations };
    if ('stateUpdates' in result) {
      if (result.stateUpdates && result.stateUpdates.consultations) {
        Object.assign(updatedConsultations, result.stateUpdates.consultations);
      }
    } else if ('newConsultations' in result && result.newConsultations) {
      console.log('New consultations:', JSON.stringify(result.newConsultations, null, 2));
      Object.assign(updatedConsultations, result.newConsultations);
    }
    
    // Update agent states
    const updatedAgentStates = { ...session.agentStates };
    if ('stateUpdates' in result && result.stateUpdates && result.stateUpdates.agentStates) {
      Object.assign(updatedAgentStates, result.stateUpdates.agentStates);
    } else if ('updatedState' in result) {
      updatedAgentStates[targetAgent] = result.updatedState;
    }
    
    // Update session phase based on the target agent
    let currentPhase = session.currentPhase;
    if (targetAgent === 'seo-keyword') {
      currentPhase = 'research';
    } else if (targetAgent === 'content-strategy') {
      currentPhase = 'planning';
    } else if (targetAgent === 'content-generation') {
      currentPhase = 'creation';
    } else if (targetAgent === 'seo-optimization') {
      currentPhase = 'refinement';
    }
    
    const stateUpdates: Partial<IterativeCollaborationState> = {
      currentPhase,
      artifacts: updatedArtifacts,
      consultations: updatedConsultations,
      agentStates: updatedAgentStates,
      messages: [...session.messages, result.response],
      iterations: session.iterations + 1
    };
    
    console.log(`[CONTROLLER] Preparing state updates for session ${sessionId}`);
    console.log(`[CONTROLLER] Current phase: ${currentPhase}`);
    console.log(`[CONTROLLER] Artifacts count: ${Object.keys(updatedArtifacts).length}`);
    console.log(`[CONTROLLER] Agent states count: ${Object.keys(updatedAgentStates).length}`);
    console.log(`[CONTROLLER] Messages count: ${session.messages.length + 1}`);
    console.log(`[CONTROLLER] Current iteration: ${session.iterations + 1}`);
    
    // Update the session state
    try {
      this.updateSession(sessionId, (state) => ({
        ...state,
        ...stateUpdates
      }));
      console.log(`[CONTROLLER] Successfully updated session ${sessionId} with new state`);
    } catch (error) {
      console.error(`[CONTROLLER] Error updating session ${sessionId}:`, error instanceof Error ? error.message : String(error));
    }
  }
  
  /**
   * Handle consultation response messages from agents
   */
  public async handleConsultationResponse(sessionId: string, message: ConsultationResponse): Promise<StandardizedHandlerResult> {
    console.log(`Handling consultation response from ${message.from} to ${Array.isArray(message.to) ? message.to.join(', ') : message.to}:`, message.content);
    
    try {
      const session = this.getSession(sessionId);
      if (!session) {
        console.error(`Session ${sessionId} not found`);
        return {
          response: null,
          stateUpdates: {}
        };
      }

      // Extract the consultation feedback and update the state
      const { artifactId, feedback, suggestions } = message.content || {};
      
      if (!artifactId) {
        console.error('No artifact ID in consultation response', message);
        return {
          response: null,
          stateUpdates: {}
        };
      }
      
      // Find the associated consultation request
      const consultationId = message.consultationId;
      if (!consultationId) {
        console.error('No consultation ID in response', message);
        return {
          response: null,
          stateUpdates: {}
        };
      }
      
      const consultation = session.consultations[consultationId];
      if (!consultation) {
        console.error(`Consultation ${consultationId} not found`);
        return {
          response: null,
          stateUpdates: {}
        };
      }

      // Update the consultation with the response
      consultation.feedback = feedback || '';
      consultation.suggestions = suggestions || [];
      consultation.incorporated = false;

      // Update the consultation in the session
      this.updateSession(sessionId, (state) => ({
        ...state,
        consultations: {
          ...state.consultations,
          [consultationId]: consultation
        }
      }));

      // Send a notification to the original requester
      const response: IterativeMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: message.from,
        to: consultation.fromAgent,
        type: IterativeMessageType.NOTIFICATION,
        content: {
          type: 'CONSULTATION_RESPONSE',
          consultationId,
          message: `${message.from} has responded to your consultation request.`
        },
        conversationId: message.conversationId
      };

      return {
        response,
        stateUpdates: {
          consultations: {
            [consultationId]: consultation
          }
        }
      };
    } catch (error) {
      console.error('Error handling consultation response:', error);
      return {
        response: null,
        stateUpdates: {}
      };
    }
  }

  /**
   * Handle iteration request messages from agents
   */
  private async handleIterationRequest(sessionId: string, message: IterationRequest): Promise<StandardizedHandlerResult> {
    console.log(`Handling iteration request from ${message.from} to ${Array.isArray(message.to) ? message.to.join(', ') : message.to}:`, message.content);
    
    const session = this.getSession(sessionId);
    if (!session) {
      console.error(`Session ${sessionId} not found`);
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    // Extract the target agent
    const targetAgent = Array.isArray(message.to) ? message.to[0] : message.to;
    const iterationId = message.iterationId || uuidv4();
    
    // Create a new iteration in the session
    const iterations = session.iterations || {};
    const iteration = {
      id: iterationId,
      requestTime: new Date().toISOString(),
      requestedBy: message.from,
      targetAgent,
      request: message.content,
      status: 'pending',
      responseTime: null,
      response: null
    };
    
    // Update the session state
    return {
      response: null,
      stateUpdates: {
        iterations: {
          ...iterations,
          [iterationId]: iteration
        }
      }
    };
  }
  
  /**
   * Handle artifact delivery messages from agents
   */
  public async handleArtifactDelivery(sessionId: string, message: IterativeMessage): Promise<StandardizedHandlerResult> {
    console.log(`Handling artifact delivery from ${message.from}:`, message.content);
    
    try {
      const session = this.getSession(sessionId);
      if (!session) {
        console.error(`Session ${sessionId} not found`);
        return {
          response: null,
          stateUpdates: {}
        };
      }

      // Extract artifact information from the message
      const { artifactId, artifact } = message.content || {};
      
      if (!artifactId || !artifact) {
        console.error('Missing artifact information in delivery', message);
        return {
          response: null,
          stateUpdates: {}
        };
      }
      
      // Store the artifact in the session
      const updatedArtifact = {
        ...artifact,
        id: artifactId,
        createdBy: message.from,
        createdAt: artifact.createdAt || new Date().toISOString(),
        lastModified: new Date().toISOString()
      };
      
      // Update the agent state to record this artifact
      if (session.agentStates[message.from]) {
        if (!session.agentStates[message.from].generatedArtifacts) {
          session.agentStates[message.from].generatedArtifacts = [];
        }
        
        if (!session.agentStates[message.from].generatedArtifacts.includes(artifactId)) {
          session.agentStates[message.from].generatedArtifacts.push(artifactId);
        }
        
        session.agentStates[message.from].lastUpdated = new Date().toISOString();
      }
      
      // Send a notification to the requester if this was in response to a request
      let response: IterativeMessage | null = null;
      
      if (message.inReplyTo) {
        // Find the original message
        const originalMessage = session.messages.find(msg => msg.id === message.inReplyTo);
        
        if (originalMessage) {
          response = {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: message.from,
            to: originalMessage.from,
            type: IterativeMessageType.NOTIFICATION,
            content: {
              type: 'ARTIFACT_DELIVERY',
              artifactId,
              message: `${message.from} has delivered the requested artifact.`
            },
            conversationId: message.conversationId,
            inReplyTo: message.inReplyTo
          };
        }
      }
      
      return {
        response,
        stateUpdates: {
          artifacts: {
            [artifactId]: updatedArtifact
          },
          agentStates: {
            [message.from]: session.agentStates[message.from]
          }
        }
      };
    } catch (error) {
      console.error('Error handling artifact delivery:', error);
      return {
        response: null,
        stateUpdates: {}
      };
    }
    const session = this.getSession(sessionId);
    if (!session) {
      console.error(`Session ${sessionId} not found`);
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    // Extract artifact details from the message
    const { artifactId, artifactType, artifactName, content, reasoning } = message.content;
    
    if (!artifactId || !artifactType || !content) {
      console.error(`Artifact delivery missing required fields: artifactId=${artifactId}, artifactType=${artifactType}, content=${!!content}`);
      return {
        response: null,
        stateUpdates: {}
      };
    }
    
    // Create or update the artifact in the session
    const artifacts = session.artifacts || {};
    const artifact = {
      id: artifactId,
      type: artifactType,
      name: artifactName || `${artifactType}-${artifactId.substring(0, 8)}`,
      createdBy: message.from,
      createdAt: new Date().toISOString(),
      content,
      reasoning: reasoning || null
    };
    
    // Update the agent state to record that they generated this artifact
    const agentStates = session.agentStates || {};
    const agentState = agentStates[message.from] || {
      id: message.from,
      processedRequests: [],
      generatedArtifacts: [],
      consultationsProvided: [],
      consultationsReceived: [],
      lastUpdated: new Date().toISOString()
    };
    
    // Add the artifact to the agent's generated artifacts if not already present
    if (!agentState.generatedArtifacts?.includes(artifactId)) {
      agentState.generatedArtifacts = [...(agentState.generatedArtifacts || []), artifactId];
    }
    
    // Synchronize artifacts after updating
    this.synchronizeAgentArtifacts(sessionId);
    
    // Return the updated state
    return {
      response: null,
      stateUpdates: {
        artifacts: {
          ...artifacts,
          [artifactId]: artifact
        },
        agentStates: {
          ...agentStates,
          [message.from]: agentState
        }
      }
    };
  }
    
  /**
   * Handle iteration response messages
   */
  private async handleIterationResponse(sessionId: string, message: IterationResponse): Promise<StandardizedHandlerResult> {
    try {
      // Get the session
      const session = this.getSession(sessionId);
      if (!session) {
        console.error(`No session found for ID ${sessionId}`);
        return { 
          response: null, 
          stateUpdates: {} 
        };
      }

      // Extract the iteration details
      const { artifactId, newVersion, changes, qualityScore } = message.content || {};
      
      if (!artifactId) {
        console.error('No artifact ID in iteration response', message);
        return { 
          response: null, 
          stateUpdates: {} 
        };
      }
      
      // Update the artifact with the new content
      const artifacts = { ...(session.artifacts || {}) };
      const artifact = artifacts[artifactId];
      if (!artifact) {
        console.error(`No artifact found with ID ${artifactId}`);
        return { 
          response: null, 
          stateUpdates: {} 
        };
      }

      // Create a new iteration record
      const iteration: Iteration = {
        version: newVersion || (artifact.currentVersion || 0) + 1,
        timestamp: new Date().toISOString(),
        agent: message.from,
        content: message.content?.content || artifact.content,
        feedback: [],
        incorporatedConsultations: [],
        changes: changes || 'Content updated with new iteration'
      };

      // Update the artifact with the new iteration
      artifacts[artifactId] = {
        ...artifact,
        iterations: [...(artifact.iterations || []), iteration],
        currentVersion: iteration.version,
        qualityScore: qualityScore || artifact.qualityScore || 0,
        updatedAt: new Date().toISOString(),
        status: (qualityScore || artifact.qualityScore || 0) >= ITERATIVE_CONFIG.qualityThreshold ? 'approved' : 'in-review'
      };

      // Return the result with updated artifact information
      return {
        response: null,
        stateUpdates: {
          artifacts
        }
      };
    } catch (error) {
      console.error('Error handling iteration response', error);
      return { 
        response: null, 
        stateUpdates: {} 
      };
    }
  }

  /**
   * Handle artifact delivery messages (private implementation)
   * @deprecated This is a duplicate implementation - use the public handleArtifactDelivery method instead
   * This method is kept for backward compatibility
   */
  private async _handleArtifactDelivery(sessionId: string, message: IterativeMessage): Promise<StandardizedHandlerResult> {
    console.log(`[DEPRECATED] Private handleArtifactDelivery called for session ${sessionId}`);
    return this.handleArtifactDelivery(sessionId, message);
  }

  /**
   * Handle artifact requests from agents
   */
  private async handleArtifactRequest(sessionId: string, message: IterativeMessage): Promise<{
    response: IterativeMessage | null;
    stateUpdates: Partial<IterativeCollaborationState>;
  }> {
    console.log(`Handling artifact request from ${message.from} to ${Array.isArray(message.to) ? message.to.join(', ') : message.to}:`, message.content);
    
    const session = this.getSession(sessionId);
    if (!session) {
      throw new Error(`Session with ID ${sessionId} not found`);
    }
    
    // Always synchronize artifacts first to ensure we have the most up-to-date view
    try {
      this.synchronizeAgentArtifacts(sessionId);
      console.log('Synchronized agent artifacts before processing artifact request');
    } catch (error) {
      console.error('Error synchronizing artifacts:', error instanceof Error ? error.message : String(error));
    }
    
    // Extract the artifact ID from the message - handle different content formats
    let artifactId: string | undefined;
    
    // Check if artifactId is directly in the content
    if (typeof message.content === 'object' && message.content !== null) {
      if ('artifactId' in message.content) {
        artifactId = message.content.artifactId;
      } else if (message.artifactId) {
        // Sometimes the artifactId might be in the message itself
        artifactId = message.artifactId;
      } else if ('id' in message.content) {
        // Or it might be called 'id' in the content
        artifactId = message.content.id;
      }
    }
    
    // If we still don't have an artifactId, check if the entire content is a string ID
    if (!artifactId && typeof message.content === 'string') {
      artifactId = message.content;
    }
    
    if (!artifactId) {
      console.error(`Could not extract artifact ID from message:`, message);
      // Create a message error response with the error details
      const errorResponse: IterativeMessage = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: 'system',
        to: message.from,
        type: IterativeMessageType.RESPONSE,
        content: {
          error: `Could not extract artifact ID from message`,
          originalMessage: message
        },
        conversationId: message.conversationId,
        inReplyTo: message.id
      };
      return {
        response: errorResponse,
        stateUpdates: {}
      };
    }
    
    // After synchronization, check if the artifact exists now
    let artifact = session.artifacts[artifactId];
    
    // Debug what artifacts are available in the session
    console.log(`Looking for artifact ${artifactId}. Available artifacts:`, 
               Object.keys(session.artifacts).join(', '));
    
    // Check if the artifact exists in any agent state's generatedArtifacts list
    for (const [agentId, agentState] of Object.entries(session.agentStates)) {
      if (agentState.generatedArtifacts && agentState.generatedArtifacts.includes(artifactId)) {
        console.log(`Artifact ${artifactId} found in ${agentId}'s generated artifacts list`);
      }
    }
    
    // If the artifact is not in the global session, check if it's in any agent's state
    if (!artifact) {
      console.log(`Artifact ${artifactId} not found in global session, checking agent states...`);
      
      // Convert message.to to string if it's an array
      const targetAgent = Array.isArray(message.to) ? message.to[0] : message.to;
      
      // Try to dynamically load the target agent handler to request the artifact
      try {
        let agentHandler;
        
        switch (targetAgent) {
          case 'market-research':
            const marketResearchModule = await import('./agents/market-research');
            agentHandler = marketResearchModule.marketResearchAgent.processMessage.bind(marketResearchModule.marketResearchAgent);
            break;
          case 'seo-keyword':
            const seoKeywordAgent = await import('./seo-keyword-agent');
            agentHandler = seoKeywordAgent.handleIterativeMessage;
            break;
          case 'content-strategy':
            const contentStrategyAgent = await import('./content-strategy-agent');
            agentHandler = contentStrategyAgent.handleIterativeMessage;
            break;
          case 'content-generation':
            const contentGenerationModule = await import('./agents/content-generation');
            agentHandler = contentGenerationModule.contentGenerationAgent.processMessage.bind(contentGenerationModule.contentGenerationAgent);
            break;
          case 'seo-optimization':
            const seoOptimizationAgent = await import('./seo-optimization-agent');
            agentHandler = seoOptimizationAgent.handleIterativeMessage;
            break;
          default:
            throw new Error(`Unknown agent: ${targetAgent}`);
        }
        
        // If we got here, we have an agent handler. Let's ask the agent to deliver the artifact
        if (agentHandler) {
          console.log(`Asking ${targetAgent} to deliver artifact ${artifactId}`);
          
          // Create artifact request message
          const requestMessage: IterativeMessage = {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            from: 'system',
            to: targetAgent,
            type: IterativeMessageType.SYSTEM_MESSAGE,
            content: {
              action: 'DELIVER_ARTIFACT',
              artifactId,
              requestedBy: message.from,
            },
            conversationId: message.conversationId
          };
          
          // Get the agent state
          const agentState = session.agentStates[targetAgent] || {
            id: targetAgent,
            processedRequests: [],
            generatedArtifacts: [],
            currentArtifactId: '',
            status: 'idle',
            metrics: {}
          };
          
          // Combine agent state with session context
          const combinedState = {
            ...agentState,
            topic: session.topic,
            contentType: session.contentType,
            targetAudience: session.targetAudience,
            tone: session.tone,
            keywords: session.keywords,
            status: session.status,
            artifacts: session.artifacts,
            consultations: session.consultations,
            agentStates: session.agentStates,
            currentPhase: session.currentPhase,
            messages: session.messages,
            // Adding missing required properties
            id: session.id,
            startTime: session.startTime,
            endTime: session.endTime,
            iterations: session.iterations,
            maxIterations: session.maxIterations
          };
          
          // Try to get the agent to create or deliver the artifact
          console.log(`Calling ${targetAgent} handler to process artifact delivery request`);
          const result = await agentHandler(
            requestMessage,
            combinedState,
            session.artifacts || {},
            session.consultations || {}
          );
          
          if (result && result.response) {
            console.log(`${targetAgent} responded to artifact delivery request:`, JSON.stringify(result.response, null, 2));
            
            // Check if the response contains the requested artifact
            if (result.response.type === 'ARTIFACT_DELIVERY') {
              // Update the session with the new artifact
              if (result.response.content && result.response.content.artifact) {
                const deliveredArtifact = result.response.content.artifact;
                
                // Store the artifact in the session
                this.updateSession(sessionId, (state) => ({
                  ...state,
                  artifacts: {
                    ...state.artifacts,
                    [artifactId]: deliveredArtifact
                  }
                }));
                
                console.log(`Received and stored artifact ${artifactId} from ${targetAgent}`);
                
                // Now we can return the artifact to the original requester
                return {
                  response: {
                    id: uuidv4(),
                    timestamp: new Date().toISOString(),
                    from: 'system',
                    to: message.from,
                    type: IterativeMessageType.FINAL_OUTPUT,
                    content: {
                      artifactId,
                      artifact: deliveredArtifact
                    },
                    conversationId: message.conversationId
                  },
                  stateUpdates: {}
                };
              }
            }
          }
        }
      } catch (error) {
        console.error(`Error trying to get agent ${targetAgent} to deliver artifact:`, 
                     error instanceof Error ? error.message : String(error));
      }
      
      // If we got here, we couldn't get the artifact from the target agent
      // Create a fallback placeholder artifact to avoid stalled workflows
      console.log(`Creating fallback placeholder artifact for ${artifactId}`);
      
      // Determine the likely creator of this artifact based on naming patterns
      let creatingAgent = targetAgent;
      if (artifactId.includes('keyword')) {
        creatingAgent = 'seo-keyword';
      } else if (artifactId.includes('research')) {
        creatingAgent = 'market-research';
      } else if (artifactId.includes('strategy')) {
        creatingAgent = 'content-strategy';
      } else if (artifactId.includes('content')) {
        creatingAgent = 'content-generation';
      }
      
      // Create a placeholder artifact
      artifact = {
        id: artifactId,
        name: `Placeholder for missing artifact (${artifactId})`,
        type: creatingAgent.includes('keyword') ? 'seo-keywords' : 
              creatingAgent.includes('research') ? 'market-research' : 
              creatingAgent.includes('strategy') ? 'content-strategy' : 
              creatingAgent.includes('generation') ? 'content' : 'other',
        createdBy: creatingAgent,
        createdAt: new Date().toISOString(),
        currentVersion: 1,
        iterations: [{
          version: 1,
          timestamp: new Date().toISOString(),
          agent: creatingAgent,
          content: {
            placeholder: true,
            message: `This is a placeholder for a missing artifact. The original artifact with ID ${artifactId} could not be found.`,
            topic: session.topic,
            contentType: session.contentType
          },
          feedback: [],
          incorporatedConsultations: [],
          changes: 'Placeholder creation'
        }],
        status: 'draft',
        qualityScore: 0
      };
      
      // Store the placeholder artifact in the session
      this.updateSession(sessionId, (state) => ({
        ...state,
        artifacts: {
          ...state.artifacts,
          [artifactId]: artifact!
        }
      }));
      
      console.log(`Created and stored fallback placeholder artifact for ${artifactId}`);
      
      // Also add it to the target agent's generatedArtifacts list
      this.updateSession(sessionId, (state) => {
        const updatedAgentState = {
          ...state.agentStates[creatingAgent],
          generatedArtifacts: [
            ...(state.agentStates[creatingAgent]?.generatedArtifacts || []),
            artifactId
          ]
        };
        
        return {
          ...state,
          agentStates: {
            ...state.agentStates,
            [creatingAgent]: updatedAgentState
          }
        };
      });
    }
    
    // Send the artifact back to the requester
    console.log(`Sending artifact ${artifactId} to ${message.from}`);
    return {
      response: {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        from: 'system',
        to: message.from,
        type: IterativeMessageType.FINAL_OUTPUT,
        content: {
          artifactId,
          artifact
        },
        conversationId: message.conversationId
      },
      stateUpdates: {}
    };
  }
  
  /**
   * Handle iteration request
   */
  private async handleIterationRequest(sessionId: string, message: IterationRequest): Promise<{
    response: IterativeMessage | null;
    stateUpdates: Partial<IterativeCollaborationState>;
  }> {
    const session = this.getSession(sessionId);
    if (!session) {
      throw new Error(`Session with ID ${sessionId} not found`);
    }
    
    // Update session iterations count
    const stateUpdates: Partial<IterativeCollaborationState> = {
      iterations: session.iterations + 1,
      currentPhase: 'refinement'
    };
    
    // Forward the request to the target agent
    // In a real implementation, this would make an HTTP request to the agent's endpoint
    
    // Synchronize the session with the stateStore
    await this.stateStore.updateSession(sessionId, stateUpdates);
    
    return {
      response: null, // The actual response will come from the target agent
      stateUpdates: {
        ...stateUpdates,
        endTime: new Date().toISOString(),
        currentPhase: 'finalization'
      }
    };
  }
  
  /**
   * Create a new artifact in the session
   */
  createArtifact(
    sessionId: string,
    type: string,
    name: string,
    createdBy: string,
    content: any,
    reasoning: Reasoning
  ): IterativeArtifact | null {
    const session = this.getSession(sessionId);
    if (!session) return null;
    
    const artifactId = uuidv4();
    const timestamp = new Date().toISOString();
    
    // Create the artifact
    const artifact: IterativeArtifact = {
      id: artifactId,
      name,
      type,
      createdBy,
      createdAt: timestamp,
      currentVersion: 1,
      iterations: [
        {
          version: 1,
          timestamp,
          agent: createdBy, // Adding the required agent field
          content,
          feedback: [],
          incorporatedConsultations: [],
          changes: 'Initial creation', // Adding the changes field
          reasoning: reasoning // Adding the reasoning field
        }
      ],
      status: 'draft',
      qualityScore: 50 // Initial score
    };
    
    // Update agent state
    const agentState = session.agentStates[createdBy];
    if (agentState) {
      agentState.generatedArtifacts.push(artifactId);
    }
    
    // Update session
    this.updateSession(sessionId, (state) => ({
      ...state,
      artifacts: {
        ...state.artifacts,
        [artifactId]: artifact
      },
      agentStates: {
        ...state.agentStates
      }
    }));
    
    return artifact;
  }
  
  /**
   * Synchronize artifacts bidirectionally between agent states and the global session state
   * This ensures that artifacts created by any agent are accessible to all other agents
   * and that each agent has access to the most up-to-date artifacts
   */
  private synchronizeAgentArtifacts(sessionId: string): void {
    const session = this.getSession(sessionId);
    if (!session) {
      console.error(`Cannot synchronize artifacts: Session ${sessionId} not found`);
      return;
    }
    
    console.log(`[Artifact Sync] Starting bidirectional artifact synchronization for session ${sessionId}...`);
    
    try {
      // Initialize artifacts and agent states if they don't exist
      const artifacts = session.artifacts || {};
      const agentStates = session.agentStates || {};
      
      // STEP 1: Sync from agent states to global session
      // Create a map to track all artifacts we need to add/update in the global session
      const artifactsToSync: Record<string, IterativeArtifact> = {};
      let artifactsAdded = 0;
      let artifactsUpdated = 0;
      
      // Check each agent's state for artifacts that need to be synced
      Object.entries(agentStates).forEach(([agentId, agentState]) => {
        if (!agentState.generatedArtifacts || agentState.generatedArtifacts.length === 0) {
          console.log(`[Artifact Sync] Agent ${agentId} has no generated artifacts to sync`);
          return;
        }
        
        console.log(`[Artifact Sync] Checking agent ${agentId} for artifacts to sync (${agentState.generatedArtifacts.length} artifacts)...`);
        
        // For each artifact ID in the agent's generatedArtifacts list
        agentState.generatedArtifacts.forEach(artifactId => {
          if (!artifactId) {
            console.warn(`[Artifact Sync] Agent ${agentId} has a null or undefined artifact ID in generatedArtifacts`);
            return;
          }
          
          // Check if the artifact exists in the global session
          if (!artifacts[artifactId]) {
            console.log(`[Artifact Sync] Creating placeholder for artifact ${artifactId} from agent ${agentId}`);
            
            // Create placeholder artifact with appropriate type based on the agent ID
            let artifactType = 'other';
            if (agentId.includes('keyword')) artifactType = 'seo-keywords';
            else if (agentId.includes('research')) artifactType = 'market-research';
            else if (agentId.includes('strategy')) artifactType = 'content-strategy';
            else if (agentId.includes('generation')) artifactType = 'content';
            else if (agentId.includes('optimization')) artifactType = 'seo-optimization';
            
            // Create a well-structured placeholder artifact
            const placeholderArtifact: IterativeArtifact = {
              id: artifactId,
              name: `${artifactType.charAt(0).toUpperCase() + artifactType.slice(1).replace(/-/g, ' ')} from ${agentId}`,
              type: artifactType,
              createdBy: agentId,
              createdAt: new Date().toISOString(),
              currentVersion: 1,
              iterations: [{
                version: 1,
                timestamp: new Date().toISOString(),
                agent: agentId,
                content: 'Placeholder content - waiting for actual content delivery',
                feedback: [],
                incorporatedConsultations: [],
                changes: 'Initial creation (placeholder)',
                reasoning: {
                  thoughts: ['Placeholder artifact needs to be created to maintain system consistency'],
                  considerations: ['Artifact was referenced but not found in the global session'],
                  alternatives: ['Could have rejected the reference', 'Could have requested the artifact from the agent'],
                  decision: 'Create a placeholder artifact to maintain workflow continuity',
                  confidence: 0.8,
                  sources: ['System synchronization process']
                }
              }],
              status: 'draft',
              qualityScore: 50
            };
            
            artifactsToSync[artifactId] = placeholderArtifact;
            artifactsAdded++;
          } else {
            // Ensure the existing artifact has all required fields
            const existingArtifact = artifacts[artifactId];
            let needsUpdate = false;
            
            // Verify and fix critical fields if missing
            if (!existingArtifact.iterations || existingArtifact.iterations.length === 0) {
              console.log(`[Artifact Sync] Fixing missing iterations for artifact ${artifactId}`);
              existingArtifact.iterations = [{
                version: 1,
                timestamp: existingArtifact.createdAt || new Date().toISOString(),
                agent: existingArtifact.createdBy || agentId,
                content: 'Reconstructed content during synchronization',
                feedback: [],
                incorporatedConsultations: [],
                changes: 'Reconstructed during synchronization'
              }];
              needsUpdate = true;
            }
            
            if (!existingArtifact.currentVersion) {
              console.log(`[Artifact Sync] Fixing missing currentVersion for artifact ${artifactId}`);
              existingArtifact.currentVersion = existingArtifact.iterations?.length || 1;
              needsUpdate = true;
            }
            
            if (needsUpdate) {
              artifactsToSync[artifactId] = existingArtifact;
              artifactsUpdated++;
            }
          }
        });
      });
      
      // STEP 2: Ensure all global artifacts are reflected in the appropriate agent's generatedArtifacts
      const updatedAgentStates: Record<string, AgentState> = { ...agentStates };
      let agentStatesUpdated = 0;
      
      // Check each artifact in the global session
      Object.entries(artifacts).forEach(([artifactId, artifact]) => {
        const createdBy = artifact.createdBy;
        if (!createdBy) {
          console.warn(`[Artifact Sync] Artifact ${artifactId} has no createdBy field, cannot sync to agent`);
          return;
        }
        
        // Get or create the agent state for the creator
        const agentState = updatedAgentStates[createdBy] || {
          id: createdBy,
          processedRequests: [],
          generatedArtifacts: [],
          consultationsProvided: [],
          consultationsReceived: [],
          lastUpdated: new Date().toISOString()
        };
        
        // Add the artifact to the agent's generatedArtifacts if not already there
        if (!agentState.generatedArtifacts.includes(artifactId)) {
          console.log(`[Artifact Sync] Adding artifact ${artifactId} to agent ${createdBy}'s generatedArtifacts`);
          agentState.generatedArtifacts.push(artifactId);
          agentState.lastUpdated = new Date().toISOString();
          updatedAgentStates[createdBy] = agentState;
          agentStatesUpdated++;
        }
      });
      
      // Update agent states in session if changes were made
      if (agentStatesUpdated > 0) {
        console.log(`[Artifact Sync] Updated ${agentStatesUpdated} agent states`);
        session.agentStates = updatedAgentStates;
        artifactsUpdated++;
      }
    } else {
      console.log('[Artifact Sync] No artifacts or agent states needed synchronization');
    }
  } catch (error) {
    console.error(`[Artifact Sync] Error during artifact synchronization:`, error instanceof Error ? error.message : String(error));
  }
}

/**
 * Check if content has converged to a final state
 */
private hasContentConverged(sessionId: string, artifactId: string): boolean {
  // Ensure we have valid parameters
  if (!sessionId || !artifactId) {
    console.error('Missing required parameters', { sessionId, artifactId });
    return false;
  }

  const session = this.getSession(sessionId);
  if (!session) return false;

  const artifact = session.artifacts?.[artifactId];
  if (!artifact) return false;

  // If we've reached maximum iterations
  if (session.iterations >= session.maxIterations) {
    return true;
  }

  // If quality score exceeds threshold
  if (artifact.qualityScore >= ITERATIVE_CONFIG.qualityThreshold) {
    return true;
  }

  // If recent iterations show minimal changes
  if (artifact.iterations.length >= 2) {
    // In a real implementation, we would calculate the difference between versions
    // For now, we'll just return false to allow more iterations
    return false;
  }
  
  // Default: continue iterations
  return false;
}
