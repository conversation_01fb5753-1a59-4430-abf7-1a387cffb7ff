// src/app/(payload)/api/agents/collaborative-iteration/routes/research-phase.ts

import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { initiateResearchPhase } from '../workflows/research-phase-workflow';
import logger from '../utils/logger';

/**
 * API handler for initiating the research phase workflow
 * 
 * @param req The Next.js request object
 * @returns NextResponse with the session ID and status
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Parse the request body
    const body = await req.json();
    const { 
      topic, 
      contentType = 'blog-article',
      targetAudience = 'general audience',
      tone = 'informative',
      sessionId = uuidv4(),
      ...additionalParams
    } = body;

    // Validate required parameters
    if (!topic) {
      return NextResponse.json(
        { error: 'Missing required parameter: topic' },
        { status: 400 }
      );
    }

    // Log the request
    logger.info(`Initiating research phase workflow via API`, {
      sessionId,
      topic,
      contentType,
      targetAudience,
      tone
    });

    // Initiate the research phase workflow
    const success = await initiateResearchPhase(
      sessionId,
      topic,
      contentType,
      targetAudience,
      tone,
      additionalParams
    );

    if (success) {
      return NextResponse.json({
        sessionId,
        status: 'initiated',
        message: 'Research phase workflow initiated successfully'
      });
    } else {
      return NextResponse.json(
        { 
          error: 'Failed to initiate research phase workflow',
          sessionId
        },
        { status: 500 }
      );
    }
  } catch (error) {
    const err = error as Error;
    logger.error(`Error in research phase API handler`, {
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: 'Internal server error', details: err.message },
      { status: 500 }
    );
  }
}

/**
 * API handler for checking the status of a research phase workflow
 * 
 * @param req The Next.js request object
 * @returns NextResponse with the current status of the workflow
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Get the session ID from the query parameters
    const { searchParams } = new URL(req.url);
    const sessionId = searchParams.get('sessionId');

    // Validate required parameters
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Import stateStore here to avoid circular dependencies
    const { stateStore } = await import('../utils/stateStore');

    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      return NextResponse.json(
        { error: 'Session not found', sessionId },
        { status: 404 }
      );
    }

    // Extract relevant information for the response
    const { 
      workflowProgress,
      currentPhase,
      metadata,
      generatedArtifacts = []
    } = state;

    // Count artifacts by type
    const artifactCounts = generatedArtifacts.reduce((counts: Record<string, number>, id: string) => {
      const type = state.artifacts[id]?.type;
      if (type) {
        counts[type] = (counts[type] || 0) + 1;
      }
      return counts;
    }, {});

    return NextResponse.json({
      sessionId,
      currentPhase,
      workflowProgress,
      researchWorkflowState: metadata?.researchWorkflowState || null,
      artifactCounts,
      artifactIds: generatedArtifacts,
      status: 'success'
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error in research phase status API handler`, {
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: 'Internal server error', details: err.message },
      { status: 500 }
    );
  }
}
