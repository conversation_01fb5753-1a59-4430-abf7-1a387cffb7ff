// src/app/(payload)/api/agents/collaborative-iteration/routes/content-generation.ts

import { NextRequest, NextResponse } from 'next/server';
import { initiateContentGenerationPhase } from '../workflows/content-generation-workflow';
import logger from '../utils/logger';
import { stateStore } from '../utils/stateStore';

/**
 * API handler for initiating the content generation phase workflow
 * 
 * @param req The Next.js request object
 * @returns NextResponse with the session ID and status
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Parse the request body
    const body = await req.json();
    const { 
      sessionId,
      topic, 
      contentType = 'blog-article',
      targetAudience = 'general audience',
      tone = 'informative',
      ...additionalParams
    } = body;

    // Validate required parameters
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Check if the session exists
    const state = await stateStore.getState(sessionId);
    if (!state) {
      return NextResponse.json(
        { error: 'Session not found', sessionId },
        { status: 404 }
      );
    }

    // Check if the research phase is complete
    if (!state.workflowProgress?.contentStrategyComplete) {
      return NextResponse.json(
        { 
          error: 'Content strategy is not complete. Cannot proceed with content generation.',
          sessionId,
          workflowProgress: state.workflowProgress
        },
        { status: 400 }
      );
    }

    // Get topic from state if not provided
    const contentTopic = topic || state.topic;
    if (!contentTopic) {
      return NextResponse.json(
        { error: 'Missing required parameter: topic (not provided and not found in session)' },
        { status: 400 }
      );
    }

    // Log the request
    logger.info(`Initiating content generation phase workflow via API`, {
      sessionId,
      topic: contentTopic,
      contentType,
      targetAudience,
      tone
    });

    // Initiate the content generation phase workflow
    const success = await initiateContentGenerationPhase(
      sessionId,
      contentTopic,
      contentType,
      targetAudience,
      tone,
      additionalParams
    );

    if (success) {
      return NextResponse.json({
        sessionId,
        status: 'initiated',
        message: 'Content generation phase workflow initiated successfully'
      });
    } else {
      return NextResponse.json(
        { 
          error: 'Failed to initiate content generation phase workflow',
          sessionId
        },
        { status: 500 }
      );
    }
  } catch (error) {
    const err = error as Error;
    logger.error(`Error in content generation phase API handler`, {
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: 'Internal server error', details: err.message },
      { status: 500 }
    );
  }
}

/**
 * API handler for checking the status of a content generation phase workflow
 * 
 * @param req The Next.js request object
 * @returns NextResponse with the current status of the workflow
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Get the session ID from the query parameters
    const { searchParams } = new URL(req.url);
    const sessionId = searchParams.get('sessionId');

    // Validate required parameters
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required parameter: sessionId' },
        { status: 400 }
      );
    }

    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      return NextResponse.json(
        { error: 'Session not found', sessionId },
        { status: 404 }
      );
    }

    // Extract relevant information for the response
    const { 
      workflowProgress,
      currentPhase,
      metadata,
      generatedArtifacts = []
    } = state;

    // Count artifacts by type
    const artifactCounts = generatedArtifacts.reduce((counts: Record<string, number>, id: string) => {
      const type = state.artifacts[id]?.type;
      if (type) {
        counts[type] = (counts[type] || 0) + 1;
      }
      return counts;
    }, {});

    // Get content generation and SEO optimization artifacts
    const contentGenerationArtifacts = generatedArtifacts
      .filter(id => state.artifacts[id]?.type === 'content-generation')
      .map(id => ({
        id,
        title: state.artifacts[id]?.title,
        createdAt: state.artifacts[id]?.createdAt
      }));

    const seoOptimizationArtifacts = generatedArtifacts
      .filter(id => state.artifacts[id]?.type === 'seo-optimization')
      .map(id => ({
        id,
        title: state.artifacts[id]?.title,
        createdAt: state.artifacts[id]?.createdAt
      }));

    return NextResponse.json({
      sessionId,
      currentPhase,
      workflowProgress,
      contentGenerationWorkflowState: metadata?.contentGenerationWorkflowState || null,
      artifactCounts,
      contentGenerationArtifacts,
      seoOptimizationArtifacts,
      status: 'success'
    });
  } catch (error) {
    const err = error as Error;
    logger.error(`Error in content generation phase status API handler`, {
      error: err.message || String(error),
      stack: err.stack
    });

    return NextResponse.json(
      { error: 'Internal server error', details: err.message },
      { status: 500 }
    );
  }
}
