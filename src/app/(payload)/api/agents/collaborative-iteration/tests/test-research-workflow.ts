// src/app/(payload)/api/agents/collaborative-iteration/tests/test-research-workflow.ts

import { v4 as uuidv4 } from 'uuid';
import { initiateResearchPhase, validateResearchPhase, validateContentStrategy } from '../workflows/research-phase-workflow.js';
import { stateStore } from '../utils/stateStore.js';
import logger from '../utils/logger.js';
import {
  marketResearchAgent,
  seoKeywordAgent,
  contentStrategyAgent
} from '../agents/index.js';

/**
 * Test the research phase workflow
 *
 * This function initiates the research phase workflow with test data
 * and monitors its progress to ensure it's working correctly.
 */
async function testResearchWorkflow() {
  try {
    // Generate a unique session ID for this test
    const sessionId = `test-${uuidv4()}`;

    // Test parameters
    const topic = 'Artificial Intelligence in Healthcare';
    const contentType = 'blog-article';
    const targetAudience = 'healthcare professionals';
    const tone = 'informative';
    const additionalParams = {
      keywords: ['AI in healthcare', 'medical AI', 'healthcare technology'],
      testMode: true
    };

    logger.info(`Starting research workflow test with session ID: ${sessionId}`, {
      sessionId,
      topic,
      contentType,
      targetAudience,
      tone
    });

    // Initiate the research phase workflow
    const success = await initiateResearchPhase(
      sessionId,
      topic,
      contentType,
      targetAudience,
      tone,
      additionalParams
    );

    if (!success) {
      logger.error(`Failed to initiate research phase workflow`, { sessionId });
      return;
    }

    logger.info(`Research phase workflow initiated successfully`, { sessionId });

    // Monitor the workflow progress
    await monitorWorkflowProgress(sessionId);

  } catch (error) {
    const err = error as Error;
    logger.error(`Error in test script`, {
      error: err.message || String(error),
      stack: err.stack
    });
  }
}

/**
 * Monitor the progress of the workflow
 *
 * This function periodically checks the state to monitor the progress
 * of the workflow and logs relevant information.
 */
async function monitorWorkflowProgress(sessionId: string, maxChecks: number = 20) {
  let checkCount = 0;
  let complete = false;

  while (!complete && checkCount < maxChecks) {
    // Wait for a few seconds between checks
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Get the current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      logger.error(`No state found for session ${sessionId}`);
      return;
    }

    // Extract relevant information
    const {
      workflowProgress,
      currentPhase,
      metadata,
      generatedArtifacts = []
    } = state;

    // Count artifacts by type
    const artifactCounts: Record<string, number> = {};
    for (const id of generatedArtifacts) {
      const type = state.artifacts[id]?.type;
      if (type) {
        artifactCounts[type] = (artifactCounts[type] || 0) + 1;
      }
    }

    // Log the current status
    logger.info(`Workflow progress check #${checkCount + 1}`, {
      sessionId,
      currentPhase,
      workflowProgress,
      artifactCounts,
      artifactIds: generatedArtifacts,
      researchWorkflowState: metadata?.researchWorkflowState || null
    });

    // Check if the workflow is complete
    if (
      workflowProgress?.marketResearchComplete &&
      workflowProgress?.keywordResearchComplete &&
      workflowProgress?.contentStrategyComplete
    ) {
      complete = true;
      logger.info(`Research workflow completed successfully!`, {
        sessionId,
        artifactCounts,
        totalArtifacts: generatedArtifacts.length
      });

      // Log details of each artifact
      for (const id of generatedArtifacts) {
        const artifact = state.artifacts[id];
        if (artifact) {
          logger.info(`Artifact: ${artifact.title}`, {
            id: artifact.id,
            type: artifact.type,
            status: artifact.status,
            createdAt: artifact.createdAt
          });
        }
      }
    }

    checkCount++;
  }

  if (!complete) {
    logger.warn(`Workflow did not complete within the expected time`, {
      sessionId,
      checksPerformed: checkCount
    });
  }
}

// Run the test
testResearchWorkflow().catch(error => {
  logger.error(`Unhandled error in test script`, {
    error: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined
  });
});
