'use client';

import { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  Typo<PERSON>, 
  TextField, 
  Button, 
  Card, 
  CardContent, 
  FormControl, 
  InputLabel, 
  MenuItem, 
  Select, 
  Divider, 
  Chip,
  CircularProgress,
  Alert,
  Tab,
  Tabs,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete,
  IconButton,
  Tooltip
} from '@mui/material';
import { Grid } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import QuestionAnswerIcon from '@mui/icons-material/QuestionAnswer';
import ChatIcon from '@mui/icons-material/Chat';
import { AgentProgressDisplay } from '@/components/AgentProgress/AgentProgressDisplay';
import { ArtifactViewer } from '@/components/AgentProgress/ArtifactViewer';
import { ReasoningDisplay } from '@/components/AgentProgress/ReasoningDisplay';
import { DiscussionsPanel } from '@/components/AgentProgress/DiscussionsPanel';
import { ConsultationsPanel } from '@/components/AgentProgress/ConsultationsPanel';

export default function CollaborativeAgentsPage() {
  // Session state
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [sessionState, setSessionState] = useState<any | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  // Form inputs for creating a new session
  const [topic, setTopic] = useState<string>('');
  const [contentType, setContentType] = useState<string>('blog-article');
  const [targetAudience, setTargetAudience] = useState<string>('');
  const [tone, setTone] = useState<string>('informative');
  const [keywords, setKeywords] = useState<string>('');
  
  // For sending messages
  const [selectedAgent, setSelectedAgent] = useState<string>('content-generation');
  const [messageType, setMessageType] = useState<string>('INITIAL_REQUEST');
  const [messageContent, setMessageContent] = useState<string>('');
  
  // For tabbed interface
  const [activeTab, setActiveTab] = useState<number>(0);
  
  // For dialogs
  const [discussionDialogOpen, setDiscussionDialogOpen] = useState<boolean>(false);
  const [consultationDialogOpen, setConsultationDialogOpen] = useState<boolean>(false);
  
  // For discussion form
  const [discussionTopic, setDiscussionTopic] = useState<string>('');
  const [discussionLeadAgent, setDiscussionLeadAgent] = useState<string>('content-strategy');
  const [discussionParticipants, setDiscussionParticipants] = useState<string[]>(['market-research', 'seo-keyword', 'content-generation']);
  const [discussionPrompt, setDiscussionPrompt] = useState<string>('');
  
  // For consultation form
  const [consultationFromAgent, setConsultationFromAgent] = useState<string>('content-generation');
  const [consultationToAgent, setConsultationToAgent] = useState<string>('market-research');
  const [consultationQuestion, setConsultationQuestion] = useState<string>('');
  const [consultationContext, setConsultationContext] = useState<string>('');
  
  // Create a new session
  const createSession = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/collaborative-agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'create_session',
          data: {
            topic,
            contentType,
            targetAudience,
            tone,
            keywords: keywords.split(',').map(k => k.trim())
          }
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to create session');
      }
      
      setSessionId(data.sessionId);
      await fetchSessionState(data.sessionId);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch session state
  const fetchSessionState = async (id: string) => {
    setLoading(true);
    
    try {
      const response = await fetch(`/api/collaborative-agents?sessionId=${id}`);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch session state');
      }
      
      setSessionState(data.state);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  // Send a message to an agent
  const sendMessage = async () => {
    if (!sessionId) {
      setError('No active session');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // Create a message based on the form inputs
      const message = createMessage();
      
      const response = await fetch('/api/collaborative-agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          message
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to send message');
      }
      
      setSessionState(data.state);
    } catch (err: any) {
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };
  
  // Start a discussion between agents
  const startDiscussion = async () => {
    if (!sessionId) {
      setError('No active session');
      return;
    }
    
    if (!discussionTopic) {
      setError('Discussion topic is required');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/collaborative-agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'facilitate_discussion',
          sessionId,
          topic: discussionTopic,
          leadAgent: discussionLeadAgent,
          participants: discussionParticipants,
          initialPrompt: discussionPrompt || `Discuss the topic: ${discussionTopic}`
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to start discussion');
      }
      
      setSessionState(data.state);
      setDiscussionDialogOpen(false);
      // Reset form fields
      setDiscussionTopic('');
      setDiscussionPrompt('');
      // Switch to discussions tab
      setActiveTab(1);
    } catch (err: any) {
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };
  
  // Request a consultation between agents
  const requestConsultation = async () => {
    if (!sessionId) {
      setError('No active session');
      return;
    }
    
    if (!consultationQuestion) {
      setError('Consultation question is required');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/collaborative-agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'request_consultation',
          sessionId,
          fromAgent: consultationFromAgent,
          toAgent: consultationToAgent,
          question: consultationQuestion,
          context: consultationContext ? JSON.parse(consultationContext) : {}
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to request consultation');
      }
      
      setSessionState(data.state);
      setConsultationDialogOpen(false);
      // Reset form fields
      setConsultationQuestion('');
      setConsultationContext('');
      // Switch to consultations tab
      setActiveTab(2);
    } catch (err: any) {
      setError(err.message || 'An error occurred with the consultation request');
    } finally {
      setLoading(false);
    }
  };
  
  // Create a message based on form inputs
  const createMessage = () => {
    const baseMessage = {
      id: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
      from: 'orchestrator',
      to: selectedAgent,
      type: messageType,
      conversationId: sessionId
    };
    
    // Add content based on message type
    switch (messageType) {
      case 'INITIAL_REQUEST':
        return {
          ...baseMessage,
          content: {
            topic,
            contentType,
            targetAudience,
            tone,
            keywords: keywords.split(',').map(k => k.trim())
          }
        };
        
      case 'ARTIFACT_REQUEST':
        return {
          ...baseMessage,
          content: {
            artifactType: messageContent,
            description: 'Please generate this artifact'
          }
        };
        
      case 'FEEDBACK':
        return {
          ...baseMessage,
          content: {
            feedback: messageContent,
            artifactId: sessionState?.artifacts ? 
              Object.keys(sessionState.artifacts)[0] : undefined
          }
        };
        
      case 'CONSULTATION_REQUEST':
        return {
          ...baseMessage,
          content: {
            question: messageContent,
            context: {
              topic,
              contentType
            }
          }
        };
        
      default:
        return {
          ...baseMessage,
          content: { message: messageContent }
        };
    }
  };
  
  // Initiate the full collaborative workflow
  const initiateFullWorkflow = async () => {
    if (!sessionId) {
      setError('No active session');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/collaborative-agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'initiate_workflow',
          sessionId,
          data: {
            topic,
            contentType,
            targetAudience,
            tone,
            keywords: keywords.split(',').map(k => k.trim())
          }
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to initiate workflow');
      }
      
      setSessionState(data.state);
    } catch (err: any) {
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };
  
  // Create an article directly without creating a session first
  const createArticle = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Validate required fields
      if (!topic) {
        throw new Error('Topic is required');
      }
      
      const response = await fetch('/api/collaborative-agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'create_article',
          data: {
            topic,
            contentType: contentType || 'blog-article',
            targetAudience: targetAudience || 'general audience',
            tone: tone || 'informative',
            keywords: keywords ? keywords.split(',').map(k => k.trim()) : [],
            skipPlanning: true, // Optional flag to skip extensive planning
            prioritizeContent: true // Optional flag to prioritize content generation
          }
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to create article');
      }
      
      // Set the session state and ID from the response
      setSessionId(data.sessionId);
      setSessionState(data.state);
      
      // Switch to the artifacts tab to show the generated content
      setActiveTab(0);
    } catch (err: any) {
      setError(err.message || 'An error occurred while creating the article');
    } finally {
      setLoading(false);
    }
  };

  // Refresh session state periodically
  useEffect(() => {
    if (!sessionId) return;
    
    const interval = setInterval(() => {
      fetchSessionState(sessionId);
    }, 5000);
    
    return () => clearInterval(interval);
  }, [sessionId]);
  
  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Collaborative Agents Testing Dashboard
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {/* Discussion Dialog */}
      <Dialog open={discussionDialogOpen} onClose={() => setDiscussionDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Start a New Discussion Between Agents</DialogTitle>
        <DialogContent>
          <TextField
            label="Discussion Topic"
            value={discussionTopic}
            onChange={(e) => setDiscussionTopic(e.target.value)}
            fullWidth
            margin="normal"
            required
          />
          
          <FormControl fullWidth margin="normal">
            <InputLabel>Lead Agent</InputLabel>
            <Select
              value={discussionLeadAgent}
              onChange={(e) => setDiscussionLeadAgent(e.target.value)}
              label="Lead Agent"
            >
              <MenuItem value="content-strategy">Content Strategy</MenuItem>
              <MenuItem value="market-research">Market Research</MenuItem>
              <MenuItem value="seo-keyword">SEO Keyword</MenuItem>
              <MenuItem value="content-generation">Content Generation</MenuItem>
              <MenuItem value="seo-optimization">SEO Optimization</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl fullWidth margin="normal">
            <InputLabel>Participants</InputLabel>
            <Select
              multiple
              value={discussionParticipants}
              onChange={(e) => setDiscussionParticipants(typeof e.target.value === 'string' ? [e.target.value] : e.target.value)}
              label="Participants"
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => (
                    <Chip key={value} label={value} />
                  ))}
                </Box>
              )}
            >
              <MenuItem value="market-research">Market Research</MenuItem>
              <MenuItem value="seo-keyword">SEO Keyword</MenuItem>
              <MenuItem value="content-strategy">Content Strategy</MenuItem>
              <MenuItem value="content-generation">Content Generation</MenuItem>
              <MenuItem value="seo-optimization">SEO Optimization</MenuItem>
            </Select>
          </FormControl>
          
          <TextField
            label="Initial Prompt (Optional)"
            value={discussionPrompt}
            onChange={(e) => setDiscussionPrompt(e.target.value)}
            fullWidth
            margin="normal"
            multiline
            rows={3}
            placeholder={`Discuss the topic: ${discussionTopic || '[Topic]'}`}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDiscussionDialogOpen(false)}>Cancel</Button>
          <Button onClick={startDiscussion} variant="contained" disabled={loading || !discussionTopic}>
            {loading ? <CircularProgress size={24} /> : 'Start Discussion'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Consultation Dialog */}
      <Dialog open={consultationDialogOpen} onClose={() => setConsultationDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Request Agent Consultation</DialogTitle>
        <DialogContent>
          <FormControl fullWidth margin="normal">
            <InputLabel>From Agent</InputLabel>
            <Select
              value={consultationFromAgent}
              onChange={(e) => setConsultationFromAgent(e.target.value)}
              label="From Agent"
            >
              <MenuItem value="content-generation">Content Generation</MenuItem>
              <MenuItem value="market-research">Market Research</MenuItem>
              <MenuItem value="seo-keyword">SEO Keyword</MenuItem>
              <MenuItem value="content-strategy">Content Strategy</MenuItem>
              <MenuItem value="seo-optimization">SEO Optimization</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl fullWidth margin="normal">
            <InputLabel>To Agent</InputLabel>
            <Select
              value={consultationToAgent}
              onChange={(e) => setConsultationToAgent(e.target.value)}
              label="To Agent"
            >
              <MenuItem value="market-research">Market Research</MenuItem>
              <MenuItem value="seo-keyword">SEO Keyword</MenuItem>
              <MenuItem value="content-strategy">Content Strategy</MenuItem>
              <MenuItem value="content-generation">Content Generation</MenuItem>
              <MenuItem value="seo-optimization">SEO Optimization</MenuItem>
            </Select>
          </FormControl>
          
          <TextField
            label="Consultation Question"
            value={consultationQuestion}
            onChange={(e) => setConsultationQuestion(e.target.value)}
            fullWidth
            margin="normal"
            multiline
            rows={2}
            required
          />
          
          <TextField
            label="Context (JSON format)"
            value={consultationContext}
            onChange={(e) => setConsultationContext(e.target.value)}
            fullWidth
            margin="normal"
            multiline
            rows={3}
            placeholder='{"topic": "Example", "keywords": ["sample", "example"], "relevantInfo": "Additional details here"}'
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConsultationDialogOpen(false)}>Cancel</Button>
          <Button onClick={requestConsultation} variant="contained" disabled={loading || !consultationQuestion}>
            {loading ? <CircularProgress size={24} /> : 'Request Consultation'}
          </Button>
        </DialogActions>
      </Dialog>
      
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
        <Box sx={{ width: { xs: '100%', md: '40%' } }}>
          <Card sx={{ mb: 4 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {sessionId ? 'Active Session' : 'Create New Session'}
              </Typography>
              
              {sessionId ? (
                <>
                  <Typography variant="body1" gutterBottom>
                    Session ID: {sessionId}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Current Phase: {sessionState?.currentPhase || 'Loading...'}
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                    <Button 
                      variant="contained" 
                      color="primary" 
                      onClick={initiateFullWorkflow}
                      disabled={loading || sessionState?.status === 'completed'}
                      sx={{ flex: 1 }}
                    >
                      {loading ? <CircularProgress size={24} /> : 'Generate Content'}
                    </Button>
                    <Button 
                      variant="outlined" 
                      color="secondary" 
                      onClick={() => {
                        setSessionId(null);
                        setSessionState(null);
                      }}
                      sx={{ flex: 1 }}
                    >
                      Reset Session
                    </Button>
                  </Box>
                </>
              ) : (
                <>
                  <TextField
                    label="Topic"
                    value={topic}
                    onChange={(e) => setTopic(e.target.value)}
                    fullWidth
                    margin="normal"
                    required
                  />
                  
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Content Type</InputLabel>
                    <Select
                      value={contentType}
                      onChange={(e) => setContentType(e.target.value)}
                      label="Content Type"
                    >
                      <MenuItem value="blog-article">Blog Article</MenuItem>
                      <MenuItem value="product-page">Product Page</MenuItem>
                      <MenuItem value="buying-guide">Buying Guide</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <TextField
                    label="Target Audience"
                    value={targetAudience}
                    onChange={(e) => setTargetAudience(e.target.value)}
                    fullWidth
                    margin="normal"
                    required
                  />
                  
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Tone</InputLabel>
                    <Select
                      value={tone}
                      onChange={(e) => setTone(e.target.value)}
                      label="Tone"
                    >
                      <MenuItem value="informative">Informative</MenuItem>
                      <MenuItem value="conversational">Conversational</MenuItem>
                      <MenuItem value="professional">Professional</MenuItem>
                      <MenuItem value="friendly">Friendly</MenuItem>
                      <MenuItem value="authoritative">Authoritative</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <TextField
                    label="Keywords (comma-separated)"
                    value={keywords}
                    onChange={(e) => setKeywords(e.target.value)}
                    fullWidth
                    margin="normal"
                  />
                  
                  <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={createSession}
                      disabled={loading || !topic || !targetAudience}
                      sx={{ flex: 1 }}
                    >
                      {loading ? <CircularProgress size={24} /> : 'Create Session'}
                    </Button>
                    
                    <Button
                      variant="contained"
                      color="success"
                      onClick={createArticle}
                      disabled={loading || !topic}
                      sx={{ flex: 1 }}
                    >
                      {loading ? <CircularProgress size={24} /> : 'Create Article'}
                    </Button>
                  </Box>
                </>
              )}
            </CardContent>
          </Card>
          
          {sessionId && (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Send Message
                </Typography>
                
                <FormControl fullWidth margin="normal">
                  <InputLabel>Agent</InputLabel>
                  <Select
                    value={selectedAgent}
                    onChange={(e) => setSelectedAgent(e.target.value)}
                    label="Agent"
                  >
                    <MenuItem value="content-generation">Content Generation</MenuItem>
                    <MenuItem value="seo-keyword">SEO Keyword</MenuItem>
                    <MenuItem value="market-research">Market Research</MenuItem>
                    <MenuItem value="content-strategy">Content Strategy</MenuItem>
                  </Select>
                </FormControl>
                
                <FormControl fullWidth margin="normal">
                  <InputLabel>Message Type</InputLabel>
                  <Select
                    value={messageType}
                    onChange={(e) => setMessageType(e.target.value)}
                    label="Message Type"
                  >
                    <MenuItem value="INITIAL_REQUEST">Initial Request</MenuItem>
                    <MenuItem value="ARTIFACT_REQUEST">Artifact Request</MenuItem>
                    <MenuItem value="FEEDBACK">Feedback</MenuItem>
                    <MenuItem value="CONSULTATION_REQUEST">Consultation Request</MenuItem>
                  </Select>
                </FormControl>
                
                {(messageType === 'ARTIFACT_REQUEST' || 
                  messageType === 'FEEDBACK' || 
                  messageType === 'CONSULTATION_REQUEST') && (
                  <TextField
                    label={messageType === 'ARTIFACT_REQUEST' ? 'Artifact Type' : 
                           messageType === 'FEEDBACK' ? 'Feedback' : 
                           'Question'}
                    value={messageContent}
                    onChange={(e) => setMessageContent(e.target.value)}
                    fullWidth
                    margin="normal"
                    multiline={messageType === 'FEEDBACK'}
                    rows={messageType === 'FEEDBACK' ? 3 : 1}
                  />
                )}
                
                <Button
                  variant="contained"
                  color="primary"
                  onClick={sendMessage}
                  disabled={loading}
                  fullWidth
                  sx={{ mt: 2 }}
                >
                  {loading ? <CircularProgress size={24} /> : 'Send Message'}
                </Button>
              </CardContent>
            </Card>
          )}
        </Box>
        
        <Box sx={{ width: { xs: '100%', md: '55%' } }}>
          {sessionState && (
            <>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Tabs value={activeTab} onChange={handleTabChange} aria-label="agent collaboration tabs">
                  <Tab label="Progress & Artifacts" />
                  <Tab label="Discussions" />
                  <Tab label="Consultations" />
                </Tabs>
                
                <Box>
                  {activeTab === 1 && (
                    <Tooltip title="Start a new discussion between agents">
                      <Button
                        variant="outlined"
                        startIcon={<ChatIcon />}
                        onClick={() => setDiscussionDialogOpen(true)}
                        disabled={!sessionId}
                      >
                        New Discussion
                      </Button>
                    </Tooltip>
                  )}
                  
                  {activeTab === 2 && (
                    <Tooltip title="Request agent consultation">
                      <Button
                        variant="outlined"
                        startIcon={<QuestionAnswerIcon />}
                        onClick={() => setConsultationDialogOpen(true)}
                        disabled={!sessionId}
                      >
                        New Consultation
                      </Button>
                    </Tooltip>
                  )}
                </Box>
              </Box>
              
              {/* Progress & Artifacts Tab */}
              <Box sx={{ display: activeTab === 0 ? 'block' : 'none' }}>
                <Card sx={{ mb: 4 }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Agent Progress
                    </Typography>
                    <AgentProgressDisplay state={sessionState} />
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Artifacts
                    </Typography>
                    <ArtifactViewer artifacts={sessionState.artifacts} />
                  </CardContent>
                </Card>
                
                <Card sx={{ mt: 3 }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Agent Reasoning
                    </Typography>
                    <ReasoningDisplay state={sessionState} />
                  </CardContent>
                </Card>
              </Box>
              
              {/* Discussions Tab */}
              <Box sx={{ display: activeTab === 1 ? 'block' : 'none' }}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Agent Discussions
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      Discussions allow multiple agents to collaborate on a specific topic, sharing their expertise and reaching a consensus.
                    </Typography>
                    <DiscussionsPanel discussions={sessionState.discussions || {}} />
                  </CardContent>
                </Card>
              </Box>
              
              {/* Consultations Tab */}
              <Box sx={{ display: activeTab === 2 ? 'block' : 'none' }}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Agent Consultations
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      Consultations allow one agent to request specific expertise from another agent on a particular question.
                    </Typography>
                    <ConsultationsPanel consultations={sessionState.consultations || {}} />
                  </CardContent>
                </Card>
              </Box>
            </>
          )}
        </Box>
      </Box>
    </Container>
  );
}