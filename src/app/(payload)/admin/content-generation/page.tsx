'use client'

import React from 'react'
import ContentGenerationDashboard from '@/components/ContentGeneration/Dashboard'

export default function ContentGenerationPage() {
  return (
    <div className="custom-admin-page">
      <header className="page-header">
        <h1>AI Content Generation</h1>
        <p className="description">
          Generate high-quality, SEO-optimized content with minimal input using our AI-powered system.
        </p>
      </header>

      <ContentGenerationDashboard />

      <footer className="page-footer">
        <p>
          This tool uses AI to generate content. Always review the generated content before publishing.
        </p>
      </footer>

      <style jsx>{`
        .custom-admin-page {
          padding: 2rem;
          max-width: 1200px;
          margin: 0 auto;
        }

        .page-header {
          margin-bottom: 2rem;
          border-bottom: 1px solid var(--theme-elevation-100);
          padding-bottom: 1rem;
        }

        h1 {
          font-size: 2rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
        }

        .description {
          color: var(--theme-elevation-500);
          font-size: 1.1rem;
          line-height: 1.5;
        }

        .page-footer {
          margin-top: 3rem;
          padding-top: 1rem;
          border-top: 1px solid var(--theme-elevation-100);
          color: var(--theme-elevation-500);
          font-size: 0.9rem;
        }
      `}</style>
    </div>
  )
}