// src/app/.well-known/agent.json/route.ts

import { NextResponse } from 'next/server';
import { agentCards } from '../../(payload)/api/agents/a2aimplementation';

// Main agent card for the content generation system
const mainAgentCard = {
  name: "AuthenCIO Content Generation System",
  description: "A collaborative AI system for generating high-quality, SEO-optimized content",
  url: "https://authencio.com/api/content-generation",
  provider: {
    organization: "AuthenCIO",
    url: "https://authencio.com"
  },
  version: "1.0.0",
  capabilities: {
    streaming: true,
    pushNotifications: true,
    stateTransitionHistory: true
  },
  authentication: {
    schemes: ["none"]
  },
  defaultInputModes: ["text/plain", "application/json"],
  defaultOutputModes: ["text/plain", "application/json"],
  skills: [
    {
      id: "content-generation",
      name: "Content Generation",
      description: "Generates high-quality, SEO-optimized content for various purposes",
      tags: ["content", "seo", "marketing"],
      examples: [
        "Generate a blog post about sustainable living",
        "Create a product page for eco-friendly water bottles"
      ]
    }
  ],
  // Include links to specialized agents
  specializedAgents: Object.keys(agentCards).map(key => ({
    id: key,
    name: agentCards[key].name,
    url: agentCards[key].url
  }))
};

// GET handler for agent card discovery
export async function GET() {
  return NextResponse.json(mainAgentCard);
}