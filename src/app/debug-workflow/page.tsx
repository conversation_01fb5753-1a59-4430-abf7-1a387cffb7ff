'use client';

import { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Container,
  Typography,
  TextField,
  Paper,
  Grid,
  List,
  ListItem,
  ListItemText,
  Divider,
  CircularProgress
} from '@mui/material';

export default function DebugWorkflowPage() {
  const [sessionId, setSessionId] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [result, setResult] = useState<any>(null);

  // Add a log message
  const addLog = (message: string) => {
    setLogs(prevLogs => [
      `${new Date().toISOString()} - ${message}`,
      ...prevLogs
    ]);
  };

  // Test artifact storage
  const testArtifactStorage = async () => {
    if (!sessionId) {
      setError('Please enter a session ID');
      return;
    }

    setLoading(true);
    setError(null);
    addLog(`Testing artifact storage for session ${sessionId}...`);

    try {
      const response = await fetch('/api/agents/dynamic-collaboration-v2/debug', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'testArtifactStorage',
          sessionId
        })
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to test artifact storage');
      }

      setResult(data.result);
      addLog(`Artifact storage test completed. Artifact ID: ${data.result.artifactId}`);
      addLog(`Artifact count before: ${data.result.artifactCountBefore}, after: ${data.result.artifactCountAfter}`);
      addLog(`Generated artifacts count before: ${data.result.generatedArtifactsCountBefore}, after: ${data.result.generatedArtifactsCountAfter}`);
      addLog(`Artifact exists in state: ${data.result.artifactExists}`);
      addLog(`Artifact in generated list: ${data.result.artifactInGeneratedList}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      addLog(`Error testing artifact storage: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Test goal completion
  const testGoalCompletion = async () => {
    if (!sessionId) {
      setError('Please enter a session ID');
      return;
    }

    setLoading(true);
    setError(null);
    addLog(`Testing goal completion for session ${sessionId}...`);

    try {
      const response = await fetch('/api/agents/dynamic-collaboration-v2/debug', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'testGoalCompletion',
          sessionId
        })
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to test goal completion');
      }

      setResult(data.result);
      
      if (data.result.message) {
        addLog(data.result.message);
      } else {
        addLog(`Goal completion test completed. Goal ID: ${data.result.goalId}`);
        addLog(`Goal type: ${data.result.goalType}`);
        addLog(`Artifact ID: ${data.result.artifactId}`);
        addLog(`Artifact exists in state: ${data.result.artifactExists}`);
        addLog(`Goal completed: ${data.result.goalCompleted}`);
        addLog(`Goal in completed list: ${data.result.goalInCompletedList}`);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      addLog(`Error testing goal completion: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Get state
  const getState = async () => {
    if (!sessionId) {
      setError('Please enter a session ID');
      return;
    }

    setLoading(true);
    setError(null);
    addLog(`Getting state for session ${sessionId}...`);

    try {
      const response = await fetch('/api/agents/dynamic-collaboration-v2/debug', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'getState',
          sessionId
        })
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to get state');
      }

      setResult(data.result);
      addLog(`State retrieved successfully`);
      
      // Log some key state information
      const state = data.result.state;
      addLog(`Current phase: ${state.currentPhase}`);
      addLog(`Completed goals: ${JSON.stringify(state.completedGoals)}`);
      addLog(`Artifacts count: ${Object.keys(state.artifacts || {}).length}`);
      addLog(`Generated artifacts count: ${state.generatedArtifacts?.length || 0}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      addLog(`Error getting state: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4 }}>
      <Typography variant="h4" gutterBottom>
        Debug Workflow
      </Typography>

      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Session ID"
              value={sessionId}
              onChange={(e) => setSessionId(e.target.value)}
              variant="outlined"
              placeholder="Enter session ID"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                color="primary"
                onClick={getState}
                disabled={loading}
              >
                Get State
              </Button>
              <Button
                variant="contained"
                color="secondary"
                onClick={testArtifactStorage}
                disabled={loading}
              >
                Test Artifact Storage
              </Button>
              <Button
                variant="contained"
                color="info"
                onClick={testGoalCompletion}
                disabled={loading}
              >
                Test Goal Completion
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Paper sx={{ p: 2, mb: 2, bgcolor: 'error.light' }}>
          <Typography color="error">{error}</Typography>
        </Paper>
      )}

      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '500px', overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom>
              Logs
            </Typography>
            <List>
              {logs.map((log, index) => (
                <ListItem key={index} divider={index < logs.length - 1}>
                  <ListItemText primary={log} />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '500px', overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom>
              Result
            </Typography>
            {result && (
              <pre>{JSON.stringify(result, null, 2)}</pre>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
}
