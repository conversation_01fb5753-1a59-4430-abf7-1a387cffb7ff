# Centralized Agent State Management

This directory contains a set of React hooks that implement a centralized state management approach for collaborative agent messaging in AuthenCIO. These hooks make it easier to implement consistent behavior across different specialized AI agents while ensuring efficient communication and state tracking.

## Key Components

### CollaborationContext

The `CollaborationContext` (located in `src/contexts/CollaborationContext.tsx`) provides the foundation for centralized state management. It maintains:

- Artifacts generated by all agents
- Consultations provided between agents
- Agent-specific state for each participating agent
- Messages exchanged between agents

### Agent Hooks

We provide several hooks that agents can use to interact with the collaborative state:

#### useAgentStateManager

Manages agent-specific state, including:
- Tracking processed messages
- Tracking artifacts created and received
- Tracking consultations provided and received
- Managing feedback from other agents

```typescript
const { 
  agentState,
  trackProcessedMessage,
  hasProcessedMessage,
  trackNewArtifact,
  trackUpdatedArtifact,
  // ...and more
} = useAgentStateManager('agent-id');
```

#### useAgentMessaging

Provides standardized message creation functions for agent-to-agent communication:

```typescript
const {
  sendAcknowledgment,
  requestArtifact,
  deliverArtifact,
  sendFeedback,
  requestConsultation,
  // ...and more
} = useAgentMessaging('agent-id');
```

#### useAgentHandler

Combines state management and messaging functionality with handler utilities:

```typescript
const handler = useAgentHandler('agent-id');
const {
  // State management
  agentState,
  trackProcessedMessage,
  
  // Messaging
  sendAcknowledgment,
  deliverArtifact,
  
  // Handler utilities
  standardizeResult,
  createErrorResponse
} = handler;
```

## Standardized Result Format

All agent message handlers should return results in a standardized format:

```typescript
interface StandardizedHandlerResult {
  response: IterativeMessage;
  stateUpdates?: Record<string, any>;
  artifactUpdates?: {
    new?: Record<string, IterativeArtifact>;
    updated?: Record<string, IterativeArtifact>;
  };
  consultationUpdates?: {
    new?: Record<string, Consultation>;
  };
}
```

This format ensures that all state updates are properly tracked and applied to the central collaboration state.

## Examples

See the `src/app/(payload)/api/agents/collaborative-iteration/examples/content-generation-with-hooks.ts` file for a complete example of implementing an agent using these hooks.

## Migration Guide

To migrate an existing agent to use the new centralized state management:

1. Implement your agent logic using the `useAgentHandler` hook
2. Standardize handler results using the `StandardizedHandlerResult` format
3. Use the messaging functions from the hook rather than creating messages directly
4. Use the state management functions to track processed messages, artifacts, etc.

The `standardizeResult` function can help convert from the old format to the new format during the transition period.
