import { useCollaboration } from '../contexts/CollaborationContext';
import { 
  AgentState, 
  IterativeArtifact, 
  Consultation, 
  AgentId 
} from '../app/(payload)/api/agents/collaborative-iteration/types';

/**
 * Hook to manage agent state within the collaboration context
 * Provides utilities for tracking processed messages, artifacts, consultations
 * and other agent-specific state
 */
export function useAgentStateManager(agentId: AgentId) {
  const { state, updateAgentState, addArtifact, updateArtifact, addConsultation, updateConsultation } = useCollaboration();
  
  // Get the current agent state
  const agentState = state.agentStates?.[agentId] || {
    processedRequests: [],
    generatedArtifacts: [],
    consultationsProvided: [],
    consultationsReceived: [],
    lastUpdated: new Date().toISOString()
  };
  
  /**
   * Update the agent's state with new values
   */
  const updateState = (updates: Partial<AgentState>) => {
    updateAgentState(agentId, updates);
  };
  
  /**
   * Track that a message has been processed by this agent
   */
  const trackProcessedMessage = (messageId: string) => {
    const processedRequests = [...(agentState.processedRequests || [])];
    
    if (!processedRequests.includes(messageId)) {
      processedRequests.push(messageId);
      updateState({ processedRequests });
    }
  };
  
  /**
   * Check if a message has already been processed
   */
  const hasProcessedMessage = (messageId: string): boolean => {
    return (agentState.processedRequests || []).includes(messageId);
  };
  
  /**
   * Track a new artifact created by this agent
   */
  const trackNewArtifact = (artifact: IterativeArtifact) => {
    // Add to the collaborative state
    addArtifact(artifact);
    
    // Update the agent's list of generated artifacts
    const generatedArtifacts = [...(agentState.generatedArtifacts || [])];
    if (!generatedArtifacts.includes(artifact.id)) {
      generatedArtifacts.push(artifact.id);
      updateState({ generatedArtifacts });
    }
    
    return artifact.id;
  };
  
  /**
   * Track an updated artifact
   */
  const trackUpdatedArtifact = (artifactId: string, updates: Partial<IterativeArtifact>) => {
    updateArtifact(artifactId, updates);
    return artifactId;
  };
  
  /**
   * Track a new consultation provided by this agent
   */
  const trackNewConsultation = (consultation: Consultation) => {
    // Add to the collaborative state
    addConsultation(consultation);
    
    // Update the agent's list of provided consultations
    const consultationsProvided = [...(agentState.consultationsProvided || [])];
    if (!consultationsProvided.includes(consultation.id)) {
      consultationsProvided.push(consultation.id);
      updateState({ consultationsProvided });
    }
    
    return consultation.id;
  };
  
  /**
   * Track a consultation received from another agent
   */
  const trackReceivedConsultation = (consultationId: string) => {
    const consultationsReceived = [...(agentState.consultationsReceived || [])];
    if (!consultationsReceived.includes(consultationId)) {
      consultationsReceived.push(consultationId);
      updateState({ consultationsReceived });
    }
  };
  
  /**
   * Track an updated consultation
   */
  const trackUpdatedConsultation = (consultationId: string, updates: Partial<Consultation>) => {
    updateConsultation(consultationId, updates);
    return consultationId;
  };
  
  /**
   * Track feedback received from another agent
   */
  const trackFeedback = (fromAgent: AgentId, messageId: string, content: string, artifactId?: string) => {
    const feedback = agentState.feedback || {};
    
    const newFeedback = {
      ...feedback,
      [messageId]: {
        fromAgent,
        timestamp: new Date().toISOString(),
        content,
        artifactId,
        acknowledged: false
      }
    };
    
    updateState({ feedback: newFeedback });
  };
  
  /**
   * Mark feedback as acknowledged
   */
  const acknowledgeFeedback = (messageId: string) => {
    const feedback = { ...(agentState.feedback || {}) };
    
    if (feedback[messageId]) {
      feedback[messageId] = {
        ...feedback[messageId],
        acknowledged: true
      };
      
      updateState({ feedback });
    }
  };
  
  return {
    agentState,
    updateState,
    trackProcessedMessage,
    hasProcessedMessage,
    trackNewArtifact,
    trackUpdatedArtifact,
    trackNewConsultation,
    trackReceivedConsultation,
    trackUpdatedConsultation,
    trackFeedback,
    acknowledgeFeedback
  };
}
