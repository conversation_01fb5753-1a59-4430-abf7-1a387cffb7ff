import { v4 as uuidv4 } from 'uuid';
import { useCollaboration } from '../contexts/CollaborationContext';
import { 
  AgentId, 
  IterativeMessage, 
  IterativeArtifact,
  Consultation,
  ArtifactType,
  AcknowledgmentContent,
  ArtifactRequestContent,
  ArtifactDeliveryContent,
  FeedbackContent,
  ConsultationRequestContent,
  ConsultationResponseContent
} from '../app/(payload)/api/agents/collaborative-iteration/types';
import { useAgentStateManager } from './useAgentStateManager';

/**
 * Hook to manage agent messaging within the collaboration context
 * Provides utilities for creating and sending different types of messages between agents
 */
export function useAgentMessaging(fromAgent: AgentId) {
  const { state, updateState } = useCollaboration();
  const { 
    trackProcessedMessage, 
    trackNewArtifact, 
    trackUpdatedArtifact,
    trackNewConsultation,
    trackFeedback
  } = useAgentStateManager(fromAgent);
  
  /**
   * Add a message to the collaboration state
   */
  const addMessage = (message: IterativeMessage) => {
    updateState({ 
      messages: [...(state.messages || []), message] 
    });
    return message;
  };
  
  /**
   * Create a basic message structure with common properties
   */
  const createBaseMessage = (toAgent: AgentId, type: string, content: Record<string, any>): IterativeMessage => {
    return {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: fromAgent,
      to: toAgent,
      type: type as any,
      content,
      conversationId: state.id
    };
  };
  
  /**
   * Send an acknowledgment message to another agent
   */
  const sendAcknowledgment = (toAgent: AgentId, originalMessageId: string, message: string) => {
    const content: AcknowledgmentContent = {
      message,
      originalMessageId
    };
    
    const acknowledgment = createBaseMessage(toAgent, 'ACKNOWLEDGMENT', content);
    return addMessage(acknowledgment);
  };
  
  /**
   * Send an artifact request to another agent
   */
  const requestArtifact = (toAgent: AgentId, artifactType: ArtifactType, description: string) => {
    const content: ArtifactRequestContent = {
      artifactType,
      description
    };
    
    const request = createBaseMessage(toAgent, 'ARTIFACT_REQUEST', content);
    return addMessage(request);
  };
  
  /**
   * Send an artifact to another agent
   */
  const deliverArtifact = (toAgent: AgentId, artifact: IterativeArtifact, originalMessageId?: string) => {
    // Track the artifact in the state
    const artifactId = trackNewArtifact(artifact);
    
    const content: ArtifactDeliveryContent = {
      artifactId,
      artifactType: artifact.type,
      originalMessageId
    };
    
    const delivery = createBaseMessage(toAgent, 'ARTIFACT_DELIVERY', content);
    return addMessage(delivery);
  };
  
  /**
   * Update an existing artifact and notify relevant agents
   */
  const updateArtifact = (toAgent: AgentId, artifactId: string, updates: Partial<IterativeArtifact>, originalMessageId?: string) => {
    // Update the artifact in the state
    trackUpdatedArtifact(artifactId, updates);
    
    const content: ArtifactDeliveryContent = {
      artifactId,
      artifactType: updates.type || 'GENERIC',
      isUpdate: true,
      originalMessageId
    };
    
    const update = createBaseMessage(toAgent, 'ARTIFACT_DELIVERY', content);
    return addMessage(update);
  };
  
  /**
   * Send feedback to another agent
   */
  const sendFeedback = (toAgent: AgentId, feedbackContent: string, artifactId?: string, originalMessageId?: string) => {
    const content: FeedbackContent = {
      feedback: feedbackContent,
      artifactId,
      originalMessageId
    };
    
    // Track the feedback in our own state
    if (originalMessageId) {
      trackProcessedMessage(originalMessageId);
    }
    
    const feedback = createBaseMessage(toAgent, 'FEEDBACK', content);
    return addMessage(feedback);
  };
  
  /**
   * Request a consultation from another agent
   */
  const requestConsultation = (toAgent: AgentId, question: string, context?: Record<string, any>) => {
    const content: ConsultationRequestContent = {
      question,
      context: context || {}
    };
    
    const request = createBaseMessage(toAgent, 'CONSULTATION_REQUEST', content);
    return addMessage(request);
  };
  
  /**
   * Respond to a consultation request
   */
  const respondToConsultation = (toAgent: AgentId, consultation: Consultation, originalMessageId: string) => {
    // Track the consultation in the state
    const consultationId = trackNewConsultation(consultation);
    
    const content: ConsultationResponseContent = {
      consultationId,
      response: consultation.response,
      originalMessageId
    };
    
    // Mark the original message as processed
    trackProcessedMessage(originalMessageId);
    
    const response = createBaseMessage(toAgent, 'CONSULTATION_RESPONSE', content);
    return addMessage(response);
  };
  
  /**
   * Send an error message to another agent
   */
  const sendError = (toAgent: AgentId, errorMessage: string, originalMessageId?: string) => {
    const content = {
      message: errorMessage,
      originalMessageId
    };
    
    const error = createBaseMessage(toAgent, 'ERROR', content);
    return addMessage(error);
  };
  
  return {
    sendAcknowledgment,
    requestArtifact,
    deliverArtifact,
    updateArtifact,
    sendFeedback,
    requestConsultation,
    respondToConsultation,
    sendError
  };
}
