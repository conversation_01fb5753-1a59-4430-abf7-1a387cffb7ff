'use client';

import { useState, useEffect, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';

interface AgentHookOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface CollaborativeBrief {
  topic: string;
  contentType: 'blog-article' | 'product-page' | 'buying-guide';
  targetAudience: string;
  tone: string;
  keywords: string[];
  [key: string]: any;
}

export interface AgentMessage {
  id?: string;
  from: string;
  to: string | string[];
  type: string;
  content: any;
  conversationId?: string;
  timestamp?: string;
  reasoning?: {
    process: string;
    steps: string[];
    timestamp: string;
    thoughts?: string[];
    considerations?: string[];
    decision?: string;
    confidence?: number;
    alternatives?: string[];
  };
}

export const useCollaborativeAgents = (options: AgentHookOptions = {}) => {
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [sessionState, setSessionState] = useState<any | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  // Default options
  const {
    autoRefresh = true,
    refreshInterval = 5000
  } = options;
  
  /**
   * Initialize a new content generation session
   */
  const initializeSession = async (brief: CollaborativeBrief) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/collaborative-agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'create_session',
          data: brief
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to create session');
      }
      
      setSessionId(data.sessionId);
      setSessionState(data.state);
      
      return data.sessionId;
    } catch (err: any) {
      setError(err.message || 'An unknown error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };
  
  /**
   * Fetch current session state
   */
  const fetchSessionState = useCallback(async (id: string = sessionId) => {
    if (!id) {
      return null;
    }
    
    setLoading(true);
    
    try {
      const response = await fetch(`/api/collaborative-agents?sessionId=${id}`);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch session state');
      }
      
      setSessionState(data.state);
      return data.state;
    } catch (err) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sessionId]);
  
  /**
   * Send a message to an agent
   */
  const sendMessage = async (message: AgentMessage) => {
    if (!sessionId) {
      setError('No active session');
      throw new Error('No active session');
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // Add message metadata if not provided
      const completeMessage = {
        ...message,
        id: message.id || uuidv4(),
        timestamp: message.timestamp || new Date().toISOString(),
        conversationId: message.conversationId || sessionId
      };
      
      const response = await fetch('/api/collaborative-agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          message: completeMessage
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to send message');
      }
      
      setSessionState(data.state);
      return data.result;
    } catch (err: any) {
      setError(err.message || 'An unknown error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };
  
  /**
   * Request an initial content plan
   */
  const requestInitialContentPlan = async () => {
    if (!sessionState) {
      throw new Error('No active session');
    }
    
    const { topic, contentType, targetAudience, tone, keywords } = sessionState;
    
    // Request content strategy first
    await sendMessage({
      from: 'orchestrator',
      to: 'content-strategy',
      type: 'INITIAL_REQUEST',
      content: {
        topic,
        contentType,
        targetAudience,
        tone,
        keywords
      }
    });
    
    // Then request SEO keywords
    await sendMessage({
      from: 'orchestrator',
      to: 'seo-keyword',
      type: 'INITIAL_REQUEST',
      content: {
        topic,
        contentType,
        targetAudience,
        tone,
        keywords
      }
    });
    
    // Then request market research
    return sendMessage({
      from: 'orchestrator',
      to: 'market-research',
      type: 'INITIAL_REQUEST',
      content: {
        topic,
        contentType,
        targetAudience,
        tone,
        keywords
      }
    });
  };
  
  /**
   * Request content generation
   */
  const requestContentGeneration = async () => {
    if (!sessionState) {
      throw new Error('No active session');
    }
    
    const { topic, contentType, targetAudience, tone, keywords } = sessionState;
    
    return sendMessage({
      from: 'orchestrator',
      to: 'content-generation',
      type: 'INITIAL_REQUEST',
      content: {
        topic,
        contentType,
        targetAudience,
        tone,
        keywords
      }
    });
  };
  
  /**
   * Request a specific artifact type from an agent
   */
  const requestArtifact = async (
    agentId: string, 
    artifactType: string, 
    description: string = 'Please generate this artifact'
  ) => {
    return sendMessage({
      from: 'orchestrator',
      to: agentId,
      type: 'ARTIFACT_REQUEST',
      content: {
        artifactType,
        description
      }
    });
  };
  
  /**
   * Send feedback on an artifact
   */
  const sendFeedback = async (
    fromAgent: string,
    toAgent: string,
    artifactId: string,
    artifactData: any,
    feedback: string
  ) => {
    if (!sessionId) {
      setError('No active session');
      throw new Error('No active session');
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/collaborative-agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'request_feedback',
          sessionId,
          fromAgent,
          toAgent,
          artifactId,
          artifactData,
          reasoning: {
            process: 'Expert feedback on artifact',
            steps: [`${fromAgent} providing feedback to ${toAgent} on artifact ${artifactId}`],
            timestamp: new Date().toISOString(),
            thoughts: ['Need expertise on this artifact', 'Improvement through collaborative review'],
            considerations: ['Expert domain knowledge', 'Iterative refinement'],
            decision: 'Request specialized feedback',
            confidence: 0.9
          }
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to request feedback');
      }
      
      setSessionState(data.state);
      return data.feedback;
    } catch (err: any) {
      setError(err.message || 'An unknown error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };
  
  /**
   * Request consultation from an agent
   */
  const requestConsultation = async (
    fromAgent: string,
    toAgent: string,
    question: string,
    context: any = {}
  ) => {
    if (!sessionId) {
      setError('No active session');
      throw new Error('No active session');
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/collaborative-agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'request_consultation',
          sessionId,
          fromAgent,
          toAgent,
          question,
          context: {
            ...context,
            topic: sessionState?.topic,
            contentType: sessionState?.contentType
          },
          reasoning: {
            process: 'Requesting specialized input',
            steps: [`${fromAgent} requesting consultation from ${toAgent}`],
            timestamp: new Date().toISOString(),
            thoughts: ['Need specialized expertise', 'Cross-agent collaboration helps improve quality'],
            considerations: ['Agent specialization', 'Knowledge sharing'],
            decision: 'Request targeted consultation',
            confidence: 0.9
          }
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to request consultation');
      }
      
      setSessionState(data.state);
      return data.consultation;
    } catch (err: any) {
      setError(err.message || 'An unknown error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };
  
  /**
   * Reset the current session
   */
  const resetSession = () => {
    setSessionId(null);
    setSessionState(null);
    setError(null);
  };
  
  // Auto-refresh session state
  useEffect(() => {
    if (!sessionId || !autoRefresh) return;
    
    const interval = setInterval(() => {
      fetchSessionState();
    }, refreshInterval);
    
    return () => clearInterval(interval);
  }, [sessionId, autoRefresh, refreshInterval, fetchSessionState]);
  
  /**
   * Facilitate a discussion between multiple agents
   */
  const facilitateDiscussion = async (
    topic: string,
    leadAgent: string,
    participants: string[],
    initialPrompt?: string
  ) => {
    if (!sessionId) {
      setError('No active session');
      throw new Error('No active session');
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/collaborative-agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'facilitate_discussion',
          sessionId,
          topic,
          leadAgent,
          participants,
          initialPrompt: initialPrompt || `Discuss the topic: ${topic}`,
          reasoning: {
            process: 'Facilitating multi-agent discussion',
            steps: [`Starting discussion on topic: ${topic}`],
            timestamp: new Date().toISOString(),
            thoughts: ['Multiple perspectives needed', 'Collaborative problem-solving'],
            considerations: ['Diverse agent expertise', 'Structured discussion flow'],
            decision: 'Facilitate divergence-convergence discussion pattern',
            confidence: 0.95
          }
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to facilitate discussion');
      }
      
      setSessionState(data.state);
      return data.discussion;
    } catch (err: any) {
      setError(err.message || 'An unknown error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };
  
  /**
   * Broadcast an artifact to all agents
   */
  const broadcastArtifact = async (
    fromAgent: string,
    artifactId: string,
    message: string
  ) => {
    if (!sessionId) {
      setError('No active session');
      throw new Error('No active session');
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // Use the direct message API to broadcast
      const response = await sendMessage({
        from: fromAgent,
        to: 'all',
        type: 'BROADCAST_ARTIFACT',
        content: {
          artifactId,
          message
        },
        reasoning: {
          process: 'Broadcasting completed artifact',
          steps: [`${fromAgent} broadcasting artifact ${artifactId} to all agents`],
          timestamp: new Date().toISOString(),
          thoughts: ['Important milestone achieved', 'All agents need this information'],
          considerations: ['Transparent information sharing', 'Cross-agent awareness'],
          decision: 'Broadcast to ensure all agents have latest context',
          confidence: 0.95
        }
      });
      
      return response;
    } catch (err: any) {
      setError(err.message || 'An unknown error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };
  
  return {
    sessionId,
    sessionState,
    loading,
    error,
    initializeSession,
    fetchSessionState,
    sendMessage,
    requestInitialContentPlan,
    requestContentGeneration,
    requestArtifact,
    sendFeedback,
    requestConsultation,
    facilitateDiscussion,
    broadcastArtifact,
    resetSession,
    artifacts: sessionState?.artifacts || {},
    messages: sessionState?.messages || [],
    currentPhase: sessionState?.currentPhase || 'planning',
    agentStates: sessionState?.agentStates || {}
  };
};
