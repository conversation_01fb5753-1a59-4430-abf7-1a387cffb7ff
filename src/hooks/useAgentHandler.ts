import {
  AgentId,
  AgentState,
  IterativeMessage,
  IterativeArtifact,
  Consultation
} from '../app/(payload)/api/agents/collaborative-iteration/types';
import { useAgentStateManager } from './useAgentStateManager';
import { useAgentMessaging } from './useAgentMessaging';

/**
 * StandardizedHandlerResult provides a consistent structure for all agent handler results
 * This makes it easier to integrate with the central collaboration state
 */
export interface StandardizedHandlerResult {
  response: IterativeMessage;
  stateUpdates?: Record<string, any>;
  artifactUpdates?: {
    new?: Record<string, IterativeArtifact>;
    updated?: Record<string, IterativeArtifact>;
  };
  consultationUpdates?: {
    new?: Record<string, Consultation>;
  };
}

/**
 * Hook to standardize agent handlers and integrate with centralized state management
 * Provides utilities for handling messages and updating state in a consistent manner
 */
export function useAgentHandler(agentId: AgentId) {
  const stateManager = useAgentStateManager(agentId);
  const messaging = useAgentMessaging(agentId);
  
  /**
   * Standardize a handler result from the old format to the new format
   * This helps with backward compatibility during migration
   */
  const standardizeResult = (result: any): StandardizedHandlerResult => {
    // If result is already in standardized format, return it as is
    if (result.response && (result.stateUpdates !== undefined || 
                            result.artifactUpdates !== undefined || 
                            result.consultationUpdates !== undefined)) {
      return result;
    }
    
    // Extract components from legacy format
    const { 
      response,
      updatedState, 
      newArtifacts, 
      updatedArtifacts,
      newConsultations
    } = result;
    
    // Build standardized result
    const standardized: StandardizedHandlerResult = {
      response
    };
    
    // Add state updates if provided
    if (updatedState) {
      standardized.stateUpdates = {};
      
      // Extract agent-specific state
      if (updatedState.agentStates?.[agentId]) {
        standardized.stateUpdates.agentStates = {
          [agentId]: updatedState.agentStates[agentId]
        };
      } 
      // Handle legacy format where agent state was top-level
      else {
        standardized.stateUpdates.agentStates = {
          [agentId]: {
            processedRequests: updatedState.processedRequests || [],
            generatedArtifacts: updatedState.generatedArtifacts || [],
            consultationsProvided: updatedState.consultationsProvided || [],
            consultationsReceived: updatedState.consultationsReceived || [],
            lastUpdated: updatedState.lastUpdated || new Date().toISOString()
          }
        };
      }
    }
    
    // Add artifact updates if provided
    if (newArtifacts || updatedArtifacts) {
      standardized.artifactUpdates = {};
      
      if (newArtifacts) {
        standardized.artifactUpdates.new = newArtifacts;
      }
      
      if (updatedArtifacts) {
        standardized.artifactUpdates.updated = updatedArtifacts;
      }
    }
    
    // Add consultation updates if provided
    if (newConsultations) {
      standardized.consultationUpdates = {
        new: newConsultations
      };
    }
    
    return standardized;
  };
  
  /**
   * Apply the result of a handler to the collaboration state
   */
  const applyHandlerResult = (result: StandardizedHandlerResult) => {
    // Process state updates
    if (result.stateUpdates) {
      if (result.stateUpdates.agentStates?.[agentId]) {
        stateManager.updateState(result.stateUpdates.agentStates[agentId]);
      }
    }
    
    // Process artifact updates
    if (result.artifactUpdates) {
      // Process new artifacts
      if (result.artifactUpdates.new) {
        Object.values(result.artifactUpdates.new).forEach(artifact => {
          stateManager.trackNewArtifact(artifact);
        });
      }
      
      // Process updated artifacts
      if (result.artifactUpdates.updated) {
        Object.entries(result.artifactUpdates.updated).forEach(([id, artifact]) => {
          stateManager.trackUpdatedArtifact(id, artifact);
        });
      }
    }
    
    // Process consultation updates
    if (result.consultationUpdates?.new) {
      Object.values(result.consultationUpdates.new).forEach(consultation => {
        stateManager.trackNewConsultation(consultation);
      });
    }
    
    // Always track the message being processed
    const messageId = result.response.content.originalMessageId;
    if (messageId) {
      stateManager.trackProcessedMessage(messageId);
    }
    
    return result.response;
  };
  
  /**
   * Create an error response when a handler fails
   */
  const createErrorResponse = (toAgent: AgentId, error: any, originalMessageId?: string): StandardizedHandlerResult => {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return {
      response: {
        id: crypto.randomUUID(),
        timestamp: new Date().toISOString(),
        from: agentId,
        to: toAgent,
        type: 'ERROR',
        content: {
          message: `Error processing message: ${errorMessage}`,
          originalMessageId
        },
        conversationId: ''
      },
      stateUpdates: {}
    };
  };
  
  return {
    ...stateManager,
    ...messaging,
    standardizeResult,
    applyHandlerResult,
    createErrorResponse
  };
}
