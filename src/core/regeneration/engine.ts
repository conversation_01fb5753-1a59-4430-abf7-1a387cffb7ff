/**
 * Regeneration Engine
 * Handles AI-powered artifact regeneration based on feedback
 */

import { v4 as uuidv4 } from 'uuid';
import { FeedbackProcessor } from '../feedback/processor';
import { 
  RegenerationRequest, 
  RegenerationResult, 
  RegenerationStatus,
  FeedbackItem,
  RegenerationPrompt
} from '../feedback/types';
import { WorkflowArtifact, ArtifactType, ArtifactStatus } from '../workflow/types';

export class RegenerationEngine {
  private feedbackProcessor: FeedbackProcessor;
  private activeRegenerations: Map<string, RegenerationRequest> = new Map();
  private workflowEngine?: any; // Will be injected

  constructor() {
    this.feedbackProcessor = new FeedbackProcessor();
  }

  /**
   * Set the workflow engine for artifact storage
   */
  setWorkflowEngine(workflowEngine: any): void {
    this.workflowEngine = workflowEngine;
  }

  /**
   * Main method to regenerate an artifact based on feedback
   */
  async regenerateArtifact(
    originalArtifact: WorkflowArtifact, 
    feedback: string,
    userId: string,
    maxIterations: number = 3
  ): Promise<RegenerationResult> {
    const startTime = Date.now();
    
    try {
      console.log(`🔄 Starting regeneration for artifact ${originalArtifact.id}`);
      
      // Create regeneration request
      const request = this.createRegenerationRequest(originalArtifact, feedback, userId, maxIterations);
      this.activeRegenerations.set(request.id, request);

      // Update status to processing
      request.status = RegenerationStatus.PROCESSING;
      request.startedAt = new Date().toISOString();

      // Generate improvement prompt
      const prompt = this.buildRegenerationPrompt(originalArtifact, feedback);
      
      // Call AI model for regeneration
      const regeneratedContent = await this.callAIForRegeneration(prompt);
      
      // Process and validate the regenerated content
      const processedContent = this.processRegeneratedContent(regeneratedContent, originalArtifact);
      
      // Create new artifact with regenerated content
      const newArtifactId = await this.createRegeneratedArtifact(originalArtifact, processedContent, feedback);
      
      // Update request status
      request.status = RegenerationStatus.COMPLETED;
      request.completedAt = new Date().toISOString();
      request.newArtifactId = newArtifactId;

      const regenerationTime = Date.now() - startTime;
      
      // Track metrics
      this.trackRegenerationMetrics(originalArtifact.id, newArtifactId, regenerationTime);

      console.log(`✅ Regeneration completed for artifact ${originalArtifact.id} -> ${newArtifactId}`);

      return {
        success: true,
        newArtifactId,
        improvementsApplied: this.extractImprovementsApplied(feedback),
        regenerationTime,
        qualityScore: this.calculateQualityScore(originalArtifact, processedContent)
      };

    } catch (error) {
      console.error(`❌ Regeneration failed for artifact ${originalArtifact.id}:`, error);
      
      // Update request status to failed
      const request = this.activeRegenerations.get(originalArtifact.id);
      if (request) {
        request.status = RegenerationStatus.FAILED;
        request.completedAt = new Date().toISOString();
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown regeneration error',
        improvementsApplied: [],
        regenerationTime: Date.now() - startTime
      };
    }
  }

  /**
   * Build regeneration prompt using feedback processor
   */
  buildRegenerationPrompt(artifact: WorkflowArtifact, feedback: string): RegenerationPrompt {
    return this.feedbackProcessor.generateImprovementPrompt(
      artifact.content,
      feedback,
      artifact.type,
      [] // TODO: Add previous iterations from version history
    );
  }

  /**
   * Process and validate regenerated content
   */
  processRegeneratedContent(content: any, originalArtifact: WorkflowArtifact): any {
    try {
      // Parse content if it's a string
      let processedContent = content;
      if (typeof content === 'string') {
        try {
          processedContent = JSON.parse(content);
        } catch {
          // Keep as string if not valid JSON
          processedContent = content;
        }
      }

      // Validate content structure based on artifact type
      this.validateContentStructure(processedContent, originalArtifact.type);

      // Apply content sanitization
      processedContent = this.sanitizeContent(processedContent, originalArtifact.type);

      return processedContent;

    } catch (error) {
      console.error('Content processing failed:', error);
      throw new Error(`Failed to process regenerated content: ${error.message}`);
    }
  }

  /**
   * Track regeneration performance metrics
   */
  trackRegenerationMetrics(originalId: string, newId: string, regenerationTime: number): void {
    console.log(`📊 Regeneration metrics:`, {
      originalId,
      newId,
      regenerationTime: `${regenerationTime}ms`,
      timestamp: new Date().toISOString()
    });

    // TODO: Store metrics in database for analytics
  }

  /**
   * Get active regeneration status
   */
  getRegenerationStatus(requestId: string): RegenerationRequest | null {
    return this.activeRegenerations.get(requestId) || null;
  }

  /**
   * Cancel active regeneration
   */
  cancelRegeneration(requestId: string): boolean {
    const request = this.activeRegenerations.get(requestId);
    if (request && request.status === RegenerationStatus.PROCESSING) {
      request.status = RegenerationStatus.CANCELLED;
      request.completedAt = new Date().toISOString();
      return true;
    }
    return false;
  }

  // Private helper methods
  private createRegenerationRequest(
    artifact: WorkflowArtifact, 
    feedback: string, 
    userId: string,
    maxIterations: number
  ): RegenerationRequest {
    return {
      id: uuidv4(),
      originalArtifactId: artifact.id,
      feedback: [{
        id: uuidv4(),
        artifactId: artifact.id,
        reviewerId: userId,
        feedback,
        category: this.feedbackProcessor.categorizeFeedback(feedback),
        priority: this.feedbackProcessor.parseFeedback(feedback, artifact.type).priority,
        timestamp: new Date().toISOString(),
        processed: false,
        improvementAreas: this.feedbackProcessor.parseFeedback(feedback, artifact.type).improvementAreas
      }],
      status: RegenerationStatus.PENDING,
      createdAt: new Date().toISOString(),
      iterationNumber: 1, // TODO: Get from version history
      maxIterations,
      metadata: {
        userId,
        workflowId: artifact.executionId, // Using executionId as workflowId for now
        executionId: artifact.executionId,
        stepId: artifact.stepId
      }
    };
  }

  private async callAIForRegeneration(prompt: RegenerationPrompt): Promise<any> {
    try {
      console.log(`🤖 Calling AI for regeneration with prompt:`, {
        systemPromptLength: prompt.systemPrompt.length,
        userPromptLength: prompt.userPrompt.length,
        artifactType: prompt.context.artifactType
      });

      // For now, return a mock improved content
      // In a full implementation, this would call the AI service
      const mockImprovedContent = {
        ...prompt.context.originalContent,
        improved: true,
        feedback_addressed: prompt.context.feedback,
        regeneration_timestamp: new Date().toISOString(),
        improvements: [
          'Addressed feedback points',
          'Enhanced content quality',
          'Improved structure and clarity'
        ]
      };

      console.log(`✅ Mock regeneration completed`);
      return mockImprovedContent;

    } catch (error) {
      console.error('AI regeneration call failed:', error);
      throw new Error(`AI regeneration failed: ${error.message}`);
    }
  }

  private async createRegeneratedArtifact(
    originalArtifact: WorkflowArtifact,
    regeneratedContent: any,
    feedback: string
  ): Promise<string> {
    const newArtifactId = uuidv4();
    const now = new Date().toISOString();

    // Create new artifact with regenerated content
    const regeneratedArtifact: WorkflowArtifact = {
      ...originalArtifact,
      id: newArtifactId,
      content: regeneratedContent,
      status: ArtifactStatus.PENDING_APPROVAL, // Needs approval again
      version: (originalArtifact.version || 1) + 1,
      createdAt: now,
      updatedAt: now,
      createdBy: 'regeneration-engine',
      title: `${originalArtifact.title} (Regenerated v${(originalArtifact.version || 1) + 1})`,
      // Add regeneration metadata
      rejectedBy: undefined,
      rejectedAt: undefined,
      rejectionReason: undefined,
      approvedBy: undefined,
      approvedAt: undefined
    };

    // Store the regenerated artifact using the same storage mechanism as the workflow engine
    // We'll use the ContentItem format since that's how artifacts are stored
    const contentItem = {
      id: newArtifactId,
      type: this.mapArtifactTypeToContentType(originalArtifact.type),
      title: regeneratedArtifact.title,
      content: regeneratedContent,
      status: 'pending' as const,
      executionId: originalArtifact.executionId,
      stepId: originalArtifact.stepId,
      createdAt: now,
      updatedAt: now,
      metadata: {
        stepId: originalArtifact.stepId,
        executionId: originalArtifact.executionId,
        version: regeneratedArtifact.version,
        createdBy: 'regeneration-engine',
        artifactType: originalArtifact.type,
        regeneratedFrom: originalArtifact.id,
        regenerationFeedback: feedback,
        regenerationTimestamp: now
      }
    };

    // Store using a simple in-memory approach for now
    // In a full implementation, this would use the state store
    console.log(`📝 Created regenerated artifact:`, {
      id: newArtifactId,
      originalId: originalArtifact.id,
      version: regeneratedArtifact.version,
      title: regeneratedArtifact.title
    });

    return newArtifactId;
  }

  private mapArtifactTypeToContentType(artifactType: ArtifactType): string {
    switch (artifactType) {
      case ArtifactType.KEYWORD_RESEARCH:
        return 'keyword_research';
      case ArtifactType.BLOG_POST:
        return 'blog_post';
      case ArtifactType.CONTENT_DRAFT:
        return 'content_draft';
      case ArtifactType.SEO_OPTIMIZATION:
        return 'seo_optimization';
      default:
        return 'generic';
    }
  }

  private validateContentStructure(content: any, artifactType: ArtifactType): void {
    // Basic validation based on artifact type
    switch (artifactType) {
      case ArtifactType.KEYWORD_RESEARCH:
        if (!content.keywords || !Array.isArray(content.keywords)) {
          throw new Error('Keyword research must contain keywords array');
        }
        break;
      case ArtifactType.BLOG_POST:
        if (!content.title || !content.content) {
          throw new Error('Blog post must contain title and content');
        }
        break;
      case ArtifactType.CONTENT_DRAFT:
        if (!content || (typeof content === 'object' && Object.keys(content).length === 0)) {
          throw new Error('Content draft cannot be empty');
        }
        break;
    }
  }

  private sanitizeContent(content: any, artifactType: ArtifactType): any {
    // Basic content sanitization
    if (typeof content === 'string') {
      return content.trim();
    }

    if (typeof content === 'object' && content !== null) {
      const sanitized = { ...content };
      
      // Remove any potentially harmful properties
      delete sanitized.__proto__;
      delete sanitized.constructor;
      
      return sanitized;
    }

    return content;
  }

  private extractImprovementsApplied(feedback: string): string[] {
    // Extract improvement areas from feedback
    const analysis = this.feedbackProcessor.parseFeedback(feedback, 'generic');
    return analysis.improvementAreas;
  }

  private calculateQualityScore(originalArtifact: WorkflowArtifact, regeneratedContent: any): number {
    // Simple quality scoring based on content length and structure
    let score = 50; // Base score

    // Content length improvement
    const originalLength = JSON.stringify(originalArtifact.content).length;
    const newLength = JSON.stringify(regeneratedContent).length;
    
    if (newLength > originalLength * 1.1) {
      score += 20; // More detailed content
    }

    // Structure improvements (basic heuristics)
    if (typeof regeneratedContent === 'object' && regeneratedContent !== null) {
      const keys = Object.keys(regeneratedContent);
      if (keys.length > 3) {
        score += 15; // Well-structured content
      }
    }

    // Cap at 100
    return Math.min(score, 100);
  }
}
