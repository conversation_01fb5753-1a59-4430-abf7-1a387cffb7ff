/**
 * Feedback Processor
 * Analyzes and processes human feedback for artifact improvement
 */

import { 
  FeedbackItem, 
  FeedbackCategory, 
  FeedbackPriority, 
  FeedbackAnalysis,
  RegenerationPrompt,
  FeedbackTemplate
} from './types';

export class FeedbackProcessor {
  private feedbackTemplates: Map<string, FeedbackTemplate> = new Map();

  constructor() {
    this.initializeFeedbackTemplates();
  }

  /**
   * Parse feedback text and extract improvement areas
   */
  parseFeedback(feedback: string, artifactType: string): FeedbackAnalysis {
    const analysis: FeedbackAnalysis = {
      mainIssues: [],
      improvementAreas: [],
      suggestedChanges: [],
      priority: FeedbackPriority.MEDIUM,
      category: FeedbackCategory.CONTENT,
      actionable: true
    };

    // Extract main issues using keyword analysis
    analysis.mainIssues = this.extractMainIssues(feedback);
    
    // Identify improvement areas
    analysis.improvementAreas = this.identifyImprovementAreas(feedback, artifactType);
    
    // Extract specific suggestions
    analysis.suggestedChanges = this.extractSuggestions(feedback);
    
    // Determine priority based on feedback language
    analysis.priority = this.determinePriority(feedback);
    
    // Categorize feedback type
    analysis.category = this.categorizeFeedback(feedback);
    
    // Check if feedback is actionable
    analysis.actionable = this.isActionable(feedback);

    return analysis;
  }

  /**
   * Generate AI prompt for artifact regeneration based on feedback
   */
  generateImprovementPrompt(
    originalContent: any, 
    feedback: string, 
    artifactType: string,
    previousIterations?: string[]
  ): RegenerationPrompt {
    const analysis = this.parseFeedback(feedback, artifactType);
    
    const systemPrompt = this.buildSystemPrompt(artifactType, analysis);
    const userPrompt = this.buildUserPrompt(originalContent, feedback, analysis, previousIterations);
    
    return {
      systemPrompt,
      userPrompt,
      context: {
        originalContent,
        feedback,
        artifactType,
        previousIterations
      },
      constraints: this.getConstraintsForType(artifactType)
    };
  }

  /**
   * Categorize feedback into predefined categories
   */
  categorizeFeedback(feedback: string): FeedbackCategory {
    const feedbackLower = feedback.toLowerCase();
    
    // Content-related keywords
    if (this.containsKeywords(feedbackLower, ['content', 'information', 'facts', 'data', 'accuracy', 'correct'])) {
      return FeedbackCategory.CONTENT;
    }
    
    // Style-related keywords
    if (this.containsKeywords(feedbackLower, ['style', 'writing', 'flow', 'readability', 'clarity'])) {
      return FeedbackCategory.STYLE;
    }
    
    // Technical keywords
    if (this.containsKeywords(feedbackLower, ['technical', 'code', 'implementation', 'syntax', 'format'])) {
      return FeedbackCategory.TECHNICAL;
    }
    
    // Structure keywords
    if (this.containsKeywords(feedbackLower, ['structure', 'organization', 'layout', 'sections', 'order'])) {
      return FeedbackCategory.STRUCTURE;
    }
    
    // Tone keywords
    if (this.containsKeywords(feedbackLower, ['tone', 'voice', 'formal', 'casual', 'professional'])) {
      return FeedbackCategory.TONE;
    }
    
    // Accuracy keywords
    if (this.containsKeywords(feedbackLower, ['wrong', 'incorrect', 'error', 'mistake', 'inaccurate'])) {
      return FeedbackCategory.ACCURACY;
    }
    
    return FeedbackCategory.OTHER;
  }

  /**
   * Prioritize feedback items based on urgency and impact
   */
  prioritizeFeedback(feedbackItems: FeedbackItem[]): FeedbackItem[] {
    return feedbackItems.sort((a, b) => {
      const priorityOrder = {
        [FeedbackPriority.CRITICAL]: 4,
        [FeedbackPriority.HIGH]: 3,
        [FeedbackPriority.MEDIUM]: 2,
        [FeedbackPriority.LOW]: 1
      };
      
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * Get feedback templates for specific artifact types
   */
  getFeedbackTemplates(artifactType: string): FeedbackTemplate[] {
    return Array.from(this.feedbackTemplates.values())
      .filter(template => template.applicableTypes.includes(artifactType) || template.applicableTypes.includes('all'));
  }

  // Private helper methods
  private extractMainIssues(feedback: string): string[] {
    const issues: string[] = [];
    const sentences = feedback.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    for (const sentence of sentences) {
      if (this.containsKeywords(sentence.toLowerCase(), ['issue', 'problem', 'wrong', 'incorrect', 'missing'])) {
        issues.push(sentence.trim());
      }
    }
    
    return issues;
  }

  private identifyImprovementAreas(feedback: string, artifactType: string): string[] {
    const areas: string[] = [];
    const feedbackLower = feedback.toLowerCase();
    
    // Common improvement areas by artifact type
    const improvementKeywords = {
      'blog_post': ['seo', 'keywords', 'readability', 'engagement', 'structure'],
      'keyword_research': ['relevance', 'volume', 'competition', 'intent'],
      'content_draft': ['clarity', 'flow', 'accuracy', 'completeness'],
      'generic': ['quality', 'relevance', 'accuracy', 'completeness']
    };
    
    const keywords = improvementKeywords[artifactType] || improvementKeywords['generic'];
    
    for (const keyword of keywords) {
      if (feedbackLower.includes(keyword)) {
        areas.push(keyword);
      }
    }
    
    return areas;
  }

  private extractSuggestions(feedback: string): string[] {
    const suggestions: string[] = [];
    const sentences = feedback.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    for (const sentence of sentences) {
      if (this.containsKeywords(sentence.toLowerCase(), ['should', 'could', 'suggest', 'recommend', 'try', 'consider'])) {
        suggestions.push(sentence.trim());
      }
    }
    
    return suggestions;
  }

  private determinePriority(feedback: string): FeedbackPriority {
    const feedbackLower = feedback.toLowerCase();
    
    if (this.containsKeywords(feedbackLower, ['critical', 'urgent', 'immediately', 'must fix', 'broken'])) {
      return FeedbackPriority.CRITICAL;
    }
    
    if (this.containsKeywords(feedbackLower, ['important', 'significant', 'major', 'serious'])) {
      return FeedbackPriority.HIGH;
    }
    
    if (this.containsKeywords(feedbackLower, ['minor', 'small', 'slight', 'optional'])) {
      return FeedbackPriority.LOW;
    }
    
    return FeedbackPriority.MEDIUM;
  }

  private isActionable(feedback: string): boolean {
    const feedbackLower = feedback.toLowerCase();
    
    // Check for specific, actionable language
    const actionableKeywords = ['change', 'add', 'remove', 'improve', 'fix', 'update', 'modify', 'include'];
    const vagueKeywords = ['bad', 'good', 'nice', 'okay', 'fine'];
    
    const hasActionable = this.containsKeywords(feedbackLower, actionableKeywords);
    const isVague = this.containsKeywords(feedbackLower, vagueKeywords) && feedback.split(' ').length < 10;
    
    return hasActionable && !isVague;
  }

  private containsKeywords(text: string, keywords: string[]): boolean {
    return keywords.some(keyword => text.includes(keyword));
  }

  private buildSystemPrompt(artifactType: string, analysis: FeedbackAnalysis): string {
    return `You are an expert content improvement AI. Your task is to regenerate and improve an artifact based on human feedback.

Artifact Type: ${artifactType}
Feedback Category: ${analysis.category}
Priority: ${analysis.priority}

Key improvement areas identified:
${analysis.improvementAreas.map(area => `- ${area}`).join('\n')}

Guidelines:
1. Address all feedback points specifically and thoroughly
2. Maintain the original intent and core message
3. Improve quality while preserving the artifact's purpose
4. Ensure the output is better than the original in the areas mentioned
5. Keep the same format and structure unless feedback suggests otherwise

Focus on making targeted improvements rather than complete rewrites unless explicitly requested.`;
  }

  private buildUserPrompt(
    originalContent: any, 
    feedback: string, 
    analysis: FeedbackAnalysis,
    previousIterations?: string[]
  ): string {
    let prompt = `Please improve the following content based on the feedback provided:

ORIGINAL CONTENT:
${JSON.stringify(originalContent, null, 2)}

FEEDBACK:
${feedback}

SPECIFIC ISSUES TO ADDRESS:
${analysis.mainIssues.map(issue => `- ${issue}`).join('\n')}

SUGGESTED CHANGES:
${analysis.suggestedChanges.map(change => `- ${change}`).join('\n')}`;

    if (previousIterations && previousIterations.length > 0) {
      prompt += `\n\nPREVIOUS ITERATIONS:
This content has been improved ${previousIterations.length} time(s) before. Please ensure this iteration addresses the current feedback while building on previous improvements.`;
    }

    prompt += `\n\nPlease provide the improved content in the same format as the original, focusing on the specific feedback provided.`;

    return prompt;
  }

  private getConstraintsForType(artifactType: string): any {
    const constraints = {
      'blog_post': {
        format: 'markdown',
        style: 'engaging',
        tone: 'professional'
      },
      'keyword_research': {
        format: 'json',
        style: 'analytical',
        tone: 'objective'
      },
      'content_draft': {
        format: 'text',
        style: 'clear',
        tone: 'appropriate'
      }
    };

    return constraints[artifactType] || constraints['content_draft'];
  }

  private initializeFeedbackTemplates(): void {
    const templates: FeedbackTemplate[] = [
      {
        id: 'content-accuracy',
        name: 'Content Accuracy Issues',
        category: FeedbackCategory.ACCURACY,
        template: 'The content contains inaccuracies in {area}. Please verify and correct {specific_issues}.',
        placeholders: ['area', 'specific_issues'],
        applicableTypes: ['all']
      },
      {
        id: 'style-improvement',
        name: 'Style Improvements',
        category: FeedbackCategory.STYLE,
        template: 'The writing style needs improvement in {aspects}. Consider {suggestions}.',
        placeholders: ['aspects', 'suggestions'],
        applicableTypes: ['blog_post', 'content_draft']
      },
      {
        id: 'structure-reorganization',
        name: 'Structure Reorganization',
        category: FeedbackCategory.STRUCTURE,
        template: 'The content structure should be reorganized. {reorganization_suggestions}',
        placeholders: ['reorganization_suggestions'],
        applicableTypes: ['all']
      }
    ];

    templates.forEach(template => {
      this.feedbackTemplates.set(template.id, template);
    });
  }
}
