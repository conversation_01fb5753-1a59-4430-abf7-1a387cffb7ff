/**
 * Feedback and Regeneration System Types
 * Defines interfaces for processing feedback and regenerating artifacts
 */

export interface FeedbackItem {
  id: string;
  artifactId: string;
  reviewerId: string;
  feedback: string;
  category: FeedbackCategory;
  priority: FeedbackPriority;
  timestamp: string;
  processed: boolean;
  improvementAreas: string[];
}

export enum FeedbackCategory {
  CONTENT = 'content',
  STYLE = 'style', 
  TECHNICAL = 'technical',
  ACCURACY = 'accuracy',
  STRUCTURE = 'structure',
  TONE = 'tone',
  OTHER = 'other'
}

export enum FeedbackPriority {
  LOW = 'low',
  MEDIUM = 'medium', 
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface RegenerationRequest {
  id: string;
  originalArtifactId: string;
  feedback: FeedbackItem[];
  status: RegenerationStatus;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  newArtifactId?: string;
  iterationNumber: number;
  maxIterations: number;
  metadata: {
    userId: string;
    workflowId: string;
    executionId: string;
    stepId: string;
  };
}

export enum RegenerationStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface RegenerationResult {
  success: boolean;
  newArtifactId?: string;
  error?: string;
  improvementsApplied: string[];
  regenerationTime: number;
  qualityScore?: number;
}

export interface FeedbackAnalysis {
  mainIssues: string[];
  improvementAreas: string[];
  suggestedChanges: string[];
  priority: FeedbackPriority;
  category: FeedbackCategory;
  actionable: boolean;
}

export interface RegenerationPrompt {
  systemPrompt: string;
  userPrompt: string;
  context: {
    originalContent: any;
    feedback: string;
    artifactType: string;
    previousIterations?: string[];
  };
  constraints: {
    maxLength?: number;
    format?: string;
    style?: string;
    tone?: string;
  };
}

export interface FeedbackTemplate {
  id: string;
  name: string;
  category: FeedbackCategory;
  template: string;
  placeholders: string[];
  applicableTypes: string[];
}

export interface RegenerationMetrics {
  totalRegenerations: number;
  successRate: number;
  averageIterations: number;
  averageTime: number;
  commonFeedbackTypes: FeedbackCategory[];
  improvementTrends: {
    category: FeedbackCategory;
    frequency: number;
    successRate: number;
  }[];
}

export interface VersionWithFeedback {
  versionId: string;
  artifactId: string;
  content: any;
  feedback?: FeedbackItem[];
  regeneratedFrom?: string;
  regeneratedTo?: string;
  iterationNumber: number;
  createdAt: string;
  metadata: {
    regenerationReason?: string;
    improvementsApplied?: string[];
    qualityScore?: number;
  };
}
