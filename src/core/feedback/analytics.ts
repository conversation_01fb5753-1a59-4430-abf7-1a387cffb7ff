/**
 * Feedback Analytics System
 * 
 * Tracks and analyzes feedback patterns to improve the regeneration system
 */

export interface FeedbackMetrics {
  totalFeedbackCount: number;
  rejectionRate: number;
  regenerationSuccessRate: number;
  averageRegenerationTime: number;
  commonFeedbackAreas: { area: string; count: number }[];
  feedbackQualityScore: number;
}

export interface FeedbackPattern {
  area: string;
  keywords: string[];
  frequency: number;
  successRate: number;
  averageImprovementScore: number;
}

export class FeedbackAnalytics {
  private feedbackHistory: any[] = [];
  private regenerationHistory: any[] = [];

  /**
   * Track a new feedback submission
   */
  trackFeedback(feedback: {
    artifactId: string;
    feedback: string;
    approver: string;
    isRejection: boolean;
    specificAreas: string[];
    timestamp: string;
  }) {
    this.feedbackHistory.push({
      ...feedback,
      id: `feedback-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    });
  }

  /**
   * Track a regeneration outcome
   */
  trackRegeneration(regeneration: {
    requestId: string;
    originalArtifactId: string;
    newArtifactId?: string;
    status: 'completed' | 'failed';
    startTime: string;
    endTime: string;
    feedbackAreas: string[];
    improvementScore?: number;
  }) {
    this.regenerationHistory.push({
      ...regeneration,
      duration: new Date(regeneration.endTime).getTime() - new Date(regeneration.startTime).getTime()
    });
  }

  /**
   * Get comprehensive feedback metrics
   */
  getFeedbackMetrics(): FeedbackMetrics {
    const totalFeedback = this.feedbackHistory.length;
    const rejections = this.feedbackHistory.filter(f => f.isRejection).length;
    const completedRegenerations = this.regenerationHistory.filter(r => r.status === 'completed').length;
    const totalRegenerations = this.regenerationHistory.length;

    // Calculate common feedback areas
    const areaCount = new Map<string, number>();
    this.feedbackHistory.forEach(feedback => {
      feedback.specificAreas.forEach((area: string) => {
        areaCount.set(area, (areaCount.get(area) || 0) + 1);
      });
    });

    const commonFeedbackAreas = Array.from(areaCount.entries())
      .map(([area, count]) => ({ area, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Calculate average regeneration time
    const completedDurations = this.regenerationHistory
      .filter(r => r.status === 'completed')
      .map(r => r.duration);
    const averageRegenerationTime = completedDurations.length > 0
      ? completedDurations.reduce((sum, duration) => sum + duration, 0) / completedDurations.length
      : 0;

    // Calculate feedback quality score (based on actionability and specificity)
    const qualityScores = this.feedbackHistory.map(f => this.calculateFeedbackQuality(f.feedback));
    const feedbackQualityScore = qualityScores.length > 0
      ? qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length
      : 0;

    return {
      totalFeedbackCount: totalFeedback,
      rejectionRate: totalFeedback > 0 ? rejections / totalFeedback : 0,
      regenerationSuccessRate: totalRegenerations > 0 ? completedRegenerations / totalRegenerations : 0,
      averageRegenerationTime,
      commonFeedbackAreas,
      feedbackQualityScore
    };
  }

  /**
   * Identify feedback patterns for improvement
   */
  getFeedbackPatterns(): FeedbackPattern[] {
    const patterns = new Map<string, {
      keywords: Set<string>;
      frequency: number;
      successfulRegenerations: number;
      totalRegenerations: number;
      improvementScores: number[];
    }>();

    // Analyze feedback areas
    this.feedbackHistory.forEach(feedback => {
      feedback.specificAreas.forEach((area: string) => {
        if (!patterns.has(area)) {
          patterns.set(area, {
            keywords: new Set(),
            frequency: 0,
            successfulRegenerations: 0,
            totalRegenerations: 0,
            improvementScores: []
          });
        }

        const pattern = patterns.get(area)!;
        pattern.frequency++;

        // Extract keywords from feedback
        const words = feedback.feedback.toLowerCase().split(/\s+/);
        words.forEach(word => {
          if (word.length > 3) {
            pattern.keywords.add(word);
          }
        });
      });
    });

    // Match with regeneration outcomes
    this.regenerationHistory.forEach(regen => {
      regen.feedbackAreas.forEach((area: string) => {
        const pattern = patterns.get(area);
        if (pattern) {
          pattern.totalRegenerations++;
          if (regen.status === 'completed') {
            pattern.successfulRegenerations++;
            if (regen.improvementScore) {
              pattern.improvementScores.push(regen.improvementScore);
            }
          }
        }
      });
    });

    // Convert to array and calculate metrics
    return Array.from(patterns.entries()).map(([area, data]) => ({
      area,
      keywords: Array.from(data.keywords).slice(0, 10), // Top 10 keywords
      frequency: data.frequency,
      successRate: data.totalRegenerations > 0 ? data.successfulRegenerations / data.totalRegenerations : 0,
      averageImprovementScore: data.improvementScores.length > 0
        ? data.improvementScores.reduce((sum, score) => sum + score, 0) / data.improvementScores.length
        : 0
    })).sort((a, b) => b.frequency - a.frequency);
  }

  /**
   * Get feedback suggestions based on patterns
   */
  getFeedbackSuggestions(artifactType: string): string[] {
    const patterns = this.getFeedbackPatterns();
    const topPatterns = patterns.slice(0, 5);

    const suggestions = [
      'Be specific about what needs improvement',
      'Provide examples of desired changes',
      'Focus on one main area at a time',
      'Explain the impact of the issue',
      'Suggest concrete solutions'
    ];

    // Add pattern-based suggestions
    topPatterns.forEach(pattern => {
      if (pattern.successRate > 0.7) {
        suggestions.push(`Consider feedback about ${pattern.area} - it has a ${Math.round(pattern.successRate * 100)}% success rate`);
      }
    });

    return suggestions.slice(0, 8);
  }

  /**
   * Calculate feedback quality score (0-1)
   */
  private calculateFeedbackQuality(feedback: string): number {
    let score = 0;

    // Length check (not too short, not too long)
    const length = feedback.length;
    if (length >= 20 && length <= 500) {
      score += 0.2;
    }

    // Specificity check (contains specific terms)
    const specificTerms = ['improve', 'add', 'remove', 'change', 'clarify', 'expand', 'reduce'];
    const hasSpecificTerms = specificTerms.some(term => feedback.toLowerCase().includes(term));
    if (hasSpecificTerms) {
      score += 0.3;
    }

    // Constructive language check
    const constructiveTerms = ['please', 'could', 'should', 'consider', 'suggest'];
    const hasConstructiveLanguage = constructiveTerms.some(term => feedback.toLowerCase().includes(term));
    if (hasConstructiveLanguage) {
      score += 0.2;
    }

    // Actionability check (contains actionable verbs)
    const actionableVerbs = ['rewrite', 'restructure', 'include', 'focus', 'emphasize', 'detail'];
    const hasActionableVerbs = actionableVerbs.some(verb => feedback.toLowerCase().includes(verb));
    if (hasActionableVerbs) {
      score += 0.3;

    }

    return Math.min(score, 1.0);
  }

  /**
   * Export analytics data for reporting
   */
  exportAnalytics() {
    return {
      metrics: this.getFeedbackMetrics(),
      patterns: this.getFeedbackPatterns(),
      suggestions: this.getFeedbackSuggestions('general'),
      rawData: {
        feedbackHistory: this.feedbackHistory,
        regenerationHistory: this.regenerationHistory
      }
    };
  }

  /**
   * Clear analytics data (for testing or reset)
   */
  clearData() {
    this.feedbackHistory = [];
    this.regenerationHistory = [];
  }
}
