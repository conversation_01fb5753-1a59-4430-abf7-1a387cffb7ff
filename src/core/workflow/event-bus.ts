/**
 * Workflow Event Bus
 * 
 * Event-driven coordination system for workflow execution
 * Based on the goal-based-collaboration event patterns
 */

import { v4 as uuidv4 } from 'uuid';

export enum WorkflowEventType {
  WORKFLOW_STARTED = 'workflow.started',
  WORKFLOW_COMPLETED = 'workflow.completed',
  WORKFLOW_FAILED = 'workflow.failed',
  WORKFLOW_PAUSED = 'workflow.paused',
  WORKFLOW_RESUMED = 'workflow.resumed',
  
  STEP_STARTED = 'step.started',
  STEP_COMPLETED = 'step.completed',
  STEP_FAILED = 'step.failed',
  STEP_SKIPPED = 'step.skipped',
  
  AGENT_CONSULTATION_REQUESTED = 'agent.consultation.requested',
  AGENT_CONSULTATION_COMPLETED = 'agent.consultation.completed',
  AGENT_CONSULTATION_FAILED = 'agent.consultation.failed',
  
  HUMAN_REVIEW_REQUIRED = 'human.review.required',
  HUMAN_REVIEW_COMPLETED = 'human.review.completed',
  
  APPROVAL_REQUIRED = 'approval.required',
  APPROVAL_COMPLETED = 'approval.completed',
  
  EXECUTION_CONTINUE = 'execution.continue'
}

export interface WorkflowEvent {
  id: string;
  type: WorkflowEventType;
  payload: any;
  timestamp: string;
  source: string;
  correlationId?: string;
  causationId?: string;
  executionId?: string;
  stepId?: string;
}

export interface EventOptions {
  correlationId?: string;
  causationId?: string;
  executionId?: string;
  stepId?: string;
}

export type EventHandler = (event: WorkflowEvent) => Promise<void> | void;

export interface IWorkflowEventBus {
  on(eventType: WorkflowEventType, handler: EventHandler): void;
  emit(eventType: WorkflowEventType, payload: any, source: string, options?: EventOptions): Promise<void>;
  off(eventType: WorkflowEventType, handler: EventHandler): void;
  once(eventType: WorkflowEventType, handler: EventHandler): void;
  removeAllListeners(eventType?: WorkflowEventType): void;
  getEventHistory(executionId?: string): WorkflowEvent[];
  clearEventHistory(): void;
}

export class WorkflowEventBus implements IWorkflowEventBus {
  private handlers: Map<WorkflowEventType, Set<EventHandler>> = new Map();
  private onceHandlers: Map<WorkflowEventType, Set<EventHandler>> = new Map();
  private eventHistory: WorkflowEvent[] = [];
  private maxHistorySize: number = 1000;

  constructor(maxHistorySize: number = 1000) {
    this.maxHistorySize = maxHistorySize;
  }

  on(eventType: WorkflowEventType, handler: EventHandler): void {
    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, new Set());
    }
    this.handlers.get(eventType)!.add(handler);
  }

  once(eventType: WorkflowEventType, handler: EventHandler): void {
    if (!this.onceHandlers.has(eventType)) {
      this.onceHandlers.set(eventType, new Set());
    }
    this.onceHandlers.get(eventType)!.add(handler);
  }

  off(eventType: WorkflowEventType, handler: EventHandler): void {
    const handlers = this.handlers.get(eventType);
    if (handlers) {
      handlers.delete(handler);
    }

    const onceHandlers = this.onceHandlers.get(eventType);
    if (onceHandlers) {
      onceHandlers.delete(handler);
    }
  }

  removeAllListeners(eventType?: WorkflowEventType): void {
    if (eventType) {
      this.handlers.delete(eventType);
      this.onceHandlers.delete(eventType);
    } else {
      this.handlers.clear();
      this.onceHandlers.clear();
    }
  }

  async emit(eventType: WorkflowEventType, payload: any, source: string, options?: EventOptions): Promise<void> {
    const event: WorkflowEvent = {
      id: uuidv4(),
      type: eventType,
      payload,
      timestamp: new Date().toISOString(),
      source,
      correlationId: options?.correlationId,
      causationId: options?.causationId,
      executionId: options?.executionId,
      stepId: options?.stepId
    };

    // Store in history
    this.storeEvent(event);

    // Log event
    console.log(`📡 Event emitted: ${eventType}`, {
      id: event.id,
      source: event.source,
      executionId: event.executionId,
      stepId: event.stepId
    });

    // Process regular handlers
    const handlers = this.handlers.get(eventType);
    if (handlers) {
      const handlerPromises = Array.from(handlers).map(async (handler) => {
        try {
          await handler(event);
        } catch (error) {
          console.error(`❌ Event handler failed for ${eventType}:`, error);
        }
      });
      await Promise.allSettled(handlerPromises);
    }

    // Process once handlers
    const onceHandlers = this.onceHandlers.get(eventType);
    if (onceHandlers) {
      const onceHandlerPromises = Array.from(onceHandlers).map(async (handler) => {
        try {
          await handler(event);
        } catch (error) {
          console.error(`❌ Once event handler failed for ${eventType}:`, error);
        }
      });
      await Promise.allSettled(onceHandlerPromises);
      
      // Clear once handlers after execution
      this.onceHandlers.delete(eventType);
    }
  }

  private storeEvent(event: WorkflowEvent): void {
    this.eventHistory.push(event);
    
    // Maintain history size limit
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory = this.eventHistory.slice(-this.maxHistorySize);
    }
  }

  getEventHistory(executionId?: string): WorkflowEvent[] {
    if (executionId) {
      return this.eventHistory.filter(event => event.executionId === executionId);
    }
    return [...this.eventHistory];
  }

  clearEventHistory(): void {
    this.eventHistory = [];
  }

  /**
   * Wait for a specific event to occur
   */
  waitForEvent(
    eventType: WorkflowEventType, 
    predicate?: (event: WorkflowEvent) => boolean,
    timeoutMs: number = 30000
  ): Promise<WorkflowEvent> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.off(eventType, handler);
        reject(new Error(`Timeout waiting for event ${eventType} after ${timeoutMs}ms`));
      }, timeoutMs);

      const handler = (event: WorkflowEvent) => {
        if (!predicate || predicate(event)) {
          clearTimeout(timeout);
          this.off(eventType, handler);
          resolve(event);
        }
      };

      this.once(eventType, handler);
    });
  }

  /**
   * Get event statistics
   */
  getStatistics(): {
    totalEvents: number;
    eventsByType: Record<string, number>;
    recentEvents: WorkflowEvent[];
  } {
    const eventsByType: Record<string, number> = {};
    
    this.eventHistory.forEach(event => {
      eventsByType[event.type] = (eventsByType[event.type] || 0) + 1;
    });

    return {
      totalEvents: this.eventHistory.length,
      eventsByType,
      recentEvents: this.eventHistory.slice(-10)
    };
  }
}
