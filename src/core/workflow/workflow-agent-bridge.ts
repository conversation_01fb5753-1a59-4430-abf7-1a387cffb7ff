/**
 * Workflow Agent Bridge - Fresh Implementation
 * 
 * Bridges the workflow system with the fresh dynamic agent consultation system
 */

import { v4 as uuidv4 } from 'uuid';
import {
  IAgent,
  IAgentBridge,
  AgentId,
  AgentConsultationRequest,
  AgentConsultationResponse,
  AgentUnavailableError,
  AgentError,
  ConsultationTimeoutError
} from '../agents/types';

export class WorkflowAgentBridge implements IAgentBridge {
  private agents: Map<AgentId, IAgent> = new Map();
  private consultationHistory: Map<string, AgentConsultationResponse> = new Map();

  constructor(initialAgents?: Record<AgentId, IAgent>) {
    if (initialAgents) {
      Object.entries(initialAgents).forEach(([agentId, agent]) => {
        this.registerAgent(agent);
      });
    }
  }

  registerAgent(agent: IAgent): void {
    const agentId = agent.getAgentId();
    this.agents.set(agentId, agent);
    console.log(`🤖 Registered agent: ${agentId}`);
  }

  unregisterAgent(agentId: AgentId): void {
    if (this.agents.has(agentId)) {
      this.agents.delete(agentId);
      console.log(`🗑️ Unregistered agent: ${agentId}`);
    }
  }

  getAgent(agentId: AgentId): IAgent | null {
    return this.agents.get(agentId) || null;
  }

  getRegisteredAgents(): AgentId[] {
    return Array.from(this.agents.keys());
  }

  async isAgentAvailable(agentId: AgentId): Promise<boolean> {
    const agent = this.getAgent(agentId);
    if (!agent) {
      return false;
    }
    
    try {
      return await agent.isAvailable();
    } catch (error) {
      console.error(`Error checking availability for agent ${agentId}:`, error);
      return false;
    }
  }

  async consultAgent(
    agentId: AgentId,
    request: AgentConsultationRequest
  ): Promise<AgentConsultationResponse> {
    const agent = this.getAgent(agentId);
    
    if (!agent) {
      throw new AgentError(
        `Agent ${agentId} is not registered`,
        agentId,
        'AGENT_NOT_FOUND'
      );
    }

    // Check agent availability
    if (!await this.isAgentAvailable(agentId)) {
      throw new AgentUnavailableError(agentId);
    }

    try {
      console.log(`🔄 Starting consultation with agent ${agentId} for request ${request.id}`);
      
      // Set up timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new ConsultationTimeoutError(agentId, request.timeoutMs));
        }, request.timeoutMs);
      });

      // Execute consultation with timeout
      const consultationPromise = agent.processConsultation(request);
      
      const response = await Promise.race([consultationPromise, timeoutPromise]);
      
      // Store in history
      this.consultationHistory.set(request.id, response);
      
      console.log(`✅ Consultation completed with agent ${agentId} for request ${request.id}`);
      return response;

    } catch (error) {
      console.error(`❌ Consultation failed with agent ${agentId} for request ${request.id}:`, error);
      
      if (error instanceof AgentError || error instanceof ConsultationTimeoutError) {
        throw error;
      }
      
      throw new AgentError(
        `Consultation failed: ${error instanceof Error ? error.message : String(error)}`,
        agentId,
        'CONSULTATION_FAILED',
        { originalError: error, requestId: request.id }
      );
    }
  }

  /**
   * Consult multiple agents in parallel
   */
  async consultMultipleAgents(
    requests: AgentConsultationRequest[]
  ): Promise<AgentConsultationResponse[]> {
    console.log(`🔄 Starting parallel consultation with ${requests.length} agents`);
    
    const consultationPromises = requests.map(async (request) => {
      try {
        return await this.consultAgent(request.agentId, request);
      } catch (error) {
        console.error(`Failed consultation with agent ${request.agentId}:`, error);
        // Return null for failed consultations, filter them out later
        return null;
      }
    });

    const results = await Promise.allSettled(consultationPromises);
    
    const successfulConsultations = results
      .filter((result): result is PromiseFulfilledResult<AgentConsultationResponse> => 
        result.status === 'fulfilled' && result.value !== null
      )
      .map(result => result.value);

    console.log(`✅ Completed ${successfulConsultations.length}/${requests.length} consultations`);
    
    return successfulConsultations;
  }

  /**
   * Get consultation history for a specific request
   */
  getConsultationHistory(requestId: string): AgentConsultationResponse | null {
    return this.consultationHistory.get(requestId) || null;
  }

  /**
   * Get all consultation history
   */
  getAllConsultationHistory(): AgentConsultationResponse[] {
    return Array.from(this.consultationHistory.values());
  }

  /**
   * Clear consultation history
   */
  clearConsultationHistory(): void {
    this.consultationHistory.clear();
  }

  /**
   * Get agent status for all registered agents
   */
  async getAllAgentStatus(): Promise<Array<{
    agentId: AgentId;
    isRegistered: boolean;
    isAvailable: boolean;
    capabilities: string[];
    status: any;
  }>> {
    const statusPromises = Array.from(this.agents.entries()).map(async ([agentId, agent]) => {
      try {
        const isAvailable = await agent.isAvailable();
        return {
          agentId,
          isRegistered: true,
          isAvailable,
          capabilities: agent.getCapabilities(),
          status: agent.getStatus ? agent.getStatus() : null
        };
      } catch (error) {
        return {
          agentId,
          isRegistered: true,
          isAvailable: false,
          capabilities: agent.getCapabilities(),
          status: { error: error instanceof Error ? error.message : String(error) }
        };
      }
    });

    return await Promise.all(statusPromises);
  }

  /**
   * Find agents by capability
   */
  findAgentsByCapability(capability: string): AgentId[] {
    const matchingAgents: AgentId[] = [];
    
    for (const [agentId, agent] of this.agents.entries()) {
      if (agent.getCapabilities().includes(capability as any)) {
        matchingAgents.push(agentId);
      }
    }
    
    return matchingAgents;
  }

  /**
   * Create consultation request
   */
  createConsultationRequest(
    agentId: AgentId,
    workflowExecutionId: string,
    stepId: string,
    question: string,
    context: Record<string, any>,
    priority: 'low' | 'medium' | 'high' = 'medium',
    timeoutMs: number = 30000
  ): AgentConsultationRequest {
    return {
      id: uuidv4(),
      workflowExecutionId,
      stepId,
      agentId,
      question,
      context,
      priority,
      timeoutMs,
      createdAt: new Date().toISOString()
    };
  }

  /**
   * Validate agent configuration
   */
  validateAgentConfiguration(agentId: AgentId): {
    isValid: boolean;
    issues: string[];
  } {
    const agent = this.getAgent(agentId);
    const issues: string[] = [];

    if (!agent) {
      return {
        isValid: false,
        issues: [`Agent ${agentId} is not registered`]
      };
    }

    try {
      const config = agent.getConfiguration();
      
      if (!config.agentId) {
        issues.push('Agent ID is missing');
      }
      
      if (!config.capabilities || config.capabilities.length === 0) {
        issues.push('Agent capabilities are not defined');
      }
      
      if (config.maxConcurrentConsultations <= 0) {
        issues.push('Max concurrent consultations must be greater than 0');
      }
      
      if (config.defaultTimeoutMs <= 0) {
        issues.push('Default timeout must be greater than 0');
      }

    } catch (error) {
      issues.push(`Error validating configuration: ${error instanceof Error ? error.message : String(error)}`);
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * Get bridge statistics
   */
  getStatistics(): {
    registeredAgents: number;
    totalConsultations: number;
    averageResponseTime: number;
    agentUtilization: Record<AgentId, number>;
  } {
    const consultations = this.getAllConsultationHistory();
    
    const agentUtilization: Record<AgentId, number> = {};
    let totalResponseTime = 0;

    consultations.forEach(consultation => {
      agentUtilization[consultation.agentId] = (agentUtilization[consultation.agentId] || 0) + 1;
      totalResponseTime += consultation.processingTime;
    });

    return {
      registeredAgents: this.agents.size,
      totalConsultations: consultations.length,
      averageResponseTime: consultations.length > 0 ? totalResponseTime / consultations.length : 0,
      agentUtilization
    };
  }

  /**
   * Health check for all agents
   */
  async performHealthCheck(): Promise<{
    overallHealth: 'healthy' | 'degraded' | 'unhealthy';
    agentHealth: Record<AgentId, 'healthy' | 'unhealthy'>;
    issues: string[];
  }> {
    const agentHealth: Record<AgentId, 'healthy' | 'unhealthy'> = {};
    const issues: string[] = [];
    let healthyCount = 0;

    for (const [agentId, agent] of this.agents.entries()) {
      try {
        const isAvailable = await agent.isAvailable();
        const validation = this.validateAgentConfiguration(agentId);
        
        if (isAvailable && validation.isValid) {
          agentHealth[agentId] = 'healthy';
          healthyCount++;
        } else {
          agentHealth[agentId] = 'unhealthy';
          issues.push(`Agent ${agentId}: ${!isAvailable ? 'unavailable' : validation.issues.join(', ')}`);
        }
      } catch (error) {
        agentHealth[agentId] = 'unhealthy';
        issues.push(`Agent ${agentId}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    const totalAgents = this.agents.size;
    let overallHealth: 'healthy' | 'degraded' | 'unhealthy';

    if (healthyCount === totalAgents) {
      overallHealth = 'healthy';
    } else if (healthyCount > totalAgents / 2) {
      overallHealth = 'degraded';
    } else {
      overallHealth = 'unhealthy';
    }

    return {
      overallHealth,
      agentHealth,
      issues
    };
  }
}
