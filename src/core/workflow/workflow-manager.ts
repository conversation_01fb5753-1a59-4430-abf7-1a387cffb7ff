/**
 * Enhanced Workflow Manager
 * Handles workflow execution with artifact approval gates and state management
 */

import { v4 as uuidv4 } from 'uuid';
import {
  Workflow,
  WorkflowExecution,
  WorkflowStep,
  StepResult,
  StepStatus,
  WorkflowArtifact,
  ArtifactStatus,
  ArtifactType,
  ApprovalGate,
  ApprovalDecision,
  ApprovalStatus,
  ExecutionStatus,
  IWorkflowEngine
} from './types';
import { ISimplifiedStateStore } from '../state/types';

export class WorkflowManager {
  private approvalGates = new Map<string, ApprovalGate>();
  private pendingApprovals = new Map<string, ApprovalStatus>();

  constructor(
    private stateStore: ISimplifiedStateStore,
    private workflowEngine: IWorkflowEngine
  ) {}

  /**
   * Execute workflow with approval gates
   */
  async executeWorkflowWithApprovals(
    workflowId: string,
    inputs: Record<string, any>,
    approvalGates: ApprovalGate[] = []
  ): Promise<string> {
    // Store approval gates for this workflow
    approvalGates.forEach(gate => {
      this.approvalGates.set(gate.id, gate);
    });

    // Start workflow execution
    const executionId = await this.workflowEngine.executeWorkflow(workflowId, inputs);
    
    return executionId;
  }

  /**
   * Create artifact and check for approval requirements
   */
  async createArtifactWithApproval(
    executionId: string,
    stepId: string,
    artifactData: {
      type: ArtifactType;
      title: string;
      content: any;
      createdBy: string;
    }
  ): Promise<string> {
    const artifactId = uuidv4();
    const now = new Date().toISOString();

    // Create artifact
    const artifact: WorkflowArtifact = {
      id: artifactId,
      stepId,
      executionId,
      type: artifactData.type,
      title: artifactData.title,
      content: artifactData.content,
      status: ArtifactStatus.DRAFT,
      version: 1,
      createdAt: now,
      updatedAt: now,
      createdBy: artifactData.createdBy
    };

    // Check if this step has an approval gate
    const approvalGate = this.findApprovalGateForStep(stepId, artifactData.type);
    
    if (approvalGate) {
      // Set artifact to pending approval
      artifact.status = ArtifactStatus.PENDING_APPROVAL;
      
      // Create approval status
      const approvalStatus: ApprovalStatus = {
        artifactId,
        status: ArtifactStatus.PENDING_APPROVAL,
        approvals: [],
        requiredApprovals: approvalGate.requiredApprovals,
        pendingApprovers: [...approvalGate.approvers],
        canProceed: false,
        escalated: false,
        escalationLevel: 0
      };

      this.pendingApprovals.set(artifactId, approvalStatus);

      // Pause workflow execution at this step
      await this.pauseWorkflowAtStep(executionId, stepId, artifactId);
    } else {
      // No approval required, mark as approved
      artifact.status = ArtifactStatus.APPROVED;
    }

    // Store artifact
    await this.storeArtifact(artifact);

    // Update step result with artifact info
    await this.updateStepWithArtifact(executionId, stepId, artifactId, approvalGate !== null);

    return artifactId;
  }

  /**
   * Submit approval decision for an artifact
   */
  async submitApproval(artifactId: string, decision: ApprovalDecision): Promise<void> {
    const approvalStatus = this.pendingApprovals.get(artifactId);
    if (!approvalStatus) {
      throw new Error(`No pending approval found for artifact ${artifactId}`);
    }

    const artifact = await this.getArtifact(artifactId);
    if (!artifact) {
      throw new Error(`Artifact ${artifactId} not found`);
    }

    // Add approval decision
    approvalStatus.approvals.push(decision);

    if (decision.approved) {
      // Remove approver from pending list
      approvalStatus.pendingApprovers = approvalStatus.pendingApprovers.filter(
        approver => approver !== decision.approver
      );

      // Check if we have enough approvals
      const approvedCount = approvalStatus.approvals.filter(a => a.approved).length;
      
      if (approvedCount >= approvalStatus.requiredApprovals) {
        // Artifact is fully approved
        approvalStatus.status = ArtifactStatus.APPROVED;
        approvalStatus.canProceed = true;

        // Update artifact
        artifact.status = ArtifactStatus.APPROVED;
        artifact.approvedBy = decision.approver;
        artifact.approvedAt = decision.timestamp;

        await this.updateArtifact(artifact);

        // Resume workflow execution
        await this.resumeWorkflowAfterApproval(artifact.executionId, artifact.stepId);

        // Remove from pending approvals
        this.pendingApprovals.delete(artifactId);
      }
    } else {
      // Artifact is rejected
      approvalStatus.status = ArtifactStatus.REJECTED;
      approvalStatus.canProceed = false;

      // Update artifact
      artifact.status = ArtifactStatus.REJECTED;
      artifact.rejectedBy = decision.approver;
      artifact.rejectedAt = decision.timestamp;
      artifact.rejectionReason = decision.reason;

      await this.updateArtifact(artifact);

      // Handle rejection - could retry, escalate, or fail workflow
      await this.handleArtifactRejection(artifact, decision);

      // Remove from pending approvals
      this.pendingApprovals.delete(artifactId);
    }

    // Update approval status
    this.pendingApprovals.set(artifactId, approvalStatus);
  }

  /**
   * Get approval status for an artifact
   */
  async getApprovalStatus(artifactId: string): Promise<ApprovalStatus | null> {
    return this.pendingApprovals.get(artifactId) || null;
  }

  /**
   * Get all pending approvals for a user
   */
  async getPendingApprovalsForUser(userId: string): Promise<ApprovalStatus[]> {
    const pendingApprovals: ApprovalStatus[] = [];
    
    for (const [artifactId, status] of this.pendingApprovals.entries()) {
      if (status.pendingApprovers.includes(userId)) {
        pendingApprovals.push(status);
      }
    }

    return pendingApprovals;
  }

  /**
   * Find approval gate for a step and artifact type
   */
  private findApprovalGateForStep(stepId: string, artifactType: ArtifactType): ApprovalGate | null {
    for (const gate of this.approvalGates.values()) {
      if (gate.stepId === stepId && gate.artifactType === artifactType) {
        return gate;
      }
    }
    return null;
  }

  /**
   * Pause workflow execution at a specific step
   */
  private async pauseWorkflowAtStep(executionId: string, stepId: string, artifactId: string): Promise<void> {
    const execution = await this.workflowEngine.getExecution(executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    // Update step status to waiting for approval
    const stepResult = execution.stepResults[stepId];
    if (stepResult) {
      stepResult.status = StepStatus.WAITING_APPROVAL;
      stepResult.artifactId = artifactId;
      stepResult.approvalRequired = true;
    }

    // Update execution
    await this.stateStore.setExecution(execution);
  }

  /**
   * Resume workflow execution after approval
   */
  private async resumeWorkflowAfterApproval(executionId: string, stepId: string): Promise<void> {
    const execution = await this.workflowEngine.getExecution(executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    // Update step status to approved
    const stepResult = execution.stepResults[stepId];
    if (stepResult) {
      stepResult.status = StepStatus.APPROVED;
      stepResult.approvedAt = new Date().toISOString();
      stepResult.completedAt = new Date().toISOString();
    }

    // Update execution
    await this.stateStore.setExecution(execution);

    // Resume workflow execution
    await this.workflowEngine.resumeExecution(executionId);
  }

  /**
   * Handle artifact rejection
   */
  private async handleArtifactRejection(artifact: WorkflowArtifact, decision: ApprovalDecision): Promise<void> {
    // For now, mark the step as failed
    // In the future, this could trigger revision workflows or escalation
    const execution = await this.workflowEngine.getExecution(artifact.executionId);
    if (execution) {
      const stepResult = execution.stepResults[artifact.stepId];
      if (stepResult) {
        stepResult.status = StepStatus.REJECTED;
        stepResult.rejectionReason = decision.reason;
        stepResult.error = `Artifact rejected: ${decision.reason}`;
      }
      await this.stateStore.setExecution(execution);
    }
  }

  /**
   * Store artifact in state
   */
  private async storeArtifact(artifact: WorkflowArtifact): Promise<void> {
    // Convert artifact to content item
    const contentItem = {
      id: artifact.id,
      type: this.mapArtifactTypeToContentType(artifact.type),
      title: artifact.title,
      content: artifact.content,
      status: artifact.status === ArtifactStatus.APPROVED ? 'approved' as any : 'pending' as any,
      executionId: artifact.executionId,
      stepId: artifact.stepId,
      createdAt: artifact.createdAt,
      updatedAt: artifact.updatedAt,
      metadata: {
        stepId: artifact.stepId,
        executionId: artifact.executionId,
        version: artifact.version,
        createdBy: artifact.createdBy,
        approvedBy: artifact.approvedBy,
        approvedAt: artifact.approvedAt,
        artifactType: artifact.type
      }
    };

    await this.stateStore.setContent(contentItem);
  }

  private mapArtifactTypeToContentType(artifactType: ArtifactType): any {
    // Simple mapping - you can enhance this based on your needs
    switch (artifactType) {
      case ArtifactType.KEYWORD_RESEARCH:
        return 'keyword_research';
      case ArtifactType.CONTENT_STRATEGY:
        return 'generic';
      case ArtifactType.CONTENT_DRAFT:
        return 'blog_post';
      case ArtifactType.SEO_OPTIMIZATION:
        return 'seo_analysis';
      case ArtifactType.FINAL_CONTENT:
        return 'blog_post';
      case ArtifactType.REVIEW_FEEDBACK:
        return 'generic';
      default:
        return 'generic';
    }
  }

  /**
   * Get artifact from state
   */
  private async getArtifact(artifactId: string): Promise<WorkflowArtifact | null> {
    const content = await this.stateStore.getContent(artifactId);
    if (!content || !content.metadata) {
      return null;
    }

    return {
      id: content.id,
      stepId: content.metadata.stepId || content.stepId,
      executionId: content.metadata.executionId || content.executionId,
      type: (content.metadata.artifactType as ArtifactType) || ArtifactType.CONTENT_DRAFT,
      title: content.title,
      content: content.content,
      status: content.status === 'approved' ? ArtifactStatus.APPROVED : ArtifactStatus.PENDING_APPROVAL,
      version: content.metadata.version || 1,
      createdAt: content.createdAt,
      updatedAt: content.updatedAt,
      createdBy: content.metadata.createdBy || 'system',
      approvedBy: content.metadata.approvedBy,
      approvedAt: content.metadata.approvedAt
    };
  }

  /**
   * Update artifact in state
   */
  private async updateArtifact(artifact: WorkflowArtifact): Promise<void> {
    await this.storeArtifact(artifact);
  }

  /**
   * Update step result with artifact information
   */
  private async updateStepWithArtifact(
    executionId: string,
    stepId: string,
    artifactId: string,
    approvalRequired: boolean
  ): Promise<void> {
    const execution = await this.workflowEngine.getExecution(executionId);
    if (!execution) return;

    const stepResult = execution.stepResults[stepId];
    if (stepResult) {
      stepResult.artifactId = artifactId;
      stepResult.approvalRequired = approvalRequired;
      
      if (approvalRequired) {
        stepResult.status = StepStatus.WAITING_APPROVAL;
      } else {
        stepResult.status = StepStatus.COMPLETED;
        stepResult.completedAt = new Date().toISOString();
      }
    }

    await this.stateStore.setExecution(execution);
  }
}
