/**
 * TDD Integration Tests for Workflow-Agent Integration
 *
 * Testing the integration between workflow engine and agent consultation system
 */

import 'openai/shims/node';
import { EnhancedAIGenerationStep } from '../enhanced-ai-generation-step';
import { WorkflowStep, StepConfig } from '../types';
import { getAgentBridge, getConsultationService } from '../singleton';

describe('Workflow-Agent Integration', () => {
  let enhancedAIStep: EnhancedAIGenerationStep;

  beforeEach(() => {
    enhancedAIStep = new EnhancedAIGenerationStep();
  });

  describe('Enhanced AI Generation Step', () => {
    test('should use singleton services', () => {
      const agentBridge = getAgentBridge();
      const consultationService = getConsultationService();

      expect(agentBridge).toBeDefined();
      expect(consultationService).toBeDefined();
      
      // Verify singleton behavior - should return same instances
      const agentBridge2 = getAgentBridge();
      const consultationService2 = getConsultationService();
      
      expect(agentBridge).toBe(agentBridge2);
      expect(consultationService).toBe(consultationService2);
    });

    test('should execute step with consultation config', async () => {
      const step: WorkflowStep = {
        id: 'test-step',
        name: 'Test Step',
        type: 'ai_generation',
        inputs: ['topic', 'target_audience'],
        outputs: ['content'],
        dependencies: [],
        config: {
          aiConfig: {
            provider: 'openai',
            model: 'gpt-4',
            temperature: 0.7,
            maxTokens: 1000,
            contentType: 'blog-post'
          }
        },
        consultationConfig: {
          enabled: true,
          triggers: [
            {
              type: 'always',
              condition: {},
              agents: ['seo-keyword', 'market-research'],
              priority: 'high'
            }
          ],
          maxConsultations: 2,
          timeoutMs: 30000,
          fallbackBehavior: 'continue'
        }
      };

      const inputs = {
        topic: 'CRM software',
        target_audience: 'business owners'
      };

      const result = await enhancedAIStep.execute(
        'test-execution-id',
        step,
        inputs
      );

      expect(result).toBeDefined();
      expect(result.outputs).toBeDefined();
      expect(result.status).toBe('completed');
    });

    test('should prepare consultation context correctly', async () => {
      const step: WorkflowStep = {
        id: 'test-step',
        name: 'Test Step',
        type: 'ai_generation',
        inputs: ['topic', 'target_audience'],
        outputs: ['content'],
        dependencies: [],
        config: {
          aiConfig: {
            provider: 'openai',
            model: 'gpt-4',
            temperature: 0.7,
            maxTokens: 1000,
            contentType: 'blog-post'
          }
        },
        consultationConfig: {
          enabled: true,
          triggers: [
            {
              type: 'always',
              condition: {},
              agents: ['seo-keyword'],
              priority: 'high'
            }
          ],
          maxConsultations: 1,
          timeoutMs: 30000,
          fallbackBehavior: 'continue'
        }
      };

      const inputs = {
        topic: 'CRM software',
        target_audience: 'business owners',
        primary_keyword: 'crm'
      };

      // Access the private method through any to test context preparation
      const context = (enhancedAIStep as any).prepareConsultationContext(inputs, step);

      expect(context).toBeDefined();
      expect(context.topic).toBe('CRM software');
      expect(context.targetAudience).toBe('business owners');
      expect(context.primaryKeyword).toBe('crm');
      expect(context.contentType).toBe('blog-post');
      expect(context.qualityScore).toBeDefined();
      expect(context.complexity).toBeDefined();
    });

    test('should calculate default quality score and complexity', async () => {
      const step: WorkflowStep = {
        id: 'test-step',
        name: 'Test Step',
        type: 'ai_generation',
        inputs: ['topic'],
        outputs: ['content'],
        dependencies: [],
        config: {
          aiConfig: {
            contentType: 'blog-post'
          }
        }
      };

      const inputs = {
        topic: 'Advanced API architecture patterns',
        target_audience: 'technical experts'
      };

      const context = (enhancedAIStep as any).prepareConsultationContext(inputs, step);

      expect(context.qualityScore).toBeGreaterThan(0);
      expect(context.qualityScore).toBeLessThanOrEqual(1);
      expect(context.complexity).toBeGreaterThan(0);
      expect(context.complexity).toBeLessThanOrEqual(1);
      
      // Technical topic should have higher complexity
      expect(context.complexity).toBeGreaterThan(0.5);
    });
  });

  describe('Template Configuration', () => {
    test('should have consultation config in templates', () => {
      const { SEO_BLOG_POST_TEMPLATE } = require('../templates');
      
      expect(SEO_BLOG_POST_TEMPLATE).toBeDefined();
      
      // Find keyword-research step
      const keywordStep = SEO_BLOG_POST_TEMPLATE.steps.find((s: any) => s.id === 'keyword-research');
      expect(keywordStep).toBeDefined();
      expect(keywordStep.consultationConfig).toBeDefined();
      expect(keywordStep.consultationConfig.enabled).toBe(true);
      
      // Should have always trigger
      const alwaysTrigger = keywordStep.consultationConfig.triggers.find((t: any) => t.type === 'always');
      expect(alwaysTrigger).toBeDefined();
      expect(alwaysTrigger.agents).toContain('seo-keyword');
      expect(alwaysTrigger.agents).toContain('market-research');
      
      // Find content-creation step
      const contentStep = SEO_BLOG_POST_TEMPLATE.steps.find((s: any) => s.id === 'content-creation');
      expect(contentStep).toBeDefined();
      expect(contentStep.consultationConfig).toBeDefined();
      expect(contentStep.consultationConfig.enabled).toBe(true);
      
      // Should have always trigger
      const contentAlwaysTrigger = contentStep.consultationConfig.triggers.find((t: any) => t.type === 'always');
      expect(contentAlwaysTrigger).toBeDefined();
      expect(contentAlwaysTrigger.agents).toContain('seo-keyword');
      expect(contentAlwaysTrigger.agents).toContain('content-strategy');
    });
  });

  describe('Singleton Service Integration', () => {
    test('should maintain same agent instances across calls', () => {
      const bridge1 = getAgentBridge();
      const bridge2 = getAgentBridge();
      const consultation1 = getConsultationService();
      const consultation2 = getConsultationService();

      expect(bridge1).toBe(bridge2);
      expect(consultation1).toBe(consultation2);
    });

    test('should have agents registered in bridge', async () => {
      const bridge = getAgentBridge();
      
      const seoAvailable = await bridge.isAgentAvailable('seo-keyword');
      const marketAvailable = await bridge.isAgentAvailable('market-research');
      const strategyAvailable = await bridge.isAgentAvailable('content-strategy');

      expect(seoAvailable).toBe(true);
      expect(marketAvailable).toBe(true);
      expect(strategyAvailable).toBe(true);
    });
  });
});
