/**
 * TDD Tests for Agent Consultation System
 * 
 * Testing the critical agent consultation functionality that's currently not working
 */

import { DynamicAgentConsultationService } from '../dynamic-agent-consultation-service';
import { WorkflowAgentBridge } from '../workflow-agent-bridge';
import { SeoKeywordAgent } from '../../agents/seo-keyword-agent';
import { MarketResearchAgent } from '../../agents/market-research-agent';
import { ContentStrategyAgent } from '../../agents/content-strategy-agent';
import { AgentConsultationConfig, ConsultationContext } from '../../agents/types';

describe('Agent Consultation System', () => {
  let consultationService: DynamicAgentConsultationService;
  let agentBridge: WorkflowAgentBridge;
  let seoAgent: SeoKeywordAgent;
  let marketAgent: MarketResearchAgent;
  let strategyAgent: ContentStrategyAgent;

  beforeEach(() => {
    // Initialize agents
    seoAgent = new SeoKeywordAgent();
    marketAgent = new MarketResearchAgent();
    strategyAgent = new ContentStrategyAgent();

    // Create agent bridge
    agentBridge = new WorkflowAgentBridge({
      'seo-keyword': seoAgent,
      'market-research': marketAgent,
      'content-strategy': strategyAgent
    });

    // Create consultation service
    consultationService = new DynamicAgentConsultationService(agentBridge);
  });

  describe('Trigger Evaluation', () => {
    test('should trigger consultation with "always" trigger', async () => {
      const config: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'always',
            condition: {},
            agents: ['seo-keyword'],
            priority: 'high'
          }
        ],
        maxConsultations: 2,
        timeoutMs: 30000,
        fallbackBehavior: 'continue'
      };

      const context: ConsultationContext = {
        topic: 'test topic',
        contentType: 'blog-post',
        targetAudience: 'general audience'
      };

      const shouldTrigger = await consultationService.shouldTriggerConsultation(config, context);
      expect(shouldTrigger).toBe(true);
    });

    test('should not trigger consultation when disabled', async () => {
      const config: AgentConsultationConfig = {
        enabled: false,
        triggers: [
          {
            type: 'always',
            condition: {},
            agents: ['seo-keyword'],
            priority: 'high'
          }
        ],
        maxConsultations: 2,
        timeoutMs: 30000,
        fallbackBehavior: 'continue'
      };

      const context: ConsultationContext = {
        topic: 'test topic',
        contentType: 'blog-post',
        targetAudience: 'general audience'
      };

      const shouldTrigger = await consultationService.shouldTriggerConsultation(config, context);
      expect(shouldTrigger).toBe(false);
    });

    test('should trigger consultation with quality threshold', async () => {
      const config: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'quality_threshold',
            condition: { threshold: 0.7 },
            agents: ['seo-keyword'],
            priority: 'high'
          }
        ],
        maxConsultations: 2,
        timeoutMs: 30000,
        fallbackBehavior: 'continue'
      };

      const context: ConsultationContext = {
        topic: 'test topic',
        contentType: 'blog-post',
        targetAudience: 'general audience',
        qualityScore: 0.6 // Below threshold
      };

      const shouldTrigger = await consultationService.shouldTriggerConsultation(config, context);
      expect(shouldTrigger).toBe(true);
    });

    test('should not trigger consultation with quality above threshold', async () => {
      const config: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'quality_threshold',
            condition: { threshold: 0.7 },
            agents: ['seo-keyword'],
            priority: 'high'
          }
        ],
        maxConsultations: 2,
        timeoutMs: 30000,
        fallbackBehavior: 'continue'
      };

      const context: ConsultationContext = {
        topic: 'test topic',
        contentType: 'blog-post',
        targetAudience: 'general audience',
        qualityScore: 0.8 // Above threshold
      };

      const shouldTrigger = await consultationService.shouldTriggerConsultation(config, context);
      expect(shouldTrigger).toBe(false);
    });
  });

  describe('Multi-Agent Consultation', () => {
    test('should execute consultation with always trigger', async () => {
      const config: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'always',
            condition: {},
            agents: ['seo-keyword', 'market-research'],
            priority: 'high'
          }
        ],
        maxConsultations: 2,
        timeoutMs: 30000,
        fallbackBehavior: 'continue'
      };

      const context: ConsultationContext = {
        topic: 'CRM software',
        contentType: 'blog-post',
        targetAudience: 'business owners'
      };

      const results = await consultationService.consultMultipleAgents(
        'test-execution-id',
        'test-step-id',
        context,
        config
      );

      expect(results).toBeDefined();
      expect(Array.isArray(results)).toBe(true);
      // Should have results since always trigger should activate
      expect(results.length).toBeGreaterThan(0);
    });

    test('should respect max consultations limit', async () => {
      const config: AgentConsultationConfig = {
        enabled: true,
        triggers: [
          {
            type: 'always',
            condition: {},
            agents: ['seo-keyword', 'market-research'],
            priority: 'high'
          }
        ],
        maxConsultations: 1,
        timeoutMs: 30000,
        fallbackBehavior: 'continue'
      };

      const context: ConsultationContext = {
        topic: 'CRM software',
        contentType: 'blog-post',
        targetAudience: 'business owners'
      };

      // First consultation should work
      const results1 = await consultationService.consultMultipleAgents(
        'test-execution-id',
        'test-step-id',
        context,
        config
      );

      // Second consultation should be skipped due to max limit
      const results2 = await consultationService.consultMultipleAgents(
        'test-execution-id',
        'test-step-id',
        context,
        config
      );

      expect(results1.length).toBeGreaterThan(0);
      expect(results2.length).toBe(0);
    });
  });

  describe('Agent Bridge Integration', () => {
    test('should have agents available', async () => {
      const seoAvailable = await agentBridge.isAgentAvailable('seo-keyword');
      const marketAvailable = await agentBridge.isAgentAvailable('market-research');
      const strategyAvailable = await agentBridge.isAgentAvailable('content-strategy');

      expect(seoAvailable).toBe(true);
      expect(marketAvailable).toBe(true);
      expect(strategyAvailable).toBe(true);
    });

    test('should return false for non-existent agent', async () => {
      const available = await agentBridge.isAgentAvailable('non-existent' as any);
      expect(available).toBe(false);
    });
  });
});
