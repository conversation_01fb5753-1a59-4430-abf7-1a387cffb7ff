/**
 * TDD Debug Tests for Agent Consultation Issue
 * 
 * Focused tests to identify why consultation is not triggering in the workflow
 */

import { DynamicAgentConsultationService } from '../dynamic-agent-consultation-service';
import { WorkflowAgentBridge } from '../workflow-agent-bridge';
import { SeoKeywordAgent } from '../../agents/seo-keyword-agent';
import { MarketResearchAgent } from '../../agents/market-research-agent';
import { ContentStrategyAgent } from '../../agents/content-strategy-agent';
import { AgentConsultationConfig, ConsultationContext } from '../../agents/types';

describe('Consultation Debug Tests', () => {
  let consultationService: DynamicAgentConsultationService;
  let agentBridge: WorkflowAgentBridge;

  beforeEach(() => {
    // Initialize exactly like the singleton does
    const seoAgent = new SeoKeywordAgent();
    const marketAgent = new MarketResearchAgent();
    const strategyAgent = new ContentStrategyAgent();

    agentBridge = new WorkflowAgentBridge({
      'seo-keyword': seoAgent,
      'market-research': marketAgent,
      'content-strategy': strategyAgent
    });

    consultationService = new DynamicAgentConsultationService(agentBridge);
  });

  describe('Template Configuration Debugging', () => {
    test('should load template with consultation config', () => {
      const { SEO_BLOG_POST_TEMPLATE } = require('../templates');

      expect(SEO_BLOG_POST_TEMPLATE).toBeDefined();
      expect(SEO_BLOG_POST_TEMPLATE.workflow).toBeDefined();
      expect(SEO_BLOG_POST_TEMPLATE.workflow.steps).toBeDefined();

      const keywordStep = SEO_BLOG_POST_TEMPLATE.workflow.steps.find((s: any) => s.id === 'keyword-research');
      console.log('Keyword step consultation config:', JSON.stringify(keywordStep.consultationConfig, null, 2));

      expect(keywordStep.consultationConfig).toBeDefined();
      expect(keywordStep.consultationConfig.enabled).toBe(true);
      expect(keywordStep.consultationConfig.triggers).toBeDefined();
      expect(keywordStep.consultationConfig.triggers.length).toBeGreaterThan(0);

      // Check for always trigger
      const alwaysTrigger = keywordStep.consultationConfig.triggers.find((t: any) => t.type === 'always');
      expect(alwaysTrigger).toBeDefined();
      console.log('Always trigger found:', JSON.stringify(alwaysTrigger, null, 2));
    });

    test('should trigger consultation with exact template config', async () => {
      const { SEO_BLOG_POST_TEMPLATE } = require('../templates');
      const keywordStep = SEO_BLOG_POST_TEMPLATE.workflow.steps.find((s: any) => s.id === 'keyword-research');
      
      const config = keywordStep.consultationConfig;
      const context: ConsultationContext = {
        topic: 'CRM software',
        contentType: 'blog-post',
        targetAudience: 'business owners',
        qualityScore: 0.6,
        complexity: 0.7
      };

      console.log('Testing with config:', JSON.stringify(config, null, 2));
      console.log('Testing with context:', JSON.stringify(context, null, 2));

      const shouldTrigger = await consultationService.shouldTriggerConsultation(config, context);
      console.log('Should trigger result:', shouldTrigger);
      
      expect(shouldTrigger).toBe(true);
    });

    test('should execute multi-agent consultation with template config', async () => {
      const { SEO_BLOG_POST_TEMPLATE } = require('../templates');
      const keywordStep = SEO_BLOG_POST_TEMPLATE.workflow.steps.find((s: any) => s.id === 'keyword-research');
      
      const config = keywordStep.consultationConfig;
      const context: ConsultationContext = {
        topic: 'CRM software',
        contentType: 'blog-post',
        targetAudience: 'business owners',
        qualityScore: 0.6,
        complexity: 0.7
      };

      console.log('Executing multi-agent consultation...');
      
      const results = await consultationService.consultMultipleAgents(
        'test-execution-id',
        'keyword-research',
        context,
        config
      );

      console.log('Consultation results:', results.length);
      expect(results).toBeDefined();
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBeGreaterThan(0);
    });
  });

  describe('Context Preparation Debugging', () => {
    test('should prepare context with default values', () => {
      const inputs = {
        topic: 'CRM software',
        target_audience: 'business owners',
        primary_keyword: 'crm'
      };

      const step = {
        id: 'keyword-research',
        config: {
          aiConfig: {
            contentType: 'blog-post'
          }
        }
      };

      // Simulate the context preparation logic from EnhancedAIGenerationStep
      const context: ConsultationContext = {
        topic: inputs.topic || '',
        contentType: step.config.aiConfig?.contentType || 'blog-post',
        targetAudience: inputs.target_audience || 'general audience',
        primaryKeyword: inputs.primary_keyword,
        qualityScore: 0.6, // Default calculated value
        complexity: 0.7    // Default calculated value
      };

      console.log('Prepared context:', JSON.stringify(context, null, 2));

      expect(context.topic).toBe('CRM software');
      expect(context.contentType).toBe('blog-post');
      expect(context.targetAudience).toBe('business owners');
      expect(context.primaryKeyword).toBe('crm');
      expect(context.qualityScore).toBeDefined();
      expect(context.complexity).toBeDefined();
    });
  });

  describe('Agent Bridge Debugging', () => {
    test('should have all agents available', async () => {
      const agents = ['seo-keyword', 'market-research', 'content-strategy'];
      
      for (const agentId of agents) {
        const available = await agentBridge.isAgentAvailable(agentId as any);
        console.log(`Agent ${agentId} available:`, available);
        expect(available).toBe(true);
      }
    });

    test('should create consultation requests', async () => {
      const request = await agentBridge.createConsultationRequest(
        'seo-keyword',
        'test-execution-id',
        'keyword-research',
        'Test question',
        {
          topic: 'CRM software',
          contentType: 'blog-post',
          targetAudience: 'business owners'
        },
        'high',
        30000
      );

      console.log('Created consultation request:', JSON.stringify(request, null, 2));
      expect(request).toBeDefined();
      expect(request.agentId).toBe('seo-keyword');
      expect(request.question).toBe('Test question');
    });
  });
});
