/**
 * TDD End-to-End Workflow Execution Debug Tests
 * 
 * Testing the exact workflow execution scenario to identify consultation issue
 */

import 'openai/shims/node';
import { EnhancedAIGenerationStep } from '../enhanced-ai-generation-step';
import { getEnhancedTemplateRegistry } from '../singleton';

describe('Workflow Execution Debug', () => {
  let enhancedAIStep: EnhancedAIGenerationStep;
  let templateRegistry: any;

  beforeEach(() => {
    enhancedAIStep = new EnhancedAIGenerationStep();
    templateRegistry = getEnhancedTemplateRegistry();
  });

  describe('Template Registry Integration', () => {
    test('should load template from registry with consultation config', () => {
      const template = templateRegistry.getTemplate('blog-post-seo');
      
      expect(template).toBeDefined();
      expect(template.workflow).toBeDefined();
      expect(template.workflow.steps).toBeDefined();
      
      const keywordStep = template.workflow.steps.find((s: any) => s.id === 'keyword-research');
      console.log('Registry keyword step:', JSON.stringify(keywordStep, null, 2));
      
      expect(keywordStep).toBeDefined();
      expect(keywordStep.consultationConfig).toBeDefined();
      expect(keywordStep.consultationConfig.enabled).toBe(true);
      
      const alwaysTrigger = keywordStep.consultationConfig.triggers.find((t: any) => t.type === 'always');
      expect(alwaysTrigger).toBeDefined();
      console.log('Registry always trigger:', JSON.stringify(alwaysTrigger, null, 2));
    });

    test('should create workflow from template with consultation config', async () => {
      const template = templateRegistry.getTemplate('blog-post-seo');
      const workflow = await templateRegistry.createWorkflowFromTemplate(
        'blog-post-seo',
        {
          topic: 'CRM software',
          target_audience: 'business owners',
          primary_keyword: 'crm'
        }
      );

      expect(workflow).toBeDefined();
      expect(workflow.steps).toBeDefined();
      
      const keywordStep = workflow.steps.find((s: any) => s.id === 'keyword-research');
      console.log('Created workflow keyword step:', JSON.stringify(keywordStep, null, 2));
      
      expect(keywordStep).toBeDefined();
      expect(keywordStep.consultationConfig).toBeDefined();
      expect(keywordStep.consultationConfig.enabled).toBe(true);
    });
  });

  describe('Enhanced AI Generation Step Execution', () => {
    test('should execute step with consultation config from template', async () => {
      const template = templateRegistry.getTemplate('blog-post-seo');
      const keywordStep = template.workflow.steps.find((s: any) => s.id === 'keyword-research');
      
      const inputs = {
        topic: 'CRM software',
        target_audience: 'business owners',
        primary_keyword: 'crm'
      };

      console.log('Executing step with inputs:', JSON.stringify(inputs, null, 2));
      console.log('Step consultation config:', JSON.stringify(keywordStep.consultationConfig, null, 2));

      // Mock the AI generation to focus on consultation
      const originalExecute = enhancedAIStep.executeAIGenerationWithConsultation;
      let consultationCalled = false;
      let consultationConfig: any = null;
      let consultationContext: any = null;

      enhancedAIStep.executeAIGenerationWithConsultation = async function(step, inputs, executionId) {
        consultationCalled = true;
        consultationConfig = step.consultationConfig;
        consultationContext = (this as any).prepareConsultationContext(inputs, step);
        
        console.log('Consultation called with config:', JSON.stringify(consultationConfig, null, 2));
        console.log('Consultation context:', JSON.stringify(consultationContext, null, 2));
        
        // Return mock result
        return {
          outputs: {
            content: 'Mock AI generated content',
            keyword_research: 'Mock keyword research'
          },
          consultationResults: []
        };
      };

      const result = await enhancedAIStep.execute(
        'test-execution-id',
        keywordStep,
        inputs
      );

      expect(consultationCalled).toBe(true);
      expect(consultationConfig).toBeDefined();
      expect(consultationConfig.enabled).toBe(true);
      expect(consultationContext).toBeDefined();
      expect(consultationContext.topic).toBe('CRM software');
      
      // Restore original method
      enhancedAIStep.executeAIGenerationWithConsultation = originalExecute;
    });

    test('should prepare consultation context correctly', () => {
      const template = templateRegistry.getTemplate('blog-post-seo');
      const keywordStep = template.workflow.steps.find((s: any) => s.id === 'keyword-research');
      
      const inputs = {
        topic: 'CRM software',
        target_audience: 'business owners',
        primary_keyword: 'crm'
      };

      // Access the private method to test context preparation
      const context = (enhancedAIStep as any).prepareConsultationContext(inputs, keywordStep);
      
      console.log('Prepared consultation context:', JSON.stringify(context, null, 2));
      
      expect(context).toBeDefined();
      expect(context.topic).toBe('CRM software');
      expect(context.targetAudience).toBe('business owners');
      expect(context.primaryKeyword).toBe('crm');
      expect(context.contentType).toBeDefined();
      expect(context.qualityScore).toBeDefined();
      expect(context.complexity).toBeDefined();
    });
  });

  describe('Consultation Service Integration', () => {
    test('should trigger consultation with prepared context', async () => {
      const { getConsultationService } = require('../singleton');
      const consultationService = getConsultationService();
      
      const template = templateRegistry.getTemplate('blog-post-seo');
      const keywordStep = template.workflow.steps.find((s: any) => s.id === 'keyword-research');
      
      const inputs = {
        topic: 'CRM software',
        target_audience: 'business owners',
        primary_keyword: 'crm'
      };

      const context = (enhancedAIStep as any).prepareConsultationContext(inputs, keywordStep);
      const config = keywordStep.consultationConfig;
      
      console.log('Testing consultation service with:');
      console.log('Config:', JSON.stringify(config, null, 2));
      console.log('Context:', JSON.stringify(context, null, 2));
      
      const shouldTrigger = await consultationService.shouldTriggerConsultation(config, context);
      console.log('Should trigger result:', shouldTrigger);
      
      expect(shouldTrigger).toBe(true);
      
      if (shouldTrigger) {
        const results = await consultationService.consultMultipleAgents(
          'test-execution-id',
          'keyword-research',
          context,
          config
        );
        
        console.log('Consultation results count:', results.length);
        expect(results).toBeDefined();
        expect(Array.isArray(results)).toBe(true);
      }
    });
  });
});
