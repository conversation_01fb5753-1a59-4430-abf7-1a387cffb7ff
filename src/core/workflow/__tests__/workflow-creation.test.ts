/**
 * TDD Tests for Workflow Creation Issues
 * Testing the critical workflow creation and retrieval problems
 */

import { WorkflowEngine } from '../engine';
import { SimplifiedStateStore } from '../../state/store';
import { MemoryStorageAdapter } from '../../state/types';
import { Workflow, WorkflowStep, StepType } from '../types';

describe('Workflow Creation TDD Tests', () => {
  let engine: WorkflowEngine;
  let stateStore: SimplifiedStateStore;

  beforeEach(async () => {
    // Use memory adapter for testing
    const memoryAdapter = new MemoryStorageAdapter();
    stateStore = new SimplifiedStateStore(memoryAdapter);
    await stateStore.initialize();
    
    engine = new WorkflowEngine(stateStore);
  });

  describe('Critical Issue: getWorkflow returns undefined after creation', () => {
    test('should create workflow and immediately retrieve it successfully', async () => {
      // Arrange
      const workflowData = {
        name: 'Test Workflow',
        description: 'Test workflow for TDD',
        steps: [
          {
            id: 'step1',
            name: 'Test Step',
            type: StepType.AI_TASK,
            config: { prompt: 'Test prompt' },
            dependencies: []
          } as WorkflowStep
        ],
        metadata: {
          category: 'test',
          difficulty: 'easy' as const,
          estimatedTime: 5,
          tags: ['test'],
          featured: false
        }
      };

      // Act
      const workflowId = await engine.createWorkflow(workflowData);
      const retrievedWorkflow = await engine.getWorkflow(workflowId);

      // Assert
      expect(workflowId).toBeDefined();
      expect(workflowId).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);
      expect(retrievedWorkflow).not.toBeNull();
      expect(retrievedWorkflow?.id).toBe(workflowId);
      expect(retrievedWorkflow?.name).toBe('Test Workflow');
      expect(retrievedWorkflow?.steps).toHaveLength(1);
    });

    test('should handle multiple workflow creations without conflicts', async () => {
      // Arrange
      const workflow1Data = {
        name: 'Workflow 1',
        description: 'First workflow',
        steps: [],
        metadata: {
          category: 'test',
          difficulty: 'easy' as const,
          estimatedTime: 5,
          tags: ['test'],
          featured: false
        }
      };

      const workflow2Data = {
        name: 'Workflow 2', 
        description: 'Second workflow',
        steps: [],
        metadata: {
          category: 'test',
          difficulty: 'medium' as const,
          estimatedTime: 10,
          tags: ['test'],
          featured: false
        }
      };

      // Act
      const workflowId1 = await engine.createWorkflow(workflow1Data);
      const workflowId2 = await engine.createWorkflow(workflow2Data);
      
      const retrieved1 = await engine.getWorkflow(workflowId1);
      const retrieved2 = await engine.getWorkflow(workflowId2);

      // Assert
      expect(workflowId1).not.toBe(workflowId2);
      expect(retrieved1).not.toBeNull();
      expect(retrieved2).not.toBeNull();
      expect(retrieved1?.name).toBe('Workflow 1');
      expect(retrieved2?.name).toBe('Workflow 2');
    });

    test('should persist workflow across state store operations', async () => {
      // Arrange
      const workflowData = {
        name: 'Persistent Workflow',
        description: 'Test persistence',
        steps: [],
        metadata: {
          category: 'test',
          difficulty: 'easy' as const,
          estimatedTime: 5,
          tags: ['persistence'],
          featured: false
        }
      };

      // Act
      const workflowId = await engine.createWorkflow(workflowData);
      
      // Simulate other state operations
      await stateStore.updateUsage({ totalWorkflows: 1 });
      
      const retrievedWorkflow = await engine.getWorkflow(workflowId);

      // Assert
      expect(retrievedWorkflow).not.toBeNull();
      expect(retrievedWorkflow?.id).toBe(workflowId);
      expect(retrievedWorkflow?.name).toBe('Persistent Workflow');
    });

    test('should return null for non-existent workflow', async () => {
      // Arrange
      const nonExistentId = '00000000-0000-0000-0000-000000000000';

      // Act
      const result = await engine.getWorkflow(nonExistentId);

      // Assert
      expect(result).toBeNull();
    });

    test('should handle workflow creation errors gracefully', async () => {
      // Test empty name
      const invalidWorkflowData1 = {
        name: '', // Invalid empty name
        description: 'Invalid workflow',
        steps: [],
        metadata: {
          category: 'test',
          difficulty: 'easy' as const,
          estimatedTime: 5,
          tags: ['test'],
          featured: false
        }
      };

      await expect(engine.createWorkflow(invalidWorkflowData1)).rejects.toThrow('Workflow name is required');

      // Test empty description
      const invalidWorkflowData2 = {
        name: 'Valid Name',
        description: '', // Invalid empty description
        steps: [],
        metadata: {
          category: 'test',
          difficulty: 'easy' as const,
          estimatedTime: 5,
          tags: ['test'],
          featured: false
        }
      };

      await expect(engine.createWorkflow(invalidWorkflowData2)).rejects.toThrow('Workflow description is required');

      // Test invalid difficulty
      const invalidWorkflowData3 = {
        name: 'Valid Name',
        description: 'Valid description',
        steps: [],
        metadata: {
          category: 'test',
          difficulty: 'invalid' as any,
          estimatedTime: 5,
          tags: ['test'],
          featured: false
        }
      };

      await expect(engine.createWorkflow(invalidWorkflowData3)).rejects.toThrow('Workflow difficulty must be');
    });
  });

  describe('State Store Integration', () => {
    test('should update workflow count correctly', async () => {
      // Arrange
      const initialUsage = await stateStore.getUsage();
      const initialCount = initialUsage.totalWorkflows;

      const workflowData = {
        name: 'Count Test Workflow',
        description: 'Test workflow counting',
        steps: [],
        metadata: {
          category: 'test',
          difficulty: 'easy' as const,
          estimatedTime: 5,
          tags: ['count'],
          featured: false
        }
      };

      // Act
      await engine.createWorkflow(workflowData);
      const updatedUsage = await stateStore.getUsage();

      // Assert
      expect(updatedUsage.totalWorkflows).toBe(initialCount + 1);
    });

    test('should maintain state consistency during concurrent operations', async () => {
      // Arrange
      const workflowPromises = Array.from({ length: 3 }, (_, i) => 
        engine.createWorkflow({
          name: `Concurrent Workflow ${i}`,
          description: `Concurrent test ${i}`,
          steps: [],
          metadata: {
            category: 'test',
            difficulty: 'easy' as const,
            estimatedTime: 5,
            tags: ['concurrent'],
            featured: false
          }
        })
      );

      // Act
      const workflowIds = await Promise.all(workflowPromises);
      const retrievalPromises = workflowIds.map(id => engine.getWorkflow(id));
      const retrievedWorkflows = await Promise.all(retrievalPromises);

      // Assert
      expect(workflowIds).toHaveLength(3);
      expect(retrievedWorkflows.every(w => w !== null)).toBe(true);
      expect(new Set(workflowIds).size).toBe(3); // All IDs should be unique
    });
  });
});
