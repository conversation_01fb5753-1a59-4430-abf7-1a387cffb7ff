/**
 * Artifact Version Manager
 * Handles versioning, revert functionality, and version history for artifacts
 */

import { v4 as uuidv4 } from 'uuid';
import { SimplifiedStateStore } from '../state/store';
import { ContentItem, ArtifactVersion } from '../types';

export interface VersionMetadata {
  description?: string;
  revertReason?: string;
  createdBy: string;
  tags?: string[];
}

export interface VersionComparison {
  versionId1: string;
  versionId2: string;
  differences: {
    field: string;
    oldValue: any;
    newValue: any;
  }[];
  summary: string;
}

export class ArtifactVersionManager {
  private store: SimplifiedStateStore;

  constructor(store: SimplifiedStateStore) {
    this.store = store;
  }

  /**
   * Create a new version of an artifact
   */
  async createVersion(
    artifactId: string, 
    content: any, 
    metadata: VersionMetadata
  ): Promise<string> {
    try {
      const versionId = uuidv4();
      const timestamp = new Date().toISOString();

      // Get current artifact to determine version number
      const currentArtifact = await this.store.getContent(artifactId);
      if (!currentArtifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }

      // Get existing versions to determine next version number
      const existingVersions = await this.getVersionHistory(artifactId);
      const nextVersionNumber = existingVersions.length + 1;

      // Create version record
      const version: ArtifactVersion = {
        id: versionId,
        artifactId,
        versionNumber: nextVersionNumber,
        content,
        metadata: {
          ...metadata,
          createdAt: timestamp,
          isActive: false // New versions are not active by default
        },
        createdAt: timestamp,
        createdBy: metadata.createdBy
      };

      // Store the version
      await this.store.setArtifactVersion(version);

      console.log(`✅ Created version ${nextVersionNumber} for artifact ${artifactId}: ${versionId}`);
      return versionId;

    } catch (error) {
      console.error(`❌ Failed to create version for artifact ${artifactId}:`, error);
      throw error;
    }
  }

  /**
   * Revert artifact to a previous version
   */
  async revertToVersion(artifactId: string, versionId: string, revertReason?: string): Promise<string> {
    try {
      // Get the version to revert to
      const targetVersion = await this.store.getArtifactVersion(versionId);
      if (!targetVersion) {
        throw new Error(`Version ${versionId} not found`);
      }

      if (targetVersion.artifactId !== artifactId) {
        throw new Error(`Version ${versionId} does not belong to artifact ${artifactId}`);
      }

      // Get current artifact
      const currentArtifact = await this.store.getContent(artifactId);
      if (!currentArtifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }

      // Create a backup version of current state before reverting
      const backupVersionId = await this.createVersion(
        artifactId,
        currentArtifact.content,
        {
          description: `Backup before reverting to version ${targetVersion.versionNumber}`,
          createdBy: 'system',
          tags: ['backup', 'pre-revert']
        }
      );

      // Update the artifact with the target version's content
      const revertedArtifact: ContentItem = {
        ...currentArtifact,
        content: targetVersion.content,
        updatedAt: new Date().toISOString(),
        version: targetVersion.versionNumber
      };

      await this.store.setContent(revertedArtifact);

      // Mark the target version as active
      const updatedTargetVersion: ArtifactVersion = {
        ...targetVersion,
        metadata: {
          ...targetVersion.metadata,
          isActive: true,
          revertedAt: new Date().toISOString(),
          revertReason
        }
      };

      await this.store.setArtifactVersion(updatedTargetVersion);

      // Deactivate other versions
      await this.deactivateOtherVersions(artifactId, versionId);

      console.log(`✅ Reverted artifact ${artifactId} to version ${targetVersion.versionNumber} (${versionId})`);
      return versionId;

    } catch (error) {
      console.error(`❌ Failed to revert artifact ${artifactId} to version ${versionId}:`, error);
      throw error;
    }
  }

  /**
   * Get a specific version
   */
  async getVersion(versionId: string): Promise<ArtifactVersion | null> {
    try {
      return await this.store.getArtifactVersion(versionId);
    } catch (error) {
      console.error(`❌ Failed to get version ${versionId}:`, error);
      return null;
    }
  }

  /**
   * Get the currently active version
   */
  async getActiveVersion(artifactId: string): Promise<ArtifactVersion | null> {
    try {
      const versions = await this.getVersionHistory(artifactId);
      return versions.find(v => v.metadata.isActive) || null;
    } catch (error) {
      console.error(`❌ Failed to get active version for artifact ${artifactId}:`, error);
      return null;
    }
  }

  /**
   * Get complete version history for an artifact
   */
  async getVersionHistory(artifactId: string): Promise<ArtifactVersion[]> {
    try {
      const allVersions = await this.store.getAllArtifactVersions();
      return allVersions
        .filter(v => v.artifactId === artifactId)
        .sort((a, b) => b.versionNumber - a.versionNumber); // Latest first
    } catch (error) {
      console.error(`❌ Failed to get version history for artifact ${artifactId}:`, error);
      return [];
    }
  }

  /**
   * Compare content between two versions
   */
  async compareVersions(versionId1: string, versionId2: string): Promise<VersionComparison | null> {
    try {
      const version1 = await this.getVersion(versionId1);
      const version2 = await this.getVersion(versionId2);

      if (!version1 || !version2) {
        throw new Error('One or both versions not found');
      }

      if (version1.artifactId !== version2.artifactId) {
        throw new Error('Versions belong to different artifacts');
      }

      const differences = this.findDifferences(version1.content, version2.content);
      
      const comparison: VersionComparison = {
        versionId1,
        versionId2,
        differences,
        summary: `Found ${differences.length} differences between version ${version1.versionNumber} and ${version2.versionNumber}`
      };

      return comparison;

    } catch (error) {
      console.error(`❌ Failed to compare versions ${versionId1} and ${versionId2}:`, error);
      return null;
    }
  }

  /**
   * Create a new artifact with rejection feedback
   */
  async createRejectionVersion(
    artifactId: string, 
    rejectionFeedback: string, 
    rejectedBy: string
  ): Promise<string> {
    try {
      // Get current artifact
      const currentArtifact = await this.store.getContent(artifactId);
      if (!currentArtifact) {
        throw new Error(`Artifact ${artifactId} not found`);
      }

      // Create new content based on rejection feedback
      const rejectionContent = {
        ...currentArtifact.content,
        rejectionFeedback,
        rejectedAt: new Date().toISOString(),
        rejectedBy,
        status: 'revision_needed'
      };

      // Create a new version with rejection feedback
      const versionId = await this.createVersion(
        artifactId,
        rejectionContent,
        {
          description: `Revision needed based on rejection feedback`,
          revertReason: rejectionFeedback,
          createdBy: rejectedBy,
          tags: ['rejection', 'revision-needed']
        }
      );

      // Update the artifact status
      const updatedArtifact: ContentItem = {
        ...currentArtifact,
        content: rejectionContent,
        status: 'revision_needed',
        updatedAt: new Date().toISOString()
      };

      await this.store.setContent(updatedArtifact);

      console.log(`✅ Created rejection version for artifact ${artifactId}: ${versionId}`);
      return versionId;

    } catch (error) {
      console.error(`❌ Failed to create rejection version for artifact ${artifactId}:`, error);
      throw error;
    }
  }

  /**
   * Private helper methods
   */
  private async deactivateOtherVersions(artifactId: string, activeVersionId: string): Promise<void> {
    const versions = await this.getVersionHistory(artifactId);
    
    for (const version of versions) {
      if (version.id !== activeVersionId && version.metadata.isActive) {
        const updatedVersion: ArtifactVersion = {
          ...version,
          metadata: {
            ...version.metadata,
            isActive: false
          }
        };
        await this.store.setArtifactVersion(updatedVersion);
      }
    }
  }

  private findDifferences(content1: any, content2: any, path: string = ''): any[] {
    const differences: any[] = [];

    if (typeof content1 !== typeof content2) {
      differences.push({
        field: path || 'root',
        oldValue: content1,
        newValue: content2
      });
      return differences;
    }

    if (typeof content1 === 'object' && content1 !== null) {
      const keys = new Set([...Object.keys(content1), ...Object.keys(content2)]);
      
      for (const key of keys) {
        const newPath = path ? `${path}.${key}` : key;
        
        if (!(key in content1)) {
          differences.push({
            field: newPath,
            oldValue: undefined,
            newValue: content2[key]
          });
        } else if (!(key in content2)) {
          differences.push({
            field: newPath,
            oldValue: content1[key],
            newValue: undefined
          });
        } else {
          differences.push(...this.findDifferences(content1[key], content2[key], newPath));
        }
      }
    } else if (content1 !== content2) {
      differences.push({
        field: path || 'root',
        oldValue: content1,
        newValue: content2
      });
    }

    return differences;
  }
}
