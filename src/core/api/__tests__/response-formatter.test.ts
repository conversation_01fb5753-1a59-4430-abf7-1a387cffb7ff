import { ResponseFormatter } from '../response-formatter';
import { ErrorType } from '../../utils/error-handler';

describe('ResponseFormatter', () => {
  describe('success', () => {
    it('should create a success response with data', async () => {
      const data = { id: 1, name: 'Test' };
      const response = ResponseFormatter.success(data);

      // Extract JSON from NextResponse
      const responseData = await response.json();

      expect(responseData).toMatchObject({
        success: true,
        data,
        meta: {
          requestId: expect.any(String),
          timestamp: expect.any(String),
          version: '1.0'
        }
      });
    });

    it('should create a success response with pagination', async () => {
      const data = [{ id: 1 }, { id: 2 }];
      const pagination = {
        page: 1,
        limit: 10,
        total: 2,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      };
      const response = ResponseFormatter.success(data, 'test-request-id', pagination);

      const responseData = await response.json();

      expect(responseData).toMatchObject({
        success: true,
        data,
        meta: {
          requestId: 'test-request-id',
          timestamp: expect.any(String),
          version: '1.0',
          pagination
        }
      });
    });
  });

  describe('error', () => {
    it('should create an error response with message', async () => {
      const message = 'Something went wrong';
      const response = ResponseFormatter.error(
        ErrorType.SYSTEM,
        'INTERNAL_ERROR',
        message
      );

      const responseData = await response.json();

      expect(responseData).toMatchObject({
        success: false,
        error: {
          type: ErrorType.SYSTEM,
          code: 'INTERNAL_ERROR',
          message
        },
        meta: {
          requestId: expect.any(String),
          timestamp: expect.any(String),
          version: '1.0'
        }
      });
    });

    it('should create an error response with details', async () => {
      const message = 'Validation failed';
      const code = 'VALIDATION_ERROR';
      const details = { field: 'email', reason: 'invalid format' };
      const response = ResponseFormatter.error(
        ErrorType.VALIDATION,
        code,
        message,
        details
      );

      const responseData = await response.json();

      expect(responseData).toMatchObject({
        success: false,
        error: {
          type: ErrorType.VALIDATION,
          code,
          message,
          details
        },
        meta: {
          requestId: expect.any(String),
          timestamp: expect.any(String),
          version: '1.0'
        }
      });
    });

    it('should create an error response from Error object', async () => {
      const error = new Error('Test error');
      const response = ResponseFormatter.fromError(error);

      const responseData = await response.json();

      expect(responseData).toMatchObject({
        success: false,
        error: {
          type: ErrorType.SYSTEM,
          code: 'INTERNAL_ERROR',
          message: 'Test error'
        },
        meta: {
          requestId: expect.any(String),
          timestamp: expect.any(String),
          version: '1.0'
        }
      });
    });
  });

  describe('paginated', () => {
    it('should create a paginated response', () => {
      const data = [{ id: 1 }, { id: 2 }];
      const page = 1;
      const limit = 10;
      const total = 25;
      const response = ResponseFormatter.paginated(data, page, limit, total);

      const responseData = JSON.parse(JSON.stringify(response));

      expect(responseData).toMatchObject({
        success: true,
        data,
        meta: {
          requestId: expect.any(String),
          timestamp: expect.any(String),
          version: '1.0',
          pagination: {
            page,
            limit,
            total,
            totalPages: 3,
            hasNext: true,
            hasPrev: false
          }
        }
      });
    });

    it('should create a paginated response with no next page', () => {
      const data = [{ id: 1 }];
      const page = 1;
      const limit = 10;
      const total = 1;
      const response = ResponseFormatter.paginated(data, page, limit, total);

      const responseData = JSON.parse(JSON.stringify(response));

      expect(responseData).toMatchObject({
        success: true,
        data,
        meta: {
          requestId: expect.any(String),
          timestamp: expect.any(String),
          version: '1.0',
          pagination: {
            page,
            limit,
            total,
            totalPages: 1,
            hasNext: false,
            hasPrev: false
          }
        }
      });
    });
  });

  describe('validationError', () => {
    it('should create a validation error response', () => {
      const message = 'Validation failed';
      const validationErrors = [
        { field: 'email', message: 'Email is required' },
        { field: 'password', message: 'Password must be at least 8 characters' }
      ];
      const response = ResponseFormatter.validationError(message, validationErrors);

      const responseData = JSON.parse(JSON.stringify(response));

      expect(responseData).toMatchObject({
        success: false,
        error: {
          type: ErrorType.VALIDATION,
          code: 'VALIDATION_ERROR',
          message,
          details: { validationErrors }
        },
        meta: {
          requestId: expect.any(String),
          timestamp: expect.any(String),
          version: '1.0'
        }
      });
    });
  });

  describe('notFound', () => {
    it('should create a 404 response', () => {
      const resource = 'User';
      const id = '123';
      const response = ResponseFormatter.notFound(resource, id);

      const responseData = JSON.parse(JSON.stringify(response));

      expect(responseData).toMatchObject({
        success: false,
        error: {
          type: ErrorType.SYSTEM,
          code: 'NOT_FOUND',
          message: `${resource} with id '${id}' not found`,
          details: { resource, id }
        },
        meta: {
          requestId: expect.any(String),
          timestamp: expect.any(String),
          version: '1.0'
        }
      });
    });
  });

  describe('unauthorized', () => {
    it('should create a 401 response', () => {
      const response = ResponseFormatter.unauthorized();

      const responseData = JSON.parse(JSON.stringify(response));

      expect(responseData).toMatchObject({
        success: false,
        error: {
          type: ErrorType.AUTHENTICATION,
          code: 'AUTHENTICATION_FAILED',
          message: 'Authentication required'
        },
        meta: {
          requestId: expect.any(String),
          timestamp: expect.any(String),
          version: '1.0'
        }
      });
    });

    it('should create a 401 response with custom message', () => {
      const message = 'Invalid credentials';
      const response = ResponseFormatter.unauthorized(message);

      const responseData = JSON.parse(JSON.stringify(response));

      expect(responseData).toMatchObject({
        success: false,
        error: {
          type: ErrorType.AUTHENTICATION,
          code: 'AUTHENTICATION_FAILED',
          message
        }
      });
    });
  });

  describe('forbidden', () => {
    it('should create a 403 response', () => {
      const response = ResponseFormatter.forbidden();

      const responseData = JSON.parse(JSON.stringify(response));

      expect(responseData).toMatchObject({
        success: false,
        error: {
          type: ErrorType.AUTHORIZATION,
          code: 'AUTHORIZATION_DENIED',
          message: 'Access denied'
        },
        meta: {
          requestId: expect.any(String),
          timestamp: expect.any(String),
          version: '1.0'
        }
      });
    });
  });

  describe('rateLimit', () => {
    it('should create a 429 response', () => {
      const message = 'Too many requests';
      const retryAfter = 60;
      const response = ResponseFormatter.rateLimit(message, retryAfter);

      const responseData = JSON.parse(JSON.stringify(response));

      expect(responseData).toMatchObject({
        success: false,
        error: {
          type: ErrorType.SYSTEM,
          code: 'RATE_LIMIT_EXCEEDED',
          message,
          details: { retryAfter }
        },
        meta: {
          requestId: expect.any(String),
          timestamp: expect.any(String),
          version: '1.0'
        }
      });
    });
  });

  describe('utility functions', () => {
    it('should extract request ID from headers', () => {
      const mockRequest = {
        headers: {
          get: jest.fn().mockReturnValue('test-request-id')
        }
      } as any;

      const requestId = ResponseFormatter.getRequestId(mockRequest);

      expect(requestId).toBe('test-request-id');
      expect(mockRequest.headers.get).toHaveBeenCalledWith('x-request-id');
    });

    it('should generate request ID when not in headers', () => {
      const mockRequest = {
        headers: {
          get: jest.fn().mockReturnValue(null)
        }
      } as any;

      const requestId = ResponseFormatter.getRequestId(mockRequest);

      expect(requestId).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
    });
  });
});
