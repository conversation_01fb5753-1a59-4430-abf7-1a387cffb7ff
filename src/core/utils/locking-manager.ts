/**
 * Distributed Locking Manager
 * Prevents race conditions in concurrent workflow execution
 */

import { Redis } from '@upstash/redis';
import { errorHandler, ErrorType, ErrorSeverity } from './error-handler';

export interface LockOptions {
  ttl?: number; // Time to live in milliseconds
  retryDelay?: number; // Delay between retry attempts in milliseconds
  maxRetries?: number; // Maximum number of retry attempts
  timeout?: number; // Maximum time to wait for lock in milliseconds
}

export interface LockResult {
  acquired: boolean;
  lockId?: string;
  expiresAt?: number;
  error?: string;
}

export interface LockInfo {
  lockId: string;
  key: string;
  acquiredAt: number;
  expiresAt: number;
  owner: string;
}

export class LockingManager {
  private static instance: LockingManager;
  private redis: Redis | null = null;
  private activeLocks = new Map<string, LockInfo>();
  private lockPrefix = 'lock:';
  private instanceId: string;

  private constructor() {
    this.instanceId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    this.initializeRedis();
  }

  static getInstance(): LockingManager {
    if (!LockingManager.instance) {
      LockingManager.instance = new LockingManager();
    }
    return LockingManager.instance;
  }

  /**
   * Acquire a distributed lock
   */
  async acquireLock(
    key: string,
    options: LockOptions = {}
  ): Promise<LockResult> {
    const {
      ttl = 30000, // 30 seconds default
      retryDelay = 100,
      maxRetries = 50,
      timeout = 5000
    } = options;

    const lockKey = this.getLockKey(key);
    const lockId = this.generateLockId();
    const startTime = Date.now();

    try {
      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        // Check timeout
        if (Date.now() - startTime > timeout) {
          return {
            acquired: false,
            error: 'Lock acquisition timeout'
          };
        }

        const acquired = await this.tryAcquireLock(lockKey, lockId, ttl);
        
        if (acquired) {
          const lockInfo: LockInfo = {
            lockId,
            key,
            acquiredAt: Date.now(),
            expiresAt: Date.now() + ttl,
            owner: this.instanceId
          };

          this.activeLocks.set(lockKey, lockInfo);
          
          console.log(`🔒 Lock acquired: ${key} (${lockId})`);
          
          return {
            acquired: true,
            lockId,
            expiresAt: lockInfo.expiresAt
          };
        }

        // Wait before retry
        if (attempt < maxRetries) {
          await this.sleep(retryDelay);
        }
      }

      return {
        acquired: false,
        error: 'Maximum retry attempts exceeded'
      };

    } catch (error) {
      const standardError = errorHandler.handleError(error, {
        operation: 'acquireLock',
        key,
        lockId,
        options
      });

      return {
        acquired: false,
        error: standardError.message
      };
    }
  }

  /**
   * Release a distributed lock
   */
  async releaseLock(key: string, lockId: string): Promise<boolean> {
    const lockKey = this.getLockKey(key);

    try {
      const released = await this.tryReleaseLock(lockKey, lockId);
      
      if (released) {
        this.activeLocks.delete(lockKey);
        console.log(`🔓 Lock released: ${key} (${lockId})`);
      }

      return released;

    } catch (error) {
      const standardError = errorHandler.handleError(error, {
        operation: 'releaseLock',
        key,
        lockId
      });
      console.error('❌ Failed to release lock:', standardError.message);
      return false;
    }
  }

  /**
   * Execute function with lock protection
   */
  async withLock<T>(
    key: string,
    fn: () => Promise<T>,
    options: LockOptions = {}
  ): Promise<T> {
    const lockResult = await this.acquireLock(key, options);
    
    if (!lockResult.acquired) {
      const error = errorHandler.createError(
        ErrorType.SYSTEM,
        'LOCK_ACQUISITION_FAILED',
        `Failed to acquire lock for key: ${key}`,
        { key, error: lockResult.error },
        ErrorSeverity.HIGH
      );
      throw error;
    }

    try {
      const result = await fn();
      return result;
    } finally {
      if (lockResult.lockId) {
        await this.releaseLock(key, lockResult.lockId);
      }
    }
  }

  /**
   * Check if a lock exists
   */
  async isLocked(key: string): Promise<boolean> {
    const lockKey = this.getLockKey(key);

    try {
      if (this.redis) {
        const exists = await this.redis.exists(lockKey);
        return exists === 1;
      } else {
        // Fallback to memory check
        const lockInfo = this.activeLocks.get(lockKey);
        return lockInfo ? Date.now() < lockInfo.expiresAt : false;
      }
    } catch (error) {
      console.error('Error checking lock status:', error);
      return false;
    }
  }

  /**
   * Get lock information
   */
  async getLockInfo(key: string): Promise<LockInfo | null> {
    const lockKey = this.getLockKey(key);

    try {
      if (this.redis) {
        const lockData = await this.redis.get(lockKey);
        return lockData ? JSON.parse(lockData as string) : null;
      } else {
        return this.activeLocks.get(lockKey) || null;
      }
    } catch (error) {
      console.error('Error getting lock info:', error);
      return null;
    }
  }

  /**
   * Clean up expired locks
   */
  async cleanupExpiredLocks(): Promise<number> {
    let cleaned = 0;

    try {
      const now = Date.now();
      
      for (const [lockKey, lockInfo] of this.activeLocks.entries()) {
        if (now > lockInfo.expiresAt) {
          this.activeLocks.delete(lockKey);
          cleaned++;
        }
      }

      console.log(`🧹 Cleaned up ${cleaned} expired locks`);
      return cleaned;

    } catch (error) {
      console.error('Error cleaning up expired locks:', error);
      return 0;
    }
  }

  /**
   * Get all active locks
   */
  getActiveLocks(): LockInfo[] {
    return Array.from(this.activeLocks.values());
  }

  /**
   * Initialize Redis connection
   */
  private initializeRedis(): void {
    try {
      if (process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN) {
        this.redis = new Redis({
          url: process.env.UPSTASH_REDIS_REST_URL,
          token: process.env.UPSTASH_REDIS_REST_TOKEN
        });
        console.log('🔗 Locking manager initialized with Redis');
      } else {
        console.log('⚠️ Redis not configured, using memory-based locking');
      }
    } catch (error) {
      console.error('❌ Failed to initialize Redis for locking:', error);
      this.redis = null;
    }
  }

  /**
   * Try to acquire lock using Redis or memory
   */
  private async tryAcquireLock(lockKey: string, lockId: string, ttl: number): Promise<boolean> {
    if (this.redis) {
      // Use Redis SET with NX (only if not exists) and PX (expiration)
      const result = await this.redis.set(lockKey, lockId, { nx: true, px: ttl });
      return result === 'OK';
    } else {
      // Memory-based locking
      const existing = this.activeLocks.get(lockKey);
      if (existing && Date.now() < existing.expiresAt) {
        return false; // Lock already exists and not expired
      }
      return true; // Can acquire lock
    }
  }

  /**
   * Try to release lock using Redis or memory
   */
  private async tryReleaseLock(lockKey: string, lockId: string): Promise<boolean> {
    if (this.redis) {
      // Use Lua script to ensure atomic check-and-delete
      const luaScript = `
        if redis.call("get", KEYS[1]) == ARGV[1] then
          return redis.call("del", KEYS[1])
        else
          return 0
        end
      `;
      const result = await this.redis.eval(luaScript, [lockKey], [lockId]);
      return result === 1;
    } else {
      // Memory-based release
      const existing = this.activeLocks.get(lockKey);
      if (existing && existing.lockId === lockId) {
        this.activeLocks.delete(lockKey);
        return true;
      }
      return false;
    }
  }

  private getLockKey(key: string): string {
    return `${this.lockPrefix}${key}`;
  }

  private generateLockId(): string {
    return `${this.instanceId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const lockingManager = LockingManager.getInstance();

// Utility functions
export async function withLock<T>(
  key: string,
  fn: () => Promise<T>,
  options: LockOptions = {}
): Promise<T> {
  return lockingManager.withLock(key, fn, options);
}

export async function acquireLock(key: string, options: LockOptions = {}): Promise<LockResult> {
  return lockingManager.acquireLock(key, options);
}

export async function releaseLock(key: string, lockId: string): Promise<boolean> {
  return lockingManager.releaseLock(key, lockId);
}

export async function isLocked(key: string): Promise<boolean> {
  return lockingManager.isLocked(key);
}

// Auto-cleanup expired locks every 5 minutes
setInterval(() => {
  lockingManager.cleanupExpiredLocks().catch(console.error);
}, 5 * 60 * 1000);
