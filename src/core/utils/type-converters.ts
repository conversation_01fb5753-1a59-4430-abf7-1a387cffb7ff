/**
 * Type Conversion Utilities
 * Safe type conversions between workflow and state systems
 */

import {
  WorkflowArtifact,
  ArtifactType,
  ArtifactStatus,
  StepR<PERSON>ult,
  StepStatus,
  ExecutionMetadata
} from '../workflow/types';
import {
  ContentItem,
  ContentType,
  ContentStatus,
  ContentMetadata,
  Review,
  ReviewType,
  ReviewStatus,
  ReviewDecision
} from '../state/types';
import { errorH<PERSON><PERSON>, ErrorType } from './error-handler';

// Type mapping constants
export const ARTIFACT_TYPE_TO_CONTENT_TYPE: Record<ArtifactType, ContentType> = {
  [ArtifactType.BLOG_POST]: ContentType.BLOG_POST,
  [ArtifactType.PRODUCT_DESCRIPTION]: ContentType.PRODUCT_DESCRIPTION,
  [ArtifactType.KEYWORD_RESEARCH]: ContentType.KEYWORD_RESEARCH,
  [ArtifactType.SEO_ANALYSIS]: ContentType.SEO_ANALYSIS,
  [ArtifactType.EMAIL]: ContentType.EMAIL,
  [ArtifactType.SOCIAL_POST]: ContentType.SOCIAL_POST,
  [ArtifactType.GENERIC]: ContentType.GENERIC
};

export const CONTENT_TYPE_TO_ARTIFACT_TYPE: Record<ContentType, ArtifactType> = {
  [ContentType.BLOG_POST]: ArtifactType.BLOG_POST,
  [ContentType.PRODUCT_DESCRIPTION]: ArtifactType.PRODUCT_DESCRIPTION,
  [ContentType.KEYWORD_RESEARCH]: ArtifactType.KEYWORD_RESEARCH,
  [ContentType.SEO_ANALYSIS]: ArtifactType.SEO_ANALYSIS,
  [ContentType.CONTENT_ANALYSIS]: ArtifactType.GENERIC,
  [ContentType.EMAIL]: ArtifactType.EMAIL,
  [ContentType.SOCIAL_POST]: ArtifactType.SOCIAL_POST,
  [ContentType.GENERIC]: ArtifactType.GENERIC
};

export const ARTIFACT_STATUS_TO_CONTENT_STATUS: Record<ArtifactStatus, ContentStatus> = {
  [ArtifactStatus.DRAFT]: ContentStatus.DRAFT,
  [ArtifactStatus.PENDING_REVIEW]: ContentStatus.REVIEW,
  [ArtifactStatus.APPROVED]: ContentStatus.APPROVED,
  [ArtifactStatus.REJECTED]: ContentStatus.REJECTED,
  [ArtifactStatus.PUBLISHED]: ContentStatus.PUBLISHED
};

export const CONTENT_STATUS_TO_ARTIFACT_STATUS: Record<ContentStatus, ArtifactStatus> = {
  [ContentStatus.DRAFT]: ArtifactStatus.DRAFT,
  [ContentStatus.REVIEW]: ArtifactStatus.PENDING_REVIEW,
  [ContentStatus.APPROVED]: ArtifactStatus.APPROVED,
  [ContentStatus.REJECTED]: ArtifactStatus.REJECTED,
  [ContentStatus.PUBLISHED]: ArtifactStatus.PUBLISHED,
  [ContentStatus.PENDING]: ArtifactStatus.DRAFT,
  [ContentStatus.PENDING_APPROVAL]: ArtifactStatus.PENDING_REVIEW
};

/**
 * Convert WorkflowArtifact to ContentItem
 */
export function workflowArtifactToContentItem(artifact: WorkflowArtifact): ContentItem {
  try {
    const contentType = ARTIFACT_TYPE_TO_CONTENT_TYPE[artifact.type];
    if (!contentType) {
      throw new Error(`Unknown artifact type: ${artifact.type}`);
    }

    const contentStatus = ARTIFACT_STATUS_TO_CONTENT_STATUS[artifact.status];
    if (!contentStatus) {
      throw new Error(`Unknown artifact status: ${artifact.status}`);
    }

    const metadata: ContentMetadata = {
      stepId: artifact.stepId,
      executionId: artifact.executionId,
      version: artifact.version,
      createdBy: artifact.createdBy,
      approvedBy: artifact.approvedBy,
      approvedAt: artifact.approvedAt,
      rejectedBy: artifact.rejectedBy,
      rejectedAt: artifact.rejectedAt,
      rejectionReason: artifact.rejectionReason,
      artifactType: artifact.type,
      // Extract additional metadata from artifact content if available
      ...(artifact.metadata || {})
    };

    // Calculate word count for text content
    if (typeof artifact.content === 'string') {
      metadata.wordCount = artifact.content.split(/\s+/).length;
    }

    const contentItem: ContentItem = {
      id: artifact.id,
      type: contentType,
      title: artifact.title,
      content: artifact.content,
      status: contentStatus,
      executionId: artifact.executionId,
      stepId: artifact.stepId,
      createdAt: artifact.createdAt,
      updatedAt: artifact.updatedAt,
      metadata
    };

    return contentItem;
  } catch (error) {
    const standardError = errorHandler.handleError(error, {
      operation: 'workflowArtifactToContentItem',
      artifactId: artifact.id,
      artifactType: artifact.type
    });
    throw standardError;
  }
}

/**
 * Convert ContentItem to WorkflowArtifact
 */
export function contentItemToWorkflowArtifact(contentItem: ContentItem): WorkflowArtifact {
  try {
    const artifactType = CONTENT_TYPE_TO_ARTIFACT_TYPE[contentItem.type];
    if (!artifactType) {
      throw new Error(`Unknown content type: ${contentItem.type}`);
    }

    const artifactStatus = CONTENT_STATUS_TO_ARTIFACT_STATUS[contentItem.status];
    if (!artifactStatus) {
      throw new Error(`Unknown content status: ${contentItem.status}`);
    }

    const artifact: WorkflowArtifact = {
      id: contentItem.id,
      stepId: contentItem.stepId,
      executionId: contentItem.executionId,
      type: artifactType,
      title: contentItem.title,
      content: contentItem.content,
      status: artifactStatus,
      version: contentItem.metadata.version || 1,
      createdAt: contentItem.createdAt,
      updatedAt: contentItem.updatedAt,
      createdBy: contentItem.metadata.createdBy || 'system',
      approvedBy: contentItem.metadata.approvedBy,
      approvedAt: contentItem.metadata.approvedAt,
      rejectedBy: contentItem.metadata.rejectedBy,
      rejectedAt: contentItem.metadata.rejectedAt,
      rejectionReason: contentItem.metadata.rejectionReason,
      metadata: {
        wordCount: contentItem.metadata.wordCount,
        language: contentItem.metadata.language,
        tags: contentItem.metadata.tags,
        seoScore: contentItem.metadata.seoScore,
        qualityScore: contentItem.metadata.qualityScore,
        aiModel: contentItem.metadata.aiModel,
        aiProvider: contentItem.metadata.aiProvider,
        cost: contentItem.metadata.cost
      }
    };

    return artifact;
  } catch (error) {
    const standardError = errorHandler.handleError(error, {
      operation: 'contentItemToWorkflowArtifact',
      contentId: contentItem.id,
      contentType: contentItem.type
    });
    throw standardError;
  }
}

/**
 * Normalize StepResult for consistent usage
 */
export function normalizeStepResult(stepResult: Partial<StepResult>): StepResult {
  const now = new Date().toISOString();
  
  return {
    stepId: stepResult.stepId || '',
    status: stepResult.status || StepStatus.PENDING,
    inputs: stepResult.inputs || {},
    outputs: stepResult.outputs || {},
    startedAt: stepResult.startedAt || now,
    completedAt: stepResult.completedAt,
    duration: stepResult.duration,
    error: stepResult.error,
    retryCount: stepResult.retryCount || 0,
    artifactId: stepResult.artifactId,
    approvalRequired: stepResult.approvalRequired || false,
    approvedBy: stepResult.approvedBy,
    approvedAt: stepResult.approvedAt,
    rejectionReason: stepResult.rejectionReason,
    metadata: stepResult.metadata || {}
  };
}

/**
 * Normalize ExecutionMetadata for consistent usage
 */
export function normalizeExecutionMetadata(metadata: Partial<ExecutionMetadata>): ExecutionMetadata {
  return {
    source: metadata.source || 'api',
    priority: metadata.priority || 'normal',
    userId: metadata.userId,
    templateId: metadata.templateId,
    tags: metadata.tags || [],
    ...metadata
  };
}

/**
 * Convert Review types between systems
 */
export function convertReviewType(reviewType: string): ReviewType {
  switch (reviewType.toLowerCase()) {
    case 'approval':
      return ReviewType.APPROVAL;
    case 'editing':
      return ReviewType.EDITING;
    case 'feedback':
      return ReviewType.FEEDBACK;
    default:
      return ReviewType.APPROVAL;
  }
}

/**
 * Convert Review status between systems
 */
export function convertReviewStatus(status: string): ReviewStatus {
  switch (status.toLowerCase()) {
    case 'pending':
      return ReviewStatus.PENDING;
    case 'in_progress':
    case 'in-progress':
      return ReviewStatus.IN_PROGRESS;
    case 'completed':
      return ReviewStatus.COMPLETED;
    case 'expired':
      return ReviewStatus.EXPIRED;
    default:
      return ReviewStatus.PENDING;
  }
}

/**
 * Convert Review decision between systems
 */
export function convertReviewDecision(decision: string): ReviewDecision {
  switch (decision.toLowerCase()) {
    case 'approved':
    case 'approve':
      return ReviewDecision.APPROVED;
    case 'rejected':
    case 'reject':
      return ReviewDecision.REJECTED;
    case 'needs_changes':
    case 'needs-changes':
    case 'changes':
      return ReviewDecision.NEEDS_CHANGES;
    default:
      throw new Error(`Invalid review decision: ${decision}`);
  }
}

/**
 * Validate type conversion safety
 */
export function validateTypeConversion<T, U>(
  input: T,
  converter: (input: T) => U,
  validator: (output: U) => boolean,
  context: string
): U {
  try {
    const output = converter(input);
    
    if (!validator(output)) {
      throw new Error(`Type conversion validation failed for ${context}`);
    }
    
    return output;
  } catch (error) {
    const standardError = errorHandler.handleError(error, {
      operation: 'validateTypeConversion',
      context,
      input: typeof input === 'object' ? JSON.stringify(input) : String(input)
    });
    throw standardError;
  }
}

/**
 * Safe type assertion with error handling
 */
export function safeTypeAssertion<T>(
  value: any,
  typeName: string,
  validator: (value: any) => value is T
): T {
  if (!validator(value)) {
    const error = errorHandler.createError(
      ErrorType.VALIDATION,
      'TYPE_ASSERTION_FAILED',
      `Value is not of type ${typeName}`,
      { value, typeName }
    );
    throw error;
  }
  return value;
}

// Type guards for validation
export function isWorkflowArtifact(value: any): value is WorkflowArtifact {
  return value &&
    typeof value.id === 'string' &&
    typeof value.stepId === 'string' &&
    typeof value.executionId === 'string' &&
    typeof value.type === 'string' &&
    typeof value.title === 'string' &&
    typeof value.status === 'string' &&
    typeof value.createdAt === 'string';
}

export function isContentItem(value: any): value is ContentItem {
  return value &&
    typeof value.id === 'string' &&
    typeof value.type === 'string' &&
    typeof value.title === 'string' &&
    typeof value.status === 'string' &&
    typeof value.executionId === 'string' &&
    typeof value.stepId === 'string' &&
    typeof value.createdAt === 'string';
}

export function isStepResult(value: any): value is StepResult {
  return value &&
    typeof value.stepId === 'string' &&
    typeof value.status === 'string' &&
    typeof value.inputs === 'object' &&
    typeof value.outputs === 'object';
}
