/**
 * Environment Validator
 * Validates required environment variables and configurations for production
 */

import { Redis } from '@upstash/redis';
import { errorHandler, ErrorType, ErrorSeverity } from './error-handler';

export interface EnvironmentConfig {
  nodeEnv: string;
  redisUrl?: string;
  redisToken?: string;
  openaiApiKey?: string;
  anthropicApiKey?: string;
  payloadSecret?: string;
  databaseUri?: string;
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  config: EnvironmentConfig;
}

export interface RedisHealthCheck {
  connected: boolean;
  latency?: number;
  error?: string;
  timestamp: string;
}

export class EnvironmentValidator {
  private static instance: EnvironmentValidator;
  private lastRedisCheck: RedisHealthCheck | null = null;
  private redis: Redis | null = null;

  private constructor() {}

  static getInstance(): EnvironmentValidator {
    if (!EnvironmentValidator.instance) {
      EnvironmentValidator.instance = new EnvironmentValidator();
    }
    return EnvironmentValidator.instance;
  }

  /**
   * Validate environment configuration
   */
  validateEnvironment(): ValidationResult {
    const config = this.loadEnvironmentConfig();
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check Node environment
    if (!config.nodeEnv) {
      warnings.push('NODE_ENV not set, defaulting to development');
    }

    // Production-specific validations
    if (this.isProduction(config.nodeEnv)) {
      this.validateProductionRequirements(config, errors, warnings);
    } else {
      this.validateDevelopmentRequirements(config, errors, warnings);
    }

    // General validations
    this.validateGeneralRequirements(config, errors, warnings);

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      config
    };
  }

  /**
   * Perform Redis health check
   */
  async performRedisHealthCheck(forceCheck: boolean = false): Promise<RedisHealthCheck> {
    // Return cached result if recent and not forced
    if (!forceCheck && this.lastRedisCheck && this.isRecentCheck(this.lastRedisCheck)) {
      return this.lastRedisCheck;
    }

    const startTime = Date.now();
    const timestamp = new Date().toISOString();

    try {
      if (!this.redis) {
        this.initializeRedis();
      }

      if (!this.redis) {
        throw new Error('Redis client not initialized');
      }

      // Perform ping test
      const result = await this.redis.ping();
      const latency = Date.now() - startTime;

      this.lastRedisCheck = {
        connected: result === 'PONG',
        latency,
        timestamp
      };

      console.log(`✅ Redis health check passed (${latency}ms)`);
      return this.lastRedisCheck;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      this.lastRedisCheck = {
        connected: false,
        error: errorMessage,
        timestamp
      };

      console.error('❌ Redis health check failed:', errorMessage);
      
      // Log error through error handler
      errorHandler.createError(
        ErrorType.SYSTEM,
        'REDIS_HEALTH_CHECK_FAILED',
        `Redis health check failed: ${errorMessage}`,
        { latency: Date.now() - startTime },
        ErrorSeverity.HIGH
      );

      return this.lastRedisCheck;
    }
  }

  /**
   * Validate and enforce Redis in production
   */
  validateRedisProduction(): void {
    const config = this.loadEnvironmentConfig();
    
    if (this.isProduction(config.nodeEnv)) {
      if (!config.redisUrl || !config.redisToken) {
        const error = errorHandler.createError(
          ErrorType.SYSTEM,
          'REDIS_REQUIRED_PRODUCTION',
          'Redis configuration is required in production environment. Please set UPSTASH_REDIS_REST_URL and UPSTASH_REDIS_REST_TOKEN environment variables.',
          { nodeEnv: config.nodeEnv },
          ErrorSeverity.CRITICAL
        );
        
        console.error('🚨 PRODUCTION DEPLOYMENT BLOCKED:', error.message);
        throw new Error(error.message);
      }

      // Test Redis connection in production
      this.performRedisHealthCheck(true).then(result => {
        if (!result.connected) {
          console.error('🚨 Redis connection failed in production:', result.error);
        }
      }).catch(error => {
        console.error('🚨 Redis health check error in production:', error);
      });
    }
  }

  /**
   * Get Redis connection status
   */
  getRedisStatus(): RedisHealthCheck | null {
    return this.lastRedisCheck;
  }

  /**
   * Initialize Redis client
   */
  private initializeRedis(): void {
    const config = this.loadEnvironmentConfig();
    
    if (config.redisUrl && config.redisToken) {
      try {
        this.redis = new Redis({
          url: config.redisUrl,
          token: config.redisToken
        });
        console.log('📡 Redis client initialized');
      } catch (error) {
        console.error('❌ Failed to initialize Redis client:', error);
        this.redis = null;
      }
    }
  }

  /**
   * Load environment configuration
   */
  private loadEnvironmentConfig(): EnvironmentConfig {
    return {
      nodeEnv: process.env.NODE_ENV || 'development',
      redisUrl: process.env.UPSTASH_REDIS_REST_URL,
      redisToken: process.env.UPSTASH_REDIS_REST_TOKEN,
      openaiApiKey: process.env.OPENAI_API_KEY,
      anthropicApiKey: process.env.ANTHROPIC_API_KEY,
      payloadSecret: process.env.PAYLOAD_SECRET,
      databaseUri: process.env.DATABASE_URI
    };
  }

  /**
   * Check if environment is production
   */
  private isProduction(nodeEnv: string): boolean {
    return nodeEnv === 'production';
  }

  /**
   * Validate production requirements
   */
  private validateProductionRequirements(
    config: EnvironmentConfig,
    errors: string[],
    warnings: string[]
  ): void {
    // Redis is mandatory in production
    if (!config.redisUrl) {
      errors.push('UPSTASH_REDIS_REST_URL is required in production');
    }
    if (!config.redisToken) {
      errors.push('UPSTASH_REDIS_REST_TOKEN is required in production');
    }

    // Database is required
    if (!config.databaseUri) {
      errors.push('DATABASE_URI is required in production');
    }

    // Payload secret is required
    if (!config.payloadSecret) {
      errors.push('PAYLOAD_SECRET is required in production');
    }

    // AI providers (at least one should be configured)
    if (!config.openaiApiKey && !config.anthropicApiKey) {
      warnings.push('No AI provider API keys configured. BYOK (Bring Your Own Key) will be required.');
    }
  }

  /**
   * Validate development requirements
   */
  private validateDevelopmentRequirements(
    config: EnvironmentConfig,
    errors: string[],
    warnings: string[]
  ): void {
    // Redis is optional in development
    if (!config.redisUrl || !config.redisToken) {
      warnings.push('Redis not configured. Using memory storage (data will not persist).');
    }

    // AI providers are optional in development
    if (!config.openaiApiKey && !config.anthropicApiKey) {
      warnings.push('No AI provider API keys configured. BYOK (Bring Your Own Key) will be required.');
    }
  }

  /**
   * Validate general requirements
   */
  private validateGeneralRequirements(
    config: EnvironmentConfig,
    errors: string[],
    warnings: string[]
  ): void {
    // Validate Redis URL format if provided
    if (config.redisUrl && !this.isValidRedisUrl(config.redisUrl)) {
      errors.push('Invalid UPSTASH_REDIS_REST_URL format');
    }

    // Validate database URI format if provided
    if (config.databaseUri && !this.isValidDatabaseUri(config.databaseUri)) {
      warnings.push('DATABASE_URI format may be invalid');
    }
  }

  /**
   * Check if Redis check is recent (within 5 minutes)
   */
  private isRecentCheck(check: RedisHealthCheck): boolean {
    const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
    return new Date(check.timestamp).getTime() > fiveMinutesAgo;
  }

  /**
   * Validate Redis URL format
   */
  private isValidRedisUrl(url: string): boolean {
    try {
      const parsed = new URL(url);
      return parsed.protocol === 'https:' && parsed.hostname.includes('upstash.io');
    } catch {
      return false;
    }
  }

  /**
   * Validate database URI format
   */
  private isValidDatabaseUri(uri: string): boolean {
    try {
      const parsed = new URL(uri);
      return ['postgres:', 'postgresql:', 'mongodb:', 'mysql:'].includes(parsed.protocol);
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const environmentValidator = EnvironmentValidator.getInstance();

// Utility functions
export function validateEnvironment(): ValidationResult {
  return environmentValidator.validateEnvironment();
}

export function enforceProductionRequirements(): void {
  environmentValidator.validateRedisProduction();
}

export async function checkRedisHealth(forceCheck: boolean = false): Promise<RedisHealthCheck> {
  return environmentValidator.performRedisHealthCheck(forceCheck);
}

// Auto-validate on import in production
if (process.env.NODE_ENV === 'production') {
  try {
    enforceProductionRequirements();
  } catch (error) {
    console.error('🚨 Environment validation failed:', error);
    process.exit(1);
  }
}
