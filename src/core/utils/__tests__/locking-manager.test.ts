import { LockingManager, withLock, acquireLock, releaseLock, isLocked } from '../locking-manager';

describe('LockingManager', () => {
  let lockingManager: LockingManager;

  beforeEach(() => {
    lockingManager = LockingManager.getInstance();
  });

  afterEach(async () => {
    // Clean up any remaining locks
    await lockingManager.cleanupExpiredLocks();
  });

  describe('acquireLock', () => {
    it('should acquire a lock successfully', async () => {
      const key = 'test-lock-1';
      const options = { ttl: 5000 }; // 5 seconds

      const result = await lockingManager.acquireLock(key, options);

      expect(result.acquired).toBe(true);
      expect(result.lockId).toBeDefined();
      expect(result.expiresAt).toBeDefined();
    });

    it('should not acquire the same lock twice', async () => {
      const key = 'test-lock-2';
      const options = { ttl: 5000 };

      const firstResult = await lockingManager.acquireLock(key, options);
      const secondResult = await lockingManager.acquireLock(key, { ttl: 5000, maxRetries: 0 });

      expect(firstResult.acquired).toBe(true);
      expect(secondResult.acquired).toBe(false);
    });

    it('should acquire lock after TTL expires', async () => {
      const key = 'test-lock-3';
      const options = { ttl: 100 }; // 100ms

      const firstResult = await lockingManager.acquireLock(key, options);
      expect(firstResult.acquired).toBe(true);

      // Wait for TTL to expire
      await new Promise(resolve => setTimeout(resolve, 150));

      const secondResult = await lockingManager.acquireLock(key, options);
      expect(secondResult.acquired).toBe(true);
    });

    it('should handle concurrent lock attempts', async () => {
      const key = 'test-lock-4';
      const options = { ttl: 5000, maxRetries: 0 };

      const promises = Array.from({ length: 5 }, () =>
        lockingManager.acquireLock(key, options)
      );

      const results = await Promise.all(promises);
      const successCount = results.filter(result => result.acquired === true).length;

      expect(successCount).toBe(1); // Only one should succeed
    });
  });

  describe('releaseLock', () => {
    it('should release a lock successfully', async () => {
      const key = 'test-lock-5';
      const options = { ttl: 5000 };

      const lockResult = await lockingManager.acquireLock(key, options);
      expect(lockResult.acquired).toBe(true);

      const released = await lockingManager.releaseLock(key, lockResult.lockId!);
      expect(released).toBe(true);

      // Should be able to acquire again after release
      const reacquired = await lockingManager.acquireLock(key, options);
      expect(reacquired.acquired).toBe(true);
    });

    it('should return false when releasing non-existent lock', async () => {
      const key = 'non-existent-lock';
      const fakeLockId = 'fake-lock-id';

      const released = await lockingManager.releaseLock(key, fakeLockId);

      expect(released).toBe(false);
    });
  });

  describe('isLocked', () => {
    it('should return true for acquired lock', async () => {
      const key = 'test-lock-6';
      const options = { ttl: 5000 };

      const lockResult = await lockingManager.acquireLock(key, options);
      expect(lockResult.acquired).toBe(true);

      const locked = await lockingManager.isLocked(key);
      expect(locked).toBe(true);
    });

    it('should return false for non-existent lock', async () => {
      const key = 'non-existent-lock-2';

      const locked = await lockingManager.isLocked(key);

      expect(locked).toBe(false);
    });

    it('should return false for expired lock', async () => {
      const key = 'test-lock-7';
      const options = { ttl: 100 }; // 100ms

      const lockResult = await lockingManager.acquireLock(key, options);
      expect(lockResult.acquired).toBe(true);

      // Wait for TTL to expire
      await new Promise(resolve => setTimeout(resolve, 150));

      const locked = await lockingManager.isLocked(key);
      expect(locked).toBe(false);
    });
  });

  describe('withLock', () => {
    it('should execute function with lock protection', async () => {
      const key = 'test-lock-8';
      const options = { ttl: 5000 };
      let executed = false;

      const result = await lockingManager.withLock(
        key,
        async () => {
          executed = true;
          return 'success';
        },
        options
      );

      expect(executed).toBe(true);
      expect(result).toBe('success');
    });

    it('should not execute function if lock cannot be acquired', async () => {
      const key = 'test-lock-9';
      const options = { ttl: 5000, maxRetries: 0 };
      let firstExecuted = false;
      let secondExecuted = false;

      // Acquire lock first
      const lockResult = await lockingManager.acquireLock(key, options);
      expect(lockResult.acquired).toBe(true);

      const promises = [
        lockingManager.withLock(key, async () => {
          firstExecuted = true;
          return 'first';
        }, options),
        lockingManager.withLock(key, async () => {
          secondExecuted = true;
          return 'second';
        }, options)
      ];

      const results = await Promise.allSettled(promises);

      // One should succeed, one should fail
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      expect(successCount).toBeLessThanOrEqual(1);
    });

    it('should release lock after function execution', async () => {
      const key = 'test-lock-10';
      const options = { ttl: 5000 };

      await lockingManager.withLock(key, async () => {
        return 'done';
      }, options);

      // Lock should be released, so we can acquire it again
      const canAcquire = await lockingManager.acquireLock(key, options);
      expect(canAcquire.acquired).toBe(true);
    });

    it('should release lock even if function throws error', async () => {
      const key = 'test-lock-11';
      const options = { ttl: 5000 };

      try {
        await lockingManager.withLock(key, async () => {
          throw new Error('Test error');
        }, options);
      } catch (error) {
        expect(error.message).toContain('Test error');
      }

      // Lock should be released even after error
      const canAcquire = await lockingManager.acquireLock(key, options);
      expect(canAcquire.acquired).toBe(true);
    });
  });

  describe('getLockInfo', () => {
    it('should return lock information', async () => {
      const key = 'test-lock-12';
      const options = { ttl: 5000 };

      const lockResult = await lockingManager.acquireLock(key, options);
      expect(lockResult.acquired).toBe(true);

      const info = await lockingManager.getLockInfo(key);

      expect(info).toHaveProperty('lockId');
      expect(info).toHaveProperty('key');
      expect(info).toHaveProperty('acquiredAt');
      expect(info).toHaveProperty('expiresAt');
      expect(info).toHaveProperty('owner');
    });

    it('should return null for non-existent lock', async () => {
      const key = 'non-existent-lock-3';

      const info = await lockingManager.getLockInfo(key);

      expect(info).toBeNull();
    });
  });

  describe('cleanupExpiredLocks', () => {
    it('should clean up expired locks', async () => {
      const key = 'test-lock-13';
      const options = { ttl: 100 }; // 100ms

      const lockResult = await lockingManager.acquireLock(key, options);
      expect(lockResult.acquired).toBe(true);

      // Wait for TTL to expire
      await new Promise(resolve => setTimeout(resolve, 150));

      const cleanedCount = await lockingManager.cleanupExpiredLocks();

      expect(cleanedCount).toBeGreaterThanOrEqual(0);
    });
  });

  describe('singleton behavior', () => {
    it('should return the same instance', () => {
      const instance1 = LockingManager.getInstance();
      const instance2 = LockingManager.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });
});
