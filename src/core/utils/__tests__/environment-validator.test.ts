import { EnvironmentValidator, validateEnvironment, checkRedisHealth, enforceProductionRequirements } from '../environment-validator';

describe('EnvironmentValidator', () => {
  let originalEnv: NodeJS.ProcessEnv;
  let validator: EnvironmentValidator;

  beforeEach(() => {
    originalEnv = { ...process.env };
    validator = EnvironmentValidator.getInstance();
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('validateEnvironment', () => {
    it('should pass validation in development environment', () => {
      process.env.NODE_ENV = 'development';
      delete process.env.UPSTASH_REDIS_REST_URL;

      const result = validateEnvironment();
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should pass validation in test environment', () => {
      process.env.NODE_ENV = 'test';
      delete process.env.UPSTASH_REDIS_REST_URL;

      const result = validateEnvironment();
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should require Redis URL in production environment', () => {
      process.env.NODE_ENV = 'production';
      delete process.env.UPSTASH_REDIS_REST_URL;
      delete process.env.UPSTASH_REDIS_REST_TOKEN;

      const result = validateEnvironment();
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('UPSTASH_REDIS_REST_URL is required in production');
    });

    it('should pass validation in production with Redis URL', () => {
      process.env.NODE_ENV = 'production';
      process.env.UPSTASH_REDIS_REST_URL = 'https://redis.upstash.io';
      process.env.UPSTASH_REDIS_REST_TOKEN = 'token123';
      process.env.DATABASE_URI = 'postgresql://localhost:5432/test';
      process.env.PAYLOAD_SECRET = 'secret123';

      const result = validateEnvironment();
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should require Redis token in production', () => {
      process.env.NODE_ENV = 'production';
      process.env.UPSTASH_REDIS_REST_URL = 'https://redis.upstash.io';
      delete process.env.UPSTASH_REDIS_REST_TOKEN;

      const result = validateEnvironment();
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('UPSTASH_REDIS_REST_TOKEN is required in production');
    });
  });

  describe('checkRedisHealth', () => {
    it('should return health check result', async () => {
      process.env.NODE_ENV = 'development';

      const result = await checkRedisHealth();

      expect(result).toHaveProperty('connected');
      expect(result).toHaveProperty('timestamp');
    });

    it('should handle Redis connection errors gracefully', async () => {
      process.env.NODE_ENV = 'production';
      process.env.UPSTASH_REDIS_REST_URL = 'https://nonexistent-redis-server-12345.upstash.io';
      process.env.UPSTASH_REDIS_REST_TOKEN = 'invalid-token-12345';

      const result = await checkRedisHealth(true); // Force check

      // The test should handle the case where Redis connection fails
      // In a real scenario with invalid credentials, this would fail
      // For now, we just check that the result has the expected structure
      expect(result).toHaveProperty('connected');
      expect(result).toHaveProperty('timestamp');
      if (!result.connected) {
        expect(result.error).toBeDefined();
      }
    });
  });

  describe('enforceProductionRequirements', () => {
    it('should not throw in development environment', () => {
      process.env.NODE_ENV = 'development';
      delete process.env.UPSTASH_REDIS_REST_URL;

      expect(() => enforceProductionRequirements()).not.toThrow();
    });

    it('should throw in production without Redis', () => {
      process.env.NODE_ENV = 'production';
      delete process.env.UPSTASH_REDIS_REST_URL;
      delete process.env.UPSTASH_REDIS_REST_TOKEN;

      expect(() => enforceProductionRequirements()).toThrow();
    });
  });

  describe('instance methods', () => {
    it('should return validation result with config', () => {
      process.env.NODE_ENV = 'test';
      process.env.UPSTASH_REDIS_REST_URL = 'https://redis.upstash.io';
      process.env.UPSTASH_REDIS_REST_TOKEN = 'token123';

      const result = validator.validateEnvironment();

      expect(result).toHaveProperty('valid');
      expect(result).toHaveProperty('errors');
      expect(result).toHaveProperty('warnings');
      expect(result).toHaveProperty('config');
      expect(result.config).toHaveProperty('nodeEnv', 'test');
      expect(result.config).toHaveProperty('redisUrl');
    });
  });
});
