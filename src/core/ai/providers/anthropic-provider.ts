/**
 * Anthropic Provider Implementation
 * Handles Anthropic Claude API integration with BYOK support
 */

import Anthropic from '@anthropic-ai/sdk';
import { 
  AIProvider, 
  GenerationOptions, 
  GenerationResult, 
  AnthropicConfig,
  AIProviderError,
  RateLimitError,
  InvalidApiKeyError
} from '../types';

export class AnthropicProvider implements AIProvider {
  name = 'anthropic';
  models = [
    'claude-3-opus-20240229',
    'claude-3-sonnet-20240229',
    'claude-3-haiku-20240307',
    'claude-2.1',
    'claude-2.0'
  ];

  private defaultClient?: Anthropic;
  private clientCache = new Map<string, Anthropic>();

  constructor(private config?: AnthropicConfig) {
    if (config?.apiKey) {
      this.defaultClient = new Anthropic({
        apiKey: config.apiKey,
        baseURL: config.baseURL
      });
    }
  }

  private getClient(apiKey?: string): Anthropic {
    if (apiKey) {
      // Use BYOK - cache clients by API key
      if (!this.clientCache.has(apiKey)) {
        this.clientCache.set(apiKey, new Anthropic({ apiKey }));
      }
      return this.clientCache.get(apiKey)!;
    }

    if (!this.defaultClient) {
      throw new InvalidApiKeyError('anthropic');
    }

    return this.defaultClient;
  }

  async generate(prompt: string, options: GenerationOptions): Promise<GenerationResult> {
    const client = this.getClient(options.apiKey);
    const startTime = Date.now();

    try {
      const messages: Anthropic.MessageParam[] = [{
        role: 'user',
        content: prompt
      }];

      const response = await client.messages.create({
        model: options.model,
        messages,
        max_tokens: options.maxTokens ?? 2000,
        temperature: options.temperature ?? 0.7,
        system: options.systemPrompt,
        stop_sequences: options.stopSequences
      });

      const content = response.content[0];
      if (content.type !== 'text' || !content.text) {
        throw new AIProviderError(
          'No text content generated',
          this.name,
          options.model,
          'NO_CONTENT'
        );
      }

      return {
        content: content.text,
        usage: {
          promptTokens: response.usage.input_tokens,
          completionTokens: response.usage.output_tokens,
          totalTokens: response.usage.input_tokens + response.usage.output_tokens
        },
        model: options.model,
        provider: this.name,
        cost: this.calculateCost(
          response.usage.input_tokens + response.usage.output_tokens, 
          options.model
        ),
        finishReason: this.mapFinishReason(response.stop_reason)
      };

    } catch (error: any) {
      const duration = Date.now() - startTime;
      
      if (error.status === 429) {
        const retryAfter = error.headers?.['retry-after'] 
          ? parseInt(error.headers['retry-after']) 
          : undefined;
        throw new RateLimitError(this.name, options.model, retryAfter);
      }

      if (error.status === 401) {
        throw new InvalidApiKeyError(this.name);
      }

      throw new AIProviderError(
        error.message || 'Unknown error',
        this.name,
        options.model,
        error.code
      );
    }
  }

  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const client = new Anthropic({ apiKey });
      // Try a minimal request to validate the key
      await client.messages.create({
        model: 'claude-3-haiku-20240307',
        messages: [{ role: 'user', content: 'Hi' }],
        max_tokens: 1
      });
      return true;
    } catch (error: any) {
      if (error.status === 401) {
        return false;
      }
      // Other errors might be network issues, so we assume key is valid
      return true;
    }
  }

  async estimateCost(prompt: string, options: GenerationOptions): Promise<number> {
    // Rough estimation: 1 token ≈ 4 characters for English text
    const estimatedPromptTokens = Math.ceil(prompt.length / 4);
    const estimatedCompletionTokens = options.maxTokens ?? 1000;
    const totalTokens = estimatedPromptTokens + estimatedCompletionTokens;
    
    return this.calculateCost(totalTokens, options.model);
  }

  private calculateCost(tokens: number, model: string): number {
    // Anthropic pricing (as of 2024) - per 1K tokens
    const pricing: Record<string, { input: number; output: number }> = {
      'claude-3-opus-20240229': { input: 0.015, output: 0.075 },
      'claude-3-sonnet-20240229': { input: 0.003, output: 0.015 },
      'claude-3-haiku-20240307': { input: 0.00025, output: 0.00125 },
      'claude-2.1': { input: 0.008, output: 0.024 },
      'claude-2.0': { input: 0.008, output: 0.024 }
    };

    const modelPricing = pricing[model] || pricing['claude-3-haiku-20240307'];
    // Simplified: assume 50/50 split between input and output tokens
    const avgPrice = (modelPricing.input + modelPricing.output) / 2;
    
    return (tokens / 1000) * avgPrice;
  }

  private mapFinishReason(reason: string | null): GenerationResult['finishReason'] {
    switch (reason) {
      case 'end_turn':
        return 'stop';
      case 'max_tokens':
        return 'length';
      case 'stop_sequence':
        return 'stop';
      default:
        return 'error';
    }
  }
}
