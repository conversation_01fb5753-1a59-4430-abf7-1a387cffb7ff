/**
 * OpenAI Provider Implementation
 * Handles OpenAI API integration with BYOK support
 */

import OpenAI from 'openai';
import { 
  AIProvider, 
  GenerationOptions, 
  GenerationResult, 
  OpenAIConfig,
  AIProviderError,
  RateLimitError,
  InvalidApiKeyError
} from '../types';

export class OpenAIProvider implements AIProvider {
  name = 'openai';
  models = [
    'gpt-4',
    'gpt-4-turbo',
    'gpt-4-turbo-preview',
    'gpt-3.5-turbo',
    'gpt-3.5-turbo-16k'
  ];

  private defaultClient?: OpenAI;
  private clientCache = new Map<string, OpenAI>();

  constructor(private config?: OpenAIConfig) {
    if (config?.apiKey) {
      this.defaultClient = new OpenAI({
        apiKey: config.apiKey,
        organization: config.organization,
        baseURL: config.baseURL
      });
    }
  }

  private getClient(apiKey?: string): OpenAI {
    if (apiKey) {
      // Use BYOK - cache clients by API key
      if (!this.clientCache.has(apiKey)) {
        this.clientCache.set(apiKey, new OpenAI({ apiKey }));
      }
      return this.clientCache.get(apiKey)!;
    }

    if (!this.defaultClient) {
      throw new InvalidApiKeyError('openai');
    }

    return this.defaultClient;
  }

  async generate(prompt: string, options: GenerationOptions): Promise<GenerationResult> {
    const client = this.getClient(options.apiKey);
    const startTime = Date.now();

    try {
      const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [];
      
      if (options.systemPrompt) {
        messages.push({
          role: 'system',
          content: options.systemPrompt
        });
      }

      messages.push({
        role: 'user',
        content: prompt
      });

      const response = await client.chat.completions.create({
        model: options.model,
        messages,
        temperature: options.temperature ?? 0.7,
        max_tokens: options.maxTokens ?? 2000,
        stop: options.stopSequences,
        stream: false
      });

      const choice = response.choices[0];
      if (!choice?.message?.content) {
        throw new AIProviderError(
          'No content generated',
          this.name,
          options.model,
          'NO_CONTENT'
        );
      }

      const usage = response.usage;
      if (!usage) {
        throw new AIProviderError(
          'No usage information returned',
          this.name,
          options.model,
          'NO_USAGE'
        );
      }

      return {
        content: choice.message.content,
        usage: {
          promptTokens: usage.prompt_tokens,
          completionTokens: usage.completion_tokens,
          totalTokens: usage.total_tokens
        },
        model: options.model,
        provider: this.name,
        cost: this.calculateCost(usage.total_tokens, options.model),
        finishReason: this.mapFinishReason(choice.finish_reason)
      };

    } catch (error: any) {
      const duration = Date.now() - startTime;
      
      if (error.status === 429) {
        const retryAfter = error.headers?.['retry-after'] 
          ? parseInt(error.headers['retry-after']) 
          : undefined;
        throw new RateLimitError(this.name, options.model, retryAfter);
      }

      if (error.status === 401) {
        throw new InvalidApiKeyError(this.name);
      }

      throw new AIProviderError(
        error.message || 'Unknown error',
        this.name,
        options.model,
        error.code
      );
    }
  }

  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const client = new OpenAI({ apiKey });
      await client.models.list();
      return true;
    } catch (error: any) {
      if (error.status === 401) {
        return false;
      }
      // Other errors might be network issues, so we assume key is valid
      return true;
    }
  }

  async estimateCost(prompt: string, options: GenerationOptions): Promise<number> {
    // Rough estimation: 1 token ≈ 4 characters for English text
    const estimatedPromptTokens = Math.ceil(prompt.length / 4);
    const estimatedCompletionTokens = options.maxTokens ?? 1000;
    const totalTokens = estimatedPromptTokens + estimatedCompletionTokens;
    
    return this.calculateCost(totalTokens, options.model);
  }

  private calculateCost(tokens: number, model: string): number {
    // OpenAI pricing (as of 2024) - per 1K tokens
    const pricing: Record<string, { input: number; output: number }> = {
      'gpt-4': { input: 0.03, output: 0.06 },
      'gpt-4-turbo': { input: 0.01, output: 0.03 },
      'gpt-4-turbo-preview': { input: 0.01, output: 0.03 },
      'gpt-3.5-turbo': { input: 0.0015, output: 0.002 },
      'gpt-3.5-turbo-16k': { input: 0.003, output: 0.004 }
    };

    const modelPricing = pricing[model] || pricing['gpt-3.5-turbo'];
    // Simplified: assume 50/50 split between input and output tokens
    const avgPrice = (modelPricing.input + modelPricing.output) / 2;
    
    return (tokens / 1000) * avgPrice;
  }

  private mapFinishReason(reason: string | null): GenerationResult['finishReason'] {
    switch (reason) {
      case 'stop':
        return 'stop';
      case 'length':
        return 'length';
      case 'content_filter':
        return 'content_filter';
      default:
        return 'error';
    }
  }
}
