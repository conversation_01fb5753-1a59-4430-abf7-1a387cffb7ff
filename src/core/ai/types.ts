/**
 * AI Model Manager Types
 * Simplified AI integration for the new system
 */

// Core AI Provider Interface
export interface AIProvider {
  name: string;
  models: string[];
  generate(prompt: string, options: GenerationOptions): Promise<GenerationResult>;
  validateApiKey(apiKey: string): Promise<boolean>;
  estimateCost(prompt: string, options: GenerationOptions): Promise<number>;
}

// Generation Options
export interface GenerationOptions {
  model: string;
  apiKey?: string; // BYOK support
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
  stopSequences?: string[];
  stream?: boolean;
}

// Generation Result
export interface GenerationResult {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  provider: string;
  cost?: number;
  finishReason: 'stop' | 'length' | 'content_filter' | 'error';
}

// AI Model Configuration
export interface AIModelConfig {
  provider: string;
  model: string;
  defaultOptions: Partial<GenerationOptions>;
  costPerToken: number;
  maxTokens: number;
  description: string;
  capabilities: ModelCapability[];
}

export enum ModelCapability {
  TEXT_GENERATION = 'text_generation',
  CODE_GENERATION = 'code_generation',
  ANALYSIS = 'analysis',
  CREATIVE_WRITING = 'creative_writing',
  TECHNICAL_WRITING = 'technical_writing',
  SEO_OPTIMIZATION = 'seo_optimization'
}

// Provider-specific types
export interface OpenAIConfig {
  apiKey: string;
  organization?: string;
  baseURL?: string;
}

export interface AnthropicConfig {
  apiKey: string;
  baseURL?: string;
}

export interface GoogleConfig {
  apiKey: string;
  projectId?: string;
}

// Model Manager Configuration
export interface AIModelManagerConfig {
  defaultProvider: string;
  defaultModel: string;
  providers: {
    openai?: OpenAIConfig;
    anthropic?: AnthropicConfig;
    google?: GoogleConfig;
  };
  costTracking: boolean;
  rateLimiting: {
    enabled: boolean;
    requestsPerMinute: number;
    tokensPerMinute: number;
  };
}

// Error types
export class AIProviderError extends Error {
  constructor(
    message: string,
    public provider: string,
    public model: string,
    public code?: string
  ) {
    super(message);
    this.name = 'AIProviderError';
  }
}

export class RateLimitError extends AIProviderError {
  constructor(provider: string, model: string, retryAfter?: number) {
    super(`Rate limit exceeded for ${provider}/${model}`, provider, model, 'RATE_LIMIT');
    this.retryAfter = retryAfter;
  }
  
  retryAfter?: number;
}

export class InvalidApiKeyError extends AIProviderError {
  constructor(provider: string) {
    super(`Invalid API key for ${provider}`, provider, '', 'INVALID_API_KEY');
  }
}

// Usage tracking
export interface UsageStats {
  provider: string;
  model: string;
  totalRequests: number;
  totalTokens: number;
  totalCost: number;
  averageResponseTime: number;
  errorRate: number;
  lastUsed: string;
}

// Model selection criteria
export interface ModelSelectionCriteria {
  task: 'content_generation' | 'keyword_research' | 'seo_optimization' | 'analysis';
  contentLength: 'short' | 'medium' | 'long';
  quality: 'fast' | 'balanced' | 'high';
  budget: 'low' | 'medium' | 'high';
  userPreference?: string; // User can override
}

export interface ModelRecommendation {
  provider: string;
  model: string;
  confidence: number;
  reasoning: string;
  estimatedCost: number;
  estimatedTime: number;
}
