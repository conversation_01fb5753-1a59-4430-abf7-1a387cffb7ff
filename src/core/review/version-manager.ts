/**
 * Artifact Version Management System
 * Handles versioning, history tracking, and comparison of artifacts
 */

import { v4 as uuidv4 } from 'uuid';
import { ArtifactVersion, ReviewAction } from './types';
import { errorHandler, ErrorType, ErrorSeverity } from '../utils/error-handler';

export interface VersionDiff {
  type: 'added' | 'removed' | 'modified';
  path: string;
  oldValue?: any;
  newValue?: any;
  description: string;
}

export interface VersionComparison {
  versionId1: string;
  versionId2: string;
  differences: VersionDiff[];
  summary: {
    totalChanges: number;
    addedFields: number;
    removedFields: number;
    modifiedFields: number;
  };
  similarity: number; // 0-1 score
}

export interface VersionMetrics {
  totalVersions: number;
  activeVersion: number;
  averageChangeSize: number;
  mostActiveContributor: string;
  changeFrequency: number; // changes per day
}

export class ArtifactVersionManager {
  private versions = new Map<string, ArtifactVersion>();
  private artifactVersions = new Map<string, string[]>(); // artifactId -> versionIds[]
  private activeVersions = new Map<string, string>(); // artifactId -> activeVersionId

  constructor() {
    console.log('🔄 Artifact Version Manager initialized');
  }

  /**
   * Create a new version of an artifact
   */
  async createVersion(
    artifactId: string,
    content: any,
    changes: string,
    createdBy: string = 'system',
    metadata: Record<string, any> = {}
  ): Promise<string> {
    try {
      const versionId = uuidv4();
      const now = new Date().toISOString();

      // Get current version number
      const existingVersions = this.artifactVersions.get(artifactId) || [];
      const version = existingVersions.length + 1;

      // Calculate content hash for deduplication
      const contentHash = this.calculateContentHash(content);

      // Check if this exact content already exists
      const existingVersion = existingVersions.find(vId => {
        const v = this.versions.get(vId);
        return v && v.contentHash === contentHash;
      });

      if (existingVersion) {
        console.log(`⚠️ Content unchanged, reusing version ${existingVersion}`);
        return existingVersion;
      }

      // Get parent version (current active version)
      const parentVersionId = this.activeVersions.get(artifactId);

      const artifactVersion: ArtifactVersion = {
        id: versionId,
        artifactId,
        version,
        content,
        contentHash,
        createdAt: now,
        createdBy,
        changeDescription: changes,
        metadata,
        parentVersionId,
        isActive: true
      };

      // Deactivate previous active version
      if (parentVersionId) {
        const parentVersion = this.versions.get(parentVersionId);
        if (parentVersion) {
          parentVersion.isActive = false;
          this.versions.set(parentVersionId, parentVersion);
        }
      }

      // Store new version
      this.versions.set(versionId, artifactVersion);

      // Update artifact versions list
      const updatedVersions = [...existingVersions, versionId];
      this.artifactVersions.set(artifactId, updatedVersions);

      // Set as active version
      this.activeVersions.set(artifactId, versionId);

      console.log(`✅ Created version ${version} for artifact ${artifactId}`);
      return versionId;

    } catch (error) {
      const standardError = errorHandler.handleError(error, {
        operation: 'createVersion',
        artifactId,
        createdBy
      });
      throw standardError;
    }
  }

  /**
   * Get version history for an artifact
   */
  async getVersionHistory(artifactId: string): Promise<ArtifactVersion[]> {
    try {
      const versionIds = this.artifactVersions.get(artifactId) || [];
      const versions = versionIds
        .map(id => this.versions.get(id))
        .filter((v): v is ArtifactVersion => v !== undefined)
        .sort((a, b) => b.version - a.version); // Latest first

      return versions;

    } catch (error) {
      const standardError = errorHandler.handleError(error, {
        operation: 'getVersionHistory',
        artifactId
      });
      throw standardError;
    }
  }

  /**
   * Compare two versions of an artifact
   */
  async compareVersions(versionId1: string, versionId2: string): Promise<VersionComparison> {
    try {
      const version1 = this.versions.get(versionId1);
      const version2 = this.versions.get(versionId2);

      if (!version1 || !version2) {
        throw new Error(`Version not found: ${!version1 ? versionId1 : versionId2}`);
      }

      if (version1.artifactId !== version2.artifactId) {
        throw new Error('Cannot compare versions from different artifacts');
      }

      const differences = this.calculateDifferences(version1.content, version2.content);
      const summary = this.summarizeDifferences(differences);
      const similarity = this.calculateSimilarity(version1.content, version2.content);

      return {
        versionId1,
        versionId2,
        differences,
        summary,
        similarity
      };

    } catch (error) {
      const standardError = errorHandler.handleError(error, {
        operation: 'compareVersions',
        versionId1,
        versionId2
      });
      throw standardError;
    }
  }

  /**
   * Revert artifact to a specific version
   */
  async revertToVersion(artifactId: string, versionId: string, revertedBy: string = 'system'): Promise<string> {
    try {
      const targetVersion = this.versions.get(versionId);
      if (!targetVersion || targetVersion.artifactId !== artifactId) {
        throw new Error(`Version ${versionId} not found for artifact ${artifactId}`);
      }

      // Create a new version with the reverted content
      const newVersionId = await this.createVersion(
        artifactId,
        targetVersion.content,
        `Reverted to version ${targetVersion.version}`,
        revertedBy,
        {
          revertedFrom: versionId,
          revertedFromVersion: targetVersion.version,
          action: ReviewAction.CONTENT_EDITED
        }
      );

      console.log(`🔄 Reverted artifact ${artifactId} to version ${targetVersion.version}`);
      return newVersionId;

    } catch (error) {
      const standardError = errorHandler.handleError(error, {
        operation: 'revertToVersion',
        artifactId,
        versionId,
        revertedBy
      });
      throw standardError;
    }
  }

  /**
   * Get the active version of an artifact
   */
  getActiveVersion(artifactId: string): ArtifactVersion | null {
    const activeVersionId = this.activeVersions.get(artifactId);
    if (!activeVersionId) {
      return null;
    }
    return this.versions.get(activeVersionId) || null;
  }

  /**
   * Get a specific version by ID
   */
  getVersion(versionId: string): ArtifactVersion | null {
    return this.versions.get(versionId) || null;
  }

  /**
   * Get version metrics for an artifact
   */
  getVersionMetrics(artifactId: string): VersionMetrics {
    const versionIds = this.artifactVersions.get(artifactId) || [];
    const versions = versionIds
      .map(id => this.versions.get(id))
      .filter((v): v is ArtifactVersion => v !== undefined);

    if (versions.length === 0) {
      return {
        totalVersions: 0,
        activeVersion: 0,
        averageChangeSize: 0,
        mostActiveContributor: '',
        changeFrequency: 0
      };
    }

    // Calculate metrics
    const activeVersion = this.getActiveVersion(artifactId);
    const contributors = new Map<string, number>();
    let totalChangeSize = 0;

    versions.forEach(version => {
      // Count contributions
      const count = contributors.get(version.createdBy) || 0;
      contributors.set(version.createdBy, count + 1);

      // Calculate change size (rough estimate based on content size)
      totalChangeSize += JSON.stringify(version.content).length;
    });

    const mostActiveContributor = Array.from(contributors.entries())
      .sort((a, b) => b[1] - a[1])[0]?.[0] || '';

    // Calculate change frequency (changes per day)
    const firstVersion = versions[versions.length - 1];
    const lastVersion = versions[0];
    const daysDiff = (new Date(lastVersion.createdAt).getTime() - new Date(firstVersion.createdAt).getTime()) / (1000 * 60 * 60 * 24);
    const changeFrequency = daysDiff > 0 ? versions.length / daysDiff : 0;

    return {
      totalVersions: versions.length,
      activeVersion: activeVersion?.version || 0,
      averageChangeSize: totalChangeSize / versions.length,
      mostActiveContributor,
      changeFrequency
    };
  }

  /**
   * Delete old versions (keep only recent ones)
   */
  async cleanupOldVersions(artifactId: string, keepCount: number = 10): Promise<number> {
    try {
      const versionIds = this.artifactVersions.get(artifactId) || [];
      if (versionIds.length <= keepCount) {
        return 0;
      }

      // Sort by version number and keep the latest ones
      const versions = versionIds
        .map(id => ({ id, version: this.versions.get(id) }))
        .filter(v => v.version !== undefined)
        .sort((a, b) => b.version!.version - a.version!.version);

      const toDelete = versions.slice(keepCount);
      let deletedCount = 0;

      for (const { id } of toDelete) {
        const version = this.versions.get(id);
        if (version && !version.isActive) {
          this.versions.delete(id);
          deletedCount++;
        }
      }

      // Update artifact versions list
      const remainingIds = versionIds.filter(id => this.versions.has(id));
      this.artifactVersions.set(artifactId, remainingIds);

      console.log(`🧹 Cleaned up ${deletedCount} old versions for artifact ${artifactId}`);
      return deletedCount;

    } catch (error) {
      const standardError = errorHandler.handleError(error, {
        operation: 'cleanupOldVersions',
        artifactId,
        keepCount
      });
      throw standardError;
    }
  }

  /**
   * Calculate content hash for deduplication
   */
  private calculateContentHash(content: any): string {
    const contentString = JSON.stringify(content, Object.keys(content).sort());
    let hash = 0;
    for (let i = 0; i < contentString.length; i++) {
      const char = contentString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  /**
   * Calculate differences between two content objects
   */
  private calculateDifferences(content1: any, content2: any, path: string = ''): VersionDiff[] {
    const differences: VersionDiff[] = [];

    // Handle primitive types
    if (typeof content1 !== 'object' || typeof content2 !== 'object') {
      if (content1 !== content2) {
        differences.push({
          type: 'modified',
          path,
          oldValue: content1,
          newValue: content2,
          description: `Changed from "${content1}" to "${content2}"`
        });
      }
      return differences;
    }

    // Handle null values
    if (content1 === null || content2 === null) {
      if (content1 !== content2) {
        differences.push({
          type: content1 === null ? 'added' : 'removed',
          path,
          oldValue: content1,
          newValue: content2,
          description: content1 === null ? 'Added value' : 'Removed value'
        });
      }
      return differences;
    }

    // Handle objects and arrays
    const keys1 = Object.keys(content1);
    const keys2 = Object.keys(content2);
    const allKeys = new Set([...keys1, ...keys2]);

    for (const key of allKeys) {
      const newPath = path ? `${path}.${key}` : key;
      const hasKey1 = keys1.includes(key);
      const hasKey2 = keys2.includes(key);

      if (!hasKey1 && hasKey2) {
        differences.push({
          type: 'added',
          path: newPath,
          newValue: content2[key],
          description: `Added field "${key}"`
        });
      } else if (hasKey1 && !hasKey2) {
        differences.push({
          type: 'removed',
          path: newPath,
          oldValue: content1[key],
          description: `Removed field "${key}"`
        });
      } else if (hasKey1 && hasKey2) {
        const subDiffs = this.calculateDifferences(content1[key], content2[key], newPath);
        differences.push(...subDiffs);
      }
    }

    return differences;
  }

  /**
   * Summarize differences for quick overview
   */
  private summarizeDifferences(differences: VersionDiff[]) {
    return {
      totalChanges: differences.length,
      addedFields: differences.filter(d => d.type === 'added').length,
      removedFields: differences.filter(d => d.type === 'removed').length,
      modifiedFields: differences.filter(d => d.type === 'modified').length
    };
  }

  /**
   * Calculate similarity score between two content objects
   */
  private calculateSimilarity(content1: any, content2: any): number {
    const str1 = JSON.stringify(content1);
    const str2 = JSON.stringify(content2);

    if (str1 === str2) return 1;
    if (str1.length === 0 && str2.length === 0) return 1;
    if (str1.length === 0 || str2.length === 0) return 0;

    // Simple similarity calculation based on common characters
    const maxLength = Math.max(str1.length, str2.length);
    const minLength = Math.min(str1.length, str2.length);
    
    let commonChars = 0;
    for (let i = 0; i < minLength; i++) {
      if (str1[i] === str2[i]) {
        commonChars++;
      }
    }

    return commonChars / maxLength;
  }
}

// Export singleton instance
export const artifactVersionManager = new ArtifactVersionManager();
