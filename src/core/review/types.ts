/**
 * Simplified Review System Types
 * Basic approve/reject with edits functionality
 */

// Simplified Review System Interface (as per critical architecture changes)
export interface ISimplifiedReviewSystem {
  // Just these 3 methods initially
  createReview(content: string, options: ReviewOptions): Promise<ReviewLink>;
  getReview(reviewId: string): Promise<ReviewData>;
  submitReview(reviewId: string, decision: 'approve' | 'reject', edits?: string): Promise<void>;
}

// Review Options
export interface ReviewOptions {
  contentId: string;
  executionId: string;
  stepId: string;
  type: ReviewType;
  instructions?: string;
  deadline?: string;
  reviewers?: string[];
  metadata?: Record<string, any>;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  requiredApprovals?: number;
  allowParallelReview?: boolean;
  escalationRules?: EscalationRule[];
}

// Escalation Rules
export interface EscalationRule {
  id: string;
  triggerAfterHours: number;
  escalateTo: string[];
  notificationMessage?: string;
  autoApprove?: boolean;
}

export enum ReviewType {
  APPROVAL = 'approval',
  EDITING = 'editing',
  FEEDBACK = 'feedback'
}

// Review Link (for sharing with reviewers)
export interface ReviewLink {
  reviewId: string;
  url: string;
  expiresAt?: string;
  accessToken?: string;
}

// Review Data (what reviewers see)
export interface ReviewData {
  id: string;
  content: ReviewContent;
  instructions: string;
  type: ReviewType;
  status: ReviewStatus;
  deadline?: string;
  createdAt: string;
  metadata?: Record<string, any>;
}

export interface ReviewContent {
  id: string;
  type: string;
  title: string;
  data: any; // The actual content to review
  context?: {
    workflowName?: string;
    stepName?: string;
    previousContent?: any;
  };
}

export enum ReviewStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  EXPIRED = 'expired'
}

// Review Submission
export interface ReviewSubmission {
  reviewId: string;
  decision: ReviewDecision;
  feedback?: string;
  edits?: ReviewEdits;
  reviewer: string;
  submittedAt: string;
}

export enum ReviewDecision {
  APPROVE = 'approve',
  REJECT = 'reject',
  NEEDS_CHANGES = 'needs_changes'
}

export interface ReviewEdits {
  type: 'text' | 'json' | 'structured';
  changes: any; // Depends on content type
  comments?: ReviewComment[];
}

export interface ReviewComment {
  id: string;
  text: string;
  position?: {
    line?: number;
    column?: number;
    selection?: string;
  };
  timestamp: string;
}

// Notification Types
export interface ReviewNotification {
  type: NotificationType;
  reviewId: string;
  recipient: string;
  subject: string;
  message: string;
  link: string;
  scheduledAt?: string;
}

export enum NotificationType {
  REVIEW_REQUESTED = 'review_requested',
  REVIEW_REMINDER = 'review_reminder',
  REVIEW_COMPLETED = 'review_completed',
  REVIEW_EXPIRED = 'review_expired'
}

// Review Configuration
export interface ReviewConfig {
  defaultTimeout: number; // hours
  reminderIntervals: number[]; // hours before deadline
  autoExpire: boolean;
  allowAnonymous: boolean;
  requireComments: boolean;
  emailNotifications: boolean;
  webhookUrl?: string;
}

// Review Analytics
export interface ReviewAnalytics {
  totalReviews: number;
  averageResponseTime: number; // hours
  approvalRate: number; // percentage
  reviewerStats: Record<string, ReviewerStats>;
  contentTypeStats: Record<string, ContentTypeStats>;
}

export interface ReviewerStats {
  totalReviews: number;
  averageResponseTime: number;
  approvalRate: number;
  lastActivity: string;
}

export interface ContentTypeStats {
  totalReviews: number;
  averageResponseTime: number;
  approvalRate: number;
  commonIssues: string[];
}

// Error Types
export class ReviewError extends Error {
  constructor(
    message: string,
    public code: string,
    public reviewId?: string
  ) {
    super(message);
    this.name = 'ReviewError';
  }
}

export class ReviewNotFoundError extends ReviewError {
  constructor(reviewId: string) {
    super(`Review ${reviewId} not found`, 'REVIEW_NOT_FOUND', reviewId);
  }
}

export class ReviewExpiredError extends ReviewError {
  constructor(reviewId: string) {
    super(`Review ${reviewId} has expired`, 'REVIEW_EXPIRED', reviewId);
  }
}

export class ReviewAlreadyCompletedError extends ReviewError {
  constructor(reviewId: string) {
    super(`Review ${reviewId} is already completed`, 'REVIEW_ALREADY_COMPLETED', reviewId);
  }
}

// Review Templates
export interface ReviewTemplate {
  id: string;
  name: string;
  contentType: string;
  instructions: string;
  questions?: ReviewQuestion[];
  criteria?: ReviewCriterion[];
}

export interface ReviewQuestion {
  id: string;
  text: string;
  type: 'text' | 'rating' | 'boolean' | 'choice';
  required: boolean;
  options?: string[]; // for choice type
  maxRating?: number; // for rating type
}

export interface ReviewCriterion {
  id: string;
  name: string;
  description: string;
  weight: number; // 0-1
  type: 'boolean' | 'rating' | 'text';
}

// Review UI Components Data
export interface ReviewUIData {
  review: ReviewData;
  content: ReviewContent;
  template?: ReviewTemplate;
  config: ReviewConfig;
  canEdit: boolean;
  canSubmit: boolean;
  timeRemaining?: number; // minutes
}

// Bulk Review Operations
export interface BulkReviewOperation {
  id: string;
  type: 'approve_all' | 'reject_all' | 'assign_reviewer';
  reviewIds: string[];
  parameters: Record<string, any>;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  createdAt: string;
  completedAt?: string;
  error?: string;
}

// Enhanced Reviewer Management
export interface Reviewer {
  id: string;
  name: string;
  email: string;
  role: 'editor' | 'approver' | 'specialist' | 'admin';
  expertise: string[];
  availability: ReviewerAvailability;
  workload: number; // current number of pending reviews
  maxWorkload: number;
  averageResponseTime: number; // in hours
  approvalRate: number;
  isActive: boolean;
}

export interface ReviewerAvailability {
  status: 'available' | 'busy' | 'away' | 'offline';
  availableUntil?: string;
  timezone: string;
  workingHours: {
    start: string; // HH:mm format
    end: string;
    days: number[]; // 0-6, Sunday to Saturday
  };
}

// Multi-reviewer Assignment
export interface ReviewAssignment {
  id: string;
  reviewId: string;
  reviewerId: string;
  assignedAt: string;
  assignedBy: string;
  role: 'primary' | 'secondary' | 'specialist';
  status: 'pending' | 'accepted' | 'declined' | 'completed' | 'expired';
  deadline: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  estimatedTime: number; // in minutes
  actualTime?: number;
  completedAt?: string;
  decision?: ReviewDecision;
  feedback?: string;
}

// Review History Entry for tracking changes
export interface ReviewHistoryEntry {
  id: string;
  reviewId: string;
  action: ReviewAction;
  performedBy: string;
  performedAt: string;
  details: Record<string, any>;
  previousValue?: any;
  newValue?: any;
  comment?: string;
}

export enum ReviewAction {
  CREATED = 'created',
  ASSIGNED = 'assigned',
  REASSIGNED = 'reassigned',
  STARTED = 'started',
  CONTENT_EDITED = 'content_edited',
  COMMENT_ADDED = 'comment_added',
  DECISION_SUBMITTED = 'decision_submitted',
  DEADLINE_EXTENDED = 'deadline_extended',
  PRIORITY_CHANGED = 'priority_changed',
  ESCALATED = 'escalated',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled'
}

// Artifact Version Management
export interface ArtifactVersion {
  id: string;
  artifactId: string;
  version: number;
  content: any;
  contentHash: string;
  createdAt: string;
  createdBy: string;
  changeDescription: string;
  tags?: string[];
  metadata?: Record<string, any>;
  parentVersionId?: string;
  isActive: boolean;
}

// Review Priority enum for better type safety
export enum ReviewPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
  CRITICAL = 'critical'
}

// Enhanced Review interface with all new fields
export interface EnhancedReview extends ReviewData {
  reviewerAssignments: string[]; // Multiple reviewers support
  reviewHistory: ReviewHistoryEntry[]; // Track all changes
  priority: ReviewPriority; // Review urgency
  estimatedTime: number; // Estimated review time in minutes
  deadline?: string; // Review deadline
  tags?: string[]; // Categorization tags
  parentReviewId?: string; // For review chains
  artifactVersion: number; // Version of artifact being reviewed
  actualTime?: number; // Actual time spent on review
  complexity: 'simple' | 'medium' | 'complex'; // Review complexity
  requiredExpertise?: string[]; // Required reviewer expertise
  autoEscalation: boolean; // Whether to auto-escalate on deadline
  collaborativeMode: boolean; // Allow multiple reviewers to work together
}
