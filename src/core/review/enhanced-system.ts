/**
 * Enhanced Review System
 * Multi-reviewer support with advanced features
 */

import { v4 as uuidv4 } from 'uuid';
import {
  ISimplifiedReviewSystem,
  ReviewOptions,
  ReviewLink,
  ReviewData,
  ReviewDecision,
  Reviewer,
  ReviewAssignment,
  EscalationRule,
  ReviewNotification,
  NotificationType,
  ReviewerAvailability
} from './types';
import { ISimplifiedStateStore } from '../state/types';
import { SimplifiedReviewSystem } from './system';

export interface IEnhancedReviewSystem extends ISimplifiedReviewSystem {
  // Reviewer management
  addReviewer(reviewer: Reviewer): Promise<void>;
  updateReviewer(reviewerId: string, updates: Partial<Reviewer>): Promise<void>;
  getReviewer(reviewerId: string): Promise<Reviewer | null>;
  listReviewers(filters?: ReviewerFilters): Promise<Reviewer[]>;
  
  // Multi-reviewer workflows
  assignReviewers(reviewId: string, assignments: ReviewAssignment[]): Promise<void>;
  getReviewAssignments(reviewId: string): Promise<ReviewAssignment[]>;
  updateAssignment(assignmentId: string, updates: Partial<ReviewAssignment>): Promise<void>;
  
  // Advanced review operations
  escalateReview(reviewId: string, reason: string): Promise<void>;
  sendReminder(reviewId: string, reviewerId?: string): Promise<void>;
  getReviewAnalytics(timeRange?: { start: string; end: string }): Promise<ReviewAnalytics>;
}

export interface ReviewerFilters {
  role?: string;
  expertise?: string[];
  availability?: 'available' | 'busy' | 'away';
  maxWorkload?: number;
}

export interface ReviewAnalytics {
  totalReviews: number;
  averageResponseTime: number;
  approvalRate: number;
  reviewerPerformance: Record<string, ReviewerPerformance>;
  contentTypeStats: Record<string, ContentTypeStats>;
  escalationRate: number;
}

export interface ReviewerPerformance {
  totalReviews: number;
  averageResponseTime: number;
  approvalRate: number;
  escalationCount: number;
  qualityScore: number;
}

export interface ContentTypeStats {
  totalReviews: number;
  averageResponseTime: number;
  approvalRate: number;
  commonIssues: string[];
}

export class EnhancedReviewSystem extends SimplifiedReviewSystem implements IEnhancedReviewSystem {
  private reviewers = new Map<string, Reviewer>();
  private assignments = new Map<string, ReviewAssignment>();
  private escalationRules = new Map<string, EscalationRule[]>();

  constructor(
    stateStore: ISimplifiedStateStore,
    baseUrl: string = 'http://localhost:3000'
  ) {
    super(stateStore, baseUrl);
    this.initializeDefaultReviewers();
  }

  private initializeDefaultReviewers(): void {
    // Add a default reviewer for testing
    const defaultReviewer: Reviewer = {
      id: 'default-reviewer',
      name: 'Default Reviewer',
      email: '<EMAIL>',
      role: 'editor',
      expertise: ['content', 'seo', 'marketing'],
      availability: {
        status: 'available',
        timezone: 'UTC',
        workingHours: {
          start: '09:00',
          end: '17:00',
          days: [1, 2, 3, 4, 5] // Monday to Friday
        }
      },
      workload: 0,
      maxWorkload: 10,
      averageResponseTime: 2, // 2 hours
      approvalRate: 0.85,
      isActive: true
    };

    this.reviewers.set(defaultReviewer.id, defaultReviewer);
  }

  // Reviewer Management
  async addReviewer(reviewer: Reviewer): Promise<void> {
    this.reviewers.set(reviewer.id, reviewer);
    
    // In a real implementation, this would be persisted to the database
    console.log(`Added reviewer: ${reviewer.name} (${reviewer.id})`);
  }

  async updateReviewer(reviewerId: string, updates: Partial<Reviewer>): Promise<void> {
    const reviewer = this.reviewers.get(reviewerId);
    if (!reviewer) {
      throw new Error(`Reviewer ${reviewerId} not found`);
    }

    const updatedReviewer = { ...reviewer, ...updates };
    this.reviewers.set(reviewerId, updatedReviewer);
  }

  async getReviewer(reviewerId: string): Promise<Reviewer | null> {
    return this.reviewers.get(reviewerId) || null;
  }

  async listReviewers(filters?: ReviewerFilters): Promise<Reviewer[]> {
    let reviewers = Array.from(this.reviewers.values());

    if (filters) {
      if (filters.role) {
        reviewers = reviewers.filter(r => r.role === filters.role);
      }
      if (filters.expertise) {
        reviewers = reviewers.filter(r => 
          filters.expertise!.some(exp => r.expertise.includes(exp))
        );
      }
      if (filters.availability) {
        reviewers = reviewers.filter(r => r.availability.status === filters.availability);
      }
      if (filters.maxWorkload) {
        reviewers = reviewers.filter(r => r.workload <= filters.maxWorkload!);
      }
    }

    return reviewers.filter(r => r.isActive);
  }

  // Enhanced Review Creation with Auto-Assignment
  async createReview(content: string, options: ReviewOptions): Promise<ReviewLink> {
    // Create the basic review first
    const reviewLink = await super.createReview(content, options);

    // Auto-assign reviewers if specified
    if (options.reviewers && options.reviewers.length > 0) {
      const assignments: ReviewAssignment[] = [];
      
      for (let i = 0; i < options.reviewers.length; i++) {
        const reviewerId = options.reviewers[i];
        const reviewer = await this.getReviewer(reviewerId);
        
        if (reviewer) {
          const assignment: ReviewAssignment = {
            id: uuidv4(),
            reviewId: reviewLink.reviewId,
            reviewerId,
            assignedAt: new Date().toISOString(),
            assignedBy: 'system',
            role: i === 0 ? 'primary' : 'secondary',
            status: 'pending',
            deadline: options.deadline || this.calculateDefaultDeadline(),
            priority: options.priority || 'medium',
            estimatedTime: this.estimateReviewTime(content, options.type)
          };
          
          assignments.push(assignment);
        }
      }

      if (assignments.length > 0) {
        await this.assignReviewers(reviewLink.reviewId, assignments);
      }
    }

    return reviewLink;
  }

  // Multi-reviewer Assignment
  async assignReviewers(reviewId: string, assignments: ReviewAssignment[]): Promise<void> {
    for (const assignment of assignments) {
      this.assignments.set(assignment.id, assignment);
      
      // Update reviewer workload
      const reviewer = this.reviewers.get(assignment.reviewerId);
      if (reviewer) {
        reviewer.workload += 1;
        this.reviewers.set(reviewer.id, reviewer);
      }

      // Send notification to reviewer
      await this.sendReviewNotification(assignment);
    }
  }

  async getReviewAssignments(reviewId: string): Promise<ReviewAssignment[]> {
    return Array.from(this.assignments.values())
      .filter(assignment => assignment.reviewId === reviewId);
  }

  async updateAssignment(assignmentId: string, updates: Partial<ReviewAssignment>): Promise<void> {
    const assignment = this.assignments.get(assignmentId);
    if (!assignment) {
      throw new Error(`Assignment ${assignmentId} not found`);
    }

    const updatedAssignment = { ...assignment, ...updates };
    this.assignments.set(assignmentId, updatedAssignment);

    // Update reviewer workload if status changed to completed
    if (updates.status === 'completed' && assignment.status !== 'completed') {
      const reviewer = this.reviewers.get(assignment.reviewerId);
      if (reviewer && reviewer.workload > 0) {
        reviewer.workload -= 1;
        this.reviewers.set(reviewer.id, reviewer);
      }
    }
  }

  // Advanced Operations
  async escalateReview(reviewId: string, reason: string): Promise<void> {
    const assignments = await this.getReviewAssignments(reviewId);
    const escalationRules = this.escalationRules.get(reviewId) || [];

    for (const rule of escalationRules) {
      // Create new assignments for escalation targets
      const escalationAssignments: ReviewAssignment[] = rule.escalateTo.map(reviewerId => ({
        id: uuidv4(),
        reviewId,
        reviewerId,
        assignedAt: new Date().toISOString(),
        assignedBy: 'escalation-system',
        role: 'specialist',
        status: 'pending',
        deadline: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
        priority: 'urgent',
        estimatedTime: 30 // 30 minutes for escalated reviews
      }));

      await this.assignReviewers(reviewId, escalationAssignments);
    }

    console.log(`Escalated review ${reviewId}: ${reason}`);
  }

  async sendReminder(reviewId: string, reviewerId?: string): Promise<void> {
    const assignments = await this.getReviewAssignments(reviewId);
    const targetAssignments = reviewerId 
      ? assignments.filter(a => a.reviewerId === reviewerId)
      : assignments.filter(a => a.status === 'pending');

    for (const assignment of targetAssignments) {
      const notification: ReviewNotification = {
        type: NotificationType.REVIEW_REMINDER,
        reviewId,
        recipient: assignment.reviewerId,
        subject: 'Review Reminder',
        message: `Please complete your review for ${reviewId}`,
        link: `${this.baseUrl}/review/${reviewId}`
      };

      await this.sendNotification(notification);
    }
  }

  async getReviewAnalytics(timeRange?: { start: string; end: string }): Promise<ReviewAnalytics> {
    // This would typically query the database for analytics
    // For now, return mock data based on current state
    
    const allAssignments = Array.from(this.assignments.values());
    const completedAssignments = allAssignments.filter(a => a.status === 'completed');
    
    const totalReviews = completedAssignments.length;
    const averageResponseTime = completedAssignments.reduce((sum, a) => {
      if (a.actualTime) return sum + a.actualTime;
      return sum + (a.estimatedTime || 60);
    }, 0) / Math.max(totalReviews, 1);

    const approvedReviews = completedAssignments.filter(a => a.decision === ReviewDecision.APPROVE);
    const approvalRate = approvedReviews.length / Math.max(totalReviews, 1);

    return {
      totalReviews,
      averageResponseTime,
      approvalRate,
      reviewerPerformance: {},
      contentTypeStats: {},
      escalationRate: 0.05 // 5% escalation rate
    };
  }

  // Helper Methods
  private estimateReviewTime(content: string, type: string): number {
    const baseTime = 15; // 15 minutes base
    const contentLength = content.length;
    const lengthMultiplier = Math.min(contentLength / 1000, 3); // Max 3x for very long content
    
    const typeMultipliers = {
      'approval': 1,
      'editing': 2,
      'feedback': 1.5
    };

    const multiplier = typeMultipliers[type as keyof typeof typeMultipliers] || 1;
    return Math.round(baseTime * lengthMultiplier * multiplier);
  }

  private async sendReviewNotification(assignment: ReviewAssignment): Promise<void> {
    const reviewer = await this.getReviewer(assignment.reviewerId);
    if (!reviewer) return;

    const notification: ReviewNotification = {
      type: NotificationType.REVIEW_REQUESTED,
      reviewId: assignment.reviewId,
      recipient: assignment.reviewerId,
      subject: 'New Review Assignment',
      message: `You have been assigned a new review with ${assignment.priority} priority`,
      link: `${this.baseUrl}/review/${assignment.reviewId}`
    };

    await this.sendNotification(notification);
  }

  private async sendNotification(notification: ReviewNotification): Promise<void> {
    // In a real implementation, this would send emails, push notifications, etc.
    console.log(`Notification sent to ${notification.recipient}: ${notification.subject}`);
  }

  private calculateDefaultDeadline(): string {
    // 48 hours from now
    return new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString();
  }
}
