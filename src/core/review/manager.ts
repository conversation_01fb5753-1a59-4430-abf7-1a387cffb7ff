/**
 * Enhanced Review Management System
 * Handles multi-reviewer workflows, deadlines, and collaborative editing
 * Uses persistent storage for production-ready review management
 */

import { SimplifiedStateStore } from '../state/store';
import { getStateStore } from '../workflow/singleton';

export interface Reviewer {
  id: string;
  name: string;
  email: string;
  role: 'editor' | 'content_manager' | 'seo_specialist' | 'subject_expert';
  permissions: ReviewPermission[];
}

export interface ReviewPermission {
  action: 'approve' | 'reject' | 'edit' | 'comment' | 'assign';
  contentTypes?: string[];
  conditions?: Record<string, any>;
}

export interface ReviewAssignment {
  id: string;
  contentId: string;
  reviewerId: string;
  assignedBy: string;
  assignedAt: string;
  deadline?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'completed' | 'overdue' | 'escalated';
  requirements: ReviewRequirement[];
  completedAt?: string;
  result?: ReviewResult;
}

export interface ReviewRequirement {
  type: 'approval' | 'rating' | 'feedback' | 'edit';
  field: string;
  required: boolean;
  minRating?: number;
  description?: string;
}

export interface ReviewResult {
  decision: 'approved' | 'rejected' | 'needs_revision';
  ratings: Record<string, number>;
  feedback: string;
  suggestions: string[];
  edits?: ContentEdit[];
  reviewedAt: string;
  timeSpent?: number; // in minutes
}

export interface ContentEdit {
  id: string;
  type: 'insert' | 'delete' | 'replace';
  position: number;
  originalText?: string;
  newText?: string;
  reason?: string;
  reviewerId: string;
  timestamp: string;
}

export interface ReviewWorkflow {
  id: string;
  name: string;
  contentTypes: string[];
  stages: ReviewStage[];
  escalationRules: EscalationRule[];
  autoApprovalRules?: AutoApprovalRule[];
}

export interface ReviewStage {
  id: string;
  name: string;
  order: number;
  reviewerRoles: string[];
  requiredApprovals: number;
  allowParallel: boolean;
  deadline?: number; // hours
  requirements: ReviewRequirement[];
}

export interface EscalationRule {
  trigger: 'deadline_missed' | 'rejection' | 'conflict';
  condition?: Record<string, any>;
  action: 'notify_manager' | 'reassign' | 'auto_approve' | 'escalate_stage';
  targetRole?: string;
  delay?: number; // hours
}

export interface AutoApprovalRule {
  condition: Record<string, any>;
  requirements: string[];
  maxValue?: number;
  contentTypes?: string[];
}

export class ReviewManager {
  private workflows = new Map<string, ReviewWorkflow>();
  private stateStore: SimplifiedStateStore;

  constructor(stateStore?: SimplifiedStateStore) {
    this.stateStore = stateStore || getStateStore();
    this.initializeDefaultWorkflows();
    // Initialize default reviewers asynchronously
    this.initializeDefaultReviewers();
  }

  // Reviewer Management
  async addReviewer(reviewer: Reviewer): Promise<void> {
    await this.stateStore.setReviewer(reviewer);
  }

  async getReviewer(id: string): Promise<Reviewer | null> {
    return await this.stateStore.getReviewer(id);
  }

  async getReviewersByRole(role: string): Promise<Reviewer[]> {
    const allReviewers = await this.stateStore.getAllReviewers();
    return allReviewers.filter(r => r.role === role);
  }

  // Get assignment by ID
  async getAssignment(assignmentId: string): Promise<ReviewAssignment | null> {
    return await this.stateStore.getReviewAssignment(assignmentId);
  }

  // Get assignments by content ID
  async getAssignmentsByContent(contentId: string): Promise<ReviewAssignment[]> {
    const allAssignments = await this.stateStore.getAllReviewAssignments();
    return allAssignments.filter(assignment => assignment.contentId === contentId);
  }

  // Get all review assignments
  async getAllReviewAssignments(): Promise<ReviewAssignment[]> {
    return await this.stateStore.getAllReviewAssignments();
  }

  // Enhanced reviewer assignment method
  async assignReviewer(artifactId: string, reviewerId: string, assignedBy: string = 'system'): Promise<string> {
    const reviewer = this.getReviewer(reviewerId);
    if (!reviewer) {
      throw new Error(`Reviewer ${reviewerId} not found`);
    }

    return this.assignReview(artifactId, reviewerId, assignedBy, {
      priority: 'medium',
      deadline: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours default
    });
  }

  // Get reviews by reviewer
  async getReviewsByReviewer(reviewerId: string): Promise<ReviewAssignment[]> {
    const allAssignments = await this.stateStore.getAllReviewAssignments();
    return allAssignments.filter(assignment => assignment.reviewerId === reviewerId);
  }

  // Get pending reviews with optional reviewer filter
  async getPendingReviews(reviewerId?: string): Promise<ReviewAssignment[]> {
    const allAssignments = await this.stateStore.getAllReviewAssignments();
    const allPending = allAssignments
      .filter(assignment => assignment.status === 'pending' || assignment.status === 'in_progress');

    if (reviewerId) {
      return allPending.filter(assignment => assignment.reviewerId === reviewerId);
    }

    return allPending;
  }

  // Update review priority
  async updateReviewPriority(reviewId: string, priority: 'low' | 'medium' | 'high' | 'urgent'): Promise<void> {
    const assignment = await this.stateStore.getReviewAssignment(reviewId);
    if (!assignment) {
      throw new Error(`Review assignment ${reviewId} not found`);
    }

    assignment.priority = priority;
    await this.stateStore.setReviewAssignment(assignment);

    // If priority is urgent, send immediate notification
    if (priority === 'urgent') {
      await this.notifyReviewer(assignment);
    }
  }

  // Calculate and set review deadline
  calculateReviewDeadline(priority: 'low' | 'medium' | 'high' | 'urgent', estimatedTime?: number): Date {
    const now = new Date();
    let hoursToAdd: number;

    switch (priority) {
      case 'urgent':
        hoursToAdd = 2;
        break;
      case 'high':
        hoursToAdd = 8;
        break;
      case 'medium':
        hoursToAdd = 24;
        break;
      case 'low':
        hoursToAdd = 72;
        break;
      default:
        hoursToAdd = 24;
    }

    // Add estimated time if provided (in minutes)
    if (estimatedTime) {
      hoursToAdd += estimatedTime / 60;
    }

    return new Date(now.getTime() + hoursToAdd * 60 * 60 * 1000);
  }

  // Assignment Management
  async assignReview(
    contentId: string,
    reviewerId: string,
    assignedBy: string,
    options: {
      deadline?: Date;
      priority?: 'low' | 'medium' | 'high' | 'urgent';
      requirements?: ReviewRequirement[];
      workflowId?: string;
    } = {}
  ): Promise<string> {
    // Validate reviewer exists
    const reviewer = await this.getReviewer(reviewerId);
    if (!reviewer) {
      throw new Error(`Reviewer ${reviewerId} not found`);
    }

    const assignmentId = `review_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    const assignment: ReviewAssignment = {
      id: assignmentId,
      contentId,
      reviewerId,
      assignedBy,
      assignedAt: new Date().toISOString(),
      deadline: options.deadline?.toISOString(),
      priority: options.priority || 'medium',
      status: 'pending',
      requirements: options.requirements || this.getDefaultRequirements()
    };

    await this.stateStore.setReviewAssignment(assignment);

    // Send notification to reviewer
    await this.notifyReviewer(assignment);

    return assignmentId;
  }

  async submitReview(
    assignmentId: string,
    result: Omit<ReviewResult, 'reviewedAt'>
  ): Promise<void> {
    const assignment = await this.stateStore.getReviewAssignment(assignmentId);
    if (!assignment) {
      throw new Error('Assignment not found');
    }

    assignment.result = {
      ...result,
      reviewedAt: new Date().toISOString()
    };
    assignment.status = 'completed';
    assignment.completedAt = new Date().toISOString();

    await this.stateStore.setReviewAssignment(assignment);

    // Check if all required reviews are complete
    await this.checkReviewCompletion(assignment.contentId);
  }

  // Workflow Management
  async startReviewWorkflow(
    contentId: string,
    workflowId: string,
    initiatedBy: string
  ): Promise<string[]> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      throw new Error('Workflow not found');
    }

    const assignmentIds: string[] = [];
    
    // Start with first stage
    const firstStage = workflow.stages.find(s => s.order === 1);
    if (firstStage) {
      const reviewers = await this.getReviewersForStage(firstStage);

      for (const reviewer of reviewers) {
        const deadline = firstStage.deadline 
          ? new Date(Date.now() + firstStage.deadline * 60 * 60 * 1000)
          : undefined;

        const assignmentId = await this.assignReview(
          contentId,
          reviewer.id,
          initiatedBy,
          {
            deadline,
            requirements: firstStage.requirements,
            workflowId
          }
        );
        
        assignmentIds.push(assignmentId);
      }
    }

    return assignmentIds;
  }

  // Review Status and Analytics
  async getContentReviewStatus(contentId: string): Promise<{
    status: 'pending' | 'in_progress' | 'completed' | 'rejected';
    assignments: ReviewAssignment[];
    overallProgress: number;
    nextActions: string[];
  }> {
    const assignments = await this.getAssignmentsByContent(contentId);

    if (assignments.length === 0) {
      return {
        status: 'pending',
        assignments: [],
        overallProgress: 0,
        nextActions: ['Assign reviewers']
      };
    }

    const completed = assignments.filter((a: ReviewAssignment) => a.status === 'completed').length;
    const rejected = assignments.some((a: ReviewAssignment) => a.result?.decision === 'rejected');
    const allCompleted = completed === assignments.length;

    let status: 'pending' | 'in_progress' | 'completed' | 'rejected';
    if (rejected) {
      status = 'rejected';
    } else if (allCompleted) {
      status = 'completed';
    } else if (completed > 0) {
      status = 'in_progress';
    } else {
      status = 'pending';
    }

    const overallProgress = assignments.length > 0 ? (completed / assignments.length) * 100 : 0;
    
    const nextActions: string[] = [];
    if (status === 'pending') {
      nextActions.push('Waiting for reviewers to start');
    } else if (status === 'in_progress') {
      const pending = assignments.filter(a => a.status === 'pending').length;
      nextActions.push(`${pending} reviews pending`);
    } else if (status === 'rejected') {
      nextActions.push('Address reviewer feedback and resubmit');
    }

    return {
      status,
      assignments,
      overallProgress,
      nextActions
    };
  }

  async getReviewerWorkload(reviewerId: string): Promise<{
    pending: number;
    overdue: number;
    avgTimeToComplete: number;
    completionRate: number;
  }> {
    const assignments = await this.getReviewsByReviewer(reviewerId);

    const pending = assignments.filter(a => a.status === 'pending' || a.status === 'in_progress').length;
    
    const now = new Date();
    const overdue = assignments.filter(a => 
      a.deadline && new Date(a.deadline) < now && a.status !== 'completed'
    ).length;

    const completed = assignments.filter(a => a.status === 'completed');
    const completionRate = assignments.length > 0 ? (completed.length / assignments.length) * 100 : 0;

    // Calculate average time to complete (in hours)
    const avgTimeToComplete = completed.length > 0
      ? completed.reduce((sum, a) => {
          if (a.completedAt) {
            const start = new Date(a.assignedAt);
            const end = new Date(a.completedAt);
            return sum + (end.getTime() - start.getTime()) / (1000 * 60 * 60);
          }
          return sum;
        }, 0) / completed.length
      : 0;

    return {
      pending,
      overdue,
      avgTimeToComplete,
      completionRate
    };
  }

  // Private helper methods
  private initializeDefaultWorkflows(): void {
    // Standard content review workflow
    const standardWorkflow: ReviewWorkflow = {
      id: 'standard-content-review',
      name: 'Standard Content Review',
      contentTypes: ['blog-post', 'article', 'product-description'],
      stages: [
        {
          id: 'content-review',
          name: 'Content Review',
          order: 1,
          reviewerRoles: ['editor', 'content_manager'],
          requiredApprovals: 1,
          allowParallel: true,
          deadline: 24,
          requirements: [
            { type: 'rating', field: 'quality', required: true, minRating: 3 },
            { type: 'rating', field: 'accuracy', required: true, minRating: 4 },
            { type: 'feedback', field: 'comments', required: false }
          ]
        },
        {
          id: 'seo-review',
          name: 'SEO Review',
          order: 2,
          reviewerRoles: ['seo_specialist'],
          requiredApprovals: 1,
          allowParallel: false,
          deadline: 12,
          requirements: [
            { type: 'rating', field: 'seo_score', required: true, minRating: 4 },
            { type: 'approval', field: 'seo_approval', required: true }
          ]
        }
      ],
      escalationRules: [
        {
          trigger: 'deadline_missed',
          action: 'notify_manager',
          delay: 2
        }
      ]
    };

    this.workflows.set(standardWorkflow.id, standardWorkflow);
  }

  private async initializeDefaultReviewers(): Promise<void> {
    // Add some default reviewers for testing
    const defaultReviewers: Reviewer[] = [
      {
        id: 'reviewer-1',
        name: 'John Editor',
        email: '<EMAIL>',
        role: 'editor',
        permissions: [
          { action: 'approve', contentTypes: ['blog-post', 'article'] },
          { action: 'edit', contentTypes: ['blog-post', 'article'] },
          { action: 'comment' }
        ]
      },
      {
        id: 'reviewer-2',
        name: 'Sarah Manager',
        email: '<EMAIL>',
        role: 'content_manager',
        permissions: [
          { action: 'approve' },
          { action: 'reject' },
          { action: 'assign' }
        ]
      },
      {
        id: 'reviewer-456',
        name: 'Test Reviewer',
        email: '<EMAIL>',
        role: 'editor',
        permissions: [
          { action: 'approve' },
          { action: 'edit' },
          { action: 'comment' }
        ]
      }
    ];

    for (const reviewer of defaultReviewers) {
      await this.addReviewer(reviewer);
    }
  }

  private getDefaultRequirements(): ReviewRequirement[] {
    return [
      { type: 'rating', field: 'quality', required: true, minRating: 3 },
      { type: 'approval', field: 'approved', required: true },
      { type: 'feedback', field: 'comments', required: false }
    ];
  }

  private async getReviewersForStage(stage: ReviewStage): Promise<Reviewer[]> {
    const reviewers: Reviewer[] = [];

    for (const role of stage.reviewerRoles) {
      const roleReviewers = await this.getReviewersByRole(role);
      reviewers.push(...roleReviewers);
    }

    return reviewers;
  }

  private async notifyReviewer(assignment: ReviewAssignment): Promise<void> {
    // In a real implementation, this would send email/notification
    console.log(`Notifying reviewer ${assignment.reviewerId} about assignment ${assignment.id}`);
  }

  private async checkReviewCompletion(contentId: string): Promise<void> {
    const status = await this.getContentReviewStatus(contentId);

    if (status.status === 'completed') {
      // All reviews complete - trigger next workflow stage or completion
      console.log(`All reviews completed for content ${contentId}`);
    } else if (status.status === 'rejected') {
      // Handle rejection - notify content creator
      console.log(`Content ${contentId} rejected by reviewers`);
    }
  }
}
