/**
 * TDD Tests for Version Revert Issues
 * Testing the critical version revert logic problems
 */

import { ArtifactVersionManager } from '../version-manager';

describe('Version Revert TDD Tests', () => {
  let versionManager: ArtifactVersionManager;

  beforeEach(() => {
    versionManager = new ArtifactVersionManager();
  });

  describe('Critical Issue: Version revert returning same ID as original', () => {
    test('should revert to previous version without creating duplicate', async () => {
      // Arrange
      const artifactId = 'test-artifact-123';
      const originalContent = { title: 'Original Title', body: 'Original content' };
      const modifiedContent = { title: 'Modified Title', body: 'Modified content' };
      const revertedBy = 'admin-user';

      // Create original version
      const version1Id = await versionManager.createVersion(
        artifactId,
        originalContent,
        'Initial version',
        'system'
      );

      // Create modified version
      const version2Id = await versionManager.createVersion(
        artifactId,
        modifiedContent,
        'Modified content',
        'editor'
      );

      // Act - Revert to version 1
      const revertedVersionId = await versionManager.revertToVersion(
        artifactId,
        version1Id,
        revertedBy
      );

      // Assert
      expect(revertedVersionId).toBeDefined();
      expect(revertedVersionId).not.toBe(version1Id); // Should create new version, not return original
      expect(revertedVersionId).not.toBe(version2Id); // Should not be the modified version

      // Check that the reverted version has the original content
      const revertedVersion = versionManager.getVersion(revertedVersionId);
      expect(revertedVersion).not.toBeNull();
      expect(revertedVersion?.content).toEqual(originalContent);
      expect(revertedVersion?.createdBy).toBe(revertedBy);
      expect(revertedVersion?.changeDescription).toContain('Reverted to version');
      expect(revertedVersion?.isActive).toBe(true);

      // Check that the reverted version is now active
      const activeVersion = versionManager.getActiveVersion(artifactId);
      expect(activeVersion?.id).toBe(revertedVersionId);
      expect(activeVersion?.content).toEqual(originalContent);
    });

    test('should maintain version history after revert', async () => {
      // Arrange
      const artifactId = 'test-artifact-history';
      const content1 = { text: 'Version 1' };
      const content2 = { text: 'Version 2' };
      const content3 = { text: 'Version 3' };

      // Create version chain
      const v1 = await versionManager.createVersion(artifactId, content1, 'V1', 'user1');
      const v2 = await versionManager.createVersion(artifactId, content2, 'V2', 'user2');
      const v3 = await versionManager.createVersion(artifactId, content3, 'V3', 'user3');

      // Act - Revert to v1
      const revertedId = await versionManager.revertToVersion(artifactId, v1, 'admin');

      // Assert
      const history = versionManager.getVersionHistory(artifactId);
      expect(history).toHaveLength(4); // v1, v2, v3, reverted
      
      // Check version numbers are sequential
      const versionNumbers = history.map(v => v.version).sort((a, b) => a - b);
      expect(versionNumbers).toEqual([1, 2, 3, 4]);

      // Check that only the reverted version is active
      const activeVersions = history.filter(v => v.isActive);
      expect(activeVersions).toHaveLength(1);
      expect(activeVersions[0].id).toBe(revertedId);
    });

    test('should handle revert to non-existent version', async () => {
      // Arrange
      const artifactId = 'test-artifact-error';
      const nonExistentVersionId = 'non-existent-version-123';

      // Act & Assert
      await expect(
        versionManager.revertToVersion(artifactId, nonExistentVersionId, 'admin')
      ).rejects.toThrow('Version non-existent-version-123 not found');
    });

    test('should handle revert to version of different artifact', async () => {
      // Arrange
      const artifact1 = 'artifact-1';
      const artifact2 = 'artifact-2';
      const content = { text: 'Test content' };

      // Create version for artifact1
      const version1 = await versionManager.createVersion(artifact1, content, 'V1', 'user');
      
      // Try to revert artifact2 to artifact1's version
      await expect(
        versionManager.revertToVersion(artifact2, version1, 'admin')
      ).rejects.toThrow(`Version ${version1} not found for artifact ${artifact2}`);
    });

    test('should track revert metadata correctly', async () => {
      // Arrange
      const artifactId = 'test-artifact-metadata';
      const originalContent = { data: 'original' };
      const modifiedContent = { data: 'modified' };

      const originalVersion = await versionManager.createVersion(
        artifactId,
        originalContent,
        'Original version',
        'creator'
      );

      await versionManager.createVersion(
        artifactId,
        modifiedContent,
        'Modified version',
        'editor'
      );

      // Act
      const revertedVersionId = await versionManager.revertToVersion(
        artifactId,
        originalVersion,
        'admin-reverter'
      );

      // Assert
      const revertedVersion = versionManager.getVersion(revertedVersionId);
      expect(revertedVersion?.metadata).toHaveProperty('revertedFrom', originalVersion);
      expect(revertedVersion?.metadata).toHaveProperty('revertedFromVersion', 1);
      expect(revertedVersion?.metadata).toHaveProperty('action');
      expect(revertedVersion?.createdBy).toBe('admin-reverter');
    });

    test('should handle multiple reverts correctly', async () => {
      // Arrange
      const artifactId = 'test-artifact-multiple-reverts';
      const content1 = { version: 'v1' };
      const content2 = { version: 'v2' };
      const content3 = { version: 'v3' };

      const v1 = await versionManager.createVersion(artifactId, content1, 'V1', 'user1');
      const v2 = await versionManager.createVersion(artifactId, content2, 'V2', 'user2');
      const v3 = await versionManager.createVersion(artifactId, content3, 'V3', 'user3');

      // Act - Multiple reverts
      const revert1 = await versionManager.revertToVersion(artifactId, v1, 'admin1'); // Revert to v1
      const revert2 = await versionManager.revertToVersion(artifactId, v2, 'admin2'); // Revert to v2

      // Assert
      const history = versionManager.getVersionHistory(artifactId);
      expect(history).toHaveLength(5); // v1, v2, v3, revert1, revert2

      // Only the last revert should be active
      const activeVersion = versionManager.getActiveVersion(artifactId);
      expect(activeVersion?.id).toBe(revert2);
      expect(activeVersion?.content).toEqual(content2);

      // Check that previous reverts are no longer active
      const revert1Version = versionManager.getVersion(revert1);
      expect(revert1Version?.isActive).toBe(false);
    });

    test('should compare versions correctly after revert', async () => {
      // Arrange
      const artifactId = 'test-artifact-compare';
      const content1 = { text: 'Original text', count: 1 };
      const content2 = { text: 'Modified text', count: 2 };

      const v1 = await versionManager.createVersion(artifactId, content1, 'V1', 'user1');
      const v2 = await versionManager.createVersion(artifactId, content2, 'V2', 'user2');
      const revertId = await versionManager.revertToVersion(artifactId, v1, 'admin');

      // Act
      const comparison = versionManager.compareVersions(v1, revertId);

      // Assert
      expect(comparison).toBeDefined();
      expect(comparison.identical).toBe(true); // Content should be identical
      expect(comparison.differences).toHaveLength(0); // No content differences
    });
  });

  describe('Version Management Edge Cases', () => {
    test('should handle revert when no versions exist', async () => {
      // Arrange
      const artifactId = 'non-existent-artifact';
      const versionId = 'non-existent-version';

      // Act & Assert
      await expect(
        versionManager.revertToVersion(artifactId, versionId, 'admin')
      ).rejects.toThrow();
    });

    test('should prevent revert to already active version', async () => {
      // Arrange
      const artifactId = 'test-artifact-active';
      const content = { text: 'Current content' };

      const currentVersion = await versionManager.createVersion(
        artifactId,
        content,
        'Current version',
        'user'
      );

      // Act - Try to revert to currently active version
      const revertResult = await versionManager.revertToVersion(
        artifactId,
        currentVersion,
        'admin'
      );

      // Assert - Should still create a new version (revert operation)
      expect(revertResult).toBeDefined();
      expect(revertResult).not.toBe(currentVersion);
      
      const revertedVersion = versionManager.getVersion(revertResult);
      expect(revertedVersion?.content).toEqual(content);
    });
  });
});
