/**
 * TDD Tests for Review Assignment Persistence Issues
 * Testing the critical review assignment tracking problems
 */

import { ReviewManager } from '../manager';
import { SimplifiedStateStore } from '../../state/store';
import { MemoryStorageAdapter } from '../../state/types';

describe('Review Assignment Persistence TDD Tests', () => {
  let reviewManager: ReviewManager;
  let stateStore: SimplifiedStateStore;

  beforeEach(async () => {
    // Use memory adapter for testing
    const memoryAdapter = new MemoryStorageAdapter();
    stateStore = new SimplifiedStateStore(memoryAdapter);
    await stateStore.initialize();

    reviewManager = new ReviewManager(stateStore);

    // Add test reviewers
    const testReviewers = [
      {
        id: 'busy-reviewer',
        name: 'Busy Reviewer',
        email: '<EMAIL>',
        role: 'editor' as const,
        permissions: [{ action: 'approve' as const }]
      },
      {
        id: 'reviewer-status',
        name: 'Status Reviewer',
        email: '<EMAIL>',
        role: 'editor' as const,
        permissions: [{ action: 'approve' as const }]
      },
      {
        id: 'reviewer-pending-1',
        name: 'Pending Reviewer 1',
        email: '<EMAIL>',
        role: 'editor' as const,
        permissions: [{ action: 'approve' as const }]
      },
      {
        id: 'reviewer-pending-2',
        name: 'Pending Reviewer 2',
        email: '<EMAIL>',
        role: 'editor' as const,
        permissions: [{ action: 'approve' as const }]
      },
      {
        id: 'reviewer-duplicate',
        name: 'Duplicate Reviewer',
        email: '<EMAIL>',
        role: 'editor' as const,
        permissions: [{ action: 'approve' as const }]
      }
    ];

    for (const reviewer of testReviewers) {
      await reviewManager.addReviewer(reviewer);
    }
  });

  describe('Critical Issue: Assignment tracking not working properly', () => {
    test('should create and retrieve review assignments within same instance', async () => {
      // Arrange
      const contentId = 'test-content-123';
      const reviewerId = 'reviewer-456';
      const assignedBy = 'admin-789';

      // Act - Create assignment
      const assignmentId = await reviewManager.assignReview(
        contentId,
        reviewerId,
        assignedBy,
        {
          priority: 'high',
          deadline: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
        }
      );

      // Retrieve assignment from same instance
      const retrievedAssignment = await reviewManager.getAssignment(assignmentId);

      // Assert
      expect(assignmentId).toBeDefined();
      expect(retrievedAssignment).not.toBeNull();
      expect(retrievedAssignment?.contentId).toBe(contentId);
      expect(retrievedAssignment?.reviewerId).toBe(reviewerId);
      expect(retrievedAssignment?.assignedBy).toBe(assignedBy);
      expect(retrievedAssignment?.priority).toBe('high');
    });

    test('should track multiple assignments for same content', async () => {
      // Arrange
      const contentId = 'test-content-multi';
      const reviewer1 = 'reviewer-1';
      const reviewer2 = 'reviewer-2';
      const assignedBy = 'admin';

      // Act
      const assignment1Id = await reviewManager.assignReview(contentId, reviewer1, assignedBy);
      const assignment2Id = await reviewManager.assignReview(contentId, reviewer2, assignedBy);

      const assignmentsByContent = await reviewManager.getAssignmentsByContent(contentId);

      // Assert
      expect(assignment1Id).not.toBe(assignment2Id);
      expect(assignmentsByContent).toHaveLength(2);
      expect(assignmentsByContent.map(a => a.reviewerId)).toContain(reviewer1);
      expect(assignmentsByContent.map(a => a.reviewerId)).toContain(reviewer2);
    });

    test('should track assignments by reviewer', async () => {
      // Arrange
      const reviewerId = 'busy-reviewer';
      const content1 = 'content-1';
      const content2 = 'content-2';
      const assignedBy = 'admin';

      // Act
      await reviewManager.assignReview(content1, reviewerId, assignedBy);
      await reviewManager.assignReview(content2, reviewerId, assignedBy);

      const reviewerAssignments = await reviewManager.getReviewsByReviewer(reviewerId);

      // Assert
      expect(reviewerAssignments).toHaveLength(2);
      expect(reviewerAssignments.map(a => a.contentId)).toContain(content1);
      expect(reviewerAssignments.map(a => a.contentId)).toContain(content2);
      expect(reviewerAssignments.every(a => a.reviewerId === reviewerId)).toBe(true);
    });

    test('should update assignment status correctly', async () => {
      // Arrange
      const contentId = 'test-content-status';
      const reviewerId = 'reviewer-status';
      const assignedBy = 'admin';

      const assignmentId = await reviewManager.assignReview(contentId, reviewerId, assignedBy);

      // Act
      await reviewManager.submitReview(assignmentId, {
        decision: 'approved',
        ratings: { quality: 5, accuracy: 4 },
        feedback: 'Excellent work!',
        suggestions: ['Minor formatting improvements'],
        reviewedAt: new Date().toISOString()
      });

      const updatedAssignment = await reviewManager.getAssignment(assignmentId);

      // Assert
      expect(updatedAssignment?.status).toBe('completed');
      expect(updatedAssignment?.result?.decision).toBe('approved');
      expect(updatedAssignment?.result?.feedback).toBe('Excellent work!');
      expect(updatedAssignment?.completedAt).toBeDefined();
    });

    test('should handle assignment not found gracefully', async () => {
      // Arrange
      const nonExistentId = 'non-existent-assignment';

      // Act & Assert
      const assignment = await reviewManager.getAssignment(nonExistentId);
      expect(assignment).toBeNull();

      await expect(
        reviewManager.submitReview(nonExistentId, {
          decision: 'approved',
          ratings: {},
          feedback: 'Test',
          suggestions: [],
          reviewedAt: new Date().toISOString()
        })
      ).rejects.toThrow('Assignment not found');
    });

    test('should get pending reviews correctly', async () => {
      // Arrange
      const reviewer1 = 'reviewer-pending-1';
      const reviewer2 = 'reviewer-pending-2';
      const assignedBy = 'admin';

      // Create some assignments
      const assignment1 = await reviewManager.assignReview('content-1', reviewer1, assignedBy);
      const assignment2 = await reviewManager.assignReview('content-2', reviewer1, assignedBy);
      const assignment3 = await reviewManager.assignReview('content-3', reviewer2, assignedBy);

      // Complete one assignment
      await reviewManager.submitReview(assignment1, {
        decision: 'approved',
        ratings: {},
        feedback: 'Good',
        suggestions: [],
        reviewedAt: new Date().toISOString()
      });

      // Act
      const allPending = await reviewManager.getPendingReviews();
      const reviewer1Pending = await reviewManager.getPendingReviews(reviewer1);
      const reviewer2Pending = await reviewManager.getPendingReviews(reviewer2);

      // Assert
      expect(allPending).toHaveLength(2); // assignment2 and assignment3
      expect(reviewer1Pending).toHaveLength(1); // only assignment2
      expect(reviewer2Pending).toHaveLength(1); // only assignment3
      expect(reviewer1Pending[0].id).toBe(assignment2);
      expect(reviewer2Pending[0].id).toBe(assignment3);
    });
  });

  describe('Assignment Validation', () => {
    test('should validate reviewer exists before assignment', async () => {
      // Arrange
      const contentId = 'test-content';
      const nonExistentReviewer = 'non-existent-reviewer';
      const assignedBy = 'admin';

      // Act & Assert
      await expect(
        reviewManager.assignReview(contentId, nonExistentReviewer, assignedBy)
      ).rejects.toThrow('Reviewer non-existent-reviewer not found');
    });

    test('should handle duplicate assignments gracefully', async () => {
      // Arrange
      const contentId = 'test-content-duplicate';
      const reviewerId = 'reviewer-duplicate';
      const assignedBy = 'admin';

      // Act
      const assignment1 = await reviewManager.assignReview(contentId, reviewerId, assignedBy);
      const assignment2 = await reviewManager.assignReview(contentId, reviewerId, assignedBy);

      // Assert
      expect(assignment1).not.toBe(assignment2); // Should create separate assignments
      
      const assignments = await reviewManager.getAssignmentsByContent(contentId);
      expect(assignments).toHaveLength(2);
    });
  });
});
