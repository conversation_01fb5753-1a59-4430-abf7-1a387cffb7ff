/**
 * Smart Feedback Processing Agent
 * 
 * Intelligently analyzes user feedback and determines the best approach
 * for content regeneration by consulting appropriate specialist agents.
 */

import { AgentId } from '../../app/(payload)/api/agents/collaborative-iteration/utils/agentTypes';

export interface FeedbackAnalysis {
  feedbackType: 'content_quality' | 'seo_optimization' | 'structure' | 'tone' | 'factual' | 'length' | 'mixed';
  severity: 'minor' | 'moderate' | 'major';
  categories: FeedbackCategory[];
  recommendedAgents: AgentId[];
  regenerationStrategy: RegenerationStrategy;
  confidence: number;
  processingTime: number;
}

export interface FeedbackCategory {
  category: string;
  issues: string[];
  priority: 'high' | 'medium' | 'low';
  suggestedActions: string[];
}

export interface RegenerationStrategy {
  approach: 'full_rewrite' | 'targeted_improvements' | 'structural_changes' | 'seo_enhancement';
  focusAreas: string[];
  agentConsultationOrder: AgentId[];
  estimatedTime: number;
  qualityThreshold: number;
}

export interface FeedbackContext {
  originalContent: string;
  userFeedback: string;
  contentMetadata: {
    wordCount: number;
    readingTime: string;
    seoScore?: number;
    topic: string;
    targetAudience: string;
  };
  previousAttempts: number;
  workflowContext: Record<string, any>;
}

export interface RegenerationPlan {
  executionId: string;
  artifactId: string;
  strategy: RegenerationStrategy;
  agentConsultations: Array<{
    agentId: AgentId;
    prompt: string;
    priority: 'high' | 'medium' | 'low';
    expectedOutputs: string[];
  }>;
  qualityThreshold: number;
  maxAttempts: number;
  timeoutMs: number;
}

export class SmartFeedbackProcessingAgent {
  private feedbackPatterns: Map<string, FeedbackCategory>;
  private agentCapabilities: Map<AgentId, string[]>;

  constructor() {
    this.initializeFeedbackPatterns();
    this.initializeAgentCapabilities();
  }

  /**
   * Analyze user feedback and determine optimal regeneration strategy
   */
  async analyzeFeedback(context: FeedbackContext): Promise<FeedbackAnalysis> {
    const startTime = Date.now();
    
    console.log('🧠 Smart Feedback Agent: Analyzing user feedback...');
    console.log('📝 Feedback:', context.userFeedback);

    // Step 1: Categorize feedback using NLP patterns
    const categories = this.categorizeFeedback(context.userFeedback);
    
    // Step 2: Determine feedback type and severity
    const feedbackType = this.determineFeedbackType(categories);
    const severity = this.assessSeverity(context.userFeedback, context.previousAttempts);
    
    // Step 3: Select appropriate agents based on feedback analysis
    const recommendedAgents = this.selectOptimalAgents(categories, feedbackType);
    
    // Step 4: Create regeneration strategy
    const regenerationStrategy = this.createRegenerationStrategy(
      feedbackType, 
      severity, 
      categories, 
      recommendedAgents,
      context
    );
    
    // Step 5: Calculate confidence based on pattern matching and context
    const confidence = this.calculateConfidence(categories, context);
    
    const processingTime = Date.now() - startTime;

    const analysis: FeedbackAnalysis = {
      feedbackType,
      severity,
      categories,
      recommendedAgents,
      regenerationStrategy,
      confidence,
      processingTime
    };

    console.log('🎯 Feedback Analysis Complete:', {
      type: feedbackType,
      severity,
      agents: recommendedAgents,
      strategy: regenerationStrategy.approach,
      confidence: Math.round(confidence * 100) + '%'
    });

    return analysis;
  }

  /**
   * Generate enhanced prompts for content regeneration based on feedback
   */
  generateEnhancedPrompts(analysis: FeedbackAnalysis, context: FeedbackContext): Record<string, string> {
    const prompts: Record<string, string> = {};

    // Base regeneration prompt
    let basePrompt = `Please regenerate the content addressing the following user feedback: "${context.userFeedback}"\n\n`;

    // Add specific instructions based on feedback analysis
    analysis.categories.forEach(category => {
      switch (category.category) {
        case 'content_quality':
          basePrompt += `Focus on improving content quality by: ${category.suggestedActions.join(', ')}\n`;
          break;
        case 'seo_optimization':
          basePrompt += `Enhance SEO optimization by: ${category.suggestedActions.join(', ')}\n`;
          break;
        case 'structure':
          basePrompt += `Improve content structure by: ${category.suggestedActions.join(', ')}\n`;
          break;
        case 'tone':
          basePrompt += `Adjust tone and style by: ${category.suggestedActions.join(', ')}\n`;
          break;
      }
    });

    // Add strategy-specific instructions
    switch (analysis.regenerationStrategy.approach) {
      case 'full_rewrite':
        basePrompt += '\nPerform a complete rewrite while maintaining the core message and incorporating all feedback points.\n';
        break;
      case 'targeted_improvements':
        basePrompt += '\nMake targeted improvements to specific sections while preserving the overall structure.\n';
        break;
      case 'structural_changes':
        basePrompt += '\nReorganize the content structure to better address the feedback while maintaining content quality.\n';
        break;
      case 'seo_enhancement':
        basePrompt += '\nFocus primarily on SEO improvements while maintaining content readability and value.\n';
        break;
    }

    prompts.base = basePrompt;

    // Generate agent-specific prompts
    analysis.recommendedAgents.forEach(agentId => {
      prompts[agentId] = this.generateAgentSpecificPrompt(agentId, analysis, context);
    });

    return prompts;
  }

  /**
   * Process feedback and trigger intelligent regeneration
   */
  async processFeedbackAndRegenerate(
    executionId: string,
    artifactId: string,
    userFeedback: string,
    originalContent: any,
    workflowContext: Record<string, any>
  ): Promise<{
    analysis: FeedbackAnalysis;
    regenerationPlan: RegenerationPlan;
    enhancedPrompts: Record<string, string>;
  }> {
    console.log('🧠 Processing feedback for intelligent regeneration...');

    // Create feedback context
    const context: FeedbackContext = {
      originalContent: typeof originalContent === 'string' ? originalContent : JSON.stringify(originalContent),
      userFeedback,
      contentMetadata: {
        wordCount: this.calculateWordCount(originalContent),
        readingTime: this.calculateReadingTime(originalContent),
        seoScore: originalContent.metadata?.seoScore || 0,
        topic: workflowContext.topic || 'Unknown',
        targetAudience: workflowContext.target_audience || 'General'
      },
      previousAttempts: workflowContext.regenerationAttempts || 0,
      workflowContext
    };

    // Analyze feedback
    const analysis = await this.analyzeFeedback(context);

    // Generate enhanced prompts
    const enhancedPrompts = this.generateEnhancedPrompts(analysis, context);

    // Create regeneration plan
    const regenerationPlan: RegenerationPlan = {
      executionId,
      artifactId,
      strategy: analysis.regenerationStrategy,
      agentConsultations: analysis.recommendedAgents.map(agentId => ({
        agentId,
        prompt: enhancedPrompts[agentId],
        priority: analysis.categories.find(cat =>
          this.getAgentForCategory(cat.category) === agentId
        )?.priority || 'medium',
        expectedOutputs: this.getExpectedOutputsForAgent(agentId, analysis)
      })),
      qualityThreshold: analysis.regenerationStrategy.qualityThreshold,
      maxAttempts: 3,
      timeoutMs: analysis.regenerationStrategy.estimatedTime * 1000
    };

    console.log('✅ Feedback processing complete:', {
      feedbackType: analysis.feedbackType,
      severity: analysis.severity,
      agentsToConsult: analysis.recommendedAgents.length,
      strategy: analysis.regenerationStrategy.approach
    });

    return {
      analysis,
      regenerationPlan,
      enhancedPrompts
    };
  }

  private calculateWordCount(content: any): number {
    const text = typeof content === 'string' ? content : content?.content || '';
    return text.split(/\s+/).filter(word => word.length > 0).length;
  }

  private calculateReadingTime(content: any): string {
    const wordCount = this.calculateWordCount(content);
    const wordsPerMinute = 200;
    const minutes = Math.ceil(wordCount / wordsPerMinute);
    return `${minutes} min read`;
  }

  private getAgentForCategory(category: string): AgentId {
    switch (category) {
      case 'seo_optimization': return 'seo-keyword';
      case 'structure':
      case 'tone':
      case 'content_quality': return 'content-strategy';
      case 'factual': return 'market-research';
      default: return 'content-strategy';
    }
  }

  private getExpectedOutputsForAgent(agentId: AgentId, analysis: FeedbackAnalysis): string[] {
    switch (agentId) {
      case 'seo-keyword':
        return ['keyword_recommendations', 'seo_improvements', 'search_optimization'];
      case 'content-strategy':
        return ['structure_improvements', 'content_enhancements', 'audience_alignment'];
      case 'market-research':
        return ['fact_verification', 'industry_insights', 'competitive_analysis'];
      default:
        return ['general_improvements'];
    }
  }

  private initializeFeedbackPatterns(): void {
    this.feedbackPatterns = new Map([
      ['too_short', {
        category: 'length',
        issues: ['insufficient_detail', 'lacks_depth'],
        priority: 'medium',
        suggestedActions: ['expand_sections', 'add_examples', 'include_more_details']
      }],
      ['too_long', {
        category: 'length',
        issues: ['excessive_length', 'repetitive_content'],
        priority: 'medium',
        suggestedActions: ['condense_content', 'remove_redundancy', 'focus_key_points']
      }],
      ['poor_seo', {
        category: 'seo_optimization',
        issues: ['missing_keywords', 'poor_optimization'],
        priority: 'high',
        suggestedActions: ['integrate_keywords', 'optimize_headings', 'improve_meta_content']
      }],
      ['unclear_structure', {
        category: 'structure',
        issues: ['poor_organization', 'confusing_flow'],
        priority: 'high',
        suggestedActions: ['reorganize_sections', 'improve_transitions', 'clarify_hierarchy']
      }],
      ['wrong_tone', {
        category: 'tone',
        issues: ['inappropriate_tone', 'inconsistent_voice'],
        priority: 'medium',
        suggestedActions: ['adjust_tone', 'maintain_consistency', 'match_audience']
      }],
      ['factual_errors', {
        category: 'factual',
        issues: ['incorrect_information', 'outdated_data'],
        priority: 'high',
        suggestedActions: ['verify_facts', 'update_information', 'cite_sources']
      }]
    ]);
  }

  private initializeAgentCapabilities(): void {
    this.agentCapabilities = new Map([
      ['seo-keyword', ['keyword_research', 'seo_optimization', 'search_intent', 'competitive_analysis']],
      ['content-strategy', ['content_structure', 'audience_targeting', 'content_planning', 'editorial_strategy']],
      ['market-research', ['industry_analysis', 'trend_research', 'competitive_intelligence', 'market_data']]
    ]);
  }

  private categorizeFeedback(feedback: string): FeedbackCategory[] {
    const categories: FeedbackCategory[] = [];
    const feedbackLower = feedback.toLowerCase();

    // Pattern matching for different feedback types
    const patterns = [
      { keywords: ['short', 'brief', 'more detail', 'expand'], category: 'length', type: 'too_short' },
      { keywords: ['long', 'lengthy', 'too much', 'condense'], category: 'length', type: 'too_long' },
      { keywords: ['seo', 'keywords', 'search', 'ranking'], category: 'seo_optimization', type: 'poor_seo' },
      { keywords: ['structure', 'organization', 'flow', 'sections'], category: 'structure', type: 'unclear_structure' },
      { keywords: ['tone', 'voice', 'style', 'formal', 'casual'], category: 'tone', type: 'wrong_tone' },
      { keywords: ['wrong', 'incorrect', 'error', 'mistake'], category: 'factual', type: 'factual_errors' }
    ];

    patterns.forEach(pattern => {
      if (pattern.keywords.some(keyword => feedbackLower.includes(keyword))) {
        const categoryData = this.feedbackPatterns.get(pattern.type);
        if (categoryData) {
          categories.push(categoryData);
        }
      }
    });

    // If no specific patterns matched, create a general content quality category
    if (categories.length === 0) {
      categories.push({
        category: 'content_quality',
        issues: ['general_improvement_needed'],
        priority: 'medium',
        suggestedActions: ['improve_overall_quality', 'enhance_readability', 'add_value']
      });
    }

    return categories;
  }

  private determineFeedbackType(categories: FeedbackCategory[]): FeedbackAnalysis['feedbackType'] {
    if (categories.length > 2) return 'mixed';
    
    const primaryCategory = categories[0]?.category;
    switch (primaryCategory) {
      case 'seo_optimization': return 'seo_optimization';
      case 'structure': return 'structure';
      case 'tone': return 'tone';
      case 'factual': return 'factual';
      case 'length': return 'length';
      default: return 'content_quality';
    }
  }

  private assessSeverity(feedback: string, previousAttempts: number): FeedbackAnalysis['severity'] {
    const severityIndicators = {
      major: ['completely wrong', 'terrible', 'awful', 'useless', 'start over'],
      moderate: ['needs improvement', 'could be better', 'missing', 'unclear'],
      minor: ['small change', 'minor adjustment', 'slightly', 'tweak']
    };

    const feedbackLower = feedback.toLowerCase();
    
    if (severityIndicators.major.some(indicator => feedbackLower.includes(indicator)) || previousAttempts >= 2) {
      return 'major';
    } else if (severityIndicators.moderate.some(indicator => feedbackLower.includes(indicator))) {
      return 'moderate';
    } else {
      return 'minor';
    }
  }

  private selectOptimalAgents(categories: FeedbackCategory[], feedbackType: string): AgentId[] {
    const agents: Set<AgentId> = new Set();

    categories.forEach(category => {
      switch (category.category) {
        case 'seo_optimization':
          agents.add('seo-keyword');
          break;
        case 'structure':
        case 'tone':
        case 'content_quality':
          agents.add('content-strategy');
          break;
        case 'factual':
          agents.add('market-research');
          break;
      }
    });

    // Always include content-strategy for coordination
    agents.add('content-strategy');

    return Array.from(agents);
  }

  private createRegenerationStrategy(
    feedbackType: string,
    severity: string,
    categories: FeedbackCategory[],
    agents: AgentId[],
    context: FeedbackContext
  ): RegenerationStrategy {
    let approach: RegenerationStrategy['approach'];
    let estimatedTime: number;

    // Determine approach based on severity and feedback type
    if (severity === 'major' || context.previousAttempts >= 2) {
      approach = 'full_rewrite';
      estimatedTime = 180; // 3 minutes
    } else if (feedbackType === 'structure') {
      approach = 'structural_changes';
      estimatedTime = 120; // 2 minutes
    } else if (feedbackType === 'seo_optimization') {
      approach = 'seo_enhancement';
      estimatedTime = 90; // 1.5 minutes
    } else {
      approach = 'targeted_improvements';
      estimatedTime = 60; // 1 minute
    }

    const focusAreas = categories.flatMap(cat => cat.suggestedActions);
    
    return {
      approach,
      focusAreas,
      agentConsultationOrder: agents,
      estimatedTime,
      qualityThreshold: severity === 'major' ? 0.9 : 0.8
    };
  }

  private calculateConfidence(categories: FeedbackCategory[], context: FeedbackContext): number {
    let confidence = 0.7; // Base confidence

    // Increase confidence for clear, specific feedback
    if (categories.length === 1) confidence += 0.1;
    if (categories.some(cat => cat.priority === 'high')) confidence += 0.1;
    
    // Decrease confidence for vague or mixed feedback
    if (categories.length > 3) confidence -= 0.2;
    if (context.previousAttempts > 1) confidence -= 0.1;

    return Math.max(0.5, Math.min(0.95, confidence));
  }

  private generateAgentSpecificPrompt(agentId: AgentId, analysis: FeedbackAnalysis, context: FeedbackContext): string {
    const baseContext = `User feedback: "${context.userFeedback}"\nContent topic: ${context.contentMetadata.topic}\n`;
    
    switch (agentId) {
      case 'seo-keyword':
        return baseContext + 'Focus on SEO optimization, keyword integration, and search performance improvements.';
      case 'content-strategy':
        return baseContext + 'Focus on content structure, flow, audience alignment, and strategic improvements.';
      case 'market-research':
        return baseContext + 'Focus on factual accuracy, industry insights, and market-relevant information.';
      default:
        return baseContext + 'Provide general content improvement recommendations.';
    }
  }
}
