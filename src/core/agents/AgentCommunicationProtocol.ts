/**
 * Agent Communication Protocol
 * 
 * Enhanced communication system for agent-to-agent messaging, consensus building,
 * and collaborative decision making. Leverages the existing A2A protocol infrastructure.
 */

import { AgentMessageBus, AgentMessage as A2AAgentMessage, AgentMessageType } from '../../app/(payload)/api/agents/agentCommunication';

export interface AgentMessage {
  type: 'suggestion' | 'question' | 'consensus-request' | 'consensus-response';
  content: any;
  timestamp: string;
  requiresResponse?: boolean;
}

export interface ConsensusProposal {
  id: string;
  title: string;
  description: string;
  proposedChanges: ArtifactChange[];
  reasoning: string;
  confidence: number;
}

export interface ArtifactChange {
  type: 'add-section' | 'modify-content' | 'remove-section' | 'restructure';
  content: string;
  position?: number;
  metadata?: Record<string, any>;
}

export interface ConsensusResponse {
  agreement: number; // 0-1 scale
  feedback: string;
  concerns?: string[];
  suggestions?: string[];
}

export interface ConsensusResult {
  consensusReached: boolean;
  averageAgreement: number;
  participantResponses: Array<{
    agentId: string;
    response: ConsensusResponse;
  }>;
  timeoutOccurred?: boolean;
  finalDecision?: string;
}

export interface AgentConversation {
  id: string;
  participants: string[];
  messages: Array<{
    id: string;
    from: string;
    to: string | string[];
    message: AgentMessage;
    timestamp: string;
  }>;
  status: 'active' | 'completed' | 'timeout';
  createdAt: string;
}

export class AgentCommunicationProtocol {
  private messageBus: AgentMessageBus;
  private activeConversations: Map<string, AgentConversation> = new Map();
  private consensusThreshold: number = 0.7;

  constructor() {
    this.messageBus = AgentMessageBus.getInstance();
  }

  /**
   * Send a message from one agent to another
   */
  async sendMessage(
    fromAgent: string,
    toAgent: string,
    message: AgentMessage,
    conversationId: string
  ): Promise<void> {
    // Get or create conversation
    const conversation = this.getOrCreateConversation(conversationId, [fromAgent, toAgent]);
    
    // Create message ID
    const messageId = `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    
    // Add to conversation
    conversation.messages.push({
      id: messageId,
      from: fromAgent,
      to: toAgent,
      message,
      timestamp: message.timestamp
    });

    // Send via A2A message bus
    await this.messageBus.publishMessage({
      id: messageId,
      timestamp: message.timestamp,
      from: fromAgent,
      to: toAgent,
      type: message.type as AgentMessageType,
      content: message.content,
      metadata: {
        conversationId,
        requiresResponse: message.requiresResponse
      }
    });
  }

  /**
   * Broadcast message to multiple agents
   */
  async broadcastToGroup(
    fromAgent: string,
    toAgents: string[],
    message: AgentMessage,
    conversationId: string
  ): Promise<void> {
    // Get or create conversation
    const conversation = this.getOrCreateConversation(conversationId, [fromAgent, ...toAgents]);
    
    // Send to each agent
    for (const toAgent of toAgents) {
      await this.sendMessage(fromAgent, toAgent, message, conversationId);
    }
  }

  /**
   * Request consensus from multiple agents
   */
  async requestConsensus(
    initiatorAgent: string,
    participantAgents: string[],
    proposal: ConsensusProposal,
    conversationId: string,
    timeoutMs: number = 10000
  ): Promise<ConsensusResult> {
    // Send proposal to all participants
    const proposalMessage: AgentMessage = {
      type: 'consensus-request',
      content: proposal,
      timestamp: new Date().toISOString(),
      requiresResponse: true
    };

    await this.broadcastToGroup(
      initiatorAgent,
      participantAgents,
      proposalMessage,
      conversationId
    );

    // Collect responses with timeout
    const responses = await this.collectConsensusResponses(
      participantAgents,
      conversationId,
      timeoutMs
    );

    return this.calculateConsensus(proposal, responses, timeoutMs);
  }

  /**
   * Get conversation by ID
   */
  getConversation(conversationId: string): AgentConversation | undefined {
    return this.activeConversations.get(conversationId);
  }

  /**
   * Get all active conversations
   */
  getActiveConversations(): AgentConversation[] {
    return Array.from(this.activeConversations.values())
      .filter(conv => conv.status === 'active');
  }

  /**
   * Get or create a conversation
   */
  private getOrCreateConversation(conversationId: string, participants: string[]): AgentConversation {
    let conversation = this.activeConversations.get(conversationId);
    
    if (!conversation) {
      conversation = {
        id: conversationId,
        participants: [...new Set(participants)], // Remove duplicates
        messages: [],
        status: 'active',
        createdAt: new Date().toISOString()
      };
      
      this.activeConversations.set(conversationId, conversation);
    }
    
    return conversation;
  }

  /**
   * Collect consensus responses from agents
   */
  private async collectConsensusResponses(
    participantAgents: string[],
    conversationId: string,
    timeoutMs: number
  ): Promise<Array<{ agentId: string; response: ConsensusResponse }>> {
    const responses: Array<{ agentId: string; response: ConsensusResponse }> = [];
    const startTime = Date.now();

    // Poll for responses until timeout
    while (Date.now() - startTime < timeoutMs) {
      // Get message history from the bus
      const messageHistory = this.messageBus.getMessageHistory();
      
      // Find consensus responses for this conversation
      const consensusResponses = messageHistory.filter(msg => 
        msg.type === 'consensus-response' &&
        msg.metadata?.conversationId === conversationId &&
        participantAgents.includes(msg.from)
      );

      // Process new responses
      for (const msg of consensusResponses) {
        const existingResponse = responses.find(r => r.agentId === msg.from);
        if (!existingResponse) {
          responses.push({
            agentId: msg.from,
            response: msg.content as ConsensusResponse
          });
        }
      }

      // Check if all responses received
      if (responses.length >= participantAgents.length) {
        break;
      }

      // Wait a bit before next poll
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    return responses;
  }

  /**
   * Calculate consensus from responses
   */
  private calculateConsensus(
    proposal: ConsensusProposal,
    responses: Array<{ agentId: string; response: ConsensusResponse }>,
    timeoutMs: number
  ): ConsensusResult {
    if (responses.length === 0) {
      return {
        consensusReached: false,
        averageAgreement: 0,
        participantResponses: [],
        timeoutOccurred: true
      };
    }

    // Calculate average agreement
    const totalAgreement = responses.reduce((sum, r) => sum + r.response.agreement, 0);
    const averageAgreement = totalAgreement / responses.length;

    // Determine if consensus reached
    const consensusReached = averageAgreement >= this.consensusThreshold;

    // Generate final decision
    let finalDecision = '';
    if (consensusReached) {
      finalDecision = `Consensus reached: ${proposal.title} approved with ${Math.round(averageAgreement * 100)}% agreement`;
    } else {
      finalDecision = `Consensus not reached: ${proposal.title} requires revision (${Math.round(averageAgreement * 100)}% agreement, need ${Math.round(this.consensusThreshold * 100)}%)`;
    }

    return {
      consensusReached,
      averageAgreement,
      participantResponses: responses,
      timeoutOccurred: false,
      finalDecision
    };
  }

  /**
   * Set consensus threshold (0-1 scale)
   */
  setConsensusThreshold(threshold: number): void {
    if (threshold >= 0 && threshold <= 1) {
      this.consensusThreshold = threshold;
    }
  }

  /**
   * Get current consensus threshold
   */
  getConsensusThreshold(): number {
    return this.consensusThreshold;
  }

  /**
   * Close a conversation
   */
  closeConversation(conversationId: string): void {
    const conversation = this.activeConversations.get(conversationId);
    if (conversation) {
      conversation.status = 'completed';
    }
  }

  /**
   * Get conversation statistics
   */
  getConversationStats(conversationId: string): {
    messageCount: number;
    participantCount: number;
    duration: number;
    lastActivity: string;
  } | null {
    const conversation = this.activeConversations.get(conversationId);
    if (!conversation) return null;

    const now = new Date().getTime();
    const created = new Date(conversation.createdAt).getTime();
    const lastMessage = conversation.messages[conversation.messages.length - 1];

    return {
      messageCount: conversation.messages.length,
      participantCount: conversation.participants.length,
      duration: now - created,
      lastActivity: lastMessage?.timestamp || conversation.createdAt
    };
  }
}
