/**
 * Base Agent Class for Fresh Dynamic Agent System
 * 
 * Provides common functionality for all specialized agents
 */

import { v4 as uuidv4 } from 'uuid';
import {
  IAgent,
  AgentId,
  AgentCapability,
  AgentConfiguration,
  AgentConsultationRequest,
  AgentConsultationResponse,
  AgentSuggestion,
  ConsultationPriority,
  AgentError,
  CollaborationContext,
  CollaborationRound,
  AgentInput,
  PeerReview,
  ConsensusContribution
} from './types';

export abstract class BaseAgent implements IAgent {
  protected configuration: AgentConfiguration;
  protected isProcessing: boolean = false;
  protected activeConsultations: Map<string, AgentConsultationRequest> = new Map();

  constructor(
    protected agentId: AgentId,
    protected capabilities: AgentCapability[],
    config?: Partial<AgentConfiguration>
  ) {
    this.configuration = {
      agentId,
      capabilities,
      maxConcurrentConsultations: 3,
      defaultTimeoutMs: 30000,
      retryAttempts: 2,
      enabled: true,
      ...config
    };
  }

  getAgentId(): AgentId {
    return this.agentId;
  }

  getCapabilities(): AgentCapability[] {
    return this.capabilities;
  }

  getConfiguration(): AgentConfiguration {
    return { ...this.configuration };
  }

  updateConfiguration(config: Partial<AgentConfiguration>): void {
    this.configuration = { ...this.configuration, ...config };
  }

  async isAvailable(): Promise<boolean> {
    if (!this.configuration.enabled) {
      return false;
    }

    // Check if we're at max concurrent consultations
    if (this.activeConsultations.size >= this.configuration.maxConcurrentConsultations) {
      return false;
    }

    return true;
  }

  async processConsultation(request: AgentConsultationRequest): Promise<AgentConsultationResponse> {
    const startTime = Date.now();

    try {
      // Validate agent availability
      if (!await this.isAvailable()) {
        throw new AgentError(
          `Agent ${this.agentId} is not available`,
          this.agentId,
          'AGENT_UNAVAILABLE'
        );
      }

      // Add to active consultations
      this.activeConsultations.set(request.id, request);

      // Validate request
      this.validateConsultationRequest(request);

      // Process the consultation (implemented by derived classes)
      const response = await this.executeConsultation(request);

      // Calculate processing time
      const processingTime = Date.now() - startTime;

      // Create standardized response
      const consultationResponse: AgentConsultationResponse = {
        consultationId: request.id,
        agentId: this.agentId,
        response: response.response,
        confidence: response.confidence,
        reasoning: response.reasoning,
        suggestions: response.suggestions || [],
        processingTime: Math.max(processingTime, 1), // Ensure minimum 1ms for testing
        metadata: response.metadata
      };

      return consultationResponse;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      if (error instanceof AgentError) {
        throw error;
      }

      throw new AgentError(
        `Consultation failed: ${error instanceof Error ? error.message : String(error)}`,
        this.agentId,
        'CONSULTATION_FAILED',
        { originalError: error, processingTime }
      );

    } finally {
      // Remove from active consultations
      this.activeConsultations.delete(request.id);
    }
  }

  /**
   * Validate consultation request
   */
  protected validateConsultationRequest(request: AgentConsultationRequest): void {
    if (!request.id) {
      throw new AgentError(
        'Consultation request must have an ID',
        this.agentId,
        'INVALID_REQUEST'
      );
    }

    if (!request.context) {
      throw new AgentError(
        'Consultation request must have context',
        this.agentId,
        'INVALID_REQUEST'
      );
    }

    if (!request.question) {
      throw new AgentError(
        'Consultation request must have a question',
        this.agentId,
        'INVALID_REQUEST'
      );
    }
  }

  /**
   * Execute the actual consultation logic (implemented by derived classes)
   */
  protected abstract executeConsultation(
    request: AgentConsultationRequest
  ): Promise<{
    response: any;
    confidence: number;
    reasoning?: string;
    suggestions?: AgentSuggestion[];
    metadata?: Record<string, any>;
  }>;

  /**
   * Collaboration methods for multi-agent workflows
   */

  /**
   * Collaborate with other agents on an artifact
   */
  async collaborate(context: CollaborationContext): Promise<AgentInput> {
    // Default implementation - can be overridden by derived classes
    const request: AgentConsultationRequest = {
      id: `collab-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      agentId: this.agentId,
      question: `Analyze and provide recommendations for the given artifact`,
      context: {
        artifact: context.artifact,
        previousRounds: context.previousRounds,
        roundNumber: context.roundNumber
      },
      priority: 'high',
      timeoutMs: 30000
    };

    const response = await this.executeConsultation(request);

    return {
      agentId: this.agentId,
      roundNumber: context.roundNumber,
      analysis: response.response,
      suggestions: response.suggestions?.map(s => s.suggestion) || [],
      confidence: response.confidence,
      reasoning: response.reasoning || 'Analysis completed',
      collaborationNotes: this.generateCollaborationNotes(context.previousRounds)
    };
  }

  /**
   * Review inputs from peer agents
   */
  async reviewPeerInputs(inputs: AgentInput[]): Promise<PeerReview[]> {
    const reviews: PeerReview[] = [];

    for (const input of inputs) {
      if (input.agentId !== this.agentId) {
        const review = await this.reviewFromPerspective(input);
        reviews.push({
          reviewerId: this.agentId,
          reviewedAgentId: input.agentId,
          agreement: review.agreement,
          concerns: review.concerns,
          suggestions: review.suggestions,
          synergies: review.synergies
        });
      }
    }

    return reviews;
  }

  /**
   * Build consensus contribution
   */
  async buildConsensus(allInputs: AgentInput[]): Promise<ConsensusContribution> {
    const myInput = allInputs.find(input => input.agentId === this.agentId);
    const otherInputs = allInputs.filter(input => input.agentId !== this.agentId);

    return {
      agentId: this.agentId,
      consensusPoints: this.identifyConsensusPoints(myInput, otherInputs),
      disagreements: this.identifyDisagreements(myInput, otherInputs),
      finalRecommendation: this.synthesizeFinalRecommendation(myInput, otherInputs),
      confidence: myInput?.confidence || 0.5
    };
  }

  /**
   * Helper method to create suggestions
   */
  protected createSuggestion(
    area: string,
    suggestion: string,
    priority: ConsultationPriority = 'medium',
    confidence?: number,
    actionable: boolean = true
  ): AgentSuggestion {
    return {
      area,
      suggestion,
      priority,
      confidence,
      actionable
    };
  }

  /**
   * Helper methods for collaboration
   */

  /**
   * Generate collaboration notes from previous rounds
   */
  protected generateCollaborationNotes(previousRounds: CollaborationRound[]): string {
    if (previousRounds.length === 0) {
      return 'Initial collaboration round';
    }

    const notes = previousRounds.map(round =>
      `Round ${round.number}: ${round.agentInputs.size} agents participated`
    ).join('; ');

    return `Building on previous rounds: ${notes}`;
  }

  /**
   * Review input from peer agent's perspective
   */
  protected async reviewFromPerspective(input: AgentInput): Promise<{
    agreement: number;
    concerns: string[];
    suggestions: string[];
    synergies: string[];
  }> {
    // Default implementation - can be overridden by derived classes
    return {
      agreement: Math.random() * 0.4 + 0.6, // 0.6-1.0 range
      concerns: [],
      suggestions: [`Consider integrating with ${this.agentId} recommendations`],
      synergies: [`Good alignment with ${this.agentId} approach`]
    };
  }

  /**
   * Identify consensus points with other agents
   */
  protected identifyConsensusPoints(
    myInput: AgentInput | undefined,
    otherInputs: AgentInput[]
  ): string[] {
    if (!myInput) return [];

    const consensusPoints: string[] = [];

    // Simple consensus detection - can be enhanced
    for (const otherInput of otherInputs) {
      if (otherInput.confidence > 0.7 && myInput.confidence > 0.7) {
        consensusPoints.push(`High confidence alignment with ${otherInput.agentId}`);
      }
    }

    return consensusPoints;
  }

  /**
   * Identify disagreements with other agents
   */
  protected identifyDisagreements(
    myInput: AgentInput | undefined,
    otherInputs: AgentInput[]
  ): string[] {
    if (!myInput) return [];

    const disagreements: string[] = [];

    // Simple disagreement detection - can be enhanced
    for (const otherInput of otherInputs) {
      if (Math.abs(otherInput.confidence - myInput.confidence) > 0.3) {
        disagreements.push(`Confidence gap with ${otherInput.agentId}`);
      }
    }

    return disagreements;
  }

  /**
   * Synthesize final recommendation considering all inputs
   */
  protected synthesizeFinalRecommendation(
    myInput: AgentInput | undefined,
    otherInputs: AgentInput[]
  ): string {
    if (!myInput) return 'No input provided';

    const allSuggestions = [
      ...myInput.suggestions,
      ...otherInputs.flatMap(input => input.suggestions)
    ];

    return `Synthesized recommendation incorporating ${allSuggestions.length} suggestions from ${otherInputs.length + 1} agents`;
  }

  /**
   * Helper method to analyze context complexity
   */
  protected analyzeContextComplexity(context: Record<string, any>): number {
    let complexity = 0;

    // Content length factor
    if (context.content) {
      const contentLength = context.content.length;
      if (contentLength > 2000) complexity += 0.3;
      else if (contentLength > 1000) complexity += 0.2;
      else if (contentLength > 500) complexity += 0.1;
    }

    // Topic complexity factor
    if (context.topic) {
      const technicalKeywords = ['algorithm', 'api', 'framework', 'architecture', 'implementation'];
      const hasTechnicalTerms = technicalKeywords.some(keyword => 
        context.topic.toLowerCase().includes(keyword)
      );
      if (hasTechnicalTerms) complexity += 0.2;
    }

    // Target audience factor
    if (context.targetAudience) {
      const expertAudiences = ['technical experts', 'developers', 'engineers', 'professionals'];
      const isExpertAudience = expertAudiences.some(audience => 
        context.targetAudience.toLowerCase().includes(audience)
      );
      if (isExpertAudience) complexity += 0.2;
    }

    // Multiple goals factor
    if (context.goals && Array.isArray(context.goals) && context.goals.length > 3) {
      complexity += 0.1;
    }

    return Math.min(complexity, 1.0); // Cap at 1.0
  }

  /**
   * Helper method to extract keywords from feedback
   */
  protected extractKeywordsFromFeedback(feedback: string, targetKeywords: string[]): string[] {
    const foundKeywords: string[] = [];
    const feedbackLower = feedback.toLowerCase();

    for (const keyword of targetKeywords) {
      if (feedbackLower.includes(keyword.toLowerCase())) {
        foundKeywords.push(keyword);
      }
    }

    return foundKeywords;
  }

  /**
   * Helper method to calculate confidence based on context quality
   */
  protected calculateConfidence(context: Record<string, any>): number {
    let confidence = 0.5; // Base confidence

    // Required fields present
    if (context.topic) confidence += 0.2;
    if (context.targetAudience) confidence += 0.1;
    if (context.contentType) confidence += 0.1;

    // Additional context
    if (context.primaryKeyword) confidence += 0.05;
    if (context.industry) confidence += 0.05;
    if (context.goals && Array.isArray(context.goals)) confidence += 0.1;

    return Math.min(confidence, 1.0); // Cap at 1.0
  }

  /**
   * Helper method to generate reasoning text
   */
  protected generateReasoning(context: Record<string, any>, analysisPoints: string[]): string {
    const reasoning = [
      `Analysis for ${this.agentId} agent consultation:`,
      `Topic: ${context.topic || 'Not specified'}`,
      `Target Audience: ${context.targetAudience || 'Not specified'}`,
      `Content Type: ${context.contentType || 'Not specified'}`,
      '',
      'Key Analysis Points:',
      ...analysisPoints.map(point => `- ${point}`),
      '',
      `Confidence level based on available context and agent expertise.`
    ];

    return reasoning.join('\n');
  }

  /**
   * Get current status of the agent
   */
  getStatus(): {
    agentId: AgentId;
    isAvailable: boolean;
    activeConsultations: number;
    maxConcurrentConsultations: number;
    enabled: boolean;
  } {
    return {
      agentId: this.agentId,
      isAvailable: this.activeConsultations.size < this.configuration.maxConcurrentConsultations && this.configuration.enabled,
      activeConsultations: this.activeConsultations.size,
      maxConcurrentConsultations: this.configuration.maxConcurrentConsultations,
      enabled: this.configuration.enabled
    };
  }
}
