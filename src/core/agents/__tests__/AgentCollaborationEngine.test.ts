/**
 * Agent Collaboration Engine Tests
 * 
 * Test-driven development for the dynamic agent collaboration system
 */

import { AgentCollaborationEngine } from '../AgentCollaborationEngine';
import { BaseAgent } from '../base-agent';
import { AgentId, AgentCapability } from '../types';

// Mock agent for testing
class MockSeoAgent extends BaseAgent {
  constructor() {
    super('seo-keyword' as AgentId, ['keyword-research'] as AgentCapability[]);
  }

  protected async executeConsultation(request: any) {
    return {
      response: {
        keywords: ['test keyword', 'seo optimization'],
        recommendations: ['Use primary keyword in title']
      },
      confidence: 0.85,
      reasoning: 'Strong keyword analysis based on topic',
      suggestions: [{
        area: 'keyword-optimization',
        suggestion: 'Focus on primary keyword',
        priority: 'high' as const,
        confidence: 0.9,
        actionable: true
      }]
    };
  }
}

class MockMarketAgent extends BaseAgent {
  constructor() {
    super('market-research' as AgentId, ['market-analysis'] as AgentCapability[]);
  }

  protected async executeConsultation(request: any) {
    return {
      response: {
        marketSize: 'large',
        targetAudience: 'professionals',
        recommendations: ['Focus on B2B market']
      },
      confidence: 0.8,
      reasoning: 'Market analysis shows strong demand',
      suggestions: [{
        area: 'market-targeting',
        suggestion: 'Target enterprise customers',
        priority: 'medium' as const,
        confidence: 0.8,
        actionable: true
      }]
    };
  }
}

describe('AgentCollaborationEngine', () => {
  let collaborationEngine: AgentCollaborationEngine;
  let mockSeoAgent: MockSeoAgent;
  let mockMarketAgent: MockMarketAgent;

  beforeEach(() => {
    collaborationEngine = new AgentCollaborationEngine();
    mockSeoAgent = new MockSeoAgent();
    mockMarketAgent = new MockMarketAgent();
  });

  describe('Agent Registration', () => {
    test('should register agents successfully', () => {
      collaborationEngine.registerAgent(mockSeoAgent);
      collaborationEngine.registerAgent(mockMarketAgent);

      const registeredAgents = collaborationEngine.getRegisteredAgents();
      expect(registeredAgents).toHaveLength(2);
      expect(registeredAgents.map(a => a.getAgentId())).toContain('seo-keyword');
      expect(registeredAgents.map(a => a.getAgentId())).toContain('market-research');
    });

    test('should not register duplicate agents', () => {
      collaborationEngine.registerAgent(mockSeoAgent);
      collaborationEngine.registerAgent(mockSeoAgent); // Duplicate

      const registeredAgents = collaborationEngine.getRegisteredAgents();
      expect(registeredAgents).toHaveLength(1);
    });
  });

  describe('Agent Selection', () => {
    beforeEach(() => {
      collaborationEngine.registerAgent(mockSeoAgent);
      collaborationEngine.registerAgent(mockMarketAgent);
    });

    test('should select relevant agents for keyword research task', async () => {
      const task = {
        type: 'artifact-refinement',
        stepId: 'keyword-research-step',
        stepType: 'keyword-research',
        objective: 'Improve keyword research artifact'
      };

      const selectedAgents = await collaborationEngine.selectAgentsForTask(
        task,
        ['seo-keyword', 'market-research']
      );

      expect(selectedAgents).toContain('seo-keyword');
      expect(selectedAgents.length).toBeGreaterThan(0);
    });

    test('should return empty array when no relevant agents available', async () => {
      const task = {
        type: 'artifact-refinement',
        stepId: 'unknown-step',
        stepType: 'unknown-type',
        objective: 'Unknown task'
      };

      const selectedAgents = await collaborationEngine.selectAgentsForTask(
        task,
        ['unknown-agent']
      );

      expect(selectedAgents).toHaveLength(0);
    });
  });

  describe('Collaboration Session', () => {
    beforeEach(() => {
      collaborationEngine.registerAgent(mockSeoAgent);
      collaborationEngine.registerAgent(mockMarketAgent);
    });

    test('should start collaboration session successfully', async () => {
      const task = {
        type: 'artifact-refinement',
        stepId: 'content-creation',
        stepType: 'content-creation',
        objective: 'Create high-quality content through collaboration'
      };

      const context = {
        initialArtifact: {
          id: 'test-artifact',
          type: 'content',
          content: 'Initial content about AI startups',
          metadata: {}
        },
        stepContext: {
          topic: 'AI startups 2025',
          targetAudience: 'entrepreneurs'
        },
        workflowContext: {},
        qualityThreshold: 0.8
      };

      const result = await collaborationEngine.startCollaboration(
        task,
        ['seo-keyword', 'market-research'],
        context
      );

      expect(result).toBeDefined();
      expect(result.artifact).toBeDefined();
      expect(result.consensus).toBeDefined();
      expect(result.rounds).toBeDefined();
      expect(result.session).toBeDefined();
      expect(result.consensus.confidence).toBeGreaterThan(0);
    });

    test('should handle collaboration with single agent', async () => {
      const task = {
        type: 'artifact-refinement',
        stepId: 'seo-optimization',
        stepType: 'seo-optimization',
        objective: 'Optimize content for SEO'
      };

      const context = {
        initialArtifact: {
          id: 'test-artifact',
          type: 'content',
          content: 'Content needing SEO optimization',
          metadata: {}
        },
        stepContext: { topic: 'SEO best practices' },
        workflowContext: {},
        qualityThreshold: 0.7
      };

      const result = await collaborationEngine.startCollaboration(
        task,
        ['seo-keyword'],
        context
      );

      expect(result).toBeDefined();
      expect(result.rounds).toHaveLength(1); // Single agent, single round
      expect(result.consensus.confidence).toBeGreaterThan(0);
    });
  });

  describe('Consensus Building', () => {
    beforeEach(() => {
      collaborationEngine.registerAgent(mockSeoAgent);
      collaborationEngine.registerAgent(mockMarketAgent);
    });

    test('should build consensus from multiple agent inputs', async () => {
      const rounds = [
        {
          number: 1,
          agentInputs: new Map([
            ['seo-keyword', {
              agentId: 'seo-keyword',
              roundNumber: 1,
              analysis: { keywords: ['AI', 'startups'] },
              suggestions: ['Use AI in title'],
              confidence: 0.85,
              reasoning: 'Strong SEO potential'
            }],
            ['market-research', {
              agentId: 'market-research',
              roundNumber: 1,
              analysis: { marketSize: 'large' },
              suggestions: ['Target B2B market'],
              confidence: 0.8,
              reasoning: 'Growing market demand'
            }]
          ]),
          peerReviews: new Map(),
          synthesizedResult: null
        }
      ];

      const consensus = await collaborationEngine.buildConsensus(rounds);

      expect(consensus).toBeDefined();
      expect(consensus.confidence).toBeGreaterThan(0);
      expect(consensus.agreements).toBeDefined();
      expect(consensus.finalRecommendations).toBeDefined();
    });
  });
});
