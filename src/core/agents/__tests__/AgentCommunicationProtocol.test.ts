/**
 * Agent Communication Protocol Tests
 * 
 * Test-driven development for agent-to-agent communication using the existing A2A protocol
 */

import { AgentCommunicationProtocol } from '../AgentCommunicationProtocol';
import { AgentMessageBus } from '../../../app/(payload)/api/agents/agentCommunication';

// Mock the AgentMessageBus
jest.mock('../../../app/(payload)/api/agents/agentCommunication');

describe('AgentCommunicationProtocol', () => {
  let protocol: AgentCommunicationProtocol;
  let mockMessageBus: jest.Mocked<AgentMessageBus>;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock AgentMessageBus instance
    mockMessageBus = {
      publishMessage: jest.fn(),
      subscribeToAgent: jest.fn(),
      getMessageHistory: jest.fn().mockReturnValue([]),
      getInstance: jest.fn()
    } as any;

    (AgentMessageBus.getInstance as jest.Mock).mockReturnValue(mockMessageBus);
    
    protocol = new AgentCommunicationProtocol();
  });

  describe('Message Sending', () => {
    test('should send message between agents', async () => {
      const message = {
        type: 'suggestion' as const,
        content: { suggestion: 'Improve SEO keywords' },
        timestamp: new Date().toISOString(),
        requiresResponse: false
      };

      await protocol.sendMessage(
        'seo-keyword',
        'content-strategy',
        message,
        'conv-123'
      );

      expect(mockMessageBus.publishMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          from: 'seo-keyword',
          to: 'content-strategy',
          type: 'suggestion',
          content: { suggestion: 'Improve SEO keywords' }
        })
      );
    });

    test('should create conversation if not exists', async () => {
      const message = {
        type: 'question' as const,
        content: { question: 'What is the target audience?' },
        timestamp: new Date().toISOString(),
        requiresResponse: true
      };

      await protocol.sendMessage(
        'market-research',
        'content-strategy',
        message,
        'new-conv'
      );

      const conversation = protocol.getConversation('new-conv');
      expect(conversation).toBeDefined();
      expect(conversation?.messages).toHaveLength(1);
    });

    test('should add message to existing conversation', async () => {
      const conversationId = 'existing-conv';
      
      // Send first message
      await protocol.sendMessage(
        'agent1',
        'agent2',
        { type: 'suggestion', content: 'First message', timestamp: new Date().toISOString() },
        conversationId
      );

      // Send second message
      await protocol.sendMessage(
        'agent2',
        'agent1',
        { type: 'suggestion', content: 'Second message', timestamp: new Date().toISOString() },
        conversationId
      );

      const conversation = protocol.getConversation(conversationId);
      expect(conversation?.messages).toHaveLength(2);
    });
  });

  describe('Consensus Building', () => {
    test('should request consensus from multiple agents', async () => {
      const proposal = {
        id: 'proposal-1',
        title: 'Content Structure Proposal',
        description: 'Proposed structure for the article',
        proposedChanges: [
          { type: 'add-section' as const, content: 'Introduction' },
          { type: 'add-section' as const, content: 'Main Content' }
        ],
        reasoning: 'Better content flow',
        confidence: 0.8
      };

      const participantAgents = ['seo-keyword', 'market-research', 'content-strategy'];

      // Mock the consensus collection to return immediate responses
      const mockResponses = [
        { agentId: 'seo-keyword', response: { agreement: 0.9, feedback: 'Good SEO structure' } },
        { agentId: 'market-research', response: { agreement: 0.8, feedback: 'Aligns with market needs' } },
        { agentId: 'content-strategy', response: { agreement: 0.85, feedback: 'Good content strategy' } }
      ];

      // Mock the private method by overriding it
      (protocol as any).collectConsensusResponses = jest.fn().mockResolvedValue(mockResponses);

      const result = await protocol.requestConsensus(
        'initiator-agent',
        participantAgents,
        proposal,
        'consensus-conv'
      );

      expect(result).toBeDefined();
      expect(result.consensusReached).toBe(true);
      expect(result.averageAgreement).toBeCloseTo(0.85); // (0.9 + 0.8 + 0.85) / 3
      expect(result.participantResponses).toHaveLength(3);
    });

    test('should handle consensus timeout', async () => {
      const proposal = {
        id: 'proposal-timeout',
        title: 'Timeout Test',
        description: 'Test proposal for timeout',
        proposedChanges: [],
        reasoning: 'Testing timeout behavior',
        confidence: 0.7
      };

      // Mock no responses (timeout scenario)
      mockMessageBus.getMessageHistory.mockReturnValue([]);

      const result = await protocol.requestConsensus(
        'initiator-agent',
        ['slow-agent'],
        proposal,
        'timeout-conv',
        1000 // 1 second timeout
      );

      expect(result.consensusReached).toBe(false);
      expect(result.participantResponses).toHaveLength(0);
      expect(result.timeoutOccurred).toBe(true);
    });

    test('should calculate consensus correctly', async () => {
      const proposal = {
        id: 'proposal-calc',
        title: 'Calculation Test',
        description: 'Test consensus calculation',
        proposedChanges: [],
        reasoning: 'Testing calculation',
        confidence: 0.8
      };

      // Mock mixed responses
      const mockResponses = [
        { agentId: 'agent1', response: { agreement: 0.9, feedback: 'Strongly agree' } },
        { agentId: 'agent2', response: { agreement: 0.4, feedback: 'Some concerns' } }
      ];

      // Mock the private method
      (protocol as any).collectConsensusResponses = jest.fn().mockResolvedValue(mockResponses);

      const result = await protocol.requestConsensus(
        'initiator-agent',
        ['agent1', 'agent2'],
        proposal,
        'calc-conv'
      );

      expect(result.averageAgreement).toBe(0.65); // (0.9 + 0.4) / 2
      expect(result.consensusReached).toBe(false); // Below 0.7 threshold
    });
  });

  describe('Conversation Management', () => {
    test('should retrieve conversation by ID', () => {
      const conversationId = 'test-conv';
      
      // Create conversation by sending a message
      protocol.sendMessage(
        'agent1',
        'agent2',
        { type: 'suggestion', content: 'Test message', timestamp: new Date().toISOString() },
        conversationId
      );

      const conversation = protocol.getConversation(conversationId);
      expect(conversation).toBeDefined();
      expect(conversation?.id).toBe(conversationId);
    });

    test('should return undefined for non-existent conversation', () => {
      const conversation = protocol.getConversation('non-existent');
      expect(conversation).toBeUndefined();
    });

    test('should list all active conversations', async () => {
      // Create multiple conversations
      await protocol.sendMessage('agent1', 'agent2', 
        { type: 'suggestion', content: 'Message 1', timestamp: new Date().toISOString() }, 'conv1');
      await protocol.sendMessage('agent2', 'agent3', 
        { type: 'suggestion', content: 'Message 2', timestamp: new Date().toISOString() }, 'conv2');

      const conversations = protocol.getActiveConversations();
      expect(conversations).toHaveLength(2);
      expect(conversations.map(c => c.id)).toContain('conv1');
      expect(conversations.map(c => c.id)).toContain('conv2');
    });
  });

  describe('Message Broadcasting', () => {
    test('should broadcast message to multiple agents', async () => {
      const message = {
        type: 'suggestion' as const,
        content: { announcement: 'Important update' },
        timestamp: new Date().toISOString(),
        requiresResponse: false
      };

      await protocol.broadcastToGroup(
        'coordinator',
        ['agent1', 'agent2', 'agent3'],
        message,
        'broadcast-conv'
      );

      // Should call publishMessage for each target agent
      expect(mockMessageBus.publishMessage).toHaveBeenCalledTimes(3);
    });
  });
});
