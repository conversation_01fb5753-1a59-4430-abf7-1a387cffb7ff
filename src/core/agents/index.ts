/**
 * Fresh Dynamic Agent System - Main Export
 * 
 * Exports all components of the fresh dynamic agent consultation system
 */

// Core types and interfaces
export * from './types';

// Base agent class
export { BaseAgent } from './base-agent';

// Specialized agents
export { SeoKeywordAgent } from './seo-keyword-agent';
export { MarketResearchAgent } from './market-research-agent';
export { ContentStrategyAgent } from './content-strategy-agent';

// Workflow integration
export { WorkflowAgentBridge } from '../workflow/workflow-agent-bridge';
export { DynamicAgentConsultationService } from '../workflow/dynamic-agent-consultation-service';
export { EnhancedAIGenerationStep } from '../workflow/enhanced-ai-generation-step';

// Re-export workflow types for convenience
export type { AgentConsultationConfig, ConsultationTrigger } from '../workflow/types';
