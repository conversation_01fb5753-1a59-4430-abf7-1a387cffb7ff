/**
 * Agent Collaboration Engine
 * 
 * Core engine for managing multi-agent collaboration in workflow systems.
 * Enables dynamic agent selection, multi-round collaboration, and consensus building.
 */

import { BaseAgent } from './base-agent';
import { AgentId, AgentConsultationRequest } from './types';
import { AgentMessageBus } from '../../app/(payload)/api/agents/agentCommunication';

export interface CollaborationTask {
  type: 'artifact-refinement' | 'consensus-building' | 'quality-review';
  stepId: string;
  stepType: string;
  objective: string;
}

export interface CollaborationContext {
  initialArtifact: {
    id: string;
    type: string;
    content: any;
    metadata: Record<string, any>;
  };
  stepContext: Record<string, any>;
  workflowContext: Record<string, any>;
  qualityThreshold: number;
}

export interface AgentInput {
  agentId: string;
  roundNumber: number;
  analysis: any;
  suggestions: string[];
  confidence: number;
  reasoning: string;
  collaborationNotes?: string;
}

export interface PeerReview {
  reviewerId: string;
  reviewedAgentId: string;
  agreement: number; // 0-1 scale
  concerns: string[];
  suggestions: string[];
  synergies: string[];
}

export interface CollaborationRound {
  number: number;
  agentInputs: Map<string, AgentInput>;
  peerReviews: Map<string, PeerReview[]>;
  synthesizedResult: any;
}

export interface CollaborationSession {
  id: string;
  task: CollaborationTask;
  agents: string[];
  startedAt: string;
  status: 'active' | 'completed' | 'failed';
}

export interface ConsensusResult {
  confidence: number;
  agreements: string[];
  disagreements: string[];
  finalRecommendations: string[];
  qualityScore: number;
}

export interface CollaborationResult {
  artifact: any;
  consensus: ConsensusResult;
  rounds: CollaborationRound[];
  session: CollaborationSession;
}

export class AgentCollaborationEngine {
  private agents: Map<string, BaseAgent> = new Map();
  private collaborationHistory: CollaborationSession[] = [];
  private messageBus: AgentMessageBus;

  constructor() {
    this.messageBus = AgentMessageBus.getInstance();
  }

  /**
   * Register an agent for collaboration
   */
  registerAgent(agent: BaseAgent): void {
    const agentId = agent.getAgentId();
    if (!this.agents.has(agentId)) {
      this.agents.set(agentId, agent);
    }
  }

  /**
   * Get all registered agents
   */
  getRegisteredAgents(): BaseAgent[] {
    return Array.from(this.agents.values());
  }

  /**
   * Start a collaboration session
   */
  async startCollaboration(
    task: CollaborationTask,
    availableAgents: string[],
    context: CollaborationContext
  ): Promise<CollaborationResult> {
    // 1. Select relevant agents dynamically
    const selectedAgents = await this.selectAgentsForTask(task, availableAgents);
    
    if (selectedAgents.length === 0) {
      throw new Error('No relevant agents available for collaboration');
    }

    // 2. Create collaboration session
    const session: CollaborationSession = {
      id: `collab-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      task,
      agents: selectedAgents,
      startedAt: new Date().toISOString(),
      status: 'active'
    };

    // 3. Execute multi-round collaboration
    const rounds = await this.executeCollaborationRounds(session, context);

    // 4. Build consensus
    const consensus = await this.buildConsensus(rounds);

    // 5. Generate final artifact
    const artifact = await this.generateCollaborativeArtifact(consensus, context);

    // 6. Update session status
    session.status = 'completed';
    this.collaborationHistory.push(session);

    return { artifact, consensus, rounds, session };
  }

  /**
   * Select agents relevant for a specific task
   */
  async selectAgentsForTask(
    task: CollaborationTask,
    availableAgents: string[]
  ): Promise<string[]> {
    const relevantAgents: string[] = [];

    // Step type to agent mapping
    const stepTypeAgentMap: Record<string, string[]> = {
      'keyword-research': ['seo-keyword', 'market-research'],
      'content-creation': ['content-strategy', 'seo-keyword', 'market-research'],
      'seo-optimization': ['seo-keyword'],
      'market-analysis': ['market-research'],
      'content-strategy': ['content-strategy'],
      'human-review': ['content-strategy']
    };

    // Get base agents for step type
    const baseAgents = stepTypeAgentMap[task.stepType] || [];
    
    // Filter to only available agents
    for (const agentId of baseAgents) {
      if (availableAgents.includes(agentId) && this.agents.has(agentId)) {
        relevantAgents.push(agentId);
      }
    }

    return relevantAgents;
  }

  /**
   * Execute multiple rounds of collaboration
   */
  private async executeCollaborationRounds(
    session: CollaborationSession,
    context: CollaborationContext
  ): Promise<CollaborationRound[]> {
    const rounds: CollaborationRound[] = [];
    let currentArtifact = context.initialArtifact;

    const maxRounds = session.agents.length === 1 ? 1 : 3; // Single agent needs only one round

    for (let roundNum = 1; roundNum <= maxRounds; roundNum++) {
      const round: CollaborationRound = {
        number: roundNum,
        agentInputs: new Map(),
        peerReviews: new Map(),
        synthesizedResult: null
      };

      // Each agent provides input
      for (const agentId of session.agents) {
        const agent = this.agents.get(agentId);
        if (!agent) continue;

        const input = await this.getAgentInput(agent, {
          artifact: currentArtifact,
          previousRounds: rounds,
          context,
          roundNumber: roundNum
        });

        round.agentInputs.set(agentId, input);
      }

      // For multi-agent collaboration, get peer reviews
      if (session.agents.length > 1) {
        for (const agentId of session.agents) {
          const agent = this.agents.get(agentId);
          if (!agent) continue;

          const reviews = await this.getPeerReviews(agent, round.agentInputs);
          round.peerReviews.set(agentId, reviews);
        }
      }

      // Synthesize round results
      currentArtifact = await this.synthesizeRoundResults(round, currentArtifact);
      round.synthesizedResult = currentArtifact;
      rounds.push(round);

      // Check for convergence (simplified for now)
      if (await this.hasConverged(rounds)) {
        break;
      }
    }

    return rounds;
  }

  /**
   * Get input from a specific agent
   */
  private async getAgentInput(
    agent: BaseAgent,
    collaborationContext: {
      artifact: any;
      previousRounds: CollaborationRound[];
      context: CollaborationContext;
      roundNumber: number;
    }
  ): Promise<AgentInput> {
    const request: AgentConsultationRequest = {
      id: `consultation-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      agentId: agent.getAgentId(),
      question: this.generateCollaborationQuestion(agent.getAgentId(), collaborationContext),
      context: {
        ...collaborationContext.context.stepContext,
        artifact: collaborationContext.artifact,
        previousRounds: collaborationContext.previousRounds,
        roundNumber: collaborationContext.roundNumber
      },
      priority: 'high',
      timeoutMs: 30000
    };

    const response = await agent.processConsultation(request);

    return {
      agentId: agent.getAgentId(),
      roundNumber: collaborationContext.roundNumber,
      analysis: response.response,
      suggestions: response.suggestions?.map(s => s.suggestion) || [],
      confidence: response.confidence,
      reasoning: response.reasoning || 'No reasoning provided'
    };
  }

  /**
   * Get peer reviews from an agent
   */
  private async getPeerReviews(
    agent: BaseAgent,
    agentInputs: Map<string, AgentInput>
  ): Promise<PeerReview[]> {
    const reviews: PeerReview[] = [];
    const agentId = agent.getAgentId();

    for (const [inputAgentId, input] of agentInputs) {
      if (inputAgentId !== agentId) {
        // Simplified peer review - in real implementation, this would be more sophisticated
        reviews.push({
          reviewerId: agentId,
          reviewedAgentId: inputAgentId,
          agreement: Math.random() * 0.4 + 0.6, // 0.6-1.0 range
          concerns: [],
          suggestions: [`Consider integrating with ${agentId} recommendations`],
          synergies: [`Good alignment with ${agentId} approach`]
        });
      }
    }

    return reviews;
  }

  /**
   * Synthesize results from a collaboration round
   */
  private async synthesizeRoundResults(
    round: CollaborationRound,
    currentArtifact: any
  ): Promise<any> {
    // Combine all agent inputs and peer reviews
    const allSuggestions: string[] = [];
    let totalConfidence = 0;
    let agentCount = 0;

    for (const [agentId, input] of round.agentInputs) {
      allSuggestions.push(...input.suggestions);
      totalConfidence += input.confidence;
      agentCount++;
    }

    const averageConfidence = agentCount > 0 ? totalConfidence / agentCount : 0;

    // Create enhanced artifact
    return {
      ...currentArtifact,
      content: `${currentArtifact.content}\n\nCollaborative improvements from Round ${round.number}:\n${allSuggestions.join('\n')}`,
      metadata: {
        ...currentArtifact.metadata,
        collaborationRound: round.number,
        averageConfidence,
        agentCount,
        lastUpdated: new Date().toISOString()
      }
    };
  }

  /**
   * Check if collaboration has converged
   */
  private async hasConverged(rounds: CollaborationRound[]): Promise<boolean> {
    if (rounds.length < 2) return false;

    // Simple convergence check - in real implementation, this would be more sophisticated
    const lastRound = rounds[rounds.length - 1];
    const confidenceSum = Array.from(lastRound.agentInputs.values())
      .reduce((sum, input) => sum + input.confidence, 0);
    const averageConfidence = confidenceSum / lastRound.agentInputs.size;

    return averageConfidence > 0.85; // High confidence threshold
  }

  /**
   * Build consensus from collaboration rounds
   */
  async buildConsensus(rounds: CollaborationRound[]): Promise<ConsensusResult> {
    const allSuggestions: string[] = [];
    const allConfidences: number[] = [];
    const agreements: string[] = [];

    for (const round of rounds) {
      for (const [agentId, input] of round.agentInputs) {
        allSuggestions.push(...input.suggestions);
        allConfidences.push(input.confidence);
        agreements.push(`${agentId}: ${input.reasoning}`);
      }
    }

    const averageConfidence = allConfidences.length > 0 
      ? allConfidences.reduce((sum, conf) => sum + conf, 0) / allConfidences.length 
      : 0;

    return {
      confidence: averageConfidence,
      agreements,
      disagreements: [], // Simplified for now
      finalRecommendations: [...new Set(allSuggestions)], // Remove duplicates
      qualityScore: averageConfidence * 100
    };
  }

  /**
   * Generate collaboration question for an agent
   */
  private generateCollaborationQuestion(
    agentId: string,
    collaborationContext: {
      artifact: any;
      previousRounds: CollaborationRound[];
      context: CollaborationContext;
      roundNumber: number;
    }
  ): string {
    const { artifact, context, roundNumber } = collaborationContext;
    const topic = context.stepContext.topic || 'content';

    const baseQuestions: Record<string, string> = {
      'seo-keyword': `Analyze and improve the SEO optimization for "${topic}". Provide keyword recommendations and content structure suggestions.`,
      'market-research': `Conduct market analysis for "${topic}". Provide insights on target audience, market trends, and competitive positioning.`,
      'content-strategy': `Develop content strategy recommendations for "${topic}". Focus on structure, messaging, and audience engagement.`
    };

    const question = baseQuestions[agentId] || `Provide expert analysis and recommendations for "${topic}".`;

    if (roundNumber > 1) {
      return `${question} Consider previous collaboration rounds and build upon peer agent insights.`;
    }

    return question;
  }

  /**
   * Generate final collaborative artifact
   */
  private async generateCollaborativeArtifact(
    consensus: ConsensusResult,
    context: CollaborationContext
  ): Promise<any> {
    return {
      ...context.initialArtifact,
      content: `${context.initialArtifact.content}\n\nFinal collaborative recommendations:\n${consensus.finalRecommendations.join('\n')}`,
      metadata: {
        ...context.initialArtifact.metadata,
        collaborationComplete: true,
        consensusConfidence: consensus.confidence,
        qualityScore: consensus.qualityScore,
        finalizedAt: new Date().toISOString()
      }
    };
  }
}
