# Fresh Dynamic Agent Consultation System

A comprehensive, fresh implementation of a dynamic agent consultation system that integrates seamlessly with the workflow engine to provide intelligent content enhancement through specialized AI agents.

## 🎯 Overview

This system provides dynamic consultation capabilities where workflow steps can automatically consult specialized agents based on context, feedback, and content requirements. It's built from scratch to avoid dependencies on the existing goal-based-collaboration codebase.

## 🏗️ Architecture

### Core Components

1. **Base Agent System** (`base-agent.ts`)
   - Abstract base class for all agents
   - Common functionality for consultation processing
   - Error handling and metrics tracking
   - Configuration management

2. **Specialized Agents**
   - **SEO Keyword Agent** (`seo-keyword-agent.ts`) - Keyword research and SEO optimization
   - **Market Research Agent** (`market-research-agent.ts`) - Market analysis and competitor research
   - **Content Strategy Agent** (`content-strategy-agent.ts`) - Content planning and strategy

3. **Workflow Integration**
   - **Workflow Agent Bridge** (`workflow-agent-bridge.ts`) - Bridges agents with workflow system
   - **Dynamic Consultation Service** (`dynamic-agent-consultation-service.ts`) - Main service orchestrator
   - **Enhanced AI Generation Step** (`enhanced-ai-generation-step.ts`) - AI generation with agent consultation

## 🚀 Key Features

### Dynamic Agent Selection
- **Content-type based**: Automatically selects relevant agents based on content type
- **Context-aware**: Analyzes context to determine which agents can provide value
- **Feedback-driven**: Responds to human feedback by consulting appropriate agents
- **Complexity-based**: Engages strategy agents for complex content

### Intelligent Consultation Triggers
- **Always**: Trigger consultation for every execution
- **Quality Threshold**: Trigger when content quality falls below threshold
- **Feedback Keywords**: Trigger when specific keywords appear in feedback
- **Content Complexity**: Trigger when content complexity exceeds threshold

### Multi-Agent Coordination
- **Parallel Processing**: Multiple agents can be consulted simultaneously
- **Timeout Management**: Configurable timeouts with graceful fallbacks
- **Error Resilience**: Continues operation even if some agents fail
- **Result Aggregation**: Combines insights from multiple agents intelligently

## 📋 Usage Examples

### Basic Agent Consultation

```typescript
import { 
  DynamicAgentConsultationService, 
  WorkflowAgentBridge,
  SeoKeywordAgent,
  MarketResearchAgent 
} from '@/core/agents';

// Initialize agents
const seoAgent = new SeoKeywordAgent();
const marketAgent = new MarketResearchAgent();

// Create bridge and service
const bridge = new WorkflowAgentBridge({
  'seo-keyword': seoAgent,
  'market-research': marketAgent
});

const consultationService = new DynamicAgentConsultationService(bridge);

// Consult single agent
const result = await consultationService.consultAgent(
  'seo-keyword',
  'workflow-123',
  'keyword-research',
  {
    topic: 'sustainable fashion trends',
    targetAudience: 'eco-conscious consumers'
  }
);
```

### Workflow Integration

```typescript
import { EnhancedAIGenerationStep } from '@/core/agents';

const enhancedStep = new EnhancedAIGenerationStep();

const step = {
  id: 'content-creation',
  type: 'AI_GENERATION',
  consultationConfig: {
    enabled: true,
    triggers: [
      {
        type: 'always',
        agents: ['seo-keyword', 'market-research'],
        priority: 'high'
      }
    ],
    maxConsultations: 2,
    timeoutMs: 30000,
    fallbackBehavior: 'continue'
  },
  // ... other step configuration
};

const result = await enhancedStep.executeAIGenerationWithConsultation(
  step,
  inputs,
  'execution-123'
);
```

### Agent Configuration

```typescript
// Configure consultation triggers
const consultationConfig = {
  enabled: true,
  triggers: [
    {
      type: 'quality_threshold',
      condition: { threshold: 0.8 },
      agents: ['content-strategy'],
      priority: 'medium'
    },
    {
      type: 'feedback_keywords',
      condition: { keywords: ['seo', 'keywords'] },
      agents: ['seo-keyword'],
      priority: 'high'
    }
  ],
  maxConsultations: 3,
  timeoutMs: 30000,
  fallbackBehavior: 'continue'
};
```

## 🧪 Testing

The system includes comprehensive test suites:

### Test Files
- `tests/integration/dynamic-agent-consultation.test.ts` - Core agent system tests
- `tests/integration/enhanced-ai-generation.test.ts` - Workflow integration tests

### Running Tests
```bash
# Run all agent system tests
npm test -- tests/integration/dynamic-agent-consultation.test.ts

# Run workflow integration tests
npm test -- tests/integration/enhanced-ai-generation.test.ts

# Run with open handles detection
npm test -- tests/integration/dynamic-agent-consultation.test.ts --detectOpenHandles
```

## 📊 Monitoring and Metrics

### Consultation Metrics
```typescript
const metrics = consultationService.getMetrics();
// Returns:
// {
//   totalConsultations: number,
//   successfulConsultations: number,
//   failedConsultations: number,
//   averageResponseTime: number,
//   averageConfidence: number,
//   successRate: number,
//   agentUtilization: Record<AgentId, number>
// }
```

### Health Monitoring
```typescript
const healthCheck = await agentBridge.performHealthCheck();
// Returns overall system health and individual agent status
```

## 🔧 Configuration

### Agent Configuration
Each agent can be configured with:
- `maxConcurrentConsultations`: Maximum parallel consultations
- `defaultTimeoutMs`: Default timeout for consultations
- `retryAttempts`: Number of retry attempts on failure
- `enabled`: Whether the agent is enabled

### Consultation Configuration
- `enabled`: Enable/disable consultation for a step
- `triggers`: Array of trigger conditions
- `maxConsultations`: Maximum number of agents to consult
- `timeoutMs`: Timeout for consultation requests
- `fallbackBehavior`: Behavior when consultations fail ('continue', 'fail', 'retry')

## 🚦 Error Handling

The system provides robust error handling:

### Error Types
- `AgentError`: General agent-related errors
- `ConsultationTimeoutError`: Consultation timeout errors
- `AgentUnavailableError`: Agent unavailability errors

### Fallback Behaviors
- **Continue**: Proceed with workflow even if consultations fail
- **Fail**: Stop workflow execution if consultations fail
- **Retry**: Attempt consultation retry with backoff

## 🔄 Integration with Existing Workflow System

The fresh agent system integrates with the existing workflow engine through:

1. **Enhanced AI Generation Steps**: Augments existing AI generation with agent consultation
2. **Consultation Configuration**: Extends workflow step configuration with agent consultation options
3. **Result Enhancement**: Enriches AI generation outputs with agent insights
4. **Metrics Integration**: Provides consultation metrics alongside workflow metrics

## 🎯 Benefits

### Content Quality Improvement
- **SEO Optimization**: Automatic keyword research and SEO recommendations
- **Market Alignment**: Content aligned with market trends and audience insights
- **Strategic Planning**: Well-structured content following strategic guidelines

### Workflow Enhancement
- **Intelligent Automation**: Reduces manual review cycles through proactive agent consultation
- **Context Awareness**: Adapts consultation strategy based on content context and feedback
- **Scalable Architecture**: Supports addition of new specialized agents

### Developer Experience
- **Type Safety**: Full TypeScript support with comprehensive type definitions
- **Modular Design**: Clean separation of concerns with pluggable agent architecture
- **Comprehensive Testing**: Extensive test coverage for reliable operation

## 🔮 Future Enhancements

- **Machine Learning Integration**: Learn from consultation patterns to improve agent selection
- **External API Integration**: Connect with real SEO tools, market research APIs
- **Advanced Analytics**: Detailed consultation analytics and performance insights
- **Custom Agent Development**: Framework for creating domain-specific agents
