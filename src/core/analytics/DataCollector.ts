/**
 * Data Collector for Collaboration Analytics
 *
 * Collects and aggregates collaboration data for analysis and reporting
 */

export interface CollaborationData {
  id: string;
  qualityScore: number;
  consensusConfidence: number;
  totalTime: number;
  agentCount: number;
  roundCount: number;
  humanInterventionRequired: boolean;
  timestamp: string;
  agentParticipation: Record<string, number>;
  conflictCount: number;
  resolutionTime: number;
  errorCount: number;
  successfulResolutions: number;
}

export interface TimeRange {
  start: string;
  end: string;
}

export interface PerformanceData {
  averageLatency: number;
  throughput: number;
  errorRate: number;
  uptime: number;
  resourceUtilization: number;
}

export interface QualityMetrics {
  averageQuality: number;
  qualityVariance: number;
  trendDirection: 'improving' | 'stable' | 'declining';
  consensusStability: number;
  improvementRate: number;
}

export class DataCollector {
  private collaborationHistory: CollaborationData[] = [];
  private performanceMetrics: PerformanceData[] = [];
  private qualityHistory: QualityMetrics[] = [];

  /**
   * Record a collaboration session
   */
  async recordCollaboration(data: Partial<CollaborationData>): Promise<void> {
    const collaborationRecord: CollaborationData = {
      id: data.id || this.generateId(),
      qualityScore: data.qualityScore || 0.8,
      consensusConfidence: data.consensusConfidence || 0.75,
      totalTime: data.totalTime || 5000,
      agentCount: data.agentCount || 3,
      roundCount: data.roundCount || 2,
      humanInterventionRequired: data.humanInterventionRequired || false,
      timestamp: data.timestamp || new Date().toISOString(),
      agentParticipation: data.agentParticipation || {},
      conflictCount: data.conflictCount || 0,
      resolutionTime: data.resolutionTime || 1000,
      errorCount: data.errorCount || 0,
      successfulResolutions: data.successfulResolutions || 1
    };

    this.collaborationHistory.push(collaborationRecord);

    // Keep only last 1000 records to prevent memory issues
    if (this.collaborationHistory.length > 1000) {
      this.collaborationHistory = this.collaborationHistory.slice(-1000);
    }
  }

  /**
   * Get collaboration data for a specific time range
   */
  async getCollaborationData(timeRange: TimeRange): Promise<CollaborationData[]> {
    const startTime = new Date(timeRange.start);
    const endTime = new Date(timeRange.end);

    return this.collaborationHistory.filter(record => {
      const recordTime = new Date(record.timestamp);
      return recordTime >= startTime && recordTime <= endTime;
    });
  }

  /**
   * Get recent collaborations
   */
  async getRecentCollaborations(days: number): Promise<CollaborationData[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    return this.collaborationHistory.filter(record => {
      const recordTime = new Date(record.timestamp);
      return recordTime >= cutoffDate;
    });
  }

  /**
   * Get all collaboration data
   */
  async getAllCollaborationData(): Promise<CollaborationData[]> {
    return [...this.collaborationHistory];
  }

  /**
   * Get performance data
   */
  async getPerformanceData(): Promise<PerformanceData> {
    if (this.collaborationHistory.length === 0) {
      return {
        averageLatency: 0,
        throughput: 0,
        errorRate: 0,
        uptime: 1.0,
        resourceUtilization: 0.5
      };
    }

    const recentData = this.collaborationHistory.slice(-100); // Last 100 records
    
    const averageLatency = recentData.reduce((sum, record) => sum + record.totalTime, 0) / recentData.length;
    const throughput = this.calculateThroughput(recentData);
    const errorRate = this.calculateErrorRate(recentData);
    const uptime = this.calculateUptime(recentData);
    const resourceUtilization = this.calculateResourceUtilization(recentData);

    return {
      averageLatency,
      throughput,
      errorRate,
      uptime,
      resourceUtilization
    };
  }

  /**
   * Record performance metrics
   */
  async recordPerformanceMetrics(metrics: PerformanceData): Promise<void> {
    this.performanceMetrics.push(metrics);

    // Keep only last 100 performance records
    if (this.performanceMetrics.length > 100) {
      this.performanceMetrics = this.performanceMetrics.slice(-100);
    }
  }

  /**
   * Get quality trends
   */
  async getQualityTrends(days: number = 30): Promise<QualityMetrics> {
    const recentData = await this.getRecentCollaborations(days);
    
    if (recentData.length === 0) {
      return {
        averageQuality: 0.8,
        qualityVariance: 0.1,
        trendDirection: 'stable',
        consensusStability: 0.75,
        improvementRate: 0
      };
    }

    const qualityScores = recentData.map(record => record.qualityScore);
    const consensusScores = recentData.map(record => record.consensusConfidence);

    return {
      averageQuality: this.calculateAverage(qualityScores),
      qualityVariance: this.calculateVariance(qualityScores),
      trendDirection: this.calculateTrend(qualityScores),
      consensusStability: this.calculateStability(consensusScores),
      improvementRate: this.calculateImprovementRate(qualityScores)
    };
  }

  /**
   * Get agent performance data
   */
  async getAgentPerformanceData(): Promise<Record<string, any>> {
    const agentStats: Record<string, any> = {};

    this.collaborationHistory.forEach(record => {
      Object.entries(record.agentParticipation).forEach(([agentId, participation]) => {
        if (!agentStats[agentId]) {
          agentStats[agentId] = {
            totalParticipations: 0,
            totalParticipationScore: 0,
            qualityContributions: [],
            averageResponseTime: 0
          };
        }

        agentStats[agentId].totalParticipations++;
        agentStats[agentId].totalParticipationScore += participation;
        agentStats[agentId].qualityContributions.push(record.qualityScore);
      });
    });

    // Calculate averages
    Object.keys(agentStats).forEach(agentId => {
      const stats = agentStats[agentId];
      stats.averageParticipation = stats.totalParticipationScore / stats.totalParticipations;
      stats.averageQualityContribution = this.calculateAverage(stats.qualityContributions);
    });

    return agentStats;
  }

  /**
   * Clear old data
   */
  async clearOldData(days: number): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    this.collaborationHistory = this.collaborationHistory.filter(record => {
      const recordTime = new Date(record.timestamp);
      return recordTime >= cutoffDate;
    });
  }

  /**
   * Export data for backup
   */
  async exportData(): Promise<{
    collaborationHistory: CollaborationData[];
    performanceMetrics: PerformanceData[];
    exportTimestamp: string;
  }> {
    return {
      collaborationHistory: [...this.collaborationHistory],
      performanceMetrics: [...this.performanceMetrics],
      exportTimestamp: new Date().toISOString()
    };
  }

  // Private helper methods

  private generateId(): string {
    return `collab-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  }

  private calculateThroughput(data: CollaborationData[]): number {
    if (data.length === 0) return 0;

    const timeSpan = new Date(data[data.length - 1].timestamp).getTime() - 
                    new Date(data[0].timestamp).getTime();
    const hours = timeSpan / (1000 * 60 * 60);
    
    return hours > 0 ? data.length / hours : 0;
  }

  private calculateErrorRate(data: CollaborationData[]): number {
    if (data.length === 0) return 0;

    const totalErrors = data.reduce((sum, record) => sum + record.errorCount, 0);
    return totalErrors / data.length;
  }

  private calculateUptime(data: CollaborationData[]): number {
    // Simple uptime calculation based on successful collaborations
    if (data.length === 0) return 1.0;

    const successfulCollaborations = data.filter(record => record.errorCount === 0).length;
    return successfulCollaborations / data.length;
  }

  private calculateResourceUtilization(data: CollaborationData[]): number {
    if (data.length === 0) return 0.5;

    // Calculate based on agent count and round count
    const avgAgentCount = this.calculateAverage(data.map(r => r.agentCount));
    const avgRoundCount = this.calculateAverage(data.map(r => r.roundCount));
    
    // Normalize to 0-1 scale
    return Math.min((avgAgentCount * avgRoundCount) / 15, 1.0);
  }

  private calculateAverage(values: number[]): number {
    if (values.length === 0) return 0;
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  private calculateVariance(values: number[]): number {
    if (values.length === 0) return 0;
    
    const avg = this.calculateAverage(values);
    const squaredDiffs = values.map(val => Math.pow(val - avg, 2));
    return this.calculateAverage(squaredDiffs);
  }

  private calculateTrend(values: number[]): 'improving' | 'stable' | 'declining' {
    if (values.length < 2) return 'stable';

    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));

    const firstAvg = this.calculateAverage(firstHalf);
    const secondAvg = this.calculateAverage(secondHalf);

    const improvement = (secondAvg - firstAvg) / firstAvg;

    if (improvement > 0.05) return 'improving';
    if (improvement < -0.05) return 'declining';
    return 'stable';
  }

  private calculateStability(values: number[]): number {
    if (values.length === 0) return 0.75;
    
    const variance = this.calculateVariance(values);
    // Lower variance means higher stability
    return Math.max(0, 1 - variance);
  }

  private calculateImprovementRate(values: number[]): number {
    if (values.length < 2) return 0;

    const firstValue = values[0];
    const lastValue = values[values.length - 1];
    
    return firstValue > 0 ? (lastValue - firstValue) / firstValue : 0;
  }
}
