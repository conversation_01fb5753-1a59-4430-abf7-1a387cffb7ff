/**
 * Trend Analyzer for Collaboration Analytics
 *
 * Analyzes trends and patterns in collaboration data over time
 */

import { CollaborationData, DataPoint } from './MetricsCalculator';

export interface TrendAnalysis {
  direction: 'improving' | 'stable' | 'declining';
  strength: 'weak' | 'moderate' | 'strong';
  confidence: number;
  slope: number;
  correlation: number;
}

export interface SeasonalPattern {
  pattern: 'daily' | 'weekly' | 'monthly';
  strength: number;
  peakPeriods: string[];
  lowPeriods: string[];
}

export interface AnomalyDetection {
  anomalies: Array<{
    timestamp: string;
    value: number;
    expectedValue: number;
    severity: 'low' | 'medium' | 'high';
    type: 'spike' | 'drop' | 'outlier';
  }>;
  anomalyRate: number;
}

export interface ForecastResult {
  predictions: DataPoint[];
  confidence: number;
  method: 'linear' | 'exponential' | 'seasonal';
  accuracy: number;
}

export class TrendAnalyzer {
  /**
   * Analyze trend in a time series of values
   */
  analyzeTrend(dataPoints: DataPoint[]): TrendAnalysis {
    if (dataPoints.length < 2) {
      return {
        direction: 'stable',
        strength: 'weak',
        confidence: 0,
        slope: 0,
        correlation: 0
      };
    }

    const values = dataPoints.map(p => p.value);
    const timestamps = dataPoints.map(p => new Date(p.timestamp).getTime());
    
    // Calculate linear regression
    const regression = this.calculateLinearRegression(timestamps, values);
    
    // Determine trend direction
    const direction = this.determineTrendDirection(regression.slope);
    
    // Calculate trend strength
    const strength = this.calculateTrendStrength(regression.correlation);
    
    // Calculate confidence based on R-squared and data points
    const confidence = this.calculateTrendConfidence(regression.rSquared, dataPoints.length);

    return {
      direction,
      strength,
      confidence,
      slope: regression.slope,
      correlation: regression.correlation
    };
  }

  /**
   * Detect seasonal patterns in data
   */
  detectSeasonalPatterns(dataPoints: DataPoint[]): SeasonalPattern[] {
    const patterns: SeasonalPattern[] = [];

    // Analyze daily patterns
    const dailyPattern = this.analyzeDailyPattern(dataPoints);
    if (dailyPattern.strength > 0.3) {
      patterns.push(dailyPattern);
    }

    // Analyze weekly patterns
    const weeklyPattern = this.analyzeWeeklyPattern(dataPoints);
    if (weeklyPattern.strength > 0.3) {
      patterns.push(weeklyPattern);
    }

    // Analyze monthly patterns
    const monthlyPattern = this.analyzeMonthlyPattern(dataPoints);
    if (monthlyPattern.strength > 0.3) {
      patterns.push(monthlyPattern);
    }

    return patterns;
  }

  /**
   * Detect anomalies in the data
   */
  detectAnomalies(dataPoints: DataPoint[]): AnomalyDetection {
    if (dataPoints.length < 10) {
      return {
        anomalies: [],
        anomalyRate: 0
      };
    }

    const values = dataPoints.map(p => p.value);
    const mean = this.calculateMean(values);
    const stdDev = this.calculateStandardDeviation(values);
    
    const anomalies: AnomalyDetection['anomalies'] = [];

    dataPoints.forEach((point, index) => {
      const zScore = Math.abs((point.value - mean) / stdDev);
      
      if (zScore > 3) { // 3 sigma rule
        const expectedValue = this.calculateExpectedValue(dataPoints, index);
        const severity = this.determineSeverity(zScore);
        const type = this.determineAnomalyType(point.value, expectedValue, mean);

        anomalies.push({
          timestamp: point.timestamp,
          value: point.value,
          expectedValue,
          severity,
          type
        });
      }
    });

    return {
      anomalies,
      anomalyRate: anomalies.length / dataPoints.length
    };
  }

  /**
   * Forecast future values based on historical data
   */
  forecast(dataPoints: DataPoint[], periodsAhead: number): ForecastResult {
    if (dataPoints.length < 3) {
      return {
        predictions: [],
        confidence: 0,
        method: 'linear',
        accuracy: 0
      };
    }

    // Try different forecasting methods and pick the best one
    const linearForecast = this.linearForecast(dataPoints, periodsAhead);
    const exponentialForecast = this.exponentialForecast(dataPoints, periodsAhead);
    
    // Choose the method with better historical accuracy
    const linearAccuracy = this.calculateForecastAccuracy(dataPoints, 'linear');
    const exponentialAccuracy = this.calculateForecastAccuracy(dataPoints, 'exponential');

    if (linearAccuracy > exponentialAccuracy) {
      return {
        predictions: linearForecast,
        confidence: linearAccuracy,
        method: 'linear',
        accuracy: linearAccuracy
      };
    } else {
      return {
        predictions: exponentialForecast,
        confidence: exponentialAccuracy,
        method: 'exponential',
        accuracy: exponentialAccuracy
      };
    }
  }

  /**
   * Calculate correlation between two data series
   */
  calculateCorrelation(series1: number[], series2: number[]): number {
    if (series1.length !== series2.length || series1.length === 0) {
      return 0;
    }

    const mean1 = this.calculateMean(series1);
    const mean2 = this.calculateMean(series2);
    
    let numerator = 0;
    let sumSq1 = 0;
    let sumSq2 = 0;

    for (let i = 0; i < series1.length; i++) {
      const diff1 = series1[i] - mean1;
      const diff2 = series2[i] - mean2;
      
      numerator += diff1 * diff2;
      sumSq1 += diff1 * diff1;
      sumSq2 += diff2 * diff2;
    }

    const denominator = Math.sqrt(sumSq1 * sumSq2);
    return denominator === 0 ? 0 : numerator / denominator;
  }

  // Private helper methods

  private calculateLinearRegression(x: number[], y: number[]): {
    slope: number;
    intercept: number;
    correlation: number;
    rSquared: number;
  } {
    const n = x.length;
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);
    const sumYY = y.reduce((sum, yi) => sum + yi * yi, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;
    
    // Calculate correlation coefficient
    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));
    const correlation = denominator === 0 ? 0 : numerator / denominator;
    
    const rSquared = correlation * correlation;

    return { slope, intercept, correlation, rSquared };
  }

  private determineTrendDirection(slope: number): 'improving' | 'stable' | 'declining' {
    if (Math.abs(slope) < 0.001) return 'stable';
    return slope > 0 ? 'improving' : 'declining';
  }

  private calculateTrendStrength(correlation: number): 'weak' | 'moderate' | 'strong' {
    const absCorr = Math.abs(correlation);
    if (absCorr < 0.3) return 'weak';
    if (absCorr < 0.7) return 'moderate';
    return 'strong';
  }

  private calculateTrendConfidence(rSquared: number, dataPoints: number): number {
    // Confidence based on R-squared and sample size
    const baseConfidence = rSquared;
    const sampleSizeBonus = Math.min(dataPoints / 100, 0.2); // Max 20% bonus
    return Math.min(baseConfidence + sampleSizeBonus, 1.0);
  }

  private analyzeDailyPattern(dataPoints: DataPoint[]): SeasonalPattern {
    const hourlyAverages = new Array(24).fill(0);
    const hourlyCounts = new Array(24).fill(0);

    dataPoints.forEach(point => {
      const hour = new Date(point.timestamp).getHours();
      hourlyAverages[hour] += point.value;
      hourlyCounts[hour]++;
    });

    // Calculate averages
    for (let i = 0; i < 24; i++) {
      if (hourlyCounts[i] > 0) {
        hourlyAverages[i] /= hourlyCounts[i];
      }
    }

    const variance = this.calculateVariance(hourlyAverages);
    const strength = Math.min(variance / this.calculateMean(hourlyAverages), 1.0);

    return {
      pattern: 'daily',
      strength,
      peakPeriods: this.findPeakPeriods(hourlyAverages, 'hour'),
      lowPeriods: this.findLowPeriods(hourlyAverages, 'hour')
    };
  }

  private analyzeWeeklyPattern(dataPoints: DataPoint[]): SeasonalPattern {
    const dailyAverages = new Array(7).fill(0);
    const dailyCounts = new Array(7).fill(0);

    dataPoints.forEach(point => {
      const dayOfWeek = new Date(point.timestamp).getDay();
      dailyAverages[dayOfWeek] += point.value;
      dailyCounts[dayOfWeek]++;
    });

    // Calculate averages
    for (let i = 0; i < 7; i++) {
      if (dailyCounts[i] > 0) {
        dailyAverages[i] /= dailyCounts[i];
      }
    }

    const variance = this.calculateVariance(dailyAverages);
    const strength = Math.min(variance / this.calculateMean(dailyAverages), 1.0);

    return {
      pattern: 'weekly',
      strength,
      peakPeriods: this.findPeakPeriods(dailyAverages, 'day'),
      lowPeriods: this.findLowPeriods(dailyAverages, 'day')
    };
  }

  private analyzeMonthlyPattern(dataPoints: DataPoint[]): SeasonalPattern {
    const monthlyAverages = new Array(12).fill(0);
    const monthlyCounts = new Array(12).fill(0);

    dataPoints.forEach(point => {
      const month = new Date(point.timestamp).getMonth();
      monthlyAverages[month] += point.value;
      monthlyCounts[month]++;
    });

    // Calculate averages
    for (let i = 0; i < 12; i++) {
      if (monthlyCounts[i] > 0) {
        monthlyAverages[i] /= monthlyCounts[i];
      }
    }

    const variance = this.calculateVariance(monthlyAverages);
    const strength = Math.min(variance / this.calculateMean(monthlyAverages), 1.0);

    return {
      pattern: 'monthly',
      strength,
      peakPeriods: this.findPeakPeriods(monthlyAverages, 'month'),
      lowPeriods: this.findLowPeriods(monthlyAverages, 'month')
    };
  }

  private findPeakPeriods(values: number[], unit: string): string[] {
    const mean = this.calculateMean(values);
    const peaks: string[] = [];

    values.forEach((value, index) => {
      if (value > mean * 1.2) { // 20% above average
        peaks.push(`${unit}-${index}`);
      }
    });

    return peaks;
  }

  private findLowPeriods(values: number[], unit: string): string[] {
    const mean = this.calculateMean(values);
    const lows: string[] = [];

    values.forEach((value, index) => {
      if (value < mean * 0.8) { // 20% below average
        lows.push(`${unit}-${index}`);
      }
    });

    return lows;
  }

  private calculateExpectedValue(dataPoints: DataPoint[], index: number): number {
    // Simple moving average of surrounding points
    const windowSize = Math.min(5, dataPoints.length);
    const start = Math.max(0, index - Math.floor(windowSize / 2));
    const end = Math.min(dataPoints.length, start + windowSize);
    
    const values = dataPoints.slice(start, end).map(p => p.value);
    return this.calculateMean(values);
  }

  private determineSeverity(zScore: number): 'low' | 'medium' | 'high' {
    if (zScore > 4) return 'high';
    if (zScore > 3.5) return 'medium';
    return 'low';
  }

  private determineAnomalyType(value: number, expected: number, mean: number): 'spike' | 'drop' | 'outlier' {
    if (value > expected * 1.5) return 'spike';
    if (value < expected * 0.5) return 'drop';
    return 'outlier';
  }

  private linearForecast(dataPoints: DataPoint[], periods: number): DataPoint[] {
    const values = dataPoints.map(p => p.value);
    const timestamps = dataPoints.map(p => new Date(p.timestamp).getTime());
    
    const regression = this.calculateLinearRegression(timestamps, values);
    const lastTimestamp = timestamps[timestamps.length - 1];
    const timeInterval = timestamps.length > 1 ? 
      (timestamps[timestamps.length - 1] - timestamps[0]) / (timestamps.length - 1) : 
      3600000; // 1 hour default

    const predictions: DataPoint[] = [];
    for (let i = 1; i <= periods; i++) {
      const futureTimestamp = lastTimestamp + (timeInterval * i);
      const predictedValue = regression.slope * futureTimestamp + regression.intercept;
      
      predictions.push({
        timestamp: new Date(futureTimestamp).toISOString(),
        value: Math.max(0, predictedValue) // Ensure non-negative values
      });
    }

    return predictions;
  }

  private exponentialForecast(dataPoints: DataPoint[], periods: number): DataPoint[] {
    // Simple exponential smoothing
    const alpha = 0.3; // Smoothing parameter
    const values = dataPoints.map(p => p.value);
    
    let smoothedValue = values[0];
    for (let i = 1; i < values.length; i++) {
      smoothedValue = alpha * values[i] + (1 - alpha) * smoothedValue;
    }

    const lastTimestamp = new Date(dataPoints[dataPoints.length - 1].timestamp).getTime();
    const timeInterval = dataPoints.length > 1 ? 
      (new Date(dataPoints[dataPoints.length - 1].timestamp).getTime() - 
       new Date(dataPoints[0].timestamp).getTime()) / (dataPoints.length - 1) : 
      3600000;

    const predictions: DataPoint[] = [];
    for (let i = 1; i <= periods; i++) {
      const futureTimestamp = lastTimestamp + (timeInterval * i);
      
      predictions.push({
        timestamp: new Date(futureTimestamp).toISOString(),
        value: Math.max(0, smoothedValue)
      });
    }

    return predictions;
  }

  private calculateForecastAccuracy(dataPoints: DataPoint[], method: 'linear' | 'exponential'): number {
    if (dataPoints.length < 6) return 0.5; // Not enough data for validation

    // Use last 20% of data for validation
    const validationSize = Math.floor(dataPoints.length * 0.2);
    const trainingData = dataPoints.slice(0, -validationSize);
    const validationData = dataPoints.slice(-validationSize);

    let predictions: DataPoint[];
    if (method === 'linear') {
      predictions = this.linearForecast(trainingData, validationSize);
    } else {
      predictions = this.exponentialForecast(trainingData, validationSize);
    }

    // Calculate mean absolute percentage error (MAPE)
    let totalError = 0;
    for (let i = 0; i < Math.min(predictions.length, validationData.length); i++) {
      const actual = validationData[i].value;
      const predicted = predictions[i].value;
      if (actual !== 0) {
        totalError += Math.abs((actual - predicted) / actual);
      }
    }

    const mape = totalError / Math.min(predictions.length, validationData.length);
    return Math.max(0, 1 - mape); // Convert to accuracy (0-1)
  }

  private calculateMean(values: number[]): number {
    if (values.length === 0) return 0;
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  private calculateVariance(values: number[]): number {
    if (values.length === 0) return 0;
    
    const mean = this.calculateMean(values);
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    return this.calculateMean(squaredDiffs);
  }

  private calculateStandardDeviation(values: number[]): number {
    return Math.sqrt(this.calculateVariance(values));
  }
}
