/**
 * Metrics Calculator for Collaboration Analytics
 *
 * Calculates various performance and quality metrics from collaboration data
 */

import { CollaborationData, PerformanceData, QualityMetrics } from './DataCollector';

export interface PerformanceMetrics {
  throughput: ThroughputMetric;
  latency: LatencyMetric;
  reliability: ReliabilityMetric;
  efficiency: EfficiencyMetric;
}

export interface ThroughputMetric {
  collaborationsPerHour: number;
  collaborationsPerDay: number;
  peakThroughput: number;
}

export interface LatencyMetric {
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
}

export interface ReliabilityMetric {
  uptime: number;
  errorRate: number;
  timeoutRate: number;
}

export interface EfficiencyMetric {
  resourceUtilization: number;
  costPerCollaboration: number;
  qualityPerDollar: number;
}

export interface AnalyticsSummary {
  totalCollaborations: number;
  averageQualityScore: number;
  successRate: number;
  averageCollaborationTime: number;
  costSavings: number;
  humanInterventionRate: number;
}

export interface DataPoint {
  timestamp: string;
  value: number;
}

export class MetricsCalculator {
  /**
   * Calculate performance metrics from collaboration data
   */
  calculatePerformanceMetrics(collaborationData: CollaborationData[]): PerformanceMetrics {
    const responseTimes = collaborationData.map(c => c.totalTime);
    const throughputData = this.calculateThroughput(collaborationData);

    return {
      throughput: {
        collaborationsPerHour: throughputData.hourly,
        collaborationsPerDay: throughputData.daily,
        peakThroughput: throughputData.peak
      },
      latency: {
        averageResponseTime: this.calculateAverage(responseTimes),
        p95ResponseTime: this.calculatePercentile(responseTimes, 95),
        p99ResponseTime: this.calculatePercentile(responseTimes, 99)
      },
      reliability: {
        uptime: this.calculateUptime(collaborationData),
        errorRate: this.calculateErrorRate(collaborationData),
        timeoutRate: this.calculateTimeoutRate(collaborationData)
      },
      efficiency: {
        resourceUtilization: this.calculateResourceUtilization(collaborationData),
        costPerCollaboration: this.calculateCostPerCollaboration(collaborationData),
        qualityPerDollar: this.calculateQualityPerDollar(collaborationData)
      }
    };
  }

  /**
   * Calculate summary statistics
   */
  calculateSummary(collaborationData: CollaborationData[]): AnalyticsSummary {
    const totalCollaborations = collaborationData.length;
    const successfulCollaborations = collaborationData.filter(c => c.qualityScore > 0.8).length;
    const totalTime = collaborationData.reduce((sum, c) => sum + c.totalTime, 0);
    const humanInterventions = collaborationData.filter(c => c.humanInterventionRequired).length;

    return {
      totalCollaborations,
      averageQualityScore: this.calculateAverage(collaborationData.map(c => c.qualityScore)),
      successRate: totalCollaborations > 0 ? successfulCollaborations / totalCollaborations : 0,
      averageCollaborationTime: totalCollaborations > 0 ? totalTime / totalCollaborations : 0,
      costSavings: this.calculateCostSavings(collaborationData),
      humanInterventionRate: totalCollaborations > 0 ? humanInterventions / totalCollaborations : 0
    };
  }

  /**
   * Calculate quality trends over time
   */
  calculateQualityTrends(collaborationData: CollaborationData[]): {
    qualityOverTime: DataPoint[];
    consensusConfidenceOverTime: DataPoint[];
    conflictRateOverTime: DataPoint[];
    improvementRate: number;
  } {
    const qualityOverTime = this.createTimeSeriesData(
      collaborationData,
      (record) => record.qualityScore
    );

    const consensusConfidenceOverTime = this.createTimeSeriesData(
      collaborationData,
      (record) => record.consensusConfidence
    );

    const conflictRateOverTime = this.createTimeSeriesData(
      collaborationData,
      (record) => record.conflictCount / Math.max(record.agentCount, 1)
    );

    const improvementRate = this.calculateImprovementRate(
      collaborationData.map(c => c.qualityScore)
    );

    return {
      qualityOverTime,
      consensusConfidenceOverTime,
      conflictRateOverTime,
      improvementRate
    };
  }

  /**
   * Calculate agent performance analytics
   */
  calculateAgentAnalytics(collaborationData: CollaborationData[]): Array<{
    agentId: string;
    participationRate: number;
    averageConfidence: number;
    successContribution: number;
    collaborationEffectiveness: number;
    strengths: string[];
    improvementAreas: string[];
  }> {
    const agentStats = new Map<string, {
      participations: number;
      totalConfidence: number;
      successfulCollaborations: number;
      totalCollaborations: number;
      qualityContributions: number[];
    }>();

    // Collect agent statistics
    collaborationData.forEach(record => {
      Object.entries(record.agentParticipation).forEach(([agentId, participation]) => {
        if (!agentStats.has(agentId)) {
          agentStats.set(agentId, {
            participations: 0,
            totalConfidence: 0,
            successfulCollaborations: 0,
            totalCollaborations: 0,
            qualityContributions: []
          });
        }

        const stats = agentStats.get(agentId)!;
        stats.participations += participation;
        stats.totalConfidence += record.consensusConfidence;
        stats.totalCollaborations++;
        stats.qualityContributions.push(record.qualityScore);

        if (record.qualityScore > 0.8) {
          stats.successfulCollaborations++;
        }
      });
    });

    // Convert to analytics format
    const analytics: Array<any> = [];
    for (const [agentId, stats] of agentStats) {
      const participationRate = stats.participations / stats.totalCollaborations;
      const averageConfidence = stats.totalConfidence / stats.totalCollaborations;
      const successContribution = stats.successfulCollaborations / stats.totalCollaborations;
      const collaborationEffectiveness = this.calculateAverage(stats.qualityContributions);

      analytics.push({
        agentId,
        participationRate,
        averageConfidence,
        successContribution,
        collaborationEffectiveness,
        strengths: this.identifyAgentStrengths(agentId, stats),
        improvementAreas: this.identifyImprovementAreas(agentId, stats)
      });
    }

    return analytics;
  }

  // Private helper methods

  private calculateThroughput(data: CollaborationData[]): {
    hourly: number;
    daily: number;
    peak: number;
  } {
    if (data.length === 0) {
      return { hourly: 0, daily: 0, peak: 0 };
    }

    // Calculate overall throughput
    const timeSpan = new Date(data[data.length - 1].timestamp).getTime() - 
                    new Date(data[0].timestamp).getTime();
    const hours = timeSpan / (1000 * 60 * 60);
    const days = hours / 24;

    const hourly = hours > 0 ? data.length / hours : 0;
    const daily = days > 0 ? data.length / days : 0;

    // Calculate peak throughput (max collaborations in any hour)
    const peak = this.calculatePeakThroughput(data);

    return { hourly, daily, peak };
  }

  private calculatePeakThroughput(data: CollaborationData[]): number {
    const hourlyBuckets = new Map<string, number>();

    data.forEach(record => {
      const hour = new Date(record.timestamp).toISOString().substring(0, 13); // YYYY-MM-DDTHH
      hourlyBuckets.set(hour, (hourlyBuckets.get(hour) || 0) + 1);
    });

    return Math.max(...Array.from(hourlyBuckets.values()), 0);
  }

  private calculatePercentile(values: number[], percentile: number): number {
    if (values.length === 0) return 0;
    
    const sorted = [...values].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[Math.max(0, index)];
  }

  private calculateUptime(data: CollaborationData[]): number {
    if (data.length === 0) return 1.0;

    const successfulCollaborations = data.filter(record => record.errorCount === 0).length;
    return successfulCollaborations / data.length;
  }

  private calculateErrorRate(data: CollaborationData[]): number {
    if (data.length === 0) return 0;

    const totalErrors = data.reduce((sum, record) => sum + record.errorCount, 0);
    return totalErrors / data.length;
  }

  private calculateTimeoutRate(data: CollaborationData[]): number {
    if (data.length === 0) return 0;

    // Estimate timeout rate based on very long collaboration times
    const timeouts = data.filter(record => record.totalTime > 30000).length; // > 30 seconds
    return timeouts / data.length;
  }

  private calculateResourceUtilization(data: CollaborationData[]): number {
    if (data.length === 0) return 0.5;

    const avgAgentCount = this.calculateAverage(data.map(r => r.agentCount));
    const avgRoundCount = this.calculateAverage(data.map(r => r.roundCount));
    
    // Normalize to 0-1 scale (assuming max 5 agents, 5 rounds)
    return Math.min((avgAgentCount * avgRoundCount) / 25, 1.0);
  }

  private calculateCostPerCollaboration(data: CollaborationData[]): number {
    // Simplified cost calculation based on time and agent count
    if (data.length === 0) return 0;

    const avgTime = this.calculateAverage(data.map(r => r.totalTime));
    const avgAgents = this.calculateAverage(data.map(r => r.agentCount));
    
    // Assume $0.01 per second per agent
    return (avgTime / 1000) * avgAgents * 0.01;
  }

  private calculateQualityPerDollar(data: CollaborationData[]): number {
    const avgQuality = this.calculateAverage(data.map(r => r.qualityScore));
    const costPerCollab = this.calculateCostPerCollaboration(data);
    
    return costPerCollab > 0 ? avgQuality / costPerCollab : 0;
  }

  private calculateCostSavings(data: CollaborationData[]): number {
    // Estimate cost savings compared to manual content creation
    const avgTime = this.calculateAverage(data.map(r => r.totalTime));
    const manualTimeEstimate = 3600000; // 1 hour in milliseconds
    const hourlyRate = 50; // $50/hour
    
    const timeSaved = Math.max(0, manualTimeEstimate - avgTime);
    return (timeSaved / 3600000) * hourlyRate * data.length;
  }

  private createTimeSeriesData(
    data: CollaborationData[],
    valueExtractor: (record: CollaborationData) => number
  ): DataPoint[] {
    return data.map(record => ({
      timestamp: record.timestamp,
      value: valueExtractor(record)
    }));
  }

  private calculateAverage(values: number[]): number {
    if (values.length === 0) return 0;
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  private calculateImprovementRate(values: number[]): number {
    if (values.length < 2) return 0;

    const firstValue = values[0];
    const lastValue = values[values.length - 1];
    
    return firstValue > 0 ? (lastValue - firstValue) / firstValue : 0;
  }

  private identifyAgentStrengths(agentId: string, stats: any): string[] {
    const strengths: string[] = [];
    
    if (stats.successfulCollaborations / stats.totalCollaborations > 0.8) {
      strengths.push('High success rate');
    }
    
    if (stats.totalConfidence / stats.totalCollaborations > 0.8) {
      strengths.push('High confidence contributions');
    }
    
    return strengths;
  }

  private identifyImprovementAreas(agentId: string, stats: any): string[] {
    const areas: string[] = [];
    
    if (stats.participations / stats.totalCollaborations < 0.5) {
      areas.push('Increase participation rate');
    }
    
    if (stats.totalConfidence / stats.totalCollaborations < 0.6) {
      areas.push('Improve confidence in suggestions');
    }
    
    return areas;
  }
}
