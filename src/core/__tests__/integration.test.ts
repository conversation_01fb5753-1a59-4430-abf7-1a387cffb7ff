/**
 * Integration Tests
 * Tests the complete workflow system integration
 */

import { getWorkflowEngine, getStateStore, getReviewSystem } from '../workflow/singleton';
import { artifactVersionManager } from '../review/version-manager';
import { errorHandler } from '../utils/error-handler';

describe('Workflow System Integration', () => {
  beforeEach(() => {
    // Clear any previous state
    errorHandler.clearErrorLog();
  });

  describe('Basic Workflow Creation and Execution', () => {
    it('should create and execute a simple workflow', async () => {
      const workflowEngine = getWorkflowEngine();
      const stateStore = getStateStore();

      // Create a simple workflow
      const workflowId = await workflowEngine.createWorkflow({
        name: 'Test Workflow',
        description: 'A test workflow',
        version: '1.0.0',
        steps: [
          {
            id: 'step1',
            name: 'Generate Content',
            type: 'ai_generation',
            config: {
              prompt: 'Generate a test article',
              model: 'gpt-3.5-turbo'
            },
            dependencies: []
          }
        ],
        metadata: {
          category: 'test',
          difficulty: 'easy',
          estimatedTime: 5
        }
      });

      expect(workflowId).toBeDefined();
      expect(typeof workflowId).toBe('string');

      // Verify workflow was stored
      const workflow = await workflowEngine.getWorkflow(workflowId);
      expect(workflow).toBeDefined();
      expect(workflow?.name).toBe('Test Workflow');
    });

    it('should handle workflow execution', async () => {
      const workflowEngine = getWorkflowEngine();

      // Create workflow
      const workflowId = await workflowEngine.createWorkflow({
        name: 'Execution Test',
        description: 'Test workflow execution',
        version: '1.0.0',
        steps: [],
        metadata: {}
      });

      // Execute workflow
      const executionId = await workflowEngine.executeWorkflow(workflowId, {
        topic: 'Test Topic',
        length: 'short'
      });

      expect(executionId).toBeDefined();
      expect(typeof executionId).toBe('string');

      // Verify execution was created
      const execution = await workflowEngine.getExecution(executionId);
      expect(execution).toBeDefined();
      expect(execution?.workflowId).toBe(workflowId);
      expect(execution?.inputs.topic).toBe('Test Topic');
    });
  });

  describe('Review System Integration', () => {
    it('should create and manage reviews', async () => {
      const reviewSystem = getReviewSystem();
      const stateStore = getStateStore();

      // Create test content
      const contentId = 'test-content-123';
      const executionId = 'test-execution-123';
      const stepId = 'test-step-123';

      // Store test content
      await stateStore.update(state => {
        if (!state) {
          state = {
            workflows: {},
            executions: {},
            content: {},
            reviews: {},
            lastUpdated: new Date().toISOString(),
            version: 1
          };
        }

        state.content[contentId] = {
          id: contentId,
          type: 'blog_post',
          title: 'Test Article',
          content: 'This is a test article content.',
          status: 'draft',
          executionId,
          stepId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          metadata: {}
        };

        return state;
      });

      // Create review
      const reviewLink = await reviewSystem.createReview(
        { id: contentId, type: 'blog_post', title: 'Test Article', data: 'Test content' },
        {
          contentId,
          executionId,
          stepId,
          type: 'approval',
          instructions: 'Please review this test content',
          reviewers: ['reviewer1'],
          priority: 'medium'
        }
      );

      expect(reviewLink).toBeDefined();
      expect(reviewLink.reviewId).toBeDefined();
      expect(reviewLink.accessToken).toBeDefined();

      // Get review
      const review = await reviewSystem.getReview(reviewLink.reviewId);
      expect(review).toBeDefined();
      expect(review.instructions).toBe('Please review this test content');
    });

    it('should handle review assignments', async () => {
      const reviewSystem = getReviewSystem();

      // Create a simple review first
      const reviewLink = await reviewSystem.createReview(
        { id: 'content-123', type: 'blog_post', title: 'Test', data: 'Test' },
        {
          contentId: 'content-123',
          executionId: 'exec-123',
          stepId: 'step-123',
          type: 'approval',
          instructions: 'Test review'
        }
      );

      // Assign reviewer
      const assignment = await reviewSystem.assignReviewer(
        reviewLink.reviewId,
        'reviewer-123',
        'admin',
        { priority: 'high' }
      );

      expect(assignment).toBeDefined();
      expect(assignment.reviewerId).toBe('reviewer-123');
      expect(assignment.assignedBy).toBe('admin');

      // Get assignments
      const assignments = await reviewSystem.getReviewAssignments(reviewLink.reviewId);
      expect(assignments).toHaveLength(1);
      expect(assignments[0].reviewerId).toBe('reviewer-123');
    });
  });

  describe('Artifact Version Management', () => {
    it('should create and manage artifact versions', async () => {
      const artifactId = 'test-artifact-123';
      const content1 = { title: 'Version 1', body: 'This is version 1' };
      const content2 = { title: 'Version 2', body: 'This is version 2' };

      // Create first version
      const version1Id = await artifactVersionManager.createVersion(
        artifactId,
        content1,
        'Initial version',
        'user1'
      );

      expect(version1Id).toBeDefined();

      // Create second version
      const version2Id = await artifactVersionManager.createVersion(
        artifactId,
        content2,
        'Updated content',
        'user2'
      );

      expect(version2Id).toBeDefined();
      expect(version2Id).not.toBe(version1Id);

      // Get version history
      const history = await artifactVersionManager.getVersionHistory(artifactId);
      expect(history).toHaveLength(2);
      expect(history[0].version).toBe(2); // Latest first
      expect(history[1].version).toBe(1);

      // Get active version
      const activeVersion = artifactVersionManager.getActiveVersion(artifactId);
      expect(activeVersion).toBeDefined();
      expect(activeVersion?.version).toBe(2);
      expect(activeVersion?.content).toEqual(content2);
    });

    it('should compare versions', async () => {
      const artifactId = 'compare-test-123';
      const content1 = { title: 'Original', count: 1 };
      const content2 = { title: 'Modified', count: 2, newField: 'added' };

      // Create versions
      const version1Id = await artifactVersionManager.createVersion(
        artifactId,
        content1,
        'Version 1',
        'user1'
      );

      const version2Id = await artifactVersionManager.createVersion(
        artifactId,
        content2,
        'Version 2',
        'user1'
      );

      // Compare versions
      const comparison = await artifactVersionManager.compareVersions(version1Id, version2Id);

      expect(comparison).toBeDefined();
      expect(comparison.versionId1).toBe(version1Id);
      expect(comparison.versionId2).toBe(version2Id);
      expect(comparison.differences).toBeDefined();
      expect(comparison.summary.totalChanges).toBeGreaterThan(0);
      expect(comparison.similarity).toBeGreaterThan(0);
      expect(comparison.similarity).toBeLessThan(1);
    });

    it('should revert to previous versions', async () => {
      const artifactId = 'revert-test-123';
      const content1 = { title: 'Good Version', status: 'good' };
      const content2 = { title: 'Bad Version', status: 'bad' };

      // Create versions
      const version1Id = await artifactVersionManager.createVersion(
        artifactId,
        content1,
        'Good version',
        'user1'
      );

      const version2Id = await artifactVersionManager.createVersion(
        artifactId,
        content2,
        'Bad version',
        'user1'
      );

      // Verify bad version is active
      let activeVersion = artifactVersionManager.getActiveVersion(artifactId);
      expect(activeVersion?.content.status).toBe('bad');

      // Revert to good version
      const revertVersionId = await artifactVersionManager.revertToVersion(
        artifactId,
        version1Id,
        'admin'
      );

      expect(revertVersionId).toBeDefined();
      expect(revertVersionId).not.toBe(version1Id);
      expect(revertVersionId).not.toBe(version2Id);

      // Verify reverted version is active and has good content
      activeVersion = artifactVersionManager.getActiveVersion(artifactId);
      expect(activeVersion?.content.status).toBe('good');
      expect(activeVersion?.changeDescription).toContain('Reverted to version 1');
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle errors gracefully across the system', async () => {
      const workflowEngine = getWorkflowEngine();

      // Try to get non-existent workflow
      const workflow = await workflowEngine.getWorkflow('non-existent-id');
      expect(workflow).toBeNull();

      // Try to execute non-existent workflow
      await expect(
        workflowEngine.executeWorkflow('non-existent-id', {})
      ).rejects.toThrow();

      // Check error statistics
      const errorStats = errorHandler.getErrorStats();
      expect(errorStats.total).toBeGreaterThan(0);
    });

    it('should maintain system stability under error conditions', async () => {
      const reviewSystem = getReviewSystem();

      // Try to get non-existent review
      await expect(
        reviewSystem.getReview('non-existent-review')
      ).rejects.toThrow();

      // Try to submit review for non-existent review
      await expect(
        reviewSystem.submitReview('non-existent-review', 'approve')
      ).rejects.toThrow();

      // System should still be functional
      const allReviews = await reviewSystem.getAllReviews();
      expect(Array.isArray(allReviews)).toBe(true);
    });
  });

  describe('State Management Integration', () => {
    it('should maintain consistent state across operations', async () => {
      const stateStore = getStateStore();

      // Get initial state
      const initialState = await stateStore.get();
      const initialVersion = initialState?.version || 0;

      // Update state
      await stateStore.update(state => {
        if (!state) {
          state = {
            workflows: {},
            executions: {},
            content: {},
            reviews: {},
            lastUpdated: new Date().toISOString(),
            version: 1
          };
        }

        state.content['test-item'] = {
          id: 'test-item',
          type: 'generic',
          title: 'Test Item',
          content: 'Test content',
          status: 'draft',
          executionId: 'test-exec',
          stepId: 'test-step',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          metadata: {}
        };

        return state;
      });

      // Verify state was updated
      const updatedState = await stateStore.get();
      expect(updatedState).toBeDefined();
      expect(updatedState?.version).toBeGreaterThan(initialVersion);
      expect(updatedState?.content['test-item']).toBeDefined();
      expect(updatedState?.content['test-item'].title).toBe('Test Item');
    });
  });
});
