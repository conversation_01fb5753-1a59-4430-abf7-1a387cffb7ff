/**
 * Advanced Conflict Detection System
 *
 * Detects semantic conflicts beyond simple confidence differences
 */

import { AgentInput } from '../agents/types';

export interface Conflict {
  id: string;
  type: 'semantic' | 'priority' | 'approach' | 'factual';
  severity: 'low' | 'medium' | 'high' | 'critical';
  agentsInvolved: string[];
  description: string;
  conflictingInputs: AgentInput[];
  suggestedResolution?: string;
  requiresHumanIntervention: boolean;
}

export interface ConflictDetectionResult {
  conflicts: Conflict[];
  overallConflictLevel: number; // 0-1 scale
  resolutionStrategy: 'automatic' | 'guided' | 'human-required';
}

interface ConflictPattern {
  pattern: RegExp;
  type: Conflict['type'];
  severity: Conflict['severity'];
}

interface SemanticAnalyzer {
  compareSuggestions(suggestions1: string[], suggestions2: string[]): Promise<number>;
}

export class ConflictDetector {
  private semanticAnalyzer: SemanticAnalyzer;
  private conflictPatterns: ConflictPattern[];

  constructor() {
    this.semanticAnalyzer = new SimpleSemanticAnalyzer();
    this.conflictPatterns = this.loadConflictPatterns();
  }

  /**
   * Detect conflicts in agent inputs using multiple analysis methods
   */
  async detectConflicts(agentInputs: AgentInput[]): Promise<ConflictDetectionResult> {
    const conflicts: Conflict[] = [];

    // 1. Semantic conflict detection
    const semanticConflicts = await this.detectSemanticConflicts(agentInputs);
    conflicts.push(...semanticConflicts);

    // 2. Priority conflicts
    const priorityConflicts = this.detectPriorityConflicts(agentInputs);
    conflicts.push(...priorityConflicts);

    // 3. Approach conflicts
    const approachConflicts = this.detectApproachConflicts(agentInputs);
    conflicts.push(...approachConflicts);

    // 4. Factual conflicts
    const factualConflicts = await this.detectFactualConflicts(agentInputs);
    conflicts.push(...factualConflicts);

    const overallConflictLevel = this.calculateConflictLevel(conflicts);
    const resolutionStrategy = this.determineResolutionStrategy(conflicts);

    return {
      conflicts,
      overallConflictLevel,
      resolutionStrategy
    };
  }

  /**
   * Detect semantic conflicts using NLP analysis
   */
  private async detectSemanticConflicts(agentInputs: AgentInput[]): Promise<Conflict[]> {
    const conflicts: Conflict[] = [];

    for (let i = 0; i < agentInputs.length; i++) {
      for (let j = i + 1; j < agentInputs.length; j++) {
        const input1 = agentInputs[i];
        const input2 = agentInputs[j];

        const semanticSimilarity = await this.semanticAnalyzer.compareSuggestions(
          input1.suggestions,
          input2.suggestions
        );

        // If suggestions are semantically opposite (similarity < 0.3)
        if (semanticSimilarity < 0.3) {
          conflicts.push({
            id: `semantic-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            type: 'semantic',
            severity: semanticSimilarity < 0.1 ? 'high' : 'medium',
            agentsInvolved: [input1.agentId, input2.agentId],
            description: `Semantic conflict between ${input1.agentId} and ${input2.agentId} suggestions`,
            conflictingInputs: [input1, input2],
            requiresHumanIntervention: semanticSimilarity < 0.1
          });
        }
      }
    }

    return conflicts;
  }

  /**
   * Detect priority conflicts (agents suggesting conflicting priorities)
   */
  private detectPriorityConflicts(agentInputs: AgentInput[]): Conflict[] {
    const conflicts: Conflict[] = [];
    const priorityMap = new Map<string, AgentInput[]>();

    // Group inputs by suggested priorities
    agentInputs.forEach(input => {
      input.suggestions.forEach(suggestion => {
        if (suggestion.includes('priority') || suggestion.includes('important')) {
          const key = this.extractPriorityKey(suggestion);
          if (!priorityMap.has(key)) {
            priorityMap.set(key, []);
          }
          priorityMap.get(key)!.push(input);
        }
      });
    });

    // Detect conflicting priorities
    for (const [priority, inputs] of priorityMap) {
      if (inputs.length > 1) {
        const confidenceDiff = Math.max(...inputs.map(i => i.confidence)) -
                              Math.min(...inputs.map(i => i.confidence));

        if (confidenceDiff > 0.3) {
          conflicts.push({
            id: `priority-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            type: 'priority',
            severity: confidenceDiff > 0.5 ? 'high' : 'medium',
            agentsInvolved: inputs.map(i => i.agentId),
            description: `Priority conflict on "${priority}"`,
            conflictingInputs: inputs,
            requiresHumanIntervention: confidenceDiff > 0.5
          });
        }
      }
    }

    return conflicts;
  }

  /**
   * Detect approach conflicts
   */
  private detectApproachConflicts(agentInputs: AgentInput[]): Conflict[] {
    const conflicts: Conflict[] = [];

    // Check for conflicting approaches using pattern matching
    for (const pattern of this.conflictPatterns) {
      if (pattern.type === 'approach') {
        const matchingInputs = agentInputs.filter(input =>
          input.suggestions.some(suggestion => pattern.pattern.test(suggestion))
        );

        if (matchingInputs.length > 1) {
          conflicts.push({
            id: `approach-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            type: 'approach',
            severity: pattern.severity,
            agentsInvolved: matchingInputs.map(i => i.agentId),
            description: `Approach conflict detected: ${pattern.pattern.source}`,
            conflictingInputs: matchingInputs,
            requiresHumanIntervention: pattern.severity === 'high' || pattern.severity === 'critical'
          });
        }
      }
    }

    return conflicts;
  }

  /**
   * Detect factual conflicts
   */
  private async detectFactualConflicts(agentInputs: AgentInput[]): Promise<Conflict[]> {
    const conflicts: Conflict[] = [];

    // Simple factual conflict detection based on contradictory statements
    for (let i = 0; i < agentInputs.length; i++) {
      for (let j = i + 1; j < agentInputs.length; j++) {
        const input1 = agentInputs[i];
        const input2 = agentInputs[j];

        // Check for contradictory factual statements
        const hasFactualConflict = this.checkFactualContradict(input1.suggestions, input2.suggestions);

        if (hasFactualConflict) {
          conflicts.push({
            id: `factual-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            type: 'factual',
            severity: 'high',
            agentsInvolved: [input1.agentId, input2.agentId],
            description: `Factual conflict between ${input1.agentId} and ${input2.agentId}`,
            conflictingInputs: [input1, input2],
            requiresHumanIntervention: true
          });
        }
      }
    }

    return conflicts;
  }

  /**
   * Calculate overall conflict level
   */
  private calculateConflictLevel(conflicts: Conflict[]): number {
    if (conflicts.length === 0) return 0;

    const severityWeights = { low: 0.25, medium: 0.5, high: 0.75, critical: 1.0 };
    const totalWeight = conflicts.reduce((sum, conflict) =>
      sum + severityWeights[conflict.severity], 0
    );

    return Math.min(totalWeight / conflicts.length, 1.0);
  }

  /**
   * Determine resolution strategy based on conflicts
   */
  private determineResolutionStrategy(conflicts: Conflict[]): 'automatic' | 'guided' | 'human-required' {
    const criticalConflicts = conflicts.filter(c => c.severity === 'critical');
    const humanRequiredConflicts = conflicts.filter(c => c.requiresHumanIntervention);

    if (criticalConflicts.length > 0 || humanRequiredConflicts.length > 2) {
      return 'human-required';
    }

    if (conflicts.length > 3 || humanRequiredConflicts.length > 0) {
      return 'guided';
    }

    return 'automatic';
  }

  private extractPriorityKey(suggestion: string): string {
    // Extract priority-related keywords from suggestions
    const priorityKeywords = ['urgent', 'important', 'critical', 'low priority', 'high priority'];
    for (const keyword of priorityKeywords) {
      if (suggestion.toLowerCase().includes(keyword)) {
        return keyword;
      }
    }
    return 'general';
  }

  private loadConflictPatterns(): ConflictPattern[] {
    // Load predefined conflict patterns for detection
    return [
      {
        pattern: /SEO.*keyword.*vs.*readability/i,
        type: 'approach',
        severity: 'medium'
      },
      {
        pattern: /technical.*detail.*vs.*accessibility/i,
        type: 'approach',
        severity: 'medium'
      }
    ];
  }

  private checkFactualContradict(suggestions1: string[], suggestions2: string[]): boolean {
    // Simple factual contradiction detection
    const contradictoryPairs = [
      ['increase', 'decrease'],
      ['more', 'less'],
      ['higher', 'lower'],
      ['better', 'worse'],
      ['yes', 'no'],
      ['true', 'false']
    ];

    for (const suggestion1 of suggestions1) {
      for (const suggestion2 of suggestions2) {
        for (const [word1, word2] of contradictoryPairs) {
          if (suggestion1.toLowerCase().includes(word1) && 
              suggestion2.toLowerCase().includes(word2)) {
            return true;
          }
        }
      }
    }

    return false;
  }
}

/**
 * Simple semantic analyzer implementation
 */
class SimpleSemanticAnalyzer implements SemanticAnalyzer {
  async compareSuggestions(suggestions1: string[], suggestions2: string[]): Promise<number> {
    // Simple word overlap-based similarity
    const words1 = new Set(suggestions1.join(' ').toLowerCase().split(/\s+/));
    const words2 = new Set(suggestions2.join(' ').toLowerCase().split(/\s+/));

    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);

    return intersection.size / union.size;
  }
}
