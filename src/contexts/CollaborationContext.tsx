import React, { createContext, useContext, useReducer } from 'react';
import { 
  IterativeCollaborationState, 
  IterativeArtifact, 
  Consultation, 
  AgentState 
} from '../app/(payload)/api/agents/collaborative-iteration/types';

interface CollaborationContextType {
  state: IterativeCollaborationState;
  updateState: (updates: Partial<IterativeCollaborationState>) => void;
  addArtifact: (artifact: IterativeArtifact) => void;
  updateArtifact: (artifactId: string, updates: Partial<IterativeArtifact>) => void;
  addConsultation: (consultation: Consultation) => void;
  updateConsultation: (consultationId: string, updates: Partial<Consultation>) => void;
  updateAgentState: (agentId: string, updates: Partial<AgentState>) => void;
}

type CollaborationAction = 
  | { type: 'UPDATE_STATE'; updates: Partial<IterativeCollaborationState> }
  | { type: 'ADD_ARTIFACT'; artifact: IterativeArtifact }
  | { type: 'UPDATE_ARTIFACT'; artifactId: string; updates: Partial<IterativeArtifact> }
  | { type: 'ADD_CONSULTATION'; consultation: Consultation }
  | { type: 'UPDATE_CONSULTATION'; consultationId: string; updates: Partial<Consultation> }
  | { type: 'UPDATE_AGENT_STATE'; agentId: string; updates: Partial<AgentState> };

const CollaborationContext = createContext<CollaborationContextType | undefined>(undefined);

// Reducer to handle different state updates
function collaborationReducer(state: IterativeCollaborationState, action: CollaborationAction): IterativeCollaborationState {
  switch (action.type) {
    case 'UPDATE_STATE':
      return { ...state, ...action.updates };
      
    case 'ADD_ARTIFACT':
      return {
        ...state,
        artifacts: {
          ...state.artifacts,
          [action.artifact.id]: action.artifact
        }
      };
      
    case 'UPDATE_ARTIFACT':
      if (!state.artifacts[action.artifactId]) {
        console.error(`Artifact ${action.artifactId} not found in state`);
        return state;
      }
      
      return {
        ...state,
        artifacts: {
          ...state.artifacts,
          [action.artifactId]: {
            ...state.artifacts[action.artifactId],
            ...action.updates
          }
        }
      };
      
    case 'ADD_CONSULTATION':
      return {
        ...state,
        consultations: {
          ...state.consultations,
          [action.consultation.id]: action.consultation
        }
      };
      
    case 'UPDATE_CONSULTATION':
      if (!state.consultations[action.consultationId]) {
        console.error(`Consultation ${action.consultationId} not found in state`);
        return state;
      }
      
      return {
        ...state,
        consultations: {
          ...state.consultations,
          [action.consultationId]: {
            ...state.consultations[action.consultationId],
            ...action.updates
          }
        }
      };
      
    case 'UPDATE_AGENT_STATE':
      return {
        ...state,
        agentStates: {
          ...state.agentStates,
          [action.agentId]: {
            ...state.agentStates[action.agentId],
            ...action.updates,
            lastUpdated: new Date().toISOString()
          }
        }
      };
      
    default:
      console.error('Unknown action type in collaboration reducer');
      return state;
  }
}

// Default initial state
const defaultInitialState: IterativeCollaborationState = {
  id: '',
  topic: '',
  contentType: 'blog-article',
  targetAudience: '',
  tone: '',
  keywords: [],
  status: 'active',
  startTime: new Date().toISOString(),
  artifacts: {},
  consultations: {},
  agentStates: {},
  currentPhase: 'planning',
  messages: [],
  iterations: 0,
  maxIterations: 5
};

interface CollaborationProviderProps {
  children: React.ReactNode;
  initialState?: Partial<IterativeCollaborationState>;
}

export const CollaborationProvider: React.FC<CollaborationProviderProps> = ({ 
  children, 
  initialState = {} 
}) => {
  const [state, dispatch] = useReducer(
    collaborationReducer, 
    { ...defaultInitialState, ...initialState }
  );

  const updateState = (updates: Partial<IterativeCollaborationState>) => {
    dispatch({ type: 'UPDATE_STATE', updates });
  };

  const addArtifact = (artifact: IterativeArtifact) => {
    dispatch({ type: 'ADD_ARTIFACT', artifact });
  };

  const updateArtifact = (artifactId: string, updates: Partial<IterativeArtifact>) => {
    dispatch({ type: 'UPDATE_ARTIFACT', artifactId, updates });
  };

  const addConsultation = (consultation: Consultation) => {
    dispatch({ type: 'ADD_CONSULTATION', consultation });
  };

  const updateConsultation = (consultationId: string, updates: Partial<Consultation>) => {
    dispatch({ type: 'UPDATE_CONSULTATION', consultationId, updates });
  };
  
  const updateAgentState = (agentId: string, updates: Partial<AgentState>) => {
    dispatch({ type: 'UPDATE_AGENT_STATE', agentId, updates });
  };

  return (
    <CollaborationContext.Provider value={{ 
      state, 
      updateState, 
      addArtifact, 
      updateArtifact, 
      addConsultation, 
      updateConsultation,
      updateAgentState
    }}>
      {children}
    </CollaborationContext.Provider>
  );
};

export const useCollaboration = () => {
  const context = useContext(CollaborationContext);
  if (context === undefined) {
    throw new Error('useCollaboration must be used within a CollaborationProvider');
  }
  return context;
};
