/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    users: User;
    media: Media;
    products: Product;
    sellers: Seller;
    'product-features': ProductFeature;
    'pricing-plans': PricingPlan;
    faqs: Faq;
    'feature-comparisons': FeatureComparison;
    'migration-considerations': MigrationConsideration;
    authors: Author;
    'use-case-recommendations': UseCaseRecommendation;
    reviews: Review;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    products: ProductsSelect<false> | ProductsSelect<true>;
    sellers: SellersSelect<false> | SellersSelect<true>;
    'product-features': ProductFeaturesSelect<false> | ProductFeaturesSelect<true>;
    'pricing-plans': PricingPlansSelect<false> | PricingPlansSelect<true>;
    faqs: FaqsSelect<false> | FaqsSelect<true>;
    'feature-comparisons': FeatureComparisonsSelect<false> | FeatureComparisonsSelect<true>;
    'migration-considerations': MigrationConsiderationsSelect<false> | MigrationConsiderationsSelect<true>;
    authors: AuthorsSelect<false> | AuthorsSelect<true>;
    'use-case-recommendations': UseCaseRecommendationsSelect<false> | UseCaseRecommendationsSelect<true>;
    reviews: ReviewsSelect<false> | ReviewsSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: number;
  };
  globals: {};
  globalsSelect: {};
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: number;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: number;
  alt: string;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "products".
 */
export interface Product {
  id: number;
  name: string;
  slug?: string | null;
  logo: number | Media;
  /**
   * Main header image for product pages
   */
  featuredImage?: (number | null) | Media;
  overview?: string | null;
  overviewRich?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  meta?: {
    lastUpdated?: string | null;
    industryCategory?: ('crm' | 'marketing' | 'sales') | null;
    tags?:
      | {
          tag?: string | null;
          id?: string | null;
        }[]
      | null;
  };
  seller: number | Seller;
  /**
   * Products that are alternatives to this one
   */
  alternatives?: (number | Product)[] | null;
  rating?: {
    /**
     * Overall rating out of 5
     */
    overall?: number | null;
    usability?: number | null;
    features?: number | null;
    support?: number | null;
    value?: number | null;
    reviewCount?: number | null;
  };
  expertView?: {
    strengths?:
      | {
          text?: string | null;
          id?: string | null;
        }[]
      | null;
    considerations?:
      | {
          text?: string | null;
          id?: string | null;
        }[]
      | null;
    weaknesses?:
      | {
          text?: string | null;
          id?: string | null;
        }[]
      | null;
    summary?: string | null;
    award?: ('none' | 'editors-choice' | 'best-value' | 'best-small-business') | null;
  };
  researchers?:
    | {
        person?: (number | null) | Author;
        role?: ('researcher' | 'fact-checker' | 'editor') | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "sellers".
 */
export interface Seller {
  id: number;
  name: string;
  logo?: (number | null) | Media;
  description?: string | null;
  website?: {
    linktext?: string | null;
    linkurl?: string | null;
  };
  foundedYear?: number | null;
  headquarters?: string | null;
  socialMedia?: {
    twitter?: {
      linktext?: string | null;
      linkurl?: string | null;
    };
    linkedin?: {
      linktext?: string | null;
      linkurl?: string | null;
    };
  };
  companySize?: ('startup' | 'small' | 'medium' | 'large' | 'enterprise') | null;
  employees?: number | null;
  fundingStatus?: ('bootstrapped' | 'seed' | 'series-a-b' | 'series-c-plus' | 'public' | 'acquired') | null;
  owner?: string | null;
  contactInfo?: {
    phone?: string | null;
    email?: string | null;
    supportUrl?: string | null;
  };
  /**
   * Important facts about the company to highlight
   */
  keyFacts?:
    | {
        fact?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Other companies that compete with this seller
   */
  competitors?: (number | Seller)[] | null;
  /**
   * Industries this seller primarily serves
   */
  industries?:
    | {
        industry?: string | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "authors".
 */
export interface Author {
  id: number;
  name: string;
  role?: string | null;
  avatar?: (number | null) | Media;
  bio?: string | null;
  social?: {
    linkedin?: string | null;
    twitter?: string | null;
  };
  /**
   * Areas of expertise (e.g., "CRM Software", "Marketing Technology")
   */
  expertise?:
    | {
        area?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * For internal use only, not displayed publicly
   */
  email?: string | null;
  /**
   * Display in featured authors sections
   */
  featuredAuthor?: boolean | null;
  /**
   * Products this author has written about
   */
  authoredProducts?: (number | Product)[] | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "product-features".
 */
export interface ProductFeature {
  id: number;
  title: string;
  product: number | Product;
  icon?:
    | (
        | 'Star'
        | 'MessageSquare'
        | 'Zap'
        | 'FileText'
        | 'Users'
        | 'Shield'
        | 'Clock'
        | 'Calendar'
        | 'CheckCircle2'
        | 'Database'
        | 'Settings'
        | 'CloudCog'
        | 'TrendingUp'
      )
    | null;
  description?: string | null;
  category:
    | 'find-attract-leads'
    | 'close-more-deals'
    | 'support-retain-customers'
    | 'understand-whats-working'
    | 'automate-save-time'
    | 'customize-connect'
    | 'collaborate-teams';
  /**
   * Question this feature answers (optional)
   */
  question?: string | null;
  technicalSpecs?:
    | {
        spec?: string | null;
        id?: string | null;
      }[]
    | null;
  themeColor?: ('blue' | 'purple' | 'indigo' | 'violet' | 'sky' | 'navy' | 'teal') | null;
  /**
   * Optional link to redirect to when clicked (e.g. /features/details)
   */
  ctaLink?: string | null;
  /**
   * Text for the call-to-action button (if link provided)
   */
  ctaText?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pricing-plans".
 */
export interface PricingPlan {
  id: number;
  title: string;
  product: number | Product;
  subtitle?: string | null;
  price: string;
  billingFrequency?: string | null;
  description?: string | null;
  /**
   * E.g., "Up to 10 users" or "Unlimited"
   */
  userLimit?: string | null;
  features?:
    | {
        feature?: string | null;
        id?: string | null;
      }[]
    | null;
  popular?: boolean | null;
  advantages?:
    | {
        advantage?: string | null;
        id?: string | null;
      }[]
    | null;
  idealFor?: string | null;
  ctaText?: string | null;
  ctaLink?: string | null;
  planType?: ('monthly' | 'annual' | 'both') | null;
  /**
   * Percentage discount for annual billing (e.g., 17)
   */
  annualDiscount?: number | null;
  /**
   * Price for annual billing (if different)
   */
  annualPrice?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "faqs".
 */
export interface Faq {
  id: number;
  question: string;
  answer: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  product: number | Product;
  category: 'general' | 'features' | 'pricing' | 'support' | 'implementation' | 'alternatives';
  /**
   * Display order within category
   */
  order?: number | null;
  featured?: boolean | null;
  /**
   * Which page type should this FAQ appear on?
   */
  pageType?: ('all' | 'overview' | 'features' | 'pricing' | 'reviews' | 'alternatives') | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "feature-comparisons".
 */
export interface FeatureComparison {
  id: number;
  name: string;
  category?: ('core' | 'integration' | 'security' | 'support' | 'pricing') | null;
  /**
   * Display order within category
   */
  order?: number | null;
  comparisons?:
    | {
        product?: (number | null) | Product;
        value?: {
          type?: ('boolean' | 'text') | null;
          booleanValue?: boolean | null;
          textValue?: string | null;
        };
        id?: string | null;
      }[]
    | null;
  notes?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  /**
   * Enable if this is for comparing different plans of the same product
   */
  isPlanComparison?: boolean | null;
  /**
   * The product whose plans are being compared
   */
  planComparisonProduct?: (number | null) | Product;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "migration-considerations".
 */
export interface MigrationConsideration {
  id: number;
  title: string;
  description: string;
  fromProduct: number | Product;
  toProduct: number | Product;
  complexity: 'simple' | 'moderate' | 'complex';
  /**
   * E.g., "2-4 weeks"
   */
  estimatedTime?: string | null;
  tools?:
    | {
        name?: string | null;
        description?: string | null;
        id?: string | null;
      }[]
    | null;
  steps?:
    | {
        step?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        id?: string | null;
      }[]
    | null;
  migrationResources?:
    | {
        title?: string | null;
        url?: string | null;
        description?: string | null;
        resourceType?: ('guide' | 'video' | 'tool' | 'documentation') | null;
        id?: string | null;
      }[]
    | null;
  considerations?:
    | {
        title: string;
        description: string;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "use-case-recommendations".
 */
export interface UseCaseRecommendation {
  id: number;
  title: string;
  description: string;
  icon?:
    | ('Users' | 'Zap' | 'Clock' | 'Shield' | 'MessageSquare' | 'TrendingUp' | 'Settings' | 'FileText' | 'Database')
    | null;
  themeColor?: ('blue' | 'purple' | 'indigo' | 'green' | 'yellow') | null;
  recommendations?:
    | {
        type?: ('product' | 'custom') | null;
        productRecommendation?: (number | null) | Product;
        customLink?: {
          text?: string | null;
          url?: string | null;
        };
        /**
         * Why this product is recommended for this use case
         */
        rationale?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * If this is a use case for alternatives to a specific product
   */
  applicableProduct?: (number | null) | Product;
  comparisonFocus?: ('general' | 'price' | 'features' | 'implementation' | 'smb' | 'enterprise') | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "reviews".
 */
export interface Review {
  id: number;
  title: string;
  product: number | Product;
  author: number | Author;
  publishedDate: string;
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  rating: {
    overall: number;
    usability?: number | null;
    features?: number | null;
    support?: number | null;
    value?: number | null;
  };
  pros?:
    | {
        pro?: string | null;
        id?: string | null;
      }[]
    | null;
  cons?:
    | {
        con?: string | null;
        id?: string | null;
      }[]
    | null;
  verdict: string;
  highlights?:
    | {
        title: string;
        description: string;
        icon?: ('Clock' | 'Gift' | 'Users' | 'Shield' | 'Zap' | 'Star') | null;
        themeColor?: ('blue' | 'purple' | 'indigo') | null;
        id?: string | null;
      }[]
    | null;
  usabilityScores?:
    | {
        label: string;
        rating: number;
        comment?: string | null;
        id?: string | null;
      }[]
    | null;
  status: 'draft' | 'review' | 'published' | 'archived';
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: number;
  document?:
    | ({
        relationTo: 'users';
        value: number | User;
      } | null)
    | ({
        relationTo: 'media';
        value: number | Media;
      } | null)
    | ({
        relationTo: 'products';
        value: number | Product;
      } | null)
    | ({
        relationTo: 'sellers';
        value: number | Seller;
      } | null)
    | ({
        relationTo: 'product-features';
        value: number | ProductFeature;
      } | null)
    | ({
        relationTo: 'pricing-plans';
        value: number | PricingPlan;
      } | null)
    | ({
        relationTo: 'faqs';
        value: number | Faq;
      } | null)
    | ({
        relationTo: 'feature-comparisons';
        value: number | FeatureComparison;
      } | null)
    | ({
        relationTo: 'migration-considerations';
        value: number | MigrationConsideration;
      } | null)
    | ({
        relationTo: 'authors';
        value: number | Author;
      } | null)
    | ({
        relationTo: 'use-case-recommendations';
        value: number | UseCaseRecommendation;
      } | null)
    | ({
        relationTo: 'reviews';
        value: number | Review;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: number;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: number;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "products_select".
 */
export interface ProductsSelect<T extends boolean = true> {
  name?: T;
  slug?: T;
  logo?: T;
  featuredImage?: T;
  overview?: T;
  overviewRich?: T;
  meta?:
    | T
    | {
        lastUpdated?: T;
        industryCategory?: T;
        tags?:
          | T
          | {
              tag?: T;
              id?: T;
            };
      };
  seller?: T;
  alternatives?: T;
  rating?:
    | T
    | {
        overall?: T;
        usability?: T;
        features?: T;
        support?: T;
        value?: T;
        reviewCount?: T;
      };
  expertView?:
    | T
    | {
        strengths?:
          | T
          | {
              text?: T;
              id?: T;
            };
        considerations?:
          | T
          | {
              text?: T;
              id?: T;
            };
        weaknesses?:
          | T
          | {
              text?: T;
              id?: T;
            };
        summary?: T;
        award?: T;
      };
  researchers?:
    | T
    | {
        person?: T;
        role?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "sellers_select".
 */
export interface SellersSelect<T extends boolean = true> {
  name?: T;
  logo?: T;
  description?: T;
  website?:
    | T
    | {
        linktext?: T;
        linkurl?: T;
      };
  foundedYear?: T;
  headquarters?: T;
  socialMedia?:
    | T
    | {
        twitter?:
          | T
          | {
              linktext?: T;
              linkurl?: T;
            };
        linkedin?:
          | T
          | {
              linktext?: T;
              linkurl?: T;
            };
      };
  companySize?: T;
  employees?: T;
  fundingStatus?: T;
  owner?: T;
  contactInfo?:
    | T
    | {
        phone?: T;
        email?: T;
        supportUrl?: T;
      };
  keyFacts?:
    | T
    | {
        fact?: T;
        id?: T;
      };
  competitors?: T;
  industries?:
    | T
    | {
        industry?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "product-features_select".
 */
export interface ProductFeaturesSelect<T extends boolean = true> {
  title?: T;
  product?: T;
  icon?: T;
  description?: T;
  category?: T;
  question?: T;
  technicalSpecs?:
    | T
    | {
        spec?: T;
        id?: T;
      };
  themeColor?: T;
  ctaLink?: T;
  ctaText?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pricing-plans_select".
 */
export interface PricingPlansSelect<T extends boolean = true> {
  title?: T;
  product?: T;
  subtitle?: T;
  price?: T;
  billingFrequency?: T;
  description?: T;
  userLimit?: T;
  features?:
    | T
    | {
        feature?: T;
        id?: T;
      };
  popular?: T;
  advantages?:
    | T
    | {
        advantage?: T;
        id?: T;
      };
  idealFor?: T;
  ctaText?: T;
  ctaLink?: T;
  planType?: T;
  annualDiscount?: T;
  annualPrice?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "faqs_select".
 */
export interface FaqsSelect<T extends boolean = true> {
  question?: T;
  answer?: T;
  product?: T;
  category?: T;
  order?: T;
  featured?: T;
  pageType?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "feature-comparisons_select".
 */
export interface FeatureComparisonsSelect<T extends boolean = true> {
  name?: T;
  category?: T;
  order?: T;
  comparisons?:
    | T
    | {
        product?: T;
        value?:
          | T
          | {
              type?: T;
              booleanValue?: T;
              textValue?: T;
            };
        id?: T;
      };
  notes?: T;
  isPlanComparison?: T;
  planComparisonProduct?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "migration-considerations_select".
 */
export interface MigrationConsiderationsSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  fromProduct?: T;
  toProduct?: T;
  complexity?: T;
  estimatedTime?: T;
  tools?:
    | T
    | {
        name?: T;
        description?: T;
        id?: T;
      };
  steps?:
    | T
    | {
        step?: T;
        id?: T;
      };
  migrationResources?:
    | T
    | {
        title?: T;
        url?: T;
        description?: T;
        resourceType?: T;
        id?: T;
      };
  considerations?:
    | T
    | {
        title?: T;
        description?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "authors_select".
 */
export interface AuthorsSelect<T extends boolean = true> {
  name?: T;
  role?: T;
  avatar?: T;
  bio?: T;
  social?:
    | T
    | {
        linkedin?: T;
        twitter?: T;
      };
  expertise?:
    | T
    | {
        area?: T;
        id?: T;
      };
  email?: T;
  featuredAuthor?: T;
  authoredProducts?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "use-case-recommendations_select".
 */
export interface UseCaseRecommendationsSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  icon?: T;
  themeColor?: T;
  recommendations?:
    | T
    | {
        type?: T;
        productRecommendation?: T;
        customLink?:
          | T
          | {
              text?: T;
              url?: T;
            };
        rationale?: T;
        id?: T;
      };
  applicableProduct?: T;
  comparisonFocus?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "reviews_select".
 */
export interface ReviewsSelect<T extends boolean = true> {
  title?: T;
  product?: T;
  author?: T;
  publishedDate?: T;
  content?: T;
  rating?:
    | T
    | {
        overall?: T;
        usability?: T;
        features?: T;
        support?: T;
        value?: T;
      };
  pros?:
    | T
    | {
        pro?: T;
        id?: T;
      };
  cons?:
    | T
    | {
        con?: T;
        id?: T;
      };
  verdict?: T;
  highlights?:
    | T
    | {
        title?: T;
        description?: T;
        icon?: T;
        themeColor?: T;
        id?: T;
      };
  usabilityScores?:
    | T
    | {
        label?: T;
        rating?: T;
        comment?: T;
        id?: T;
      };
  status?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}