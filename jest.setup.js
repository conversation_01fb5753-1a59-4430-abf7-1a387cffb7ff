/**
 * Jest Setup
 * Global test setup and configuration
 */

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.UPSTASH_REDIS_REST_URL = 'https://test-redis.upstash.io';
process.env.UPSTASH_REDIS_REST_TOKEN = 'test-token';
process.env.OPENAI_API_KEY = 'test-openai-key';
process.env.ANTHROPIC_API_KEY = 'test-anthropic-key';

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleLog = console.log;

beforeEach(() => {
  // Reset console mocks before each test
  console.error = jest.fn();
  console.warn = jest.fn();
  console.log = jest.fn();
});

afterEach(() => {
  // Restore console methods after each test
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
  console.log = originalConsoleLog;
});

// Global test utilities
global.testUtils = {
  // Create a mock workflow
  createMockWorkflow: (overrides = {}) => ({
    id: 'test-workflow-id',
    name: 'Test Workflow',
    description: 'Test workflow description',
    version: '1.0.0',
    steps: [],
    metadata: {
      category: 'test',
      difficulty: 'easy',
      estimatedTime: 5
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...overrides
  }),

  // Create a mock execution
  createMockExecution: (overrides = {}) => ({
    id: 'test-execution-id',
    workflowId: 'test-workflow-id',
    status: 'running',
    inputs: {},
    outputs: {},
    stepResults: {},
    progress: 0,
    startedAt: new Date().toISOString(),
    metadata: {
      source: 'test',
      priority: 'normal'
    },
    ...overrides
  }),

  // Create a mock content item
  createMockContentItem: (overrides = {}) => ({
    id: 'test-content-id',
    type: 'generic',
    title: 'Test Content',
    content: 'Test content data',
    status: 'draft',
    executionId: 'test-execution-id',
    stepId: 'test-step-id',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    metadata: {},
    ...overrides
  }),

  // Create a mock review
  createMockReview: (overrides = {}) => ({
    id: 'test-review-id',
    contentId: 'test-content-id',
    executionId: 'test-execution-id',
    stepId: 'test-step-id',
    type: 'approval',
    status: 'pending',
    instructions: 'Please review this content',
    createdAt: new Date().toISOString(),
    ...overrides
  }),

  // Wait for async operations
  waitFor: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),

  // Create a mock error
  createMockError: (message = 'Test error', code = 'TEST_ERROR') => {
    const error = new Error(message);
    error.code = code;
    return error;
  }
};

// Mock timers for tests that use setTimeout/setInterval
// jest.useFakeTimers(); // Disabled for error handler tests

// Increase timeout for async tests
jest.setTimeout(30000);
